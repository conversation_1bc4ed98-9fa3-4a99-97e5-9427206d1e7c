[10:07:10,745][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[10:07:10,851][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 5392 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[10:07:10,852][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[10:07:10,852][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[10:07:15,541][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:07:15,542][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:07:17,256][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[10:07:18,512][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[10:07:18,561][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[10:07:18,685][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:07:19,052][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:07:20,083][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[10:07:20,083][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[10:07:20,083][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[10:07:20,083][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[10:07:20,085][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[10:07:21,095][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:07:21,097][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:07:21,100][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:2}
[10:07:21,112][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:07:21,112][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:07:21,154][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.HibernateException: Access to DialectResolutionInfo cannot be null when 'hibernate.dialect' not set
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:100) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:54) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
[10:09:55,917][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[10:09:56,024][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 12600 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[10:09:56,028][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[10:09:56,029][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[10:09:59,960][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:09:59,961][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:10:01,806][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[10:10:03,053][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[10:10:03,100][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[10:10:03,214][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:10:03,642][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[10:10:04,722][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[10:10:05,809][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:10:05,811][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:10:05,814][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:1}
[10:10:05,825][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:10:05,825][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:10:05,872][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.HibernateException: Access to DialectResolutionInfo cannot be null when 'hibernate.dialect' not set
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:100) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:54) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
[10:13:10,357][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[10:13:10,424][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 20580 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[10:13:10,425][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[10:13:10,426][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[10:13:14,155][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:13:14,156][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:13:15,833][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[10:13:17,102][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[10:13:17,158][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[10:13:17,338][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:13:17,709][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[10:13:18,755][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[10:13:19,765][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:13:19,766][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:13:19,769][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:1}
[10:13:19,781][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:13:19,781][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:13:19,824][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.HibernateException: Access to DialectResolutionInfo cannot be null when 'hibernate.dialect' not set
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:100) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:54) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
[13:56:03,932][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[13:56:03,998][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 18708 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[13:56:03,999][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[13:56:04,000][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[13:56:07,841][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[13:56:07,842][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[13:56:09,477][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[13:56:10,701][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[13:56:10,744][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[13:56:10,858][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[13:56:11,238][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[13:56:12,288][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[13:56:13,234][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[13:56:13,236][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[13:56:13,239][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:1}
[13:56:13,251][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[13:56:13,251][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[13:56:13,294][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.HibernateException: Access to DialectResolutionInfo cannot be null when 'hibernate.dialect' not set
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:100) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:54) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
[14:01:51,826][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[14:01:51,898][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 9416 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[14:01:51,898][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[14:01:51,899][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[14:01:55,747][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:01:55,748][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:01:57,447][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[14:01:58,756][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[14:01:58,801][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[14:01:58,933][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:01:59,348][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[14:02:00,550][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[14:02:01,575][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[14:02:01,577][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[14:02:01,580][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:1}
[14:02:01,591][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:02:01,591][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:02:01,634][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.HibernateException: Access to DialectResolutionInfo cannot be null when 'hibernate.dialect' not set
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:100) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:54) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
[14:08:00,278][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[14:08:00,353][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 18736 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[14:08:00,353][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[14:08:00,354][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[14:08:04,088][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:08:04,088][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:08:05,766][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[14:08:07,051][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[14:08:07,095][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[14:08:07,210][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:08:07,585][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[14:08:09,097][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[14:08:09,548][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[14:08:09,550][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[14:08:09,553][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:2}
[14:08:09,564][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:08:09,564][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:08:09,611][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.boot.registry.selector.spi.StrategySelectionException: Unable to resolve name [dm.dialect.Dialect] as strategy [org.hibernate.dialect.Dialect]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:156) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:239) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:183) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:170) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:164) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect(DialectFactoryImpl.java:74) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:51) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.boot.registry.classloading.spi.ClassLoadingException: Unable to load class [dm.dialect.Dialect]
	at org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:133) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:239) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:183) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:170) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:164) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect(DialectFactoryImpl.java:74) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:51) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: java.lang.ClassNotFoundException: Could not load requested class : dm.dialect.Dialect
	at org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:210) ~[hibernate-core-5.4.33.jar:5.4.33]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:592) ~[?:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:525) ~[?:?]
	at java.lang.Class.forName0(Native Method) ~[?:?]
	at java.lang.Class.forName(Class.java:467) ~[?:?]
	at org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:130) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:239) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:183) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:170) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:164) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect(DialectFactoryImpl.java:74) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:51) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
