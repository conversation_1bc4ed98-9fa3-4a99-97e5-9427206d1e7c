[10:07:10,745][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[10:07:10,851][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 5392 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[10:07:10,852][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[10:07:10,852][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[10:07:15,541][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:07:15,542][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:07:17,256][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[10:07:18,512][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[10:07:18,561][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[10:07:18,685][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:07:19,052][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:07:20,083][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[10:07:20,083][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[10:07:20,083][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[10:07:20,083][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[10:07:20,084][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[10:07:20,085][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[10:07:21,095][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:07:21,097][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:07:21,100][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:2}
[10:07:21,112][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:07:21,112][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:07:21,154][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.HibernateException: Access to DialectResolutionInfo cannot be null when 'hibernate.dialect' not set
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:100) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:54) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
[10:09:55,917][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[10:09:56,024][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 12600 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[10:09:56,028][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[10:09:56,029][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[10:09:59,960][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:09:59,961][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:10:01,806][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[10:10:03,053][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[10:10:03,100][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[10:10:03,214][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:10:03,642][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[10:10:04,721][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[10:10:04,722][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[10:10:05,809][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:10:05,811][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:10:05,814][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:1}
[10:10:05,825][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:10:05,825][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:10:05,872][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.HibernateException: Access to DialectResolutionInfo cannot be null when 'hibernate.dialect' not set
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:100) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:54) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
[10:13:10,357][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[10:13:10,424][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 20580 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[10:13:10,425][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[10:13:10,426][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[10:13:14,155][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:13:14,156][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:13:15,833][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[10:13:17,102][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[10:13:17,158][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[10:13:17,338][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:13:17,709][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[10:13:18,752][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[10:13:18,753][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[10:13:18,755][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[10:13:19,765][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:13:19,766][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[10:13:19,769][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:1}
[10:13:19,781][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:13:19,781][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[10:13:19,824][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.HibernateException: Access to DialectResolutionInfo cannot be null when 'hibernate.dialect' not set
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:100) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:54) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
[13:56:03,932][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[13:56:03,998][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 18708 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[13:56:03,999][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[13:56:04,000][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[13:56:07,841][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[13:56:07,842][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[13:56:09,477][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[13:56:10,701][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[13:56:10,744][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[13:56:10,858][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[13:56:11,238][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[13:56:12,286][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[13:56:12,288][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[13:56:13,234][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[13:56:13,236][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[13:56:13,239][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:1}
[13:56:13,251][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[13:56:13,251][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[13:56:13,294][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.HibernateException: Access to DialectResolutionInfo cannot be null when 'hibernate.dialect' not set
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:100) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:54) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
[14:01:51,826][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[14:01:51,898][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 9416 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[14:01:51,898][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[14:01:51,899][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[14:01:55,747][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:01:55,748][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:01:57,447][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[14:01:58,756][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[14:01:58,801][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[14:01:58,933][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:01:59,348][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[14:02:00,548][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[14:02:00,550][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[14:02:01,575][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[14:02:01,577][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[14:02:01,580][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:1}
[14:02:01,591][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:02:01,591][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:02:01,634][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.HibernateException: Access to DialectResolutionInfo cannot be null when 'hibernate.dialect' not set
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:100) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:54) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
[14:08:00,278][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[14:08:00,353][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 18736 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[14:08:00,353][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[14:08:00,354][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[14:08:04,088][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:08:04,088][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:08:05,766][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[14:08:07,051][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[14:08:07,095][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[14:08:07,210][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:08:07,585][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[14:08:09,095][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[14:08:09,097][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[14:08:09,548][ERROR][org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean][main] [] [] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[14:08:09,550][WARN ][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][main] [] [] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
[14:08:09,553][DEBUG][org.mongodb.driver.connection][main] [] [] Closing connection connectionId{localValue:2}
[14:08:09,564][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:08:09,564][DEBUG][reactor.core.publisher.Hooks][main] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:08:09,611][ERROR][org.springframework.boot.SpringApplication][main] [] [] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370) ~[spring-boot-2.5.14.jar:2.5.14]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359) ~[spring-boot-2.5.14.jar:2.5.14]
	at com.goodsogood.ows.OwsMeetingApplication.main(OwsMeetingApplication.java:152) ~[classes/:?]
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:275) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.boot.registry.selector.spi.StrategySelectionException: Unable to resolve name [dm.dialect.Dialect] as strategy [org.hibernate.dialect.Dialect]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:156) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:239) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:183) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:170) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:164) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect(DialectFactoryImpl.java:74) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:51) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: org.hibernate.boot.registry.classloading.spi.ClassLoadingException: Unable to load class [dm.dialect.Dialect]
	at org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:133) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:239) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:183) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:170) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:164) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect(DialectFactoryImpl.java:74) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:51) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
Caused by: java.lang.ClassNotFoundException: Could not load requested class : dm.dialect.Dialect
	at org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:210) ~[hibernate-core-5.4.33.jar:5.4.33]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:592) ~[?:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:525) ~[?:?]
	at java.lang.Class.forName0(Native Method) ~[?:?]
	at java.lang.Class.forName(Class.java:467) ~[?:?]
	at org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:130) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:239) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:183) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:170) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:164) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect(DialectFactoryImpl.java:74) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:51) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:137) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1255) ~[hibernate-core-5.4.33.jar:5.4.33]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341) ~[spring-orm-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.20.jar:5.3.20]
	... 16 more
[14:11:55,569][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[14:11:55,639][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 15392 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[14:11:55,639][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[14:11:55,640][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[14:11:59,768][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:11:59,769][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:12:01,504][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[14:12:02,774][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[14:12:02,813][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[14:12:02,927][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:12:03,311][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:12:04,835][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[14:12:04,835][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[14:12:04,836][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[14:12:04,838][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[14:12:09,050][WARN ][tk.mybatis.mapper.mapperhelper.resolve.DefaultEntityResolve][main] [] [] 通用 Mapper 警告信息: <[EntityColumn{table=t_meeting_comment, property='excellentNum', column='excellent_num', javaType=int, jdbcType=null, typeHandler=null, id=false, identity=false, blob=false, generator='null', orderBy='null', orderPriority='0', insertable=true, updatable=true, order=DEFAULT}]> 使用了基本类型，基本类型在动态 SQL 中由于存在默认值，因此任何时候都不等于 null，建议修改基本类型为对应的包装类型!
[14:12:09,050][WARN ][tk.mybatis.mapper.mapperhelper.resolve.DefaultEntityResolve][main] [] [] 通用 Mapper 警告信息: <[EntityColumn{table=t_meeting_comment, property='status', column='status', javaType=int, jdbcType=null, typeHandler=null, id=false, identity=false, blob=false, generator='null', orderBy='null', orderPriority='0', insertable=true, updatable=true, order=DEFAULT}]> 使用了基本类型，基本类型在动态 SQL 中由于存在默认值，因此任何时候都不等于 null，建议修改基本类型为对应的包装类型!
[14:12:12,030][INFO ][com.goodsogood.ows.component.DingEventSyncScheduler][main] [] [] 有日程的活动结束后同步钉钉日程的签到状态定时任务初始化成功！
[14:12:12,828][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:2}
[14:12:12,828][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:1}
[14:12:12,828][INFO ][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Exception in monitor thread while connecting to server **************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70) ~[mongodb-driver-core-4.2.3.jar:?]
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:143) ~[mongodb-driver-core-4.2.3.jar:?]
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188) ~[mongodb-driver-core-4.2.3.jar:?]
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144) ~[mongodb-driver-core-4.2.3.jar:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.SocketTimeoutException: Connect timed out
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:107) ~[mongodb-driver-core-4.2.3.jar:?]
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79) ~[mongodb-driver-core-4.2.3.jar:?]
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65) ~[mongodb-driver-core-4.2.3.jar:?]
	... 4 more
[14:12:12,829][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:12:12,829][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:12:13,801][WARN ][tk.mybatis.mapper.mapperhelper.resolve.DefaultEntityResolve][main] [] [] 通用 Mapper 警告信息: <[EntityColumn{table=t_meeting_talk, property='talkType', column='talk_type', javaType=int, jdbcType=null, typeHandler=null, id=false, identity=false, blob=false, generator='null', orderBy='null', orderPriority='0', insertable=true, updatable=true, order=DEFAULT}]> 使用了基本类型，基本类型在动态 SQL 中由于存在默认值，因此任何时候都不等于 null，建议修改基本类型为对应的包装类型!
[14:12:13,801][WARN ][tk.mybatis.mapper.mapperhelper.resolve.DefaultEntityResolve][main] [] [] 通用 Mapper 警告信息: <[EntityColumn{table=t_meeting_talk, property='source', column='source', javaType=int, jdbcType=null, typeHandler=null, id=false, identity=false, blob=false, generator='null', orderBy='null', orderPriority='0', insertable=true, updatable=true, order=DEFAULT}]> 使用了基本类型，基本类型在动态 SQL 中由于存在默认值，因此任何时候都不等于 null，建议修改基本类型为对应的包装类型!
[14:12:14,019][WARN ][tk.mybatis.mapper.mapperhelper.resolve.DefaultEntityResolve][main] [] [] 通用 Mapper 警告信息: <[EntityColumn{table=count_meeting_talk_vo, property='peopleNum', column='people_num', javaType=int, jdbcType=null, typeHandler=null, id=false, identity=false, blob=false, generator='null', orderBy='null', orderPriority='0', insertable=true, updatable=true, order=DEFAULT}]> 使用了基本类型，基本类型在动态 SQL 中由于存在默认值，因此任何时候都不等于 null，建议修改基本类型为对应的包装类型!
[14:12:16,502][WARN ][org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration][main] [] [] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[14:12:16,653][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.ValidateExceptionHandler:
	
[14:12:16,665][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CategoryController:
	{DELETE [/category/del/{id}]}: delCategory(HttpHeaders,long)
	{POST [/category/update]}: updateCategory(HttpHeaders,CategoryUpdateForm,BindingResult)
	{GET [/category/list]}: listAllCategory(HttpHeaders,Long,String)
	{POST [/category/add]}: addCategory(HttpHeaders,CategoryAddForm,BindingResult)
	{GET [/category/list-all/{tag}]}: listAll(HttpHeaders,short)
[14:12:16,673][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentApproveController:
	{POST [/comment-approve/list]}: selectApproveList(CommentApproveQueryForm,BindingResult,HttpHeaders)
	{POST [/comment-approve/approve]}: approveComment(ApproveCommentInfoForm,BindingResult,HttpHeaders)
	{GET [/comment-approve/get]}: getComment(long)
[14:12:16,675][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentController:
	{GET [/comment/auto-genera-comment]}: autoGeneraComment(int,HttpHeaders)
	{GET [/comment/flush-appraisal]}: flushCommentAppraisalFile(long,HttpHeaders)
	{GET [/comment/query-list-by-ids]}: queryCommentListById(List,HttpHeaders)
	{GET [/comment/query-list]}: queryCommentList(Integer,Long,Integer,int,int,HttpHeaders)
	{GET [/comment/is-submit]}: isSubmitComment(long,HttpHeaders)
	{GET [/comment/excellent/get]}: getExcellent(Long,long,HttpHeaders)
	{GET [/comment/submit]}: submitComment(long,HttpHeaders)
	{GET [/comment/excellent/set]}: setExcellent(long,int,HttpHeaders)
	{GET [/comment/flush-grade]}: flushGradeFile(long,HttpHeaders)
	{GET [/comment/get]}: getComment(Long,HttpHeaders)
	{GET [/comment/start]}: startComment(Long,long,int,int,HttpHeaders)
[14:12:16,679][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentMemberAppraisalController:
	{GET [/comment-member-appraisal/get]}: getCommentMemberAppraisal(Long,Long,HttpHeaders)
	{POST [/comment-member-appraisal/edit]}: insertCommentMemberAppraisal(MemberAppraisalForm,BindingResult,HttpHeaders)
	{POST [/comment-member-appraisal/batch-deal]}: batchInputAppraisal(MemberAppraisalBatchForm,BindingResult,HttpHeaders)
	{GET [/comment-member-appraisal/appraisal-statistical]}: appraisalStatistical(long,HttpHeaders)
	{POST [/comment-member-appraisal/user-list]}: getCommentMember(MemberAppraisalQueryVO,BindingResult,HttpHeaders)
	{GET [/comment-member-appraisal/no-appraisal]}: noAppraisalNum(long,HttpHeaders)
	{GET [/comment-member-appraisal/submit]}: submitAppraisal(long,HttpHeaders)
	{GET [/comment-member-appraisal/notice-no-appraisal]}: noticeNoAppraisal(long,HttpHeaders)
[14:12:16,681][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentMemberComplexController:
	{POST [/comment-member-complex/insert]}: insertMemberComplex(CommentMemberComplexAddForm,BindingResult,HttpHeaders)
	{POST [/comment-member-complex/batch-insert]}: batchInsertMemberComplex(CommentMemberComplexBatchAddForm,BindingResult,HttpHeaders)
	{GET [/comment-member-complex/get]}: getMemberComplex(long,HttpHeaders)
	{GET [/comment-member-complex/complex-statistical]}: complexStatistical(long,HttpHeaders)
[14:12:16,682][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentMemberController:
	{POST [/comment-member/insert]}: insertCommentMember(CommentMemberForm,BindingResult,HttpHeaders)
	{POST [/comment-member/query-list]}: getCommentMemberList(CommentMemberVO,BindingResult,HttpHeaders)
	{GET [/comment-member/get-member-status]}: getCommentMemberStatus(HttpHeaders)
	{GET [/comment-member/get]}: getCommentMember(Long,Long,HttpHeaders)
	{POST [/comment-member/add-member]}: addCommentMember(DealMemberVO,BindingResult,HttpHeaders)
	{POST [/comment-member/del-member]}: delCommentMember(DealMemberVO,BindingResult,HttpHeaders)
	{GET [/comment-member/notice-no-self]}: noticeNoSelf(long,HttpHeaders)
	{GET [/comment-member/no-self]}: noSelf(long,HttpHeaders)
	{GET [/comment-member/self-statistical]}: selfStatistical(long,HttpHeaders)
[14:12:16,684][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentStaticController:
	{POST [/comment/statics/query_selftemplate]}: querySelfCommentFile(CommentMemberVO,List,HttpHeaders)
	{GET [/comment/statics/down_statics]}: downStaticsReport(Integer,Long,List,HttpHeaders,HttpServletResponse)
	{GET [/comment/statics/query_template]}: queryCommentFile(List,List,HttpHeaders)
	{GET [/comment/statics/query]}: queryStatics(Integer,Long,Integer,Integer,HttpHeaders)
[14:12:16,685][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.GloryController:
	{GET [/glory/pull-data]}: queryCommentListById(Long,Integer,HttpHeaders)
[14:12:16,685][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.GroupController:
	{DELETE [/type-group/del/{id}]}: delTypeGroup(HttpHeaders,long)
	{GET [/type-group/list]}: listTypeGroup(HttpHeaders,Integer,Integer)
	{GET [/type-group/list-all]}: listAllTypeGroup(HttpHeaders)
	{POST [/type-group/add]}: addTypeGroup(HttpHeaders,GroupAddForm,BindingResult)
[14:12:16,686][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.IndexController:
	{GET [/index]}: addMeeting(HttpHeaders)
[14:12:16,689][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.LifeController:
	{POST [/life/add_edit]}: addOrEdit(HttpHeaders,LifeAddEditForm,BindingResult)
	{GET [/life/change]}: changeStatus(HttpHeaders,Long,Integer,Integer)
	{POST [/life/save_check]}: saveCheck(HttpHeaders,Long,Integer,List)
	{GET [/life/check_self]}: checkSelf(Long,Integer)
	{GET [/life/delete_special]}: deleteSpecial(HttpHeaders,Long,Integer,Integer,List,Integer)
	{GET [/life/in]}: inTheMeeting(Long)
	{GET [/life/join_life]}: joinLifeByActivity(Long,List,Integer,Integer,Integer,HttpHeaders)
	{GET [/life/only_file]}: onlyFile(Long,Integer,List)
	{GET [/life/list]}: list(HttpHeaders,String,Long,List,Integer,List,Integer,Integer,Integer)
	{GET [/life/finish]}: finish(HttpHeaders,Long)
	{POST [/life/tag]}: tag(HttpHeaders,LifeTagManageForm,BindingResult)
	{GET [/life/del]}: del(HttpHeaders,Long)
	{GET [/life/title]}: title(Long)
	{GET [/life/advice]}: advice(Long,Integer)
[14:12:16,692][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.LifeFileController:
	{POST [/life/file/file_task]}: fileTask(HttpHeaders,LifeFileTaskForm)
	{GET [/life/file/open_task]}: openTask(HttpHeaders,String)
	{GET [/life/file/file_info]}: queryFileInfo(HttpHeaders,Long,Integer)
	{POST [/life/file/delete_talk_attach]}: deleteTalkFile(SaveAttachForm)
	{GET [/life/file/query_attach]}: queryAttach(HttpHeaders,Long,List,Integer)
	{GET [/life/file/query_uploader]}: queryUploader(HttpHeaders,Long,Integer,Long)
	{GET [/life/file/delete_uploader]}: deleteUploader(HttpHeaders,Long,Long,Integer,Long)
	{POST [/life/file/save_attach]}: saveAttach(HttpHeaders,SaveAttachForm,BindingResult)
	{GET [/life/file/delete_attach]}: deleteAttach(HttpHeaders,Long,List,Integer)
	{POST [/life/file/report_attach]}: reportAttach(HttpHeaders,Long,Map)
	{GET [/life/file/clone]}: test(Long)
[14:12:16,697][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingController:
	{POST [/meeting/add-user]}: addUser(HttpHeaders,MeetingAddUserForm,BindingResult)
	{POST [/meeting/notice]}: notice(MeetingNoticeForm,BindingResult,HttpHeaders)
	{GET [/meeting/statistical/org-front-page]}: statisticalOrgJoinTimes(HttpHeaders,Integer,Long)
	{GET [/meeting/statistical/user-front-page]}: statisticalUserJoinTimes(HttpHeaders,Integer)
	{GET [/meeting/hs/{meeting_id}]}: getMeetingHistoryList(long)
	{POST [/meeting/add]}: addMeeting(HttpHeaders,MeetingEntity,BindingResult)
	{POST [/meeting/update]}: updateMeeting(HttpHeaders,MeetingEntity,BindingResult)
	{DELETE [/meeting/cancel/{id}]}: cancelMeeting(HttpHeaders,long)
	{DELETE [/meeting/del/{id}]}: delMeeting(HttpHeaders,long)
	{GET [/meeting/scheduleList]}: getScheduleList(HttpHeaders,String,String)
	{DELETE [/meeting/revoke/{id}]}: revokeMeeting(HttpHeaders,long)
	{GET [/meeting/sign-qr-code]}: signQrCod(HttpServletResponse,Long,Integer,Integer)
	{GET [/meeting/sign-list]}: signList(Long)
	{POST [/meeting/sign-in]}: signIn(HttpHeaders,MeetingSignInForm,BindingResult)
	{GET [/meeting/report]}: meetingReport(HttpServletResponse,HttpHeaders,List,String,String,List,Integer,Date,Date,Short,List,List,List,Date,Date,Integer)
	{POST [/meeting/update-agenda]}: updateAgenda(HttpHeaders,UpdateAgendaForm,BindingResult)
	{POST [/meeting/tag/edit]}: tagEdit(HttpHeaders,TagEditForm,BindingResult)
	{GET [/meeting/tag/del]}: tagDel(HttpHeaders,Long)
	{GET [/meeting/query-meeting-task]}: queryMeetingTask(HttpHeaders,Long,Long,String,String)
	{GET [/meeting/query-meeting-type]}: queryMeetingType(HttpHeaders,Long)
	{GET [/meeting/query-meeting-agenda]}: queryMeetingAgenda(HttpHeaders,Long)
	{GET [/meeting/org/list-all]}: orgListAll(HttpHeaders)
	{GET [/meeting/list-all]}: listAllMeeting(HttpHeaders,String,String,List,Short,List,Date,Date,Integer,Integer,Short)
	{GET [/meeting/list-v2]}: listMeetingV2(HttpHeaders,String,String,List,Short,List,Date,Date,Integer,Integer,Integer,Long,Long,Integer,Integer)
	{GET [/meeting/list]}: listMeeting(HttpHeaders,String,String,List,Short,List,Date,Date,Integer,Integer,Short)
	{GET [/meeting/static/review]}: staticList(HttpHeaders,String,Integer,List,Integer,Date,Date,Short,String,List,List,List,Date,Date,Integer,Integer,Integer)
	{GET [/meeting/test/dingEventSync]}: testDingEventSync(Long,Integer,String)
	{GET [/meeting/ding/event/sync]}: testDingEventSync(HttpHeaders,Long)
	{GET [/meeting/signIn]}: downloadSignIn(HttpHeaders,Long)
	{GET [/meeting/download]}: download(HttpHeaders,String)
	{GET [/meeting/export]}: downloadWord(HttpHeaders,List,List)
	{GET [/meeting/tag/update]}: tagUpdName(HttpHeaders,Long,String)
	{GET [/meeting/header-json]}: tranfHead(HttpHeaders)
	{GET [/meeting/detail/{id}]}: detail(HttpHeaders,long,Short)
	{GET [/meeting/index]}: index(HttpHeaders)
	{GET [/meeting/review]}: list(HttpHeaders,String,List,Integer,Date,Date,Short,String,List,List,List,Date,Date,Integer,Integer,Integer)
	{GET [/meeting/type/list]}: typeList(HttpHeaders,Long,Long,Long,Long,Date)
[14:12:16,701][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingDraftController:
	{POST [/meeting/saveDraft/{orgId}]}: saveDraft(HttpHeaders,Long,MeetingDraftSaveForm,BindingResult)
	{GET [/meeting/deleteDraft/{orgId}]}: deleteDraft(HttpHeaders,Long,Integer,Long)
	{GET [/meeting/getDraft/{orgId}]}: getDraft(HttpHeaders,Long,Integer,Long)
[14:12:16,703][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingLeaderSurveyController:
	{GET [/mls/get]}: findById(HttpHeaders,long)
	{GET [/mls/findSurveyByYear]}: findSurveyByYear(HttpHeaders,int)
	{POST [/mls/append]}: add(HttpHeaders,MeetingLeaderSurveyEntity,BindingResult)
	{POST [/mls/mutate]}: update(HttpHeaders,MeetingLeaderSurveyEntity,BindingResult)
	{POST [/mls/list]}: list(HttpHeaders,MeetingLeaderSurveyForm,Integer,Integer,BindingResult)
	{GET [/mls/eliminate]}: delete(HttpHeaders,long)
[14:12:16,705][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingLeaveController:
	{GET [/leave/list-wait-approve]}: queryApprovelist(HttpHeaders,Integer,List,String,Integer,String,String,String)
	{GET [/leave/detail/{meeting_leave_id}]}: detail(long)
	{POST [/leave/add]}: add(HttpHeaders,MeetingLeaveAddForm,BindingResult)
	{GET [/leave/list]}: list(HttpHeaders,Integer,List,Integer,String,String,String)
	{GET [/leave/t1]}: t1(Long)
	{POST [/leave/check]}: check(HttpHeaders,MeetingLeaveCheckForm,BindingResult)
	{GET [/leave/cancel/{meeting_leave_id}]}: cancel(HttpHeaders,Long,String)
[14:12:16,708][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingOrgCommendPenalizeController:
	{GET [/org/commend/penalize/get-org-commend-statistics]}: getOrgCommendStatistics(Long,HttpHeaders)
	{POST [/org/commend/penalize/export]}: excelList(MeetingOrgCommendPenalizeQueryForm,BindingResult,HttpHeaders)
	{POST [/org/commend/penalize/edit]}: edit(HttpHeaders,MeetingOrgCommendPenalizeForm,BindingResult)
	{GET [/org/commend/penalize/select-by-org-id]}: selectByOrgId(Long,Integer,Integer,HttpHeaders)
	{POST [/org/commend/penalize/detail]}: detail(MeetingOrgCommendPenalizeForm)
	{POST [/org/commend/penalize/add]}: add(HttpHeaders,MeetingOrgCommendPenalizeForm)
	{POST [/org/commend/penalize/list]}: list(MeetingOrgCommendPenalizeQueryForm,BindingResult,HttpHeaders)
	{POST [/org/commend/penalize/delete]}: delete(HttpHeaders,MeetingOrgCommendPenalizeForm,BindingResult)
	{GET [/org/commend/penalize/export]}: export(String,HttpHeaders)
[14:12:16,710][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingOrgDebriefReviewController:
	{GET [/org/debrief/review/statistics/excel/list]}: statisticsExcelList(HttpHeaders,Integer,Long,String,HttpServletResponse)
	{GET [/org/debrief/review/excel/list]}: excelList(HttpHeaders,Integer,Long,String,Integer,HttpServletResponse)
	{POST [/org/debrief/review/edit]}: edit(HttpHeaders,MeetingOrgDebriefReviewForm)
	{POST [/org/debrief/review/statistics/list]}: statisticsList(HttpHeaders,MeetingOrgDebriefReviewStatisticsQueryForm)
	{GET [/org/debrief/review/statistics/list/test]}: statisticsList()
	{POST [/org/debrief/review/detail]}: detail(MeetingOrgDebriefReviewForm)
	{POST [/org/debrief/review/add]}: add(HttpHeaders,MeetingOrgDebriefReviewForm)
	{POST [/org/debrief/review/list]}: list(HttpHeaders,MeetingOrgDebriefReviewQueryForm)
	{POST [/org/debrief/review/delete]}: delete(HttpHeaders,MeetingOrgDebriefReviewForm)
[14:12:16,712][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingPlanController:
	{GET [/plan/list]}: listPageMeetingPlan(HttpHeaders,String,Integer,Integer)
	{GET [/plan/list-all/{tag}]}: listAllMeetingPlan(HttpHeaders,Short)
	{GET [/plan/list-all]}: listAllMeetingPlan(HttpHeaders,String)
	{POST [/plan/add]}: addMeetingPlan(HttpHeaders,MeetingPlanAddForm,BindingResult)
	{DELETE [/plan/del/{id}]}: delMeetingPlan(HttpHeaders,long)
	{POST [/plan/execute-org/{meetingId}/{page}]}: executeOrg(HttpHeaders,Long,Integer,HashMap)
	{GET [/plan/detail/{id}]}: detail(HttpHeaders,long)
	{POST [/plan/execute]}: execute(HttpHeaders,MeetingExecuteForm,BindingResult)
[14:12:16,713][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingReportController:
	{GET [/result/check/list]}: page(HttpHeaders,int,String,Integer,String,String,String,String,String,Integer,Integer)
	{GET [/result/topic/submit/precondition/check]}: topicSubmitPreconditionCheck(HttpHeaders,Long,Long)
	{GET [/result/topic/detail]}: answerDetail(Long,Long,Long)
	{POST [/result/topic/submit]}: topicSubmit(HttpHeaders,MeetingReportTopicContentForm,BindingResult)
	{POST [/result/update/leader]}: updateLeader(HttpHeaders,MeetingResultForm,BindingResult)
	{GET [/result/detail/{id}]}: detail(HttpHeaders,Long,Short)
	{POST [/result/add]}: add(HttpHeaders,MeetingAndResultForm,BindingResult)
	{POST [/result/update]}: update(HttpHeaders,MeetingResultForm,BindingResult)
	{POST [/result/check]}: check(HttpHeaders,MeetingResultCheckForm,BindingResult)
	{POST [/result/topic/answer]}: answer(HttpHeaders,MeetingReportForm,BindingResult)
	{POST [/result/submit]}: submit(HttpHeaders,MeetingResultForm,BindingResult)
[14:12:16,715][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingScoreController:
	{GET [/meeting/score/test/basicsScoreAdd]}: unfinishedGroupLeader(HttpHeaders,Long,String,Integer)
	{GET [/meeting/score/test/unfinishedGroupLeader]}: unfinishedGroupLeader(HttpHeaders,Long,String)
	{GET [/meeting/score/test/operation/meetingScore]}: operationMeetingScore(HttpHeaders,Long,String,String,Integer,Integer,Integer,List,Integer,Long)
[14:12:16,716][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingTalkController:
	{GET [/meeting-talk/countMeetingTalk]}: countMeetingTalkByCondition(HttpHeaders,String,long)
	{GET [/meeting-talk/select-by-type]}: queryMeetingTalkList(int,long,int,Long,String,String,String,String,int,int,int)
	{POST [/meeting-talk/batch-del]}: batchDelMeetingTalk(BatchMeetingTalkForm,HttpHeaders)
	{GET [/meeting-talk/del/{talk_id}]}: delMeetingTalk(long,HttpHeaders)
	{GET [/meeting-talk/find/{talk_id}]}: findMeetingTalk(long,HttpHeaders)
	{POST [/meeting-talk/edit]}: editMeetingTalk(MeetingTalkForm,HttpHeaders)
	{POST [/meeting-talk/genera-leader-talk]}: generaLeaderTalk(GeneraLeaderAndUserTalkForm,HttpHeaders)
[14:12:16,717][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingTaskController:
	{GET [/meeting-task/list-all]}: listAll(HttpHeaders,Long,String,Long,List,Long,Short,String,Date,Date,Date,Date)
	{POST [/meeting-task/find/task/info]}: findTaskInfo(HttpHeaders,TaskQueryForm)
	{GET [/meeting-task/list]}: list(HttpHeaders,Long,String,Long,List,Long,Short,String,Date,Date,Date,Date,Integer,Integer)
[14:12:16,718][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingTopicTaskController:
	{GET [/topic-task/list/{tag}]}: page(HttpHeaders,short,Short,String,Date,Date,Date,Date,Integer,Integer)
	{GET [/topic-task/list-all/{tag}]}: list(HttpHeaders,short,Short,String,Date,Date,Date,Date)
[14:12:16,718][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingWaitSignController:
	{POST [/w-sign/add]}: AddStudyCert(HttpHeaders,AddStudyCertForm,BindingResult)
	{POST [/w-sign/queryList]}: queryAddStudyList(HttpHeaders,StudyAddListForm)
	{GET [/w-sign/find]}: find(Long,HttpHeaders)
[14:12:16,719][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingWorkPointController:
	{GET [/mwp/get]}: findById(HttpHeaders,long)
	{GET [/mwp/cbo_id]}: createByOwnerId(HttpHeaders,Integer)
	{POST [/mwp/fill]}: update(HttpHeaders,MeetingWorkPointEntity,BindingResult)
	{POST [/mwp/list]}: list(HttpHeaders,MeetingWorkPointForm,Integer,Integer,BindingResult)
[14:12:16,720][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MyMeetingController:
	{GET [/my-meeting/stats]}: stats(HttpHeaders)
	{GET [/my-meeting/list]}: list(HttpHeaders,String,Short,Long,List,Date,Date)
[14:12:16,720][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.OrgChangeCallbackController:
	{GET [/user_change_political_type_callback]}: userChangePoliticalTypeCallback(Long,Integer,String,HttpHeaders)
	{POST [/user_change_org_callback]}: userChangeCallBack(UserChangeForm,HttpHeaders)
	{POST [/org_change_callback]}: orgChangeCallback(OrganizationBase,HttpHeaders)
[14:12:16,723][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.OrgLifeController:
	{POST [/org_life/add_edit]}: addOrEdit(HttpHeaders,OrgLifeAddEditForm,BindingResult)
	{GET [/org_life/change]}: changeStatus(HttpHeaders,Long,Integer,Integer)
	{POST [/org_life/save_check]}: saveCheck(HttpHeaders,Long,Integer,List)
	{GET [/org_life/check]}: checkSelf(Long,Integer)
	{GET [/org_life/delete_special]}: deleteSpecial(HttpHeaders,Long,Integer,Integer,List,Integer)
	{GET [/org_life/in]}: inTheMeeting(Long)
	{GET [/org_life/only_file]}: onlyFile(Long,Integer,List)
	{GET [/org_life/quit-relate-meeting]}: quitRelateMeeting(HttpHeaders,Long,List)
	{GET [/org_life/link_comment]}: linkComment(HttpHeaders,Long,Long,Integer)
	{GET [/org_life/relate-meeting]}: relateMeeting(HttpHeaders,Long,List)
	{GET [/org_life/query-meeting-list]}: queryMeetingList(HttpHeaders,String,String,List,Short,List,Date,Date,Integer,Integer,Integer,Long,Long,Integer,Integer,Integer)
	{GET [/org_life/test-del-task]}: testDelTask(HttpHeaders,Long)
	{GET [/org_life/list]}: list(HttpHeaders,String,Long,List,Integer,List,Integer,Integer,Integer)
	{GET [/org_life/finish]}: finish(HttpHeaders,Long)
	{POST [/org_life/tag]}: tag(HttpHeaders,OrgLifeTagManageForm,BindingResult)
	{GET [/org_life/del]}: del(HttpHeaders,Long)
	{GET [/org_life/title]}: title(Long)
	{GET [/org_life/advice]}: advice(Long,Integer)
[14:12:16,726][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.OrgLifeFileController:
	{GET [/org_life/file/t7]}: t7()
	{GET [/org_life/file/t6]}: t6()
	{GET [/org_life/file/t5]}: t5(Long,List)
	{POST [/org_life/file/file_task]}: fileTask(HttpHeaders,LifeFileTaskForm)
	{GET [/org_life/file/open_task]}: openTask(HttpHeaders,String)
	{GET [/org_life/file/file_info]}: queryFileInfo(HttpHeaders,Long,Integer)
	{POST [/org_life/file/delete_talk_attach]}: deleteTalkFile(SaveAttachForm)
	{GET [/org_life/file/query_attach]}: queryAttach(HttpHeaders,Long,List,Integer)
	{GET [/org_life/file/query_uploader]}: queryUploader(HttpHeaders,Long,Integer,Long)
	{GET [/org_life/file/delete_uploader]}: deleteUploader(HttpHeaders,Long,Long,Integer,Long)
	{POST [/org_life/file/save_attach]}: saveAttach(HttpHeaders,SaveAttachForm,BindingResult)
	{GET [/org_life/file/delete_attach]}: deleteAttach(HttpHeaders,Long,List,Integer)
	{POST [/org_life/file/report_attach]}: reportAttach(HttpHeaders,Long,Map)
	{GET [/org_life/file/t3]}: test3(Long,Integer,HttpHeaders)
	{GET [/org_life/file/t2]}: test2(Long,HttpHeaders)
	{GET [/org_life/file/t1]}: test1(Long,HttpHeaders)
	{GET [/org_life/file/t4]}: test4(List,HttpHeaders)
	{GET [/org_life/file/clone]}: test(Long)
[14:12:16,728][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.PracticableController:
	{POST [/practicable/compile]}: updatePracticable(HttpHeaders,PracticableEntity,BindingResult)
	{POST [/practicable/list]}: listPracticable(HttpHeaders,PracticableForm,Integer,Integer)
	{POST [/practicable/append]}: addPracticable(HttpHeaders,PracticableEntity,BindingResult)
	{GET [/practicable/erasure]}: deletePracticable(HttpHeaders,Long)
[14:12:16,729][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.ReportController:
	{POST [/report/awareness/compile]}: changeReportAwareness(HttpHeaders,ReportEntity)
	{GET [/report/erasure]}: eliminateReport(Long)
	{POST [/report/compile]}: changeReport(HttpHeaders,ReportEntity)
	{POST [/report/append]}: addReport(HttpHeaders,ReportEntity)
	{POST [/report/awareness/append]}: addReportAwareness(HttpHeaders,ReportEntity)
	{POST [/report/list]}: listReport(ReportForm,Integer,Integer)
[14:12:16,730][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SbwMyTaskController:
	{GET [/sbw/my/task]}: myTaskList(String,String,String,Integer,Integer,HttpHeaders)
	{POST [/sbw/my/save]}: save(SbwHandleForm,HttpHeaders)
	{POST [/sbw/my/submit]}: submit(SbwHandleForm,HttpHeaders)
[14:12:16,731][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SbwNewTaskController:
	{POST [/sbw-task/del]}: delTask(SbwNewTaskForm,BindingResult,HttpHeaders)
	{GET [/sbw-task/releaseFind]}: releaseTaskList(HttpHeaders,String,Date,Date,Integer,Integer)
	{GET [/sbw-task/check]}: releaseTaskDetails(HttpHeaders,Long)
	{GET [/sbw-task/myCheck]}: taskDetails(HttpHeaders,Long)
	{POST [/sbw-task/addTurn]}: createTurnTask(SbwNewTaskForm,BindingResult,HttpHeaders)
	{GET [/sbw-task/print]}: createPrintWord(HttpHeaders,Long,Long)
	{GET [/sbw-task/myTask]}: getMyTask(HttpHeaders,String,Date,Date,Integer,Integer)
	{GET [/sbw-task/orderFind]}: showTask(Long,Integer,Long,HttpHeaders)
	{GET [/sbw-task/find-org]}: showTask(Long,Integer)
[14:12:16,732][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SbwShiftTaskController:
	{POST [/sbw/shift/save]}: saveShiftTask(HttpHeaders,SbwShiftTaskFrom)
	{POST [/sbw/shift/submit]}: submitShiftTask(HttpHeaders,SbwShiftTaskFrom)
	{GET [/sbw/shift/details]}: showTask(HttpHeaders,Long)
	{POST [/sbw/shift/del]}: compileShiftTask(HttpHeaders,SbwShiftTaskFrom)
	{GET [/sbw/shift/list]}: list(HttpHeaders,String,String,String,Integer,Integer)
[14:12:16,733][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SbwTaskTypeController:
	{GET [/sbw/type/list]}: list()
[14:12:16,735][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SbwVerifyTaskController:
	{GET [/sbw/verify/org]}: myTaskList(Long)
	{GET [/sbw/verify/task]}: myTaskList(String,String,String,Integer,Integer,HttpHeaders)
	{POST [/sbw/verify/save]}: save(SbwHandleForm,HttpHeaders)
	{POST [/sbw/verify/submit]}: submit(SbwHandleForm,HttpHeaders)
[14:12:16,736][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SchedulerController:
	{GET [/refresh/cache-index-collect]}: refreshCacheOfIndexCollect()
	{GET [/send/notice]}: test(Integer)
[14:12:16,736][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.TaskController:
	{GET [/task/undone-org/stats]}: undoneOrgStats(HttpHeaders)
	{GET [/task/stats]}: list(HttpHeaders)
[14:12:16,738][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.TobaccoTaskHandleController:
	{POST [/tobacco/handle/fill_save]}: fillSave(HttpHeaders,TobaccoTaskHandleForm)
	{POST [/tobacco/handle/verify_save]}: handleVerifyDraft(HttpHeaders,TobaccoTaskHandleForm)
	{POST [/tobacco/handle/fill_submit]}: fill(HttpHeaders,TobaccoTaskHandleForm)
	{POST [/tobacco/handle/verify]}: verify(HttpHeaders,TobaccoTaskHandleForm)
[14:12:16,740][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.TopPriorityController:
	{POST [/top_priority/eliminate]}: removeTopPriorityByIds(HttpHeaders,List)
	{POST [/top_priority/learning_progress]}: getTopPriorityLearningProgress(HttpHeaders,TopPriorityForm,Integer,Integer,BindingResult)
	{POST [/top_priority/by_priority]}: listSummaryByPriority(HttpHeaders,TopPriorityForm,Integer,Integer,BindingResult)
	{POST [/top_priority/by_corps]}: listSummaryByPriority(HttpHeaders,TopPriorityForm,BindingResult)
	{GET [/top_priority/get]}: getTopPriorityById(HttpHeaders,String)
	{POST [/top_priority/mutate]}: updateTopPriority(HttpHeaders,TopPriorityEntity,BindingResult)
	{POST [/top_priority/append]}: addTopPriority(HttpHeaders,TopPriorityEntity,BindingResult)
	{GET [/top_priority/test/read_from_spider]}: readFromSpider(String,HttpHeaders)
	{POST [/top_priority/list]}: list(HttpHeaders,TopPriorityForm,Integer,Integer,BindingResult)
[14:12:16,741][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.TopicController:
	{GET [/topic/page]}: page(HttpHeaders,String,Date,Date,Integer,Integer)
	{GET [/topic/taskOrgList]}: detailOrgList(Long,Integer,String)
	{GET [/topic/taskOrgList/export]}: orgListExport(HttpServletResponse,Long,Integer,String)
	{GET [/topic/detail]}: detail(Long)
	{POST [/topic/add]}: add(HttpHeaders,TopicEntity,BindingResult)
	{POST [/topic/update]}: update(HttpHeaders,TopicEntity,BindingResult)
	{GET [/topic/list]}: list(HttpHeaders,String)
	{DELETE [/topic/del/{topic_id}]}: delete(HttpHeaders,Long)
[14:12:16,743][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.TypeController:
	{GET [/type/list-all]}: listAllType(HttpHeaders,Long,Long,String)
	{GET [/type/update-user-rule]}: updateUserRule(HttpHeaders,Long,Integer,Integer,Integer)
	{DELETE [/type/del/{id}]}: delType(HttpHeaders,long)
	{GET [/type/list-all/{tag}]}: listAll(HttpHeaders,short)
	{POST [/type/add]}: addType(HttpHeaders,TypeAddForm,BindingResult)
	{POST [/type/update]}: updateType(HttpHeaders,TypeUpdateForm,BindingResult)
	{GET [/type/list]}: listType(HttpHeaders,Long,Long,String,Integer,Integer)
[14:12:16,745][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.UserCommendPenalizeController:
	{GET [/user/commend/penalize/get-my-commend-statistics]}: getMyCommendStatistics(Long,HttpHeaders)
	{GET [/user/commend/penalize/select]}: selectUserCommendPenalize(Long,HttpHeaders)
	{GET [/user/commend/penalize/select-by-user-id]}: selectUserCommendPenalizeByUserId(Long,Integer,Integer,HttpHeaders)
	{GET [/user/commend/penalize/del]}: delUserCommendPenalize(Long,HttpHeaders)
	{POST [/user/commend/penalize/query]}: queryUserCommendPenalize(UserCommendPenalizeQueryForm,BindingResult,HttpHeaders)
	{POST [/user/commend/penalize/export]}: exportUserCommendPenalize(UserCommendPenalizeExportQueryForm,HttpHeaders,HttpServletResponse)
	{POST [/user/commend/penalize/add]}: insertUserCommendPenalize(UserCommendPenalizeAddVO,BindingResult,HttpHeaders)
	{POST [/user/commend/penalize/update]}: updateUserCommendPenalize(UserCommendPenalizeUpdateVO,BindingResult,HttpHeaders)
	{GET [/user/commend/penalize/export]}: getUserCommendPenalizeFile(String,HttpHeaders,HttpServletResponse)
	{GET [/user/commend/penalize/fields]}: fields(Integer,HttpHeaders)
[14:12:16,746][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.UserCommentController:
	{POST [/comment/user/update]}: updateUserComment(UserCommentUpdateVO,BindingResult,HttpHeaders)
	{GET [/comment/user/del]}: delUserComment(Long,HttpHeaders)
	{GET [/comment/user/export]}: exportUserComment(String,HttpServletRequest,HttpServletResponse,HttpHeaders)
	{POST [/comment/user/export]}: exportUserCommentList(UserCommentQueryForm,BindingResult,HttpHeaders)
	{POST [/comment/user/query]}: queryUserCommentList(UserCommentQueryForm,BindingResult,HttpHeaders)
	{GET [/comment/user/select]}: selectUserComment(Long,HttpHeaders)
	{POST [/comment/user/insert]}: insertUserComment(UserCommentAddVO,BindingResult,HttpHeaders)
[14:12:16,747][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.UserCommentStatisticsController:
	{GET [/comment/user/statistics/generate]}: generateCommentStatistics(Integer,HttpHeaders)
	{POST [/comment/user/statistics/query]}: queryCommentStatisticsList(UserCommentStatisticsQueryForm,BindingResult,HttpHeaders)
	{GET [/comment/user/statistics/export]}: exportCommentStatisticsList(String,HttpHeaders,HttpServletRequest,HttpServletResponse)
	{POST [/comment/user/statistics/export]}: exportCommentStatisticsList(Long,Integer,String,HttpHeaders,HttpServletResponse)
[14:12:16,748][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.VideoConferenceController:
	{POST [/videoConference/cancelScheduleConferences]}: cancelScheduleConferences(HttpHeaders,VideoConferenceVo)
	{POST [/videoConference/addScheduleConferences]}: addScheduleConferences(HttpHeaders,VideoConferenceVo)
	{POST [/videoConference/editScheduleConferences]}: editScheduleConferences(HttpHeaders,VideoConferenceVo)
	{POST [/videoConference/addVideoConference]}: addVideoConference(HttpHeaders,VideoConferenceVo)
	{GET [/videoConference/join]}: join(HttpHeaders,String)
	{POST [/videoConference/list]}: list(HttpHeaders,VideoConferenceForm)
	{GET [/videoConference/info]}: info(HttpHeaders,Long)
[14:12:16,748][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.WorkflowCallbackController:
	{GET [/workflow_callback]}: wfCallback(long,long,String,long,int,String,Long,HttpHeaders)
[14:12:16,750][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.ApplicationConfigHelperController:
	{GET [/app/config/show/cache]}: show()
	{GET [/app/config/manual/refresh]}: refresh()
[14:12:16,750][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.HikariPoolController:
	{GET [/hikari/status]}: getHikariStatus()
[14:12:16,751][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.UserChangeNameCallBakController:
	{GET [/user_change_name_callback]}: callback(HttpServletRequest,Long,String)
[14:12:16,751][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.UserChangePhoneCallbackController:
	{GET [/user_change_phone_callback]}: callBack(Long,String)
[14:12:16,752][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	s.d.o.w.OpenApiControllerWebMvc:
	{GET [/v3/api-docs], produces [application/json || application/hal+json]}: getDocumentation(String,HttpServletRequest)
[14:12:16,754][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	s.d.s.w.ApiResourceController:
	{GET [/swagger-resources/configuration/security], produces [application/json]}: securityConfiguration()
	{GET [/swagger-resources], produces [application/json]}: swaggerResources()
	{GET [/swagger-resources/configuration/ui], produces [application/json]}: uiConfiguration()
[14:12:16,756][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	s.d.s.w.Swagger2ControllerWebMvc:
	{GET [/v2/api-docs], produces [application/json || application/hal+json]}: getDocumentation(String,HttpServletRequest)
[14:12:16,758][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
[14:12:17,174][INFO ][com.goodsogood.ows.dmconfig.MyBatisInterceptorConfig][main] [] [] === 创建拦截器数组 ===
[14:12:17,492][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 'viewControllerHandlerMapping' {/swagger-ui/=ParameterizableViewController [view="forward:/swagger-ui/index.html"]}
[14:12:17,499][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 'beanNameHandlerMapping' {}
[14:12:17,527][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]], /swagger-ui/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/springfox-swagger-ui/]], /static/**=ResourceHttpRequestHandler [classpath [static/]], /templates/**=ResourceHttpRequestHandler [classpath [templates/]], /swagger-ui.html=ResourceHttpRequestHandler [classpath [META-INF/resources/]]}
[14:12:17,619][INFO ][tk.mybatis.mapper.autoconfigure.MapperCacheDisabler][main] [] [] Clear tk.mybatis.mapper.util.MsUtil CLASS_CACHE cache.
[14:12:17,620][INFO ][tk.mybatis.mapper.autoconfigure.MapperCacheDisabler][main] [] [] Clear tk.mybatis.mapper.genid.GenIdUtil CACHE cache.
[14:12:17,620][INFO ][tk.mybatis.mapper.autoconfigure.MapperCacheDisabler][main] [] [] Clear tk.mybatis.mapper.version.VersionUtil CACHE cache.
[14:12:17,621][INFO ][tk.mybatis.mapper.autoconfigure.MapperCacheDisabler][main] [] [] Clear EntityHelper entityTableMap cache.
[14:12:17,873][DEBUG][com.github.pagehelper.PageInterceptor][main] [] [] 

,------.                           ,--.  ,--.         ,--.                         
|  .--. '  ,--,--.  ,---.   ,---.  |  '--'  |  ,---.  |  |  ,---.   ,---.  ,--.--. 
|  '--' | ' ,-.  | | .-. | | .-. : |  .--.  | | .-. : |  | | .-. | | .-. : |  .--' 
|  | --'  \ '-'  | ' '-' ' \   --. |  |  |  | \   --. |  | | '-' ' \   --. |  |    
`--'       `--`--' .`-  /   `----' `--'  `--'  `----' `--' |  |-'   `----' `--'    
                   `---'                                   `--'                        is intercepting.

[14:12:18,511][WARN ][org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration][main] [] [] Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
[14:12:19,233][WARN ][org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger][main] [] [] Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[14:12:32,831][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:3}
[14:12:32,832][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:4}
[14:12:32,832][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:12:32,832][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:12:52,832][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:5}
[14:12:52,834][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:6}
[14:12:52,834][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:12:52,834][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:13:12,001][ERROR][com.netflix.discovery.DiscoveryClient][DiscoveryClient-HeartbeatExecutor-0] [] [] DiscoveryClient_MEETING-TC/<EMAIL> - was unable to send heartbeat!
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:893) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1457) ~[eureka-client-1.10.17.jar:1.10.17]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:13:12,834][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:7}
[14:13:12,836][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:8}
[14:13:12,836][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:13:12,836][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:13:23,445][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:13:32,837][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:9}
[14:13:32,839][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:10}
[14:13:32,839][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:13:32,839][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:13:41,992][ERROR][com.netflix.discovery.DiscoveryClient][DiscoveryClient-HeartbeatExecutor-0] [] [] DiscoveryClient_MEETING-TC/<EMAIL> - was unable to send heartbeat!
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:893) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1457) ~[eureka-client-1.10.17.jar:1.10.17]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:13:44,572][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Started OwsMeetingApplication in 110.267 seconds (JVM running for 112.088)
[14:13:49,511][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:13:52,839][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:11}
[14:13:52,840][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:12}
[14:13:52,840][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:13:52,840][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:14:02,047][ERROR][org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler][scheduling-1] [] [] Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:292) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:514) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:209) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:768) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at com.goodsogood.ows.service.TopPriorityService.addBySpider(TopPriorityService.kt:833) ~[classes/:?]
	at com.goodsogood.ows.component.TopPriorityScheduler.runner(TopPriorityScheduler.kt:25) ~[classes/:4.0.2-SNAPSHOT]
	at com.goodsogood.ows.component.TopPriorityScheduler$$FastClassBySpringCGLIB$$e0107901.invoke(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.cloud.sleuth.instrument.scheduling.TraceSchedulingAspect.traceBackgroundThread(TraceSchedulingAspect.java:73) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.component.TopPriorityScheduler$$EnhancerBySpringCGLIB$$fc01fccf.runner(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95) ~[spring-context-5.3.20.jar:5.3.20]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:110) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
Caused by: java.net.SocketTimeoutException: Connect timed out
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:80) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
[14:14:10,573][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:14:12,842][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:14}
[14:14:12,842][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:14:12,842][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:13}
[14:14:12,842][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:14:17,780][ERROR][com.goodsogood.ows.configuration.GlobalExceptionHandler][http-nio-8140-exec-1] [] [] Failed to convert value of type 'java.lang.String' to required type 'java.util.Date'; nested exception is org.springframework.core.convert.ConversionFailedException: Failed to convert from type [java.lang.String] to type [@io.swagger.annotations.ApiParam @org.springframework.format.annotation.DateTimeFormat @org.springframework.web.bind.annotation.RequestParam java.util.Date] for value '2025-08-12 14:13:49'; nested exception is java.lang.IllegalArgumentException: Invalid format: "2025-08-12 14:13:49" is malformed at ":49"
org.springframework.web.method.annotation.MethodArgumentTypeMismatchException: Failed to convert value of type 'java.lang.String' to required type 'java.util.Date'; nested exception is org.springframework.core.convert.ConversionFailedException: Failed to convert from type [java.lang.String] to type [@io.swagger.annotations.ApiParam @org.springframework.format.annotation.DateTimeFormat @org.springframework.web.bind.annotation.RequestParam java.util.Date] for value '2025-08-12 14:13:49'; nested exception is java.lang.IllegalArgumentException: Invalid format: "2025-08-12 14:13:49" is malformed at ":49"
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at com.goodsogood.ows.filter.TranslateRequestFilter.doFilter(TranslateRequestFilter.java:34) ~[ows-starter-spring-boot-4.0.2-SNAPSHOT.jar:4.0.2-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:114) ~[spring-cloud-sleuth-autoconfigure-3.0.5.jar:3.0.5]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.5.14.jar:2.5.14]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: org.springframework.core.convert.ConversionFailedException: Failed to convert from type [java.lang.String] to type [@io.swagger.annotations.ApiParam @org.springframework.format.annotation.DateTimeFormat @org.springframework.web.bind.annotation.RequestParam java.util.Date] for value '2025-08-12 14:13:49'; nested exception is java.lang.IllegalArgumentException: Invalid format: "2025-08-12 14:13:49" is malformed at ":49"
	at org.springframework.core.convert.support.ConversionUtils.invokeConverter(ConversionUtils.java:47) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.core.convert.support.GenericConversionService.convert(GenericConversionService.java:192) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.beans.TypeConverterDelegate.convertIfNecessary(TypeConverterDelegate.java:129) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.TypeConverterSupport.convertIfNecessary(TypeConverterSupport.java:73) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.TypeConverterSupport.convertIfNecessary(TypeConverterSupport.java:53) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.validation.DataBinder.convertIfNecessary(DataBinder.java:729) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:125) ~[spring-web-5.3.20.jar:5.3.20]
	... 58 more
Caused by: java.lang.IllegalArgumentException: Invalid format: "2025-08-12 14:13:49" is malformed at ":49"
	at org.joda.time.format.DateTimeFormatter.parseDateTime(DateTimeFormatter.java:945) ~[joda-time-2.10.14.jar:2.10.14]
	at org.springframework.format.datetime.joda.DateTimeParser.parse(DateTimeParser.java:51) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.format.datetime.joda.DateTimeParser.parse(DateTimeParser.java:34) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.format.support.FormattingConversionService$ParserConverter.convert(FormattingConversionService.java:217) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.format.support.FormattingConversionService$AnnotationParserConverter.convert(FormattingConversionService.java:338) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.core.convert.support.ConversionUtils.invokeConverter(ConversionUtils.java:41) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.core.convert.support.GenericConversionService.convert(GenericConversionService.java:192) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.beans.TypeConverterDelegate.convertIfNecessary(TypeConverterDelegate.java:129) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.TypeConverterSupport.convertIfNecessary(TypeConverterSupport.java:73) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.beans.TypeConverterSupport.convertIfNecessary(TypeConverterSupport.java:53) ~[spring-beans-5.3.20.jar:5.3.20]
	at org.springframework.validation.DataBinder.convertIfNecessary(DataBinder.java:729) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:125) ~[spring-web-5.3.20.jar:5.3.20]
	... 58 more
[14:14:32,007][ERROR][com.netflix.discovery.DiscoveryClient][DiscoveryClient-HeartbeatExecutor-0] [] [] DiscoveryClient_MEETING-TC/<EMAIL> - was unable to send heartbeat!
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:893) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1457) ~[eureka-client-1.10.17.jar:1.10.17]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:14:32,844][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:16}
[14:14:32,844][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:15}
[14:14:32,844][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:14:32,844][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:14:36,619][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:14:48,048][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-2] [05bccd24-4413-48f1-85ff-a456fd5c9cd7] [meeting-tc] ctime:1754979288043,tracker_id:05bccd24-4413-48f1-85ff-a456fd5c9cd7,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SR,name:/meeting/type/list,env:{"url":"http://localhost:8140/meeting/type/list","method":"GET","uri":"/meeting/type/list","queryString":null,"requestParams":{}}
[14:14:48,096][WARN ][com.goodsogood.ows.helper.HeaderHelper][http-nio-8140-exec-2] [05bccd24-4413-48f1-85ff-a456fd5c9cd7] [meeting-tc] 请求头创建错误,缺少token[_tk]
[14:14:48,097][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-2] [05bccd24-4413-48f1-85ff-a456fd5c9cd7] [meeting-tc] ctime:1754979288097,tracker_id:05bccd24-4413-48f1-85ff-a456fd5c9cd7,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SS,name:/meeting/type/list,env:{"url":"http://localhost:8140/meeting/type/list","method":"GET","uri":"/meeting/type/list","queryString":null,"requestParams":{}}
[14:14:48,097][ERROR][com.goodsogood.ows.configuration.GlobalExceptionHandler][http-nio-8140-exec-2] [] [] 请求头信息参数有错, _region_id 为空
com.goodsogood.ows.exception.ApiException: 请求头信息参数有错, _region_id 为空
	at com.goodsogood.ows.controller.ControllerHelper.checkNotNull(ControllerHelper.java:62) ~[classes/:?]
	at com.goodsogood.ows.controller.ControllerHelper.getSysHeaderNoCheckHeadersNotNull(ControllerHelper.java:40) ~[classes/:?]
	at com.goodsogood.ows.controller.ControllerHelper.getSysHeader(ControllerHelper.java:21) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController.typeList(MeetingController.java:307) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController$$FastClassBySpringCGLIB$$acb197b3.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.controller.MeetingController$$EnhancerBySpringCGLIB$$272734f4.typeList(<generated>) ~[classes/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at com.goodsogood.ows.filter.TranslateRequestFilter.doFilter(TranslateRequestFilter.java:34) ~[ows-starter-spring-boot-4.0.2-SNAPSHOT.jar:4.0.2-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:114) ~[spring-cloud-sleuth-autoconfigure-3.0.5.jar:3.0.5]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.5.14.jar:2.5.14]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:14:52,848][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:18}
[14:14:52,848][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:17}
[14:14:52,848][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:14:52,848][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:14:57,664][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:15:02,001][ERROR][org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler][scheduling-1] [] [] Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:292) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:514) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:209) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:768) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at com.goodsogood.ows.service.TopPriorityService.addBySpider(TopPriorityService.kt:833) ~[classes/:?]
	at com.goodsogood.ows.component.TopPriorityScheduler.runner(TopPriorityScheduler.kt:25) ~[classes/:4.0.2-SNAPSHOT]
	at com.goodsogood.ows.component.TopPriorityScheduler$$FastClassBySpringCGLIB$$e0107901.invoke(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.cloud.sleuth.instrument.scheduling.TraceSchedulingAspect.traceBackgroundThread(TraceSchedulingAspect.java:73) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.component.TopPriorityScheduler$$EnhancerBySpringCGLIB$$fc01fccf.runner(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95) ~[spring-context-5.3.20.jar:5.3.20]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:110) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
Caused by: java.net.SocketTimeoutException: Connect timed out
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:80) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
[14:15:06,635][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-7] [793b33c3-de3b-4bbe-9332-6c684e75a350] [meeting-tc] ctime:1754979306634,tracker_id:793b33c3-de3b-4bbe-9332-6c684e75a350,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SR,name:/meeting/type/list,env:{"url":"http://localhost:8140/meeting/type/list","method":"GET","uri":"/meeting/type/list","queryString":null,"requestParams":{}}
[14:15:10,607][WARN ][com.goodsogood.ows.helper.HeaderHelper][http-nio-8140-exec-7] [793b33c3-de3b-4bbe-9332-6c684e75a350] [meeting-tc] 请求头创建错误,缺少token[_tk]
[14:15:14,678][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:20}
[14:15:14,678][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:15:14,678][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:19}
[14:15:14,678][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:15:14,683][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-7] [793b33c3-de3b-4bbe-9332-6c684e75a350] [meeting-tc] ctime:1754979314683,tracker_id:793b33c3-de3b-4bbe-9332-6c684e75a350,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SS,name:/meeting/type/list,env:{"url":"http://localhost:8140/meeting/type/list","method":"GET","uri":"/meeting/type/list","queryString":null,"requestParams":{}}
[14:15:14,684][ERROR][com.goodsogood.ows.configuration.GlobalExceptionHandler][http-nio-8140-exec-7] [] [] 请求头信息参数有错, _region_id 为空
com.goodsogood.ows.exception.ApiException: 请求头信息参数有错, _region_id 为空
	at com.goodsogood.ows.controller.ControllerHelper.checkNotNull(ControllerHelper.java:62) ~[classes/:?]
	at com.goodsogood.ows.controller.ControllerHelper.getSysHeaderNoCheckHeadersNotNull(ControllerHelper.java:40) ~[classes/:?]
	at com.goodsogood.ows.controller.ControllerHelper.getSysHeader(ControllerHelper.java:21) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController.typeList(MeetingController.java:307) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController$$FastClassBySpringCGLIB$$acb197b3.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.controller.MeetingController$$EnhancerBySpringCGLIB$$272734f4.typeList(<generated>) ~[classes/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at com.goodsogood.ows.filter.TranslateRequestFilter.doFilter(TranslateRequestFilter.java:34) ~[ows-starter-spring-boot-4.0.2-SNAPSHOT.jar:4.0.2-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:114) ~[spring-cloud-sleuth-autoconfigure-3.0.5.jar:3.0.5]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.5.14.jar:2.5.14]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:15:23,712][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:15:34,680][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:21}
[14:15:34,681][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:22}
[14:15:34,681][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:15:34,681][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:15:44,754][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:15:54,681][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:23}
[14:15:54,682][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:24}
[14:15:54,682][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:15:54,682][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:15:58,670][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-4] [535bb4e5-6435-458c-b835-4b7b0875cd19] [meeting-tc] ctime:1754979358669,tracker_id:535bb4e5-6435-458c-b835-4b7b0875cd19,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SR,name:/meeting/type/list,env:{"url":"http://localhost:8140/meeting/type/list","method":"GET","uri":"/meeting/type/list","queryString":null,"requestParams":{}}
[14:16:02,316][ERROR][com.netflix.discovery.DiscoveryClient][DiscoveryClient-HeartbeatExecutor-0] [] [] DiscoveryClient_MEETING-TC/<EMAIL> - was unable to send heartbeat!
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:893) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1457) ~[eureka-client-1.10.17.jar:1.10.17]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:16:02,319][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-4] [535bb4e5-6435-458c-b835-4b7b0875cd19] [meeting-tc] ctime:1754979362319,tracker_id:535bb4e5-6435-458c-b835-4b7b0875cd19,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SS,name:/meeting/type/list,env:{"url":"http://localhost:8140/meeting/type/list","method":"GET","uri":"/meeting/type/list","queryString":null,"requestParams":{}}
[14:16:02,320][ERROR][com.goodsogood.ows.configuration.GlobalExceptionHandler][http-nio-8140-exec-4] [] [] 请求头信息参数有错, _region_id 为空
com.goodsogood.ows.exception.ApiException: 请求头信息参数有错, _region_id 为空
	at com.goodsogood.ows.controller.ControllerHelper.checkNotNull(ControllerHelper.java:62) ~[classes/:?]
	at com.goodsogood.ows.controller.ControllerHelper.getSysHeaderNoCheckHeadersNotNull(ControllerHelper.java:40) ~[classes/:?]
	at com.goodsogood.ows.controller.ControllerHelper.getSysHeader(ControllerHelper.java:21) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController.typeList(MeetingController.java:307) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController$$FastClassBySpringCGLIB$$acb197b3.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.controller.MeetingController$$EnhancerBySpringCGLIB$$272734f4.typeList(<generated>) ~[classes/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at com.goodsogood.ows.filter.TranslateRequestFilter.doFilter(TranslateRequestFilter.java:34) ~[ows-starter-spring-boot-4.0.2-SNAPSHOT.jar:4.0.2-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:114) ~[spring-cloud-sleuth-autoconfigure-3.0.5.jar:3.0.5]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.5.14.jar:2.5.14]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:16:04,316][ERROR][org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler][scheduling-1] [] [] Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:292) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:514) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:209) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:768) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at com.goodsogood.ows.service.TopPriorityService.addBySpider(TopPriorityService.kt:833) ~[classes/:?]
	at com.goodsogood.ows.component.TopPriorityScheduler.runner(TopPriorityScheduler.kt:25) ~[classes/:4.0.2-SNAPSHOT]
	at com.goodsogood.ows.component.TopPriorityScheduler$$FastClassBySpringCGLIB$$e0107901.invoke(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.cloud.sleuth.instrument.scheduling.TraceSchedulingAspect.traceBackgroundThread(TraceSchedulingAspect.java:73) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.component.TopPriorityScheduler$$EnhancerBySpringCGLIB$$fc01fccf.runner(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95) ~[spring-context-5.3.20.jar:5.3.20]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:110) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
Caused by: java.net.SocketTimeoutException: Connect timed out
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:80) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
[14:16:10,802][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:16:14,683][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:25}
[14:16:14,686][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:26}
[14:16:14,686][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:16:14,686][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:16:31,852][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:16:34,686][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:27}
[14:16:34,689][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:28}
[14:16:34,689][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:16:34,689][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:16:54,688][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:29}
[14:16:54,690][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:30}
[14:16:54,690][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:16:54,690][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:16:57,888][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:17:02,003][ERROR][org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler][scheduling-1] [] [] Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:292) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:514) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:209) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:768) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at com.goodsogood.ows.service.TopPriorityService.addBySpider(TopPriorityService.kt:833) ~[classes/:?]
	at com.goodsogood.ows.component.TopPriorityScheduler.runner(TopPriorityScheduler.kt:25) ~[classes/:4.0.2-SNAPSHOT]
	at com.goodsogood.ows.component.TopPriorityScheduler$$FastClassBySpringCGLIB$$e0107901.invoke(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.cloud.sleuth.instrument.scheduling.TraceSchedulingAspect.traceBackgroundThread(TraceSchedulingAspect.java:73) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.component.TopPriorityScheduler$$EnhancerBySpringCGLIB$$fc01fccf.runner(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95) ~[spring-context-5.3.20.jar:5.3.20]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:110) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
Caused by: java.net.SocketTimeoutException: Connect timed out
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:80) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
[14:17:14,689][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:31}
[14:17:14,694][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:32}
[14:17:14,694][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:17:14,694][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:17:18,921][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:17:25,759][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] ctime:1754979445758,tracker_id:8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SR,name:/meeting/type/list,env:{"url":"http://localhost:8140/meeting/type/list","method":"GET","uri":"/meeting/type/list","queryString":null,"requestParams":{}}
[14:17:25,780][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.CachingExecutor
[14:17:25,838][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.scripting.defaults.DefaultParameterHandler
[14:17:25,842][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.resultset.DefaultResultSetHandler
[14:17:25,842][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.statement.RoutingStatementHandler
[14:17:25,857][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] 🚀🚀🚀 StatementHandler拦截器被调用！🚀🚀🚀
[14:17:25,857][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] StatementHandler拦截到SQL: SELECT a.meeting_task_id, a.meeting_plan_id, a.meeting_require_id,a.type_id,a.category_id,c.code,c.has_lecturer,c.has_lecture_title,c.type_sys, a.org_id,a.p_org_id,a.type,a.category,a.name,a.meeting_num,a.status,a.create_user,a.start_time,a.end_time,   CASE      WHEN a.STATUS = 1  AND a.end_time   >=    CURDATE() THEN 1      WHEN a.STATUS = 1 AND a.end_time   <    CURDATE() THEN 3      ELSE a.STATUS   END AS status_form  FROM t_meeting_task a,t_meeting_plan b,t_type c WHERE a.meeting_plan_id = b.meeting_plan_id and a.type_id=c.type_id                          and a.start_time   <=     CURDATE() and a.end_time   >=    CURDATE()                                                  and a.org_id = ?                                      ORDER BY a.meeting_num ASC,status_form ASC,a.start_time ASC,a.meeting_task_id
[14:17:25,859][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] SQL不需要转换
[14:17:25,859][DEBUG][com.goodsogood.ows.mapper.MeetingTaskMapper.findByForm][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] ==>  Preparing: SELECT a.meeting_task_id, a.meeting_plan_id, a.meeting_require_id,a.type_id,a.category_id,c.code,c.has_lecturer,c.has_lecture_title,c.type_sys, a.org_id,a.p_org_id,a.type,a.category,a.name,a.meeting_num,a.status,a.create_user,a.start_time,a.end_time, CASE WHEN a.STATUS = 1 AND a.end_time >= CURDATE() THEN 1 WHEN a.STATUS = 1 AND a.end_time < CURDATE() THEN 3 ELSE a.STATUS END AS status_form FROM t_meeting_task a,t_meeting_plan b,t_type c WHERE a.meeting_plan_id = b.meeting_plan_id and a.type_id=c.type_id and a.start_time <= CURDATE() and a.end_time >= CURDATE() and a.org_id = ? ORDER BY a.meeting_num ASC,status_form ASC,a.start_time ASC,a.meeting_task_id
[14:17:25,898][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] 🚀🚀🚀 StatementHandler拦截器执行完成！🚀🚀🚀
[14:17:25,905][DEBUG][com.goodsogood.ows.mapper.MeetingTaskMapper.findByForm][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] ==> Parameters: 2(Long)
[14:17:25,937][DEBUG][com.goodsogood.ows.mapper.MeetingTaskMapper.findByForm][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] <==      Total: 0
[14:17:25,943][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.CachingExecutor
[14:17:25,943][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.scripting.defaults.DefaultParameterHandler
[14:17:25,943][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.resultset.DefaultResultSetHandler
[14:17:25,943][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.statement.RoutingStatementHandler
[14:17:25,943][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] 🚀🚀🚀 StatementHandler拦截器被调用！🚀🚀🚀
[14:17:25,943][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] StatementHandler拦截到SQL: SELECT group_id,org_id,org_name,create_time,create_user,update_time,last_change_user FROM t_group where region_id=?
[14:17:25,943][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] SQL不需要转换
[14:17:25,943][DEBUG][com.goodsogood.ows.mapper.GroupMapper.findAll][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] ==>  Preparing: SELECT group_id,org_id,org_name,create_time,create_user,update_time,last_change_user FROM t_group where region_id=?
[14:17:25,959][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] 🚀🚀🚀 StatementHandler拦截器执行完成！🚀🚀🚀
[14:17:25,960][DEBUG][com.goodsogood.ows.mapper.GroupMapper.findAll][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] ==> Parameters: 3(Long)
[14:17:25,971][DEBUG][com.goodsogood.ows.mapper.GroupMapper.findAll][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] <==      Total: 0
[14:17:25,973][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-5] [8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f] [meeting-tc] ctime:1754979445973,tracker_id:8b1470cb-8dd0-48b0-b994-f1bb73ed7f7f,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SS,name:/meeting/type/list,env:{"url":"http://localhost:8140/meeting/type/list","method":"GET","uri":"/meeting/type/list","queryString":null,"requestParams":{}}
[14:17:34,690][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:33}
[14:17:34,697][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:34}
[14:17:34,697][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:17:34,697][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:17:44,970][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:17:52,012][ERROR][com.netflix.discovery.DiscoveryClient][DiscoveryClient-HeartbeatExecutor-0] [] [] DiscoveryClient_MEETING-TC/<EMAIL> - was unable to send heartbeat!
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:893) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1457) ~[eureka-client-1.10.17.jar:1.10.17]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:17:54,692][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:35}
[14:17:54,700][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:36}
[14:17:54,700][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:17:54,700][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:18:02,001][ERROR][org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler][scheduling-1] [] [] Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:292) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:514) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:209) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:768) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at com.goodsogood.ows.service.TopPriorityService.addBySpider(TopPriorityService.kt:833) ~[classes/:?]
	at com.goodsogood.ows.component.TopPriorityScheduler.runner(TopPriorityScheduler.kt:25) ~[classes/:4.0.2-SNAPSHOT]
	at com.goodsogood.ows.component.TopPriorityScheduler$$FastClassBySpringCGLIB$$e0107901.invoke(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.cloud.sleuth.instrument.scheduling.TraceSchedulingAspect.traceBackgroundThread(TraceSchedulingAspect.java:73) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.component.TopPriorityScheduler$$EnhancerBySpringCGLIB$$fc01fccf.runner(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95) ~[spring-context-5.3.20.jar:5.3.20]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:110) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
Caused by: java.net.SocketTimeoutException: Connect timed out
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:80) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
[14:18:05,994][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-7] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:18:14,695][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:37}
[14:18:14,703][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:38}
[14:18:14,703][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:18:14,703][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:18:32,063][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-7] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:18:34,698][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:39}
[14:18:34,706][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:40}
[14:18:34,706][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:18:34,706][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:18:53,099][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-8] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:18:54,700][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:41}
[14:18:54,708][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:42}
[14:18:54,708][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:18:54,708][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:18:57,219][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] ctime:1754979537218,tracker_id:ceb12907-b6df-4e99-a536-ffbff0ff5751,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SR,name:/meeting/type/list,env:{"url":"http://localhost:8140/meeting/type/list","method":"GET","uri":"/meeting/type/list","queryString":null,"requestParams":{}}
[14:18:57,219][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.CachingExecutor
[14:18:57,220][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.scripting.defaults.DefaultParameterHandler
[14:18:57,220][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.resultset.DefaultResultSetHandler
[14:18:57,220][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.statement.RoutingStatementHandler
[14:18:57,227][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] 🚀🚀🚀 StatementHandler拦截器被调用！🚀🚀🚀
[14:18:57,227][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] StatementHandler拦截到SQL: SELECT a.meeting_task_id, a.meeting_plan_id, a.meeting_require_id,a.type_id,a.category_id,c.code,c.has_lecturer,c.has_lecture_title,c.type_sys, a.org_id,a.p_org_id,a.type,a.category,a.name,a.meeting_num,a.status,a.create_user,a.start_time,a.end_time,   CASE      WHEN a.STATUS = 1  AND a.end_time   >=    CURDATE() THEN 1      WHEN a.STATUS = 1 AND a.end_time   <    CURDATE() THEN 3      ELSE a.STATUS   END AS status_form  FROM t_meeting_task a,t_meeting_plan b,t_type c WHERE a.meeting_plan_id = b.meeting_plan_id and a.type_id=c.type_id                          and a.start_time   <=     CURDATE() and a.end_time   >=    CURDATE()                                                  and a.org_id = ?                                      ORDER BY a.meeting_num ASC,status_form ASC,a.start_time ASC,a.meeting_task_id
[14:18:57,227][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] SQL不需要转换
[14:18:57,227][DEBUG][com.goodsogood.ows.mapper.MeetingTaskMapper.findByForm][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] ==>  Preparing: SELECT a.meeting_task_id, a.meeting_plan_id, a.meeting_require_id,a.type_id,a.category_id,c.code,c.has_lecturer,c.has_lecture_title,c.type_sys, a.org_id,a.p_org_id,a.type,a.category,a.name,a.meeting_num,a.status,a.create_user,a.start_time,a.end_time, CASE WHEN a.STATUS = 1 AND a.end_time >= CURDATE() THEN 1 WHEN a.STATUS = 1 AND a.end_time < CURDATE() THEN 3 ELSE a.STATUS END AS status_form FROM t_meeting_task a,t_meeting_plan b,t_type c WHERE a.meeting_plan_id = b.meeting_plan_id and a.type_id=c.type_id and a.start_time <= CURDATE() and a.end_time >= CURDATE() and a.org_id = ? ORDER BY a.meeting_num ASC,status_form ASC,a.start_time ASC,a.meeting_task_id
[14:18:57,235][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] 🚀🚀🚀 StatementHandler拦截器执行完成！🚀🚀🚀
[14:18:57,236][DEBUG][com.goodsogood.ows.mapper.MeetingTaskMapper.findByForm][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] ==> Parameters: 2(Long)
[14:18:57,245][DEBUG][com.goodsogood.ows.mapper.MeetingTaskMapper.findByForm][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] <==      Total: 0
[14:18:57,245][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.CachingExecutor
[14:18:57,245][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.scripting.defaults.DefaultParameterHandler
[14:18:57,245][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.resultset.DefaultResultSetHandler
[14:18:57,245][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.statement.RoutingStatementHandler
[14:18:57,245][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] 🚀🚀🚀 StatementHandler拦截器被调用！🚀🚀🚀
[14:18:57,245][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] StatementHandler拦截到SQL: SELECT group_id,org_id,org_name,create_time,create_user,update_time,last_change_user FROM t_group where region_id=?
[14:18:57,245][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] SQL不需要转换
[14:18:57,245][DEBUG][com.goodsogood.ows.mapper.GroupMapper.findAll][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] ==>  Preparing: SELECT group_id,org_id,org_name,create_time,create_user,update_time,last_change_user FROM t_group where region_id=?
[14:18:57,253][INFO ][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] 🚀🚀🚀 StatementHandler拦截器执行完成！🚀🚀🚀
[14:18:57,253][DEBUG][com.goodsogood.ows.mapper.GroupMapper.findAll][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] ==> Parameters: 3(Long)
[14:18:57,260][DEBUG][com.goodsogood.ows.mapper.GroupMapper.findAll][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] <==      Total: 0
[14:18:57,260][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-8] [ceb12907-b6df-4e99-a536-ffbff0ff5751] [meeting-tc] ctime:1754979537260,tracker_id:ceb12907-b6df-4e99-a536-ffbff0ff5751,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SS,name:/meeting/type/list,env:{"url":"http://localhost:8140/meeting/type/list","method":"GET","uri":"/meeting/type/list","queryString":null,"requestParams":{}}
[14:19:02,004][ERROR][org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler][scheduling-1] [] [] Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:292) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:514) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:209) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:768) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at com.goodsogood.ows.service.TopPriorityService.addBySpider(TopPriorityService.kt:833) ~[classes/:?]
	at com.goodsogood.ows.component.TopPriorityScheduler.runner(TopPriorityScheduler.kt:25) ~[classes/:4.0.2-SNAPSHOT]
	at com.goodsogood.ows.component.TopPriorityScheduler$$FastClassBySpringCGLIB$$e0107901.invoke(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.cloud.sleuth.instrument.scheduling.TraceSchedulingAspect.traceBackgroundThread(TraceSchedulingAspect.java:73) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.component.TopPriorityScheduler$$EnhancerBySpringCGLIB$$fc01fccf.runner(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95) ~[spring-context-5.3.20.jar:5.3.20]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:110) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
Caused by: java.net.SocketTimeoutException: Connect timed out
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:80) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
[14:19:14,702][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:43}
[14:19:14,710][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:44}
[14:19:14,710][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:19:14,710][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:19:19,154][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-8] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:19:34,706][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:45}
[14:19:34,714][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:46}
[14:19:34,714][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:19:34,714][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:19:40,200][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-9] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:19:41,992][ERROR][com.netflix.discovery.DiscoveryClient][DiscoveryClient-HeartbeatExecutor-0] [] [] DiscoveryClient_MEETING-TC/<EMAIL> - was unable to send heartbeat!
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:893) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1457) ~[eureka-client-1.10.17.jar:1.10.17]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:19:54,068][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-10] [6dd6230f-d28e-4fd9-8205-3b10eff8f4a9] [meeting-tc] ctime:1754979594067,tracker_id:6dd6230f-d28e-4fd9-8205-3b10eff8f4a9,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SR,name:/meeting/list-all,env:{"url":"http://localhost:8140/meeting/list-all","method":"GET","uri":"/meeting/list-all","queryString":null,"requestParams":{}}
[14:19:54,077][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-10] [6dd6230f-d28e-4fd9-8205-3b10eff8f4a9] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.CachingExecutor
[14:19:54,091][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-10] [6dd6230f-d28e-4fd9-8205-3b10eff8f4a9] [meeting-tc] ctime:1754979594091,tracker_id:6dd6230f-d28e-4fd9-8205-3b10eff8f4a9,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SS,name:/meeting/list-all,env:{"url":"http://localhost:8140/meeting/list-all","method":"GET","uri":"/meeting/list-all","queryString":null,"requestParams":{}}
[14:19:54,093][ERROR][com.goodsogood.ows.configuration.GlobalExceptionHandler][http-nio-8140-exec-10] [] [] nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96) ~[mybatis-spring-2.0.7.jar:2.0.7]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.7.jar:2.0.7]
	at jdk.proxy2.$Proxy252.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.7.jar:2.0.7]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.proxy2.$Proxy284.findAll(Unknown Source) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215) ~[spring-aop-5.3.20.jar:5.3.20]
	at jdk.proxy2.$Proxy285.findAll(Unknown Source) ~[?:?]
	at com.goodsogood.ows.service.MeetingRedisService.listAll(MeetingRedisService.java:115) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController.listAllMeeting(MeetingController.java:459) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController$$FastClassBySpringCGLIB$$acb197b3.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.controller.MeetingController$$EnhancerBySpringCGLIB$$272734f4.listAllMeeting(<generated>) ~[classes/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at com.goodsogood.ows.filter.TranslateRequestFilter.doFilter(TranslateRequestFilter.java:34) ~[ows-starter-spring-boot-4.0.2-SNAPSHOT.jar:4.0.2-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:114) ~[spring-cloud-sleuth-autoconfigure-3.0.5.jar:3.0.5]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.5.14.jar:2.5.14]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.7.jar:2.0.7]
	... 100 more
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:354) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:297) ~[?:?]
	at java.lang.reflect.Method.checkCanSetAccessible(Method.java:200) ~[?:?]
	at java.lang.reflect.Method.setAccessible(Method.java:194) ~[?:?]
	at org.apache.ibatis.ognl.AccessibleObjectHandlerPreJDK9.setAccessible(AccessibleObjectHandlerPreJDK9.java:58) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.OgnlRuntime.invokeMethod(OgnlRuntime.java:1211) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.OgnlRuntime.callAppropriateMethod(OgnlRuntime.java:1962) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ObjectMethodAccessor.callMethod(ObjectMethodAccessor.java:68) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.OgnlRuntime.callMethod(OgnlRuntime.java:2038) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTMethod.getValueBody(ASTMethod.java:97) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTChain.getValueBody(ASTChain.java:141) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTGreater.getValueBody(ASTGreater.java:50) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTAnd.getValueBody(ASTAnd.java:61) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:586) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:550) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.OgnlCache.getValue(OgnlCache.java:46) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.ExpressionEvaluator.evaluateBoolean(ExpressionEvaluator.java:32) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.IfSqlNode.apply(IfSqlNode.java:34) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.lambda$apply$0(MixedSqlNode.java:32) ~[mybatis-3.5.9.jar:3.5.9]
	at java.util.ArrayList.forEach(ArrayList.java:1511) ~[?:?]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.apply(MixedSqlNode.java:32) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.DynamicSqlSource.getBoundSql(DynamicSqlSource.java:39) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.mapping.MappedStatement.getBoundSql(MappedStatement.java:305) ~[mybatis-3.5.9.jar:3.5.9]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:103) ~[pagehelper-5.3.1.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.proxy2.$Proxy527.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.7.jar:2.0.7]
	... 100 more
[14:19:54,708][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:47}
[14:19:54,718][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:48}
[14:19:54,718][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:19:54,718][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:19:59,690][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-1] [7dea065f-813a-4287-8273-e83504f129bd] [meeting-tc] ctime:1754979599690,tracker_id:7dea065f-813a-4287-8273-e83504f129bd,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SR,name:/meeting/list-all,env:{"url":"http://localhost:8140/meeting/list-all","method":"GET","uri":"/meeting/list-all","queryString":null,"requestParams":{}}
[14:19:59,691][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-1] [7dea065f-813a-4287-8273-e83504f129bd] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.CachingExecutor
[14:19:59,692][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-1] [7dea065f-813a-4287-8273-e83504f129bd] [meeting-tc] ctime:1754979599692,tracker_id:7dea065f-813a-4287-8273-e83504f129bd,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SS,name:/meeting/list-all,env:{"url":"http://localhost:8140/meeting/list-all","method":"GET","uri":"/meeting/list-all","queryString":null,"requestParams":{}}
[14:19:59,692][ERROR][com.goodsogood.ows.configuration.GlobalExceptionHandler][http-nio-8140-exec-1] [] [] nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96) ~[mybatis-spring-2.0.7.jar:2.0.7]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.7.jar:2.0.7]
	at jdk.proxy2.$Proxy252.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.7.jar:2.0.7]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.proxy2.$Proxy284.findAll(Unknown Source) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215) ~[spring-aop-5.3.20.jar:5.3.20]
	at jdk.proxy2.$Proxy285.findAll(Unknown Source) ~[?:?]
	at com.goodsogood.ows.service.MeetingRedisService.listAll(MeetingRedisService.java:115) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController.listAllMeeting(MeetingController.java:459) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController$$FastClassBySpringCGLIB$$acb197b3.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.controller.MeetingController$$EnhancerBySpringCGLIB$$272734f4.listAllMeeting(<generated>) ~[classes/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at com.goodsogood.ows.filter.TranslateRequestFilter.doFilter(TranslateRequestFilter.java:34) ~[ows-starter-spring-boot-4.0.2-SNAPSHOT.jar:4.0.2-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:114) ~[spring-cloud-sleuth-autoconfigure-3.0.5.jar:3.0.5]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.5.14.jar:2.5.14]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.7.jar:2.0.7]
	... 100 more
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:354) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:297) ~[?:?]
	at java.lang.reflect.Method.checkCanSetAccessible(Method.java:200) ~[?:?]
	at java.lang.reflect.Method.setAccessible(Method.java:194) ~[?:?]
	at org.apache.ibatis.ognl.AccessibleObjectHandlerPreJDK9.setAccessible(AccessibleObjectHandlerPreJDK9.java:58) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.OgnlRuntime.invokeMethod(OgnlRuntime.java:1211) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.OgnlRuntime.callAppropriateMethod(OgnlRuntime.java:1962) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ObjectMethodAccessor.callMethod(ObjectMethodAccessor.java:68) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.OgnlRuntime.callMethod(OgnlRuntime.java:2038) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTMethod.getValueBody(ASTMethod.java:97) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTChain.getValueBody(ASTChain.java:141) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTGreater.getValueBody(ASTGreater.java:50) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTAnd.getValueBody(ASTAnd.java:61) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:586) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:550) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.OgnlCache.getValue(OgnlCache.java:46) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.ExpressionEvaluator.evaluateBoolean(ExpressionEvaluator.java:32) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.IfSqlNode.apply(IfSqlNode.java:34) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.lambda$apply$0(MixedSqlNode.java:32) ~[mybatis-3.5.9.jar:3.5.9]
	at java.util.ArrayList.forEach(ArrayList.java:1511) ~[?:?]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.apply(MixedSqlNode.java:32) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.DynamicSqlSource.getBoundSql(DynamicSqlSource.java:39) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.mapping.MappedStatement.getBoundSql(MappedStatement.java:305) ~[mybatis-3.5.9.jar:3.5.9]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:103) ~[pagehelper-5.3.1.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.proxy2.$Proxy527.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.7.jar:2.0.7]
	... 100 more
[14:20:02,003][ERROR][org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler][scheduling-1] [] [] Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:292) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:514) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:209) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:768) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at com.goodsogood.ows.service.TopPriorityService.addBySpider(TopPriorityService.kt:833) ~[classes/:?]
	at com.goodsogood.ows.component.TopPriorityScheduler.runner(TopPriorityScheduler.kt:25) ~[classes/:4.0.2-SNAPSHOT]
	at com.goodsogood.ows.component.TopPriorityScheduler$$FastClassBySpringCGLIB$$e0107901.invoke(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.cloud.sleuth.instrument.scheduling.TraceSchedulingAspect.traceBackgroundThread(TraceSchedulingAspect.java:73) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.component.TopPriorityScheduler$$EnhancerBySpringCGLIB$$fc01fccf.runner(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95) ~[spring-context-5.3.20.jar:5.3.20]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:110) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
Caused by: java.net.SocketTimeoutException: Connect timed out
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:80) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
[14:20:06,253][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-9] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:20:14,710][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:49}
[14:20:14,719][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:50}
[14:20:14,719][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:20:14,719][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:20:27,296][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-10] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:20:34,713][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:51}
[14:20:34,722][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:52}
[14:20:34,722][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:20:34,722][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:20:53,341][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-10] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:20:54,717][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:53}
[14:20:54,725][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:54}
[14:20:54,725][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:20:54,725][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:21:02,002][ERROR][org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler][scheduling-1] [] [] Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:292) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:514) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:209) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:768) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at com.goodsogood.ows.service.TopPriorityService.addBySpider(TopPriorityService.kt:833) ~[classes/:?]
	at com.goodsogood.ows.component.TopPriorityScheduler.runner(TopPriorityScheduler.kt:25) ~[classes/:4.0.2-SNAPSHOT]
	at com.goodsogood.ows.component.TopPriorityScheduler$$FastClassBySpringCGLIB$$e0107901.invoke(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.cloud.sleuth.instrument.scheduling.TraceSchedulingAspect.traceBackgroundThread(TraceSchedulingAspect.java:73) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.component.TopPriorityScheduler$$EnhancerBySpringCGLIB$$fc01fccf.runner(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95) ~[spring-context-5.3.20.jar:5.3.20]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:110) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
Caused by: java.net.SocketTimeoutException: Connect timed out
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:80) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
[14:21:14,384][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-11] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:21:14,720][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:55}
[14:21:14,727][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:56}
[14:21:14,727][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:21:14,727][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:21:31,997][ERROR][com.netflix.discovery.DiscoveryClient][DiscoveryClient-HeartbeatExecutor-0] [] [] DiscoveryClient_MEETING-TC/<EMAIL> - was unable to send heartbeat!
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:893) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1457) ~[eureka-client-1.10.17.jar:1.10.17]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:21:34,722][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:57}
[14:21:34,729][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:58}
[14:21:34,729][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:21:34,729][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:21:40,422][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-11] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:21:54,725][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:59}
[14:21:54,731][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:60}
[14:21:54,731][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:21:54,731][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689adb321cdef9525c1809f0', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:22:01,461][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-12] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:22:02,003][ERROR][org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler][scheduling-1] [] [] Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:292) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:514) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:209) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:768) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at com.goodsogood.ows.service.TopPriorityService.addBySpider(TopPriorityService.kt:833) ~[classes/:?]
	at com.goodsogood.ows.component.TopPriorityScheduler.runner(TopPriorityScheduler.kt:25) ~[classes/:4.0.2-SNAPSHOT]
	at com.goodsogood.ows.component.TopPriorityScheduler$$FastClassBySpringCGLIB$$e0107901.invoke(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.cloud.sleuth.instrument.scheduling.TraceSchedulingAspect.traceBackgroundThread(TraceSchedulingAspect.java:73) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.component.TopPriorityScheduler$$EnhancerBySpringCGLIB$$fc01fccf.runner(<generated>) ~[classes/:4.0.2-SNAPSHOT]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95) ~[spring-context-5.3.20.jar:5.3.20]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: Failed to create socket.
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:110) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
Caused by: java.net.SocketTimeoutException: Connect timed out
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at redis.clients.jedis.DefaultJedisSocketFactory.createSocket(DefaultJedisSocketFactory.java:80) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Connection.connect(Connection.java:226) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:135) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:309) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:87) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:82) ~[jedis-3.6.3.jar:?]
	at redis.clients.jedis.Jedis.<init>(Jedis.java:54) ~[jedis-3.6.3.jar:?]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302) ~[spring-data-redis-2.5.11.jar:2.5.11]
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286) ~[spring-data-redis-2.5.11.jar:2.5.11]
	... 44 more
[14:22:11,041][DEBUG][org.mongodb.driver.connection][SpringApplicationShutdownHook] [] [] Closing connection connectionId{localValue:61}
[14:22:18,295][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[14:22:18,361][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 20968 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[14:22:18,361][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[14:22:18,362][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[14:22:22,364][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:22:22,365][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:22:24,973][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[14:22:27,905][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[14:22:27,983][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[14:22:28,203][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:22:28,872][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[14:22:32,579][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[14:22:32,580][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[14:22:32,580][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[14:22:32,582][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[14:22:47,129][DEBUG][org.jboss.logging        ][background-preinit] [] [] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[14:22:47,205][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Starting OwsMeetingApplication using Java 17.0.13 on zhy with PID 19952 (D:\gsyw\zy\ows-meeting\target\classes started by A in D:\gsyw\zy\ows-meeting)
[14:22:47,206][DEBUG][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Running with Spring Boot v2.5.14, Spring v5.3.20
[14:22:47,206][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] No active profile set, falling back to 1 default profile: "default"
[14:22:53,276][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:22:53,277][DEBUG][reactor.core.publisher.Hooks][main] [] [] Hooking onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:22:54,963][DEBUG][io.micrometer.core.util.internal.logging.InternalLoggerFactory][main] [] [] Using SLF4J as the default logging framework
[14:22:56,296][INFO ][org.mongodb.driver.cluster][main] [] [] Cluster created with settings {hosts=[**************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[14:22:56,342][DEBUG][org.mongodb.driver.cluster][main] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING}]
[14:22:56,484][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:22:56,917][WARN ][org.springframework.data.convert.CustomConversions][main] [] [] Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization...
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] Using logger: net.sf.log4jdbc.log.log4j2.Log4j2SpyLogDelegator
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.debug.stack.prefix is not defined
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.warn.threshold is not defined
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.sqltiming.error.threshold is not defined
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.booleanastruefalse is not defined (using default value false)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.maxlinelength is not defined (using default of 90)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.fulldebugstacktrace is not defined (using default value false)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.statement.warn is not defined (using default value false)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.select is not defined (using default value true)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.insert is not defined (using default value true)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.update is not defined (using default value true)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.delete is not defined (using default value true)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.create is not defined (using default value true)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.dump.sql.addsemicolon is not defined (using default value false)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.auto.load.popular.drivers = false
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] []   log4jdbc.drivers = com.mysql.cj.jdbc.Driver
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] []     will look for specific driver com.mysql.cj.jdbc.Driver
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql is not defined (using default value true)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.trim.sql.extrablanklines is not defined (using default value true)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] x log4jdbc.suppress.generated.keys.exception is not defined (using default value false)
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] log4jdbc-logj2 properties initialization done.
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization...
[14:22:58,573][DEBUG][log4jdbc.debug           ][main] [] []   FOUND DRIVER com.mysql.cj.jdbc.Driver
[14:22:58,575][DEBUG][log4jdbc.debug           ][main] [] [] DriverSpy intialization done.
[14:23:02,811][WARN ][tk.mybatis.mapper.mapperhelper.resolve.DefaultEntityResolve][main] [] [] 通用 Mapper 警告信息: <[EntityColumn{table=t_meeting_comment, property='excellentNum', column='excellent_num', javaType=int, jdbcType=null, typeHandler=null, id=false, identity=false, blob=false, generator='null', orderBy='null', orderPriority='0', insertable=true, updatable=true, order=DEFAULT}]> 使用了基本类型，基本类型在动态 SQL 中由于存在默认值，因此任何时候都不等于 null，建议修改基本类型为对应的包装类型!
[14:23:02,811][WARN ][tk.mybatis.mapper.mapperhelper.resolve.DefaultEntityResolve][main] [] [] 通用 Mapper 警告信息: <[EntityColumn{table=t_meeting_comment, property='status', column='status', javaType=int, jdbcType=null, typeHandler=null, id=false, identity=false, blob=false, generator='null', orderBy='null', orderPriority='0', insertable=true, updatable=true, order=DEFAULT}]> 使用了基本类型，基本类型在动态 SQL 中由于存在默认值，因此任何时候都不等于 null，建议修改基本类型为对应的包装类型!
[14:23:05,442][INFO ][com.goodsogood.ows.component.DingEventSyncScheduler][main] [] [] 有日程的活动结束后同步钉钉日程的签到状态定时任务初始化成功！
[14:23:06,359][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:1}
[14:23:06,359][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:2}
[14:23:06,359][INFO ][org.mongodb.driver.cluster][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Exception in monitor thread while connecting to server **************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70) ~[mongodb-driver-core-4.2.3.jar:?]
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:143) ~[mongodb-driver-core-4.2.3.jar:?]
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188) ~[mongodb-driver-core-4.2.3.jar:?]
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144) ~[mongodb-driver-core-4.2.3.jar:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.SocketTimeoutException: Connect timed out
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:551) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:107) ~[mongodb-driver-core-4.2.3.jar:?]
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79) ~[mongodb-driver-core-4.2.3.jar:?]
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65) ~[mongodb-driver-core-4.2.3.jar:?]
	... 4 more
[14:23:06,360][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:23:06,360][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:23:07,237][WARN ][tk.mybatis.mapper.mapperhelper.resolve.DefaultEntityResolve][main] [] [] 通用 Mapper 警告信息: <[EntityColumn{table=t_meeting_talk, property='talkType', column='talk_type', javaType=int, jdbcType=null, typeHandler=null, id=false, identity=false, blob=false, generator='null', orderBy='null', orderPriority='0', insertable=true, updatable=true, order=DEFAULT}]> 使用了基本类型，基本类型在动态 SQL 中由于存在默认值，因此任何时候都不等于 null，建议修改基本类型为对应的包装类型!
[14:23:07,237][WARN ][tk.mybatis.mapper.mapperhelper.resolve.DefaultEntityResolve][main] [] [] 通用 Mapper 警告信息: <[EntityColumn{table=t_meeting_talk, property='source', column='source', javaType=int, jdbcType=null, typeHandler=null, id=false, identity=false, blob=false, generator='null', orderBy='null', orderPriority='0', insertable=true, updatable=true, order=DEFAULT}]> 使用了基本类型，基本类型在动态 SQL 中由于存在默认值，因此任何时候都不等于 null，建议修改基本类型为对应的包装类型!
[14:23:07,413][WARN ][tk.mybatis.mapper.mapperhelper.resolve.DefaultEntityResolve][main] [] [] 通用 Mapper 警告信息: <[EntityColumn{table=count_meeting_talk_vo, property='peopleNum', column='people_num', javaType=int, jdbcType=null, typeHandler=null, id=false, identity=false, blob=false, generator='null', orderBy='null', orderPriority='0', insertable=true, updatable=true, order=DEFAULT}]> 使用了基本类型，基本类型在动态 SQL 中由于存在默认值，因此任何时候都不等于 null，建议修改基本类型为对应的包装类型!
[14:23:10,218][WARN ][org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration][main] [] [] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[14:23:10,369][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.ValidateExceptionHandler:
	
[14:23:10,381][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CategoryController:
	{DELETE [/category/del/{id}]}: delCategory(HttpHeaders,long)
	{POST [/category/update]}: updateCategory(HttpHeaders,CategoryUpdateForm,BindingResult)
	{GET [/category/list]}: listAllCategory(HttpHeaders,Long,String)
	{POST [/category/add]}: addCategory(HttpHeaders,CategoryAddForm,BindingResult)
	{GET [/category/list-all/{tag}]}: listAll(HttpHeaders,short)
[14:23:10,388][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentApproveController:
	{GET [/comment-approve/get]}: getComment(long)
	{POST [/comment-approve/approve]}: approveComment(ApproveCommentInfoForm,BindingResult,HttpHeaders)
	{POST [/comment-approve/list]}: selectApproveList(CommentApproveQueryForm,BindingResult,HttpHeaders)
[14:23:10,391][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentController:
	{GET [/comment/get]}: getComment(Long,HttpHeaders)
	{GET [/comment/start]}: startComment(Long,long,int,int,HttpHeaders)
	{GET [/comment/auto-genera-comment]}: autoGeneraComment(int,HttpHeaders)
	{GET [/comment/query-list-by-ids]}: queryCommentListById(List,HttpHeaders)
	{GET [/comment/flush-appraisal]}: flushCommentAppraisalFile(long,HttpHeaders)
	{GET [/comment/query-list]}: queryCommentList(Integer,Long,Integer,int,int,HttpHeaders)
	{GET [/comment/excellent/get]}: getExcellent(Long,long,HttpHeaders)
	{GET [/comment/is-submit]}: isSubmitComment(long,HttpHeaders)
	{GET [/comment/submit]}: submitComment(long,HttpHeaders)
	{GET [/comment/excellent/set]}: setExcellent(long,int,HttpHeaders)
	{GET [/comment/flush-grade]}: flushGradeFile(long,HttpHeaders)
[14:23:10,394][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentMemberAppraisalController:
	{GET [/comment-member-appraisal/get]}: getCommentMemberAppraisal(Long,Long,HttpHeaders)
	{POST [/comment-member-appraisal/batch-deal]}: batchInputAppraisal(MemberAppraisalBatchForm,BindingResult,HttpHeaders)
	{POST [/comment-member-appraisal/edit]}: insertCommentMemberAppraisal(MemberAppraisalForm,BindingResult,HttpHeaders)
	{GET [/comment-member-appraisal/appraisal-statistical]}: appraisalStatistical(long,HttpHeaders)
	{POST [/comment-member-appraisal/user-list]}: getCommentMember(MemberAppraisalQueryVO,BindingResult,HttpHeaders)
	{GET [/comment-member-appraisal/no-appraisal]}: noAppraisalNum(long,HttpHeaders)
	{GET [/comment-member-appraisal/notice-no-appraisal]}: noticeNoAppraisal(long,HttpHeaders)
	{GET [/comment-member-appraisal/submit]}: submitAppraisal(long,HttpHeaders)
[14:23:10,396][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentMemberComplexController:
	{POST [/comment-member-complex/batch-insert]}: batchInsertMemberComplex(CommentMemberComplexBatchAddForm,BindingResult,HttpHeaders)
	{POST [/comment-member-complex/insert]}: insertMemberComplex(CommentMemberComplexAddForm,BindingResult,HttpHeaders)
	{GET [/comment-member-complex/get]}: getMemberComplex(long,HttpHeaders)
	{GET [/comment-member-complex/complex-statistical]}: complexStatistical(long,HttpHeaders)
[14:23:10,397][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentMemberController:
	{POST [/comment-member/query-list]}: getCommentMemberList(CommentMemberVO,BindingResult,HttpHeaders)
	{POST [/comment-member/insert]}: insertCommentMember(CommentMemberForm,BindingResult,HttpHeaders)
	{GET [/comment-member/get-member-status]}: getCommentMemberStatus(HttpHeaders)
	{GET [/comment-member/get]}: getCommentMember(Long,Long,HttpHeaders)
	{POST [/comment-member/add-member]}: addCommentMember(DealMemberVO,BindingResult,HttpHeaders)
	{POST [/comment-member/del-member]}: delCommentMember(DealMemberVO,BindingResult,HttpHeaders)
	{GET [/comment-member/no-self]}: noSelf(long,HttpHeaders)
	{GET [/comment-member/notice-no-self]}: noticeNoSelf(long,HttpHeaders)
	{GET [/comment-member/self-statistical]}: selfStatistical(long,HttpHeaders)
[14:23:10,399][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.CommentStaticController:
	{POST [/comment/statics/query_selftemplate]}: querySelfCommentFile(CommentMemberVO,List,HttpHeaders)
	{GET [/comment/statics/query_template]}: queryCommentFile(List,List,HttpHeaders)
	{GET [/comment/statics/down_statics]}: downStaticsReport(Integer,Long,List,HttpHeaders,HttpServletResponse)
	{GET [/comment/statics/query]}: queryStatics(Integer,Long,Integer,Integer,HttpHeaders)
[14:23:10,399][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.GloryController:
	{GET [/glory/pull-data]}: queryCommentListById(Long,Integer,HttpHeaders)
[14:23:10,400][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.GroupController:
	{DELETE [/type-group/del/{id}]}: delTypeGroup(HttpHeaders,long)
	{POST [/type-group/add]}: addTypeGroup(HttpHeaders,GroupAddForm,BindingResult)
	{GET [/type-group/list]}: listTypeGroup(HttpHeaders,Integer,Integer)
	{GET [/type-group/list-all]}: listAllTypeGroup(HttpHeaders)
[14:23:10,400][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.IndexController:
	{GET [/index]}: addMeeting(HttpHeaders)
[14:23:10,403][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.LifeController:
	{GET [/life/list]}: list(HttpHeaders,String,Long,List,Integer,List,Integer,Integer,Integer)
	{GET [/life/finish]}: finish(HttpHeaders,Long)
	{POST [/life/tag]}: tag(HttpHeaders,LifeTagManageForm,BindingResult)
	{GET [/life/del]}: del(HttpHeaders,Long)
	{GET [/life/title]}: title(Long)
	{GET [/life/advice]}: advice(Long,Integer)
	{POST [/life/add_edit]}: addOrEdit(HttpHeaders,LifeAddEditForm,BindingResult)
	{GET [/life/delete_special]}: deleteSpecial(HttpHeaders,Long,Integer,Integer,List,Integer)
	{GET [/life/check_self]}: checkSelf(Long,Integer)
	{GET [/life/in]}: inTheMeeting(Long)
	{POST [/life/save_check]}: saveCheck(HttpHeaders,Long,Integer,List)
	{GET [/life/change]}: changeStatus(HttpHeaders,Long,Integer,Integer)
	{GET [/life/only_file]}: onlyFile(Long,Integer,List)
	{GET [/life/join_life]}: joinLifeByActivity(Long,List,Integer,Integer,Integer,HttpHeaders)
[14:23:10,405][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.LifeFileController:
	{GET [/life/file/clone]}: test(Long)
	{POST [/life/file/delete_talk_attach]}: deleteTalkFile(SaveAttachForm)
	{GET [/life/file/query_uploader]}: queryUploader(HttpHeaders,Long,Integer,Long)
	{GET [/life/file/open_task]}: openTask(HttpHeaders,String)
	{GET [/life/file/delete_uploader]}: deleteUploader(HttpHeaders,Long,Long,Integer,Long)
	{POST [/life/file/file_task]}: fileTask(HttpHeaders,LifeFileTaskForm)
	{POST [/life/file/save_attach]}: saveAttach(HttpHeaders,SaveAttachForm,BindingResult)
	{GET [/life/file/delete_attach]}: deleteAttach(HttpHeaders,Long,List,Integer)
	{GET [/life/file/query_attach]}: queryAttach(HttpHeaders,Long,List,Integer)
	{GET [/life/file/file_info]}: queryFileInfo(HttpHeaders,Long,Integer)
	{POST [/life/file/report_attach]}: reportAttach(HttpHeaders,Long,Map)
[14:23:10,409][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingController:
	{GET [/meeting/index]}: index(HttpHeaders)
	{GET [/meeting/review]}: list(HttpHeaders,String,List,Integer,Date,Date,Short,String,List,List,List,Date,Date,Integer,Integer,Integer)
	{GET [/meeting/type/list]}: typeList(HttpHeaders,Long,Long,Long,Long,Date)
	{POST [/meeting/add-user]}: addUser(HttpHeaders,MeetingAddUserForm,BindingResult)
	{POST [/meeting/notice]}: notice(MeetingNoticeForm,BindingResult,HttpHeaders)
	{GET [/meeting/statistical/user-front-page]}: statisticalUserJoinTimes(HttpHeaders,Integer)
	{GET [/meeting/hs/{meeting_id}]}: getMeetingHistoryList(long)
	{GET [/meeting/statistical/org-front-page]}: statisticalOrgJoinTimes(HttpHeaders,Integer,Long)
	{POST [/meeting/add]}: addMeeting(HttpHeaders,MeetingEntity,BindingResult)
	{GET [/meeting/scheduleList]}: getScheduleList(HttpHeaders,String,String)
	{DELETE [/meeting/revoke/{id}]}: revokeMeeting(HttpHeaders,long)
	{POST [/meeting/update]}: updateMeeting(HttpHeaders,MeetingEntity,BindingResult)
	{DELETE [/meeting/cancel/{id}]}: cancelMeeting(HttpHeaders,long)
	{DELETE [/meeting/del/{id}]}: delMeeting(HttpHeaders,long)
	{GET [/meeting/sign-qr-code]}: signQrCod(HttpServletResponse,Long,Integer,Integer)
	{GET [/meeting/sign-list]}: signList(Long)
	{POST [/meeting/sign-in]}: signIn(HttpHeaders,MeetingSignInForm,BindingResult)
	{GET [/meeting/report]}: meetingReport(HttpServletResponse,HttpHeaders,List,String,String,List,Integer,Date,Date,Short,List,List,List,Date,Date,Integer)
	{POST [/meeting/tag/edit]}: tagEdit(HttpHeaders,TagEditForm,BindingResult)
	{GET [/meeting/query-meeting-task]}: queryMeetingTask(HttpHeaders,Long,Long,String,String)
	{GET [/meeting/tag/del]}: tagDel(HttpHeaders,Long)
	{POST [/meeting/update-agenda]}: updateAgenda(HttpHeaders,UpdateAgendaForm,BindingResult)
	{GET [/meeting/query-meeting-type]}: queryMeetingType(HttpHeaders,Long)
	{GET [/meeting/query-meeting-agenda]}: queryMeetingAgenda(HttpHeaders,Long)
	{GET [/meeting/org/list-all]}: orgListAll(HttpHeaders)
	{GET [/meeting/list]}: listMeeting(HttpHeaders,String,String,List,Short,List,Date,Date,Integer,Integer,Short)
	{GET [/meeting/list-v2]}: listMeetingV2(HttpHeaders,String,String,List,Short,List,Date,Date,Integer,Integer,Integer,Long,Long,Integer,Integer)
	{GET [/meeting/list-all]}: listAllMeeting(HttpHeaders,String,String,List,Short,List,Date,Date,Integer,Integer,Short)
	{GET [/meeting/static/review]}: staticList(HttpHeaders,String,Integer,List,Integer,Date,Date,Short,String,List,List,List,Date,Date,Integer,Integer,Integer)
	{GET [/meeting/download]}: download(HttpHeaders,String)
	{GET [/meeting/signIn]}: downloadSignIn(HttpHeaders,Long)
	{GET [/meeting/ding/event/sync]}: testDingEventSync(HttpHeaders,Long)
	{GET [/meeting/test/dingEventSync]}: testDingEventSync(Long,Integer,String)
	{GET [/meeting/export]}: downloadWord(HttpHeaders,List,List)
	{GET [/meeting/tag/update]}: tagUpdName(HttpHeaders,Long,String)
	{GET [/meeting/header-json]}: tranfHead(HttpHeaders)
	{GET [/meeting/detail/{id}]}: detail(HttpHeaders,long,Short)
[14:23:10,412][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingDraftController:
	{GET [/meeting/deleteDraft/{orgId}]}: deleteDraft(HttpHeaders,Long,Integer,Long)
	{GET [/meeting/getDraft/{orgId}]}: getDraft(HttpHeaders,Long,Integer,Long)
	{POST [/meeting/saveDraft/{orgId}]}: saveDraft(HttpHeaders,Long,MeetingDraftSaveForm,BindingResult)
[14:23:10,413][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingLeaderSurveyController:
	{POST [/mls/append]}: add(HttpHeaders,MeetingLeaderSurveyEntity,BindingResult)
	{POST [/mls/mutate]}: update(HttpHeaders,MeetingLeaderSurveyEntity,BindingResult)
	{POST [/mls/list]}: list(HttpHeaders,MeetingLeaderSurveyForm,Integer,Integer,BindingResult)
	{GET [/mls/eliminate]}: delete(HttpHeaders,long)
	{GET [/mls/get]}: findById(HttpHeaders,long)
	{GET [/mls/findSurveyByYear]}: findSurveyByYear(HttpHeaders,int)
[14:23:10,415][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingLeaveController:
	{POST [/leave/add]}: add(HttpHeaders,MeetingLeaveAddForm,BindingResult)
	{GET [/leave/list]}: list(HttpHeaders,Integer,List,Integer,String,String,String)
	{GET [/leave/t1]}: t1(Long)
	{POST [/leave/check]}: check(HttpHeaders,MeetingLeaveCheckForm,BindingResult)
	{GET [/leave/cancel/{meeting_leave_id}]}: cancel(HttpHeaders,Long,String)
	{GET [/leave/list-wait-approve]}: queryApprovelist(HttpHeaders,Integer,List,String,Integer,String,String,String)
	{GET [/leave/detail/{meeting_leave_id}]}: detail(long)
[14:23:10,418][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingOrgCommendPenalizeController:
	{POST [/org/commend/penalize/add]}: add(HttpHeaders,MeetingOrgCommendPenalizeForm)
	{POST [/org/commend/penalize/list]}: list(MeetingOrgCommendPenalizeQueryForm,BindingResult,HttpHeaders)
	{POST [/org/commend/penalize/delete]}: delete(HttpHeaders,MeetingOrgCommendPenalizeForm,BindingResult)
	{GET [/org/commend/penalize/export]}: export(String,HttpHeaders)
	{GET [/org/commend/penalize/get-org-commend-statistics]}: getOrgCommendStatistics(Long,HttpHeaders)
	{POST [/org/commend/penalize/export]}: excelList(MeetingOrgCommendPenalizeQueryForm,BindingResult,HttpHeaders)
	{POST [/org/commend/penalize/edit]}: edit(HttpHeaders,MeetingOrgCommendPenalizeForm,BindingResult)
	{GET [/org/commend/penalize/select-by-org-id]}: selectByOrgId(Long,Integer,Integer,HttpHeaders)
	{POST [/org/commend/penalize/detail]}: detail(MeetingOrgCommendPenalizeForm)
[14:23:10,419][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingOrgDebriefReviewController:
	{POST [/org/debrief/review/add]}: add(HttpHeaders,MeetingOrgDebriefReviewForm)
	{POST [/org/debrief/review/list]}: list(HttpHeaders,MeetingOrgDebriefReviewQueryForm)
	{POST [/org/debrief/review/delete]}: delete(HttpHeaders,MeetingOrgDebriefReviewForm)
	{GET [/org/debrief/review/statistics/excel/list]}: statisticsExcelList(HttpHeaders,Integer,Long,String,HttpServletResponse)
	{GET [/org/debrief/review/excel/list]}: excelList(HttpHeaders,Integer,Long,String,Integer,HttpServletResponse)
	{POST [/org/debrief/review/edit]}: edit(HttpHeaders,MeetingOrgDebriefReviewForm)
	{POST [/org/debrief/review/statistics/list]}: statisticsList(HttpHeaders,MeetingOrgDebriefReviewStatisticsQueryForm)
	{GET [/org/debrief/review/statistics/list/test]}: statisticsList()
	{POST [/org/debrief/review/detail]}: detail(MeetingOrgDebriefReviewForm)
[14:23:10,420][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingPlanController:
	{POST [/plan/execute]}: execute(HttpHeaders,MeetingExecuteForm,BindingResult)
	{GET [/plan/list]}: listPageMeetingPlan(HttpHeaders,String,Integer,Integer)
	{DELETE [/plan/del/{id}]}: delMeetingPlan(HttpHeaders,long)
	{POST [/plan/add]}: addMeetingPlan(HttpHeaders,MeetingPlanAddForm,BindingResult)
	{GET [/plan/list-all/{tag}]}: listAllMeetingPlan(HttpHeaders,Short)
	{GET [/plan/list-all]}: listAllMeetingPlan(HttpHeaders,String)
	{POST [/plan/execute-org/{meetingId}/{page}]}: executeOrg(HttpHeaders,Long,Integer,HashMap)
	{GET [/plan/detail/{id}]}: detail(HttpHeaders,long)
[14:23:10,422][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingReportController:
	{POST [/result/add]}: add(HttpHeaders,MeetingAndResultForm,BindingResult)
	{POST [/result/update]}: update(HttpHeaders,MeetingResultForm,BindingResult)
	{POST [/result/check]}: check(HttpHeaders,MeetingResultCheckForm,BindingResult)
	{POST [/result/topic/answer]}: answer(HttpHeaders,MeetingReportForm,BindingResult)
	{POST [/result/submit]}: submit(HttpHeaders,MeetingResultForm,BindingResult)
	{GET [/result/check/list]}: page(HttpHeaders,int,String,Integer,String,String,String,String,String,Integer,Integer)
	{GET [/result/topic/submit/precondition/check]}: topicSubmitPreconditionCheck(HttpHeaders,Long,Long)
	{GET [/result/topic/detail]}: answerDetail(Long,Long,Long)
	{POST [/result/topic/submit]}: topicSubmit(HttpHeaders,MeetingReportTopicContentForm,BindingResult)
	{POST [/result/update/leader]}: updateLeader(HttpHeaders,MeetingResultForm,BindingResult)
	{GET [/result/detail/{id}]}: detail(HttpHeaders,Long,Short)
[14:23:10,423][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingScoreController:
	{GET [/meeting/score/test/basicsScoreAdd]}: unfinishedGroupLeader(HttpHeaders,Long,String,Integer)
	{GET [/meeting/score/test/unfinishedGroupLeader]}: unfinishedGroupLeader(HttpHeaders,Long,String)
	{GET [/meeting/score/test/operation/meetingScore]}: operationMeetingScore(HttpHeaders,Long,String,String,Integer,Integer,Integer,List,Integer,Long)
[14:23:10,424][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingTalkController:
	{GET [/meeting-talk/countMeetingTalk]}: countMeetingTalkByCondition(HttpHeaders,String,long)
	{POST [/meeting-talk/batch-del]}: batchDelMeetingTalk(BatchMeetingTalkForm,HttpHeaders)
	{GET [/meeting-talk/select-by-type]}: queryMeetingTalkList(int,long,int,Long,String,String,String,String,int,int,int)
	{GET [/meeting-talk/find/{talk_id}]}: findMeetingTalk(long,HttpHeaders)
	{GET [/meeting-talk/del/{talk_id}]}: delMeetingTalk(long,HttpHeaders)
	{POST [/meeting-talk/edit]}: editMeetingTalk(MeetingTalkForm,HttpHeaders)
	{POST [/meeting-talk/genera-leader-talk]}: generaLeaderTalk(GeneraLeaderAndUserTalkForm,HttpHeaders)
[14:23:10,425][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingTaskController:
	{GET [/meeting-task/list]}: list(HttpHeaders,Long,String,Long,List,Long,Short,String,Date,Date,Date,Date,Integer,Integer)
	{GET [/meeting-task/list-all]}: listAll(HttpHeaders,Long,String,Long,List,Long,Short,String,Date,Date,Date,Date)
	{POST [/meeting-task/find/task/info]}: findTaskInfo(HttpHeaders,TaskQueryForm)
[14:23:10,425][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingTopicTaskController:
	{GET [/topic-task/list-all/{tag}]}: list(HttpHeaders,short,Short,String,Date,Date,Date,Date)
	{GET [/topic-task/list/{tag}]}: page(HttpHeaders,short,Short,String,Date,Date,Date,Date,Integer,Integer)
[14:23:10,425][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingWaitSignController:
	{GET [/w-sign/find]}: find(Long,HttpHeaders)
	{POST [/w-sign/queryList]}: queryAddStudyList(HttpHeaders,StudyAddListForm)
	{POST [/w-sign/add]}: AddStudyCert(HttpHeaders,AddStudyCertForm,BindingResult)
[14:23:10,426][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MeetingWorkPointController:
	{POST [/mwp/fill]}: update(HttpHeaders,MeetingWorkPointEntity,BindingResult)
	{POST [/mwp/list]}: list(HttpHeaders,MeetingWorkPointForm,Integer,Integer,BindingResult)
	{GET [/mwp/get]}: findById(HttpHeaders,long)
	{GET [/mwp/cbo_id]}: createByOwnerId(HttpHeaders,Integer)
[14:23:10,426][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.MyMeetingController:
	{GET [/my-meeting/list]}: list(HttpHeaders,String,Short,Long,List,Date,Date)
	{GET [/my-meeting/stats]}: stats(HttpHeaders)
[14:23:10,427][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.OrgChangeCallbackController:
	{GET [/user_change_political_type_callback]}: userChangePoliticalTypeCallback(Long,Integer,String,HttpHeaders)
	{POST [/org_change_callback]}: orgChangeCallback(OrganizationBase,HttpHeaders)
	{POST [/user_change_org_callback]}: userChangeCallBack(UserChangeForm,HttpHeaders)
[14:23:10,428][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.OrgLifeController:
	{GET [/org_life/list]}: list(HttpHeaders,String,Long,List,Integer,List,Integer,Integer,Integer)
	{GET [/org_life/finish]}: finish(HttpHeaders,Long)
	{POST [/org_life/tag]}: tag(HttpHeaders,OrgLifeTagManageForm,BindingResult)
	{GET [/org_life/del]}: del(HttpHeaders,Long)
	{GET [/org_life/title]}: title(Long)
	{GET [/org_life/advice]}: advice(Long,Integer)
	{POST [/org_life/add_edit]}: addOrEdit(HttpHeaders,OrgLifeAddEditForm,BindingResult)
	{GET [/org_life/delete_special]}: deleteSpecial(HttpHeaders,Long,Integer,Integer,List,Integer)
	{GET [/org_life/check]}: checkSelf(Long,Integer)
	{GET [/org_life/in]}: inTheMeeting(Long)
	{POST [/org_life/save_check]}: saveCheck(HttpHeaders,Long,Integer,List)
	{GET [/org_life/change]}: changeStatus(HttpHeaders,Long,Integer,Integer)
	{GET [/org_life/only_file]}: onlyFile(Long,Integer,List)
	{GET [/org_life/query-meeting-list]}: queryMeetingList(HttpHeaders,String,String,List,Short,List,Date,Date,Integer,Integer,Integer,Long,Long,Integer,Integer,Integer)
	{GET [/org_life/link_comment]}: linkComment(HttpHeaders,Long,Long,Integer)
	{GET [/org_life/relate-meeting]}: relateMeeting(HttpHeaders,Long,List)
	{GET [/org_life/quit-relate-meeting]}: quitRelateMeeting(HttpHeaders,Long,List)
	{GET [/org_life/test-del-task]}: testDelTask(HttpHeaders,Long)
[14:23:10,431][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.OrgLifeFileController:
	{GET [/org_life/file/clone]}: test(Long)
	{GET [/org_life/file/t6]}: t6()
	{GET [/org_life/file/t7]}: t7()
	{GET [/org_life/file/t5]}: t5(Long,List)
	{POST [/org_life/file/delete_talk_attach]}: deleteTalkFile(SaveAttachForm)
	{GET [/org_life/file/query_uploader]}: queryUploader(HttpHeaders,Long,Integer,Long)
	{GET [/org_life/file/open_task]}: openTask(HttpHeaders,String)
	{GET [/org_life/file/delete_uploader]}: deleteUploader(HttpHeaders,Long,Long,Integer,Long)
	{POST [/org_life/file/file_task]}: fileTask(HttpHeaders,LifeFileTaskForm)
	{POST [/org_life/file/save_attach]}: saveAttach(HttpHeaders,SaveAttachForm,BindingResult)
	{GET [/org_life/file/delete_attach]}: deleteAttach(HttpHeaders,Long,List,Integer)
	{GET [/org_life/file/query_attach]}: queryAttach(HttpHeaders,Long,List,Integer)
	{GET [/org_life/file/file_info]}: queryFileInfo(HttpHeaders,Long,Integer)
	{POST [/org_life/file/report_attach]}: reportAttach(HttpHeaders,Long,Map)
	{GET [/org_life/file/t2]}: test2(Long,HttpHeaders)
	{GET [/org_life/file/t1]}: test1(Long,HttpHeaders)
	{GET [/org_life/file/t3]}: test3(Long,Integer,HttpHeaders)
	{GET [/org_life/file/t4]}: test4(List,HttpHeaders)
[14:23:10,432][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.PracticableController:
	{GET [/practicable/erasure]}: deletePracticable(HttpHeaders,Long)
	{POST [/practicable/compile]}: updatePracticable(HttpHeaders,PracticableEntity,BindingResult)
	{POST [/practicable/append]}: addPracticable(HttpHeaders,PracticableEntity,BindingResult)
	{POST [/practicable/list]}: listPracticable(HttpHeaders,PracticableForm,Integer,Integer)
[14:23:10,433][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.ReportController:
	{POST [/report/awareness/compile]}: changeReportAwareness(HttpHeaders,ReportEntity)
	{POST [/report/awareness/append]}: addReportAwareness(HttpHeaders,ReportEntity)
	{POST [/report/compile]}: changeReport(HttpHeaders,ReportEntity)
	{GET [/report/erasure]}: eliminateReport(Long)
	{POST [/report/append]}: addReport(HttpHeaders,ReportEntity)
	{POST [/report/list]}: listReport(ReportForm,Integer,Integer)
[14:23:10,434][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SbwMyTaskController:
	{POST [/sbw/my/save]}: save(SbwHandleForm,HttpHeaders)
	{POST [/sbw/my/submit]}: submit(SbwHandleForm,HttpHeaders)
	{GET [/sbw/my/task]}: myTaskList(String,String,String,Integer,Integer,HttpHeaders)
[14:23:10,435][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SbwNewTaskController:
	{POST [/sbw-task/del]}: delTask(SbwNewTaskForm,BindingResult,HttpHeaders)
	{GET [/sbw-task/myCheck]}: taskDetails(HttpHeaders,Long)
	{GET [/sbw-task/myTask]}: getMyTask(HttpHeaders,String,Date,Date,Integer,Integer)
	{POST [/sbw-task/addTurn]}: createTurnTask(SbwNewTaskForm,BindingResult,HttpHeaders)
	{GET [/sbw-task/releaseFind]}: releaseTaskList(HttpHeaders,String,Date,Date,Integer,Integer)
	{GET [/sbw-task/print]}: createPrintWord(HttpHeaders,Long,Long)
	{GET [/sbw-task/check]}: releaseTaskDetails(HttpHeaders,Long)
	{GET [/sbw-task/find-org]}: showTask(Long,Integer)
	{GET [/sbw-task/orderFind]}: showTask(Long,Integer,Long,HttpHeaders)
[14:23:10,436][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SbwShiftTaskController:
	{GET [/sbw/shift/list]}: list(HttpHeaders,String,String,String,Integer,Integer)
	{GET [/sbw/shift/details]}: showTask(HttpHeaders,Long)
	{POST [/sbw/shift/save]}: saveShiftTask(HttpHeaders,SbwShiftTaskFrom)
	{POST [/sbw/shift/del]}: compileShiftTask(HttpHeaders,SbwShiftTaskFrom)
	{POST [/sbw/shift/submit]}: submitShiftTask(HttpHeaders,SbwShiftTaskFrom)
[14:23:10,436][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SbwTaskTypeController:
	{GET [/sbw/type/list]}: list()
[14:23:10,438][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SbwVerifyTaskController:
	{POST [/sbw/verify/save]}: save(SbwHandleForm,HttpHeaders)
	{POST [/sbw/verify/submit]}: submit(SbwHandleForm,HttpHeaders)
	{GET [/sbw/verify/task]}: myTaskList(String,String,String,Integer,Integer,HttpHeaders)
	{GET [/sbw/verify/org]}: myTaskList(Long)
[14:23:10,439][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.SchedulerController:
	{GET [/send/notice]}: test(Integer)
	{GET [/refresh/cache-index-collect]}: refreshCacheOfIndexCollect()
[14:23:10,440][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.TaskController:
	{GET [/task/stats]}: list(HttpHeaders)
	{GET [/task/undone-org/stats]}: undoneOrgStats(HttpHeaders)
[14:23:10,441][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.TobaccoTaskHandleController:
	{POST [/tobacco/handle/fill_submit]}: fill(HttpHeaders,TobaccoTaskHandleForm)
	{POST [/tobacco/handle/verify]}: verify(HttpHeaders,TobaccoTaskHandleForm)
	{POST [/tobacco/handle/fill_save]}: fillSave(HttpHeaders,TobaccoTaskHandleForm)
	{POST [/tobacco/handle/verify_save]}: handleVerifyDraft(HttpHeaders,TobaccoTaskHandleForm)
[14:23:10,442][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.TopPriorityController:
	{POST [/top_priority/list]}: list(HttpHeaders,TopPriorityForm,Integer,Integer,BindingResult)
	{POST [/top_priority/learning_progress]}: getTopPriorityLearningProgress(HttpHeaders,TopPriorityForm,Integer,Integer,BindingResult)
	{POST [/top_priority/eliminate]}: removeTopPriorityByIds(HttpHeaders,List)
	{POST [/top_priority/by_priority]}: listSummaryByPriority(HttpHeaders,TopPriorityForm,Integer,Integer,BindingResult)
	{POST [/top_priority/by_corps]}: listSummaryByPriority(HttpHeaders,TopPriorityForm,BindingResult)
	{POST [/top_priority/append]}: addTopPriority(HttpHeaders,TopPriorityEntity,BindingResult)
	{GET [/top_priority/get]}: getTopPriorityById(HttpHeaders,String)
	{POST [/top_priority/mutate]}: updateTopPriority(HttpHeaders,TopPriorityEntity,BindingResult)
	{GET [/top_priority/test/read_from_spider]}: readFromSpider(String,HttpHeaders)
[14:23:10,444][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.TopicController:
	{POST [/topic/add]}: add(HttpHeaders,TopicEntity,BindingResult)
	{POST [/topic/update]}: update(HttpHeaders,TopicEntity,BindingResult)
	{GET [/topic/list]}: list(HttpHeaders,String)
	{DELETE [/topic/del/{topic_id}]}: delete(HttpHeaders,Long)
	{GET [/topic/page]}: page(HttpHeaders,String,Date,Date,Integer,Integer)
	{GET [/topic/taskOrgList/export]}: orgListExport(HttpServletResponse,Long,Integer,String)
	{GET [/topic/taskOrgList]}: detailOrgList(Long,Integer,String)
	{GET [/topic/detail]}: detail(Long)
[14:23:10,445][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.TypeController:
	{POST [/type/add]}: addType(HttpHeaders,TypeAddForm,BindingResult)
	{POST [/type/update]}: updateType(HttpHeaders,TypeUpdateForm,BindingResult)
	{GET [/type/list]}: listType(HttpHeaders,Long,Long,String,Integer,Integer)
	{GET [/type/list-all/{tag}]}: listAll(HttpHeaders,short)
	{DELETE [/type/del/{id}]}: delType(HttpHeaders,long)
	{GET [/type/update-user-rule]}: updateUserRule(HttpHeaders,Long,Integer,Integer,Integer)
	{GET [/type/list-all]}: listAllType(HttpHeaders,Long,Long,String)
[14:23:10,447][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.UserCommendPenalizeController:
	{GET [/user/commend/penalize/fields]}: fields(Integer,HttpHeaders)
	{GET [/user/commend/penalize/select]}: selectUserCommendPenalize(Long,HttpHeaders)
	{GET [/user/commend/penalize/select-by-user-id]}: selectUserCommendPenalizeByUserId(Long,Integer,Integer,HttpHeaders)
	{GET [/user/commend/penalize/get-my-commend-statistics]}: getMyCommendStatistics(Long,HttpHeaders)
	{GET [/user/commend/penalize/del]}: delUserCommendPenalize(Long,HttpHeaders)
	{POST [/user/commend/penalize/export]}: exportUserCommendPenalize(UserCommendPenalizeExportQueryForm,HttpHeaders,HttpServletResponse)
	{POST [/user/commend/penalize/add]}: insertUserCommendPenalize(UserCommendPenalizeAddVO,BindingResult,HttpHeaders)
	{POST [/user/commend/penalize/query]}: queryUserCommendPenalize(UserCommendPenalizeQueryForm,BindingResult,HttpHeaders)
	{GET [/user/commend/penalize/export]}: getUserCommendPenalizeFile(String,HttpHeaders,HttpServletResponse)
	{POST [/user/commend/penalize/update]}: updateUserCommendPenalize(UserCommendPenalizeUpdateVO,BindingResult,HttpHeaders)
[14:23:10,448][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.UserCommentController:
	{POST [/comment/user/query]}: queryUserCommentList(UserCommentQueryForm,BindingResult,HttpHeaders)
	{POST [/comment/user/export]}: exportUserCommentList(UserCommentQueryForm,BindingResult,HttpHeaders)
	{POST [/comment/user/update]}: updateUserComment(UserCommentUpdateVO,BindingResult,HttpHeaders)
	{GET [/comment/user/del]}: delUserComment(Long,HttpHeaders)
	{POST [/comment/user/insert]}: insertUserComment(UserCommentAddVO,BindingResult,HttpHeaders)
	{GET [/comment/user/export]}: exportUserComment(String,HttpServletRequest,HttpServletResponse,HttpHeaders)
	{GET [/comment/user/select]}: selectUserComment(Long,HttpHeaders)
[14:23:10,449][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.UserCommentStatisticsController:
	{POST [/comment/user/statistics/query]}: queryCommentStatisticsList(UserCommentStatisticsQueryForm,BindingResult,HttpHeaders)
	{POST [/comment/user/statistics/export]}: exportCommentStatisticsList(Long,Integer,String,HttpHeaders,HttpServletResponse)
	{GET [/comment/user/statistics/export]}: exportCommentStatisticsList(String,HttpHeaders,HttpServletRequest,HttpServletResponse)
	{GET [/comment/user/statistics/generate]}: generateCommentStatistics(Integer,HttpHeaders)
[14:23:10,449][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.VideoConferenceController:
	{GET [/videoConference/join]}: join(HttpHeaders,String)
	{POST [/videoConference/list]}: list(HttpHeaders,VideoConferenceForm)
	{GET [/videoConference/info]}: info(HttpHeaders,Long)
	{POST [/videoConference/editScheduleConferences]}: editScheduleConferences(HttpHeaders,VideoConferenceVo)
	{POST [/videoConference/cancelScheduleConferences]}: cancelScheduleConferences(HttpHeaders,VideoConferenceVo)
	{POST [/videoConference/addScheduleConferences]}: addScheduleConferences(HttpHeaders,VideoConferenceVo)
	{POST [/videoConference/addVideoConference]}: addVideoConference(HttpHeaders,VideoConferenceVo)
[14:23:10,450][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.WorkflowCallbackController:
	{GET [/workflow_callback]}: wfCallback(long,long,String,long,int,String,Long,HttpHeaders)
[14:23:10,451][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.ApplicationConfigHelperController:
	{GET [/app/config/manual/refresh]}: refresh()
	{GET [/app/config/show/cache]}: show()
[14:23:10,452][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.HikariPoolController:
	{GET [/hikari/status]}: getHikariStatus()
[14:23:10,452][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.UserChangeNameCallBakController:
	{GET [/user_change_name_callback]}: callback(HttpServletRequest,Long,String)
[14:23:10,452][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	c.g.o.c.UserChangePhoneCallbackController:
	{GET [/user_change_phone_callback]}: callBack(Long,String)
[14:23:10,453][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	s.d.o.w.OpenApiControllerWebMvc:
	{GET [/v3/api-docs], produces [application/json || application/hal+json]}: getDocumentation(String,HttpServletRequest)
[14:23:10,455][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	s.d.s.w.ApiResourceController:
	{GET [/swagger-resources/configuration/ui], produces [application/json]}: uiConfiguration()
	{GET [/swagger-resources], produces [application/json]}: swaggerResources()
	{GET [/swagger-resources/configuration/security], produces [application/json]}: securityConfiguration()
[14:23:10,456][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	s.d.s.w.Swagger2ControllerWebMvc:
	{GET [/v2/api-docs], produces [application/json || application/hal+json]}: getDocumentation(String,HttpServletRequest)
[14:23:10,458][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
[14:23:10,846][INFO ][com.goodsogood.ows.dmconfig.MyBatisInterceptorConfig][main] [] [] === 创建拦截器数组 ===
[14:23:11,166][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 'viewControllerHandlerMapping' {/swagger-ui/=ParameterizableViewController [view="forward:/swagger-ui/index.html"]}
[14:23:11,173][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 'beanNameHandlerMapping' {}
[14:23:11,209][DEBUG][_org.springframework.web.servlet.HandlerMapping.Mappings][main] [] [] 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]], /swagger-ui/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/springfox-swagger-ui/]], /static/**=ResourceHttpRequestHandler [classpath [static/]], /templates/**=ResourceHttpRequestHandler [classpath [templates/]], /swagger-ui.html=ResourceHttpRequestHandler [classpath [META-INF/resources/]]}
[14:23:11,306][INFO ][tk.mybatis.mapper.autoconfigure.MapperCacheDisabler][main] [] [] Clear tk.mybatis.mapper.util.MsUtil CLASS_CACHE cache.
[14:23:11,307][INFO ][tk.mybatis.mapper.autoconfigure.MapperCacheDisabler][main] [] [] Clear tk.mybatis.mapper.genid.GenIdUtil CACHE cache.
[14:23:11,307][INFO ][tk.mybatis.mapper.autoconfigure.MapperCacheDisabler][main] [] [] Clear tk.mybatis.mapper.version.VersionUtil CACHE cache.
[14:23:11,307][INFO ][tk.mybatis.mapper.autoconfigure.MapperCacheDisabler][main] [] [] Clear EntityHelper entityTableMap cache.
[14:23:11,527][DEBUG][com.github.pagehelper.PageInterceptor][main] [] [] 

,------.                           ,--.  ,--.         ,--.                         
|  .--. '  ,--,--.  ,---.   ,---.  |  '--'  |  ,---.  |  |  ,---.   ,---.  ,--.--. 
|  '--' | ' ,-.  | | .-. | | .-. : |  .--.  | | .-. : |  | | .-. | | .-. : |  .--' 
|  | --'  \ '-'  | ' '-' ' \   --. |  |  |  | \   --. |  | | '-' ' \   --. |  |    
`--'       `--`--' .`-  /   `----' `--'  `--'  `----' `--' |  |-'   `----' `--'    
                   `---'                                   `--'                        is intercepting.

[14:23:12,165][WARN ][org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration][main] [] [] Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
[14:23:12,876][WARN ][org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger][main] [] [] Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[14:23:26,361][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:3}
[14:23:26,363][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:4}
[14:23:26,363][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:23:26,363][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:23:41,386][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-1] [25e4db48-fd20-43fd-8763-8b6cb2ec3d52] [meeting-tc] ctime:1754979821380,tracker_id:25e4db48-fd20-43fd-8763-8b6cb2ec3d52,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SR,name:/meeting/list-all,env:{"url":"http://localhost:8140/meeting/list-all","method":"GET","uri":"/meeting/list-all","queryString":null,"requestParams":{}}
[14:23:45,803][DEBUG][com.goodsogood.ows.dmconfig.StatementHandlerInterceptor][http-nio-8140-exec-1] [25e4db48-fd20-43fd-8763-8b6cb2ec3d52] [meeting-tc] StatementHandlerInterceptor.plugin被调用，target: org.apache.ibatis.executor.CachingExecutor
[14:23:52,374][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:5}
[14:23:52,374][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:6}
[14:23:52,374][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:23:52,374][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:23:52,378][INFO ][com.aidangqun.log4j2cm.aop.HttpLogAspect][http-nio-8140-exec-1] [25e4db48-fd20-43fd-8763-8b6cb2ec3d52] [meeting-tc] ctime:1754979832378,tracker_id:25e4db48-fd20-43fd-8763-8b6cb2ec3d52,span_id:meeting-tc,p_span_id:0,node_name:ZHY:8140,type:SS,name:/meeting/list-all,env:{"url":"http://localhost:8140/meeting/list-all","method":"GET","uri":"/meeting/list-all","queryString":null,"requestParams":{}}
[14:23:52,382][ERROR][com.goodsogood.ows.configuration.GlobalExceptionHandler][http-nio-8140-exec-1] [] [] nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96) ~[mybatis-spring-2.0.7.jar:2.0.7]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.7.jar:2.0.7]
	at jdk.proxy2.$Proxy252.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.7.jar:2.0.7]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.proxy2.$Proxy284.findAll(Unknown Source) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215) ~[spring-aop-5.3.20.jar:5.3.20]
	at jdk.proxy2.$Proxy285.findAll(Unknown Source) ~[?:?]
	at com.goodsogood.ows.service.MeetingRedisService.listAll(MeetingRedisService.java:115) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController.listAllMeeting(MeetingController.java:459) ~[classes/:?]
	at com.goodsogood.ows.controller.MeetingController$$FastClassBySpringCGLIB$$acb197b3.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123) ~[spring-context-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.goodsogood.ows.controller.MeetingController$$EnhancerBySpringCGLIB$$106d1b7b.listAllMeeting(<generated>) ~[classes/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) ~[tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at com.goodsogood.ows.filter.TranslateRequestFilter.doFilter(TranslateRequestFilter.java:34) ~[ows-starter-spring-boot-4.0.2-SNAPSHOT.jar:4.0.2-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) ~[spring-cloud-sleuth-instrumentation-3.0.5.jar:3.0.5]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:114) ~[spring-cloud-sleuth-autoconfigure-3.0.5.jar:3.0.5]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.5.14.jar:2.5.14]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.7.jar:2.0.7]
	... 100 more
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make public int java.util.Collections$SingletonList.size() accessible: module java.base does not "opens java.util" to unnamed module @2e4b8173
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:354) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:297) ~[?:?]
	at java.lang.reflect.Method.checkCanSetAccessible(Method.java:200) ~[?:?]
	at java.lang.reflect.Method.setAccessible(Method.java:194) ~[?:?]
	at org.apache.ibatis.ognl.AccessibleObjectHandlerPreJDK9.setAccessible(AccessibleObjectHandlerPreJDK9.java:58) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.OgnlRuntime.invokeMethod(OgnlRuntime.java:1211) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.OgnlRuntime.callAppropriateMethod(OgnlRuntime.java:1962) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ObjectMethodAccessor.callMethod(ObjectMethodAccessor.java:68) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.OgnlRuntime.callMethod(OgnlRuntime.java:2038) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTMethod.getValueBody(ASTMethod.java:97) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTChain.getValueBody(ASTChain.java:141) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTGreater.getValueBody(ASTGreater.java:50) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.ASTAnd.getValueBody(ASTAnd.java:61) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:586) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:550) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.OgnlCache.getValue(OgnlCache.java:46) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.ExpressionEvaluator.evaluateBoolean(ExpressionEvaluator.java:32) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.IfSqlNode.apply(IfSqlNode.java:34) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.lambda$apply$0(MixedSqlNode.java:32) ~[mybatis-3.5.9.jar:3.5.9]
	at java.util.ArrayList.forEach(ArrayList.java:1511) ~[?:?]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.apply(MixedSqlNode.java:32) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.scripting.xmltags.DynamicSqlSource.getBoundSql(DynamicSqlSource.java:39) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.mapping.MappedStatement.getBoundSql(MappedStatement.java:305) ~[mybatis-3.5.9.jar:3.5.9]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:103) ~[pagehelper-5.3.1.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.proxy2.$Proxy525.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.9.jar:3.5.9]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.9.jar:3.5.9]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.7.jar:2.0.7]
	... 100 more
[14:24:06,839][ERROR][com.netflix.discovery.DiscoveryClient][DiscoveryClient-HeartbeatExecutor-0] [] [] DiscoveryClient_MEETING-TC/<EMAIL> - was unable to send heartbeat!
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:893) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1457) ~[eureka-client-1.10.17.jar:1.10.17]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:24:12,377][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:7}
[14:24:12,377][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:8}
[14:24:12,377][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:24:12,377][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:24:17,103][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:24:32,380][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:10}
[14:24:32,380][DEBUG][org.mongodb.driver.connection][cluster-rtt-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:9}
[14:24:32,380][DEBUG][org.mongodb.driver.cluster][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Updating cluster description to  {type=UNKNOWN, servers=[{address=**************:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketTimeoutException: Connect timed out}}]
[14:24:32,380][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Invalidating the connection pool
[14:24:36,801][ERROR][com.netflix.discovery.DiscoveryClient][DiscoveryClient-HeartbeatExecutor-0] [] [] DiscoveryClient_MEETING-TC/<EMAIL> - was unable to send heartbeat!
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:893) ~[eureka-client-1.10.17.jar:1.10.17]
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1457) ~[eureka-client-1.10.17.jar:1.10.17]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[14:24:38,273][INFO ][com.goodsogood.ows.OwsMeetingApplication][main] [] [] Started OwsMeetingApplication in 112.419 seconds (JVM running for 114.3)
[14:24:43,168][WARN ][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [] [] Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
[14:24:44,109][DEBUG][org.mongodb.driver.connection][SpringApplicationShutdownHook] [] [] Closing connection connectionId{localValue:11}
[14:24:52,383][DEBUG][org.mongodb.driver.connection][cluster-ClusterId{value='689addc02ffa5d4bf36ecf87', description='null'}-**************:27017] [] [] Closing connection connectionId{localValue:12}
[14:25:04,198][ERROR][org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer][org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] [] [] Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:252) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2175) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2148) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2128) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1914) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1895) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1374) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1220) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:60) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1220) ~[amqp-client-5.12.0.jar:5.12.0]
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1170) ~[amqp-client-5.12.0.jar:5.12.0]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615) ~[spring-rabbit-2.3.16.jar:2.3.16]
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565) ~[spring-rabbit-2.3.16.jar:2.3.16]
	... 12 more
[14:25:04,207][DEBUG][reactor.core.publisher.Hooks][SpringApplicationShutdownHook] [] [] Reset onEachOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
[14:25:04,207][DEBUG][reactor.core.publisher.Hooks][SpringApplicationShutdownHook] [] [] Reset onLastOperator: org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration
