package com.goodsogood.ows.utils;



import org.junit.jupiter.api.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateUtilsTest {
    @Test
    public void inCurrentQuarter() throws ParseException {

        //设置要获取到什么样的时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        Date date = sdf.parse("2020-05-27 11:22:10");
        System.out.println(date);
        System.out.println(DateUtils.inCurrentQuarter(date));
    }
}