package com.goodsogood.ows.service;

import com.goodsogood.ows.model.vo.MeetingListForm;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
public class MeetingServiceTest {
    @Autowired
    MeetingService meetingService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void redisTest() {
        String key = "PUSH_API_CHECK_IS_SEND_9999_http://www.baidu.com/check-page-to-detail/2/60";
//        System.out.println(stringRedisTemplate.opsForSet().add(key, "1asdfdasfdsagfdsa"));
//        System.out.println(stringRedisTemplate.opsForSet().add(key, "cxzvcxzvxczvxczvzxcv2"));
//        System.out.println(stringRedisTemplate.opsForSet().add(key, "cxzvcxzvxczvxczvzxcv2"));
        System.out.println(stringRedisTemplate.opsForSet().members(key));
    }

    @Test
    public void addMeeting() {
//        String str = "{\"meeting_types\":[{\"meeting_task_id\":3694},{\"meeting_task_id\":770}],\"topics\":[{\"name\":\"123\",\"topic_id\":1}],\"participant_users\":[{\"user_name\":\"陈万华\",\"user_id\":38,\"phone\":\"186****2512\",\"cert_number\":\"510212********4577\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":1},{\"user_name\":\"陈秋竹\",\"user_id\":54,\"phone\":\"132****5210\",\"cert_number\":\"500104********0416\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":2},{\"user_name\":\"陈娇\",\"user_id\":87,\"phone\":\"158****9843\",\"cert_number\":\"500230********002X\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":3},{\"user_name\":\"陈强\",\"user_id\":114,\"phone\":\"186****6614\",\"cert_number\":\"510202********2116\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":4},{\"user_name\":\"陈本利\",\"user_id\":132,\"phone\":\"136****6493\",\"cert_number\":\"512301********0277\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":5},{\"user_name\":\"陈攀\",\"user_id\":137,\"phone\":\"137****9917\",\"cert_number\":\"500383********0735\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":6},{\"user_name\":\"陈洁\",\"user_id\":157,\"phone\":\"137****6668\",\"cert_number\":\"522323********0043\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":7},{\"user_name\":\"陈星宇\",\"user_id\":159,\"phone\":\"187****8288\",\"cert_number\":\"500228********0067\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":8},{\"user_name\":\"陈荣辉\",\"user_id\":186,\"phone\":\"133****6665\",\"cert_number\":\"510211********1216\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":9},{\"user_name\":\"赵亮\",\"user_id\":52669,\"phone\":\"176****2850\",\"cert_number\":\"342623********0633\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":10},{\"user_name\":\"陈安顺\",\"user_id\":52778,\"phone\":\"173****8553\",\"cert_number\":\"522132********795X\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":11},{\"user_name\":\"陈虹交\",\"user_id\":63525,\"phone\":\"133****1327\",\"cert_number\":\"500221********4342\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":12},{\"user_name\":\"冯思源\",\"user_id\":63526,\"phone\":\"134****1111\",\"cert_number\":\"510222********1316\",\"org_id\":4683,\"org_name\":\"固守-党委\",\"reason\":\"\",\"is_add\":1,\"sign_status\":1,\"key\":13}],\"attend_users\":[],\"host_user\":[{\"user_id\":38,\"org_id\":4683,\"org_name\":\"固守-党委\",\"org_type\":102803,\"cert_number\":\"510212********4577\",\"phone\":\"186****2512\",\"position\":\"\",\"appoint_able\":1,\"key\":\"38-4683\",\"is_add\":1,\"user_name\":\"陈万华\"}],\"record_user\":[{\"user_id\":54,\"org_id\":4683,\"org_name\":\"固守-党委\",\"org_type\":102803,\"cert_number\":\"500104********0416\",\"phone\":\"132****5210\",\"position\":\"\",\"appoint_able\":1,\"key\":\"54-4683\",\"is_add\":1,\"user_name\":\"陈秋竹\"}],\"must_approve\":1,\"workflow_id\":294,\"workflow_name\":\"ddd\",\"gps_type\":1,\"lng\":\"106.563019\",\"lat\":\"29.528856\",\"name\":\"1111111\",\"types\":\"党课,党课\",\"address\":\"固守供应链管理有限公司南坪西路36号嘉发跨贸中心34楼\",\"start_time\":\"2019-01-18 15:15\",\"is_sign_in\":1,\"sign_start_time\":\"2019-01-17 16:59\",\"sign_end_time\":\"2019-01-17 18:59\"}\n";
//        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
//        MeetingEntity meetingEntity = JsonUtils.fromJson(str, MeetingEntity.class);
//        meetingService.addMeeting(null, null, meetingEntity);

    }


}