package com.goodsogood.ows.service;

import com.goodsogood.ows.common.MeetingCanstant;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
public class MeetingAnswerServiceTest {
    @Autowired
    TopicOrgService topicOrgService;

    @Test
    public void updateAllTopicOrgStatus() {
        topicOrgService.updateAllTopicOrgStatus(35, MeetingCanstant.MEETING_STATUS_CANCEL.shortValue());
    }
}