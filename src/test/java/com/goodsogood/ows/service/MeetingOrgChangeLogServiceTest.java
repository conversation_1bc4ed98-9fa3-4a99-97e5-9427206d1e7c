package com.goodsogood.ows.service;

import com.goodsogood.ows.model.vo.OrgChangeForm;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;


@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
public class MeetingOrgChangeLogServiceTest {
    @Autowired
    MeetingOrgChangeLogService meetingOrgChangeLogService;


    @Test
    public void saveOrgChangeLog() {
        OrgChangeForm orgChangeForm = new OrgChangeForm();
        orgChangeForm.setType((short) 1);
        orgChangeForm.setOrgId(3L);
        orgChangeForm.setOrgName("test");
        orgChangeForm.setGroup((short) 1);
        orgChangeForm.setIsRetire((short) 1);
        orgChangeForm.setPeriod((short) 1);
        orgChangeForm.setOrgLevel("-3-");
        orgChangeForm.setOrgTypeChild(1);
        meetingOrgChangeLogService.saveOrgChangeLog(RestTemplateHelper.getRegionIdLogHeader(3L), orgChangeForm);
    }
}