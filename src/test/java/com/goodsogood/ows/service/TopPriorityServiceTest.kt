package com.goodsogood.ows.service

import com.goodsogood.ows.model.mongo.TopPriorityMeeting
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDate

@ExtendWith(SpringExtension::class)
@SpringBootTest
@ActiveProfiles("dev")
class TopPriorityServiceTest {
    @Autowired
    private var topPriorityService: TopPriorityService? = null

    @Test
    fun testAddAssociatedAndMeetingsById() {
        topPriorityService!!.addAssociatedAndMeetingsById(TopPriorityMeeting().also {
            it.meetingId = 1
            it.associatedName = "XXX"
            it.associatedOrg = 7
            it.associatedUnitOrgId = 2
            it.associatedUnitOrgName = "XXX"
            it.associatedUnitShortName = "XXX"
            it.meetingTitle = "123"
            it.meetingTime = LocalDate.now()
            it.meetingTypes = "1,2,3,4"
            it.meetingTypeNames = "A,B,C,D"
        }, "6513ce0114fa5e12ebd5a53a")
        topPriorityService!!.addAssociatedAndMeetingsById(TopPriorityMeeting().also {
            it.meetingId = 2
            it.associatedName = "XXX"
            it.associatedOrg = 7
            it.associatedUnitOrgId = 2
            it.associatedUnitOrgName = "XXX"
            it.associatedUnitShortName = "XXX"
            it.meetingTitle = "123"
            it.meetingTime = LocalDate.now()
            it.meetingTypes = "1,2,3,4"
            it.meetingTypeNames = "A,B,C,D"
        }, "6513ce0114fa5e12ebd5a53a")
        topPriorityService!!.addAssociatedAndMeetingsById(TopPriorityMeeting().also {
            it.meetingId = 3
            it.associatedName = "XXX"
            it.associatedOrg = 7
            it.associatedUnitOrgId = 2
            it.associatedUnitOrgName = "XXX"
            it.associatedUnitShortName = "XXX"
            it.meetingTitle = "123"
            it.meetingTime = LocalDate.now()
            it.meetingTypes = "1,2,3,4"
            it.meetingTypeNames = "A,B,C,D"
        }, "6513ce0114fa5e12ebd5a53a")
    }

    @Test
    fun testRemoveAssociatedAndMeetings() {
        topPriorityService!!.removeAssociatedAndMeetings(
            TopPriorityMeeting().also {
                it.meetingId = 1
                it.associatedName = "XXX"
                it.associatedOrg = 7
                it.associatedUnitOrgId = 2
                it.associatedUnitOrgName = "XXX"
                it.associatedUnitShortName = "XXX"
                it.meetingTitle = "123"
                it.meetingTime = LocalDate.now()
                it.meetingTypes = "1,2,3,4"
                it.meetingTypeNames = "A,B,C,D"
            }
        )
    }

}