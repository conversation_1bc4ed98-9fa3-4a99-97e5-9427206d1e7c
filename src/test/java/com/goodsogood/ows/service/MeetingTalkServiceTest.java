package com.goodsogood.ows.service;

import com.goodsogood.ows.model.vo.MeetingTalkDocFileForm;
import com.goodsogood.ows.model.vo.TalkDocContent;
import com.goodsogood.ows.utils.word.ExportData;
import com.goodsogood.ows.utils.word.SoMap;
import com.goodsogood.ows.utils.word.WordUtil;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Objects;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
public class MeetingTalkServiceTest {

    @Autowired
    private MeetingTalkService meetingTalkService;

    @Test
    public void test01() {
        final MeetingTalkDocFileForm fileForm = new MeetingTalkDocFileForm();
        fileForm.setTitle("张三-李白");
        fileForm.setUsername("张三");
        fileForm.setToUsername("李白");
        fileForm.setBeginTime("2021-11-31 09:00");
        fileForm.setEndTime("2021-11-31 11:00");
        fileForm.setLocation("重庆市渝北区嘉州路新光天地4栋1层1098");
        fileForm.setType("成员之间");
        final ArrayList<TalkDocContent> contents = new ArrayList<>();
        final TalkDocContent docContent = new TalkDocContent();
        docContent.setTitle("猎人海力布");
        docContent.setContent("从前有一个猎人，他叫海力布，他非常乐于助人。\n" +
                "\n" +
                "有一次，海力布看到一只老鹰捉住一条小白蛇，在高高的天空上，海力布看到后，用箭把老鹰射下来，救下了小白蛇。小白蛇说她是龙王的女儿，海里布救了她，她要报答海力布，如果海力布不想要珍宝，她爸爸嘴里含了一颗宝石，它可以听懂动物的话，但是不能把自己听到的话说给别人听。\n" +
                "\n" +
                "有一天，海力布听到，鸟儿们说今晚大山要崩塌，洪水要淹没整个村庄，海力布赶快跑到乡亲们那让大家快搬走，乡亲们不相信他，海力布只好一五一十的把真像告诉了乡亲们，海力布刚说完就变成了一块石头。");
        contents.add(docContent);
        final TalkDocContent docContent1 = new TalkDocContent();
        docContent1.setTitle("这一天，我来了");
        docContent1.setContent("过了国庆就降温，可是被我的母上大人说对了。这不我已经裹着厚厚的棉被睡觉了，可是一点都不敢把脚伸出去。\n" +
                "\n" +
                "还记得前两天我们还计划着去郑州玩，这天气一冷我的游玩计划也打水漂了，这个假期只能躲在被窝里了写作业了。\n" +
                "\n" +
                "记得以前国庆，天气都还暖暖的，我可以穿着短袖出去游玩，可是今年却是不行了，我只能穿着厚厚的棉衣准备过冬了。\n" +
                "\n" +
                "这小雨也是一天一天的下个不停，哪里还顾的上游玩，天气冷的不行，而我最怕冷了，看来这个假期只能在被窝里写作业了。");
        contents.add(docContent1);
        fileForm.setList(contents);

        final ExportData valuation = WordUtil.createExportData("file/talk_template.docx");
        final ArrayList<SoMap> list = new ArrayList<>();
        fileForm.getList().forEach( form ->list.add(new SoMap(form)));
        valuation.setData("talk", fileForm);
        valuation.setTable("users", list);

        final byte[] data = valuation.getByteArr();

        String name = fileForm.getType() + "-" + Objects.requireNonNull(fileForm.getUsername()).split(",")[0] + "-" + Objects.requireNonNull(fileForm.getToUsername()).split(",")[0] + ".doc";
        String tmpPath = "/Users/<USER>/Downloads";
        // 可以直接写入本地的文件
        String fileName = Paths.get(tmpPath, name).toString();

        try (FileOutputStream fos = new FileOutputStream(fileName)) {
            fos.write(data, 0, data.length);
        } catch (IOException ex) {
            System.out.println(ex.getMessage());
        }
    }

}
