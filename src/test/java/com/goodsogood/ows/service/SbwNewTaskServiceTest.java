package com.goodsogood.ows.service;

import com.aidangqun.ows.service.OAuthDingTalkService;
import com.aidangqun.ows.service.PushMessageDingTalkService;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.SbwTaskMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class SbwNewTaskServiceTest {
    @Autowired
    SbwNewTaskService sbwNewTaskService;

    @Autowired
    SbwTaskMapper mapper;

    @Test
    public void test() {
        HttpHeaders headers = new HttpHeaders();
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        header.setRegionId(3L);
        header.setOid(3L);
        Long taskId = 9L;
//        System.out.println("->>>"+sbwNewTaskService.showTask(headers, taskId));
    }
}