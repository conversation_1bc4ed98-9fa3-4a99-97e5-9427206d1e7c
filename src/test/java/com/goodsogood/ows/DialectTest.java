package com.goodsogood.ows;

/**
 * 达梦数据库方言测试类
 * 用于验证达梦数据库方言类是否能够正确加载
 */
public class DialectTest {
    
    public static void main(String[] args) {
        System.out.println("=== 达梦数据库方言测试 ===");
        
        // 测试方言类是否可以加载
        try {
            Class<?> dialectClass = Class.forName("org.hibernate.dialect.DmDialect");
            System.out.println("✓ 达梦数据库方言类加载成功: " + dialectClass.getName());
            
            // 尝试创建实例
            Object dialectInstance = dialectClass.getDeclaredConstructor().newInstance();
            System.out.println("✓ 达梦数据库方言实例创建成功: " + dialectInstance.getClass().getSimpleName());
            
        } catch (ClassNotFoundException e) {
            System.err.println("✗ 达梦数据库方言类未找到: " + e.getMessage());
            System.err.println("请检查是否正确添加了 DmDialect-for-hibernate5.3 依赖");
        } catch (Exception e) {
            System.err.println("✗ 创建达梦数据库方言实例失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 测试达梦数据库驱动是否可以加载
        try {
            Class<?> driverClass = Class.forName("dm.jdbc.driver.DmDriver");
            System.out.println("✓ 达梦数据库驱动类加载成功: " + driverClass.getName());
        } catch (ClassNotFoundException e) {
            System.err.println("✗ 达梦数据库驱动类未找到: " + e.getMessage());
            System.err.println("请检查是否正确添加了 DmJdbcDriver18 依赖");
        }
        
        System.out.println("=== 测试完成 ===");
    }
}
