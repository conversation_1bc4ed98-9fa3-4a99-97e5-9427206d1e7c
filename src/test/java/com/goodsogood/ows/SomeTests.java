package com.goodsogood.ows;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.URLUtil;
import com.goodsogood.ows.helper.Escape;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.xml.bind.DatatypeConverter;
import java.io.*;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.GregorianCalendar;
import java.util.Objects;
import java.util.TimeZone;

public class SomeTests {


    @Test
    public void contextLoads() throws ParseException {

//
//        List<Integer> list = Arrays.asList(1, 2, 3);
//        System.out.println(list.stream().filter(integer -> integer.equals(5)).findFirst().orElse(1));
//        Calendar ca = Calendar.getInstance();
//        ca.set(2018, 1, 1);
//        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");
//
//        System.out.println(ft.format(ca.getTime()));


//        Calendar ca = Calendar.getInstance();
//        ca.set(2018, Calendar.JANUARY, 31);
//        System.out.println("+++++++++++++++++> "+ca.get(Calendar.MONTH));
////        ca.add(Calendar.DAY_OF_MONTH,1);
//        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");

//        System.out.println(ft.parse("2018-10-25"));
//
//        Date date = DateTime.parse("")


//        System.out.println(ft.format(ca.getTime()));
//        ca.add(Calendar.MONTH,2);
//        ca.set(Calendar.DAY_OF_MONTH,ca.getActualMaximum(Calendar.DAY_OF_MONTH));
//        System.out.println(ft.format(ca.getTime()));
//        System.out.println(Math.ceil((double) 2/3));
//        String dt = DateUtil.format(DateUtil.offsetMonth(new DateTime(),-1), "yyyy-MM-dd'T'HH:mm:ssXXX");

//        String d = DatatypeConverter.printDateTime(new GregorianCalendar());
//        System.out.println(dt);
    }

    @Test
    public void test01() {
        final String escape = Escape.escape("自动生成");
        System.out.print(escape);
        final String unescape = Escape.unescape(escape);
        System.out.print(unescape);

    }

    /**
     * 保存文件
     *
     * @param filePath
     * @param fileName
     */
    public void save(String filePath, String fileName, InputStream inputStream) {
        OutputStream os = null;
        try {
            byte[] bs = new byte[1024];
            int length;
            File tmpFile = new File(filePath);
            if (!tmpFile.exists()) {
                boolean mkdirs = tmpFile.mkdirs();
                System.out.println("是否创建成功 -> " + mkdirs);
            }
            os = new FileOutputStream(tmpFile.getPath() + File.separator + fileName);
            while ((length = inputStream.read(bs)) != -1) {
                os.write(bs, 0, length);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Test
    public void text() {
        System.out.println(gcd(5, 2));
    }

    /**
     * 欧几里得
     *
     * @param p
     * @param q
     * @return
     */
    private int gcd(int p, int q) {
        if (q == 0) {
            return p;
        }
        int r = p % q;
        return gcd(q, r);
    }


}
