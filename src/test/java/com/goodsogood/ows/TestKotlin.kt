package com.goodsogood.ows

import com.goodsogood.ows.service.MeetingTalkService
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders

/*@RunWith(SpringRunner::class)
@SpringBootTest
@ActiveProfiles("dev")*/
class TestKotlin {

    @Autowired
    lateinit var meetingTalkService: MeetingTalkService

    @Test
    fun test01() {
        val headers = HttpHeaders()
        headers.set("_uid", "63511")
        headers.set("_region_id", "19")
        this.meetingTalkService.generaTalkData(8, 2, 4, headers)
    }

    @Test
    fun test02() {
        val value = "-0-1-3-6-"
        println(value.substring(4).replace("-", " ").trim().split(" "))
    }

    @Test
    fun caTest() {
        val str = "12"
        val array = str.split(",")
        println(array.contains(str))
    }
}