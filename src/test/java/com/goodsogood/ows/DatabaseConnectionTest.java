package com.goodsogood.ows;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据库连接测试类
 * 用于验证达梦数据库连接配置是否正确
 */
public class DatabaseConnectionTest {
    
    public static void main(String[] args) {
        // 达梦数据库连接参数
        String url = "jdbc:dm://113.204.105.250:55237/GS_OWS_ZY_GRAY?stringtype=unspecified&currentSchema=GS_OWS_ZY_TEST";
        String username = "SYSDBA";
        String password = "Dameng@123456";
        String driverClass = "dm.jdbc.driver.DmDriver";
        
        Connection connection = null;
        
        try {
            // 加载达梦数据库驱动
            Class.forName(driverClass);
            System.out.println("达梦数据库驱动加载成功！");
            
            // 建立数据库连接
            connection = DriverManager.getConnection(url, username, password);
            System.out.println("数据库连接成功！");
            System.out.println("连接URL: " + url);
            System.out.println("用户名: " + username);
            
            // 测试连接是否有效
            if (connection != null && !connection.isClosed()) {
                System.out.println("数据库连接状态: 正常");
                System.out.println("数据库产品名称: " + connection.getMetaData().getDatabaseProductName());
                System.out.println("数据库版本: " + connection.getMetaData().getDatabaseProductVersion());
            }
            
        } catch (ClassNotFoundException e) {
            System.err.println("达梦数据库驱动未找到: " + e.getMessage());
            e.printStackTrace();
        } catch (SQLException e) {
            System.err.println("数据库连接失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭连接
            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("数据库连接已关闭");
                } catch (SQLException e) {
                    System.err.println("关闭数据库连接时出错: " + e.getMessage());
                }
            }
        }
    }
}
