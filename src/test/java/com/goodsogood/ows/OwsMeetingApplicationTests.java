package com.goodsogood.ows;

import cn.hutool.core.date.DateUtil;
import com.aidangqun.ows.service.ScheduleDingTalkService;
import com.aliyun.dingtalkcalendar_1_0.models.CreateEventRequest;
import com.aliyun.dingtalkcalendar_1_0.models.CreateEventResponse;
import com.goodsogood.ows.configuration.MeetingScore;
import com.goodsogood.ows.configuration.MeetingScoreConfig;
import com.goodsogood.ows.service.MeetingService;
import com.goodsogood.ows.utils.JsonUtils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;


@ExtendWith(SpringExtension.class)
@SpringBootTest
//@ActiveProfiles("dev")
public class OwsMeetingApplicationTests {
    @Autowired
    MeetingService meetingService;
    @Autowired
    ScheduleDingTalkService scheduleService;

    @Autowired
    MeetingScoreConfig meetingScoreConfig;


    @Test
    public void contextLoads() {

        /**
         * 查询钉钉日程

        ListEventsRequest ler = new ListEventsRequest();
        try {
            ListEventsResponse result = scheduleService.getScheduleList("xmJRYl9iS8oyfqJHk38LiSUgiEiE", ler);
            System.out.println(JsonUtils.toJson( result.getBody().getEvents()));
        }catch (Exception e){
            e.printStackTrace();
        }*/


        /**
         * 创建钉钉日程
         */
        try {
            CreateEventRequest createEventRequest = new CreateEventRequest();
            createEventRequest.setSummary("测试联调");
            /**
             * 设置日程描述，使用 议程名称拼接
             * eg:
             * 一、议程名称
             * 二、议程名称
             * 三、议程名称
             */
            String des = "一、议程名称2\n二、议程名称2\n 三、议程名称2";
            createEventRequest.setDescription(des);
            createEventRequest.setLocation(new CreateEventRequest.CreateEventRequestLocation().setDisplayName("测试地点"));
            List<CreateEventRequest.CreateEventRequestAttendees> list = new ArrayList<>(1);
            //陈虹交
            CreateEventRequest.CreateEventRequestAttendees c1 = new CreateEventRequest.CreateEventRequestAttendees();
            c1.setId("xmJRYl9iS8oyfqJHk38LiSUgiEiE");
            list.add(c1);
            //安素兰
            CreateEventRequest.CreateEventRequestAttendees c2 = new CreateEventRequest.CreateEventRequestAttendees();
            c2.setId("ii4w0xDXrlZGfqJHk38LiSUgiEiE");
            list.add(c2);

                String[] times = "1,2".split(",");
                List<CreateEventRequest.CreateEventRequestReminders> reminders = new ArrayList<>(times.length);
                String method = "dingtalk";
                for (String time : times) {
                    CreateEventRequest.CreateEventRequestReminders reminder = new CreateEventRequest.CreateEventRequestReminders();
                    reminder.setMethod(method);
                    if ("0".equals(time)) {
                        reminder.setMinutes(1);
                    } else if ("1".equals(time)) {
                        reminder.setMinutes(5);
                    } else if ("2".equals(time)) {
                        reminder.setMinutes(15);
                    } else if ("3".equals(time)) {
                        reminder.setMinutes(30);
                    } else if ("4".equals(time)) {
                        reminder.setMinutes(60);
                    } else if ("5".equals(time)) {
                        reminder.setMinutes(60 * 24);
                    }
                    reminders.add(reminder);
                }
                createEventRequest.setReminders(reminders);
            createEventRequest.setAttendees(list);
            String startTime = "2021-09-24 15:35:00";
//            String endTime = "2021-09-24 16:15:00";
            String endTime = null;
            if (endTime == null) {
                // 全天日程
                createEventRequest.setIsAllDay(true);
                String start = "2021-09-24";
                LocalDate localDate = LocalDate.parse(start, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1);
                String end = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(localDate);
                createEventRequest.setStart(new CreateEventRequest.CreateEventRequestStart().setDate(start));
                createEventRequest.setEnd(new CreateEventRequest.CreateEventRequestEnd().setDate(end));
            } else {
                String startDateTime = DateUtil.format(DateUtil.parse(startTime,"yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd'T'HH:mm:ssXXX");
                String endDateTime = DateUtil.format(DateUtil.parse(endTime,"yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd'T'HH:mm:ssXXX");
                createEventRequest.setStart(new CreateEventRequest.CreateEventRequestStart()
                        .setDateTime(startDateTime)
                        .setTimeZone("Asia/Shanghai")
                );
                createEventRequest.setEnd(new CreateEventRequest.CreateEventRequestEnd()
                        .setDateTime(endDateTime)
                        .setTimeZone("Asia/Shanghai")
                );
            }
            CreateEventResponse cer = scheduleService.createSchedule("xmJRYl9iS8oyfqJHk38LiSUgiEiE", createEventRequest);
            System.out.println(JsonUtils.toJson(cer.getBody()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testConfig(){
        MeetingScoreConfig.ScoreConf sc = meetingScoreConfig.getScoreConf().get(19L);
        MeetingScore msConf = sc.getUserScore().stream().filter(ms->{return ms.getMeetingType()==1&&ms.getScoreType()==1;}).findFirst().get();
        System.out.println(msConf);
    }

}
