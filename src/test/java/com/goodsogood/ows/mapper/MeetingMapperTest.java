package com.goodsogood.ows.mapper;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;
import java.util.Set;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class MeetingMapperTest {
    @Autowired
    MeetingMapper meetingMapper;

    @Test
    public void findByOrgIdAndTopicIds() {

        Set<Long> set = meetingMapper.findByOrgIdAndTopicIds(Collections.singletonList(190L), 378L);
//        Assert.assertTrue(set.isEmpty());
    }

}