package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.StringCanstant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.CategoryEntity;
import com.goodsogood.ows.model.vo.CategoryAddForm;
import com.goodsogood.ows.model.vo.CategoryListForm;
import com.goodsogood.ows.model.vo.CategoryUpdateForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.CategoryService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Description: 活动类别controller</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 9:55
 */
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/category")
public class CategoryController {
    private final Errors errors;
    private final CategoryService categoryService;

    public CategoryController(Errors errors, CategoryService categoryService) {
        this.errors = errors;
        this.categoryService = categoryService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "添加活动类别")
    @PostMapping("/add")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> addCategory(@RequestHeader HttpHeaders headers,
                                                    @Valid @RequestBody CategoryAddForm categoryFrom,
                                                    BindingResult bindingResult) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         *  3.已经存在的类别 不能添加
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        // 判断类别是否存在
        categoryFrom.setCategory(categoryFrom.getCategory().trim());//去掉字符串两端的多余的空格
        checkIsExist(categoryFrom.getCategory());
        CategoryEntity categoryEntity = categoryFrom.toEntity();
        Result<Long> result = new Result<>(categoryService.addCategory(headers, sysHeader, categoryEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "修改活动类别")
    @PostMapping("/update")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> updateCategory(@RequestHeader HttpHeaders headers,
                                                          @Valid @RequestBody CategoryUpdateForm categoryUpdateFrom,
                                                          BindingResult bindingResult) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         *  3.不能修改为已经存在的类别
         *  4.已在使用的类别不能修改
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        categoryUpdateFrom.setCategory(categoryUpdateFrom.getCategory().trim());//去掉字符串两端的多余的空格
        checkIsUse(categoryUpdateFrom.getCategoryId(), "修改！");
        // 修改后的category是否存在
        CategoryEntity ce = categoryService.listOneByName(categoryUpdateFrom.getCategory());
        if (ce != null && !ce.getCategoryId().equals(categoryUpdateFrom.getCategoryId())) {
            throw new ApiException("category exist", new Result<>(errors, 1801, HttpStatus.BAD_REQUEST.value(), categoryUpdateFrom.getCategory()));
        }
        Result<Boolean> result = new Result<>(categoryService.updateCategory(sysHeader, categoryUpdateFrom.toEntity()) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "删除活动类别")
    @DeleteMapping("/del/{id}")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> delCategory(@RequestHeader HttpHeaders headers,
                                                       @PathVariable long id) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         *  3.已经在使用的类别不能删除
         */
        ControllerHelper.getSysHeader(headers, errors);
        // 是否被使用
        checkIsUse(id, "删除！");
        Result<Boolean> result = new Result<>(categoryService.delCategory(id) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动类别列表,all")
    @GetMapping("/list")
    public ResponseEntity<Result<List<CategoryEntity>>> listAllCategory(@RequestHeader HttpHeaders headers,
                                                                        @ApiParam("类别id")
                                                                        @RequestParam(value = "category_id", required = false) Long id,
                                                                        @ApiParam("类别")
                                                                        @RequestParam(value = "category", required = false) @Length(max = 50, message = "{Length.category.category}") String category) {
        CategoryListForm categoryListFrom = new CategoryListForm();
        categoryListFrom.setCategoryId(id);
        if (StringUtils.isNotBlank(category)) {
            categoryListFrom.setCategory(category.trim());
        }
        Result<List<CategoryEntity>> result = new Result<>(categoryService.listAllCategory(categoryListFrom), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "发起活动和任务完成情况下拉选择查询类别")
    @GetMapping("/list-all/{tag}")
    public ResponseEntity<Result<List<CategoryEntity>>> listAll(@RequestHeader HttpHeaders headers,
                                                                @ApiParam("1:发起活动下拉框；2:任务完成情况下拉框")
                                                                @Range(min = 1, max = 2, message = "{Range.select.tag}") @PathVariable short tag) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<List<CategoryEntity>> result = new Result<>(categoryService.listAllCategory(sysHeader.getOid(), tag), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 校验类别是否被使用
     *
     * @param id 类别id
     */
    private void checkIsUse(long id, String operate) {
        // 是否被使用
        if (categoryService.isUse(id)) {
            throw new ApiException("is using", new Result<>(errors, 1802, HttpStatus.BAD_REQUEST.value(), StringCanstant.ACTIVITY + "类别", operate));
        }
    }

    /**
     * 校验类别是否已存在
     */
    private void checkIsExist(String category) {
        if (categoryService.isExist(category)) {
            throw new ApiException("category exist", new Result<>(errors, 1801, HttpStatus.BAD_REQUEST.value(), category));
        }
    }
}
