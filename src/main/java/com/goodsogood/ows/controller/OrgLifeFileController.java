package com.goodsogood.ows.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.FileSourceEnum;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingCommentMemberComplexMapper;
import com.goodsogood.ows.model.db.MeetingCommentMemberComplexEntity;
import com.goodsogood.ows.model.db.OrgLifeFileEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.CommentFileService;
import com.goodsogood.ows.service.CommentStaticService;
import com.goodsogood.ows.service.OrgLifeFileService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 民主生活会-附件处理
 */
@Controller
@RequestMapping("/org_life/file")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
public class OrgLifeFileController {
    private final OrgLifeFileService orgLifeFileService;
    private final
    CommentFileService commentFileService;
    CommentStaticService commentStaticService;
    MeetingCommentMemberComplexMapper meetingCommentMemberComplexMapper;
    private final Errors errors;
    private final RestTemplate myRestTemplate;

    @Autowired
    public OrgLifeFileController(OrgLifeFileService orgLifeFileService, Errors errors,
                                 CommentFileService commentFileService,
                                 CommentStaticService commentStaticService,
                                 MeetingCommentMemberComplexMapper meetingCommentMemberComplexMapper,
                                 RestTemplate myRestTemplate) {
        this.orgLifeFileService = orgLifeFileService;
        this.errors = errors;
        this.commentFileService = commentFileService;
        this.commentStaticService = commentStaticService;
        this.meetingCommentMemberComplexMapper = meetingCommentMemberComplexMapper;
        this.myRestTemplate = myRestTemplate;
    }

    @ApiOperation("查询附件")
    @HttpMonitorLogger
    @GetMapping("/query_attach")
    public ResponseEntity<Result<List<OrgLifeFileEntity>>> queryAttach(@RequestHeader HttpHeaders headers,
                                                                       @RequestParam("life_id") Long lifeId, @RequestParam("type") List<Integer> type, Integer step) {
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<OrgLifeFileEntity> list = orgLifeFileService.queryAttach(lifeId, type, step);
        Result<List<OrgLifeFileEntity>> result = new Result<>(list, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @ApiOperation("查询附件汇总详情")
    @HttpMonitorLogger
    @GetMapping("/file_info")
    public ResponseEntity<Result<Map<Integer, List<OrgLifeFileVO>>>> queryFileInfo(@RequestHeader HttpHeaders headers,
                                                                                   @RequestParam("life_id") Long lifeId, Integer step) {
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Map<Integer, List<OrgLifeFileVO>> map = orgLifeFileService.queryFileInfo(lifeId, step);
        Result<Map<Integer, List<OrgLifeFileVO>>> result = new Result<>(map, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation("查询指定上传人")
    @HttpMonitorLogger
    @GetMapping("/query_uploader")
    public ResponseEntity<Result<List<LifeFileUserVO>>> queryUploader(@RequestHeader HttpHeaders headers,
                                                                      @RequestParam("life_id") Long lifeId, Integer type, @RequestParam(value = "data_id", required = false) Long dataId) {
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<LifeFileUserVO> list = orgLifeFileService.queryUploader(lifeId, type, dataId);
        Result<List<LifeFileUserVO>> result = new Result<>(list, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("上传后保存附件到数据库")
    @HttpMonitorLogger
    @PostMapping("/save_attach")
    public ResponseEntity<Result<List<OrgLifeFileEntity>>> saveAttach(@RequestHeader HttpHeaders headers,
                                                                      @Valid @RequestBody SaveAttachForm saveAttachForm, BindingResult bindingResult) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(orgLifeFileService.saveAttach(saveAttachForm, header), errors), HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("上报材料")
    @HttpMonitorLogger
    @PostMapping("/report_attach")
    public ResponseEntity<Result<Boolean>> reportAttach(@RequestHeader HttpHeaders headers, @RequestParam("life_id") Long lifeId,
                                                        @RequestBody Map<Integer, List<OrgLifeFileVO>> lifeFileMap) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        orgLifeFileService.report(lifeId, lifeFileMap, header);
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("删除附件")
    @HttpMonitorLogger
    @GetMapping("/delete_attach")
    public ResponseEntity<Result<Boolean>> deleteAttach(@RequestHeader HttpHeaders headers,
                                                        @RequestParam("life_id") Long lifeId,
                                                        @RequestParam(value = "life_file_id") List<Long> lifeFileId,
                                                        @RequestParam(value = "step") Integer step) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(orgLifeFileService.deleteAttach(lifeId, lifeFileId, step, header), errors), HttpStatus.OK);
    }


    @RepeatedCheck
    @ApiOperation("删除上传人")
    @HttpMonitorLogger
    @GetMapping("/delete_uploader")
    public ResponseEntity<Result<Boolean>> deleteUploader(@RequestHeader HttpHeaders headers,
                                                          @RequestParam("user_id") Long userId,
                                                          @RequestParam("life_id") Long lifeId, Integer type, @RequestParam(value = "data_id", required = false) Long dataId) {
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(orgLifeFileService.deleteUploader(userId, lifeId, type, dataId), errors), HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("指定上传人并发起上传任务")
    @HttpMonitorLogger
    @PostMapping("/file_task")
    public ResponseEntity<Result<Boolean>> fileTask(@RequestHeader HttpHeaders headers,
                                                    @RequestBody LifeFileTaskForm lifeFileTaskForm) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(orgLifeFileService.fileTask(lifeFileTaskForm, header, headers), errors), HttpStatus.OK);
    }

    @ApiOperation("上传人打开任务时校验-注意参数")
    @HttpMonitorLogger
    @GetMapping("/open_task")
    public ResponseEntity<Result<Boolean>> openTask(@RequestHeader HttpHeaders headers,
                                                    @RequestParam("task_info") String taskInfo) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(orgLifeFileService.openTask(taskInfo, header), errors), HttpStatus.OK);
    }

    @GetMapping("/clone")
    @ResponseBody
    public void test(@RequestParam("life_id") Long lifeId) {
        orgLifeFileService.cloneMeetingToLife(lifeId);
    }

    @RepeatedCheck
    @ApiOperation("删除谈心谈话附件")
    @HttpMonitorLogger
    @PostMapping("/delete_talk_attach")
    public ResponseEntity<Result<Boolean>> deleteTalkFile(@RequestBody SaveAttachForm saveAttachForm) {
        orgLifeFileService.deleteTalkFile(saveAttachForm);
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }

    @GetMapping("/t1")
    @ResponseBody
    public String test1(@RequestParam("id") Long id, @RequestHeader HttpHeaders headers) throws IOException {
        commentFileService.selfTemplateHandle(id, null, headers);
        return "sucess";

//      以下可以本地调用成功，在灰度会报错，不能直接访问该地址
//        ResponseEntity<byte[]> source = null;
//        String httpUrl = "https://dangjian.cq.tobacco.gov.cn/zuul/owsz/file/file/imgdownload?img_url=/img/62d074948ab7394a23412a8dfc3abe8a.png";
//        log.debug("访问地址：" + httpUrl);
//        source = myRestTemplate.exchange(httpUrl, HttpMethod.GET, null,byte[].class );
//
//        byte[] data = source.getBody();
//        BASE64Encoder encoder = new BASE64Encoder();
//        return encoder.encode(data);

    }

    @GetMapping("/t2")
    @ResponseBody
    public String test2(@RequestParam("id") Long id, @RequestHeader HttpHeaders headers) {
        commentFileService.orgTemplateHandle(id, headers);
        return "完成";
    }

    @GetMapping("/t3")
    @ResponseBody
    public String test3(@RequestParam("id") Long id, @RequestParam("type") Integer type, @RequestHeader HttpHeaders headers) {
        if (type == 6) {
            commentFileService.delHandle(id, null, FileSourceEnum.MEETING_COMMENT, headers);
        } else {
            commentFileService.delHandle(id, null, FileSourceEnum.MEETING_ORG_COMMENT, headers);
        }
        return "完成";
    }

    @GetMapping("/t4")
    @ResponseBody
    public String test4(@RequestParam("ids") List<Long> ids, @RequestHeader HttpHeaders headers) {
        commentStaticService.saveToStatistics(ids, headers);
        return "完成";
    }

    @GetMapping("/t5")
    @ResponseBody
    public MeetingFileListVo t5(Long id, List<Integer> type) {
        return commentFileService.queryCommentFile(Collections.singletonList(1L), type);

    }

    @GetMapping("/t6")
    @ResponseBody
    public void t6() throws JsonProcessingException {
        MeetingCommentMemberComplexEntity i = meetingCommentMemberComplexMapper.selectByPrimaryKey(1);
        List<String> suggestions = new ObjectMapper().readValue(i.getComplexSuggestion(), new TypeReference<List<String>>() {
        });
        StringBuilder sb = new StringBuilder();
        for (int k = 0; k < suggestions.size(); k++) {
            sb.append(suggestions.get(k));
            sb.append("\r\n");
        }
        log.debug("t6->sb:{}", sb);
    }

    @GetMapping("/t7")
    @ResponseBody
    public void t7() {
        List<UploaderVO> list = new ArrayList<>();
        list.add(new UploaderVO(7L,"伯乐"));
        orgLifeFileService.sendDingding(19L,list,"ceshi1");
    }
}
