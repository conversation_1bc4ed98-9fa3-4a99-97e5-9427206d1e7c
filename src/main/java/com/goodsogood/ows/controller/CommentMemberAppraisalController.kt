package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.annotation.RepeatedCheck
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.MemberAppraisalBatchForm
import com.goodsogood.ows.model.vo.MemberAppraisalForm
import com.goodsogood.ows.model.vo.MemberAppraisalQueryVO
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.CommentMemberAppraisalService
import io.swagger.annotations.ApiOperation
import lombok.extern.log4j.Log4j2
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@RequestMapping("/comment-member-appraisal")
@Log4j2
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Validated
class CommentMemberAppraisalController(@Autowired val errors: Errors,
                                       @Autowired val commentMemberAppraisalService: CommentMemberAppraisalService) {

    /**
     * 查询民主评议互评列表
     * @param  type 1-普通党员查看互评列表 2-管理员查看互评列表
     */
    @HttpMonitorLogger
    @PostMapping("/user-list")
    @ApiOperation("查询民主评议互评列表详情")
    fun getCommentMember(@Valid @RequestBody queryVO: MemberAppraisalQueryVO,
                         bindingResult : BindingResult,
                         @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberAppraisalService.getMemberList(queryVO, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/get")
    @ApiOperation("查询民主评议互评详情")
    fun getCommentMemberAppraisal(@RequestParam("member_appraisal_id", required = false) memberAppraisalId: Long?,
                                  @RequestParam("comment_member_id", required = false) memberId: Long?,
                                  @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberAppraisalService.getMemberAppraisal(memberId, memberAppraisalId, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/edit")
    @ApiOperation("新增民主评议互评")
    @RepeatedCheck(check = true, time = 5) // 防重复提交
    fun insertCommentMemberAppraisal(@Valid @RequestBody form: MemberAppraisalForm,
                                     bindingResult : BindingResult,
                                     @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberAppraisalService.editMemberAppraisal(form, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/batch-deal")
    @ApiOperation("批量处理民主评议互评")
    @RepeatedCheck(check = true, time = 10) // 防重复提交
    fun batchInputAppraisal(@Valid @RequestBody form: MemberAppraisalBatchForm,
                            bindingResult : BindingResult,
                            @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberAppraisalService.batchInputAppraisal(form, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/submit")
    @ApiOperation("批量处理民主评议互评")
    @RepeatedCheck // 防重复提交
    fun submitAppraisal(@RequestParam("comment_id") commentId: Long,
                        @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberAppraisalService.submit(commentId, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/appraisal-statistical")
    @ApiOperation("获取互评统计结果")
    fun appraisalStatistical(@RequestParam("comment_member_id") commentMemberId: Long,
                           @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberAppraisalService.statisticalAppraisal(commentMemberId),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/no-appraisal")
    @ApiOperation("查询没有互评完成的人员")
    fun noAppraisalNum(@RequestParam("comment_id") commentId: Long,
                           @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberAppraisalService.memberNoAppraisal(commentId).size,
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/notice-no-appraisal")
    @ApiOperation("通知没有互评完成的人员")
    @RepeatedCheck // 防重复提交
    fun noticeNoAppraisal(@RequestParam("comment_id") commentId: Long,
                       @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberAppraisalService.sendMsg(commentId, headers),
                errors),
            HttpStatus.OK
        )
    }
}