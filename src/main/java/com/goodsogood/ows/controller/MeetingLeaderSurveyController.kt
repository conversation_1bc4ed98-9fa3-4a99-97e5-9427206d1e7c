package com.goodsogood.ows.controller

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.annotation.RepeatedCheck
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.db.MeetingLeaderSurveyEntity
import com.goodsogood.ows.model.vo.MeetingLeaderSurveyForm
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.MeetingLeaderSurveyService
import io.swagger.annotations.ApiOperation
import org.slf4j.LoggerFactory
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import java.io.IOException
import javax.validation.Valid

/**
 * <AUTHOR>
 * @date 2023/10/10
 * @description class MeetingWorkPointController
 */
@RestController
@RequestMapping("/mls")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Validated
class MeetingLeaderSurveyController(
    val errors: Errors,
    val meetingLeaderSurveyService: MeetingLeaderSurveyService,
    val objectMapper: ObjectMapper,
) {
    private val log = LoggerFactory.getLogger(MeetingLeaderSurveyController::class.java)

    // 查询党建工作要点列表
    @HttpMonitorLogger
    @PostMapping("/list")
    @ApiOperation("查询领导调研")
    fun list(
        @RequestHeader headers: HttpHeaders,
        @Valid @RequestBody form: MeetingLeaderSurveyForm,
        @RequestParam("page", required = false, defaultValue = "1") page: Int? = 1,
        @RequestParam("page_size", required = false, defaultValue = "20") pageSize: Int? = 20,
        bindingResult: BindingResult,
    ): ResponseEntity<Result<List<MeetingLeaderSurveyEntity>>> {
        val data = meetingLeaderSurveyService.listByQuery(
            form,
            page ?: 1,
            pageSize ?: 20,
            headers,
            HeaderHelper.buildMyHeader(headers)
        )
        return ResponseEntity(Result(data.toList().map {
            if (it.subject == 1) {
                it.leader = it.target
                it.target = null
            }
            it
        }, errors).also {
            it.pageSize = pageSize
            it.pageNum = data.number.plus(1)
            it.total = data.totalElements
        }, HttpStatus.OK)
    }

    // 通过党建工作要点id获取党建工作要点详情
    @HttpMonitorLogger
    @GetMapping("/get")
    @ApiOperation("获取领导调研")
    fun findById(
        @RequestHeader headers: HttpHeaders,
        @RequestParam("id") id: Long,
    ): ResponseEntity<Result<MeetingLeaderSurveyEntity>> {
        val data = meetingLeaderSurveyService.getOne(id)
        return ResponseEntity(Result(data, errors), HttpStatus.OK)
    }

    // 新增领导调研
    @HttpMonitorLogger
    @PostMapping("/append")
    @ApiOperation("获取领导调研")
    @RepeatedCheck
    fun add(
        @RequestHeader headers: HttpHeaders,
        @Valid @RequestBody entity: MeetingLeaderSurveyEntity,
        bindingResult: BindingResult,
    ): ResponseEntity<Result<MeetingLeaderSurveyEntity>> {
        if (entity.subject == 1) {
            try {
                val list: List<Map<String, Any>> =
                    objectMapper.readValue(entity.information, object : TypeReference<List<Map<String, Any>>>() {})

                val targetBuilder = StringBuilder()
                val targetIdBuilder = StringBuilder()

                for (map in list) {
                    val name = map["name"] as String?
                    val userIds = map["user_id"] as Int?

                    if (!name.isNullOrEmpty()) {
                        if (targetBuilder.isNotEmpty()) {
                            targetBuilder.append(",")
                        }
                        targetBuilder.append(name)
                    }

                    userIds?.let {
                        if (targetIdBuilder.isNotEmpty()) {
                            targetIdBuilder.append(",")
                        }
                        targetIdBuilder.append(userIds)
                    }
                }

                val target = targetBuilder.toString()
                val targetId = targetIdBuilder.toString()

                // 将结果存储到对象的属性中
                entity.target = target
                entity.targetId = targetId
            } catch (e: IOException) {
                // 处理异常情况
                log.error(e.localizedMessage, e)
            }
        }
        entity.targetId ?: throw ApiException(
            "调研对象id不能为空", Result<Any>(
                errors,
                9404,
                HttpStatus.OK.value(),
                "调研对象id不能为空"
            )
        )

        val header = HeaderHelper.buildMyHeader(headers)
        entity.id = null
        return ResponseEntity(
            Result(meetingLeaderSurveyService.saveOrUpdate(entity, headers, header), errors),
            HttpStatus.OK
        )
    }

    // 更新领导调研
    @HttpMonitorLogger
    @PostMapping("/mutate")
    @ApiOperation("更新领导调研")
    @RepeatedCheck
    fun update(
        @RequestHeader headers: HttpHeaders,
        @Valid @RequestBody entity: MeetingLeaderSurveyEntity,
        bindingResult: BindingResult,
    ): ResponseEntity<Result<MeetingLeaderSurveyEntity>> {
        entity.id ?: throw ApiException(
            "领导调研id不能为空", Result<Any>(
                errors,
                9404,
                HttpStatus.OK.value(),
                "对应的领导调研"
            )
        )
        if (entity.subject == 1) {
            try {
                val list: List<Map<String, Any>> =
                    objectMapper.readValue(entity.information, object : TypeReference<List<Map<String, Any>>>() {})

                val targetBuilder = StringBuilder()
                val targetIdBuilder = StringBuilder()

                for (map in list) {
                    val name = map["name"] as String?
                    val userIds = map["user_id"] as Int?

                    if (!name.isNullOrEmpty()) {
                        if (targetBuilder.isNotEmpty()) {
                            targetBuilder.append(",")
                        }
                        targetBuilder.append(name)
                    }

                    userIds?.let {
                        if (targetIdBuilder.isNotEmpty()) {
                            targetIdBuilder.append(",")
                        }
                        targetIdBuilder.append(userIds)
                    }
                }

                val target = targetBuilder.toString()
                val targetId = targetIdBuilder.toString()

                // 将结果存储到对象的属性中
                entity.target = target
                entity.targetId = targetId
            } catch (e: IOException) {
                // 处理异常情况
                log.error(e.localizedMessage, e)
            }
        }
        entity.targetId ?: throw ApiException(
            "调研对象id不能为空", Result<Any>(
                errors,
                9404,
                HttpStatus.OK.value(),
                "调研对象id不能为空"
            )
        )
        return ResponseEntity(
            Result(
                meetingLeaderSurveyService.saveOrUpdate(entity, headers, HeaderHelper.buildMyHeader(headers)),
                errors
            ),
            HttpStatus.OK
        )
    }

    // 删除领导调研
    @HttpMonitorLogger
    @GetMapping("/eliminate")
    @ApiOperation("删除领导调研")
    fun delete(
        @RequestHeader headers: HttpHeaders,
        @RequestParam("id") id: Long,
    ): ResponseEntity<Result<String>> {
        meetingLeaderSurveyService.deleteById(id)
        return ResponseEntity(Result("删除成功", errors), HttpStatus.OK)
    }


    //查询调查研究饼状图
    @HttpMonitorLogger
    @GetMapping("/findSurveyByYear")
    @ApiOperation("初始化调查研究饼状图")
    fun findSurveyByYear(
        @RequestHeader headers: HttpHeaders,
        @RequestParam("year") year: Int,
    ): ResponseEntity<Result<Map<String, Int>>> {
        return ResponseEntity(Result(meetingLeaderSurveyService.findSurveyByYear(year), errors), HttpStatus.OK)
    }

}