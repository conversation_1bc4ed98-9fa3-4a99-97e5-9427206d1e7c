package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.StringCanstant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.GroupEntity;
import com.goodsogood.ows.model.db.TypeEntity;
import com.goodsogood.ows.model.vo.GroupAddForm;
import com.goodsogood.ows.model.vo.GroupAllResultForm;
import com.goodsogood.ows.model.vo.GroupListForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.GroupService;
import com.goodsogood.ows.service.TypeService;
import com.goodsogood.ows.utils.PageUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Description: 活动类型组合controller</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 9:55
 */
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/type-group")
public class GroupController {
    private final Errors errors;
    private final GroupService groupService;
    private final TypeService typeService;

    public GroupController(Errors errors, GroupService groupService, TypeService typeService) {
        this.errors = errors;
        this.groupService = groupService;
        this.typeService = typeService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "添加活动类型组合")
    @PostMapping("/add")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> addTypeGroup(@RequestHeader HttpHeaders headers,
                                                     @Valid @RequestBody GroupAddForm groupAddFrom,
                                                     BindingResult bindingResult) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         *  3.所选类型是否存在
         *  4.所选类型是否是同一类别
         *  5.已经存在的组合 不能重复添加
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        List<Long> typeIds = groupAddFrom.getTypeIds().stream().distinct().sorted().collect(Collectors.toList());
        groupAddFrom.setTypeIds(typeIds);
        // 判断所选类型是否存在
        List<TypeEntity> typeEntities = typeService.listAllByIds(typeIds);
        if (typeEntities.isEmpty() || typeEntities.size() != typeIds.size()) {
            throw new ApiException("not found", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), StringCanstant.ACTIVITY + "类型"));
        }
        //所选类型是否是同一类别
        if (typeEntities.stream().map(TypeEntity::getCategoryId).distinct().count() > 1) {
            throw new ApiException("category inconformity", new Result<>(errors, 1803, HttpStatus.BAD_REQUEST.value()));
        }
        // 判断组合是否存在
        List<GroupEntity> groupEntities = groupService.listAllGroupEntity(sysHeader);
        if (groupEntities.stream().anyMatch(groupEntity -> {
            List<Long> gTypeIds = groupEntity.getTypes().stream().map(TypeEntity::getTypeId).sorted().collect(Collectors.toList());
            return StringUtils.join(gTypeIds, ",").equals(StringUtils.join(typeIds, ","));
        })) {
            throw new ApiException("group exist", new Result<>(errors, 1810, HttpStatus.BAD_REQUEST.value()));
        }
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setTypes(typeEntities);
        Result<Long> result = new Result<>(groupService.addGroup(sysHeader, groupEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "删除活动类型组合")
    @DeleteMapping("/del/{id}")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> delTypeGroup(@RequestHeader HttpHeaders headers,
                                                        @PathVariable long id) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         *  3.已经在使用的类别不能删除
         */
        ControllerHelper.getSysHeader(headers, errors);
        Result<Boolean> result = new Result<>(groupService.delGroup(id) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动类型组合列表（分页查询）")
    @GetMapping("/list")
    public ResponseEntity<Result<Page<GroupAllResultForm>>> listTypeGroup(@RequestHeader HttpHeaders headers,
                                                                          @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                                          @RequestParam(value = "page_size", required = false) Integer pageSize) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        GroupListForm groupListFrom = new GroupListForm();
        groupListFrom.setRegionId(sysHeader.getRegionId());
        groupListFrom.setPageBean(PageUtils.page(pageNo, pageSize));
        Result<Page<GroupAllResultForm>> result = new Result<>(groupService.listPageGroup(groupListFrom), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动类型组合列表")
    @GetMapping("/list-all")
    public ResponseEntity<Result<List<GroupAllResultForm>>> listAllTypeGroup(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<List<GroupAllResultForm>> result = new Result<>(groupService.listAllGroupResultForm(sysHeader), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}
