package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.common.pojo.PageBean;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.SbwNewTaskMapper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SbwHandleForm;
import com.goodsogood.ows.model.vo.SbwNewTaskForm;
import com.goodsogood.ows.service.SbwNewTaskService;
import com.goodsogood.ows.utils.PageUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;

/**
 * 南岸区信创新建任务
 *
 * <AUTHOR>
 * @date 2021/7/29
 */
@RestController
@RequestMapping("/sbw-task")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class SbwNewTaskController {

    private final Errors errors;
    private final SbwNewTaskService sbwNewTaskService;

    @Autowired
    public SbwNewTaskController(Errors errors, SbwNewTaskService sbwNewTaskService) {
        this.errors = errors;
        this.sbwNewTaskService = sbwNewTaskService;
    }

    /**
     * 　新建-转办任务
     *
     * @return
     */
    @PostMapping("/addTurn")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> createTurnTask(@Valid @RequestBody SbwNewTaskForm form,
                                                    BindingResult bindingResult,
                                                    @RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(sbwNewTaskService.addTurnTask(headers, form), errors), HttpStatus.OK);
    }
//
//    /**
//     * 　新建-工作任务
//     *
//     * @return
//     */
//    @PostMapping("/addWord")
//    @HttpMonitorLogger
//    public ResponseEntity<Result<?>> createWordTask(@Valid @RequestBody SbwNewTaskForm form,
//                                                    BindingResult bindingResult,
//                                                    @RequestHeader HttpHeaders headers) {
//        return new ResponseEntity<>(new Result<>(sbwNewTaskService.addWordTask(headers, form), errors), HttpStatus.OK);
//    }

    /**
     * 　我的任务-查询列表
     *
     * @return
     */
    @GetMapping("/myTask")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> getMyTask(@RequestHeader HttpHeaders headers,
                                               @RequestParam(required = false) String title,
                                               @DateTimeFormat(pattern = "yyyy/MM/dd")
                                               @RequestParam(value = "begin_time", required = false) Date beginTime,
                                               @DateTimeFormat(pattern = "yyyy/MM/dd")
                                               @RequestParam(value = "end_time", required = false) Date endTime,
                                               @RequestParam(value = "page_no", required = false) Integer pageNo,
                                               @RequestParam(value = "page_size", required = false) Integer pageSize) {
        PageBean pageBean = PageUtils.page(pageNo, pageSize);
        return new ResponseEntity<>(new Result<>(sbwNewTaskService.getTask(headers, title, beginTime, endTime, pageBean), errors), HttpStatus.OK);
    }

    /**
     * 　发布任务-页面展示
     *
     * @return
     */
    @GetMapping("/releaseFind")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> releaseTaskList(@RequestHeader HttpHeaders headers,
                                                     @RequestParam(required = false) String title,
                                                     @DateTimeFormat(pattern = "yyyy/MM/dd")
                                                     @RequestParam(value = "begin_time", required = false) Date beginTime,
                                                     @DateTimeFormat(pattern = "yyyy/MM/dd")
                                                     @RequestParam(value = "end_time", required = false) Date endTime,
                                                     @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                     @RequestParam(value = "page_size", required = false) Integer pageSize) {
        PageBean pageBean = PageUtils.page(pageNo, pageSize);
        return new ResponseEntity<>(new Result<>(sbwNewTaskService.releaseTaskList(headers, title, beginTime, endTime, pageBean), errors), HttpStatus.OK);
    }


    /**
     * 　我的任务-详情查看
     *
     * @return
     */
    @GetMapping("/myCheck")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> taskDetails(@RequestHeader HttpHeaders headers,
                                                 @RequestParam(value = "task_id") Long taskId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(sbwNewTaskService.taskDetails(header,taskId), errors), HttpStatus.OK);
    }

    /**
     * 　发布任务-查看
     *
     * @return
     */
    @GetMapping("/check")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> releaseTaskDetails(@RequestHeader HttpHeaders headers,
                                                        @RequestParam(value = "task_id") Long taskId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(sbwNewTaskService.releaseTaskDetails(header.getOid(),taskId), errors), HttpStatus.OK);
    }

    /**
     * 　发布任务-删除
     *
     * @return
     */
    @PostMapping("/del")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> delTask(@Valid @RequestBody SbwNewTaskForm form,
                                             BindingResult bindingResult,
                                             @RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(sbwNewTaskService.delTask(headers, form.getTaskId()), errors), HttpStatus.OK);
    }

    /**
     * 新建任务-提交转办单-查询
     *
     * @param taskId 任务id
     * @param orgId 支部id 选填
     * @param headers
     * @return
     */
    @GetMapping("/orderFind")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> showTask(@RequestParam(value = "task_id") Long taskId,
                                              @RequestParam(value = "flag")Integer flag,
                                              @RequestParam(value = "org_id",required = false) Long orgId,
                                              @RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(sbwNewTaskService.showTask(headers, taskId,flag,orgId), errors), HttpStatus.OK);
    }

    /**
     * 　新建任务-生成打印单
     *
     * @return
     */
    @GetMapping("/print")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> createPrintWord(@RequestHeader HttpHeaders headers,
                                                     @RequestParam(value = "task_id") Long taskId,
                                                     @RequestParam(value = "org_id",required = false)Long orgId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(sbwNewTaskService.createPrintWord(header,taskId,orgId), errors), HttpStatus.OK);
    }

    /**
     * 查询各个任务状态下的组织列表
     * @param taskId 任务id
     * @param flag 1:已接受任务组织
     *             2:已拒绝任务组织
     *             3:未接受任务组织
     *             4:已反馈任务组织
     *             5:已审核任务组织
     *             6:未按时反馈任务组织
     * @return
     */
    @GetMapping("/find-org")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> showTask(@RequestParam(value = "task_id") Long taskId,
                                              @RequestParam(value = "flag")Integer flag) {
        return new ResponseEntity<>(new Result<>(sbwNewTaskService.findOrg(taskId,flag), errors), HttpStatus.OK);
    }
}
