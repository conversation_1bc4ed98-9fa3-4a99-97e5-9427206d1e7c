package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.StringCanstant;
import com.goodsogood.ows.common.pojo.Headers;
import com.goodsogood.ows.common.pojo.PageBean;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.TopicEntity;
import com.goodsogood.ows.model.db.TopicOrgEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.TopicOrgForm;
import com.goodsogood.ows.service.HeaderService;
import com.goodsogood.ows.service.TopicOrgListExportService;
import com.goodsogood.ows.service.TopicOrgService;
import com.goodsogood.ows.service.TopicService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 任务管理类
 * <AUTHOR> 主题管理
 * @create 2018-10-19 16:39
 **/
@RestController
@RequestMapping("/topic")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "任务管理", tags = {"任务管理"})
@Validated
public class TopicController {

     private final Errors errors;
     private final TopicService service;
     private final TopicService topicService;
     private final HeaderService headerService;
    private final TopicOrgService topicOrgService;

     @Value("${tog-services.user-center}")
     private String userCenter;

     @Autowired
     public TopicController(Errors errors, TopicService service, TopicService topicService, HeaderService headerService, TopicOrgService topicOrgService) {
          this.errors = errors;
          this.service = service;
          this.topicService = topicService;
          this.headerService = headerService;
         this.topicOrgService = topicOrgService;
     }

     /**
      *  新增一个任务
      * @return
      * @throws Exception
      */
     @HttpMonitorLogger
     @PostMapping(value = "/add")
     @ApiOperation(value = "新增任务")
     public ResponseEntity<Result<?>> add(@RequestHeader HttpHeaders headers,
                                          @Valid @RequestBody TopicEntity topic,
                                          BindingResult bindingResult) {
         log.debug("新增任务：入参 = {}", topic);
         HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
         int code = this.topicService.add(sysHeader, topic);
         if (code != 0) {
             log.debug("新增任务出错，返回状态码 code = {}", code);
             throw new ApiException("新增任务出错", new Result<>(errors, code, HttpStatus.OK.value()));
         }

         return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }


    /**
     * 任务详情
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @GetMapping(value = "/detail")
    @ApiOperation(value = "任务详情")
    public ResponseEntity<Result<?>> detail(@RequestParam(value = "topic_id")
                                                    Long topicId) {
        TopicEntity topic = this.service.detail(topicId);
        return new ResponseEntity<>(new Result<>(topic, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping(value = "/taskOrgList/export")
    @ApiOperation(value = "导出任务执行组织列表")
    public ResponseEntity<Result<?>> orgListExport(
            HttpServletResponse response, @RequestParam(value = "id")
            Long topicId,
            @RequestParam(value = "task_status", required = false)
                    Integer taskStatus,
            @RequestParam(value = "org_name", required = false)
                    String orgName) {
        TopicOrgForm orgForm = getTopicOrgForm(topicId, taskStatus, orgName);
        // 导出
        TopicOrgListExportService exportService = new TopicOrgListExportService();
        try {
            exportService.export03(response, orgForm.getOrgs());
        } catch (IOException e) {
            throw new ApiException(
                    "导出任务执行组织列表失败！", new Result<>(errors, 2005, HttpStatus.OK.value()));
        }
        Result<String> result = new Result<>("success", errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/11/12 10:41 上午
     */

    @HttpMonitorLogger
    @GetMapping(value = "/taskOrgList")
    @ApiOperation(value = "任务详情组织列表")
    public ResponseEntity<Result<?>> detailOrgList(@RequestParam(value = "id")
                                                           Long topicId,
                                                   @RequestParam(value = "task_status", required = false)
                                                           Integer taskStatus,
                                                   @RequestParam(value = "org_name", required = false)
                                                           String orgName) {
        TopicOrgForm orgForm = getTopicOrgForm(topicId, taskStatus, orgName);
        return new ResponseEntity<>(new Result<>(orgForm, errors), HttpStatus.OK);
    }

    private TopicOrgForm getTopicOrgForm(Long topicId, Integer taskStatus, String orgName) {

        //查询基本信息
        TopicEntity topic = this.topicService.getTopicById(topicId);
        if (topic == null) {
            throw new ApiException("not found", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.OK.value(), StringCanstant.TASK + "详情"));
        }
        //统计全部，已完成，未完成的总数量
        TopicOrgForm orgForm = this.topicOrgService.sasTotal(topicId);
        orgForm.setName(topic.getName());
        //查询所有的组织
        List<TopicOrgEntity> orgs = this.topicOrgService.getTopicOrgList(topicId, taskStatus, orgName);
        orgForm.setOrgs(orgs);
        return orgForm;
    }

    /**
     * 删除任务
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @DeleteMapping(value = "/del/{topic_id}")
    @ApiOperation(value = "任务详情")
    public ResponseEntity<Result<?>> delete(@RequestHeader HttpHeaders headers,
                                            @PathVariable(value = "topic_id") Long topicId) {
        Headers header = this.headerService.bulidHeader(headers);
        this.service.delete(topicId, header.getUserId(), 0);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 修改任务
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @PostMapping(value = "/update")
    @ApiOperation(value = "修改任务")
    public ResponseEntity<Result<?>> update(@RequestHeader HttpHeaders headers,
                                            @Valid @RequestBody TopicEntity topic,
                                            BindingResult bindingResult) {
        this.service.update(topic, this.headerService.bulidHeader(headers));
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    /**
     * 任务分页查询列表 -- 查询当前组织新建的
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @GetMapping(value = "/page")
    @ApiOperation(value = "任务分页列表 -- 查询当前组织新建的")
    public ResponseEntity<Result<?>> page(@RequestHeader HttpHeaders headers,
                                          @RequestParam(required = false) String name,
                                          @DateTimeFormat(pattern = "yyyy-MM-dd")
                                          @ApiParam("开始时间的起始时间。yyyy-MM-dd")
                                          @RequestParam(value = "s_start_time", required = false)
                                                  Date sStartTime,
                                          @DateTimeFormat(pattern = "yyyy-MM-dd")
                                          @ApiParam("开始时间的结束时间。yyyy-MM-dd")
                                          @RequestParam(value = "e_start_time", required = false)
                                                  Date eStartTime,
                                          @RequestParam(value = "page_no", required = false) Integer pageNo,
                                          @RequestParam(value = "page_size", required = false) Integer pageSize) {
        Headers header = this.headerService.bulidHeader(headers);
        //处理分页信息
        PageBean pageBean = PageUtils.page(pageNo, pageSize);
        Page<TopicEntity> page = this.service.page(name, header.getOid(), sStartTime, DateUtils.toLastSecondOfDay(eStartTime), pageBean);
        return new ResponseEntity<>(new Result<>(page, errors), HttpStatus.OK);
    }

    /**
     * 整个任务列表
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @GetMapping(value = "/list")
    @ApiOperation(value = "任务列表 -- 自己收到的和自己创建的")
    public ResponseEntity<Result<?>> list(@RequestHeader HttpHeaders headers, @RequestParam(required = false) String name) {
        Headers header = this.headerService.bulidHeader(headers);
        List<TopicEntity> list = this.service.getTopicList(name, header.getOid());
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }


}
