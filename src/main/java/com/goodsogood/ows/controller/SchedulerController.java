package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.pojo.MeetingPushParam;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SchedulerConfiguration;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.service.IndexService;
import com.goodsogood.ows.service.MeetingNoticeService;
import com.goodsogood.ows.service.UserCenterService;
import com.goodsogood.ows.utils.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 定时任务手动触发器
 *
 * <AUTHOR>
 * @date 2019-06-17 9:23
 * @since 1.0.3
 **/
@RestController
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "定时任务手动触发器", tags = {"定时任务手动触发器"})
public class SchedulerController {

    private final Errors errors;

    private final MeetingNoticeService meetingNoticeService;
    private final SchedulerConfiguration schedulerConfiguration;
    private final IndexService indexService;
    private final UserCenterService userCenterService;
    private final OrgTypeConfig orgTypeConfig;

    public SchedulerController(Errors errors, MeetingNoticeService meetingNoticeService, SchedulerConfiguration schedulerConfiguration, IndexService indexService, UserCenterService userCenterService, OrgTypeConfig orgTypeConfig) {
        this.errors = errors;
        this.meetingNoticeService = meetingNoticeService;
        this.schedulerConfiguration = schedulerConfiguration;
        this.indexService = indexService;
        this.userCenterService = userCenterService;
        this.orgTypeConfig = orgTypeConfig;
    }

    @ApiOperation(value = "手动发通知")
    @GetMapping("/send/notice")
    @HttpMonitorLogger
    @RepeatedCheck
    public ResponseEntity test(@RequestParam(value = "push_model") Integer pushModel) {
        MeetingPushParam pushParam = new MeetingPushParam();
        pushParam.setPushModel(pushModel);
        log.debug("纪实手动发送考核单位定时任务配置：{}, pushParm:{}", schedulerConfiguration, pushParam);
        log.debug("scheduler.notice.run -> {}", schedulerConfiguration.getNoticeRun());
        if (schedulerConfiguration.getNoticeRun()) {
            log.debug("纪实手动发放通知定时任务开始！");
            try {
                this.meetingNoticeService.sendNotice(HttpLogAspect.getSSLog(), pushParam);
            } catch (Exception e) {
                log.error("纪实手动发放通知定时任务失败！", e);
            }
            log.debug("纪实手动发放通知定时任务结束");
        }
        return new ResponseEntity<>(new Result<>("手动发放通知成功", errors), HttpStatus.OK);
    }

    @ApiOperation(value = "刷新移动端首页缓存")
    @GetMapping("/refresh/cache-index-collect")
    @RepeatedCheck
    @HttpMonitorLogger
    public ResponseEntity refreshCacheOfIndexCollect() {
        indexService.collectRedis();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

}
