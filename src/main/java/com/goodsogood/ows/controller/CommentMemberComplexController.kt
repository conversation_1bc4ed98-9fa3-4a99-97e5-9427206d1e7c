package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.annotation.RepeatedCheck
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.CommentMemberComplexAddForm
import com.goodsogood.ows.model.vo.CommentMemberComplexBatchAddForm
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.CommentMemberComplexService
import io.swagger.annotations.ApiOperation
import lombok.extern.log4j.Log4j2
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@RequestMapping("/comment-member-complex")
@Log4j2
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Validated
class CommentMemberComplexController(@Autowired val errors: Errors,
                                     @Autowired val complexMemberComplexService: CommentMemberComplexService) {

    @HttpMonitorLogger
    @PostMapping("/insert")
    @ApiOperation("新增民主评议综合评定")
    @RepeatedCheck // 防重复提交
    fun insertMemberComplex(@Valid @RequestBody form : CommentMemberComplexAddForm,
                            bindingResult : BindingResult,
                            @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                complexMemberComplexService.insertMemberComplex(form, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/batch-insert")
    @ApiOperation("新增民主评议综合评定")
    @RepeatedCheck // 防重复提交
    fun batchInsertMemberComplex(@Valid @RequestBody form : CommentMemberComplexBatchAddForm,
                                 bindingResult : BindingResult,
                                 @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                complexMemberComplexService.batchInsertMemberComplex(form, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/get")
    @ApiOperation("查询民主评议综合评定")
    fun getMemberComplex(@RequestParam("comment_member_id") commentMemberId: Long,
                         @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                complexMemberComplexService.getMemberComplex(commentMemberId),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/complex-statistical")
    @ApiOperation("获取综合结果统计")
    fun complexStatistical(@RequestParam("comment_id") commentId: Long,
                        @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                complexMemberComplexService.statisticalComplex(commentId),
                errors),
            HttpStatus.OK
        )
    }
}