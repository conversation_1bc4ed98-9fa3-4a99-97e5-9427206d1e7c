package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.IndexForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.IndexService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2018/10/31 13:38
 */
@RestController
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "首页（移动段）", tags = {"首页汇总"})
public class IndexController {

    private final Errors errors;
    private final IndexService indexService;

    @Autowired
    public IndexController(IndexService indexService, Errors errors) {
        this.indexService = indexService;
        this.errors = errors;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "首页汇总（移动端）")
    @GetMapping("/index")
    public ResponseEntity<Result<IndexForm>> addMeeting(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<IndexForm> result = new Result<>(indexService.collect(sysHeader), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
