package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.MeetingWaitSignEntity;
import com.goodsogood.ows.model.dto.MeetingWaitSignVo;
import com.goodsogood.ows.model.vo.AddStudyCertForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.StudyAddListForm;
import com.goodsogood.ows.service.MeetingAndStudyService;
import com.goodsogood.ows.service.MeetingWaitSignService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/11/11
 */
@RestController
@RequestMapping("/w-sign")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class MeetingWaitSignController {

    private final Errors errors;
    private final MeetingWaitSignService meetingWaitSignService;
    private final MeetingAndStudyService meetingAndStudyService;

    @Autowired
    public MeetingWaitSignController(Errors errors,
                                     MeetingWaitSignService meetingWaitSignService,
                                     MeetingAndStudyService meetingAndStudyService) {
        this.errors = errors;
        this.meetingWaitSignService = meetingWaitSignService;
        this.meetingAndStudyService = meetingAndStudyService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "查询补学详情")
    @GetMapping("/find")
    public ResponseEntity<Result<MeetingWaitSignVo>> find(@RequestParam("wait_sign_id") Long waitSignId,
                                          @RequestHeader HttpHeaders headers) {
        MeetingWaitSignVo signEntity = meetingWaitSignService.find(waitSignId);
        return new ResponseEntity<>(new Result<>(signEntity, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "补学列表查询")
    @PostMapping("/queryList")
    public ResponseEntity<Result<Page<StudyAddListForm>>> queryAddStudyList(@RequestHeader HttpHeaders headers,
                                                                            @RequestBody @Valid StudyAddListForm studyAddListForm
    ) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Page<StudyAddListForm>> result = new Result<>(meetingAndStudyService.selList(studyAddListForm), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "新增补学证明")
    @PostMapping("/add")
    public ResponseEntity<Result<Boolean>> AddStudyCert(@RequestHeader HttpHeaders headers,
                                                        @RequestBody @Valid AddStudyCertForm addStudyCertForm,
                                                        BindingResult bindingResult) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Long user_id_put = sysHeader.getUserId();
        Result<Boolean> result = new Result<>(meetingAndStudyService.addStudyCert(addStudyCertForm,sysHeader), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}
