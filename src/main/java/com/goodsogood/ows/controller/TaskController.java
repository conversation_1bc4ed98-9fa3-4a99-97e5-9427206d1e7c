package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.MeetingTaskRedisService;
import com.goodsogood.ows.service.MeetingTaskService;
import com.goodsogood.ows.service.TopicOrgRedisService;
import com.goodsogood.ows.service.UserCenterService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务管理
 *
 * <AUTHOR>
 * @create 2018-10-25 10:53
 **/
@RestController
@RequestMapping("/task")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class TaskController {

    private final Errors errors;
    private final MeetingTaskService meetingTaskService;
    private final MeetingTaskRedisService meetingTaskRedisService;
    private final TopicOrgRedisService topicOrgRedisService;
    private final UserCenterService userService;

    @Autowired
    public TaskController(
            Errors errors,
            MeetingTaskService meetingTaskService,
            MeetingTaskRedisService meetingTaskRedisService,
            TopicOrgRedisService topicOrgRedisService,
            UserCenterService userService) {
        this.errors = errors;
        this.meetingTaskService = meetingTaskService;
        this.meetingTaskRedisService = meetingTaskRedisService;
        this.topicOrgRedisService = topicOrgRedisService;
        this.userService = userService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "任务管理统计")
    @GetMapping("/stats")
    public ResponseEntity<Result<TaskStatsForm>> list(@RequestHeader HttpHeaders headers) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        List<Long> orgIds = userService.getUserOidByH5(headers, sysHeader);
        TaskStatsForm taskStatsForm = new TaskStatsForm();
        // 活动未完成总数，不包含逾期任务
        MeetingTypeTaskListForm meetingTypeListForm = new MeetingTypeTaskListForm();
        meetingTypeListForm.setStatus((short) 1);
        meetingTypeListForm.setOrgIds(orgIds);
        taskStatsForm.setMeetingTaskCount(meetingTaskRedisService.taskCount(meetingTypeListForm));
        // 任务未完成总数，不包含逾期任务
        MeetingTopicTaskListForm meetingTopicTaskListForm = new MeetingTopicTaskListForm();
        meetingTopicTaskListForm.setStatus((short) 1);
        meetingTopicTaskListForm.setOrgIds(orgIds);
        taskStatsForm.setTopicTaskCount(topicOrgRedisService.taskCount(meetingTopicTaskListForm));

        Result<TaskStatsForm> result = new Result<>(taskStatsForm, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "未完成活动任务组织统计")
    @GetMapping("/undone-org/stats")
    public ResponseEntity<Result<UndoneMeeting>> undoneOrgStats(@RequestHeader HttpHeaders headers) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        try {
            Result<UndoneMeeting> result = new Result<>(meetingTaskService.undoneMeetingTaskStatistics(sysHeader), errors);
            return new ResponseEntity<>(result, HttpStatus.OK);
        } catch (ApiException e) {
            log.error("未完成活动任务组织统计查询失败->" + e.getResult().getCode(), e);
            throw e;
        } catch (Exception e) {
            log.error("未完成活动任务组织统计查询失败", e);
            throw new ApiException("未完成活动任务组织统计查询失败", new Result<>(errors, 9913, HttpStatus.OK.value()));
        }
    }

}
