package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.annotation.RepeatedCheck
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.mongo.TopPriorityEntity
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.model.vo.TopPriorityForm
import com.goodsogood.ows.service.TopPriorityService
import io.swagger.annotations.ApiOperation
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import java.time.Duration
import javax.validation.Valid

/**
 * <AUTHOR>
 * @date 2023/9/23
 * @description class TopPriorityController
 */
@RestController
@RequestMapping("/top_priority")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Validated
class TopPriorityController(
    val errors: Errors,
    val topPriorityService: TopPriorityService,
) {
    /**
     * 查询第一议题列表
     */
    @HttpMonitorLogger
    @PostMapping("/list")
    @ApiOperation("查询第一议题列表")
//    @RepeatedCheck
    fun list(
        @RequestHeader headers: HttpHeaders,
        @Valid @RequestBody form: TopPriorityForm,
        @RequestParam("page", required = false, defaultValue = "1") page: Int? = 1,
        @RequestParam("page_size", required = false, defaultValue = "20") pageSize: Int? = 20,
        bindingResult: BindingResult,
    ): ResponseEntity<Result<List<TopPriorityEntity>>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val data = topPriorityService.listByTopPriorityForm(form, header, page ?: 1, pageSize ?: 20)
        val oid = header.uoid ?: header.oid
        return ResponseEntity(Result(data.toList().map {
            // 处理是否已经被本组织关联,meetings.associatedOrg 是否存在oid
            it.associated = if (it.meetings?.find { me -> me.associatedOrg == oid } != null) {
                1
            } else {
                0
            }
            it
        }, errors).also {
            it.pageSize = pageSize
            it.pageNum = data.number.plus(1)
            it.total = data.totalElements
        }, HttpStatus.OK)
    }

    // 添加第一议题
    @HttpMonitorLogger
    @PostMapping("/append")
    @ApiOperation("添加第一议题")
    fun addTopPriority(
        @RequestHeader headers: HttpHeaders,
        @Valid @RequestBody entity: TopPriorityEntity,
        bindingResult: BindingResult,
    ): ResponseEntity<Result<String>> {
        val header = HeaderHelper.buildMyHeader(headers)
        entity.topPriorityId = null
        return ResponseEntity(
            Result(topPriorityService.addOrUpdate(entity, header, headers).topPriorityId, errors),
            HttpStatus.OK
        )
    }

    // 修改第一议题
    @HttpMonitorLogger
    @PostMapping("/mutate")
    @ApiOperation("修改第一议题")
    @RepeatedCheck
    fun updateTopPriority(
        @RequestHeader headers: HttpHeaders,
        @Valid @RequestBody entity: TopPriorityEntity,
        bindingResult: BindingResult,
    ): ResponseEntity<Result<TopPriorityEntity>> {
        entity.topPriorityId ?: throw ApiException(
            "第一议题id不能为空", Result<Any>(
                errors,
                9404,
                HttpStatus.OK.value(),
                "对应的第一议题"
            )
        )
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity(
            Result(topPriorityService.addOrUpdate(entity, header, headers), errors),
            HttpStatus.OK
        )
    }


    // 获取第一议题详情
    @HttpMonitorLogger
    @GetMapping("/get")
    @ApiOperation("获取第一议题详情")
    fun getTopPriorityById(
        @RequestHeader headers: HttpHeaders,
        @RequestParam("top_priority_id") id: String,
    ): ResponseEntity<Result<TopPriorityEntity>> {
//        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity(
            Result(topPriorityService.getTopPriorityById(id), errors), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/eliminate")
    @ApiOperation("删除第一议题")
    fun removeTopPriorityByIds(
        @RequestHeader headers: HttpHeaders,
        @RequestParam ids: List<String>,
    ): ResponseEntity<Result<Long>> {
        val header = HeaderHelper.buildMyHeader(headers)
        // 循环删除第一议题，并返回删除不成功（topPriorityService.delete(it) 返回 非"success"）的数据形成一个新数组
        val result = ids.map {
            topPriorityService.delete(it)
        }.filter {
            it != "success"
        }
        if (result.isNotEmpty()) {
            throw ApiException(
                "部分数据删除失败",
                Result<Any>(
                    errors,
                    3200,
                    HttpStatus.OK.value(),
                    result.joinToString(separator = "\r\n")
                )
            )
        }
        return ResponseEntity(Result(ids.size.toLong(), errors), HttpStatus.OK)
    }

    // 获取第一议题学习情况
    @HttpMonitorLogger
    @PostMapping("/learning_progress")
    @ApiOperation("获取第一议题学习情况")
    fun getTopPriorityLearningProgress(
        @RequestHeader headers: HttpHeaders,
        @Valid @RequestBody form: TopPriorityForm,
        @RequestParam("page", required = false, defaultValue = "1") page: Int? = 1,
        @RequestParam("page_size", required = false, defaultValue = "20") pageSize: Int? = 30,
        bindingResult: BindingResult,
    ): ResponseEntity<Result<*>> {
        val data = topPriorityService.listAssociated(
            form.unitOrgId,
            form.unitOrgName,
            if (form.learningTime?.size == 2) {
                Pair(form.learningTime?.get(0), form.learningTime?.get(1))
            } else {
                null
            },
            form.keyword,
            form.type,
            HeaderHelper.buildMyHeader(headers),
            page ?: 1,
            pageSize ?: 30
        )
        return ResponseEntity(Result(data.content.map {
            it.intervals = if (it.sourceTime != null && it.learningTime != null) {
                Duration.between(
                    it.sourceTime!!.atTime(0, 0),
                    it.learningTime!!
                ).toDays()
            } else {
                null
            }
            it
        }, errors).also {
            it.pageSize = pageSize
            it.pageNum = data.number.plus(1)
            it.total = data.totalElements
        }, HttpStatus.OK)
    }

    @HttpMonitorLogger
    @PostMapping("/by_priority")
    @ApiOperation("按议题统计")
    fun listSummaryByPriority(
        @RequestHeader headers: HttpHeaders,
        @Valid @RequestBody form: TopPriorityForm,
        @RequestParam("page", required = false, defaultValue = "1") page: Int? = 1,
        @RequestParam("page_size", required = false, defaultValue = "20") pageSize: Int? = 20,
        bindingResult: BindingResult,
    ): ResponseEntity<Result<*>> {
        return ResponseEntity(
            topPriorityService.listSummaryByPriority(
                form.unitOrgId,
                form.title,
                form.intervalTime,
                headers,
                HeaderHelper.buildMyHeader(headers),
                page ?: 1,
                pageSize ?: 20
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/by_corps")
    @ApiOperation("按单位统计(不分页)")
    fun listSummaryByPriority(
        @RequestHeader headers: HttpHeaders,
        @Valid @RequestBody form: TopPriorityForm,
        bindingResult: BindingResult,
    ): ResponseEntity<Result<*>> {
        return ResponseEntity(
            Result(
                topPriorityService.listSummaryByUnit(
                    form.unitOrgId,
                    form.unitOrgName,
                    form.intervalTime,
                    headers,
                    HeaderHelper.buildMyHeader(headers),
                ),
                errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/test/read_from_spider")
    fun readFromSpider(
        @RequestParam("start_day", required = false) startDay: String? = null,
        @RequestHeader headers: HttpHeaders,
    ): ResponseEntity<Result<*>> {
        return ResponseEntity(
            Result(
                topPriorityService.addBySpider(startDay),
                errors
            ), HttpStatus.OK
        )
    }
}