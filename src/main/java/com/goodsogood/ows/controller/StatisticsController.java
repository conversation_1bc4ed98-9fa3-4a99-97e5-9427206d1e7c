/*
package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SasUserOrgLifeForm;
import com.goodsogood.ows.model.vo.StatisticsMeetingForm;
import com.goodsogood.ows.model.vo.StatisticsUserOrgLifeForm;
import com.goodsogood.ows.service.StatisticsService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

*/
/**
 * @program: ows-meeting
 * @description: 用户统计提供数据信息 SAS调用的相关接口
 * @author: Mr.<PERSON>
 * @create: 2019-04-22 11:39
 **//*

@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/statistics")
public class StatisticsController {

    private final Errors errors;
    private final StatisticsService statisticsService;

    public StatisticsController(Errors errors,StatisticsService statisticsService) {
        this.errors = errors;
        this.statisticsService=statisticsService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "统计党支部组织生活统计")
    @GetMapping("/staOrganize")
    public ResponseEntity<Result<List<StatisticsMeetingForm>>> statisticsOrganizeInfo(@RequestHeader HttpHeaders headers,
                                                                               @NotNull(message = "组织不能为空") @RequestParam(name = "org_id") Long orgId,
                                                                               @RequestParam(name = "sta_code",required = false,defaultValue = "0") Long staTimeCode ,
                                                                               @RequestParam(name = "query_status_code",required = false,defaultValue = "0") Long queryStatusCode) {
        List<StatisticsMeetingForm> list = statisticsService.statisticsOrganizeInfo(orgId,getQueryTime(staTimeCode),getQueryStatus(queryStatusCode));
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "统计党支部用户组织生活统计")
    @GetMapping("/staOrganizeUser")
    public ResponseEntity<Result<List<StatisticsMeetingForm>>> statisticsOrganizeUserInfo( @RequestHeader HttpHeaders headers,
                                                                                    @RequestParam(name = "org_id",required = false) Long orgId,
                                                                                    @NotNull(message = "用户Id不能为空") @RequestParam(name = "user_id") Long userId,
                                                                                    @RequestParam(name = "sta_code",required = false,defaultValue = "0") Long staTimeCode,
                                                                                    @RequestParam(name = "sign_status",required = false) String signStatus) {
        List<StatisticsMeetingForm> list = statisticsService.statisticsOrganizeUserInfo(orgId,userId,getQueryTime(staTimeCode), signStatus);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @ApiOperation(value = "统计用户每月参加各类型的组织生活次数")
    @PostMapping("/sasUserOrgLife")
    public ResponseEntity<Result<List<StatisticsUserOrgLifeForm>>> sasUserOrgLife(@RequestHeader HttpHeaders headers,
                                                                                  @Valid @RequestBody SasUserOrgLifeForm sasUserOrgLifeForm,
                                                                                  BindingResult bindingResult) {
        List<StatisticsUserOrgLifeForm> list = statisticsService.sasUserOrgLife(sasUserOrgLifeForm);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }


    */
/**
 * 根据查询时间code 返回那些查询时间
 *
 * @param staTimeCode
 * @return 查询状态码
 * @return
 *//*

    private  String getQueryTime(Long staTimeCode){
            //查询所有时间
            if(staTimeCode==1){
                return  "INTERVAL 5 YEAR";
            //查询一年
            }else  if(staTimeCode==2){
                return  "INTERVAL  1 YEAR";
            //查询3个月
            }else if(staTimeCode==3){
                return  "INTERVAL  6 MONTH";
            //查询1个月
            }else if(staTimeCode==4){
                return  "INTERVAL  1 MONTH";
            //默认查询半年
            }else {
                return  "INTERVAL  6 MONTH";
            }
    }

    */
/**
 * 查询状态码
 * @return
 *//*

    private String getQueryStatus(Long queryStatusCode){
        if(queryStatusCode==null){
            return  "7,8,10,11,12,13,14";
        }
        if(queryStatusCode==0){
            return  "7,8,10,11,12,13,14";
        }else if(queryStatusCode==1){
            return  "9";
        }else {
            return  "7,8,10,11,12,13,14";
        }
    }

}
*/
