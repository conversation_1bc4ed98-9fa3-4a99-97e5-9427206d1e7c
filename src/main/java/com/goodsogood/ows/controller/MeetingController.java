package com.goodsogood.ows.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.aliyun.dingtalkcalendar_1_0.models.ListEventsResponseBody;
import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.pojo.Headers;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.MeetingStatusHelper;
import com.goodsogood.ows.mapper.MeetingUserMapper;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingHistoryEntity;
import com.goodsogood.ows.model.db.MeetingTaskEntity;
import com.goodsogood.ows.model.db.MeetingUserEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.service.*;
import com.goodsogood.ows.utils.*;
import com.goodsogood.ows.utils.redisLock.RedisLockUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-25 10:53
 **/
@RestController
@RequestMapping("/meeting")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class MeetingController {

    /**
     * 组织生活word报表导出根目录地址
     */
    @Value("${file-path}")
    private String fileParentPath;

    private final Errors errors;
    private final MeetingService service;
    private final MeetingRedisService meetingRedisService;
    private final MeetingTaskService meetingTaskService;
    private final GroupService groupService;
    private final MeetingHistoryService meetingHistoryService;
    private final HeaderService headerService;
    private final PushService pushService;
    private final UserCenterService userService;
    private final Long excludeOrgId;
    private final MeetingEventService meetingEventService;
    private final StringRedisTemplate redisTemplate;
    private final MeetingTagService meetingTagService;
    private final MeetingPeoplePartyLifeService partyLifeService;
    private final ThirdService thirdService;
    private final MeetingUserMapper meetingUserMapper;

    private final RestTemplate restTemplate;
    private final OpenService openService;

    @Autowired
    public MeetingController(Errors errors,
                             MeetingService service,
                             MeetingRedisService meetingRedisService,
                             MeetingTaskService meetingTaskService,
                             GroupService groupService,
                             MeetingHistoryService meetingHistoryService,
                             HeaderService headerService,
                             PushService pushService,
                             UserCenterService userService,
                             @Value("${excludeOrgId:-1}") Long excludeOrgId, MeetingEventService meetingEventService, StringRedisTemplate redisTemplate, MeetingTagService meetingTagService, MeetingPeoplePartyLifeService partyLifeService, ThirdService thirdService, MeetingUserMapper meetingUserMapper, RestTemplate restTemplate, OpenService openService) {
        this.errors = errors;
        this.service = service;
        this.meetingRedisService = meetingRedisService;
        this.meetingTaskService = meetingTaskService;
        this.groupService = groupService;
        this.meetingHistoryService = meetingHistoryService;
        this.headerService = headerService;
        this.pushService = pushService;
        this.userService = userService;
        this.excludeOrgId = excludeOrgId;
        this.meetingEventService = meetingEventService;
        this.redisTemplate = redisTemplate;
        this.meetingTagService = meetingTagService;
        this.partyLifeService = partyLifeService;
        this.thirdService = thirdService;
        this.meetingUserMapper = meetingUserMapper;
        this.restTemplate = restTemplate;
        this.openService = openService;
    }


    @HttpMonitorLogger
    @ApiOperation(value = "发起活动")
    @PostMapping("/add")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> addMeeting(@RequestHeader HttpHeaders headers,
                                                   @Valid @RequestBody MeetingEntity meetingEntity,
                                                   BindingResult bindingResult) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        log.info("meeting/add-发起活动入参：{}", JsonUtils.toJson(meetingEntity));
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Long> result = new Result<>(service.addMeeting(headers, sysHeader, meetingEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 日程列表
     *
     * @param headers
     * @return
     */
    @GetMapping("/scheduleList")
    public ResponseEntity<Result<?>> getScheduleList(@RequestHeader HttpHeaders headers,
                                                     @RequestParam(value = "start_date", required = false) String startDate,
                                                     @RequestParam(value = "end_date", required = false) String endDate) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        List<ListEventsResponseBody.ListEventsResponseBodyEvents> result = service.getScheduleList(sysHeader, startDate, endDate);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * @param id     活动id
     * @param isEdit 是否是编辑时查询详情	0：不是（默认）；1：是。
     */
    @HttpMonitorLogger
    @ApiOperation(value = "查询活动详情")
    @GetMapping("/detail/{id}")
    public ResponseEntity<Result<MeetingEntity>> detail(@RequestHeader HttpHeaders headers,
                                                        @PathVariable long id,
                                                        @RequestParam(value = "is_edit", required = false)
                                                        @Range(min = 0, max = 1, message = "{Range.meeting.isEdit}")
                                                        Short isEdit) {
        isEdit = isEdit == null ? MeetingService.IS_NOT_EDIT : isEdit;
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingEntity meetingEntity = service.detail(sysHeader, id, isEdit);
        Result<MeetingEntity> result = new Result<>(meetingEntity, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "更新活动")
    @PostMapping("/update")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> updateMeeting(@RequestHeader HttpHeaders headers,
                                                         @Valid @RequestBody MeetingEntity meetingEntity,
                                                         BindingResult bindingResult) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         *  3.置空活动状态
         */
        log.info("meeting/update-更新活动入参：{}", JsonUtils.toJson(meetingEntity));
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Boolean> result = new Result<>(service.updateMeeting(headers, sysHeader, meetingEntity) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "待举办添加参会人员")
    @PostMapping("/add-user")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> addUser(@RequestHeader HttpHeaders headers,
                                                   @Valid @RequestBody MeetingAddUserForm meetingAddUserForm,
                                                   BindingResult bindingResult) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         *  3.置空活动状态
         */
        log.info("meeting/add-user-待举办添加参会人员入参：{}", JsonUtils.toJson(meetingAddUserForm));
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Boolean> result = new Result<>(service.addUser(headers, sysHeader, meetingAddUserForm) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "主动撤回活动。限制：状态为检察通过、已提交、待复核，且提交组织为当前组织的，且提交日期为当月（未跨月）的组织生活，允许撤回")
    @DeleteMapping("/revoke/{id}")
//    @GetMapping("/revoke/{id}")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> revokeMeeting(@RequestHeader HttpHeaders headers,
                                                         @PathVariable long id) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        log.info("meeting/revoke/-主动撤回活动：{}", id);
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Boolean> result = new Result<>(service.revokeMeeting(headers, sysHeader, id) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "取消活动。当前组织发起的活动")
    @DeleteMapping("/cancel/{id}")
    @GetMapping("/cancel/{id}")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> cancelMeeting(@RequestHeader HttpHeaders headers,
                                                         @PathVariable long id) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        log.info("meeting/cancel/-取消活动：{}", id);
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Boolean> result = new Result<>(service.cancelMeeting(headers, sysHeader, id) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "删除活动。当前组织发起的活动")
    @DeleteMapping("/del/{id}")
    @GetMapping("/del/{id}")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> delMeeting(@RequestHeader HttpHeaders headers,
                                                      @PathVariable long id) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        log.info("meeting/del-删除活动：{}", id);
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Boolean> result = new Result<>(service.delMeeting(headers, sysHeader, id) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "任务来源")
    @GetMapping("/org/list-all")
    public ResponseEntity<Result<List<OrgForm>>> orgListAll(@RequestHeader HttpHeaders headers) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<List<OrgForm>> result = new Result<>(meetingTaskService.orgListAllByOid(sysHeader.getOid()), errors);
        if (result.getData() != null && result.getData().size() > 0) {
            Iterator<OrgForm> iterator = result.getData().iterator();
            while (iterator.hasNext()) {
                OrgForm next = iterator.next();
                if (next != null && next.getOrgId().equals(excludeOrgId)) {
                    iterator.remove();
                }
            }
        }
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动类型查询，下级组织查询，包括待完成任务字段。返回类型组合")
    @GetMapping("/type/list")
    public ResponseEntity<Result<MeetingTypeListResultForm>> typeList(@RequestHeader HttpHeaders headers,
                                                                      @ApiParam("组织生活id")
                                                                      @RequestParam(value = "plan_id", required = false) Long planId,
                                                                      @ApiParam("来源组织id")
                                                                      @RequestParam(value = "org_id", required = false) Long pOrgId,
                                                                      @ApiParam("类型id")
                                                                      @RequestParam(value = "type_id", required = false) Long typeId,
                                                                      @ApiParam("类别id")
                                                                      @RequestParam(value = "category_id", required = false) Long categoryId,
                                                                      @ApiParam("会议开始时间")
                                                                      @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
                                                                      @RequestParam(value = "start_time", required = false) Date startTime) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingTypeTaskListForm meetingTypeListForm = this.getMeetingTypeListForm(MeetingTypeTaskListForm.TAG_MEETING, planId, pOrgId, sysHeader.getOid(), typeId, categoryId);
        meetingTypeListForm.setStartTime(startTime);
        MeetingTypeListResultForm meetingTypeListResultForm = new MeetingTypeListResultForm();

        // 查询任务
        List<MeetingTaskEntity> tasks = meetingTaskService.typeList(meetingTypeListForm);
        meetingTypeListResultForm.setTasks(tasks);

        //获取类型id
        Set<Long> typeIds = tasks.stream().map(MeetingTaskEntity::getTypeId).collect(Collectors.toSet());

        // 查询任务组合
        List<GroupAllResultForm> groupAllResultForms = groupService.listAllGroupResultForm(sysHeader);
        // 单个type_id 为一个组合
        typeIds.forEach(id -> {
            GroupAllResultForm groupAllResultForm = new GroupAllResultForm();
            groupAllResultForm.setTypeIds(Collections.singletonList(id));
            groupAllResultForms.add(groupAllResultForm);
        });
        meetingTypeListResultForm.setGroups(groupAllResultForms);
        Result<MeetingTypeListResultForm> result = new Result<>(meetingTypeListResultForm, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动查询（PC端分页查询）")
    @GetMapping("/list")
    public ResponseEntity<Result<Page<MeetingEntity>>> listMeeting(@RequestHeader HttpHeaders headers,
                                                                   @ApiParam("活动名称")
                                                                   @Length(message = "{Length.meeting.name}", max = 50)
                                                                   @RequestParam(value = "name", required = false) String name,
                                                                   @RequestParam(value = "agenda", required = false) String agenda,
                                                                   @RequestParam(value = "tag_ids", required = false) List<Long> tagIds,
                                                                   @ApiParam("活动状态 ：\n" +
                                                                           "1 发起审批中 活动发起后本组织内部审批中 活动详情、取消活动、修改\n" +
                                                                           "2 发起未通过 活动发起后本组织内部审批不通过 活动详情、取消活动、修改\n" +
                                                                           "3 活动待举办 活动已发起但尚未到举办时间 活动详情、取消活动、添加人员、发送活动通知\n" +
                                                                           "4 待填报结果 状态为“活动待举办”的活动，到举办时间时，状态自动置为待填报结果 活动详情、取消活动、填写纪实情况表\n" +
                                                                           "5 填报审批中 活动填报结果在本组织内部审批中 活动详情、填写纪实情况表\n" +
                                                                           "6 填报未通过 活动填报结果在本组织内部审批未通过 活动详情、填写纪实情况表\n" +
                                                                           "7 已提交 活动结果已提交到上级组织 活动详情\n" +
                                                                           "8 退回 活动结果被上级组织退回 活动详情、填写纪实情况表、退回记录\n" +
                                                                           "9 活动已取消 活动被取消后状态置为“活动已取消” 活动详情、删除\n" +
                                                                           "13 检查通过 活动结果被上级组织检查通过处理 活动详情\n" +
                                                                           "12 待复核 存在退回记录的活动结果再次提交到上级组织 活动详情")
                                                                   @Range(min = 1, max = 13, message = "{Range.meeting.status}")
                                                                   @RequestParam(value = "status", required = false) Short status,
                                                                   @ApiParam("类型id")
                                                                   @RequestParam(value = "type_ids", required = false) List<Long> typeIds,
//                                                                   @ApiParam("组织状态(1：启用，2：禁用)") @RequestParam(value = "org_status", required = false,defaultValue = "1,3")
//                                                                               List<Short> orgStatus,
                                                                   @ApiParam("活动举办时间的起止范围开始时间")
                                                                   @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                   @RequestParam(value = "start_time", required = false) Date startTime,
                                                                   @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                   @ApiParam("活动举办时间的起止范围结束时间")
                                                                   @RequestParam(value = "end_time", required = false) Date endTime,
                                                                   @ApiParam("页码，默认为1")
                                                                   @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                                   @ApiParam("每页行数")
                                                                   @RequestParam(value = "page_size", required = false) Integer pageSize,
                                                                   @Range(min = 0, max = 1, message = "{Range.meeting.isH5}")
                                                                   @ApiParam("是否是移动端查询。0：不是；1：是") @RequestParam(value = "is_h5", required = false) Short isH5) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingListForm meetingListForm = getMeetingListForm(headers, name, agenda, tagIds, typeIds, status, startTime, endTime, pageNo, pageSize, isH5);
        // 查询当前组织的数据
        meetingListForm.setQOrgIds(Collections.singletonList(sysHeader.getOid()));
        Page<MeetingEntity> page = service.listPage(meetingListForm);
        page.forEach(meetingEntity -> // 活动状态
                meetingEntity.setStatus(MeetingStatusHelper.convertToFormStatus(meetingEntity.getStatus(), meetingEntity.getStartTime())));
        Result<Page<MeetingEntity>> result = new Result<>(page, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @HttpMonitorLogger
    @ApiOperation(value = "活动查询（PC端分页查询）")
    @GetMapping("/list-v2")
    public ResponseEntity<Result<Page<MeetingEntity>>> listMeetingV2(@RequestHeader HttpHeaders headers,
                                                                     @RequestParam(value = "name", required = false) String name,
                                                                     @RequestParam(value = "agenda", required = false) String agenda,
                                                                     @RequestParam(value = "tag_ids", required = false) List<Long> tagIds,
                                                                     @RequestParam(value = "status", required = false) Short status,
                                                                     @RequestParam(value = "type_ids", required = false) List<Long> typeIds,
                                                                     @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "start_time", required = false) Date startTime,
                                                                     @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "end_time", required = false) Date endTime,
                                                                     @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                                     @RequestParam(value = "page_size", required = false) Integer pageSize,
                                                                     @RequestParam(value = "record_type", required = false, defaultValue = "0") Integer recordType,
                                                                     @RequestParam(value = "life_id", required = false) Long lifeId,
                                                                     @RequestParam(value = "model_id", required = false) Long modelId,
                                                                     @RequestParam(value = "step", required = false) @Range(min = 1, max = 2, message = "step超出查询范围") Integer step,
                                                                     @RequestParam(value = "source_type", required = false) @Range(min = 1, max = 2) Integer sourceType) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingListForm meetingListForm = getMeetingListForm(headers, name, agenda, tagIds, typeIds, status, startTime, endTime, pageNo, pageSize, (short) 0);
        // 查询当前组织的数据
        meetingListForm.setQOrgIds(Collections.singletonList(sysHeader.getOid()));
        meetingListForm.setRecordType(recordType);
        Page<MeetingEntity> page;
        if (null == lifeId || null == modelId || null == step || null == sourceType) {
            page = service.listPageV2(meetingListForm);
        } else {
            page = partyLifeService.listMeetingByLife(modelId, lifeId, step, meetingListForm, sourceType);
        }
        Result<Page<MeetingEntity>> result = new Result<>(page, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @HttpMonitorLogger
    @ApiOperation(value = "活动查询（all）")
    @GetMapping("/list-all")
    public ResponseEntity<Result<List<MeetingEntity>>> listAllMeeting(@RequestHeader HttpHeaders headers,
                                                                      @ApiParam("活动名称")
                                                                      @Length(message = "{Length.meeting.name}", max = 50)
                                                                      @RequestParam(value = "name", required = false) String name,
                                                                      @RequestParam(value = "agenda", required = false) String agenda,
                                                                      @RequestParam(value = "tag_ids", required = false) List<Long> tagIds,
                                                                      @ApiParam("活动状态 ：\n" +
                                                                              "1 发起审批中 活动发起后本组织内部审批中 活动详情、取消活动、修改\n" +
                                                                              "2 发起未通过 活动发起后本组织内部审批不通过 活动详情、取消活动、修改\n" +
                                                                              "3 活动待举办 活动已发起但尚未到举办时间 活动详情、取消活动、添加人员、发送活动通知\n" +
                                                                              "4 待填报结果 状态为“活动待举办”的活动，到举办时间时，状态自动置为待填报结果 活动详情、取消活动、填写纪实情况表\n" +
                                                                              "5 填报审批中 活动填报结果在本组织内部审批中 活动详情、填写纪实情况表\n" +
                                                                              "6 填报未通过 活动填报结果在本组织内部审批未通过 活动详情、填写纪实情况表\n" +
                                                                              "7 已提交 活动结果已提交到上级组织 活动详情\n" +
                                                                              "8 退回 活动结果被上级组织退回 活动详情、填写纪实情况表、退回记录\n" +
                                                                              "9 活动已取消 活动被取消后状态置为“活动已取消” 活动详情、删除\n" +
                                                                              "13 检查通过 活动结果被上级组织检查通过处理 活动详情\n" +
                                                                              "12 待复核 存在退回记录的活动结果再次提交到上级组织 活动详情")
                                                                      @Range(min = 1, max = 13, message = "{Range.meeting.status}")
                                                                      @RequestParam(value = "status", required = false) Short status,
                                                                      @ApiParam("类型id")
                                                                      @RequestParam(value = "type_ids", required = false) List<Long> typeIds,
                                                                      @ApiParam("活动举办时间的起止范围开始时间")
                                                                      @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                      @RequestParam(value = "start_time", required = false) Date startTime,
                                                                      @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                      @ApiParam("活动举办时间的起止范围结束时间")
                                                                      @RequestParam(value = "end_time", required = false) Date endTime,
                                                                      @ApiParam("页码，默认为1")
                                                                      @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                                      @ApiParam("每页行数")
                                                                      @RequestParam(value = "page_size", required = false) Integer pageSize,
                                                                      @Range(min = 0, max = 1, message = "{Range.meeting.isH5}")
                                                                      @ApiParam("是否是移动端查询。0：不是；1：是") @RequestParam(value = "is_h5", required = false) Short isH5) {

        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingListForm meetingListForm = getMeetingListForm(headers, name, agenda, tagIds, typeIds, status, startTime, endTime, pageNo, pageSize, isH5);
        // 查询当前组织的数据
        meetingListForm.setQOrgIds(Collections.singletonList(sysHeader.getOid()));
        List<MeetingEntity> list = meetingRedisService.listAll(meetingListForm);
        list.forEach((meetingEntity -> // 活动状态
                meetingEntity.setStatus(MeetingStatusHelper.convertToFormStatus(meetingEntity.getStatus(), meetingEntity.getStartTime()))));
        Result<List<MeetingEntity>> result = new Result<>(list, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动管理首页汇总（移动端）")
    @GetMapping("/index")
    public ResponseEntity<Result<MeetingIndexForm>> index(@RequestHeader HttpHeaders headers) {
        Headers header = this.headerService.bulidHeader(headers);
        Result<MeetingIndexForm> result = new Result<>(meetingRedisService.meetingH5Count(header.getOid()), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 组装查询参数
     */
    private MeetingListForm getMeetingListForm(HttpHeaders headers,
                                               String name,
                                               String agenda,
                                               List<Long> tagIds,
                                               List<Long> typeIds,
                                               Short status,
                                               Date startTime,
                                               Date endTime,
                                               Integer pageNo,
                                               Integer pageSize,
                                               Short isH5) {
        MeetingListForm meetingListForm = new MeetingListForm();
        meetingListForm.setTypeIds(typeIds);
        meetingListForm.setTagIds(tagIds);
        if (StringUtils.isNotBlank(name)) {
            meetingListForm.setName(name.trim());
        }
        if (StringUtils.isNotBlank(agenda)) {
            meetingListForm.setAgenda(agenda.trim());
        }
        meetingListForm.setEndTime(DateUtils.toLastSecondOfDay(endTime));
        meetingListForm.setStatus(status);
        meetingListForm.setStartTime(startTime);
        meetingListForm.setIsH5(isH5);
        meetingListForm.setPageBean(PageUtils.page(pageNo, pageSize));
        return meetingListForm;
    }

    /**
     * 组织生活报表查询参数
     */
    private MeetingListForm getMeetingListForm(HttpHeaders headers,
                                               String name,
                                               String agenda,
                                               List<Long> typeIds,
                                               Short status,
                                               Date startTime,
                                               Date endTime,
                                               Short isH5) {
        MeetingListForm meetingListForm = new MeetingListForm();
        meetingListForm.setTypeIds(typeIds);
        if (StringUtils.isNotBlank(name)) {
            meetingListForm.setName(name.trim());
        }
        if (StringUtils.isNotBlank(agenda)) {
            meetingListForm.setAgenda(agenda.trim());
        }
        meetingListForm.setEndTime(DateUtils.toLastSecondOfDay(endTime));
        meetingListForm.setStatus(status);
        meetingListForm.setStartTime(startTime);
        meetingListForm.setIsH5(isH5);
        return meetingListForm;
    }

    /**
     * 活动类型查询参数
     */
    private MeetingTypeTaskListForm getMeetingTypeListForm(Short tag,
                                                           Long planId,
                                                           Long pOrgId,
                                                           Long orgId,
                                                           Long typeId,
                                                           Long categoryId
    ) {
        MeetingTypeTaskListForm meetingTypeListForm = new MeetingTypeTaskListForm();
        meetingTypeListForm.setTag(tag);
        meetingTypeListForm.setPlanId(planId);
        meetingTypeListForm.setPOrgId(pOrgId);
        meetingTypeListForm.setOrgId(orgId);
        if (typeId != null) {
            meetingTypeListForm.setTypeIds(Collections.singletonList(typeId));
        }
        meetingTypeListForm.setCategoryId(categoryId);
        return meetingTypeListForm;
    }

    // 2018-11-7-07 10:42 zhangchuanhao(修改)
    @HttpMonitorLogger
    @ApiOperation(value = "获取活动历史记录")
    @GetMapping("/hs/{meeting_id}")
    public ResponseEntity<Result<List<MeetingHistoryEntity>>> getMeetingHistoryList(@PathVariable("meeting_id") long meetingId) {
        List<MeetingHistoryEntity> meetingHistoryList = meetingHistoryService.getMeetingHistoryList(meetingId);
        meetingHistoryList.forEach(temp -> {
            temp.setStatus(MeetingStatusHelper.convertToFormStatus(temp.getStatus().shortValue(), null).intValue());
        });
        Result<List<MeetingHistoryEntity>> result = new Result<>(meetingHistoryList, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "签到列表")
    @GetMapping("/sign-list")
    @RepeatedCheck
    public ResponseEntity<Result<List<MeetingUserEntity>>> signList(@RequestParam("meeting_id") Long meetingId) {
        Result<List<MeetingUserEntity>> result = new Result<>(service.signList(meetingId), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动签到二维码")
    @GetMapping("/sign-qr-code")
    @RepeatedCheck
    public ResponseEntity<Result<QrCodeForm>> signQrCod(HttpServletResponse response,
                                                        @RequestParam("meeting_id") Long meetingId,
                                                        @RequestParam(name = "is_dynamic", required = false, defaultValue = "0") Integer isDynamic,
                                                        @RequestParam(name = "allow_all_sign", required = false, defaultValue = "-1") Integer allowAllSign) throws Exception {
        Result<QrCodeForm> result = new Result<>(service.signQrCod(meetingId, isDynamic, allowAllSign, response), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动签到")
    @PostMapping("/sign-in")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> signIn(@RequestHeader HttpHeaders headers,
                                                  @Valid @RequestBody MeetingSignInForm meetingSignInForm,
                                                  BindingResult bindingResult) {
        Headers header = this.headerService.bulidHeader(headers);
        Result<Boolean> result = new Result<>(service.signIn(headers, header, meetingSignInForm), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @HttpMonitorLogger
    @PostMapping("/notice")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> notice(@RequestBody @Valid MeetingNoticeForm meetingNoticeForm,
                                                  BindingResult bindingResult, @RequestHeader HttpHeaders headers) throws UnsupportedEncodingException {
        log.info("meeting/notice-活动通知入参：{}", JsonUtils.toJson(meetingNoticeForm));
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Boolean> result = new Result<>(pushService.meetingNoticeByGlobalPushSame(headers, sysHeader, meetingNoticeForm), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动回顾")
    @GetMapping("/review")
    public ResponseEntity<Result<Page<MeetingEntity>>> list(
            @RequestHeader HttpHeaders headers,
            @ApiParam("活动名称或议程名称(模糊匹配)") @RequestParam(value = "key_word", required = false) String keyWord,
            @RequestParam(value = "tag_ids", required = false) List<Long> tagIds,
            // 2019-02-12 11:45:23 新增条件 chenanshn
            @ApiParam(value = "所属类别") @RequestParam(name = "meeting_class", required = false)
            Integer meetingClass,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "提交时间（开始）")
            @RequestParam(name = "submit_start_time", required = false)
            Date submitStartTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "提交时间（结束）")
            @RequestParam(name = "submit_end_time", required = false)
            Date submitEndTime,
            @ApiParam("活动状态") @RequestParam(value = "status", required = false) Short status,
            @ApiParam("组织名称或活动名称(模糊匹配)") @RequestParam(value = "key", required = false) String key,
            @ApiParam("活动类型") @RequestParam(value = "type_ids", required = false) List<Long> typeIds,
            @ApiParam("组织id") @RequestParam(value = "org_ids", required = false) List<Long> orgIds,
            @ApiParam("组织状态(1：启用，2：禁用)") @RequestParam(value = "org_status", required = false, defaultValue = "1,3")
            List<Short> orgStatus,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("活动开始开始起始时间。yyyy-MM-dd")
            @RequestParam(value = "start_time", required = false)
            Date startTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("活动结束结束截止时间。yyyy-MM-dd")
            @RequestParam(value = "end_time", required = false)
            Date endTime,
            @ApiParam("页码，默认为1") @RequestParam(value = "page_no", required = false) Integer pageNo,
            @ApiParam("每页行数") @RequestParam(value = "page_size", required = false) Integer pageSize,
            @ApiParam("是否包含下级 1:仅查询选择的组织 2.包含所选组织及其下级组织（默认）") @RequestParam(value = "only_current_org", required = false, defaultValue = "2") Integer onlyCurrentOrg) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        //追加组织状态条件
        if (CollectionUtils.isNotEmpty(orgStatus)) {
            if (orgStatus.contains((short) 1) && !orgStatus.contains((short) 3)) {
                orgStatus.add((short) 3);
            }
        }
        /*2019年1月21日 11:38:17 chenanshun
         *TOGT4-944
         *活动回顾：组织名称或活动名称，活动开始时间段查询有问题
         */
        // 条件
        MeetingListForm meetingListForm = new MeetingListForm();
        meetingListForm.setIsReview((short) 1);
        meetingListForm.setMeetingClass(meetingClass);
        meetingListForm.setSubmitStartTime(DateUtils.to0SecondOfDay(submitStartTime));
        meetingListForm.setSubmitEndTime(DateUtils.toLastSecondOfDay(submitEndTime));
        meetingListForm.setStatus(status);
        meetingListForm.setOrgStatus(orgStatus);
        meetingListForm.setKeyWord(keyWord);
        meetingListForm.setTagIds(tagIds);
        if (StringUtils.isNotBlank(key)) {
            meetingListForm.setKey(key.trim());
        }
        /* 默认查询组织
           1.当前组织
           2.查询当前组织的下级组织
        */
        if (orgIds == null || orgIds.isEmpty()) {
            orgIds = new ArrayList<>();
            // 当前登录组织
            orgIds.add(sysHeader.getOid());
        }
        Integer includeDel = 0;
        if (orgStatus.contains((short) 2)) {
            includeDel = 1;
        }
        if (1 != onlyCurrentOrg) {
            // 当前orgIds只有一个。如果不止一个时，此处需要循环获取各组织的下级
            orgIds.addAll(userService.findAllChildOrg(sysHeader, orgIds.get(0), includeDel).stream().map(OrganizationBase::getOrgId).collect(Collectors.toList()));
        }
        meetingListForm.setQOrgIds(orgIds);
        meetingListForm.setTypeIds(typeIds);
        // TOGT5-47  活动回顾：查询接口中不应该有默认时间 2019-02-25 13:51:20 chenanshun
        meetingListForm.setStartTime(DateUtils.to0SecondOfDay(startTime));
        meetingListForm.setEndTime(DateUtils.toLastSecondOfDay(endTime));
        meetingListForm.setPageBean(PageUtils.page(pageNo, pageSize));
        log.debug("review 查询参数：{}", JsonUtils.toJson(meetingListForm));
//        meetingListForm.setIsH5((short)1);
        Page<MeetingEntity> page = service.listPage(meetingListForm);
        page.forEach(meetingEntity -> { // 活动状态
            meetingEntity.setMeetingStatus(MeetingStatusHelper.convertToFormStatus(meetingEntity.getStatus(), meetingEntity.getStartTime()));
            //获取组织简称
            OrganizationBase orgBase = thirdService.findOrgInfoByOrgId(meetingEntity.getOrgId());
            if (orgBase != null && StringUtils.isNotBlank(orgBase.getShortName())) {
                meetingEntity.setOrgShortName(orgBase.getShortName());
            } else {
                //如果简称没有就使用全称
                meetingEntity.setOrgShortName(meetingEntity.getOrgName());
            }
        });
        Result<Page<MeetingEntity>> result = new Result<>(page, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "组织概况/活动回顾（增加参与人数返回）")
    @GetMapping("/static/review")
    public ResponseEntity<Result<Page<MeetingStaticReviewVo>>> staticList(
            @RequestHeader HttpHeaders headers,
            @ApiParam("活动名称或议程名称(模糊匹配)") @RequestParam(value = "key_word", required = false) String keyWord,
            @ApiParam("排序1-正序 2-倒叙") @RequestParam(value = "order_type", required = false) Integer orderType,
            @RequestParam(value = "tag_ids", required = false) List<Long> tagIds,
            // 2019-02-12 11:45:23 新增条件 chenanshn
            @ApiParam(value = "所属类别") @RequestParam(name = "meeting_class", required = false)
            Integer meetingClass,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "提交时间（开始）")
            @RequestParam(name = "submit_start_time", required = false)
            Date submitStartTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "提交时间（结束）")
            @RequestParam(name = "submit_end_time", required = false)
            Date submitEndTime,
            @ApiParam("活动状态") @RequestParam(value = "status", required = false) Short status,
            @ApiParam("组织名称或活动名称(模糊匹配)") @RequestParam(value = "key", required = false) String key,
            @ApiParam("活动类型") @RequestParam(value = "type_ids", required = false) List<Long> typeIds,
            @ApiParam("组织id") @RequestParam(value = "org_ids", required = false) List<Long> orgIds,
            @ApiParam("组织状态(1：启用，2：禁用)") @RequestParam(value = "org_status", required = false, defaultValue = "1,3")
            List<Short> orgStatus,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("活动开始开始起始时间。yyyy-MM-dd")
            @RequestParam(value = "start_time", required = false)
            Date startTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("活动结束结束截止时间。yyyy-MM-dd")
            @RequestParam(value = "end_time", required = false)
            Date endTime,
            @ApiParam("页码，默认为1") @RequestParam(value = "page_no", required = false, defaultValue = "1") Integer pageNo,
            @ApiParam("每页行数") @RequestParam(value = "page_size", required = false, defaultValue = "5") Integer pageSize,
            @ApiParam("是否包含下级 1:仅查询选择的组织 2.包含所选组织及其下级组织（默认）") @RequestParam(value = "only_current_org", required = false, defaultValue = "2") Integer onlyCurrentOrg) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        //追加组织状态条件
        if (CollectionUtils.isNotEmpty(orgStatus)) {
            if (orgStatus.contains((short) 1) && !orgStatus.contains((short) 3)) {
                orgStatus.add((short) 3);
            }
        }
        /*2019年1月21日 11:38:17 chenanshun
         *TOGT4-944
         *活动回顾：组织名称或活动名称，活动开始时间段查询有问题
         */
        // 条件
        MeetingListForm meetingListForm = new MeetingListForm();
        meetingListForm.setOrderType(orderType);
        meetingListForm.setIsReview((short) 1);
        meetingListForm.setMeetingClass(meetingClass);
        meetingListForm.setSubmitStartTime(DateUtils.to0SecondOfDay(submitStartTime));
        meetingListForm.setSubmitEndTime(DateUtils.toLastSecondOfDay(submitEndTime));
        meetingListForm.setStatus(status);
        meetingListForm.setOrgStatus(orgStatus);
        meetingListForm.setKeyWord(keyWord);
        meetingListForm.setTagIds(tagIds);
        if (StringUtils.isNotBlank(key)) {
            meetingListForm.setKey(key.trim());
        }
        /* 默认查询组织
           1.当前组织
           2.查询当前组织的下级组织
        */
        if (orgIds == null || orgIds.isEmpty()) {
            orgIds = new ArrayList<>();
            // 当前登录组织
            orgIds.add(sysHeader.getOid());
        }
        Integer includeDel = 0;
        if (orgStatus.contains((short) 2)) {
            includeDel = 1;
        }
        if (1 != onlyCurrentOrg) {
            // 当前orgIds只有一个。如果不止一个时，此处需要循环获取各组织的下级
            orgIds.addAll(userService.findAllChildOrg(sysHeader, orgIds.get(0), includeDel).stream().map(OrganizationBase::getOrgId).collect(Collectors.toList()));
        }
        meetingListForm.setQOrgIds(orgIds);
        meetingListForm.setTypeIds(typeIds);
        // TOGT5-47  活动回顾：查询接口中不应该有默认时间 2019-02-25 13:51:20 chenanshun
        meetingListForm.setStartTime(DateUtils.to0SecondOfDay(startTime));
        meetingListForm.setEndTime(DateUtils.toLastSecondOfDay(endTime));
        meetingListForm.setPageBean(PageUtils.page(pageNo, pageSize));
        log.debug("review 查询参数：{}", JsonUtils.toJson(meetingListForm));
//        meetingListForm.setIsH5((short)1);
        Page<MeetingEntity> page = service.listPage(meetingListForm);
        List<MeetingStaticReviewVo> meetingStaticReviewVos = new ArrayList<>();
        page.forEach(meetingEntity -> { // 活动状态
            MeetingStaticReviewVo meetingStaticReviewVo = BeanUtil.copy(meetingEntity,MeetingStaticReviewVo.class);
            meetingStaticReviewVo.setMeetingStatus(MeetingStatusHelper.convertToFormStatus(meetingEntity.getStatus(), meetingEntity.getStartTime()));
            //获取参与人数
            Example example = new Example(MeetingUserEntity.class);
            example.createCriteria().andEqualTo("meetingId",meetingStaticReviewVo.getMeetingId())
                    .andEqualTo("signStatus",1);
            Integer joinNum = meetingUserMapper.selectCountByExample(example);
            meetingStaticReviewVo.setJoinNum(joinNum);
            meetingStaticReviewVos.add(meetingStaticReviewVo);
        });
        Page<MeetingStaticReviewVo> meetingStaticReviewVosPage = new Page<>();
        meetingStaticReviewVosPage.setPageNum(page.getPageNum());
        meetingStaticReviewVosPage.setPages(page.getPages());
        meetingStaticReviewVosPage.setPageSize(page.getPageSize());
        meetingStaticReviewVosPage.setTotal(page.getTotal());
        meetingStaticReviewVosPage.addAll(meetingStaticReviewVos);
        Result<Page<MeetingStaticReviewVo>> result = new Result<>(meetingStaticReviewVosPage, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("活动回顾报表")
    @GetMapping("/report")
    public void meetingReport(
            HttpServletResponse response,
            @RequestHeader HttpHeaders headers,
            @RequestParam(required = false) List<Integer> head,
            @RequestParam(value = "key", required = false) String key,
            @ApiParam("活动名称或议程名称(模糊匹配)") @RequestParam(value = "key_word", required = false) String keyWord,
            @RequestParam(value = "tag", required = false) List<Long> tag,
            // 2019-02-12 11:45:23 新增条件 chenanshn
            @ApiParam(value = "所属类别") @RequestParam(name = "meeting_class", required = false)
            Integer meetingClass,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "提交时间（开始）")
            @RequestParam(name = "submit_start_time", required = false)
            Date submitStartTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "提交时间（结束）")
            @RequestParam(name = "submit_end_time", required = false)
            Date submitEndTime,
            @ApiParam("活动状态") @RequestParam(value = "status", required = false) Short status,
            @ApiParam("活动类型") @RequestParam(value = "type_ids", required = false) List<Long> typeIds,
            @ApiParam("组织id") @RequestParam(value = "org_ids", required = false) List<Long> orgIds,
            @ApiParam("组织状态(1：启用，2：禁用)") @RequestParam(value = "org_status", required = false, defaultValue = "1,3")
            List<Short> orgStatus,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("活动开始开始起始时间。yyyy-MM-dd")
            @RequestParam(value = "start_time", required = false)
            Date startTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("活动结束结束截止时间。yyyy-MM-dd")
            @RequestParam(value = "end_time", required = false)
            Date endTime,
            @ApiParam("是否包含下级 1:仅查询选择的组织 2.包含所选组织及其下级组织（默认）") @RequestParam(value = "only_current_org", required = false, defaultValue = "2") Integer onlyCurrentOrg) throws IOException {
        /* *
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        //追加组织状态条件
        if (CollectionUtils.isNotEmpty(orgStatus)) {
            if (orgStatus.contains((short) 1) && !orgStatus.contains((short) 3)) {
                orgStatus.add((short) 3);
            }
        }
        /*2019年1月21日 11:38:17 chenanshun
         *TOGT4-944
         *活动回顾：组织名称或活动名称，活动开始时间段查询有问题
         */
        // 条件
        MeetingListForm meetingListForm = new MeetingListForm();
        meetingListForm.setIsReview((short) 1);
        meetingListForm.setMeetingClass(meetingClass);
        meetingListForm.setKeyWord(keyWord);
        meetingListForm.setTagIds(tag);
        meetingListForm.setSubmitStartTime(DateUtils.to0SecondOfDay(submitStartTime));
        meetingListForm.setSubmitEndTime(DateUtils.toLastSecondOfDay(submitEndTime));
        meetingListForm.setStatus(status);
        meetingListForm.setOrgStatus(orgStatus);
        meetingListForm.setStartTime(startTime);
        meetingListForm.setEndTime(DateUtils.toLastSecondOfDay(endTime));
        if (StringUtils.isNotBlank(key)) {
            meetingListForm.setKey(key.trim());
        }
        /* 默认查询组织
           1.当前组织
           2.查询当前组织的下级组织
        */
        if (orgIds == null || orgIds.isEmpty()) {
            orgIds = new ArrayList<>();
            // 当前登录组织
            orgIds.add(sysHeader.getOid());
        }
        Integer includeDel = 0;
        if (orgStatus.contains((short) 2)) {
            includeDel = 1;
        }
        if (1 != onlyCurrentOrg) {
            // 当前orgIds只有一个。如果不止一个时，此处需要循环获取各组织的下级
            orgIds.addAll(userService.findAllChildOrg(sysHeader, orgIds.get(0), includeDel).stream().map(OrganizationBase::getOrgId).collect(Collectors.toList()));
        }
        meetingListForm.setQOrgIds(orgIds);
        meetingListForm.setTypeIds(typeIds);
        List<MeetingReportVo> vos = service.meetingReport(meetingListForm);

        //表头字段
        if (CollectionUtils.isNotEmpty(head)) {
            head.add(0);
        }
        log.debug("表头 -> {}", head);
        String fileName = URLEncoder.encode("活动报表", "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

        // excel头策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 11);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.index);

        // excel内容策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置handler
        HorizontalCellStyleStrategy styleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

        EasyExcel.write(response.getOutputStream(), MeetingReportVo.class)
                .includeColumnIndexes(head)
                .sheet("sheet1")
                .registerWriteHandler(styleStrategy)
                .doWrite(vos);
    }


    @HttpMonitorLogger
    @ApiOperation(value = "组织生活word报表下载")
    @GetMapping("/export")
    @RepeatedCheck
    public ResponseEntity<Result<?>> downloadWord(@RequestHeader HttpHeaders headers,
                                                  @RequestParam List<Long> ids,
                                                  @RequestParam List<Integer> flag) {
        String uid = UUID.randomUUID().toString().replace("-", "");//key
        meetingEventService.getStreamData(headers, ids, MeetingService.IS_NOT_EDIT, flag, uid);
        return new ResponseEntity<>(new Result<>(uid, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "组织生活word报表文件流")
    @GetMapping("/download")
    @RepeatedCheck
    public ResponseEntity<Result<?>> download(@RequestHeader HttpHeaders headers,
                                              @RequestParam String uuid) {
        if (uuid == null) {
            throw new ApiException("下载失败",
                    new Result<>(errors, HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        String value;
        if (redisTemplate.hasKey(uuid)) {
            value = redisTemplate.opsForValue().get(uuid);
        } else {
            FileUtil.deleteDirectory(fileParentPath);//清空父目录下生成的垃圾文件
            return new ResponseEntity<>(new Result<>(0, errors), HttpStatus.OK);
        }
        if ("error".equals(value)) {
            FileUtil.deleteDirectory(fileParentPath);//清空父目录下生成的垃圾文件
            throw new ApiException("组织生活报告导出失败 ", new Result<>(errors, 2005, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        List<FileForm> forms = (List<FileForm>) JsonUtils.fromJson(value, List.class, FileForm.class);
        return new ResponseEntity<>(new Result<>(forms, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "组织生活word报表文件流")
    @GetMapping("/signIn")
    @RepeatedCheck
    public ResponseEntity<Result<?>> downloadSignIn(@RequestHeader HttpHeaders headers,
                                                    @RequestParam Long id) {
        List<FileForm> result = meetingEventService.getSignInInputStream(headers, id, (short) 0);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "删除活动中对指定标签的引用")
    @GetMapping("/tag/del")
    @RepeatedCheck
    public ResponseEntity<Result<?>> tagDel(@RequestHeader HttpHeaders headers,
                                            @RequestParam(value = "tag_id") Long tagId) {
        Boolean re = meetingTagService.tagDel(tagId);
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "修改活动中对指定标签的名称")
    @GetMapping("/tag/update")
    @RepeatedCheck
    public ResponseEntity<Result<?>> tagUpdName(@RequestHeader HttpHeaders headers,
                                                @RequestParam(value = "tag_id") Long tagId,
                                                @RequestParam(value = "tag_name") String tagName) {
        Boolean re = meetingTagService.tagUpd(tagId, tagName);
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "批量编辑活动标签")
    @PostMapping("/tag/edit")
    @RepeatedCheck
    public ResponseEntity<Result<?>> tagEdit(@RequestHeader HttpHeaders headers,
                                             @Valid @RequestBody TagEditForm tagEditForm,
                                             BindingResult bindingResult) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        meetingTagService.tagEdit(tagEditForm, header.getUserId());
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }

    /**
     * 发起活动同步钉钉日程的签到状态
     *
     * @param meetingId 会议编号
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/ding/event/sync")
    @ApiOperation("发起活动同步钉钉日程的签到状态")
    public ResponseEntity<Result<?>> testDingEventSync(@RequestHeader HttpHeaders headers,
                                                       @RequestParam(value = "meeting_id", required = false) Long meetingId) {
        String logTxt = "发起活动同步钉钉日程的签到状态";
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long regionId = header.getRegionId();
        if (null == regionId) {
            throw new ApiException("区县编号不能为空", new Result<>(errors, 2009, HttpStatus.OK.value(), "区县编号不能为空"));
        }

        log.debug(logTxt + " regionId={} statsDate={}", regionId, meetingId);

        String requestId = UUID.randomUUID().toString();
        //获取分布式锁
        boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, "MEETING_DING_EVENT_SYNC_LOCK_" + regionId + "_" + meetingId, requestId, 15 * 60 * 1000);
        log.debug(logTxt + " 是否拿到锁:  requestId = {} lock = {}", requestId, lock);
        try {
            if (lock) {
                log.debug(logTxt + "  拿到锁，开始执行代码~");
                service.dingEventSync(regionId, Collections.singletonList(meetingId), null);
            }
        } catch (Exception e) {
            log.error(logTxt + " 报错!", e);
            throw new ApiException(logTxt + "报错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), logTxt + "报错"));
        } finally {
            if (lock) {
                //解锁
                log.debug(logTxt + "执行完毕，释放锁:  requestId = {}", requestId);
                RedisLockUtil.releaseDistributedLock(redisTemplate, "MEETING_DING_EVENT_SYNC_LOCK_" + regionId + "_" + meetingId, requestId);
            }
        }
        return new ResponseEntity<>(new Result<>(meetingId, errors), HttpStatus.OK);
    }


    /**
     * 有日程的活动结束后同步钉钉日程的签到状态，手动调用接口(非前端接口)
     *
     * @param meetingIds 会议编号   多个逗号间隔
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/test/dingEventSync")
    @ApiOperation("有日程的活动结束后同步钉钉日程的签到状态手动调用")
    public ResponseEntity<Result<?>> testDingEventSync(@RequestParam(value = "region_id", required = false) Long regionId,
                                                       @RequestParam(value = "ding_event_sync", required = false) Integer dingEventSync,
                                                       @RequestParam(value = "meeting_ids", required = false) String meetingIds) {
        String logTxt = "有日程的活动结束后同步钉钉日程的签到状态手动调用";
        if (null == regionId) {
            throw new ApiException("区县编号不能为空", new Result<>(errors, 2009, HttpStatus.OK.value(), "区县编号不能为空"));
        }
        List<Long> meetingIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(meetingIds)) {
            meetingIdList = Arrays.stream(meetingIds.split(",")).map(s -> {
                return Long.valueOf(s);
            }).collect(Collectors.toList());
        }

        log.debug(logTxt + " regionId={} statsDate={}", regionId, meetingIds);

        String requestId = UUID.randomUUID().toString();
        //获取分布式锁
        boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, "MEETING_DING_EVENT_SYNC_LOCK_TEST_" + regionId, requestId, 20 * 1000);
        log.debug(logTxt + " 是否拿到锁:  requestId = {} lock = {}", requestId, lock);
        try {
            if (lock) {
                log.debug(logTxt + "  拿到锁，开始执行代码~");
                service.dingEventSync(regionId, meetingIdList, dingEventSync);
            }
        } catch (Exception e) {
            log.error(logTxt + " 报错!", e);
            throw new ApiException(logTxt + "报错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), logTxt + "报错"));
        } finally {
            if (lock) {
                //解锁
                log.debug(logTxt + "执行完毕，释放锁:  requestId = {}", requestId);
                RedisLockUtil.releaseDistributedLock(redisTemplate, "MEETING_DING_EVENT_SYNC_LOCK_TEST", requestId);
            }
        }
        return new ResponseEntity<>(new Result<>(meetingIds, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/statistical/user-front-page")
    @ApiOperation("首页统计参加活动次数及出勤率")
    public ResponseEntity<Result<?>> statisticalUserJoinTimes(@RequestHeader HttpHeaders headers,
                                                              @RequestParam(value = "year", required = false) Integer year) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        year = (null == year ? LocalDate.now().getYear() : year);
        Map<String, Object> map = service.statisticalUserJoinTimes(year, header.getUserId());
        return new ResponseEntity<>(new Result<>(map, errors), HttpStatus.OK);

    }

    @HttpMonitorLogger
    @GetMapping("/statistical/org-front-page")
    @ApiOperation("支部首页统计参加活动次数及出勤率")
    public ResponseEntity<Result<?>> statisticalOrgJoinTimes(@RequestHeader HttpHeaders headers,
                                                             @RequestParam(value = "year", required = false) Integer year,
                                                             @RequestParam(value = "org_id", required = false) Long orgId
    ) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        year = (null == year ? LocalDate.now().getYear() : year);
        orgId = (orgId == null ? header.getOid() : orgId);
        Map<String, Object> map = service.statisticalOrgJoinTimes(year, orgId);
        return new ResponseEntity<>(new Result<>(map, errors), HttpStatus.OK);

    }

    @GetMapping("/query-meeting-task")
    public ResponseEntity<Result<?>> queryMeetingTask(@RequestHeader HttpHeaders headers,
                                                      @RequestParam("type_id") Long typeId,
                                                      @RequestParam("org_id") Long orgId,
                                                      @RequestParam("stime") String stime,
                                                      @RequestParam("etime") String etime) {
        log.debug("mt开始查询/query-meeting-task");
        return new ResponseEntity<>(new Result<>(service.queryMeetingTask(typeId, orgId, stime, etime), errors), HttpStatus.OK);
    }

    @GetMapping("/header-json")
    public void tranfHead(@RequestHeader HttpHeaders headers) {
        String s = JsonUtils.toJson(headers);
        log.debug("获取烟草请求头json：" + s);
    }

    @GetMapping("/query-meeting-agenda")
    public ResponseEntity<Result<?>> queryMeetingAgenda(@RequestHeader HttpHeaders headers,
                                                        @RequestParam("meeting_id") Long meetingId) {
        log.debug("mt开始查询/query-meeting-agenda=>{}", meetingId);
        return new ResponseEntity<>(new Result<>(service.queryMeetingAgenda(meetingId), errors), HttpStatus.OK);
    }

    @GetMapping("/query-meeting-type")
    public ResponseEntity<Result<?>> queryMeetingType(@RequestHeader HttpHeaders headers,
                                                      @RequestParam("meeting_id") Long meetingId) {
        log.debug("mt开始查询/query-meeting-type=>{}", meetingId);
        return new ResponseEntity<>(new Result<>(service.queryMeetingType(meetingId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "单独更新活动议程")
    @PostMapping("/update-agenda")
    @RepeatedCheck
    public ResponseEntity<Result<?>> updateAgenda(@RequestHeader HttpHeaders headers,
                                                  @Valid @RequestBody UpdateAgendaForm form,
                                                  BindingResult bindingResult) {
        this.service.updateAgenda(form, headers);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

}
