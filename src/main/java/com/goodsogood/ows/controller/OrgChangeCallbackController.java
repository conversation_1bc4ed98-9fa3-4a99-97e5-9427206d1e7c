package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.OrgChangeForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.user.UserChangeForm;
import com.goodsogood.ows.service.*;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Auther: ruoyu
 * Date: 19-4-18
 * Description:
 */
@RestController
@RequestMapping("/")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class OrgChangeCallbackController {


    private final MeetingOrgChangeLogService meetingOrgChangeLogService;
    private final UserCommentService userCommentService;
    private final UserCommendPenalizeService userCommendPenalizeService;
    private final UserCommentStatisticsService userCommentStatisticsService;
    private final MeetingTalkService meetingTalkService;
    private final LifeService lifeService;
    private final OrgLifeService orgLifeService;
    private final CommentService commentService;
    private final Errors errors;

    @Autowired
    public OrgChangeCallbackController(MeetingOrgChangeLogService meetingOrgChangeLogService, UserCommentService userCommentService,
                                       UserCommendPenalizeService userCommendPenalizeService, UserCommentStatisticsService userCommentStatisticsService,
                                       MeetingTalkService meetingTalkService, LifeService lifeService, OrgLifeService orgLifeService, CommentService commentService, Errors errors) {
        this.meetingOrgChangeLogService = meetingOrgChangeLogService;
        this.userCommentService = userCommentService;
        this.userCommendPenalizeService = userCommendPenalizeService;
        this.userCommentStatisticsService = userCommentStatisticsService;
        this.meetingTalkService = meetingTalkService;
        this.lifeService = lifeService;
        this.orgLifeService = orgLifeService;
        this.commentService = commentService;
        this.errors = errors;
    }


    /**
     * 　组织发生变化回调接口
     *
     * @return
     */
    @PostMapping("/org_change_callback")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> orgChangeCallback(@RequestBody OrganizationBase organizationBase,
                                               @RequestHeader HttpHeaders headers) {
        log.debug("org_change_callback.HttpHeaders->{}", JsonUtils.toJson(headers));
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeaderNoCheckHeadersNotNull(headers, errors);
        log.debug("OrgChangeCallbackController -> org_change_callback -> value : {}", JsonUtils.toJson(organizationBase));

        OrgChangeForm orgChangeForm = new OrgChangeForm();
        orgChangeForm.setOrgId(organizationBase.getOrgId());
        orgChangeForm.setType(organizationBase.getType().shortValue());
        orgChangeForm.setOrgName(organizationBase.getOrgName());
        orgChangeForm.setOrgType(organizationBase.getOrgType());
        orgChangeForm.setOrgTypeChild(organizationBase.getOrgTypeChild());
        orgChangeForm.setIsRetire(organizationBase.getIsRetire().shortValue());
        orgChangeForm.setGroup(organizationBase.getHasGroup().shortValue());
        orgChangeForm.setPeriod(organizationBase.getHasPeriod().shortValue());
        orgChangeForm.setOrgLevel(organizationBase.getOrgLevel());

        meetingOrgChangeLogService.saveOrgChangeLog(sysHeader, orgChangeForm);
        // Type = 1 -> 新增， 2 -> 更新，3 -> 删除
        if (organizationBase.getType() == 2) {
            this.userCommentService.updateOrgInfo(organizationBase);
            this.userCommentStatisticsService.updateOrgInfo(organizationBase);
            this.userCommendPenalizeService.updateOrgInfo(organizationBase);
            this.meetingTalkService.updateOrgInfo(organizationBase);
            this.lifeService.updateOrgInfo(organizationBase);
            this.orgLifeService.updateOrgInfo(organizationBase);
        }

        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);

    }

    /**
     * 　人员信息发生变化回调接口
     *
     * @return
     */
    @PostMapping("/user_change_org_callback")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> userChangeCallBack(@RequestBody UserChangeForm userChangeForm,
                                                        @RequestHeader HttpHeaders headers) {
        log.debug("人员数据更新 -> [{}]",  userChangeForm);
        if (userChangeForm.getStatus() == 2) {
            this.userCommentService.updateUserInfo(userChangeForm);
            this.userCommendPenalizeService.updateUserInfo(userChangeForm);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @GetMapping("/user_change_political_type_callback")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> userChangePoliticalTypeCallback(@RequestParam(value = "uid") Long userId,
                                                                     @RequestParam(value = "political_type") Integer politicalType,
                                                                     @RequestParam(value = "time") String time,
                                                                     @RequestHeader HttpHeaders headers) {
        log.debug("更新用政治面貌: [{}] 的政治面貌:[{}]", userId, politicalType);
        this.commentService.updateUser(userId, politicalType, time, headers);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);

    }
}
