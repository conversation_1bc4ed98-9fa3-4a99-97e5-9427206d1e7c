package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SbwTaskTypeForm;
import com.goodsogood.ows.service.SbwTaskTypeService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/sbw/type")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class SbwTaskTypeController {

    private final SbwTaskTypeService sbwTaskTypeService;
    private final Errors errors;

    @Autowired
    public SbwTaskTypeController(SbwTaskTypeService sbwTaskTypeService, Errors errors) {
        this.sbwTaskTypeService = sbwTaskTypeService;
        this.errors = errors;
    }

    @HttpMonitorLogger
    @ApiOperation("舆情分类列表")
    @GetMapping("/list")
    public ResponseEntity<Result<List<SbwTaskTypeForm>>> list(){
        return new ResponseEntity<>(new Result<>(sbwTaskTypeService.list(),errors), HttpStatus.OK);
    }
}
