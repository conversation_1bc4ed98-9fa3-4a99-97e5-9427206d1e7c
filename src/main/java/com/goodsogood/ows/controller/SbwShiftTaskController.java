package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SbwShiftTaskFrom;
import com.goodsogood.ows.model.vo.SbwShiftTaskListForm;
import com.goodsogood.ows.model.vo.SbwTaskListForm;
import com.goodsogood.ows.service.SbwShiftTaskService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;

@RestController
@RequestMapping("/sbw/shift")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class SbwShiftTaskController {

    private final SbwShiftTaskService sbwShiftTaskService;
    private final Errors errors;

    @Autowired
    public SbwShiftTaskController(SbwShiftTaskService sbwShiftTaskService, Errors errors) {
        this.sbwShiftTaskService = sbwShiftTaskService;
        this.errors = errors;
    }

    @HttpMonitorLogger
    @ApiOperation("代办任务列表")
    @GetMapping("/list")
    public ResponseEntity<Result<SbwShiftTaskListForm>> list(@RequestHeader HttpHeaders headers,
                                                             @RequestParam(required = false) String title,
                                                             @RequestParam(required = false,value = "begin_time") String beginTime,
                                                             @RequestParam(required = false,value = "end_time") String endTime,
                                                             @RequestParam(required = false,defaultValue = "1") Integer page,
                                                             @RequestParam(required = false,defaultValue = "10",name = "page_size") Integer pageSize) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        SbwShiftTaskListForm form = new SbwShiftTaskListForm();
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (StringUtils.isNotBlank(beginTime)){
            form.setBeginTime(format.parse(beginTime));
        }
        if (StringUtils.isNotBlank(endTime)){
            form.setEndTime(format.parse(endTime));
        }
        form.setOrgId(header.getOid());
        form.setTitle(title);
        return new ResponseEntity(new Result<>(sbwShiftTaskService.list(form,page,pageSize),errors), HttpStatus.OK);
    }

    /**
     * 　融媒体发布转办任务 - 提交任务
     *
     * @return
     */
    @PostMapping("/submit")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> submitShiftTask(@RequestHeader HttpHeaders headers,
                                                 @RequestBody SbwShiftTaskFrom from) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(sbwShiftTaskService.submitShiftTask(header,from), errors), HttpStatus.OK);
    }

    /**
     * 　融媒体发布转办任务 - 保存草稿
     *
     * @return
     */
    @PostMapping("/save")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> saveShiftTask(@RequestHeader HttpHeaders headers,
                                                 @RequestBody SbwShiftTaskFrom from) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(sbwShiftTaskService.saveShiftTask(header,from), errors), HttpStatus.OK);
    }

    /**
     * 　融媒体发布转办任务 - 编辑任务
     *
     * @return
     */
    @PostMapping("/del")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> compileShiftTask(@RequestHeader HttpHeaders headers,
                                                      @RequestBody()SbwShiftTaskFrom from) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(sbwShiftTaskService.delShiftTask(header,from.getShiftTaskId()), errors), HttpStatus.OK);
    }

    /**
     * 　融媒体转办单详情
     *
     * @return
     */
    @GetMapping("/details")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> showTask(@RequestHeader HttpHeaders headers,
                                                 @RequestParam(value = "shift_task_id") Long shiftTaskId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(sbwShiftTaskService.showTask(header,shiftTaskId), errors), HttpStatus.OK);
    }
}
