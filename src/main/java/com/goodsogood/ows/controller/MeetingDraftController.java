package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.MeetingDraftEntity;
import com.goodsogood.ows.model.vo.MeetingDraftSaveForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.MeetingDraftService;
import com.goodsogood.ows.service.TransferService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/12/14
 */
@RestController
@RequestMapping("/meeting")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class MeetingDraftController {

    private final Errors errors;
    private final MeetingDraftService meetingDraftService;
    private final TransferService transferService;

    @Autowired
    public MeetingDraftController(Errors errors,
                                  MeetingDraftService meetingDraftService, TransferService transferService) {
        this.errors = errors;
        this.meetingDraftService = meetingDraftService;
        this.transferService = transferService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "保存草稿")
    @PostMapping("/saveDraft/{orgId}")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> saveDraft(@RequestHeader HttpHeaders headers,
                                                     @PathVariable Long orgId,
                                                     @Valid @RequestBody MeetingDraftSaveForm meetingDraftSaveForm,
                                                     BindingResult bindingResult) {
        Result<Long> result = new Result<>(meetingDraftService.saveDraft(headers, orgId, meetingDraftSaveForm), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取草稿")
    @GetMapping("/getDraft/{orgId}")
    public ResponseEntity<Result<MeetingDraftEntity>> getDraft(@RequestHeader HttpHeaders headers,
                                                               @PathVariable Long orgId,
                                                               @RequestParam Integer type,
                                                               @RequestParam(name = "meeting_id", required = false) Long meetingId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        MeetingDraftEntity meetingDraft = meetingDraftService.getDraft(sysHeader.getRegionId(), orgId, sysHeader.getUserId(), type, meetingId);
        Result<MeetingDraftEntity> result = new Result<>(meetingDraft, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "删除草稿")
    @GetMapping("/deleteDraft/{orgId}")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> deleteDraft(@RequestHeader HttpHeaders headers,
                                                       @PathVariable Long orgId,
                                                       @NotNull(message = "{NotNull.MeetingDraftSaveForm.type}")
                                                       @RequestParam Integer type,
                                                       @RequestParam(name = "meeting_id", required = false) Long meetingId) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Result<Boolean> result = new Result<>(meetingDraftService.deleteDraft(sysHeader.getRegionId(), orgId, sysHeader.getUserId(),
                type, meetingId), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}
