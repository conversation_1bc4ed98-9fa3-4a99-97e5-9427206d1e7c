package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SbwHandleForm;
import com.goodsogood.ows.model.vo.SbwTaskListForm;
import com.goodsogood.ows.model.vo.SbwTaskOrgListForm;
import com.goodsogood.ows.service.SbwVerifyTaskService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 * 南岸区网信办审核任务
 * <AUTHOR>
 * @date 2021.08.02
 */
@RestController
@RequestMapping("/sbw/verify")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class SbwVerifyTaskController {

    private final Errors errors;
    private final SbwVerifyTaskService sbwVerifyTaskService;

    @Autowired
    public SbwVerifyTaskController(Errors errors, SbwVerifyTaskService sbwVerifyTaskService) {
        this.errors = errors;
        this.sbwVerifyTaskService = sbwVerifyTaskService;
    }

    @ApiOperation("审核任务列表")
    @HttpMonitorLogger
    @GetMapping("/task")
    public ResponseEntity<Result<SbwTaskListForm>> myTaskList(@RequestParam(required = false) String title,
                                                              @RequestParam(required = false,value = "begin_time") String beginTime,
                                                              @RequestParam(required = false,value = "end_time") String endTime,
                                                              @RequestParam(required = false,defaultValue = "1") Integer page,
                                                              @RequestParam(required = false,defaultValue = "10",name = "page_size") Integer pageSize,
                                                              @RequestHeader HttpHeaders headers) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        SbwTaskListForm form = new SbwTaskListForm();
        if (StringUtils.isNotBlank(beginTime)){
            form.setBeginTime(format.parse(beginTime));
        }
        if (StringUtils.isNotBlank(endTime)){
            form.setEndTime(format.parse(endTime));
        }
        form.setTitle(title);
        form.setRegionId(header.getRegionId());
        form.setOrgId(header.getOid());
        return new ResponseEntity(new Result<>(sbwVerifyTaskService.verifyList(form,page,pageSize),errors), HttpStatus.OK);
    }

    @ApiOperation("任务下发组织列表")
    @HttpMonitorLogger
    @GetMapping("/org")
    public ResponseEntity<Result<SbwTaskOrgListForm>> myTaskList(@RequestParam(value = "task_id") Long taskId){
        return new ResponseEntity(new Result<>(sbwVerifyTaskService.orgList(taskId),errors), HttpStatus.OK);
    }

    @ApiOperation("保存审批草稿")
    @HttpMonitorLogger
    @PostMapping("/save")
    public ResponseEntity<Result<?>> save(@RequestBody SbwHandleForm form, @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        form.setUserId(header.getUserId());
        form.setUserOrgId(header.getOid());
        form.setUserOrgName(header.getOrgName());
        form.setRegionId(header.getRegionId());
        form.setFlag(2);
        form.setStatus(1);
        return new ResponseEntity(new Result<>(sbwVerifyTaskService.save(form),errors), HttpStatus.OK);
    }

    @ApiOperation("提交审核")
    @HttpMonitorLogger
    @PostMapping("/submit")
    public ResponseEntity<Result<?>> submit(@RequestBody SbwHandleForm form,@RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        form.setUserId(header.getUserId());
        form.setUserOrgId(header.getOid());
        form.setUserOrgName(header.getOrgName());
        form.setRegionId(header.getRegionId());
        form.setFlag(2);
        form.setStatus(2);
        return new ResponseEntity(new Result<>(sbwVerifyTaskService.submit(form),errors), HttpStatus.OK);
    }
}
