package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.annotation.RepeatedCheck
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.ApproveCommentInfoForm
import com.goodsogood.ows.model.vo.CommentApproveQueryForm
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.CommentApproveService
import io.swagger.annotations.ApiOperation
import lombok.extern.log4j.Log4j2
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid
import javax.validation.constraints.NotNull

@RestController
@RequestMapping("/comment-approve")
@Log4j2
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Validated
class CommentApproveController (@Autowired val errors: Errors,
                                @Autowired val commentApproveService: CommentApproveService
) {

    /**
     * 查询民主评议审核列表
     * @param  type 1-普通党员查看互评列表 2-管理员查看互评列表
     */
    @HttpMonitorLogger
    @PostMapping("/list")
    @ApiOperation("查询民主评议审核列表")
    fun selectApproveList(@Valid @RequestBody form: CommentApproveQueryForm,
                          bindingResult : BindingResult,
                          @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentApproveService.selectApproveList(form, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/approve")
    @ApiOperation("审核民主评议")
    @RepeatedCheck // 防重复提交
    fun approveComment(@Valid @RequestBody form: ApproveCommentInfoForm,
                       bindingResult : BindingResult,
                       @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentApproveService.approveComment(form, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/get")
    @ApiOperation("获取民主评议审核信息")
    fun getComment(@RequestParam("comment_id")
                   @NotNull(message = "民主评议主键不能为空") commentId: Long
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentApproveService.getApprove(commentId),
                errors),
            HttpStatus.OK
        )
    }
}