package com.goodsogood.ows.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.PracticableMapper;
import com.goodsogood.ows.model.db.PracticableEntity;
import com.goodsogood.ows.model.db.ReportEntity;
import com.goodsogood.ows.model.vo.PracticableForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.PracticableService;
import com.goodsogood.ows.service.ReportService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/practicable")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class PracticableController {
    private final Errors errors;

    private final PracticableService practicableService;

    private final PracticableMapper practicableMapper;

    @Autowired
    public PracticableController(Errors errors, PracticableService practicableService, PracticableMapper practicableMapper) {
        this.errors = errors;
        this.practicableService = practicableService;
        this.practicableMapper = practicableMapper;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "落实基层联系点制度新增")
    @PostMapping("/append")
    @RepeatedCheck
    @Validated
    public ResponseEntity<Result<Long>> addPracticable(@RequestHeader HttpHeaders headers, @Valid @RequestBody PracticableEntity practicableEntity, BindingResult bindingResult){
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        Long userId = sysHeader.getUserId();
        practicableEntity.setCreateUser(userId);
        practicableEntity.setCreateTime(LocalDateTime.now());
        Result<Long> result = new Result<>(practicableService.addPracticable(practicableEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "落实基层联系点制度修改")
    @PostMapping("/compile")
    @RepeatedCheck
    @Validated
    public ResponseEntity<Result<Long>> updatePracticable(@RequestHeader HttpHeaders headers, @Valid @RequestBody PracticableEntity practicableEntity, BindingResult bindingResult){
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        Long userId = sysHeader.getUserId();
        practicableEntity.setUpdateUser(userId);
        practicableEntity.setUpdateTime(LocalDateTime.now());
        Result<Long> result = new Result<>(practicableService.updatePracticable(practicableEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "落实基层联系点制度删除")
    @GetMapping("/erasure")
    public ResponseEntity<Result<Boolean>> deletePracticable(@RequestHeader HttpHeaders headers, @RequestParam("practicable_id") Long practicableId){
        Result<Boolean> result = new Result<>(practicableService.deletePracticable(practicableId) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @HttpMonitorLogger
    @ApiOperation(value = "落实基层联系点制度列表")
    @PostMapping("/list")
    public ResponseEntity<Result<List<PracticableEntity>>> listPracticable(
            @RequestHeader HttpHeaders headers,
            @RequestBody PracticableForm practicableForm, @RequestParam(value = "page", required = false, defaultValue = "1") Integer page, @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize) {
        return new ResponseEntity<>(new Result<>(practicableService.listPracticable(headers,practicableForm, page, pageSize), errors), HttpStatus.OK);
    }
}
