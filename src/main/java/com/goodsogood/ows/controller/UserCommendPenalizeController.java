package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.UserCommendPenalizeService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 党员奖惩控制层
 * @date 2019/12/31
 */
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/user/commend/penalize")
public class UserCommendPenalizeController {

    private final Errors errors;
    private final UserCommendPenalizeService userCommendPenalizeService;

    @Autowired
    public UserCommendPenalizeController(Errors errors, UserCommendPenalizeService userCommendPenalizeService) {
        this.errors = errors;
        this.userCommendPenalizeService = userCommendPenalizeService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "党员奖惩查询")
    @PostMapping("/query")
    public ResponseEntity<Result<?>> queryUserCommendPenalize(@Valid @RequestBody UserCommendPenalizeQueryForm queryForm,
                                                              BindingResult bindingResult,
                                                              @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("党员奖惩查询 参数:[{}]", queryForm);
        Page<UserCommendPenalizeVO> voPage = this.userCommendPenalizeService.queryUserCommendPenalizeList(queryForm, header.getRegionId());
        log.debug("查询党员奖惩列表结果 -> [{}]", voPage);
        return new ResponseEntity<>(new Result<>(voPage, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "新增党员奖惩")
    @PostMapping("/add")
    public ResponseEntity<Result<?>> insertUserCommendPenalize(@Valid @RequestBody UserCommendPenalizeAddVO addVO,
                                                               BindingResult bindingResult,
                                                               @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("新增党员奖惩 参数:[{}], 操作人:[{}]", addVO, header.getUserId());
        int i = this.userCommendPenalizeService.insertUserCommendPenalize(addVO, header.getUserId(), headers);
        log.debug("新增党员奖惩结果 -> [{}]", i);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "更新党员奖惩")
    @PostMapping("/update")
    public ResponseEntity<Result<?>> updateUserCommendPenalize(@Valid @RequestBody UserCommendPenalizeUpdateVO updateVO,
                                                               BindingResult bindingResult,
                                                               @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("更新党员奖惩 参数:[{}], 操作人:[{}]", updateVO, header.getUserId());
        int i = this.userCommendPenalizeService.updateUserCommendPenalize(updateVO, header.getUserId(), headers);
        log.debug("更新党员奖惩结果 -> [{}]", i);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "党员奖惩删除")
    @GetMapping("/del")
    public ResponseEntity<Result<?>> delUserCommendPenalize(@RequestParam("user_commend_penalize_id") Long userCommendPenalizeId,
                                                    @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("删除党员奖惩 主键:[{}]", userCommendPenalizeId);
        int i = this.userCommendPenalizeService.delUserCommendPenalize(userCommendPenalizeId, header.getUserId(), headers);
        log.debug("删除党员奖惩 -> [{}]", i);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "党员奖惩详情")
    @GetMapping("/select")
    public ResponseEntity<Result<?>> selectUserCommendPenalize(@RequestParam("user_commend_penalize_id") Long userCommendPenalizeId,
                                                            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("查询党员奖惩 主键:[{}]", userCommendPenalizeId);
        UserCommendPenalizeVO userCommendPenalizeInfo = this.userCommendPenalizeService.getUserCommendPenalizeInfo(userCommendPenalizeId);
        log.debug("查询党员奖惩 -> [{}]", userCommendPenalizeInfo);
        return new ResponseEntity<>(new Result<>(userCommendPenalizeInfo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "个人党员奖惩列表")
    @GetMapping("/select-by-user-id")
    public ResponseEntity<Result<?>> selectUserCommendPenalizeByUserId(@RequestParam("user_id") Long userId,
                                                                       @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                       @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize,
                                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("查询个人党员奖惩列表 主键:[{}]", userId);
        List<UserCommendPenalizeVO> userCommendPenalizeList = this.userCommendPenalizeService.queryUserCommendPenalizeListByUserId(userId, page, pageSize);
        log.debug("查询个人党员奖惩列表 -> [{}]", userCommendPenalizeList);
        return new ResponseEntity<>(new Result<>(userCommendPenalizeList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取下载报表字段")
    @GetMapping("/fields")
    public ResponseEntity<Result<?>> fields(@RequestParam("type") Integer type,
                                            @RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(userCommendPenalizeService.getFields(type), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "党员奖惩构建导出文件")
    @PostMapping("/export")
    public ResponseEntity<Result<?>> exportUserCommendPenalize(@Valid @RequestBody UserCommendPenalizeExportQueryForm form,
                                                               @RequestHeader HttpHeaders headers, HttpServletResponse response){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("构建党员奖惩文件 参数:[{}]", form);
        final String uuid = UUID.randomUUID().toString();
        this.userCommendPenalizeService.exportUserCommendPenalizeList(uuid, form, header.getRegionId(), headers);
        return new ResponseEntity<>(new Result<>(uuid, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "下载党员奖惩文件")
    @GetMapping("/export")
    public ResponseEntity<Result<?>> getUserCommendPenalizeFile(@RequestParam("uuid") String uuid,
                                                               @RequestHeader HttpHeaders headers, HttpServletResponse response){
        log.debug("下载党员奖惩文件 参数:[{}]", uuid);
        return new ResponseEntity<>(new Result<>(this.userCommendPenalizeService.getCommendPenalizeFile(uuid), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取我的荣誉统计信息")
    @GetMapping("/get-my-commend-statistics")
    public ResponseEntity<Result<?>> getMyCommendStatistics(@RequestParam(value = "user_id",required = false) Long userId,
                                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (Objects.isNull(userId)) {
            userId = header.getUserId();
        }
        log.debug("获取我的荣誉统计信息 参数:[{}]", userId);
        return new ResponseEntity<>(new Result<>(
                this.userCommendPenalizeService.getMyCommendStatistics(userId),
                errors), HttpStatus.OK);
    }
}
