package com.goodsogood.ows.controller;

import cn.hutool.core.convert.Convert;
import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.UserCommentStatisticsQueryForm;
import com.goodsogood.ows.model.vo.UserCommentStatisticsVO;
import com.goodsogood.ows.service.UserCommentStatisticsService;
import com.goodsogood.ows.utils.RateUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 民主评议统计
 * @date 2020/1/6
 */
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/comment/user/statistics")
public class UserCommentStatisticsController {

    private static final String REDIS_REPEAT_DOWN_KEY = "REDIS_REPEAT_DOWN_COMMENT_";

    private final Errors errors;
    private final UserCommentStatisticsService userCommentStatisticsService;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public UserCommentStatisticsController(Errors errors, UserCommentStatisticsService userCommentStatisticsService,
                                           StringRedisTemplate redisTemplate) {
        this.errors = errors;
        this.userCommentStatisticsService = userCommentStatisticsService;
        this.redisTemplate = redisTemplate;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "民主评议触发统计")
    @GetMapping("/generate")
    public ResponseEntity<Result<?>> generateCommentStatistics(@RequestParam(value = "year", required = false) Integer year,
                                                               @RequestHeader HttpHeaders headers) {
        log.debug("民主评议触发统计开始 年度:[{}]", year);
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        this.userCommentStatisticsService.generateUserCommentStatistics(sysHeader, year);
        log.debug("民主评议触发统计结束 ");
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "民主评议统计查询")
    @PostMapping("/query")
    public ResponseEntity<Result<?>> queryCommentStatisticsList(@Valid @RequestBody UserCommentStatisticsQueryForm queryForm,
                                                                    BindingResult bindingResult,
                                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        log.debug("民主评议统计查询 参数:[{}]", queryForm);
        Page<UserCommentStatisticsVO> voList = this.userCommentStatisticsService.queryUserCommentStatistics(sysHeader, queryForm);
        log.debug("查询列表结果 -> [{}]", voList);
        return new ResponseEntity<>(new Result<>(voList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "民主评议统计导出")
    @PostMapping("/export")
    public ResponseEntity<Result<?>> exportCommentStatisticsList(@ApiParam("组织ID") @NotNull(message = "{NotNull.Org.Id}") @RequestParam("org_id") Long orgId,
                                                                 @ApiParam("评议年度") @RequestParam(value = "year", required = false) Integer year,
                                                                 @ApiParam("组织名称") @RequestParam(value = "org_name", required = false) String orgName,
                                                                 @RequestHeader HttpHeaders headers, HttpServletResponse response) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Long userId = sysHeader.getUserId();
        String redisKey = REDIS_REPEAT_DOWN_KEY + userId;
        if (Boolean.TRUE.equals(this.redisTemplate.hasKey(redisKey))) {
            throw new ApiException("请不要重复提交下载", new Result<>(errors, Global.Errors.REPEAT_DATA_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "请不要重复提交下载"));
        } else {
            this.redisTemplate.opsForValue().set(redisKey, Convert.toStr(userId), 5L, TimeUnit.MINUTES);
        }
        String uuid = UUID.randomUUID().toString();
        UserCommentStatisticsQueryForm queryForm = new UserCommentStatisticsQueryForm();
        queryForm.setOrgId(orgId);
        queryForm.setOrgName(orgName);
        queryForm.setYear(year);
        log.debug("民主评议统计查询 参数:[{}]", queryForm);
        this.userCommentStatisticsService.exportUserCommentStatistics(headers, queryForm, uuid, redisKey);
        return new ResponseEntity<>(new Result<>(uuid, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "民主评议统计导出")
    @GetMapping("/export")
    public ResponseEntity<Result<?>> exportCommentStatisticsList(@ApiParam("下载进度token") @RequestParam(value = "uuid") String uuid,
                                                                 @RequestHeader HttpHeaders headers,
                                                                 HttpServletRequest request,
                                                                 HttpServletResponse response) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Long userId = sysHeader.getUserId();
        String redisKey = REDIS_REPEAT_DOWN_KEY+ userId;
        return new ResponseEntity<>(new Result<>(this.userCommentStatisticsService.downExcel(request, response, uuid, redisKey), errors), HttpStatus.OK);
    }
}
