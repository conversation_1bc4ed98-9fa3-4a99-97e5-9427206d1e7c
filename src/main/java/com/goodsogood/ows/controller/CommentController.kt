package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.annotation.RepeatedCheck
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.CommentService
import io.swagger.annotations.ApiOperation
import lombok.extern.log4j.Log4j2
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/comment")
@Log4j2
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Validated
class CommentController(@Autowired val errors: Errors,
                        @Autowired val commentService: CommentService) {

    @HttpMonitorLogger
    @GetMapping("/query-list")
    @ApiOperation("查询民主评议组织组织列表")
    fun queryCommentList(@RequestParam("year", required = false) year: Int?,
                         @RequestParam("org_id", required = false) orgId: Long?,
                         @RequestParam("status", required = false) status: Int?,
                         @RequestParam("type", required = false, defaultValue = "1") type: Int,
                         @RequestParam("include_child", required = false, defaultValue = "0") includeChild: Int,
                         @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentService.getCommentList(year, orgId, status, includeChild, type, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/query-list-by-ids")
    @ApiOperation("查询民主评议组织组织列表")
    fun queryCommentListById(@RequestParam("comment_ids", required = false) commentIds: List<Long>?,
                             @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentService.getCommentListById(commentIds, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/start")
    @ApiOperation("开启民主评议")
    @RepeatedCheck // 防重复提交
    fun startComment(@RequestParam("comment_id", required = false) commentId: Long?,
                     @RequestParam("org_id") orgId: Long,
                     @RequestParam("year") year: Int,
                     @RequestParam("excellent_num") excellentNum: Int,
                     @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>>  {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentService.startComment(commentId, orgId, year, excellentNum, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/submit")
    @ApiOperation("提交民主评议")
    @RepeatedCheck // 防重复提交
    fun submitComment(@RequestParam("comment_id") commentId: Long,
                      @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentService.submit(commentId,headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/get")
    @ApiOperation("获取民主评议")
    fun getComment(@RequestParam("comment_id", required = false) commentId: Long?,
                   @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentService.getCommentByHeader(commentId, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/is-submit")
    @ApiOperation("是否可以提交民主评议")
    @RepeatedCheck // 防重复提交
    fun isSubmitComment(@RequestParam("comment_id") commentId: Long,
                      @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentService.isSubmit(commentId),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/auto-genera-comment")
    @ApiOperation("自动生成民主评议")
    fun autoGeneraComment(@RequestParam("year") year: Int,
                          @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentService.autoGeneraComment(header.regionId, year),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/excellent/get")
    @ApiOperation("查询设置优秀党员页面数据")
    fun getExcellent(@RequestParam("comment_id", required = false) commentId: Long?,
                     @RequestParam("org_id", required = false) orgId: Long,
                     @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>>  {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentService.getExcellentData(commentId, orgId, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/excellent/set")
    @ApiOperation("设置优秀党员数据")
    @RepeatedCheck  // 防重复提交
    fun setExcellent(@RequestParam("comment_id") commentId: Long,
                     @RequestParam("excellent_num") excellentNum: Int,
                     @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>>  {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentService.setExcellentData(commentId, excellentNum, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/flush-appraisal")
    @ApiOperation("刷新自评互评表")
    fun flushCommentAppraisalFile(@RequestParam("comment_id") commentId: Long,
                                    @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentService.createAppraisal(commentId, headers),
                errors),
            HttpStatus.OK
        )
    }


    @HttpMonitorLogger
    @GetMapping("/flush-grade")
    @ApiOperation("刷新登记表")
    fun flushGradeFile(@RequestParam("comment_id") commentId: Long,
                                  @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentService.createGradeFormByComment(commentId, headers),
                errors),
            HttpStatus.OK
        )
    }


}