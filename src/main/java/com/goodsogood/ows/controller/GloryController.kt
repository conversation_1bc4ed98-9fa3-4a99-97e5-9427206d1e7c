package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.GloryService
import io.swagger.annotations.ApiOperation
import lombok.extern.log4j.Log4j2
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/glory")
@Log4j2
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Validated
class GloryController (@Autowired val errors: Errors,
                       @Autowired val gloryService: GloryService){

    @HttpMonitorLogger
    @GetMapping("/pull-data")
    @ApiOperation("推送你是我的荣耀")
    fun queryCommentListById(@RequestParam("user_id", required = false) userId: Long? = null,
                             @RequestParam("type", required = false) type: Int? = null,
                             @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                gloryService.batchAddGlory(userId, type, headers),
                errors),
            HttpStatus.OK
        )
    }
}