package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.github.pagehelper.Page;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.ResultConstant;
import com.goodsogood.ows.common.pojo.Headers;
import com.goodsogood.ows.common.pojo.PageBean;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.MeetingStatusHelper;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.*;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 纪实管理
 *
 * <AUTHOR>
 * @create 2018-10-24 15:15
 **/
@RestController
@RequestMapping("/result")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "纪实结果", tags = {"纪实结果"})
@Validated
public class MeetingReportController {

    private final Errors errors;
    private final HeaderService headerService;
    private final MeetingAnswerService meetingAnswerService;
    private final MeetingResultService meetingResultService;
    private final TransferService transferService;

    private final TopPriorityService topPriorityService;

    private final MeetingService meetingService;


    @Autowired
    public MeetingReportController(Errors errors, HeaderService headerService,
                                   MeetingAnswerService meetingAnswerService,
                                   MeetingResultService meetingResultService,
                                   TransferService transferService,
                                   TopPriorityService topPriorityService,
                                   MeetingService meetingService) {
        this.errors = errors;
        this.headerService = headerService;
        this.meetingAnswerService = meetingAnswerService;
        this.meetingResultService = meetingResultService;
        this.transferService = transferService;
        this.topPriorityService = topPriorityService;
        this.meetingService = meetingService;
    }

    /**
     * 回答任务答案
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @PostMapping(value = "/topic/answer")
    @ApiOperation(value = "填写纪实结果-任务")
    public ResponseEntity<Result<?>> answer(@RequestHeader HttpHeaders headers,
                                            @Valid @RequestBody MeetingReportForm reportForm,
                                            BindingResult bindingResult) {
        log.debug("填写纪实结果-任务：入参 = {}", reportForm);
        Headers header = this.headerService.bulidHeader(headers);
        this.meetingAnswerService.answer(reportForm, header, ResultConstant.HAS_MEETING);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 提交任务
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @PostMapping(value = "/topic/submit")
    @ApiOperation(value = "提交任务")
    public ResponseEntity<Result<?>> topicSubmit(@RequestHeader HttpHeaders headers,
                                                 @Valid @RequestBody MeetingReportTopicContentForm reportForm,
                                                 BindingResult bindingResult) {
        log.debug("提交任务：入参 = {}", reportForm);
        Headers header = this.headerService.bulidHeader(headers);
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        if (reportForm.getOrgId() == null) {
            reportForm.setOrgId(sysHeader.getOid());
        }
        this.meetingAnswerService.topicSubmit(sysHeader, header, reportForm);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 提交任务前权限、任务状态校验
     */
    @HttpMonitorLogger
    @GetMapping(value = "/topic/submit/precondition/check")
    @ApiOperation(value = "提交任务")
    public ResponseEntity<Result<?>> topicSubmitPreconditionCheck(@RequestHeader HttpHeaders headers,
                                                                  @RequestParam(value = "topic_org_id") Long topicOrgId,
                                                                  @RequestParam(value = "org_id") Long orgId) {
        log.debug("提交任务前权限、任务状态校验：topic_org_id = {},org_id={}", topicOrgId, orgId);
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        TopicSubmitPreconditionStatus.STATUS status = this.meetingAnswerService.topicSubmitPreconditionCheck(sysHeader, topicOrgId, orgId);
        return new ResponseEntity<>(new Result<>(new TopicSubmitPreconditionStatus(status), errors), HttpStatus.OK);
    }

    /**
     * 回答任务答案详情
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @GetMapping(value = "/topic/detail")
    @ApiOperation(value = "查看任务回答详情")
    public ResponseEntity<Result<?>> answerDetail(@RequestParam(value = "mt_id", required = false) Long meetingTopicId,
                                                  @RequestParam(value = "topic_id") Long topicId,
                                                  @RequestParam(value = "topic_org_id", required = false) Long topicOrgId) {
        return new ResponseEntity<>(
                new Result<>(this.meetingAnswerService.answerDetail(meetingTopicId, topicId, topicOrgId), errors),
                HttpStatus.OK);
    }

    /**
     * 有活动流程的提交
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @PostMapping(value = "/submit")
    @ApiOperation(value = "填写纪实考核表-有活动流程")
    @RepeatedCheck
    public ResponseEntity<Result<?>> submit(@RequestHeader HttpHeaders headers,
                                            @Valid @RequestBody MeetingResultForm result,
                                            BindingResult bindingResult) {
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        log.debug("考核纪实填写-有活动流程入参：{}", result);
        //验证是否有结束时间(已经在参数字段里验证非空，但是怕改需求先注释)
//        if(result.getMeeting().getEndTime()==null){
//            throw new ApiException("结束时间未设置", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "结束时间未设置"));
//        }

        //判断是否是红岩魂传入的,如果是红岩魂传入的，需要设置user_id org_id,还需要将记录人等手机号转为烟草的user_id
        if (transferService.fromSystemSzf(headers)) {
            transferService.transferHeaders(headers);
            transferService.transferPhoneToUserId(headers, result.getMeeting());
            result.getMeeting().setRegionId(19L);
        }
        log.debug("数据转换后:" + result);
        //验证是否包含现场照片
//        if (result.getMeeting().getResultFiles() != null && result.getMeeting().getResultFiles().stream().noneMatch(o -> o.getType() == 0)) {
//            throw new ApiException("现场照片未上传！", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "现场照片未上传！"));
//        }
        this.meetingResultService.submit(headers, result);
        var meetingEntity = meetingService.findById(sysHeader, result.getMeeting().getMeetingId());
        meetingService.setTopPriorityService(meetingEntity, headers);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 更新纪实报表 - 下级
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @PostMapping(value = {"/update"})
    @ApiOperation(value = "更新纪实考核表 -下级")
    @RepeatedCheck
    public ResponseEntity<Result<?>> update(@RequestHeader HttpHeaders headers,
                                            @Valid @RequestBody MeetingResultForm result,
                                            BindingResult bindingResult) {
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        //判断是否是红岩魂传入的,如果是红岩魂传入的，需要设置user_id org_id,还需要将记录人等手机号转为烟草的user_id
        if (transferService.fromSystemSzf(headers)) {
            transferService.transferHeaders(headers);
            transferService.transferPhoneToUserId(headers, result.getMeeting());
            result.getMeeting().setRegionId(19L);
        }
        log.debug("更新考核纪实入参：{}", result);
        //验证是否包含现场照片
//        if (result.getMeeting().getResultFiles() != null && result.getMeeting().getResultFiles().stream().noneMatch(o -> o.getType() == 0)) {
//            throw new ApiException("现场照片未上传！", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "现场照片未上传！"));
//        }
        this.meetingResultService.update(headers, result);
        var meeting = meetingService.findById(sysHeader, result.getMeeting().getMeetingId());
        meetingService.setTopPriorityService(meeting, headers);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 更新纪实报表 - 上级
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @PostMapping(value = {"/update/leader"})
    @ApiOperation(value = "更新纪实考核表 -上级")
    @RepeatedCheck
    public ResponseEntity<Result<?>> updateLeader(@RequestHeader HttpHeaders headers,
                                                  @Valid @RequestBody MeetingResultForm result,
                                                  BindingResult bindingResult) {
        //判断是否是红岩魂传入的,如果是红岩魂传入的，需要设置user_id org_id,还需要将记录人等手机号转为烟草的user_id
        if (transferService.fromSystemSzf(headers)) {
            transferService.transferHeaders(headers);
            transferService.transferPhoneToUserId(headers, result.getMeeting());
        }
        log.debug("更新考核纪实入参：{}", result);
        this.meetingResultService.updateLeader(headers, result);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 无活动的纪实情况新增 - 直接填写
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @PostMapping(value = "/add")
    @ApiOperation(value = "填写纪实考核表-直接填写")
    @RepeatedCheck
    public ResponseEntity<Result<?>> add(@RequestHeader HttpHeaders headers,
                                         @Valid @RequestBody MeetingAndResultForm resultForm,
                                         BindingResult bindingResult) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        boolean szf = false;
        //判断是否是红岩魂传入的,如果是红岩魂传入的，需要设置user_id org_id,还需要将记录人等手机号转为烟草的user_id
        if (transferService.fromSystemSzf(headers)) {
            transferService.transferHeaders(headers);
            transferService.transferPhoneToUserId(headers, resultForm.getMeeting());
            szf = true;
            resultForm.getMeeting().setRegionId(19L);
        }
        log.debug("数据转换后:" + resultForm);
        resultForm.getMeeting().setRecordType(1);
        log.debug("考核纪实填写-有活动流程入参：{}", resultForm);
        log.debug("活动录入参数：{}", JsonUtils.toJson(resultForm));
        //验证是否有结束时间(已经在参数字段里验证非空，但是怕改需求先注释)
//        if(resultForm.getMeeting().getEndTime()==null){
//            throw new ApiException("结束时间未设置", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "结束时间未设置"));
//        }
        //验证是否包含现场照片
//        if (resultForm.getMeeting().getResultFiles() != null && resultForm.getMeeting().getResultFiles().stream().noneMatch(o -> o.getType() == 0)) {
//            throw new ApiException("现场照片未上传！", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "现场照片未上传！"));
//        }
        Headers header = this.headerService.bulidHeader(headers);
//        int code = this.meetingResultService.add(headers, header, resultForm);
        Long meetingId = this.meetingResultService.add(headers, header, resultForm, szf);
        log.debug("生成的meetingid为：" + meetingId);
        var meeting = meetingService.findById(sysHeader, meetingId);
        meetingService.setTopPriorityService(meeting, headers);
//        if (code != 0) {
//            throw new ApiException("填写纪实考核表-直接填写出错", new Result<>(errors, Math.abs(code), HttpStatus.OK.value()));
//        }
        return new ResponseEntity<>(new Result<>(meetingId, errors), HttpStatus.OK);
    }


    /**
     * 纪实结果的详情
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @GetMapping(value = "/detail/{id}")
    @ApiOperation(value = "查看纪实结果详情")
    public ResponseEntity<Result<?>> detail(@RequestHeader HttpHeaders headers,
                                            @PathVariable(value = "id", required = false) Long meetingId,
                                            @RequestParam(value = "is_edit", required = false, defaultValue = "0")
                                            @Range(min = 0, max = 1, message = "{Range.meeting.isEdit}")
                                            Short isEdit) {
        // 默认非编辑查询详情 is_edit=1时，为标记查询详情
        isEdit = isEdit == 0 ? MeetingService.IS_NOT_EDIT : MeetingService.IS_REPORT_EDIT;
        return new ResponseEntity<>(
                new Result<>(this.meetingResultService.detail(headers, meetingId, isEdit), errors),
                HttpStatus.OK);
    }

    // 2018-11-7-07 10:42 zhangchuanhao(修改)
    @HttpMonitorLogger
    @GetMapping(value = "/check/list")
    @ApiOperation(value = "纪实结果检查分页（全部/追踪）")
    public ResponseEntity<Result<Page<MeetingResultListForm>>> page(@RequestHeader HttpHeaders headers,
                                                                    @ApiParam(value = "查询类型，1：纪实结果检查（全部） 2：纪实结果追踪，默认为纪实结果检查") @RequestParam(name = "oper", required = false, defaultValue = "1") int oper,
                                                                    @ApiParam(value = "组织名称") @RequestParam(name = "org_name", required = false) String orgName,
                                                                    @ApiParam(value = "所属类别") @RequestParam(name = "meeting_class", required = false) Integer meetingClass,
                                                                    @ApiParam(value = "活动类型") @RequestParam(name = "meeting_types", required = false) String meetingTypes,
                                                                    @ApiParam(value = "活动时间（开始）") @RequestParam(name = "meeting_start_time", required = false) String meetingStartTime,
                                                                    @ApiParam(value = "活动时间（结束）") @RequestParam(name = "meeting_end_time", required = false) String meetingEndTime,
                                                                    @ApiParam(value = "提交时间（开始）") @RequestParam(name = "submit_start_time", required = false) String submitStartTime,
                                                                    @ApiParam(value = "提交时间（结束）") @RequestParam(name = "submit_end_time", required = false) String submitEndTime,
                                                                    @ApiParam(value = "页码") @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                                    @ApiParam(value = "页面容量") @RequestParam(value = "page_size", required = false) Integer pageSize) {
        Headers header = this.headerService.bulidHeader(headers);
        //处理分页信息
        PageBean pageBean = PageUtils.page(pageNo, pageSize);
        Page<MeetingResultListForm> page = this.meetingResultService.page(
                header,
                oper,
                orgName,
                meetingClass,
                meetingTypes,
                meetingStartTime,
                meetingEndTime,
                submitStartTime,
                submitEndTime,
                pageBean
        );
        page.getResult().forEach(temp -> {
            temp.setStatus(MeetingStatusHelper.convertToFormStatus(temp.getStatus(), null));
        });
        return new ResponseEntity<>(new Result<>(page, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping(value = "/check")
    @ApiOperation(value = "纪实结果检查通过 、退回")
    public ResponseEntity<Result<Boolean>> check(@RequestHeader HttpHeaders headers,
                                                 @Valid @RequestBody MeetingResultCheckForm meetingResultCheckForm,
                                                 BindingResult bindingResult) {
        return new ResponseEntity<>(new Result<>(this.meetingResultService.check(headers, meetingResultCheckForm), errors), HttpStatus.OK);
    }
}
