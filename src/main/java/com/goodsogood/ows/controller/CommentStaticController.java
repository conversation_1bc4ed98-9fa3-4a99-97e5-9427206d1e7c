package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingCommentStatisticsMapper;
import com.goodsogood.ows.model.db.LifeFileEntity;
import com.goodsogood.ows.model.vo.CommentMemberVO;
import com.goodsogood.ows.model.vo.MeetingFileListVo;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.StaticsForm;
import com.goodsogood.ows.service.CommentFileService;
import com.goodsogood.ows.service.CommentStaticService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

@Controller
@RequestMapping("/comment/statics")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
public class CommentStaticController {
    private final CommentStaticService commentStaticService;
    private final CommentFileService commentFileService;
    private final Errors errors;
    @Autowired
    public CommentStaticController(CommentStaticService commentStaticService,
                                   CommentFileService commentFileService,
                                   Errors errors){
        this.commentStaticService = commentStaticService;
        this.commentFileService = commentFileService;
        this.errors = errors;
    }
    @ApiOperation("查询统计信息")
    @HttpMonitorLogger
    @GetMapping("/query")
    public ResponseEntity<Result<Page<StaticsForm>>> queryStatics(@RequestParam(value="year",required = false) Integer year,
                                                                  @RequestParam(value="org_id",required = false)  Long orgId,
                                                                  @RequestParam(value="page",required = false,defaultValue = "1") Integer page,
                                                                  @RequestParam(value="page_size",required = false,defaultValue = "10")Integer pageSize,
                                                                  @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if(Objects.isNull(orgId)){//如果机构为空，则查自身及下级-安素兰
            orgId = header.getOid();
        }
        Page<StaticsForm> list = commentStaticService.queryStatistics(year,orgId,page,pageSize);
        Result<Page<StaticsForm>> result = new Result<>(list, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation("查询报告用于下载-根据选择的民主评议id查询")
    @HttpMonitorLogger
    @GetMapping("/query_template")
    public ResponseEntity<Result<MeetingFileListVo>> queryCommentFile(@RequestParam("comment_ids") List<Long> commentIds,
                                              @RequestParam(value="type") List<Integer> type,
                                              @RequestHeader HttpHeaders headers){
        return new ResponseEntity<>(new Result<MeetingFileListVo>(commentFileService.queryCommentFile(commentIds,type),errors),HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("下载统计报表-根据查询条件下载")
    @HttpMonitorLogger
    @GetMapping("/down_statics")
    public ResponseEntity<Result<Boolean>> downStaticsReport(@RequestParam(value="year",required = false) Integer year,
                                                             @RequestParam(value="org_id",required = false)  Long orgId,
                                                             @RequestParam("codes") List<Integer> codes,
                                                             @RequestHeader HttpHeaders headers,HttpServletResponse response){
        final HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if(orgId == null) {
            orgId = header.getOid();
        }
        return new   ResponseEntity<>(new Result<Boolean>(commentStaticService.downStaticsReport(year,orgId,codes,response),errors),HttpStatus.OK);
    }

    @ApiOperation("查询自评报告用于下载-根据选择的民主评议id查询--后需求变更，该接口还可以导出测评表")
    @HttpMonitorLogger
    @PostMapping("/query_selftemplate")
    public ResponseEntity<Result<MeetingFileListVo>> querySelfCommentFile(@RequestBody CommentMemberVO commentMemberVO,
                                                                          @RequestParam(value="type") List<Integer> type,
                                                                      @RequestHeader HttpHeaders headers){
        return new ResponseEntity<>(new Result<MeetingFileListVo>(commentFileService.querySelfCommentFile(commentMemberVO,type,headers),errors),HttpStatus.OK);

    }

}
