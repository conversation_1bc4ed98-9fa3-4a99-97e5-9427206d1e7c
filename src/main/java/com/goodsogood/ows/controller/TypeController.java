package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.StringCanstant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.CategoryEntity;
import com.goodsogood.ows.model.db.TypeEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.CategoryService;
import com.goodsogood.ows.service.TypeService;
import com.goodsogood.ows.utils.PageUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Description: 活动类型controller</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 9:55
 */
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/type")
public class TypeController {

    private static final List<Integer> TYPE_CODE = Arrays.asList(-1, 1, 2, 3);

    private final Errors errors;
    private final TypeService typeService;
    private final CategoryService categoryService;

    public TypeController(Errors errors, TypeService typeService, CategoryService categoryService) {
        this.errors = errors;
        this.typeService = typeService;
        this.categoryService = categoryService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "添加活动类型")
    @PostMapping("/add")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> addType(@RequestHeader HttpHeaders headers,
                                                @Valid @RequestBody TypeAddForm typeAddFrom,
                                                BindingResult bindingResult) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         *  3.已经存在的类型 不能添加
         *  4.所选类别是否存在
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        typeAddFrom.setType(typeAddFrom.getType().trim());//去掉字符串两端的多余的空格
        this.checkIsExist(sysHeader.getRegionId(), typeAddFrom.getType());
        if(typeAddFrom.getTypeSys()==null){
            typeAddFrom.setTypeSys((short) 1);
        }
        TypeEntity typeEntity = typeAddFrom.toEntity();
        this.checkCategoryIsExist(typeEntity);
        Result<Long> result = new Result<>(typeService.addType(headers, sysHeader, typeEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "修改活动类型")
    @PostMapping("/update")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> updateType(@RequestHeader HttpHeaders headers,
                                                      @Valid @RequestBody TypeUpdateForm typeUpdateFrom,
                                                      BindingResult bindingResult) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         *  3.已经在使用的类型不能修改（1）类型组合中使用；（2）组织生活中使用
         *  4.不能修改为已经存在的类别
         *  4.所选类别是否存在
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        typeUpdateFrom.setType(typeUpdateFrom.getType().trim());//去掉字符串两端的多余的空格
        // 判断已经在使用
        this.checkIsUse(typeUpdateFrom.getTypeId(), "修改！");

        // 不能修改为已经存在的类型
        TypeEntity te = typeService.listOneByName(sysHeader.getRegionId(), typeUpdateFrom.getType());
        if (te != null && !te.getTypeId().equals(typeUpdateFrom.getTypeId())) {
            throw new ApiException("type exist", new Result<>(errors, 1801, HttpStatus.BAD_REQUEST.value(), typeUpdateFrom.getType()));
        }
        TypeEntity typeEntity = typeUpdateFrom.toEntity();
        // 判断所选类别是否存在
        this.checkCategoryIsExist(typeEntity);

        Result<Boolean> result = new Result<>(typeService.updateType(sysHeader, typeEntity) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "删除活动类型")
    @DeleteMapping("/del/{id}")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> delType(@RequestHeader HttpHeaders headers,
                                                   @PathVariable long id) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         *  3.已经在使用的类型不能删除。（1）类型组合中使用；（2）组织生活中使用
         */
        ControllerHelper.getSysHeader(headers, errors);
        this.checkIsUse(id, "删除！");
        Result<Boolean> result = new Result<>(typeService.delType(id) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动类型列表（分页查询）")
    @GetMapping("/list")
    public ResponseEntity<Result<Page<TypeEntity>>> listType(@RequestHeader HttpHeaders headers,
                                                             @RequestParam(value = "type_id", required = false) Long id,
                                                             @RequestParam(value = "category_id", required = false) Long categoryId,
                                                             @RequestParam(value = "type", required = false) @Length(max = 50, message = "{Length.type.type}") String type,
                                                             @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                             @RequestParam(value = "page_size", required = false) Integer pageSize) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        TypeListForm typeListFrom = new TypeListForm(sysHeader.getRegionId());
        typeListFrom.setTypeId(id);
        typeListFrom.setCategoryId(categoryId);
        typeListFrom.setType(type);
        typeListFrom.setPageBean(PageUtils.page(pageNo, pageSize));
        Result<Page<TypeEntity>> result = new Result<>(typeService.listPageType(typeListFrom), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动类型列表")
    @GetMapping("/list-all")
    public ResponseEntity<Result<List<TypeAllResultForm>>> listAllType(@RequestHeader HttpHeaders headers,
                                                                       @RequestParam(value = "category_id", required = false) Long categoryId,
                                                                       @RequestParam(value = "type_id", required = false) Long typeId,
                                                                       @RequestParam(value = "type", required = false) @Length(max = 50, message = "{Length.type}") String type) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        TypeListForm typeListFrom = new TypeListForm(sysHeader.getRegionId());
        typeListFrom.setCategoryId(categoryId);
        typeListFrom.setTypeId(typeId);
        // 2018年11月6日 10:08:05
        if (StringUtils.isNotBlank(type)) {
            typeListFrom.setType(type.trim());
        }
        List<TypeEntity> typeEntities = typeService.listAllType(typeListFrom);
        List<Long> categoryIds = typeEntities.stream().map(TypeEntity::getCategoryId).distinct().sorted().collect(Collectors.toList());
        List<TypeAllResultForm> typeAllResultFroms = new ArrayList<>();
        categoryIds.forEach(cId -> {
            TypeAllResultForm typeAllResultFrom = new TypeAllResultForm();
            typeAllResultFrom.setTypes(typeEntities.stream().filter(typeEntity -> typeEntity.getCategoryId().equals(cId)).collect(Collectors.toList()));
            typeAllResultFrom.setCategoryId(cId);
            typeAllResultFrom.setCategory(typeAllResultFrom.getTypes().get(0).getCategory());
            typeAllResultFroms.add(typeAllResultFrom);
        });

        //查询类别
        List<CategoryEntity> categoryEntities = categoryService.listAllCategory(null);
        if (categoryId != null && typeEntities.isEmpty()) {
            CategoryEntity categoryEntity = categoryEntities.stream().filter(ce -> ce.getCategoryId().equals(categoryId)).findFirst().orElse(null);
            if (categoryEntity != null) {
                TypeAllResultForm typeAllResultFrom = new TypeAllResultForm();
                typeAllResultFrom.setCategoryId(categoryEntity.getCategoryId());
                typeAllResultFrom.setCategory(categoryEntity.getCategory());
                typeAllResultFroms.add(typeAllResultFrom);
            }
        }
        if (StringUtils.isBlank(type) && categoryId == null && typeId == null) {
            if (categoryEntities.size() != typeAllResultFroms.size()) {
                categoryEntities.forEach(categoryEntity -> {
                    if (typeAllResultFroms.stream().noneMatch(typeAllResultFrom -> typeAllResultFrom.getCategoryId().equals(categoryEntity.getCategoryId()))) {
                        TypeAllResultForm typeAllResultFrom = new TypeAllResultForm();
                        typeAllResultFrom.setCategoryId(categoryEntity.getCategoryId());
                        typeAllResultFrom.setCategory(categoryEntity.getCategory());
                        typeAllResultFroms.add(typeAllResultFrom);
                    }
                });
            }
        }
        Result<List<TypeAllResultForm>> result = new Result<>(typeAllResultFroms, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "发起活动和任务完成情况下拉选择查询类别")
    @GetMapping("/list-all/{tag}")
    public ResponseEntity<Result<List<TypeEntity>>> listAll(@RequestHeader HttpHeaders headers,
                                                            @ApiParam("1:发起活动下拉框；2:任务完成情况下拉框")
                                                            @Range(min = 1, max = 2, message = "{Range.select.tag}") @PathVariable short tag) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<List<TypeEntity>> result = new Result<>(typeService.listAllType(sysHeader.getOid(), tag), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "活动类型配置-选择人员规则修改")
    @GetMapping("/update-user-rule")
    public ResponseEntity<Result<?>> updateUserRule(
            @RequestHeader HttpHeaders headers,
            @RequestParam(value = "type_id") Long typeId,
            @RequestParam(value = "code") Integer code,
            @Range(min = 0, max = 1, message = "否需要填写讲课人:0 否；1 是")
            @RequestParam(value = "has_lecturer", defaultValue = "0", required = false)
                    Integer hasLecturer,
            @Range(min = 0, max = 1, message = "是否需要填写讲课标题:0 否；1 是")
            @RequestParam(value = "has_lecture_title", defaultValue = "0", required = false)
                    Integer hasLectureTitle) {
        if (!TYPE_CODE.contains(code)) {
            throw new ApiException("类型选择错误", new Result<>(errors, 1815, HttpStatus.BAD_REQUEST.value()));
        }
        typeService.updateUserRule(typeId, code, hasLecturer, hasLectureTitle);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 校验类型是否已存在
     */
    private void checkIsExist(long regionId, String type) {
        if (typeService.isExist(regionId, type)) {
            throw new ApiException("type exist", new Result<>(errors, 1801, HttpStatus.BAD_REQUEST.value(), type));
        }
    }

    /**
     * 校验所选类别是否存在,存在，冗余category
     */
    private void checkCategoryIsExist(TypeEntity typeEntity) {
        CategoryEntity categoryEntity = categoryService.findById(typeEntity.getCategoryId());
        if (categoryEntity == null) {
            throw new ApiException("not found", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), "类别"));
        }
        typeEntity.setCategory(categoryEntity.getCategory());
    }

    /**
     * 校验类型是否被使用
     *
     * @param id 类型id
     */
    private void checkIsUse(long id, String operate) {
        // 是否被使用
        if (typeService.isUse(id)) {
            throw new ApiException("is using", new Result<>(errors, 1802, HttpStatus.BAD_REQUEST.value(), StringCanstant.ACTIVITY + "类型", operate));
        }
    }

}
