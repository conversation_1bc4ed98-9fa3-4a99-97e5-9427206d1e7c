package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.StringCanstant;
import com.goodsogood.ows.common.pojo.PageBean;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.MeetingPlanEntity;
import com.goodsogood.ows.model.db.TypeEntity;
import com.goodsogood.ows.model.vo.MeetingExecuteForm;
import com.goodsogood.ows.model.vo.MeetingPlanAddForm;
import com.goodsogood.ows.model.vo.MeetingRequireAddForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.MeetingPlanService;
import com.goodsogood.ows.service.TypeService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.PageUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-23 13:55
 **/
@RestController
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
@RequestMapping("/plan")
public class MeetingPlanController {

    private final Errors errors;
    private final MeetingPlanService service;
    private final TypeService typeService;

    @Autowired
    public MeetingPlanController(Errors errors, MeetingPlanService service, TypeService typeService) {
        this.errors = errors;
        this.service = service;
        this.typeService = typeService;
    }


    @HttpMonitorLogger
    @ApiOperation(value = "添加组织生活")
    @PostMapping("/add")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> addMeetingPlan(@RequestHeader HttpHeaders headers,
                                                       @Valid @RequestBody MeetingPlanAddForm meetingPlanAddForm,
                                                       BindingResult bindingResult) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         *  3.校验参数
         */
        log.info("plan/add-添加组织生活入参：{}", JsonUtils.toJson(meetingPlanAddForm));
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        this.checkAddParam(sysHeader, meetingPlanAddForm);
        Result<Long> result = new Result<>(service.addMeetingPlan(sysHeader, meetingPlanAddForm), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);

    }

    @HttpMonitorLogger
    @ApiOperation(value = "修改组织生活启用状态")
    @PostMapping("/execute")
    public ResponseEntity<Result<Boolean>> execute(@RequestHeader HttpHeaders headers,
                                                   @Valid @RequestBody MeetingExecuteForm meetingExecuteForm,
                                                   BindingResult bindingResult) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        log.info("plan/execute-修改组织生活启用状态入参：{}", JsonUtils.toJson(meetingExecuteForm));
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingPlanEntity meetingPlanEntity = this.getMeetingPlanEntity(meetingExecuteForm.getMeetingPlanId());
        // 数据库中的状态和修改状态一致，直接返回true
        if (meetingExecuteForm.getIsExecute().equals(meetingPlanEntity.getIsExecute())) {
            new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
        }
        meetingPlanEntity.setIsExecute(meetingExecuteForm.getIsExecute());
        Result<Boolean> result = new Result<>(service.executeMeetingPlan(sysHeader, meetingPlanEntity) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @HttpMonitorLogger
    @ApiOperation(value = "删除组织生活")
    @DeleteMapping("/del/{id}")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> delMeetingPlan(@RequestHeader HttpHeaders headers,
                                                          @PathVariable long id) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        log.info("plan/del-删除组织生活：{}", id);
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingPlanEntity meetingPlanEntity = this.getMeetingPlanEntity(id);
        Result<Boolean> result = new Result<>(service.delMeetingPlan(sysHeader, meetingPlanEntity) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "查询组织生活详情")
    @GetMapping("/detail/{id}")
    public ResponseEntity<Result<MeetingPlanEntity>> detail(@RequestHeader HttpHeaders headers,
                                                            @PathVariable long id) {
        MeetingPlanEntity meetingPlanEntity = this.getMeetingPlanEntity(id);
        Result<MeetingPlanEntity> result = new Result<>(meetingPlanEntity, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "查询组织生活,all")
    // chenanshun 2018年11月6日 15:44:46
    @GetMapping("/list-all")
    public ResponseEntity<Result<List<MeetingPlanEntity>>> listAllMeetingPlan(@RequestHeader HttpHeaders headers,
                                                                              @RequestParam(value = "name", required = false)
                                                                              @Length(max = 50, message = "{Length.meetingPlan.name}") String name) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<List<MeetingPlanEntity>> result = new Result<>(service.listAllMeetingPlan(name, sysHeader.getOid()), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "查询组织生活,page")
    @GetMapping("/list")
    public ResponseEntity<Result<Page<MeetingPlanEntity>>> listPageMeetingPlan(@RequestHeader HttpHeaders headers,
                                                                               @RequestParam(value = "name", required = false)
                                                                               @Length(max = 50, message = "{Length.meetingPlan.name}") String name,
                                                                               @ApiParam("页码，默认为1")
                                                                               @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                                               @ApiParam("每页行数")
                                                                               @RequestParam(value = "page_size", required = false) Integer pageSize) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        PageBean pageBean = PageUtils.page(pageNo, pageSize);
        Result<Page<MeetingPlanEntity>> result = new Result<>(service.listPageMeetingPlan(name, sysHeader.getOid(), pageBean), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "发起活动和任务完成情况下拉选择查询组织生活")
    @GetMapping("/list-all/{tag}")
    public ResponseEntity<Result<List<MeetingPlanEntity>>> listAllMeetingPlan(@RequestHeader HttpHeaders headers,
                                                                              @ApiParam("1:发起活动下拉框；2:任务完成情况下拉框")
                                                                              @Range(min = 1, max = 2, message = "{Range.select.tag}") @PathVariable Short tag) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<List<MeetingPlanEntity>> result = new Result<>(service.listAllMeetingPlan(sysHeader.getOid(), tag), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 分页　纪实详情执行组织查询
     *
     * @param meetingId 　主键id
     * @param page      页数
     * @return
     */
    @PostMapping(value = {"/execute-org/{meetingId}/{page}"})
    public ResponseEntity<Result<?>> executeOrg(@RequestHeader HttpHeaders headers, @PathVariable Long meetingId,
                                                @PathVariable Integer page,
                                                @RequestBody(required = false) HashMap<String,String > map) {


        return new ResponseEntity<>(new Result<>(service.findExecuteOrg(meetingId, page, MapUtils.getString(map,"org_name")), errors), HttpStatus.OK);

    }

    /**
     * 参数校验
     *
     * @param meetingPlanAddForm 参数
     */
    private void checkAddParam(HeaderHelper.SysHeader sysHeader, MeetingPlanAddForm meetingPlanAddForm) {

        /*
         *  1.自定义间段(默认值 start_time 和end_time 不能为null,开始时间在结束时间之前，且结束时间在当前时间之后
         *  2自然月周期; startYear 和 startMonth 不能为null
         *  3.季度周期;  startQuarter 不能为null
         *  4.年度周期 startYear  不能为null
         *  5.所选活动类型是否存在
         *  6.派发的组织不能包含当前组织
         *  7.未执行扣分范围校验
         */
        switch (meetingPlanAddForm.getExecuteType()) {
            case 1:
                if (meetingPlanAddForm.getStartTime() == null || meetingPlanAddForm.getEndTime() == null) {
                    throw new ApiException("开始时间或结束时间未设置", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "开始时间或结束时间未设置"));
                }
                if (DateUtils.ltToday(meetingPlanAddForm.getStartTime())) {
                    throw new ApiException("开始时间在当前时间之前 ", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "开始时间在当前时间之前"));
                }
                if (meetingPlanAddForm.getStartTime().getTime() > meetingPlanAddForm.getEndTime().getTime()) {
                    throw new ApiException("结束时间在开始时间之前 ", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "结束时间在开始时间之前"));
                }
                break;
            case 2:
                checkStartYear(meetingPlanAddForm);
                checkStartMonth(meetingPlanAddForm);
                break;
            case 3:
                checkStartYear(meetingPlanAddForm);
                if (meetingPlanAddForm.getStartQuarter() == null) {
                    throw new ApiException("开始季度未设置 ", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "开始季度未设置"));
                }
                if (meetingPlanAddForm.getStartYear() == DateUtils.getYear() && meetingPlanAddForm.getStartQuarter() < DateUtils.getQuarter()) {
                    throw new ApiException("季度小于当前季度", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "季度小于当前季度"));
                }
                break;
            case 4:
                checkStartYear(meetingPlanAddForm);
                break;
        }
        //手动发放时需要执行组织不能为空 2019-04-18
        final short sendType = meetingPlanAddForm.getSendType();
        if (sendType == 2) {
            if (meetingPlanAddForm.getExecuteOrgs() == null || meetingPlanAddForm.getExecuteOrgs().isEmpty())
                throw new ApiException("执行组织不能为空 ", new Result<>(errors, 1812, HttpStatus.BAD_REQUEST.value()));
            if (meetingPlanAddForm.getExecuteOrgs().stream().anyMatch(oid -> oid.getOrgId().equals(sysHeader.getOid())))
                throw new ApiException("执行组织不能包含自己所在组织 ", new Result<>(errors, 1809, HttpStatus.BAD_REQUEST.value()));
        }
        if (sendType == 1 && meetingPlanAddForm.getAutoSendTypeForm() == null)
            throw new ApiException("自动发放活动类型值不能为空 ", new Result<>(errors, 1814, HttpStatus.BAD_REQUEST.value()));
        List<Long> typeIds = meetingPlanAddForm.getMeetingTypes().stream().map(MeetingRequireAddForm::getTypeId).distinct().collect(Collectors.toList());
        List<TypeEntity> typeEntities = typeService.listAllByIds(typeIds);
        if (typeEntities.isEmpty() || typeEntities.size() != typeIds.size()) {
            throw new ApiException("活动类型未找到", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), StringCanstant.ACTIVITY + "类型"));
        }
        meetingPlanAddForm.getMeetingTypes().forEach(meetingRequireAddFrom -> {
            TypeEntity typeEntity = typeEntities.stream().filter(te -> te.getTypeId().equals(meetingRequireAddFrom.getTypeId())).findFirst().orElse(null);
            if (typeEntity != null) {
                meetingRequireAddFrom.setType(typeEntity.getType());
                meetingRequireAddFrom.setCategory(typeEntity.getCategory());
                meetingRequireAddFrom.setCategoryId(typeEntity.getCategoryId());
            }
        });

    }

    /**
     * 校验年份
     * !null && 小于当前年
     */
    private void checkStartYear(MeetingPlanAddForm meetingPlanAddForm) {
        if (meetingPlanAddForm.getStartYear() == null) {
            throw new ApiException("开始年份未设置", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "开始年份未设置"));
        }
        if (meetingPlanAddForm.getStartYear() < DateUtils.getYear()) {
            throw new ApiException("年份小于当前年份", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "年份小于当前年份"));
        }
    }

    /**
     * 校验月份份
     * !null && 小于当月份
     */
    private void checkStartMonth(MeetingPlanAddForm meetingPlanAddForm) {
        if (meetingPlanAddForm.getStartMonth() == null) {
            throw new ApiException("开始月份未设置", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "开始月份未设置"));
        }
        if (meetingPlanAddForm.getStartYear() == DateUtils.getYear() && meetingPlanAddForm.getStartMonth() < DateUtils.getMonth()) {
            throw new ApiException("月份小于当前月份", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "月份小于当前月份"));
        }
    }

    /**
     * 校验组织生活是否已存在
     */
    private MeetingPlanEntity getMeetingPlanEntity(long id) {
        MeetingPlanEntity meetingPlanEntity = service.detail(id);
        if (meetingPlanEntity == null) {
            throw new ApiException(
                    "组织生活不存在",
                    new Result<>(
                            errors,
                            Global.Errors.NOT_FOUND,
                            HttpStatus.NOT_FOUND.value(),
                            StringCanstant.ORG_LIFE));
        }
        return meetingPlanEntity;
    }

    /**
     * TODO  组织生活查询执行组织 分页查询
     *
     * @param meetingPlanId
     * @return
     */
    /*@HttpMonitorLogger
    @GetMapping("/execute-org-page")
    public ResponseEntity<Result<?>> executeOrgPage(@Range(min = 1, max = 99999999999L, message = "{Range.id}")
                                                    @RequestParam("meeting_plan_id") long meetingPlanId
            , @Length(max = 200, message = "{Length.org.name}") @RequestParam(name = "org_name", required = false) String orgName
            , @RequestParam(name = "page_no", defaultValue = "1") int pageNo
            , @RequestParam(name = "page_size", defaultValue = "10") int pageSize) {

        return new ResponseEntity<>(new Result<>(this.service.executeOrgPage(
                meetingPlanId, orgName, pageNo, pageSize), errors), HttpStatus.OK);
    }*/

}
