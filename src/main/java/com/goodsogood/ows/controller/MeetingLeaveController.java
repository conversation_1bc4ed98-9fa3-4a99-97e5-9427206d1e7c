package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.HeaderService;
import com.goodsogood.ows.service.MeetingLeaveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.models.auth.In;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 请假管理
 *
 * <AUTHOR>
 * @create 2018/10/29 15:11
 */
@RestController
@RequestMapping("/leave")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "请假管理", tags = {"请假管理"})
@Validated
public class MeetingLeaveController {

    private final Errors errors;
    private final MeetingLeaveService meetingLeaveService;
    private final HeaderService headerService;


    @Autowired
    public MeetingLeaveController(MeetingLeaveService meetingLeaveService, HeaderService headerService, Errors errors) {
        this.meetingLeaveService = meetingLeaveService;
        this.headerService = headerService;
        this.errors = errors;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "添加请假")
    @PostMapping("/add")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> add(@RequestHeader HttpHeaders headers,
                                            @Valid @RequestBody MeetingLeaveAddForm meetingLeaveAddForm,
                                            BindingResult bindingResult) throws Exception {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Long> result = new Result<>(meetingLeaveService.add(sysHeader, meetingLeaveAddForm), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "移动端请假列表查询")
    @GetMapping("/list")
    public ResponseEntity<Result<List<LeaveMeetingForm>>> list(@RequestHeader HttpHeaders headers,
                                                               @ApiParam(value = "审批类型(1:请假，2:销假 3：请假记录)") @RequestParam(name = "type", required = false, defaultValue = "1") Integer type,
                                                               @ApiParam(value = "会议类型(多个按逗号分隔)") @RequestParam(name = "types", required = false) List<Integer> types,
                                                               @ApiParam(value = "请假审批状态") @RequestParam(name = "status", required = false) Integer status,
                                                               @ApiParam(value = "会议开始时间") @RequestParam(name = "start_time", required = false) String startTime,
                                                               @ApiParam(value = "会议结束时间") @RequestParam(name = "end_time", required = false) String endTime,
                                                               @ApiParam(value = "关键字-搜索") @Length(max = 50, message = "{Length.keyWord}") @RequestParam(name = "key_word", required = false) String keyWord) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        List<LeaveMeetingForm> list = meetingLeaveService.leaveList(sysHeader.getRegionId(), sysHeader.getUserId(), type, types, startTime, endTime, keyWord,status);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @ApiOperation(value = "审批列表查询")
    @GetMapping("/list-wait-approve")
    public ResponseEntity<Result<List<LeaveMeetingForm>>> queryApprovelist(@RequestHeader HttpHeaders headers,
                                                            @ApiParam(value = "类型(1:待我审批  2：我已审批的)") @RequestParam(name = "type") Integer type,
                                                            @ApiParam(value = "会议类型(多个按逗号分隔)") @RequestParam(name = "types", required = false) List<Integer> types,
                                                            @ApiParam(value = "姓名") @RequestParam(name = "user_name", required = false) String userName,
                                                            @ApiParam(value = "请假审批状态") @RequestParam(name = "status", required = false) Integer status,
                                                            @ApiParam(value = "会议开始时间") @RequestParam(name = "start_time", required = false) String startTime,
                                                            @ApiParam(value = "会议结束时间") @RequestParam(name = "end_time", required = false) String endTime,
                                                            @ApiParam(value = "关键字-搜索") @Length(max = 50, message = "{Length.keyWord}") @RequestParam(name = "key_word", required = false) String keyWord) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        List<LeaveMeetingForm> list = meetingLeaveService.approveList(sysHeader.getRegionId(), sysHeader.getUserId(), type, types, userName, startTime, endTime, keyWord,status);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }



    @HttpMonitorLogger
    @ApiOperation(value = "详情")
    @GetMapping("/detail/{meeting_leave_id}")
    public ResponseEntity<Result<LeaveMeetingForm>> detail(@ApiParam(value = "请假id", required = true) @PathVariable(name = "meeting_leave_id") long meetingLeaveId) {
        Result<LeaveMeetingForm> result = new Result<>(meetingLeaveService.detail(meetingLeaveId), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @HttpMonitorLogger
    @ApiOperation(value = "请假或销假审批")
    @PostMapping("/check")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> check(@RequestHeader HttpHeaders headers,
                                                 @Valid @RequestBody MeetingLeaveCheckForm meetingLeaveCheckForm,
                                                 BindingResult bindingResult) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Boolean> result = new Result<>(meetingLeaveService.check(sysHeader, meetingLeaveCheckForm), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }



    @HttpMonitorLogger
    @ApiOperation(value = "撤销请假")
    @GetMapping("/cancel/{meeting_leave_id}")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> cancel(@RequestHeader HttpHeaders headers,
                                                  @ApiParam(value = "请假id", required = true) @PathVariable(name = "meeting_leave_id") Long meetingLeaveId,
                                                  @RequestParam(value="reason", required = false) String reason) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Boolean> result = new Result<>(meetingLeaveService.cancel(sysHeader, meetingLeaveId,reason), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


//    @HttpMonitorLogger
//    @ApiOperation(value = " 请假管理:待我审批/我已审批的列表查询")
//    @GetMapping("/list")
//    public ResponseEntity<Result<List<LeaveListForm>>> list(@RequestHeader HttpHeaders headers,
//                                                            @ApiParam("请假人")
//                                                            @Length(max = 50, message = "{Length.meetingLeave.name}")
//                                                            @RequestParam(value = "name", required = false) String name,
//                                                            @ApiParam("活动类型")
//                                                            @RequestParam(value = "type_ids", required = false) List<Long> typeIds,
//                                                            @ApiParam("审批状态：1.待审批的；2.以审批的")
//                                                            @Range(min = 1, max = 2, message = "Range.meetingLeave.checkStatus")
//                                                            @RequestParam(value = "check_status", required = false) Short checkStatus,
//                                                            @DateTimeFormat(pattern = "yyyy-MM-dd")
//                                                            @ApiParam("活动开始开始起始时间。yyyy-MM-dd")
//                                                            @RequestParam(value = "start_time", required = false) Date startTime,
//                                                            @DateTimeFormat(pattern = "yyyy-MM-dd")
//                                                            @ApiParam("活动结束结束截止时间。yyyy-MM-dd")
//                                                            @RequestParam(value = "end_time", required = false) Date endTime) {
//        /*
//         *  1.组织id不能为null
//         *  2.用户id不能为null
//         */
//        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
//        LeaveListForm leaveListForm = new LeaveListForm(typeIds, checkStatus, sysHeader.getUserId(), sysHeader.getOid(),startTime);
//        if (StringUtils.isNotBlank(name)) {
//            leaveListForm.setUserName(name.trim());
//        }
//        if (endTime != null) {
//            leaveListForm.setSendTime(DateUtils.toLastSecondOfDay(endTime));
//        }
//        Result<List<LeaveListForm>> result = new Result<>(meetingLeaveService.leaveList(leaveListForm), errors);
//        return new ResponseEntity<>(result, HttpStatus.OK);
//    }

    @GetMapping("/t1")
    public void t1(Long meetingId){
        meetingLeaveService.meetingAfterRejectLeave(meetingId);
    }
}
