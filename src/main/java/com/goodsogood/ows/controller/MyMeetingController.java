package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.MyMeetingListForm;
import com.goodsogood.ows.model.vo.MyMeetingResultListForm;
import com.goodsogood.ows.model.vo.MyMeetingStatsForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.MyMeetingService;
import com.goodsogood.ows.utils.DateUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 我的活动
 *
 * <AUTHOR>
 * @create 2018-10-25 10:53
 */
@RestController
@RequestMapping("/my-meeting")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class MyMeetingController {

    private final Errors errors;
    private final MyMeetingService myMeetingService;

    @Autowired
    public MyMeetingController(Errors errors, MyMeetingService myMeetingService) {
        this.errors = errors;
        this.myMeetingService = myMeetingService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "当前人员为参会人员或列席人员的活动")
    @GetMapping("/list")
    public ResponseEntity<Result<List<MyMeetingResultListForm>>> list(
            @RequestHeader HttpHeaders headers,
            @ApiParam("活动名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("1：待举办；2：进行中：3：结束") @RequestParam(value = "status", required = false) Short status,
            @ApiParam("活动id") @RequestParam(value = "meeting_id", required = false) Long meetingId,
            @ApiParam("活动类型") @RequestParam(value = "type_ids", required = false) List<Long> typeIds,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("活动开始开始起始时间。yyyy-MM-dd")
            @RequestParam(value = "start_time", required = false)
                    Date startTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("活动结束结束截止时间。yyyy-MM-dd")
            @RequestParam(value = "end_time", required = false)
                    Date endTime) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MyMeetingListForm meetingListForm = new MyMeetingListForm();
        meetingListForm.setRegionId(sysHeader.getRegionId());
        meetingListForm.setStatus(status);
        meetingListForm.setMeetingId(meetingId);
        if (StringUtils.isNotBlank(name)) {
            meetingListForm.setName(name.trim());
        }
        meetingListForm.setStartTime(startTime);
        meetingListForm.setEndTime(DateUtils.toLastSecondOfDay(endTime));
        meetingListForm.setTypeIds(typeIds);
        meetingListForm.setUserId(sysHeader.getUserId());
        Result<List<MyMeetingResultListForm>> result =
                new Result<>(myMeetingService.list(meetingListForm), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "我的活动统计")
    @GetMapping("/stats")
    public ResponseEntity<Result<MyMeetingStatsForm>> stats(@RequestHeader HttpHeaders headers) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MyMeetingListForm meetingListForm = new MyMeetingListForm();
        meetingListForm.setUserId(sysHeader.getUserId());
        meetingListForm.setRegionId(sysHeader.getRegionId());
        MyMeetingStatsForm myMeetingStatsForm = new MyMeetingStatsForm();
        meetingListForm.setStatus((short) 1); // 待举办
        myMeetingStatsForm.setSoonStartCount(myMeetingService.stats(meetingListForm));
        meetingListForm.setStatus((short) 2); // 进行中
        myMeetingStatsForm.setOnGoingCount(myMeetingService.stats(meetingListForm));
        meetingListForm.setStatus((short) 3); // 结束
        myMeetingStatsForm.setEndCount(myMeetingService.stats(meetingListForm));
        Result<MyMeetingStatsForm> result = new Result<>(myMeetingStatsForm, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}
