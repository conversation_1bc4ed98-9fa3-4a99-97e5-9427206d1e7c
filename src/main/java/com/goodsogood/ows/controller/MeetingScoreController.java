package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.MeetingScore;
import com.goodsogood.ows.configuration.MeetingScoreConfig;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.MeetingScoreService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.redisLock.RedisLockUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * 组织生活活动积分
 */
@RestController
@RequestMapping("/meeting/score")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class MeetingScoreController {

    private final Errors errors;
    private final MeetingScoreService meetingScoreService;
    private final MeetingScoreConfig meetingScoreConfig;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public MeetingScoreController(Errors errors, MeetingScoreService meetingScoreService, MeetingScoreConfig meetingScoreConfig, StringRedisTemplate redisTemplate) {
        this.errors = errors;
        this.meetingScoreService = meetingScoreService;
        this.meetingScoreConfig = meetingScoreConfig;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 未开展党小组会的组长扣分 手动调用接口
     * @param queryDate  查询时间 yyyy-MM-dd
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/test/unfinishedGroupLeader")
    public ResponseEntity<Result<?>> unfinishedGroupLeader(@RequestHeader HttpHeaders headers,
                                           @RequestParam(value = "regionId") Long regionId,
                                           @RequestParam(value = "queryDate") String queryDate) {
        String requestId = UUID.randomUUID().toString();
        boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, "MEETING_UNFINISHED_GROUP_LEADER_TEST_KEY", requestId, 20*60*1000);
        try {
            if(lock){
                log.debug("<未开展党小组会的组长扣分 手动调用接口> 调用开始! regionId={} queryDate={} ",regionId,queryDate);
                //限制查询日期，不能超过上月
                String qd = queryDate.substring(0,7);
                if(DateUtils.beforeThisMonth(qd)){
                    meetingScoreService.unfinishedGroupLeader(regionId, DateUtils.stringToDate(queryDate,"yyyy-MM-dd"));
                }else{
                    log.debug("<未开展党小组会的组长扣分 手动调用接口> 指定时间不在当前月份之前！regionId={} queryDate={} ",regionId,queryDate);
                }
                log.debug("<未开展党小组会的组长扣分 手动调用接口> 调用完成! regionId={} queryDate={} ",regionId,queryDate);
            }else {
                log.debug("<未开展党小组会的组长扣分 手动调用接口> 正在执行上一次调用，本次未获得锁，请稍后再试! regionId={} queryDate={}", regionId,queryDate);
            }
        }catch (Exception e){
                log.error("<未开展党小组会的组长扣分 手动调用接口>  报错！regionId={} queryDate={}",regionId,queryDate,e);
        }finally {
            //解锁
            RedisLockUtil.releaseDistributedLock(redisTemplate, "MEETING_UNFINISHED_GROUP_LEADER_TEST_KEY", requestId);
        }
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 基础分发放 手动调用接口
     * @param queryDate  查询时间 yyyy-MM-dd
     * @param scoreType  积分类型 4.支委会成员每年基础分 5.党小组长每年基础分
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/test/basicsScoreAdd")
    public ResponseEntity<Result<?>> unfinishedGroupLeader(@RequestHeader HttpHeaders headers,
                                                           @RequestParam(value = "regionId") Long regionId,
                                                           @RequestParam(value = "queryDate") String queryDate,
                                                           @RequestParam(value = "scoreType") Integer scoreType) {
        String requestId = UUID.randomUUID().toString();
        boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, "MEETING_BASICS_SCORE_ADD_TEST_KEY", requestId, 20*60*1000);
        try {
            if(lock){
                log.debug("<基础分发放 手动调用接口> 调用开始! regionId={} queryDate={} scoreType={}",regionId,queryDate, scoreType);
                //限制查询日期，不能超过当前月
                String qd = queryDate.substring(0,7);
                if(!DateUtils.afertThisMonth(qd)){// 判断是否在本月之前
                    meetingScoreService.basicsScoreAdd(regionId,scoreType,DateUtils.stringToDate(queryDate,"yyyy-MM-dd"));
                }else{
                    log.debug("<基础分发放 手动调用接口> 指定时间不能超过当前月！regionId={} queryDate={} ",regionId,queryDate);
                }
                log.debug("<基础分发放 手动调用接口> 调用完毕! regionId={} queryDate={} scoreType={}",regionId,queryDate, scoreType);
            }else {
                log.debug("<基础分发放 手动调用接口> 正在执行上一次调用，本次未获得锁，请稍后再试! regionId={} queryDate={} scoreType={}", regionId,queryDate,scoreType);
            }
        }catch (Exception e){
            log.error("<基础分发放 手动调用接口>  报错！regionId={} queryDate={} scoreType={}",regionId,queryDate, scoreType,e);
        }finally {
            //解锁
            RedisLockUtil.releaseDistributedLock(redisTemplate, "MEETING_BASICS_SCORE_ADD_TEST_KEY", requestId);
        }
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 人员或组织加分 手动调用接口
     * @param model 积分模式 user org
     * @param operationType 操作类型  0 加分  1 扣分
     * @param meetingType 活动类型 1.党支部党员大会 2.党支部委员会会议 3.党小组会 4.党课  5.主题党日
     * @param scoreType 积分类型 1.党员考勤积分 2.讲课人积分 3.党小组组长开展积分
     * @param idList 编号集合
     *
     * isRollBack 是否为回滚操作  0 否 1 是。
     * 如果是回滚操作则需要判断引起回滚操作的会议的当前积分情况(获得和扣减的情况)，根据本次会议的积分情况判断是否进行真实的加分或扣分
     * 例如考勤已签到加分后，撤回/回退/取消操作时扣分就是回滚操作。
     * 例如考勤未签到扣分后，撤回/回退/取消操作时加分就是回滚操作。
     * meetingId 会议编号，isRollBack 为 1 时，此字段为必填字段
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/test/operation/meetingScore")
    public ResponseEntity<Result<?>> operationMeetingScore(@RequestHeader HttpHeaders headers,
                                                           @RequestParam(value = "regionId") Long regionId,
                                                           @RequestParam(value = "queryDate") String queryDate,
                                                           @RequestParam(value = "model") String model,
                                                           @RequestParam(value = "operationType") Integer operationType,
                                                           @RequestParam(value = "meetingType") Integer meetingType,
                                                           @RequestParam(value = "scoreType") Integer scoreType,
                                                           @RequestParam(value = "idList") List<Long> idList,
                                                           @RequestParam(value = "isRollBack") Integer isRollBack,
                                                           @RequestParam(value = "meetingId") Long meetingId) {

        String requestId = UUID.randomUUID().toString();
        boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, "MEETING_OPERATION_MEETING_SCORE_TEST_KEY", requestId, 20*60*1000);
        try {
            if(lock){
                log.debug("<人员或组织加分 手动调用接口> 调用开始! regionId={} queryDate={} model={} operationType={} meetingType={} scoreType={} idList={} isRollBack={} meetingId={}",
                        regionId,queryDate,model,operationType,meetingType, scoreType,idList,isRollBack,meetingId);
                //限制查询日期，不能超过当前月
                if(!DateUtils.afertThisMonth(queryDate)){
                    //读取配置
                    MeetingScoreConfig.ScoreConf sc = meetingScoreConfig.getScoreConf().get(regionId);
                    MeetingScore msConf = sc.getUserScore().stream().filter(ms->{return ms.getMeetingType()==meetingType&&ms.getScoreType()==scoreType;}).findFirst().get();
                    log.debug("<人员或组织加分 手动调用接口> 获得配置: msConf={}",msConf);
                    if("user".equals(model)){
                        if(operationType==0){
                            meetingScoreService.userScoreAdd(regionId,meetingId,msConf.getTypeId(),scoreType,idList,DateUtils.stringToDate(queryDate,"yyyy-MM-dd"),isRollBack);
                        }else{
                            meetingScoreService.userScoreReduce(regionId,meetingId,msConf.getTypeId(),scoreType,idList,DateUtils.stringToDate(queryDate,"yyyy-MM-dd"),isRollBack);
                        }
                    }else if("org".equals(model)){
                        if(operationType==0){
                            meetingScoreService.orgScoreAdd(regionId,meetingId,msConf.getTypeId(),scoreType,idList,DateUtils.stringToDate(queryDate,"yyyy-MM-dd"),isRollBack);
                        }else{
                            meetingScoreService.orgScoreReduce(regionId,meetingId,msConf.getTypeId(),scoreType,idList,DateUtils.stringToDate(queryDate,"yyyy-MM-dd"),isRollBack);
                        }
                    }
                }else{
                    log.debug("<基础分发放 手动调用接口> 指定时间不能超过当前月！regionId={} queryDate={} ",regionId,queryDate);
                }
                log.debug("<人员或组织加分 手动调用接口> 调用完成! regionId={} queryDate={} model={} operationType={} meetingType={} scoreType={} idList={}",
                        regionId,queryDate,model,operationType,meetingType, scoreType,idList);
            }else {
                log.debug("<人员或组织加分 手动调用接口> 正在执行上一次调用，本次未获得锁，请稍后再试! regionId={} queryDate={} model={} operationType={} meetingType={} scoreType={} idList={} isRollBack={} meetingId={}",
                        regionId,queryDate,model,operationType,meetingType, scoreType,idList,isRollBack,meetingId);
            }
        }catch (Exception e){
            log.error("<人员或组织加分 手动调用接口> 调用报错! regionId={} queryDate={} model={} operationType={} meetingType={} scoreType={} idList={} ",
                    regionId,queryDate,model,operationType,meetingType, scoreType,idList,e);
        }finally {
            //解锁
            RedisLockUtil.releaseDistributedLock(redisTemplate, "MEETING_OPERATION_MEETING_SCORE_TEST_KEY", requestId);
        }
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }
}
