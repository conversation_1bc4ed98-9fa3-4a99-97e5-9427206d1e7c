package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.StatusEnum;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.MeetingPlanEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.MeetingOrgCommendPenalizeService;
import com.goodsogood.ows.validate.group.Add;
import com.goodsogood.ows.validate.group.Delete;
import com.goodsogood.ows.validate.group.Detail;
import com.goodsogood.ows.validate.group.Edit;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @describe 组织奖惩入口
 * @date 2019-12-30
 */
@RestController
@RequestMapping("/org/commend/penalize")
@Log4j2
@Validated
public class MeetingOrgCommendPenalizeController {

    private final Errors errors;

    private final MeetingOrgCommendPenalizeService meetingOrgCommendPenalizeService;

    @Autowired
    public MeetingOrgCommendPenalizeController(Errors errors, MeetingOrgCommendPenalizeService meetingOrgCommendPenalizeService) {
        this.errors = errors;
        this.meetingOrgCommendPenalizeService = meetingOrgCommendPenalizeService;
    }


    @HttpMonitorLogger
    @ApiOperation(value = "添加组织奖励")
    @PostMapping("/add")
    @RepeatedCheck
    public ResponseEntity<Result> add(@RequestHeader HttpHeaders headers,
                                      @Validated(value = Add.class) @RequestBody MeetingOrgCommendPenalizeForm dto) {
        meetingOrgCommendPenalizeService.add(headers, dto);
        Result<MeetingPlanEntity> result = new Result<>(null, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "修改组织奖惩信息")
    @PostMapping("/edit")
    public ResponseEntity<Result> edit(@RequestHeader HttpHeaders headers,
                                       @Validated(value = Edit.class) @RequestBody MeetingOrgCommendPenalizeForm orgCommendPenalizelForm,
                                       BindingResult bindingResult) {

        meetingOrgCommendPenalizeService.edit(HeaderHelper.buildMyHeader(headers), orgCommendPenalizelForm, headers);
        Result<MeetingPlanEntity> result = new Result<>(null, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "查询组织奖惩详情")
    @PostMapping("/detail")
    public ResponseEntity<Result<MeetingOrgCommendPenalizeDetailVO>> detail(@Validated(value = Detail.class) @RequestBody MeetingOrgCommendPenalizeForm orgCommendPenalizeForm) {
        MeetingOrgCommendPenalizeDetailVO vo = meetingOrgCommendPenalizeService.detail(orgCommendPenalizeForm);
        Result<MeetingOrgCommendPenalizeDetailVO> result = new Result<>(vo, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "删除组织奖惩")
    @PostMapping("/delete")
    public ResponseEntity<Result<?>> delete(@RequestHeader HttpHeaders headers,
                                            @Validated(value = Delete.class) @RequestBody MeetingOrgCommendPenalizeForm orgCommendPenalizelForm,
                                            BindingResult bindingResult) {
        meetingOrgCommendPenalizeService.delete(headers, orgCommendPenalizelForm);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "查询组织奖惩信息列表")
    @PostMapping("/list")
    public ResponseEntity<Result<Page<MeetingOrgCommendPenalizeQueryVO>>> list(
            @Valid @RequestBody MeetingOrgCommendPenalizeQueryForm orgCommendPenalizeQueryForm,
            BindingResult bindingResult,
            @RequestHeader HttpHeaders headers) {
        Result<Page<MeetingOrgCommendPenalizeQueryVO>> result = new Result<>(meetingOrgCommendPenalizeService.list(orgCommendPenalizeQueryForm, headers), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "查询单个组织奖惩信息列表")
    @GetMapping("/select-by-org-id")
    public ResponseEntity<Result<?>> selectByOrgId(@RequestParam("org_id") Long orgId,
                                                   @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                   @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize,
                                                   @RequestHeader HttpHeaders headers) {
        Result<Page<MeetingOrgCommendPenalizeQueryVO>> result = new Result<>(meetingOrgCommendPenalizeService.queryOrgCommendPenalizeListByOrgId(orgId, page, pageSize), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "构建导出组织奖惩信息文件")
    @PostMapping("/export")
    public ResponseEntity<Result<String>> excelList(@Valid @RequestBody MeetingOrgCommendPenalizeQueryForm form,
                                                     BindingResult bindingResult,
                                                     @RequestHeader HttpHeaders headers
                                                     ) {
        final String uuid = UUID.randomUUID().toString();
        this.meetingOrgCommendPenalizeService.excelList(form, uuid, headers);
        Result<String> result = new Result<>(uuid, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "导出组织奖惩信息列表")
    @GetMapping("/export")
    public ResponseEntity<Result<Object>> export(@RequestParam("uuid") String uuid,
                                                 @RequestHeader HttpHeaders headers
    ) {
        Result<Object> result = new Result<>(this.meetingOrgCommendPenalizeService.getCommendPenalizeFile(uuid), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取组织荣誉统计信息")
    @GetMapping("/get-org-commend-statistics")
    public ResponseEntity<Result<?>> getOrgCommendStatistics(@RequestParam(value = "org_id",required = false) Long orgId,
                                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("获取组织荣誉统计信息 参数:[{}]", orgId);
        return new ResponseEntity<>(new Result<>(
                this.meetingOrgCommendPenalizeService.getOrgCommendStatistics(orgId),
                errors), HttpStatus.OK);
    }
}
