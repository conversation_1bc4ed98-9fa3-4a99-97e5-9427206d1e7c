package com.goodsogood.ows.controller;

import cn.hutool.core.convert.Convert;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.UserCommentEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.service.UserCommentExportService;
import com.goodsogood.ows.service.UserCommentService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 党员评议控制层
 * @date 2019/12/27
 */
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/comment/user")
public class UserCommentController {

    private static final String REDIS_REPEAT_DOWN_KEY = "REDIS_REPEAT_DOWN_COMMENT_";

    private final Errors errors;
    private final StringRedisTemplate redisTemplate;
    private final UserCommentService userCommentService;
    private final UserCommentExportService userCommentExportService;

    @Autowired
    public UserCommentController(Errors errors, StringRedisTemplate redisTemplate, UserCommentService userCommentService,
                                 UserCommentExportService userCommentExportService) {
        this.errors = errors;
        this.redisTemplate = redisTemplate;
        this.userCommentService = userCommentService;
        this.userCommentExportService = userCommentExportService;
    }

    /**
     * 民主评议查询接口
     * <AUTHOR>
     * @date 2019/12/30
     * @param form
     * @param bindingResult
     * @param headers
     * @return org.springframework.http.ResponseEntity<com.goodsogood.ows.model.vo.Result<?>>
     */
    @HttpMonitorLogger
    @ApiOperation(value = "民主评议查询")
    @PostMapping("/query")
    public ResponseEntity<Result<?>> queryUserCommentList(@Valid @RequestBody UserCommentQueryForm form,
                                                          BindingResult bindingResult,
                                                          @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("民主评议查询 参数:[{}]", form);
        Result<?> result = this.userCommentService.getUserCommentList(form, headers);
        log.debug("查询列表结果 -> [{}]", result);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 民主评议新增接口
     * <AUTHOR>
     * @date 2019/12/30
     * @param addVO
     * @param bindingResult
     * @param headers
     * @return org.springframework.http.ResponseEntity<com.goodsogood.ows.model.vo.Result<?>>
     */
    @HttpMonitorLogger
    @ApiOperation(value = "民主评议新增")
    @PostMapping("/insert")
    public ResponseEntity<Result<?>> insertUserComment(@Valid @RequestBody UserCommentAddVO addVO,
                                                    BindingResult bindingResult,
                                                    @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("新增民主评议 参数:[{}], 操作人:[{}]", addVO, header.getUserId());
        // 不合格需要判断处理意见
        if(addVO.getRating().equals(Constant.UNQUALIFIED)) {
            if (null == addVO.getDealOpinion()) {
                return new ResponseEntity<>(new Result<>(errors, 2001, HttpStatus.OK.value()), HttpStatus.OK);
            }
        } else {
            if (null != addVO.getDealOpinion()) {
                return new ResponseEntity<>(new Result<>(errors, 2004, HttpStatus.OK.value()), HttpStatus.OK);
            }
        }
        int i = this.userCommentService.insertUserComment(addVO, header.getUserId(), header.getRegionId());
        log.debug("新增民主评议结果 -> [{}]", i);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 民主评议更新接口
     * <AUTHOR>
     * @date 2019/12/30
     * @param updateVO
     * @param bindingResult
     * @param headers
     * @return org.springframework.http.ResponseEntity<com.goodsogood.ows.model.vo.Result<?>>
     */
    @HttpMonitorLogger
    @ApiOperation(value = "民主评议更新")
    @PostMapping("/update")
    public ResponseEntity<Result<?>> updateUserComment(@Valid @RequestBody UserCommentUpdateVO updateVO,
                                                       BindingResult bindingResult,
                                                       @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("更新民主评议 参数:[{}], 操作人:[{}]", updateVO, header.getUserId());
        // 不合格需要判断处理意见
        if(updateVO.getRating().equals(Constant.UNQUALIFIED)) {
            if (null == updateVO.getDealOpinion()) {
                return new ResponseEntity<>(new Result<>(errors, 2001, HttpStatus.OK.value()), HttpStatus.OK);
            }
        } else {
            if (null != updateVO.getDealOpinion()) {
                return new ResponseEntity<>(new Result<>(errors, 2004, HttpStatus.OK.value()), HttpStatus.OK);
            }
        }
        int i = this.userCommentService.updateUserComment(updateVO, header.getUserId());
        log.debug("更新民主评议结果 -> [{}]", i);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 民主评议删除接口
     * <AUTHOR>
     * @date 2019/12/30
     * @param userCommentId
     * @param headers
     * @return org.springframework.http.ResponseEntity<com.goodsogood.ows.model.vo.Result<?>>
     */
    @HttpMonitorLogger
    @ApiOperation(value = "民主评议删除")
    @GetMapping("/del")
    public ResponseEntity<Result<?>> delUserComment(@RequestParam("user_comment_id") Long userCommentId,
                                                       @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("删除民主评议 主键:[{}]", userCommentId);
        int i = this.userCommentService.delUserComment(userCommentId, header.getUserId());
        log.debug("删除民主评议 -> [{}]", i);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 单个查询民主评议
     * <AUTHOR>
     * @date 2019/12/30
     * @param userCommentId
     * @param headers
     * @return org.springframework.http.ResponseEntity<com.goodsogood.ows.model.vo.Result<?>>
     */
    @HttpMonitorLogger
    @ApiOperation(value = "民主评议查询")
    @GetMapping("/select")
    public ResponseEntity<Result<?>> selectUserComment(@RequestParam("user_comment_id") Long userCommentId,
                                                    @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("查询民主评议 主键:[{}]", userCommentId);
        UserCommentEntity userComment = this.userCommentService.getUserComment(userCommentId);
        log.debug("查询民主评议 -> [{}]", userComment);
        return new ResponseEntity<>(new Result<>(userComment, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "民主评议开始导出")
    @PostMapping("/export")
    public ResponseEntity<Result<?>> exportUserCommentList(@Valid @RequestBody UserCommentQueryForm form,
                                                           BindingResult bindingResult,
                                                           @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long userId = header.getUserId();
        String redisKey = REDIS_REPEAT_DOWN_KEY + userId;
        if (Boolean.TRUE.equals(this.redisTemplate.hasKey(redisKey))) {
            throw new ApiException("请不要重复提交下载", new Result<>(errors, Global.Errors.REPEAT_DATA_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "请不要重复提交下载"));
        } else {
            this.redisTemplate.opsForValue().set(redisKey, Convert.toStr(userId), 5L, TimeUnit.MINUTES);
        }
        log.debug("民主评议导出 参数:[{}]", form);
        // 生成随机码
        String uuid = UUID.randomUUID().toString();
        this.userCommentExportService.exportUserComment(form, uuid, redisKey, headers);
        return new ResponseEntity<>(new Result<>(uuid, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "民主评议导出")
    @GetMapping("/export")
    public ResponseEntity<Result<?>> exportUserComment(@RequestParam("uuid") String uuid,
                                                       HttpServletRequest request,
                                                       HttpServletResponse response,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long userId = header.getUserId();
        String redisKey = REDIS_REPEAT_DOWN_KEY + userId;
        return new ResponseEntity<>(new Result<>(this.userCommentExportService.exportComment(uuid, redisKey, request, response), errors), HttpStatus.OK);
    }
}
