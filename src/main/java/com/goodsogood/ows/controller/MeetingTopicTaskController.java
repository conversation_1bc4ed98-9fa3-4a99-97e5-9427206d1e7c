package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.MeetingTopicTaskListForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.TopicOrgForm;
import com.goodsogood.ows.service.TopicOrgService;
import com.goodsogood.ows.service.UserCenterService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.PageUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 任务任务 主题管理
 *
 * <AUTHOR>
 * @create 2018-10-25 10:53
 **/
@RestController
@RequestMapping("/topic-task")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class MeetingTopicTaskController {

    private final Errors errors;
    private final TopicOrgService topicOrgService;
    private final UserCenterService userService;

    @Autowired
    public MeetingTopicTaskController(Errors errors, TopicOrgService topicOrgService, UserCenterService userService) {
        this.errors = errors;
        this.topicOrgService = topicOrgService;
        this.userService = userService;
    }

    // 2018年11月21日 17:10:15 chenanshun
    @HttpMonitorLogger
    @ApiOperation(value = "指定给本组织的所有任务任务(all)")
    @GetMapping("/list-all/{tag}")
    public ResponseEntity<Result<List<TopicOrgForm>>> list(
            @RequestHeader HttpHeaders headers,
            @ApiParam("请求标记:1、发起活动时查询；2、任务完成情况页面查询")
            @Range(min = 1, max = 3, message = "{Range.select.taskTag}")
            @PathVariable("tag")
                    short tag,
            @ApiParam("完成情况:1：未完成；2：完成；3：已逾期") @RequestParam(value = "status", required = false)
                    Short status,
            @ApiParam("任务名称")
            @Length(max = 100, message = "{Length.topic.name}")
            @RequestParam(value = "name", required = false)
                    String name,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("任务开始起始时间。yyyy-MM-dd")
            @RequestParam(value = "s_start_time", required = false)
                    Date sStartTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("任务开始结束时间。yyyy-MM-dd")
            @RequestParam(value = "e_start_time", required = false)
                    Date eStartTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("任务结束起始时间。yyyy-MM-dd")
            @RequestParam(value = "s_end_time", required = false)
                    Date sEndTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("任务结束截止时间。yyyy-MM-dd")
            @RequestParam(value = "e_end_time", required = false)
                    Date eEndTime) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingTopicTaskListForm meetingTopicTaskListForm = this.getMeetingTopicTaskListForm(tag, sysHeader.getOid(), status, name, sStartTime, eStartTime, sEndTime, eEndTime);
        if (tag == 2) { // 此时表示任务完成情况页面查询
            meetingTopicTaskListForm.setIsH5((short) 1);
            meetingTopicTaskListForm.setOrgId(null);
            meetingTopicTaskListForm.setOrgIds(userService.getUserOidByH5(headers, sysHeader));
        }
        Result<List<TopicOrgForm>> result = new Result<>(topicOrgService.list(meetingTopicTaskListForm), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "指定给本组织的所有任务任务(page)")
    @GetMapping("/list/{tag}")
    public ResponseEntity<Result<List<TopicOrgForm>>> page(
            @RequestHeader HttpHeaders headers,
            @ApiParam("请求标记:1、发起活动时查询；2、任务完成情况页面查询")
            @Range(min = 1, max = 2, message = "{Range.select.taskTag}")
            @PathVariable("tag")
                    short tag,
            @ApiParam("完成情况:1：未完成；2：完成；3：已逾期")
            @Range(min = 1, max = 3, message = "{Range.topicTask.status}")
            @RequestParam(value = "status", required = false)
                    Short status,
            @ApiParam("任务名称")
            @Length(max = 100, message = "{Length.topic.name}")
            @RequestParam(value = "name", required = false)
                    String name,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("任务开始起始时间。yyyy-MM-dd")
            @RequestParam(value = "s_start_time", required = false)
                    Date sStartTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("任务开始结束时间。yyyy-MM-dd")
            @RequestParam(value = "e_start_time", required = false)
                    Date eStartTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("任务结束起始时间。yyyy-MM-dd")
            @RequestParam(value = "s_end_time", required = false)
                    Date sEndTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam("任务结束截止时间。yyyy-MM-dd")
            @RequestParam(value = "e_end_time", required = false)
                    Date eEndTime,
            @ApiParam("页码，默认为1") @RequestParam(value = "page_no", required = false) Integer pageNo,
            @ApiParam("每页行数") @RequestParam(value = "page_size", required = false) Integer pageSize) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingTopicTaskListForm meetingTopicTaskListForm = this.getMeetingTopicTaskListForm(tag, sysHeader.getOid(), status, name, sStartTime, eStartTime, sEndTime, eEndTime);
        meetingTopicTaskListForm.setPageBean(PageUtils.page(pageNo,pageSize));
        Result<List<TopicOrgForm>> result = new Result<>(topicOrgService.page(meetingTopicTaskListForm), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 活动类型查询参数
     */
    private MeetingTopicTaskListForm getMeetingTopicTaskListForm(short tag,
                                                                 Long orgId,
                                                                 Short status,
                                                                 String name,
                                                                 Date sStartTime,
                                                                 Date eStartTime,
                                                                 Date sEndTime,
                                                                 Date eEndTime) {
        MeetingTopicTaskListForm meetingTopicTaskListForm = new MeetingTopicTaskListForm();
        meetingTopicTaskListForm.setTag(tag);
        meetingTopicTaskListForm.setOrgId(orgId);
        meetingTopicTaskListForm.setStatus(status);
        if (StringUtils.isNotBlank(name)) {
            meetingTopicTaskListForm.setName(name.trim());
        }
        meetingTopicTaskListForm.setSStartTime(sStartTime);
        meetingTopicTaskListForm.setEStartTime(DateUtils.toLastSecondOfDay(eStartTime));
        meetingTopicTaskListForm.setSEndTime(sEndTime);
        meetingTopicTaskListForm.setEEndTime(DateUtils.toLastSecondOfDay(eEndTime));
        return meetingTopicTaskListForm;
    }
}
