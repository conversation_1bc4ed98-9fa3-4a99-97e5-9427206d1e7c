package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.LifeFileEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.LifeFileService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
民主生活会-附件处理
 */
@Controller
@RequestMapping("/life/file")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
public class LifeFileController {
    private final LifeFileService lifeFileService;
    private final Errors errors;
    @Autowired
    public LifeFileController(LifeFileService lifeFileService,Errors errors) {
        this.lifeFileService = lifeFileService;
        this.errors = errors;
    }

    @ApiOperation("查询附件")
    @HttpMonitorLogger
    @GetMapping("/query_attach")
    public ResponseEntity<Result<List<LifeFileEntity>>> queryAttach(@RequestHeader HttpHeaders headers,
                                                                    @RequestParam("life_id") Long lifeId, @RequestParam("type") List<Integer> type, Integer step){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<LifeFileEntity> list = lifeFileService.queryAttach(lifeId,type,step);
        Result<List<LifeFileEntity>> result = new Result<>(list, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @ApiOperation("查询附件汇总详情")
    @HttpMonitorLogger
    @GetMapping("/file_info")
    public ResponseEntity<Result<Map<Integer,List<LifeFileVO>>>> queryFileInfo(@RequestHeader HttpHeaders headers,
                                                                    @RequestParam("life_id") Long lifeId,Integer step){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Map<Integer,List<LifeFileVO>> map = lifeFileService.queryFileInfo(lifeId,step);
        Result<Map<Integer,List<LifeFileVO>>> result = new Result<>(map, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation("查询指定上传人")
    @HttpMonitorLogger
    @GetMapping("/query_uploader")
    public ResponseEntity<Result<List<LifeFileUserVO>>> queryUploader(@RequestHeader HttpHeaders headers,
                                                                  @RequestParam("life_id") Long lifeId, Integer type, @RequestParam(value="data_id",required = false)Long dataId){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<LifeFileUserVO> list = lifeFileService.queryUploader(lifeId,type,dataId);
        Result<List<LifeFileUserVO>> result = new Result<>(list,errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("上传后保存附件到数据库")
    @HttpMonitorLogger
    @PostMapping("/save_attach")
    public ResponseEntity<Result<List<LifeFileEntity>>> saveAttach(@RequestHeader HttpHeaders headers,
                                                               @Valid @RequestBody SaveAttachForm saveAttachForm,BindingResult bindingResult){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result(lifeFileService.saveAttach(saveAttachForm,header),errors), HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("上报材料")
    @HttpMonitorLogger
    @PostMapping("/report_attach")
    public ResponseEntity<Result<Boolean>> reportAttach(@RequestHeader HttpHeaders headers,@RequestParam("life_id") Long lifeId,
                                                        @RequestBody Map<Integer,List<LifeFileVO>> lifeFileMap ){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        lifeFileService.report(lifeId,lifeFileMap,header);
        return new ResponseEntity<>(new Result(true,errors), HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("删除附件")
    @HttpMonitorLogger
    @GetMapping("/delete_attach")
    public ResponseEntity<Result<Boolean>> deleteAttach(@RequestHeader HttpHeaders headers,
                                                        @RequestParam("life_id") Long lifeId,
                                                        @RequestParam (value="life_file_id",required = true) List<Long> lifeFileId,
                                                        @RequestParam(value= "step",required = true) Integer step){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result(lifeFileService.deleteAttach(lifeId,lifeFileId,step,header),errors), HttpStatus.OK);
    }


    @RepeatedCheck
    @ApiOperation("删除上传人")
    @HttpMonitorLogger
    @GetMapping("/delete_uploader")
    public ResponseEntity<Result<Boolean>> deleteUploader(@RequestHeader HttpHeaders headers,
                                                        @RequestParam("user_id") Long userId,
                                                          @RequestParam("life_id") Long lifeId,Integer type,@RequestParam(value="data_id",required = false) Long dataId){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result(lifeFileService.deleteUploader(userId,lifeId,type,dataId),errors), HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("指定上传人并发起上传任务")
    @HttpMonitorLogger
    @PostMapping("/file_task")
    public ResponseEntity<Result<Boolean>> fileTask(@RequestHeader HttpHeaders headers,
                                                          @RequestBody LifeFileTaskForm lifeFileTaskForm){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result(lifeFileService.fileTask(lifeFileTaskForm,header,headers),errors), HttpStatus.OK);
    }

    @ApiOperation("上传人打开任务时校验-注意参数")
    @HttpMonitorLogger
    @GetMapping("/open_task")
    public ResponseEntity<Result<Boolean>> openTask(@RequestHeader HttpHeaders headers,
                                                          @RequestParam("task_info") String taskInfo){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result(lifeFileService.openTask(taskInfo,header),errors), HttpStatus.OK);
    }

    @GetMapping("/clone")
    @ResponseBody
    public void test(@RequestParam("life_id") Long lifeId){
        lifeFileService.cloneMeetingToLife(lifeId);
    }

    @RepeatedCheck
    @ApiOperation("删除谈心谈话附件")
    @HttpMonitorLogger
    @PostMapping("/delete_talk_attach")
    public ResponseEntity<Result<Boolean>> deleteTalkFile( @RequestBody SaveAttachForm saveAttachForm){
        lifeFileService.deleteTalkFile(saveAttachForm);
        return new ResponseEntity<>(new Result(true,errors), HttpStatus.OK);
    }


}
