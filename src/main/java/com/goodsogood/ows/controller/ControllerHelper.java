package com.goodsogood.ows.controller;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.google.common.base.Preconditions;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

public class ControllerHelper {
    /**
     * headers转换为HeaderHelper.SysHeader
     *
     * <p>登录校验，会进行非空校验 {@link ControllerHelper#checkHeadersNotNullByLogin(HeaderHelper.SysHeader,
     * Errors)}
     * <p>如果是微信端访问，uiod不为空，oid被设置为uiod</>
     */
    public static HeaderHelper.SysHeader getSysHeader(HttpHeaders headers, Errors errors) {
        HeaderHelper.SysHeader sysHeader = getSysHeaderNoCheckHeadersNotNull(headers, errors);
        // 校验头信息
        checkHeadersNotNullByLogin(sysHeader, errors);
        // uoid不为空，oid=uoid
        if (sysHeader.getUoid() != null) {
            sysHeader.setOid(sysHeader.getUoid());
        }
        return sysHeader;
    }

    /**
     * headers转换为HeaderHelper.SysHeader
     *
     * <p>除必须参数外，不会进行非空校验 {@link
     */
    public static HeaderHelper.SysHeader getSysHeaderNoCheckHeadersNotNull(
            HttpHeaders headers, Errors errors) {
        checkNotNull("HttpHeaders", headers, errors);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        checkNotNull(HeaderHelper.OPERATOR_REGION, sysHeader.getRegionId(), errors);
        return sysHeader;
    }

    /**
     * 非空验证 校验oid、uoid,uid、type、token、userName、orgName、regionId
     */
    private static void checkHeadersNotNullByLogin(HeaderHelper.SysHeader sysHeader, Errors errors) {
        checkNotNull(HeaderHelper.TOKEN, sysHeader.getToken(), errors);
        checkNotNull(HeaderHelper.OPERATOR_OID, sysHeader.getOid(), errors);
        checkNotNull(HeaderHelper.OPERATOR_ID, sysHeader.getUserId(), errors);
        checkNotNull(HeaderHelper.OPERATOR_TYPE, sysHeader.getType(), errors);
        checkNotNull(HeaderHelper.OPERATOR_NAME, sysHeader.getUserName(), errors);
        checkNotNull(HeaderHelper.OPERATOR_ORG_NAME, sysHeader.getOrgName(), errors);
    }

    private static <T> void checkNotNull(String message, T reference, Errors errors) {
        try {
            Preconditions.checkNotNull(reference);
        } catch (NullPointerException e) {
            throw new ApiException(
                    "请求头信息参数有错, " + message + " 为空",
                    new Result<>(errors, Global.Errors.ERROR_HEADER, HttpStatus.OK.value()));
        }
    }
}
