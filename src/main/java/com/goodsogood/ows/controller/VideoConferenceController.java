package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.github.pagehelper.Page;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.VideoConferenceEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.VideoConferenceForm;
import com.goodsogood.ows.model.vo.VideoConferenceVo;
import com.goodsogood.ows.service.VideoConferenceService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 *  视频会议
 *
 */
@RestController
@RequestMapping("/videoConference")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class VideoConferenceController {

    private final Errors errors;
    private final VideoConferenceService videoConferenceService;

    @Autowired
    public VideoConferenceController(Errors errors, VideoConferenceService videoConferenceService) {
        this.errors = errors;
        this.videoConferenceService = videoConferenceService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "查询视频会议列表")
    @PostMapping("/list")
    public ResponseEntity<Result<Page<VideoConferenceEntity>>> list(@RequestHeader HttpHeaders headers,
                                                                    @RequestBody VideoConferenceForm videoConferenceForm) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Page<VideoConferenceEntity>> result = new Result<>(videoConferenceService.list(sysHeader.getRegionId(), videoConferenceForm), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "查询视频会议详情")
    @GetMapping("/info")
    public ResponseEntity<Result<VideoConferenceVo>> info(@RequestHeader HttpHeaders headers,
                                                                    @RequestParam(value = "conferenceId") Long conferenceId) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<VideoConferenceVo> result = new Result<>(videoConferenceService.info(conferenceId), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "加入视频会议")
    @GetMapping("/join")
    @RepeatedCheck
    public ResponseEntity<Result<VideoConferenceEntity>> join(@RequestHeader HttpHeaders headers,
                                                                    @RequestParam(value = "roomCode") String roomCode) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<VideoConferenceEntity> result = new Result<>(videoConferenceService.join(sysHeader.getRegionId(), roomCode), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "创建视频会议")
    @PostMapping("/addVideoConference")
    @RepeatedCheck
    public ResponseEntity<Result<Integer>> addVideoConference(@RequestHeader HttpHeaders headers,
                                         @RequestBody VideoConferenceVo videoConferenceVo) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(videoConferenceService.addVideoConference(sysHeader, videoConferenceVo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "创建预约会议")
    @PostMapping("/addScheduleConferences")
    @RepeatedCheck
    public ResponseEntity<Result<Integer>> addScheduleConferences(@RequestHeader HttpHeaders headers,
                                               @RequestBody VideoConferenceVo videoConferenceVo) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(videoConferenceService.addScheduleConferences(sysHeader, videoConferenceVo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "修改预约会议（预约会议用）")
    @PostMapping("/editScheduleConferences")
    @RepeatedCheck
    public ResponseEntity<Result<Integer>> editScheduleConferences(@RequestHeader HttpHeaders headers,
                                                                  @RequestBody VideoConferenceVo videoConferenceVo) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(videoConferenceService.editScheduleConferences(sysHeader, videoConferenceVo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "取消预约会议（预约会议用）")
    @PostMapping("/cancelScheduleConferences")
    @RepeatedCheck
    public ResponseEntity<Result<Integer>> cancelScheduleConferences(@RequestHeader HttpHeaders headers,
                                                                   @RequestBody VideoConferenceVo videoConferenceVo) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(videoConferenceService.cancelScheduleConferences(sysHeader, videoConferenceVo, errors), HttpStatus.OK);
    }
}
