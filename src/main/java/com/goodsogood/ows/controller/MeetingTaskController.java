package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.MeetingTaskEntity;
import com.goodsogood.ows.model.vo.MeetingTypeTaskListForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.TaskQueryForm;
import com.goodsogood.ows.model.vo.TaskQueryVo;
import com.goodsogood.ows.service.MeetingTaskService;
import com.goodsogood.ows.service.UserCenterService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.PageUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 活动任务
 *
 * <AUTHOR>
 * @create 2018-10-25 10:53
 **/
@RestController
@RequestMapping("/meeting-task")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class MeetingTaskController {

    private final Errors errors;
    private final MeetingTaskService meetingTaskService;
    private final UserCenterService userService;

    @Autowired
    public MeetingTaskController(Errors errors, MeetingTaskService meetingTaskService, UserCenterService userService) {
        this.errors = errors;
        this.meetingTaskService = meetingTaskService;
        this.userService = userService;
    }

    @HttpMonitorLogger
    @ApiOperation(value = "通过活动组织生活指定给本组织的所有活动任务（page）")
    @GetMapping("/list")
    public ResponseEntity<Result<Page<MeetingTaskEntity>>> list(@RequestHeader HttpHeaders headers,
                                                                @ApiParam("组织生活id")
                                                                @RequestParam(value = "plan_id", required = false) Long planId,
                                                                @ApiParam("组织生活名称")
                                                                @Length(max = 50, message = "{Length.meetingPlan.name}")
                                                                @RequestParam(value = "plan_name", required = false) String planName,
                                                                @ApiParam("来源组织id")
                                                                @RequestParam(value = "org_id", required = false) Long pOrgId,
                                                                @ApiParam("类型id")
                                                                @RequestParam(value = "type_ids", required = false) List<Long> typeIds,
                                                                @ApiParam("类别id")
                                                                @RequestParam(value = "category_id", required = false) Long categoryId,
                                                                @ApiParam("完成情况:1：未完成；2：完成；3：逾期")
                                                                @Range(min = 1, max = 3, message = "{Length.meetingPlan.status}")
                                                                @RequestParam(value = "status", required = false) Short status,
                                                                @ApiParam("类型名称")
                                                                @RequestParam(value = "type", required = false) String type,
                                                                @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                @ApiParam("任务开始起始时间。yyyy-MM-dd")
                                                                @RequestParam(value = "s_start_time", required = false) Date sStartTime,
                                                                @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                @ApiParam("任务开始结束时间。yyyy-MM-dd")
                                                                @RequestParam(value = "e_start_time", required = false) Date eStartTime,
                                                                @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                @ApiParam("任务结束起始时间。yyyy-MM-dd")
                                                                @RequestParam(value = "s_end_time", required = false) Date sEndTime,
                                                                @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                @ApiParam("任务结束截止时间。yyyy-MM-dd")
                                                                @RequestParam(value = "e_end_time", required = false) Date eEndTime,
                                                                @ApiParam("页码，默认为1")
                                                                @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                                @ApiParam("每页行数")
                                                                @RequestParam(value = "page_size", required = false) Integer pageSize
    ) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        // chenanshun 2018年11月6日 14:09:29
        MeetingTypeTaskListForm meetingTypeListForm = this.getMeetingTypeListForm(planId, planName, pOrgId, sysHeader.getOid(), null, typeIds, categoryId, status, type, sStartTime, eStartTime, sEndTime, eEndTime);
        meetingTypeListForm.setPageBean(PageUtils.page(pageNo,pageSize));
        Page<MeetingTaskEntity> meetingTaskEntities = meetingTaskService.typeListPage(meetingTypeListForm);
        this.convertStatus(meetingTaskEntities);
        Result<Page<MeetingTaskEntity>> result = new Result<>(meetingTaskEntities, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    // 2018年11月21日 16:51:23 chenanshun
    @HttpMonitorLogger
    @ApiOperation(value = "通过活动组织生活指定给本组织的所有活动任务（all）,移动端使用")
    @GetMapping("/list-all")
    public ResponseEntity<Result<List<MeetingTaskEntity>>> listAll(@RequestHeader HttpHeaders headers,
                                                                   @ApiParam("组织生活id")
                                                                   @RequestParam(value = "plan_id", required = false) Long planId,
                                                                   @ApiParam("组织生活名称")
                                                                   @Length(max = 50, message = "{Length.meetingPlan.name}")
                                                                   @RequestParam(value = "plan_name", required = false) String planName,
                                                                   @ApiParam("来源组织id")
                                                                   @RequestParam(value = "org_id", required = false) Long pOrgId,
                                                                   @ApiParam("类型id")
                                                                   @RequestParam(value = "type_ids", required = false) List<Long> typeIds,
                                                                   @ApiParam("类别id")
                                                                   @RequestParam(value = "category_id", required = false) Long categoryId,
                                                                   @ApiParam("完成情况:1：未完成；2：完成；3：逾期")
                                                                   @Range(min = 1, max = 3, message = "{Length.meetingPlan.status}")
                                                                   @RequestParam(value = "status", required = false) Short status,
                                                                   @ApiParam("类型名称")
                                                                   @RequestParam(value = "type", required = false) String type,
                                                                   @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                   @ApiParam("任务开始起始时间。yyyy-MM-dd")
                                                                   @RequestParam(value = "s_start_time", required = false) Date sStartTime,
                                                                   @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                   @ApiParam("任务开始结束时间。yyyy-MM-dd")
                                                                   @RequestParam(value = "e_start_time", required = false) Date eStartTime,
                                                                   @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                   @ApiParam("任务结束起始时间。yyyy-MM-dd")
                                                                   @RequestParam(value = "s_end_time", required = false) Date sEndTime,
                                                                   @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                   @ApiParam("任务结束截止时间。yyyy-MM-dd")
                                                                   @RequestParam(value = "e_end_time", required = false) Date eEndTime) {
        /*
         *  1.组织id不能为null
         *  2.用户id不能为null
         */
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingTypeTaskListForm meetingTypeListForm = this.getMeetingTypeListForm(planId, planName, pOrgId, null, userService.getUserOidByH5(headers, sysHeader), typeIds, categoryId, status, type, sStartTime, eStartTime, sEndTime, eEndTime);
        meetingTypeListForm.setIsH5((short) 1);
        List<MeetingTaskEntity> meetingTaskEntities = meetingTaskService.typeList(meetingTypeListForm);
        this.convertStatus(meetingTaskEntities);
        Result<List<MeetingTaskEntity>> result = new Result<>(meetingTaskEntities, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "根据类型编号、年份和组织编号获取活动开展情况")
    @PostMapping("/find/task/info")
    public ResponseEntity<Result<List<TaskQueryVo>>> findTaskInfo(@RequestHeader HttpHeaders headers,@RequestBody TaskQueryForm form){
        List<TaskQueryVo> result =meetingTaskService.findByTypeIdOrgIdQueryDate(form.getTypeId(),form.getOrgIdList(),form.getQueryYear());
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 状态转换
     */
    private void convertStatus(List<MeetingTaskEntity> meetingTaskEntities) {
        meetingTaskEntities.forEach(meetingTaskEntity -> {
            short s = meetingTaskEntity.getStatusForm();
            meetingTaskEntity.setStatus(s);
            if (s == 1) {
                meetingTaskEntity.setStatusRemark("未完成");
            } else if (s == 2) {
                meetingTaskEntity.setStatusRemark("已完成");
            }else if (s == 3) {
                meetingTaskEntity.setStatusRemark("已逾期");
            }
        });
    }

    /**
     * 活动类型查询参数
     */
    // chenanshun 2018年11月6日 14:09:29
    private MeetingTypeTaskListForm getMeetingTypeListForm(Long planId,
                                                           String planName,
                                                           Long pOrgId,
                                                           Long orgId,
                                                           List<Long> orgIds,
                                                           List<Long> typeIds,
                                                           Long categoryId,
                                                           Short status,
                                                           String type,
                                                           Date sStartTime,
                                                           Date eStartTime,
                                                           Date sEndTime,
                                                           Date eEndTime) {
        MeetingTypeTaskListForm meetingTypeListForm = new MeetingTypeTaskListForm();
        if (StringUtils.isNotBlank(planName)) {
            meetingTypeListForm.setPlanName(planName.trim());
        }
        meetingTypeListForm.setPlanId(planId);
        meetingTypeListForm.setPOrgId(pOrgId);
        meetingTypeListForm.setOrgId(orgId);
        meetingTypeListForm.setOrgIds(orgIds);
        meetingTypeListForm.setTypeIds(typeIds);
        meetingTypeListForm.setCategoryId(categoryId);
        meetingTypeListForm.setStatus(status);
        if (StringUtils.isNotBlank(type)) {
            meetingTypeListForm.setType(type.trim());
        }
        meetingTypeListForm.setSStartTime(sStartTime);
        meetingTypeListForm.setEStartTime(DateUtils.toLastSecondOfDay(eStartTime));
        meetingTypeListForm.setSEndTime(sEndTime);
        meetingTypeListForm.setEEndTime(DateUtils.toLastSecondOfDay(eEndTime));
        return meetingTypeListForm;
    }
}
