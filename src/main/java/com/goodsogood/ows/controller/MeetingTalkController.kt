package com.goodsogood.ows.controller

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.CountMeetingTalkVo
import com.goodsogood.ows.model.vo.GeneraLeaderAndUserTalkForm
import com.goodsogood.ows.model.vo.MeetingTalkForm
import com.goodsogood.ows.model.vo.MeetingTalkVO
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.MeetingTalkService
import com.goodsogood.ows.service.TobaccoTaskListService
import com.goodsogood.ows.utils.DateUtils
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@RequestMapping("/meeting-talk")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Validated
class MeetingTalkController(
    @Autowired val errors: Errors,
    @Autowired val meetingTalkService: MeetingTalkService,
    private val tobaccoTaskListService: TobaccoTaskListService
) {

    @HttpMonitorLogger
    @PostMapping("/edit")
    @ApiOperation("新增谈心谈话")
    fun editMeetingTalk(
        @Valid @RequestBody form: MeetingTalkForm, @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.meetingTalkService.editMeetingTalk(form, headers), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/find/{talk_id}")
    @ApiOperation("查询单个谈心谈话")
    fun findMeetingTalk(
        @PathVariable("talk_id") talkId: Long, @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.meetingTalkService.queryMeetingTalk(talkId), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/del/{talk_id}")
    @ApiOperation("删除谈心谈话")
    fun delMeetingTalk(
        @PathVariable("talk_id") talkId: Long, @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                meetingTalkService.delMeetingTalk(talkId, header), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/batch-del")
    @ApiOperation("删除谈心谈话")
    fun batchDelMeetingTalk(
        @RequestBody form: BatchMeetingTalkForm, @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                meetingTalkService.batchDelMeetingTalk(form.talkIds, header), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/select-by-type")
    @ApiOperation("根据类型查询谈心谈话")
    fun queryMeetingTalkList(
        @RequestParam("talk_type", required = false, defaultValue = "-1") talkType: Int,
        @RequestParam("org_id") orgId: Long,
        @RequestParam(value = "source", required = false, defaultValue = "-1") source: Int,
        @RequestParam("source_id", required = false, defaultValue = "-1") sourceId: Long?,
        @RequestParam("talk_user", required = false) talkUser: String?,
        @RequestParam("to_talk_user", required = false) toTalkUser: String?,
        @RequestParam("talk_time", required = false) talkTime: String?,
        @RequestParam("end_time", required = false) endTime: String?,
        @RequestParam("page", required = false, defaultValue = "1") page: Int,
        @RequestParam("page_size", required = false, defaultValue = "20") pageSize: Int,
        @RequestParam("show_null_time", required = false, defaultValue = "1") showNullTime:Int
    ): ResponseEntity<Result<Any>> {
        val start = if (talkTime != null) DateUtils.stringToLocalDateTime("$talkTime 00:00:00", "yyyy-MM-dd HH:mm:ss")
        else DateUtils.stringToLocalDateTime("2000-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss")
        val end = if (endTime != null) DateUtils.stringToLocalDateTime("$endTime 23:59:59", "yyyy-MM-dd HH:mm:ss")
        else DateUtils.stringToLocalDateTime("9999-12-31 00:00:00", "yyyy-MM-dd HH:mm:ss")
        val list = meetingTalkService.queryList(orgId, talkType, source, sourceId, talkUser, toTalkUser, start, end ,showNullTime)
        val total = list.size
        val resulList : MutableList<MeetingTalkVO> = if((page*pageSize)<total)list.subList((page-1)*pageSize,page*pageSize)else list.subList((page-1)*pageSize,total)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                resulList, errors
            ).also {
                   it.pageNum = page
                    it.pageSize = pageSize
                    it.total = list.size.toLong()
            }, HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/genera-leader-talk")
    @ApiOperation("生成领导与之谈话记录")
    fun generaLeaderTalk(
        @Valid @RequestBody form: GeneraLeaderAndUserTalkForm, @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                meetingTalkService.generaLeaderTalk(form, headers), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/countMeetingTalk")
    @ApiOperation("谈心谈话统计")
    fun countMeetingTalkByCondition(
        @RequestHeader headers: HttpHeaders,
        @RequestParam("unit_name", required = false) unitName: String?,
        @RequestParam("unit_id", required = true) unitId : Long
    ): ResponseEntity<Result<Any>> {
        val listResult: Collection<CountMeetingTalkVo> =
            meetingTalkService.countMeetingTalkByCondition(unitName, headers,unitId)
        return if (listResult.isEmpty()) {
            ResponseEntity(Result(listOf<CountMeetingTalkVo>(), errors), HttpStatus.OK)
        } else ResponseEntity(Result(listResult, errors), HttpStatus.OK)
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class BatchMeetingTalkForm {

    @ApiModelProperty("谈心谈话主键ID列表")
    @JsonProperty(value = "talk_ids")
    var talkIds: MutableList<Long> = mutableListOf()

}


