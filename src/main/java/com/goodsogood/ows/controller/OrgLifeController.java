package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.service.MeetingPeoplePartyLifeService;
import com.goodsogood.ows.service.OpenService;
import com.goodsogood.ows.service.OrgLifeService;
import com.goodsogood.ows.service.UserCenterService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.PageUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 民主生活会
 */

@Controller
@RequestMapping("/org_life")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class OrgLifeController {

    private final OrgLifeService lifeService;
    private final Errors errors;
    private final MeetingPeoplePartyLifeService partyLifeService;
    private final UserCenterService userCenterService;
    private final OpenService openService;

    public OrgLifeController(OrgLifeService lifeService, Errors errors,
                             MeetingPeoplePartyLifeService partyLifeService,
                             UserCenterService userCenterService, OpenService openService) {
        this.lifeService = lifeService;
        this.errors = errors;
        this.partyLifeService = partyLifeService;
        this.userCenterService = userCenterService;
        this.openService = openService;
    }

    @ApiOperation("添加/编辑 民主生活会")
    @HttpMonitorLogger
    @PostMapping("/add_edit")
    public ResponseEntity<Result<?>> addOrEdit(@RequestHeader HttpHeaders headers,
                                               @Valid @RequestBody OrgLifeAddEditForm form, BindingResult bindingResult) {
        return new ResponseEntity<>(new Result<>(lifeService.addOrEdit(form, headers), errors), HttpStatus.OK);
    }

    @ApiOperation("删除 民主生活会")
    @HttpMonitorLogger
    @GetMapping("/del")
    public ResponseEntity<Result<?>> del(@RequestHeader HttpHeaders headers, @RequestParam("life_id") Long lifeId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(lifeService.delAll(headers, lifeId), errors), HttpStatus.OK);
    }

    @ApiOperation("标签管理")
    @HttpMonitorLogger
    @PostMapping("/tag")
    public ResponseEntity<Result<?>> tag(@RequestHeader HttpHeaders headers,
                                         @Valid @RequestBody OrgLifeTagManageForm form, BindingResult bindingResult) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(lifeService.tagMange(header, form), errors), HttpStatus.OK);
    }

    @ApiOperation("查询 民主生活会")
    @HttpMonitorLogger
    @GetMapping("/list")
    public ResponseEntity<Result<?>> list(@RequestHeader HttpHeaders headers,
                                          @RequestParam(value = "title", required = false) String title,
                                          @RequestParam(value = "org_id", required = false) Long orgId,
                                          @RequestParam(value = "tag", required = false) List<Long> tagId,
                                          @RequestParam(value = "years", required = false) Integer years,
                                          @RequestParam(value = "status", required = false) List<Integer> status,
                                          @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                          @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                          @RequestParam(value = "flag", required = false, defaultValue = "0") @Range(min = 0, max = 1, message = "{Range.life.list.flag}") Integer flag) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        OrgLifeFindForm findForm = new OrgLifeFindForm();
        findForm.setRegionId(header.getRegionId());
        findForm.setTitle(title);
        if (ObjectUtils.isEmpty(orgId)) {
            findForm.setOrgId(header.getOid());
            if (flag == 1) { //查询下级组织
                List<Long> orgIds = new ArrayList<Long>() {{
                    add(header.getOid());
                }};
                orgIds.addAll(userCenterService.findAllChildOrg(header, header.getOid(), 1).stream().map(OrganizationBase::getOrgId).collect(Collectors.toList()));
                findForm.setOrgIds(orgIds);
            }
        } else {
            findForm.setOrgId(orgId);
        }
        findForm.setTag(tagId);
        findForm.setYears(years);
        findForm.setStatus(status);
        return new ResponseEntity<>(new Result<>(lifeService.list(findForm, page, pageSize), errors), HttpStatus.OK);
    }

    @ApiOperation("标题年度查询")
    @HttpMonitorLogger
    @GetMapping("/title")
    public ResponseEntity<Result<?>> title(@RequestParam("life_id") Long lifeId) {
        return new ResponseEntity<>(new Result<>(lifeService.lifeTitle(lifeId), errors), HttpStatus.OK);
    }

    @ApiOperation("结束会议")
    @HttpMonitorLogger
    @GetMapping("/finish")
    public ResponseEntity<Result<?>> finish(@RequestHeader HttpHeaders headers,
                                            @RequestParam("life_id") Long lifeId) {
        return new ResponseEntity<>(new Result<>(lifeService.finish(lifeId, headers), errors), HttpStatus.OK);
    }

    @ApiOperation("文件相关回显查询")
    @HttpMonitorLogger
    @GetMapping("/only_file")
    public ResponseEntity<Result<?>> onlyFile(@RequestParam("life_id") Long lifeId,
                                              @RequestParam("step") @Range(min = 1, max = 2, message = "{Range.life.step}") Integer step,
                                              @RequestParam("model_type") List<Integer> modelType) {
        return new ResponseEntity<>(new Result<>(lifeService.OnlyLifeFileFind(lifeId, step, modelType), errors), HttpStatus.OK);
    }

    @ApiOperation("相互检察查询")
    @HttpMonitorLogger
    @GetMapping("/check")
    public ResponseEntity<Result<?>> checkSelf(@RequestParam("life_id") Long lifeId,
                                               @RequestParam("step") @Range(min = 1, max = 2, message = "{Range.life.step}") Integer step) {
        return new ResponseEntity<>(new Result<>(lifeService.check(lifeId, step), errors), HttpStatus.OK);
    }

    @ApiOperation("征求意见查询")
    @HttpMonitorLogger
    @GetMapping("/advice")
    public ResponseEntity<Result<?>> advice(@RequestParam("life_id") Long lifeId,
                                            @RequestParam("step") @Range(min = 1, max = 2, message = "{Range.life.step}") Integer step) {
        return new ResponseEntity<>(new Result<>(lifeService.advice(lifeId, step), errors), HttpStatus.OK);
    }

//    @ApiOperation("谈心谈话查询")
//    @HttpMonitorLogger
//    @GetMapping("/talk")
//    public ResponseEntity<Result<?>> talk(@RequestParam("life_id") Long lifeId,
//                                            @RequestParam("talk_type") @Range(min = 1,max = 4,message = "{Range.life.talkType}") Integer talkType,
//                                            @RequestParam("step") @Range(min = 1,max = 2,message = "{Range.life.step}") Integer step){
//        return new ResponseEntity<>(new Result<>(lifeService.talk(lifeId,talkType,step),errors), HttpStatus.OK);
//    }


    @ApiOperation("会中详情查询")
    @HttpMonitorLogger
    @GetMapping("/in")
    public ResponseEntity<Result<?>> inTheMeeting(@RequestParam("life_id") Long lifeId) {
        return new ResponseEntity<>(new Result<>(lifeService.inTheMeeting(lifeId), errors), HttpStatus.OK);
    }

//    @ApiOperation("会后修改会议材料")
//    @HttpMonitorLogger
//    @PostMapping("/after_edit")
//    public ResponseEntity<Result<?>> afterEdit(@RequestHeader HttpHeaders headers,
//                                               @Valid @RequestBody LifeEditDataForm form, BindingResult bindingResult){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        return new ResponseEntity<>(new Result<>(lifeService.editData(form,header),errors), HttpStatus.OK);
//    }

//    @ApiOperation("问卷调查关联上民主生活会")
//    @HttpMonitorLogger
//    @GetMapping("/join_life")
//    public ResponseEntity<Result<?>> joinLifeByActivity(@RequestParam("life_id") Long lifeId,
//                                                        @RequestParam("data_id") List<Long> dataId,
//                                                        @RequestParam("model_id") Integer modelId,
//                                                        @RequestParam("step") @Range(min = 1, max = 2) Integer step,
//                                                        @RequestHeader HttpHeaders headers) {
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        partyLifeService.joinToLife(header, lifeId, dataId, modelId, step);
//        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
//    }

    @RepeatedCheck
    @ApiOperation("删除剖析/征求意见")
    @HttpMonitorLogger
    @GetMapping("/delete_special")
    public ResponseEntity<Result<Boolean>> deleteSpecial(@RequestHeader HttpHeaders headers,
                                                         @RequestParam("life_id") Long lifeId, @RequestParam("step") Integer step,
                                                         @RequestParam("type") Integer type, @RequestParam(value = "data_ids", required = true) List<Long> dataIds,
                                                         @RequestParam(value = "advice_type", required = false) Integer adviceType) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        lifeService.deleteSpecial(header, headers, lifeId, step, type, dataIds, adviceType);
        return new ResponseEntity<>(new Result(true, errors), HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("保存检视剖析数据到t_meeting_check")
    @HttpMonitorLogger
    @PostMapping("/save_check")
    public ResponseEntity<Result<Boolean>> saveCheck(@RequestHeader HttpHeaders headers,
                                                     @RequestParam("life_id") Long lifeId, @RequestParam("step") Integer step,
                                                     @RequestBody List<LeaderForm> leaders
    ) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        lifeService.saveCheck(header, lifeId, step, leaders);
        return new ResponseEntity<>(new Result(true, errors), HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("修改民主生活会状态")
    @HttpMonitorLogger
    @GetMapping("/change")
    public ResponseEntity<Result<Boolean>> changeStatus(@RequestHeader HttpHeaders headers,
                                                        @RequestParam("life_id") Long lifeId,
                                                        @RequestParam("status") Integer status,
                                                        @RequestParam("step") @Range(min = 1, max = 2, message = "{Range.life.step}") Integer step) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        lifeService.changeStatus(lifeId, status, step, header);
        return new ResponseEntity<>(new Result("success", errors), HttpStatus.OK);
    }

    @RepeatedCheck
    @ApiOperation("关联民主评议")
    @HttpMonitorLogger
    @GetMapping("/link_comment")
    public ResponseEntity<Result<?>> linkComment(@RequestHeader HttpHeaders headers,
                                                 @RequestParam("life_id") Long lifeId,
                                                 @RequestParam("comment_id") Long commentId,
                                                 @RequestParam("flag") @Range(min = 1, max = 2, message = "{Range.org.life.comment.flag}") Integer flag) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(lifeService.linkComment(header, lifeId, commentId, flag), errors), HttpStatus.OK);
    }


    //关联活动
    @RepeatedCheck
    @ApiOperation("关联活动")
    @HttpMonitorLogger
    @GetMapping("/relate-meeting")
    public ResponseEntity<Result<Boolean>> relateMeeting(@RequestHeader HttpHeaders headers,
                                                         @RequestParam("life_id") Long lifeId, @RequestParam("meeting_ids") List<Long> meetingIds) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        lifeService.relateMeeting(headers, lifeId, meetingIds, 1);
        return new ResponseEntity<Result<Boolean>>(new Result<>(true, errors), HttpStatus.OK);
    }


    //取消关联活动
    @RepeatedCheck
    @ApiOperation("取消关联活动")
    @HttpMonitorLogger
    @GetMapping("/quit-relate-meeting")
    public ResponseEntity<Result<Boolean>> quitRelateMeeting(@RequestHeader HttpHeaders headers,
                                                             @RequestParam("life_id") Long lifeId, @RequestParam("life_study_ids") List<Long> lifeStudyIds) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        lifeService.quitRelateMeeting(lifeId, lifeStudyIds);
        return new ResponseEntity<Result<Boolean>>(new Result<>(true, errors), HttpStatus.OK);
    }

    //查询组织学习-meeting列表过滤掉已关联的
    @RepeatedCheck
    @ApiOperation("查询组织学习-meeting列表")
    @HttpMonitorLogger
    @GetMapping("/query-meeting-list")
    public ResponseEntity<Result<Page<MeetingEntity>>> queryMeetingList(@RequestHeader HttpHeaders headers,
                                                                        @RequestParam(value = "name", required = false) String name,
                                                                        @RequestParam(value = "agenda", required = false) String agenda,
                                                                        @RequestParam(value = "tag_ids", required = false) List<Long> tagIds,
                                                                        @RequestParam(value = "status", required = false) Short status,
                                                                        @RequestParam(value = "type_ids", required = false) List<Long> typeIds,
                                                                        @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "start_time", required = false) Date startTime,
                                                                        @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "end_time", required = false) Date endTime,
                                                                        @RequestParam(value = "page_no", required = false) Integer pageNo,
                                                                        @RequestParam(value = "page_size", required = false) Integer pageSize,
                                                                        @RequestParam(value = "record_type", required = false, defaultValue = "0") Integer recordType,
                                                                        @RequestParam(value = "life_id") Long lifeId,
                                                                        @RequestParam(value = "model_id", required = false) Long modelId,
                                                                        @RequestParam(value = "step", required = false) @Range(min = 1, max = 2, message = "step超出查询范围") Integer step,
                                                                        @RequestParam(value = "source_type", required = false) @Range(min = 1, max = 2) Integer sourceType,
                                                                        @RequestParam(value = "relate_type", required = false) Integer relateType) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingListForm meetingListForm = getMeetingListForm(headers, name, agenda, tagIds, typeIds, status, startTime, endTime, pageNo, pageSize, (short) 0,relateType);
        // 调用用户中心查询当前组织及下級组织的数据
        List<Long> orgIds = openService.getOrgInfoList(sysHeader.getOid(),1).stream().map(OrgInfoForm::getOrgId).collect(Collectors.toList());
        meetingListForm.setQOrgIds(orgIds);
        Page<MeetingEntity> page = lifeService.queryMeetingList(lifeId, meetingListForm);
        return new ResponseEntity<Result<Page<MeetingEntity>>>(new Result<>(page, errors), HttpStatus.OK);


    }


    /**
     * 组装查询参数
     */
    private MeetingListForm getMeetingListForm(HttpHeaders headers,
                                               String name,
                                               String agenda,
                                               List<Long> tagIds,
                                               List<Long> typeIds,
                                               Short status,
                                               Date startTime,
                                               Date endTime,
                                               Integer pageNo,
                                               Integer pageSize,
                                               Short isH5,
                                               Integer relateType) {
        MeetingListForm meetingListForm = new MeetingListForm();
        meetingListForm.setTypeIds(typeIds);
        meetingListForm.setTagIds(tagIds);
        meetingListForm.setRelateType(relateType);
        if (StringUtils.isNotBlank(name)) {
            meetingListForm.setName(name.trim());
        }
        if (StringUtils.isNotBlank(agenda)) {
            meetingListForm.setAgenda(agenda.trim());
        }
        meetingListForm.setEndTime(DateUtils.toLastSecondOfDay(endTime));
        meetingListForm.setStatus(status);
        meetingListForm.setStartTime(startTime);
        meetingListForm.setIsH5(isH5);
        meetingListForm.setPageBean(PageUtils.page(pageNo, pageSize));
        return meetingListForm;
    }

    @GetMapping("/test-del-task")
    @ResponseBody
    public void testDelTask(@RequestHeader HttpHeaders headers,@RequestParam("life_id") Long lifeId){
        lifeService.delTask(headers,lifeId);
    }
}
