package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.StatusEnum;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.MeetingOrgDebriefReviewScheduler;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.MeetingPlanEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.MeetingOrgDebriefReviewService;
import com.goodsogood.ows.validate.group.Add;
import com.goodsogood.ows.validate.group.Delete;
import com.goodsogood.ows.validate.group.Edit;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @describe 组织述职评议入口
 * @date 2019-12-30
 */
@RestController
@RequestMapping("/org/debrief/review")
@Log4j2
@Validated
public class MeetingOrgDebriefReviewController {

    private final Errors errors;

    private final MeetingOrgDebriefReviewService meetingOrgDebriefReviewService;

    private final MeetingOrgDebriefReviewScheduler meetingOrgDebriefReviewScheduler;

    @Autowired
    public MeetingOrgDebriefReviewController(Errors errors, MeetingOrgDebriefReviewService meetingOrgDebriefReviewService, MeetingOrgDebriefReviewScheduler meetingOrgDebriefReviewScheduler) {
        this.errors = errors;
        this.meetingOrgDebriefReviewService = meetingOrgDebriefReviewService;
        this.meetingOrgDebriefReviewScheduler = meetingOrgDebriefReviewScheduler;
    }


    @HttpMonitorLogger
    @ApiOperation(value = "添加组织述职")
    @PostMapping("/add")
    @RepeatedCheck
    public ResponseEntity<Result> add(@RequestHeader HttpHeaders headers,
                                      @Validated(value = Add.class) @RequestBody MeetingOrgDebriefReviewForm meetingOrgDebriefReviewForm) {

        meetingOrgDebriefReviewService.add(HeaderHelper.buildMyHeader(headers), meetingOrgDebriefReviewForm);
        Result<MeetingPlanEntity> result = new Result<>(null, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "修改组织述职评议信息")
    @PostMapping("/edit")
    public ResponseEntity<Result> edit(@RequestHeader HttpHeaders headers,
                                       @Validated(value = Edit.class) @RequestBody MeetingOrgDebriefReviewForm meetingOrgDebriefReviewForm) {


        meetingOrgDebriefReviewService.edit(HeaderHelper.buildMyHeader(headers), meetingOrgDebriefReviewForm);
        Result<MeetingPlanEntity> result = new Result<>(null, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "查询组织述职评议详情")
    @PostMapping("/detail")
    public ResponseEntity<Result<MeetingOrgDebriefReviewDetailVO>> detail(@Valid @RequestBody MeetingOrgDebriefReviewForm meetingOrgDebriefReviewForm) {
        MeetingOrgDebriefReviewDetailVO vo = meetingOrgDebriefReviewService.detail(meetingOrgDebriefReviewForm);
        Result<MeetingOrgDebriefReviewDetailVO> result = new Result<>(vo, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "删除组织述职评议")
    @PostMapping("/delete")
    public ResponseEntity<Result> delete(@RequestHeader HttpHeaders headers,
                                         @Validated(value = Delete.class) @RequestBody MeetingOrgDebriefReviewForm meetingOrgDebriefReviewForm) {
        meetingOrgDebriefReviewService.delete(HeaderHelper.buildMyHeader(headers), meetingOrgDebriefReviewForm);
        Result<MeetingPlanEntity> result = new Result<>(null, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "查询组织述职评议信息列表")
    @PostMapping("/list")
    public ResponseEntity<Result<Page<MeetingOrgDebriefReviewQueryVO>>> list(@RequestHeader HttpHeaders headers, @Valid @RequestBody MeetingOrgDebriefReviewQueryForm dto) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Page<MeetingOrgDebriefReviewQueryVO>> result = new Result<>(meetingOrgDebriefReviewService.list(sysHeader, dto), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "基层组织述职评议导出")
    @GetMapping("/excel/list")
    public ResponseEntity<Result<?>> excelList(@RequestHeader HttpHeaders headers,
                                               @RequestParam(value = "review_year") Integer reviewYear,
                                               @RequestParam(value = "org_id") Long orgId,
                                               @RequestParam(value = "org_name", required = false) String orgName,
                                               @RequestParam(value = "rating", required = false) Integer rating,
                                               HttpServletResponse response) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingOrgDebriefReviewQueryForm dto = new MeetingOrgDebriefReviewQueryForm(orgName, rating, reviewYear, orgId,
                null, null, StatusEnum.NORMAL.getStatus());
        boolean success = this.meetingOrgDebriefReviewService.excelList(sysHeader, dto, response);
        Result<Boolean> result = new Result<>(success, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "基层述职评议结果统计查询")
    @PostMapping("/statistics/list")
    public ResponseEntity<Result<Page<MeetingOrgDebriefReviewStatisticsQueryVO>>> statisticsList(
            @RequestHeader HttpHeaders headers, @Valid @RequestBody MeetingOrgDebriefReviewStatisticsQueryForm dto) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        Result<Page<MeetingOrgDebriefReviewStatisticsQueryVO>> result = new Result<>(meetingOrgDebriefReviewService.statisticsList(sysHeader, dto), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "基层组织述职评议导出")
    @GetMapping("/statistics/excel/list")
    public ResponseEntity<Result<?>> statisticsExcelList(
            @RequestHeader HttpHeaders headers, @RequestParam(value = "review_year") Integer reviewYear,
            @RequestParam(value = "org_id") Long orgId,
            @RequestParam(value = "org_name", required = false) String orgName,
            HttpServletResponse response) {
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        MeetingOrgDebriefReviewStatisticsQueryForm dto = new MeetingOrgDebriefReviewStatisticsQueryForm(orgName, reviewYear, orgId);
        boolean success = this.meetingOrgDebriefReviewService.statisticsExcelList(sysHeader, dto, response);
        Result<Boolean> result = new Result<>(success, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "定时器执行")
    @GetMapping("/statistics/list/test")
    public void statisticsList() {
        meetingOrgDebriefReviewScheduler.executeScheduled();
    }
}
