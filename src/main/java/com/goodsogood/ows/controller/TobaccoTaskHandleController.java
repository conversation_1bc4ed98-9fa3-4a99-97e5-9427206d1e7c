package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.TobaccoTaskHandleForm;
import com.goodsogood.ows.service.TobaccoTaskHandleService;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/tobacco/handle")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class TobaccoTaskHandleController {

    private final Errors errors;
    private final TobaccoTaskHandleService tobaccoTaskHandleService;

    @Autowired
    public TobaccoTaskHandleController(Errors errors, TobaccoTaskHandleService tobaccoTaskHandleService) {
        this.errors = errors;
        this.tobaccoTaskHandleService = tobaccoTaskHandleService;
    }

    @ApiModelProperty("保存任务填报草稿")
    @HttpMonitorLogger
    @PostMapping("/fill_save")
    public ResponseEntity<Result<?>> fillSave(@RequestHeader HttpHeaders headers, @RequestBody TobaccoTaskHandleForm form){
        return new ResponseEntity<>(new Result<>(tobaccoTaskHandleService.fillTaskDraft(HeaderHelper.buildMyHeader(headers),form),errors), HttpStatus.OK);
    }

    @ApiModelProperty("提交任务填报")
    @HttpMonitorLogger
    @PostMapping("/fill_submit")
    public ResponseEntity<Result<?>> fill(@RequestHeader HttpHeaders headers, @RequestBody TobaccoTaskHandleForm form){
        return new ResponseEntity<>(new Result<>(tobaccoTaskHandleService.fillTask(HeaderHelper.buildMyHeader(headers),form),errors), HttpStatus.OK);
    }

    @ApiModelProperty("任务填报审核")
    @HttpMonitorLogger
    @PostMapping("/verify")
    public ResponseEntity<Result<?>> verify(@RequestHeader HttpHeaders headers, @RequestBody TobaccoTaskHandleForm form){
        return new ResponseEntity<>(new Result<>(tobaccoTaskHandleService.handleVerify(HeaderHelper.buildMyHeader(headers),form),errors), HttpStatus.OK);
    }

    @ApiModelProperty("任务填报审核(草稿)")
    @HttpMonitorLogger
    @PostMapping("/verify_save")
    public ResponseEntity<Result<?>> handleVerifyDraft(@RequestHeader HttpHeaders headers, @RequestBody TobaccoTaskHandleForm form){
        return new ResponseEntity<>(new Result<>(tobaccoTaskHandleService.handleVerifyDraft(HeaderHelper.buildMyHeader(headers),form),errors), HttpStatus.OK);
    }
}
