package com.goodsogood.ows.controller;

import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.ReportEntity;
import com.goodsogood.ows.model.vo.ReportForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.ReportService;
import com.goodsogood.ows.utils.JsonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/report")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class ReportController {
    private final Errors errors;

    private final ReportService reportService;

    private final ObjectMapper objectMapper;

    @Autowired
    public ReportController(Errors errors, ReportService reportService, ObjectMapper objectMapper) {
        this.errors = errors;
        this.reportService = reportService;
        this.objectMapper = objectMapper;
    }

    /**
     * 半年听取工作汇报新增
     */
    @HttpMonitorLogger
    @ApiOperation(value = "半年听取工作汇报新增")
    @PostMapping("/append")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> addReport(@RequestHeader HttpHeaders headers, @RequestBody ReportEntity reportEntity){
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        Long userId = sysHeader.getUserId();
//        // 提取leader中的name
//        List<String> leaderNamesList = extractLeaderNames(reportEntity.getLeader());
//        String leaderNames = String.join(",", leaderNamesList);
//        reportEntity.setLeaderNames(leaderNames);
//        // 提取leader中的user_id
//        List<String> leaderIdsList = extractLeaderIds(reportEntity.getLeader());
//        String leaderIds = String.join(",", leaderIdsList);
//        reportEntity.setLeaderIds(leaderIds);
        reportEntity.setCreateUser(userId);
        try {
            var list = objectMapper.readValue(reportEntity.getLeader(), new TypeReference<List<Map<String, Object>>>() {});

            StringBuilder leaderNamesBuilder = new StringBuilder();
            StringBuilder leaderIdsBuilder = new StringBuilder();

            for (Map<String, Object> map : list) {
                String name = (String) map.get("name");
                Integer userIds = (Integer) map.get("unit_id");

                if (name != null && !name.isEmpty()) {
                    if (leaderNamesBuilder.length() > 0) {
                        leaderNamesBuilder.append(",");
                    }
                    leaderNamesBuilder.append(name);
                }

                if (userIds != null) {
                    if (leaderIdsBuilder.length() > 0) {
                        leaderIdsBuilder.append(",");
                    }
                    leaderIdsBuilder.append(userIds);
                }
            }

            String unitNames = leaderNamesBuilder.toString();
            String unitIds = leaderIdsBuilder.toString();

            // 将结果存储到对象的属性中
            reportEntity.setUnitName(unitNames);
            reportEntity.setUnitId(unitIds);
        } catch (IOException e) {
            // 处理异常情况
//            e.printStackTrace();
            log.error(e.getLocalizedMessage(),e);
        }
        Result<Long> result = new Result<>(reportService.addReport(reportEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    /**
     * 半年听取工作汇报修改
     */
    @HttpMonitorLogger
    @ApiOperation(value = "半年听取工作汇报修改")
    @PostMapping("/compile")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> changeReport(@RequestHeader HttpHeaders headers, @RequestBody ReportEntity reportEntity) {
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        Long userId = sysHeader.getUserId();
        reportEntity.setUpdateUser(userId);
        try {
            var list = objectMapper.readValue(reportEntity.getLeader(), new TypeReference<List<Map<String, Object>>>() {});

            StringBuilder leaderNamesBuilder = new StringBuilder();
            StringBuilder leaderIdsBuilder = new StringBuilder();

            for (Map<String, Object> map : list) {
                String name = (String) map.get("name");
                Integer userIds = (Integer) map.get("unit_id");

                if (name != null && !name.isEmpty()) {
                    if (leaderNamesBuilder.length() > 0) {
                        leaderNamesBuilder.append(",");
                    }
                    leaderNamesBuilder.append(name);
                }

                if (userIds != null) {
                    if (leaderIdsBuilder.length() > 0) {
                        leaderIdsBuilder.append(",");
                    }
                    leaderIdsBuilder.append(userIds);
                }
            }

            String unitNames = leaderNamesBuilder.toString();
            String unitIds = leaderIdsBuilder.toString();

            // 将结果存储到对象的属性中
            reportEntity.setUnitName(unitNames);
            reportEntity.setUnitId(unitIds);
        } catch (IOException e) {
            // 处理异常情况
//            e.printStackTrace();
            log.error(e.getLocalizedMessage(),e);
        }
        Result<Long> result = new Result<>(reportService.updateReport(reportEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 半年听取工作汇报列表
     */
    @HttpMonitorLogger
    @ApiOperation(value = "半年听取工作汇报列表")
    @PostMapping("/list")
    public ResponseEntity<Result<List<ReportEntity>>> listReport(@RequestBody ReportForm reportForm, @RequestParam(value = "page", required = false, defaultValue = "1") Integer page, @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize) {
        return new ResponseEntity<>(new Result<>(reportService.listReport(reportForm, page, pageSize), errors), HttpStatus.OK);
    }


    /**
     * 半年听取工作汇报删除
     */
    @HttpMonitorLogger
    @ApiOperation(value = "半年听取工作汇报删除")
    @GetMapping("/erasure")
    public ResponseEntity<Result<Boolean>> eliminateReport(Long id) {
        Result<Boolean> result = new Result<>(reportService.deleteReport(id) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 半年意识形态汇报新增
     * @return
     */
    @HttpMonitorLogger
    @ApiOperation(value = "半年听取意识形态工作新增")
    @PostMapping("/awareness/append")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> addReportAwareness(@RequestHeader HttpHeaders headers, @RequestBody ReportEntity reportEntity) {
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        Long userId = sysHeader.getUserId();
        reportEntity.setCreateUser(userId);
        reportEntity.setCreateTime(LocalDateTime.now());
        reportEntity.setUpdateUser(userId);
        reportEntity.setUpdateTime(LocalDateTime.now());
        Result<Long> result = new Result<>(reportService.addReportAwareness(reportEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "半年听取意识形态工作修改")
    @PostMapping("/awareness/compile")
    @RepeatedCheck
    public ResponseEntity<Result<Long>> changeReportAwareness(@RequestHeader HttpHeaders headers, @RequestBody ReportEntity reportEntity) {
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        Long userId = sysHeader.getUserId();
        reportEntity.setUpdateUser(userId);
        reportEntity.setUpdateTime(LocalDateTime.now());
        Result<Long> result = new Result<>(reportService.changeReportAwareness(reportEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    // 提取leader中的name
    private List<String> extractLeaderNames(String leader) {
        List<String> leaderNamesList = new ArrayList<>();
        Pattern pattern = Pattern.compile("name:'(.*?)'");
        Matcher matcher = pattern.matcher(leader);
        while (matcher.find()) {
            leaderNamesList.add(matcher.group(1));
        }
        return leaderNamesList;
    }

    // 提取leader中的user_id
    private List<String> extractLeaderIds(String leader) {
        List<String> leaderIdsList = new ArrayList<>();
        Pattern pattern = Pattern.compile("user_id=(\\d+)");
        Matcher matcher = pattern.matcher(leader);
        while (matcher.find()) {
            leaderIdsList.add(matcher.group(1));
        }
        return leaderIdsList;
    }
}
