package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.db.MeetingWorkPointEntity
import com.goodsogood.ows.model.vo.MeetingWorkPointForm
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.MeetingWorkPointService
import io.swagger.annotations.ApiOperation
import org.slf4j.LoggerFactory
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 * <AUTHOR>
 * @date 2023/10/10
 * @description class MeetingWorkPointController
 */
@RestController
@RequestMapping("/mwp")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Validated
class MeetingWorkPointController(
    val errors: Errors,
    val meetingWorkPointService: MeetingWorkPointService,
) {
    private val log = LoggerFactory.getLogger(MeetingWorkPointController::class.java)

    // 查询党建工作要点列表
    @HttpMonitorLogger
    @PostMapping("/list")
    @ApiOperation("查询党建工作要点列表")
    fun list(
        @RequestHeader headers: HttpHeaders,
        @Valid @RequestBody form: MeetingWorkPointForm,
        @RequestParam("page", required = false, defaultValue = "1") page: Int? = 1,
        @RequestParam("page_size", required = false, defaultValue = "20") pageSize: Int? = 20,
        bindingResult: BindingResult,
    ): ResponseEntity<Result<List<MeetingWorkPointEntity>>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val data = meetingWorkPointService.listByQuery(form, page ?: 1, pageSize ?: 20, headers, header)
        return ResponseEntity(Result(data.toList(), errors).also {
            it.pageSize = pageSize
            it.pageNum = data.number.plus(1)
            it.total = data.totalElements
        }, HttpStatus.OK)
    }

    // 通过党建工作要点id获取党建工作要点详情
    @HttpMonitorLogger
    @GetMapping("/get")
    @ApiOperation("查询党建工作要点")
    fun findById(
        @RequestHeader headers: HttpHeaders,
        @RequestParam("id") id: Long,
    ): ResponseEntity<Result<MeetingWorkPointEntity>> {
        val data = meetingWorkPointService.findById(id)
        return ResponseEntity(Result(data, errors), HttpStatus.OK)
    }

    // 添加党建工作要点（其实是更新）
    @HttpMonitorLogger
    @PostMapping("/fill")
    @ApiOperation("填充党建工作要点列表")
    fun update(
        @RequestHeader headers: HttpHeaders,
        @Valid @RequestBody form: MeetingWorkPointEntity,
        bindingResult: BindingResult,
    ): ResponseEntity<Result<MeetingWorkPointEntity>> {
        if (form.id == null) {
            throw ApiException("id不能为空", Result<Any>(errors, 2009, HttpStatus.OK.value(), "id不能为空"))
        }
        val header = HeaderHelper.buildMyHeader(headers)
        val data = meetingWorkPointService.updateById(form.id!!, form, header)
        return ResponseEntity(Result(data, errors), HttpStatus.OK)
    }

    // 手动生成某年的党建工作要点
    @HttpMonitorLogger
    @GetMapping("/cbo_id")
    @ApiOperation("手动生成某年的党建工作要点")
    fun createByOwnerId(
        @RequestHeader headers: HttpHeaders,
        @RequestParam("year", required = false) year: Int? = null,
    ): ResponseEntity<Result<String>> {
        val header = HeaderHelper.buildMyHeader(headers)
        meetingWorkPointService.createByOwnerId(header.regionId, headers, year)
        return ResponseEntity(Result("success", errors), HttpStatus.OK)
    }
}