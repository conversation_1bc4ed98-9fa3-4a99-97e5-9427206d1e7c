package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.CommendPenalizeApprovalForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.MeetingOrgCommendPenalizeService;
import com.goodsogood.ows.service.MeetingResultService;
import com.goodsogood.ows.service.MeetingService;
import com.goodsogood.ows.service.UserCommendPenalizeService;
import com.goodsogood.ows.utils.JsonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;


/**
 *
 * 工作流回调
 *
 * <AUTHOR>
 * @create 2018-10-24 15:15
 **/
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/")
public class WorkflowCallbackController {

    private final Errors errors;
    private final MeetingResultService meetingResultService;
    private final MeetingService meetingService;
    private final UserCommendPenalizeService userCommendPenalizeService;
    private final MeetingOrgCommendPenalizeService orgCommendPenalizeService;

    @Autowired
    public WorkflowCallbackController(Errors errors, MeetingResultService meetingResultService,
                                      MeetingService meetingService, UserCommendPenalizeService userCommendPenalizeService, MeetingOrgCommendPenalizeService orgCommendPenalizeService) {
        this.errors = errors;
        this.meetingResultService = meetingResultService;
        this.meetingService = meetingService;
        this.userCommendPenalizeService = userCommendPenalizeService;
        this.orgCommendPenalizeService = orgCommendPenalizeService;
    }


    /**
     * 工作流模块的回调接口
     *
     * @return 处理是否成功
     */
    @HttpMonitorLogger
    @GetMapping("/workflow_callback")
    @ApiOperation("工作流模块的回调接口")
    public ResponseEntity<Result<?>> wfCallback(
            @RequestParam
                    long atp,
            @RequestParam(required = false)
                    long aid,
            @RequestParam(required = false)
                    String abd,
            @RequestParam(required = false)
                    long tid,
            @RequestParam(required = false)
                    int status,
            @RequestParam(required = false)
                    String reason,
            @RequestParam(value = "re_uid", required = false)
                    Long reUid,
            @RequestHeader HttpHeaders headers
    ) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        log.info("回调参数：apt:{};aid:{};abd:{};tid:{};status:{};reason:{};reUid:{}", atp, aid, abd, tid, status, reason, reUid);
        try {
            reason = URLDecoder.decode(StringUtils.defaultIfEmpty(reason, ""), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.debug("URLDecoder.decode 失败。reason： " + reason, e);
        }
        try {
            abd = URLDecoder.decode(StringUtils.defaultIfEmpty(abd, ""), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.debug("URLDecoder.decode 失败。abd： " + abd, e);
        }
        //发起活动
        if (atp == 6) {
            // chenanshun 2018年11月26日 15:36:42
            if (status == 1 || status == 2) {// 1:审批通过 2：打回
                meetingService.updateStatusByWorkflow(aid, tid, sysHeader.getUserId(), status, reason, reUid);
            } else {// 其它回调不做处理
               log.debug("status = " + status + " 被丢弃");
            }
        }
        //填写纪实表
        if (atp == 7) {
            if (status == 1 || status == 2) {// 1:审批通过 2：打回
                this.meetingResultService.updateStatusByWorkflow(aid, tid, sysHeader.getUserId(), status, reason, reUid);
            } else {// 其它回调不做处理
                log.debug("status = " + status + " 被丢弃");
            }
        }
        //奖惩登记 - 两层审批
        if (atp == 9) {
            if (status == 1 || status == 2) {// 1:审批通过 2：打回
                if (StringUtils.isNotBlank(abd)) {
                    final CommendPenalizeApprovalForm approvalForm = JsonUtils.fromJson(abd, CommendPenalizeApprovalForm.class);
                    if (approvalForm.getType().equals(CommendPenalizeApprovalForm.USER)) {
                        this.userCommendPenalizeService.workflowCallBack(aid, tid, sysHeader.getUserId(), status, reason, reUid, headers);
                    } else {
                        this.orgCommendPenalizeService.workflowCallBack(aid, tid, sysHeader.getUserId(), status, reason, reUid, headers);
                    }
                }
            } else {// 其它回调不做处理
                log.debug("status = " + status + " 被丢弃");
            }
        }

        //奖惩登记 - 一层审批
        if (atp == 11) {
            if (status == 1 || status == 2) {// 1:审批通过 2：打回
                if (StringUtils.isNotBlank(abd)) {
                    final CommendPenalizeApprovalForm approvalForm = JsonUtils.fromJson(abd, CommendPenalizeApprovalForm.class);
                    if (approvalForm.getType().equals(CommendPenalizeApprovalForm.USER)) {
                        this.userCommendPenalizeService.workflowCallBack(aid, tid, sysHeader.getUserId(), status, reason, reUid, headers);
                    } else {
                        this.orgCommendPenalizeService.workflowCallBack(aid, tid, sysHeader.getUserId(), status, reason, reUid, headers);
                    }
                }
            } else {// 其它回调不做处理
                log.debug("status = " + status + " 被丢弃");
            }
        }

        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }
}
