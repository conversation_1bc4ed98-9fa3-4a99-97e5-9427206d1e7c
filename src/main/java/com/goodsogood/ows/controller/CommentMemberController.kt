package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.annotation.RepeatedCheck
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.CommentMemberForm
import com.goodsogood.ows.model.vo.CommentMemberVO
import com.goodsogood.ows.model.vo.DealMemberVO
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.CommentMemberService
import io.swagger.annotations.ApiOperation
import lombok.extern.log4j.Log4j2
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@RequestMapping("/comment-member")
@Log4j2
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Validated
class CommentMemberController(@Autowired val errors: Errors,
                              @Autowired val commentMemberService: CommentMemberService) {

    @HttpMonitorLogger
    @PostMapping("/query-list")
    @ApiOperation("查询民主评议人员列表")
    fun getCommentMemberList(@Valid @RequestBody commentMemberVO: CommentMemberVO,
                             bindingResult : BindingResult,
                             @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberService.getCommentMemberList(commentMemberVO, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/add-member")
    @ApiOperation("新增民主评议党员")
    @RepeatedCheck  // 防重复提交
    fun addCommentMember(@Valid @RequestBody vo: DealMemberVO,
                         bindingResult: BindingResult,
                         @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        val list = commentMemberService.addMember(vo, headers)
        val result = if (list.size == 0) {"success"} else {list.joinToString(separator = "、")}
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                result,
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/del-member")
    @ApiOperation("删除民主评议党员")
    @RepeatedCheck  // 防重复提交
    fun delCommentMember(@Valid @RequestBody vo: DealMemberVO,
                         bindingResult: BindingResult,
                         @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberService.delMember(vo, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/get")
    @ApiOperation("查询民主评议党员详情")
    fun getCommentMember(@RequestParam("comment_member_id", required = false) commentMemberId: Long?,
                         @RequestParam("user_id", required = false) userId: Long?,
                         @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberService.getCommentMember(commentMemberId, userId, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/insert")
    @ApiOperation("新增自评")
    @RepeatedCheck // 防重复提交
    fun insertCommentMember(@Valid @RequestBody form: CommentMemberForm,
                         bindingResult : BindingResult,
                         @RequestHeader headers: HttpHeaders
    ) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberService.insertCommentMember(form, headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/get-member-status")
    @ApiOperation("获取自评状态")
    fun getCommentMemberStatus(@RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberService.getCommentMemberStatus(headers),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/self-statistical")
    @ApiOperation("获取自评统计")
    fun selfStatistical(@RequestParam("comment_id") commentId: Long,
                        @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberService.statisticalSelf(commentId),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/no-self")
    @ApiOperation("获取没有自评的数量")
    fun noSelf(@RequestParam("comment_id") commentId: Long,
               @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberService.getNoSelfNum(commentId),
                errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/notice-no-self")
    @ApiOperation("通知没有自评完成的人员")
    @RepeatedCheck // 防重复提交
    fun noticeNoSelf(@RequestParam("comment_id") commentId: Long,
                     @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                commentMemberService.sendMsg(commentId, headers),
                errors),
            HttpStatus.OK
        )
    }
}