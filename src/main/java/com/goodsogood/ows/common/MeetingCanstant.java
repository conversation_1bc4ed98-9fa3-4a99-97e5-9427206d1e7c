package com.goodsogood.ows.common;

/**
 * 活动常量池
 * <AUTHOR>
 * @create 2018-10-26 9:21
 **/
public class MeetingCanstant {

    //1：发起审批中、2：发起未通过、3：活动待举办（待填报结果）、5：填报审查中（第一次）、6：填报未通过（第一次）、
    // 7：已提交、8：退回、9：活动已取消、10：填报审查中（第一次之后）、11：填报未通过（第一次之后）
     // 12：待复核（第二次提交并且审批通过）

    /**
     * 活动状态：1   发起审批中
     */
     public static final Integer MEETING_STATUS_APPROVAL_APPLY = 1;

    /**
     * 活动状态：2   发起未通过
     */
     public static final Integer MEETING_STATUS_APPROVAL_NOT_PASS = 2;

    /**
     * 活动状态：3   活动待举办（待填报结果）
     */
     public static final Integer MEETING_STATUS_SOON_START = 3;

    /**
     * 活动状态：4   待填报结果
     */
     public static final Integer MEETING_STATUS_APPROVAL = 4;

    /**
     * 活动状态：5   本组织填报审批中（第一次）
     */
     public static final Integer MEETING_STATUS_SUBMIT_FIRST = 5;

    /**
     * 活动状态：6   本组织填报审批未通过（第一次）
     */
     public static final Integer MEETING_STATUS_SUBMIT_FIRST_NOT_PASS = 6;

    /** 活动状态：7   已提交 填写纪实报告审批通过后*/
     public static final Integer MEETING_STATUS_SUBMIT = 7;

    /** 活动状态：8   退回 */
     public static final Integer MEETING_STATUS_BACK = 8;

    /** 活动状态：9   活动已取消 */
     public static final Integer MEETING_STATUS_CANCEL = 9;

    /**
     * 活动状态：10   本组织填报审批中（第一次之后）
     */
     public static final Integer MEETING_STATUS_SUBMIT_MORE = 10;

    /**
     * 活动状态：11   本组织填报审批未通过（第一次之后）
     */
     public static final Integer MEETING_STATUS_SUBMIT_MORE_NOT_PSSS = 11;

    /** 活动状态：12   待复核（第二次提交并且审批通过） */
     public static final Integer MEETING_STATUS_SUBMIT_CHECK = 12;

    /** 活动状态：13   通过（第一次考核就通过） */
     public static final Integer MEETING_STATUS_PASS_FIRST = 13;

    /** 活动状态：14   通过（第一次考核被退回后的通过） */
     public static final Integer MEETING_STATUS_PASS_MORE = 14;

    /**
     * 活动状态：15   撤回 管理员自己撤回
     */
    public static final Integer MEETING_STATUS_REVOKE = 15;




    /**
     * 发起活动代办任务业务KEY 前缀
     * 前缀_区县编号_活动编号
     */
    public static final String MEETING_PENDING_TASK_KEY = "MEETING_PENDING_TASK_";



}
