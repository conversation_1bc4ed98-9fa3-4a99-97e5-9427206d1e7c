package com.goodsogood.ows.common.pojo;

import lombok.Data;

/**
 * 推送参数
 * <AUTHOR>
 * @date 2019-10-25 10:49
 * @since 3.0.0
 **/
@Data
public class MeetingPushParam {

     /**
      * 推送的模式 1-只推当前渠道，2-只推主题消息，3-全部推送 默认为1
      */
     public Integer pushModel;

     /**
      * push_model = 2时，
      * 此参数决定是否拉取用户数据，
      * 如果使用了外链并且需要使用{openId}此时必须要去用户中心拉取，
      * 否则不需要拉取，需要业务方传递对应的参数，默认为拉取用户数据
      */
     public Integer isPullUser;

     public MeetingPushParam() {
     }

     public MeetingPushParam(Integer pushModel) {
          this.pushModel = pushModel;
     }

     public MeetingPushParam(Integer pushModel, Integer isPullUser) {
          this.pushModel = pushModel;
          this.isPullUser = isPullUser;
     }
}
