package com.goodsogood.ows.common.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 分页实体
 * <AUTHOR>
 * @create 2018-10-24 10:40
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PageBean {

     /** 当前页码 默认第一页 */
     @JsonProperty(value = "page_no")
     private Integer pageNo = 1;

     /** 每页大小 默认10条 */
     @JsonProperty(value = "page_size")
     private Integer pageSize = 10;

}
