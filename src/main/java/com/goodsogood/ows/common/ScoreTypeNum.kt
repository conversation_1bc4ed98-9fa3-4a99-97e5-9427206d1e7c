package com.goodsogood.ows.common

enum class ScoreTypeNum(val type: Int, val typeName: String) {
    ORGANIZE_LIFE(12, "组织生活"),
    PARTY_TASK(13, "党建任务"),
    INNOVATION_TASK(14, "创新任务"),
    BUSINESS_TASK(15, "业务任务"),
    PIONEER_BEST(16, "创先争优"),
    THEORY_ARMED(17, "理论武装"),
    PPMD(18, "党费交纳"),
    MAIN_RESPONSIBILITY(19, "主体责任"),
    PARTY_EDUCATION(20, "党性教育"),
    SERVICE_GRASSROOTS(21, "服务基层"),
    PARTY_BRAND(22, "党建品牌建设"),
    PARTY_POSITION(23, "党建阵地建设"),
    ORG_BUILDING(24, "组织建设"),
    PPMD_USE_MANAGEMENT(25, "党费收缴使用管理"),
    ACT_AS(26, "担当作为"),
    ECP_TASKS(27, "云区任务"),
    BEST_EXCELLENT(28, "争先创优"),
    ONLINE_INTERACTION(29, "线上互动"),
    PARTY_SPIRIT(35, "党性修养")
}