package com.goodsogood.ows.common;

import lombok.Getter;

/**
 * 删除状态
 *
 * <AUTHOR>
 * @version 2019/12/31
 */
public enum StatusEnum {
    NORMAL(1, "正常"),

    DELETE(2, "删除"),
    ;

    @Getter
    private final Integer status;

    @Getter
    private final String description;

    StatusEnum(Integer status, String description) {
        this.status = status;
        this.description = description;
    }

}
