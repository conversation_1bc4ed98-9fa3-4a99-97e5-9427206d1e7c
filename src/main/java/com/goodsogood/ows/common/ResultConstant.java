package com.goodsogood.ows.common;

/**
 * 纪实结果常量池
 * <AUTHOR>
 * @create 2018-10-25 15:50
 **/
public class ResultConstant {

     /** 不需要审批 */
     public static final int NOT_MUST_APPROVE = 0;

     /** 需要审批 */
     public static final int MUST_APPROVE = 1;

     /**  审批中 */
     public static final int APPROVE_STATUS_ING = 2;

     /**  审批通过 */
     public static final int APPROVE_STATUS_PASS = 1;

     /**  审批不通过 */
     public static final int APPROVE_STATUS_NO_PASS = 3;

     /**  是否需要填写决议 0：不需要；1：需要 */
     public static final int IS_W_RESOLUTION = 1;

    /**
     * 有活动流程
     */
     public static final int HAS_MEETING = 1;
    /**
     * 无活动流程
     */
     public static final int HAS_NO_MEETING = 0;

    /**
     * 工作任务是未完成
     */
     public static final int MEETING_TOPIC_STATUS_NO = 1;

    /**
     * 工作任务是已完成
     */
     public static final int MEETING_TOPIC_STATUS_YES = 2;


     /**
      * 是否是下级在操作
      * @param meetingStatus
      * @return
      */
     public static boolean isWorker(int meetingStatus){
         return meetingStatus == MeetingCanstant.MEETING_STATUS_SUBMIT_FIRST ||
                 meetingStatus == MeetingCanstant.MEETING_STATUS_BACK ||
                 meetingStatus == MeetingCanstant.MEETING_STATUS_SUBMIT_MORE ||
                 meetingStatus == MeetingCanstant.MEETING_STATUS_SUBMIT_MORE_NOT_PSSS ||
                 meetingStatus == MeetingCanstant.MEETING_STATUS_SOON_START ||
                 meetingStatus == MeetingCanstant.MEETING_STATUS_APPROVAL;
     }

     /**
      * 判断是否是下级在操作
      * @param meetingOid
      * @param userOid
      * @return
      */
     public static boolean isWorker(long meetingOid, long userOid) {
         //如果活动的创建oid 等于填写纪实报告的人的ois，则是下级
         return meetingOid == userOid;
     }

     /**
      * 是否能撤回审批
      * @param meetingStatus
      * @return
      */
     public static boolean canUndoApprove(int meetingStatus){
         return meetingStatus == MeetingCanstant.MEETING_STATUS_SUBMIT_FIRST ||
                 meetingStatus == MeetingCanstant.MEETING_STATUS_SUBMIT_MORE;
     }





}
