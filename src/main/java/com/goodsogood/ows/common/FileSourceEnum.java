package com.goodsogood.ows.common;

import lombok.Getter;

/**
 * 文件来源 1 组织奖惩 2组织评议 3 党员奖惩
 *
 * <AUTHOR>
 * @version 2019/01/06
 */
@Getter
public enum FileSourceEnum {
    ORG_COMMEND_PENALIZE(1, "组织奖惩"),

    ORG_DEBRIEF_REVIEW(2, "组织评议"),

    MEMBER_COMMEND_PENALIZE(3, "党员奖惩"),

    MEETING_FILE_TALK(4, "谈心谈话附件"),

    MEETING_TALK(5, "谈心谈话模板"),

    MEETING_COMMENT(6,"民主评议模板-自评"),

    MEETING_ORG_COMMENT(7,"民主评议模板-综合"),

    MEETING_COMMENT_ATTACH(8, "民主评议综合评定附件"),

    ORG_COMMEND_PENALIZE_PIC(9, "组织奖惩图片"),

    MEMBER_COMMEND_PENALIZE_PIC(10, "党员奖惩图片"),

    MEETING_COMMENT_APPRAISAL(11, "民主评议自评互评表");

    private final Integer source;

    private final String description;

    FileSourceEnum(Integer source, String description) {
        this.source = source;
        this.description = description;
    }

    public Integer getSource() {
        return source;
    }

    public String getDescription() {
        return description;
    }
}
