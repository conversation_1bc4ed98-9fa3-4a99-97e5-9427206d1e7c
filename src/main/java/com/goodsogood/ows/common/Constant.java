package com.goodsogood.ows.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.naming.ldap.Control;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019/12/30
 */
public class Constant {

    public static final int YES = 1;
    public static final int DEL = 2;
    public static final int NO = 0;

    public static final short YES_SHORT = 1;
    public static final short DEL_SHORT = 2;
    public static final short NO_SHORT = 0;

    /**
     * 优秀
     */
    public static final int FINE = 1;
    /**
     * 合格
     */
    public static final int QUALIFIED = 2;
    /**
     * 基本合格
     */
    public static final int BASIC_QUALIFIED = 3;
    /**
     * 不合格
     */
    public static final int UNQUALIFIED = 4;
    /**
     * 不确定等次
     */
    public static final int UNKNOWN = 5;

    /**
     * 限期整改
     */
    public static final int RECTIFICATION = 1;
    /**
     * 除名
     */
    public static final int EXPEL = 2;

    /**
     * 不合格-限期整改
     */
    public static final int UNQUALIFIED_RECTIFICATION = 41;

    /**
     * 不合格-除名
     */
    public static final int UNQUALIFIED_EXPEL = 42;

    /**
     * 奖励
     */
    public static final int REWARD = 1;
    /**
     * 惩罚
     */
    public static final int PENALIZE = 2;

    /**
     * 定时任务创建的数据
     */
    public static final Long USER = -888L;

    /**
     * 人员奖励名称字典code(旧)
     */
    public static final String REWARD_NAME_OLD = "103801";

    /**
     * 人员奖励名称字典code
     */
    public static final String REWARD_NAME = "103806";

    /**
     * 人员惩罚名称字典code
     */
    public static final String PENALIZE_NAME = "103803";

    /**
     * 组织奖励名称字典code(旧)
     */
    public static final String ORG_REWARD_NAME_OLD = "103701";

    /**
     * 组织奖励名称字典code
     */
    public static final String ORG_REWARD_NAME = "103705";

    /**
     * 组织惩罚名称字典code
     */
    public static final String OGR_PENALIZE_NAME = "103702";

    /**
     * 提交接受
     */
    public static final Integer HANDLE_TYPE_SA = 1;

    /**
     * 提交拒绝
     */
    public static final Integer HANDLE_TYPE_SR = 2;

    /**
     * 退回
     */
    public static final Integer HANDLE_TYPE_RETURN = 3;

    /**
     * 审核拒绝
     */
    public static final Integer HANDLE_TYPE_VR = 4;

    /**
     * 审核同意
     */
    public static final Integer HANDLE_TYPE_VA = 5;

    /**
     * 任务状态 草稿
     */
    public static final Integer TASK_STATUS_DRAFT = 1;

    /**
     * 任务状态 未开始
     */
    public static final Integer TASK_STATUS_NP = 2;

    /**
     * 任务状态 进行中
     */
    public static final Integer TASK_STATUS_ING = 3;

    /**
     * 任务状态 已结束
     */
    public static final Integer TASK_STATUS_END = 4;

    /**
     * 任务状态 已拒绝
     */
    public static final Integer TASK_STATUS_REFUSE = 5;

    /**
     * 任务类型 工作单
     */
    public static final Integer TASK_WORD_TYPE = 1;

    /**
     * 任务类型 转办单
     */
    public static final Integer TASK_PROXY_TYPE = 2;

    /**
     * 微信
     */
    public static final Integer WE_CHAT_TYPE = 1;

    /**
     * 短信
     */
    public static final Integer NOTE_TYPE = 1;

    /**
     * 网信办舆情分类
     */
    public static final String SBW_TYPE = "SBW_TYPE";

    /**
     * 网信办代办草稿
     */
    public static final Integer SHIFT_DRAFT = 1;

    /**
     * 网信办代办提交
     */
    public static final Integer SHIFT_SUBMIT = 2;

    /**
     * 网信办代办处理中
     */
    public static final Integer SHIFT_HANDLING = 3;

    /**
     * 网信办代办不予处理
     */
    public static final Integer SHIFT_NOT = 4;

    /**
     * 网信办代办已结束
     */
    public static final Integer SHIFT_END = 5;

    /**
     * 网信办处理代办流水
     */
    public static final Integer SHIFT_FLOW_IS_HANDLE = -1;

    /**
     * 网信办不处理代办 流水
     */
    public static final Integer SHIFT_FLOW_NOT_HANDLE = -2;

    /**
     * 网信办代办 已提交流水
     */
    public static final Integer SHIFT_FLOW_SUBMIT = -3;

    /**
     * 烟草任务接收范围-组织
     */
    public static final Integer TASK_TO_ORG = 1;

    /**
     * 烟草任务接收范围-人员
     */
    public static final Integer TASK_TO_PERSON = 2;

    /**
     * 烟草任务处理类型-填报
     */
    public static final Integer HANDLE_Fill = 1;

    /**
     * 烟草任务处理类型-审核
     */
    public static final Integer HANDLE_VERIFY = 2;

    /**
     * 烟草任务处理状态-草稿
     */
    public static final Integer HANDLE_STATUS_DRAFT = 1;

    /**
     * 烟草任务处理状态-已填报
     */
    public static final Integer HANDLE_STATUS_FILL = 2;

    /**
     * 烟草任务处理状态-已通过
     */
    public static final Integer HANDLE_STATUS_VA = 3;

    /**
     * 烟草任务处理状态-未通过
     */
    public static final Integer HANDLE_STATUS_VR = 4;

    /**
     * 烟草任务处理状态-退回
     */
    public static final Integer HANDLE_STATUS_BACK = 5;

    /**
     * 奖励类别
     */
    public static final Integer REWARD_CATEGORY = 104801;

    /**
     * 奖励级别
     */
    public static final Integer REWARD_LEVEL = 104802;

    /**
     * 奖励名称-党员
     */
    public static final Integer REWARD_NAME_USER_NEW = 104803;

    /**
     * 奖励名称-组织
     */
    public static final Integer REWARD_NAME_ORG_NEW = 104804;

    /**
     * 奖励级别 - 其他
     */
    public static final Integer REWARD_LEVEL_OTHER = 10480207;

    /**
     * 奖励名称 - 用户 - 其他
     */
    public static final Integer REWARD_NAME_USER_OTHER = 10480304;

    /**
     * 奖励名称 - 组织 - 其他
     */
    public static final Integer REWARD_NAME_ORG_OTHER = 10480402;

    /**
     * 惩罚类别
     */
    public static final Integer PENALIZE_CATEGORY = 104901;

    /**
     * 惩罚所有
     */
    public static final Integer PENALIZE_ALL = 1049;

    /**
     * 谈心谈话 - 谈话人
     */
    public static final Integer TALK_USER = 1;

    /**
     * 谈心谈话 - 被谈话人
     */
    public static final Integer TO_TALK_USER = 2;
    //学历、文化程度
    public static final String EDUCATION_CODE_NEW = "1039";
    //性别
    public static final String GENDER_CODE = "1002";

    @AllArgsConstructor
    @Getter
    public enum SignWay {
        WRITE((short) 0, "手写签到"),
        SCAN((short) 1, "扫码签到"),
        GPS((short) 2, "GPS定位签到"),
        FACE((short) 3, "人脸识别");

        private Short key;
        private String way;

        public static String getWay(Short num) {
            for (SignWay way : SignWay.values()) {
                if (way.getKey().equals(num)) {
                    return way.getWay();
                }
            }
            return " ";
        }
    }

    @AllArgsConstructor
    @Getter
    public enum MeetingStatus {
        VERIFY((short) 1, "发起审批中"),
        UNPASS((short) 2, "发起未通过"),
        WAIT((short) 3, "会议待召开"),
        VERIFY1((short) 5, "填报审查中"),
        UNPASS1((short) 6, "填报未通过"),
        SUBMIT((short) 7, "已提交"),
        CANCEL((short) 8, "退回"),
        RETURN((short) 9, "会议已取消"),
        VERIFY2((short) 10, "填报审查中"),
        UNPASS2((short) 11, "填报未通过"),
        REVIEW((short) 12, "待复核"),
        PASS((short) 13, "检查通过"),
        PASS2((short) 14, "检查通过");

        private Short key;
        private String status;

        public static String getStatus(Short num) {
            for (MeetingStatus status : MeetingStatus.values()) {
                if (status.getKey().equals(num)) {
                    return status.getStatus();
                }
            }
            return " ";
        }
    }

    @AllArgsConstructor
    @Getter
    public enum SignCase {
        CHECKED_IN((short) 1, "已签到"),
        NOT_SIGN((short) 2, "未签到"),
        PUBLIC_VACATE((short) 3, "因公请假"),
        PRIVATE_VACATE((short) 4, "因私请假"),
        ABSENT((short) 5, "缺席"),
        MAKEUP((short) 6, "补学");

        private Short key;
        private String way;

        public static String getSignCase(Short k) {
            for (SignCase v : SignCase.values()) {
                if (v.getKey().equals(k)) {
                    return v.getWay();
                }
            }
            return "-1";
        }
    }

    //民主生活会发布附件上传任务标识
    public static final String TASKKEY = "lifemt";

    //组织生活会发布附件上传任务标识
    public static final String ORGTASKKEY = "orglifemt";

    //民主生活会状态-刚新建
    public static final Integer LIFE_NEW = 1;
    //民主生活会状态-进行了会前准备
    public static final Integer LIFE_READY = 2;
    //民主生活会状态-已结束
    public static final Integer LIFE_END = 3;
    //民主生活会状态-进行了会后梳理
    public static final Integer LIFE_COMBING = 4;
    //民主生活会状态-已上报归档
    public static final Integer LIFE_REPORT = 5;

    /**
     * 民主生活会 会前
     */
    public static final Integer LIFE_STEP_BEFORE = 1;

    /**
     * 民主生活会 会后
     */
    public static final Integer LIFE_STEP_AFTER = 2;

    /**
     * 民主生活status 会前准备状态 1:会前准备 2:继续准备
     */
    public static final List<Integer> LIFE_BEFORE_STAY_STATUS = Arrays.asList(1, 2);


    /**
     * 民主生活status 会中状态  3:会前已提交,会后梳理 4:会前已提交,继续梳理
     */
    public static final List<Integer> LIFE_BEFORE_DONE_STAY_STATUS = Arrays.asList(3, 4);

    //民主生活会meeting附件插入附件表标识
    public static final String LIFEFILE = "lifenotice";

    //组织生活会meeting附件插入附件表标识
    public static final String ORGLIFEFILE = "orglifenotice";

    public static final String SUCCESS = "操作成功";

    //民主生活会模块
    @AllArgsConstructor
    @Getter
    public enum ModelType {
        PLAN(1, 1), //上传会议方案
        PRE_STUDY(2, 2), //会前学习
        ORG_STUDY(3, 2), //组织学习
        CHECK_LEADER(4, 3), //检视剖析材料（领导班子）
        CHECK_SELF_ORIGIN(5, 3), //班子成员个人检视剖析（原版）
        CHECK_SELF_SIGN(6, 3), //班子成员个人检视剖析（签字版）
        ADVICE_DIRECT(7, 4), //征求意见-直接上传
        ADVICE_QU(8, 4), //征求意见-问卷
        ADVICE_TALK(9, 4), //征求意见-个别访谈
        ADVICE_FORUM(10, 4), //征求意见-座谈会
        TALK_MEMBERS(11, 5), //谈心谈话（班子成员之间）
        TALK_DEPARTMENT(12, 5), //谈心谈话（成员与分管部门）
        TALK_PARTY(13, 5), //谈心谈话（成员与党支部）
        TALK_UNIT(14, 5), //谈心谈话（成员与联系点单位）
        NOTICE_DATA(15, 6), //上传通报材料
        PRE_OTHER(16, 7), //会前其他材料
        INITIATE(17, 8), //发起民主生活会
        RECTIFY(18, 9), //整改方案
        REPORT(19, 10), //情况报告
        MEETING_NOTICE(20, 11), //会议通报
        AFT_OTHER(21, 12), //会后其他材料
        ADVICE_TALK_MODEL(22, 4), //征求意见-个别访谈（模版）
        TALK_MEMBERS_MODEL(23, 5), //谈心谈话（班子成员之间-模板）
        TALK_DEPARTMENT_MODEL(24, 5), //谈心谈话（成员与分管部门-模板）
        TALK_PARTY_MODEL(25, 5), //谈心谈话（成员与党支部-模板）
        TALK_UNIT_MODEL(26, 5);//谈心谈话（成员与联系点单位-模板）




        private Integer modelId;
        private Integer type;

        public static List<Integer> getModeIdByType(Integer type) {
            List<Integer> list = new ArrayList<>();
            for (ModelType modelType : ModelType.values()) {
                if (modelType.getType().equals(type)) {
                    list.add(modelType.getModelId());
                }
            }
            return list;
        }

        public static List<Integer> getModeIdByType(List<Integer> type) {
            List<Integer> list = new ArrayList<>();
            for (ModelType modelType : ModelType.values()) {
                if (type.contains(modelType.getType())) {
                    list.add(modelType.getModelId());
                }
            }
            return list;
        }

        public static ModelType getModelByModeId(Integer modelId) {
            for (ModelType entity : ModelType.values()) {
                if (entity.getModelId().equals(modelId)) {
                    return entity;
                }
            }
            return null;
        }
    }


    //组织生活会状态-刚新建
    public static final Integer ORG_LIFE_NEW = 1;
    //组织生活会状态-进行了会前准备
    public static final Integer ORG_LIFE_READY = 2;
    //组织生活会状态-已结束
    public static final Integer ORG_LIFE_END = 3;
    //组织生活会状态-进行了会后梳理
    public static final Integer ORG_LIFE_COMBING = 4;
    //组织生活会状态-已上报归档
    public static final Integer ORG_LIFE_REPORT = 5;

    //组织生活会 会前
    public static final Integer ORG_LIFE_STEP_BEFORE = 1;
    //组织生活会 会后
    public static final Integer ORG_LIFE_STEP_AFTER = 2;

    //组织生活会模块
    @AllArgsConstructor
    @Getter
    public enum OrgModelType {
        PLAN(1, 1), //上传会议方案
        PRE_STUDY(2, 2), //会前学习
        ORG_STUDY(3, 2), //组织学习
        CHECK_LEADER(4, 3), //检视剖析材料（领导班子）
        CHECK_SELF_ORIGIN(5, 3), //班子成员个人检视剖析（原版）
        CHECK_SELF_SIGN(6, 3), //班子成员个人检视剖析（签字版）
        ADVICE_DIRECT(7, 4), //征求意见-直接上传
        ADVICE_QU(8, 4), //征求意见-问卷
        ADVICE_TALK(9, 4), //征求意见-个别访谈
        ADVICE_FORUM(10, 4), //征求意见-座谈会
        TALK_MEMBERS(11, 5), //谈心谈话（班子成员之间）
        TALK_DEPARTMENT(12, 5), //谈心谈话（成员与分管部门）
        TALK_PARTY(13, 5), //谈心谈话（成员与党支部）
        TALK_UNIT(14, 5), //谈心谈话（成员与联系点单位）
        NOTICE_DATA(15, 6), //上传通报材料
        PRE_OTHER(16, 7), //会前其他材料
        INITIATE(17, 8), //发起民主生活会
        RECTIFY(18, 9), //整改方案
        REPORT(19, 10), //情况报告
        MEETING_NOTICE(20, 11), //会议通报
        AFT_OTHER(21, 12), //会后其他材料
        ADVICE_TALK_MODEL(22, 4), //征求意见-个别访谈（模版）
        TALK_MEMBERS_MODEL(23, 5), //谈心谈话（班子成员之间-模板）
        TALK_DEPARTMENT_MODEL(24, 5), //谈心谈话（成员与分管部门-模板）
        TALK_PARTY_MODEL(25, 5), //谈心谈话（成员与党支部-模板）
        TALK_UNIT_MODEL(26, 5), //谈心谈话（成员与联系点单位-模板）
        CHECK(27,3), //支部班子和支部委员对照检查材料-有数据行
        PARTY_COMMENT(28,13), //党员民主评议测评表
        ORG_COMMENT(29,13), //组织民主评议测评表
        ASSESS(30,13), //综合评定
        ORG_CHECK(31,3);//新增的支部班子对照检查材料，无数据行

        private Integer modelId;
        private Integer type;

        public static List<Integer> getModeIdByType(Integer type) {
            List<Integer> list = new ArrayList<>();
            for (ModelType modelType : ModelType.values()) {
                if (modelType.getType().equals(type)) {
                    list.add(modelType.getModelId());
                }
            }
            return list;
        }

        public static List<Integer> getModeIdByType(List<Integer> type) {
            List<Integer> list = new ArrayList<>();
            for (ModelType modelType : ModelType.values()) {
                if (type.contains(modelType.getType())) {
                    list.add(modelType.getModelId());
                }
            }
            return list;
        }

        public static ModelType getModelByModeId(Integer modelId) {
            for (ModelType entity : ModelType.values()) {
                if (entity.getModelId().equals(modelId)) {
                    return entity;
                }
            }
            return null;
        }
    }




}
