package com.goodsogood.ows.common;

/**
 * <AUTHOR>
 * @create 2018-10-22 14:32
 **/
public class TopicConstant {

     /** 是否删除 0 默认*/
     public static final int IS_DEL_0 = 0;
     /** 是否删除 1 删除*/
     public static final int IS_DEL_1 = 1;

     /** 创建方式 派发*/
     public static final int STATUS_LEADER = 1;

     /** 创建方式 自有的*/
     public static final int STATUS_OWNER = 2;

     /**
      * 是否是多选
      * @param type
      * @return
      */
     public static boolean isOpts(int type){
         return type == ContentType.CHECK_BOX.getType() ||
                 type == ContentType.RADIO.getType();
     }

     /**
      * 是否是文本
      * @param type
      * @return
      */
     public static boolean isContent(int type){
         return type == ContentType.TEXT.getType();
     }


     /**
      * 内容类型
      */
     public enum ContentType {

          TEXT(1),RADIO(2),CHECK_BOX(3);

          /** 问题选项类型 */
          private final int type;

          ContentType(int type) {
               this.type = type;
          }

          /**
           * 获取问题类型
           * @param type
           * @return
           */
          public static ContentType getContentType(int type) {
               for (ContentType _enum :  ContentType.values()) {
                    if(type == _enum.getType()) {
                         return _enum;
                    }
               }
               return null;
          }

          public int getType() {
               return this.type;
          }
     }

}
