package com.goodsogood.ows.common;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.model.vo.Result;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@ControllerAdvice
@RestController
@Log4j2
public class ValidateExceptionHandler {
    private final Errors errors;

    @Autowired
    public ValidateExceptionHandler(Errors errors) {
        this.errors = errors;
    }

    /**
     * 处理spring boot + hibernate validators的表单验证错误
     *
     * @param cve ConstraintViolationException
     * @return error
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    ResponseEntity<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException cve) {

        final Result result = new Result(errors, Global.Errors.VALID_ERROR, HttpStatus.BAD_REQUEST.value());
        BindingResult cveBindingResult = cve.getBindingResult();

        if (cveBindingResult.getErrorCount() > 0) {

            result.setMessage(cveBindingResult.getAllErrors().get(0).getDefaultMessage());

            return new ResponseEntity<>(result, HttpStatus.OK);
        }

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
