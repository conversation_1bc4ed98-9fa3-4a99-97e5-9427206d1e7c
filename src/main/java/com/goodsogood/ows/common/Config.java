package com.goodsogood.ows.common;

import org.springframework.stereotype.Component;

/**
 * 通用常量配置
 * <AUTHOR>
 **/
@Component
public class Config {


    /**
     * 定时任务 锁的过期时间， 单位秒
     */
    public static final int SCHEDULER_LOCK_EXPIRE = 3 * 60 * 1000;

    /**
     * 有日程的活动结束后同步钉钉日程的签到状态定时任务锁
     */
    public static final String DING_EVENT_SYNC_LOCK_PREFIX = "MEETING_DING_EVENT_SYNC_LOCK_";

    /**
     * 民主评议分布式事务锁的
     */
    public static final String COMMENT_MEMBER_APPRAISAL_LOCK_PREFIX = "COMMENT_MEMBER_APPRAISAL_LOCK_";


    /**
     * 如果锁被占有，则间隔该时间再次重新去获取锁(ms)
     */
    public static final int TRY_GET_LOCK_INTERVAL = 1000;


    /**
     * Redis缓存服务类相关配置
     */
    public static class RabbitmqConf{
        /**
         * 交换机名称
         */
        public static final String DIRECT_EXCHANGE_NAME = "meeting_direct_exchange";
    }

    /**
     * 组织生活积分操作相关配置
     */
    public static class MeetingScoreConf{
        /**
         * 增加积分操作
         */
        public static final Integer OPERATION_ADD = 0;
        /**
         * 减少积分操作
         */
        public static final Integer OPERATION_REDUCE = 1;


        /**
         * MQ操作类型   组织生活增加积分   对应addMeetingScore方法
         */
        public static final Integer MQ_ADD_MEETING_SCORE = 1;

        /**
         * MQ操作类型   撤回/退回和取消组织生活时  对应reduceMeetingScore方法
         */
        public static final Integer MQ_REDUCE_MEETING_SCORE = 2;

        /**
         * MQ操作类型   规定时间内补学添加个党员积分  对应userScoreAdd方法
         */
        public static final Integer MQ_USER_SCORE_ADD = 3;
    }

}
