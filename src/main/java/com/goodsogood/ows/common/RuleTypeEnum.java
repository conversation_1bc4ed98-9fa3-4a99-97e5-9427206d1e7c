package com.goodsogood.ows.common;

/**
 * <p>Description: 类型选择人员规则</p>
 *
 * <AUTHOR>
 * @version 2019/9/19 14:22
 */
public enum RuleTypeEnum {
    NO_RULE(-1, "未指定"),

    HAS_ORG_GROUP(1, "必须选择党小组"),

    HAS_ORG_PERIOD(2, "必须选择支委会届次"),

    ALL_USERS(3, "选择组织所有成员");

    private final Integer type;
    
    private final String description;

    RuleTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer getType() {
        return this.type;
    }

}
