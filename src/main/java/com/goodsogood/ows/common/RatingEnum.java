package com.goodsogood.ows.common;

import com.google.common.collect.ImmutableMap;
import lombok.Getter;

/**
 * 删除状态
 *
 * <AUTHOR>
 * @version 2019/12/31
 */
@Getter
public enum RatingEnum {
    GOOD(1, "好"),

    BETTER(2, "较好"),

    GENERAL(3, "一般"),

    DIFFER(4, "差"),
    ;

    private final Integer rating;

    private final String description;

    RatingEnum(Integer rating, String description) {
        this.rating = rating;
        this.description = description;
    }

    public final static ImmutableMap<Integer, RatingEnum> RATING_MAP = ImmutableMap.of(
            RatingEnum.GOOD.getRating(), RatingEnum.GOOD,
            RatingEnum.BETTER.getRating(), RatingEnum.BETTER,
            RatingEnum.GENERAL.getRating(), RatingEnum.GENERAL,
            RatingEnum.DIFFER.getRating(), RatingEnum.DIFFER
    );

}
