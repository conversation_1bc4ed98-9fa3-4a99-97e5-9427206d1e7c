package com.goodsogood.ows.common;

import java.util.Arrays;
import java.util.List;

/**
 * 活动请假常量
 *
 * <AUTHOR>
 * @create 2018/10/30 9:01
 */
public class MeetingLeaveConstant {

    public enum LeaveType {

        PUBLIC((short) 1, "因公请假"),
        PRIVATE((short) 2, "因私请假");
        private final short status;
        private final String desc;

        LeaveType(short status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        public short getStatus() {
            return status;
        }
    }

    //审批类型（0:初始-预留  1：待审批/审批中/提交 2：请假审批通过 3：请假审批不通过  4：销假审批中 5：已撤销  6：销假审批不通过）
    public enum LeaveStatus {
        INIT((short)0,"创建"),
        LEAVE_WAIT_APPROVE((short)1,"请假审批中"),
        LEAVE_APPROVE_PASS((short)2,"请假审批通过"),
        LEAVE_APPROVE_NOT_PASS((short)3,"请假审批不通过"),
        OFF_LEAVE_WAIT_APPROVE((short)4,"销假审批中"),//请假中
        OFF_LEAVE_BACK((short)5,"已撤销"),//没有请假
        OFF_LEAVE_NOT_PASS((short)6,"销假审批不通过"),//请假中
        OFF_LEAVE_PASS((short)7,"销假审批通过");//没有请假
        private final short status;
        private final String desc;

        LeaveStatus(short status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        public short getStatus() {
            return status;
        }

        /**
         * 待审核的请假状态，用于提交活动后取消所有该活动的相关请假审批
         *
         */
        public static List<Short> getRejectLeaveStatus(){
            return Arrays.asList(LeaveStatus.INIT.status,LeaveStatus.LEAVE_WAIT_APPROVE.status,LeaveStatus.OFF_LEAVE_WAIT_APPROVE.status);
        }

        /**
         * 请假中的状态：0 1 2 6-正在请假或请假审批通过
         * @return
         */
        public static List<Short> getCanNotLeaveStatus(){
            return Arrays.asList(LeaveStatus.INIT.status,LeaveStatus.LEAVE_WAIT_APPROVE.status,LeaveStatus.LEAVE_APPROVE_PASS.status,
                   LeaveStatus.OFF_LEAVE_NOT_PASS.status);
        }


        /**
         * 请假状态 0 1
         */
        public static List<Short> getLeaveStatus(){
            return Arrays.asList(LeaveStatus.INIT.status,LeaveStatus.LEAVE_WAIT_APPROVE.status);
        }


        /**
         * 销假状态 4
         */
        public static List<Short> getOffLeaveStatus(){
            return Arrays.asList(LeaveStatus.OFF_LEAVE_WAIT_APPROVE.status);
        }

        /**
         * 可以销假的状态 0 1 2,6
         * @return
         */
        public static List<Short> getCanOffLeaveStatus(){
            return Arrays.asList(LeaveStatus.INIT.status,LeaveStatus.LEAVE_WAIT_APPROVE.status,
                    LeaveStatus.LEAVE_APPROVE_PASS.status,LeaveStatus.OFF_LEAVE_NOT_PASS.status);
        }
    }

    //不可以请假的状态

}
