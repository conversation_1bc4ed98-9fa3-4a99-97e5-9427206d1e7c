package com.goodsogood.ows.common;

/**
 * <AUTHOR>
 * @date 2018-12-21 11:18:45
 * @description interface RedisConstant
 */
public interface RedisConstant {
    /**
     * #############oid_month
     * 未完成活动任务组织统计
     */
    String MEETING_TASK_UNDONE_ORG_STATS = "meeting_task_undone_org_stats";
    /**
     * 活动检查锁+id
     */
    String MEETING_RESULT_CHECK = "MEETING_RESULT_CHECK_";

    /**
     * 组织变更锁+oid
     */
    String MEETING_ORG_CHANGE = "MEETING_ORG_CHANGE_";

    /**
     * 首页数据
     */
    String MEETING_INDEX = "MEETING_INDEX";
    /**
     * 待审批的请假数量 MEETING_WAIT_APPROVAL_{regionId}_{uid}
     */
    String MEETING_WAIT_APPROVAL = "MEETING_WAIT_APPROVAL_";
    /**
     * 我待举办的活动数量MEETING_WAIT_DO_MEETING_{regionId}_{uid}
     */
    String MEETING_WAIT_DO_MEETING = "MEETING_WAIT_DO_MEETING_";
    /**
     * 移动端活动统计MEETING_H5_COUNT_{oid}
     */
    String MEETING_H5_COUNT = "MEETING_H5_COUNT_";
    /**
     * 未完成任务数量统计（不包括逾期）MEETING_TASK_UNDONE_COUNT_{oid}
     */
    String MEETING_TASK_UNDONE_COUNT = "MEETING_TASK_UNDONE_COUNT_";
    /**
     * 未完成TOPIC数量统计（不包括逾期）MEETING_TOPIC_UNDONE_COUNT_{oid}
     */
    String MEETING_TOPIC_UNDONE_COUNT = "MEETING_TOPIC_UNDONE_COUNT_";

    /**
     * 派发任务分布式锁+${planId}
     */
    String MEETING_CREATE_TASK = "MEETING_CREATE_TASK_";
}
