package com.goodsogood.ows.common;

import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对应modelId需要包含的type活动类型
 *
 * <AUTHOR> ruoyu
 * @date : 2021/11/24
 */
public enum PartyLifeMeetingEnum {

    /**
     * 民主生活会 会前学习-组织学习
     */
    BEFORE_PARTY_STUDY(3L, "民主生活会:会前学习-组织学习", (short) 3,
            Constant.LIFE_BEFORE_STAY_STATUS, Constant.LIFE_STEP_BEFORE, 1),

    /**
     * 民主生活会 征求意见-座谈会
     */
    BEFORE_PARTY_FORUM(10L, "民主生活会:征求意见-座谈会", (short) 4,
            Constant.LIFE_BEFORE_STAY_STATUS, Constant.LIFE_STEP_BEFORE, 1),

    /**
     * 民主生活会 会前准备完毕-发起民主生活会
     */
    BEFORE_DONE_SUBMIT(17L, "民主生活会:会前准备完毕-发起民主生活会", (short) 2,
            Constant.LIFE_BEFORE_STAY_STATUS, Constant.LIFE_STEP_BEFORE, 1),

    /**
     * 民主生活会 征求意见-座谈会
     */
    SUMMARY_PARTY_NOTICE(20L, "民主生活会:会议通报-会议通报", (short) 2,
            Constant.LIFE_BEFORE_DONE_STAY_STATUS, Constant.LIFE_STEP_AFTER, 1),

    /* 以下是组织生活会 */

    /**
     * 组织生活会 会前学习-组织学习
     */
    ORG_LIFE_BEFORE_PARTY_STUDY(3L, "组织生活会:会前学习-组织学习", (short) 6,
            Constant.LIFE_BEFORE_STAY_STATUS, Constant.LIFE_STEP_BEFORE, 2),

    /**
     * 组织生活会 征求意见-座谈会
     */
    ORG_LIFE_BEFORE_PARTY_FORUM(10L, "组织生活会:征求意见-座谈会", (short) 7,
            Constant.LIFE_BEFORE_STAY_STATUS, Constant.LIFE_STEP_BEFORE, 2),

    /**
     * 组织生活会 会前准备完毕-发起民主生活会
     */
    ORG_LIFE_BEFORE_DONE_SUBMIT(17L, "组织生活会:会前准备完毕-发起民主生活会", (short) 5,
            Constant.LIFE_BEFORE_STAY_STATUS, Constant.LIFE_STEP_BEFORE, 2),

    /**
     * 组织生活会 征求意见-座谈会
     */
    ORG_LIFE_SUMMARY_PARTY_NOTICE(20L, "组织生活会:会议通报-会议通报", (short) 5,
            Constant.LIFE_BEFORE_DONE_STAY_STATUS, Constant.LIFE_STEP_AFTER, 2);


    PartyLifeMeetingEnum(long modelId, String modelName, Short typeSys,
                         List<Integer> stayStatus, Integer step, Integer sourceType) {
        this.modelId = modelId;
        this.modelName = modelName;
        this.typeSys = typeSys;
        this.stayStatus = stayStatus;
        this.step = step;
        this.sourceType = sourceType;
    }

    /**
     * 民主生活会模块id
     */
    @Getter
    private final Long modelId;

    /**
     * 模块类型名称
     */
    @Getter
    private final String modelName;

    /**
     * 提交的组织生活必须包含的类型
     */
    @Getter
    private final Short typeSys;

    /**
     * 提交meeting/modelId 校验民主生活会 允许存在的状态
     */
    @Getter
    private final List<Integer> stayStatus;

    /**
     * 步骤状态
     *
     * @see Constant
     */
    @Getter
    private final Integer step;

    /**
     * 所属类型 1:民主生活会 2:组织生活会
     */
    @Getter
    private final Integer sourceType;

    public static PartyLifeMeetingEnum getByModelId(Long modelId, Integer sourceType) {
        Map<Integer, Map<Long, PartyLifeMeetingEnum>> mapByModelId = getMapByModelId();
        Map<Long, PartyLifeMeetingEnum> map = mapByModelId.get(sourceType);
        if (CollectionUtils.isEmpty(map)) {
            return null;
        }
        return map.get(modelId);
    }

    public static PartyLifeMeetingEnum getByTypeSys(Short typeSys) {
        return getMapTypeSys().get(typeSys);
    }

    /**
     * sourceType为key  map.key=modelId 封装的hash枚举
     */
    private volatile static Map<Integer, Map<Long, PartyLifeMeetingEnum>> mapModelId;

    /**
     * typeSys 为key 封装的hash 枚举
     */
    private volatile static Map<Short, PartyLifeMeetingEnum> mapTypeSys;

    public static Map<Short, PartyLifeMeetingEnum> getMapTypeSys() {
        if (null == mapTypeSys) {
            synchronized (PartyLifeMeetingEnum.class) {
                if (null == mapTypeSys) {
                    PartyLifeMeetingEnum[] values = PartyLifeMeetingEnum.values();
                    mapTypeSys = new HashMap<>(values.length);
                    for (PartyLifeMeetingEnum item : values) {
                        mapTypeSys.put(item.getTypeSys(), item);
                    }
                }
            }
        }
        return mapTypeSys;
    }

    public static Map<Integer, Map<Long, PartyLifeMeetingEnum>> getMapByModelId() {
        if (null == mapModelId) {
            synchronized (PartyLifeMeetingEnum.class) {
                if (null == mapModelId) {
                    PartyLifeMeetingEnum[] values = PartyLifeMeetingEnum.values();
                    mapModelId = new HashMap<>();
                    for (PartyLifeMeetingEnum value : values) {
                        if (mapModelId.containsKey(value.getSourceType())) {
                            mapModelId.get(value.getSourceType()).put(value.getModelId(), value);
                        } else {
                            Map<Long, PartyLifeMeetingEnum> temp = new HashMap<>();
                            mapModelId.put(value.getSourceType(), temp);
                            temp.put(value.getModelId(), value);
                        }
                    }
                }
            }
        }
        return mapModelId;
    }
}
