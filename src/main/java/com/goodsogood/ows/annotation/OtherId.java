package com.goodsogood.ows.annotation;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 校验是否是删除时的另一个id.@Id为主id
 *
 * <AUTHOR>
 * @see javax.persistence.Id
 */
@Target({METHOD, FIELD})
@Retention(RUNTIME)

public @interface OtherId {
}