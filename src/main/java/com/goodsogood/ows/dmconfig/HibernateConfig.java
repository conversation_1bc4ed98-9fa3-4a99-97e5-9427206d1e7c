package com.goodsogood.ows.dmconfig;

import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HibernateConfig {
    @Bean
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer() {
        return props -> {
            props.put("hibernate.temp.use_jdbc_metadata_defaults",  false);
            props.put("hibernate.jdbc.lob.non_contextual_creation",  true);
        };
    }
}