package com.goodsogood.ows.dmconfig;

import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.Properties;

/**
 * StatementHandler拦截器
 * 专门拦截StatementHandler的prepare方法来转换SQL
 * 这是MyBatis执行SQL的关键点
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Component
@Log4j2
@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class StatementHandlerInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        log.info("🚀🚀🚀 StatementHandler拦截器被调用！🚀🚀🚀");
        
        try {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            
            // 获取BoundSql
            BoundSql boundSql = statementHandler.getBoundSql();
            String originalSql = boundSql.getSql();
            
            log.info("StatementHandler拦截到SQL: {}", originalSql.trim());
            
            // 检查是否需要转换
            if (SqlCompatibilityUtil.needsConversion(originalSql)) {
                log.info("SQL需要转换");
                
                // 转换SQL
                String convertedSql = SqlCompatibilityUtil.convertSql(originalSql);
                
                if (!originalSql.equals(convertedSql)) {
                    log.info("🔄 SQL转换完成！");
                    log.info("原SQL: {}", originalSql.trim());
                    log.info("转换后: {}", convertedSql.trim());

                    // 直接修改BoundSql中的SQL字符串
                    modifySqlInBoundSql(boundSql, convertedSql);

                    log.info("✅ SQL已成功替换");
                } else {
                    log.info("SQL转换后无变化");
                }
            } else {
                log.debug("SQL不需要转换");
            }
            
        } catch (Exception e) {
            log.error("StatementHandler拦截器处理过程中发生错误: {}", e.getMessage(), e);
            // 发生错误时继续使用原始SQL
        }
        
        // 继续执行原方法
        Object result = invocation.proceed();
        
        log.info("🚀🚀🚀 StatementHandler拦截器执行完成！🚀🚀🚀");
        
        return result;
    }

    /**
     * 直接修改BoundSql中的SQL字符串
     */
    private void modifySqlInBoundSql(BoundSql boundSql, String newSql) {
        try {
            Field sqlField = BoundSql.class.getDeclaredField("sql");
            sqlField.setAccessible(true);
            sqlField.set(boundSql, newSql);
            log.info("成功修改BoundSql中的SQL");
        } catch (Exception e) {
            log.error("修改BoundSql中的SQL失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public Object plugin(Object target) {
        log.debug("StatementHandlerInterceptor.plugin被调用，target: {}", target.getClass().getName());
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        log.info("StatementHandlerInterceptor.setProperties被调用");
        log.info("StatementHandler拦截器初始化完成");
    }
}
