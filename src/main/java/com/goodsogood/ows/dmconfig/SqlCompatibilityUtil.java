package com.goodsogood.ows.dmconfig;

import lombok.extern.log4j.Log4j2;

import java.util.regex.Pattern;

/**
 * SQL兼容性工具类
 * 用于将MySQL语法转换为达梦数据库兼容的语法
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Log4j2
public class SqlCompatibilityUtil {

    // 常用的MySQL到达梦SQL转换规则
    private static final Pattern LIMIT_PATTERN = Pattern.compile("\\s+LIMIT\\s+(\\d+)(?:\\s*,\\s*(\\d+))?", Pattern.CASE_INSENSITIVE);
    private static final Pattern BACKTICK_PATTERN = Pattern.compile("`([^`]+)`");
    private static final Pattern NOW_PATTERN = Pattern.compile("\\bNOW\\(\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern SYSDATE_PATTERN = Pattern.compile("\\bSYSDATE\\(\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern UNIX_TIMESTAMP_PATTERN = Pattern.compile("\\bUNIX_TIMESTAMP\\s*\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern UNIX_TIMESTAMP_NOW_PATTERN = Pattern.compile("\\bUNIX_TIMESTAMP\\s*\\(\\s*\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern IFNULL_PATTERN = Pattern.compile("\\bIFNULL\\s*\\(([^,]+),([^)]+)\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern CONCAT_PATTERN = Pattern.compile("\\bCONCAT\\s*\\(", Pattern.CASE_INSENSITIVE);
    private static final Pattern DATE_FORMAT_PATTERN = Pattern.compile("\\bDATE_FORMAT\\s*\\(([^,]+),\\s*'([^']+)'\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern AUTO_INCREMENT_PATTERN = Pattern.compile("\\bAUTO_INCREMENT\\b", Pattern.CASE_INSENSITIVE);
    private static final Pattern ENGINE_PATTERN = Pattern.compile("\\bENGINE\\s*=\\s*\\w+", Pattern.CASE_INSENSITIVE);
    private static final Pattern CHARSET_PATTERN = Pattern.compile("\\bCHARSET\\s*=\\s*\\w+", Pattern.CASE_INSENSITIVE);

    /**
     * 转换SQL语句，使其兼容达梦数据库
     * 
     * @param originalSql 原始MySQL SQL语句
     * @return 转换后的达梦兼容SQL语句
     */
    public static String convertSql(String originalSql) {
        if (originalSql == null || originalSql.trim().isEmpty()) {
            return originalSql;
        }

        String convertedSql = originalSql;
        
        try {
            // 1. 处理LIMIT语法 - MySQL: LIMIT offset, count -> 达梦: LIMIT count OFFSET offset
            convertedSql = convertLimit(convertedSql);
            
            // 2. 移除反引号 - MySQL使用反引号，达梦使用双引号或不使用
            convertedSql = convertBackticks(convertedSql);
            
            // 3. 转换时间函数
            convertedSql = convertTimeFunctions(convertedSql);
            
            // 4. 转换字符串函数
            convertedSql = convertStringFunctions(convertedSql);
            
            // 5. 转换其他函数
            convertedSql = convertOtherFunctions(convertedSql);
            
            // 6. 处理DDL语句
            convertedSql = convertDdlStatements(convertedSql);
            
            // 记录转换日志（仅在SQL发生变化时）
            if (!originalSql.equals(convertedSql)) {
                log.debug("SQL转换: {} -> {}", originalSql.trim(), convertedSql.trim());
            }
            
        } catch (Exception e) {
            log.warn("SQL转换失败，使用原始SQL: {}", e.getMessage());
            return originalSql;
        }
        
        return convertedSql;
    }

    /**
     * 转换LIMIT语法
     * MySQL: LIMIT 10, 20 或 LIMIT 20 OFFSET 10
     * 达梦: LIMIT 20 OFFSET 10
     */
    private static String convertLimit(String sql) {
        return LIMIT_PATTERN.matcher(sql).replaceAll(matchResult -> {
            String group1 = matchResult.group(1); // 第一个数字
            String group2 = matchResult.group(2); // 第二个数字（可能为null）
            
            if (group2 != null) {
                // MySQL: LIMIT offset, count -> 达梦: LIMIT count OFFSET offset
                return " LIMIT " + group2 + " OFFSET " + group1;
            } else {
                // MySQL: LIMIT count -> 达梦: LIMIT count
                return " LIMIT " + group1;
            }
        });
    }

    /**
     * 转换反引号为双引号或移除
     */
    private static String convertBackticks(String sql) {
        // 移除反引号，达梦数据库通常不需要引号
        // 但如果表名或字段名是关键字，可能需要双引号
        return BACKTICK_PATTERN.matcher(sql).replaceAll("$1");
    }

    /**
     * 转换时间函数
     */
    private static String convertTimeFunctions(String sql) {
        // NOW() -> SYSDATE
        sql = NOW_PATTERN.matcher(sql).replaceAll("SYSDATE");
        
        // SYSDATE() -> SYSDATE (达梦中SYSDATE不需要括号)
        sql = SYSDATE_PATTERN.matcher(sql).replaceAll("SYSDATE");
        
        // UNIX_TIMESTAMP(date) -> EXTRACT(EPOCH FROM date)
        sql = UNIX_TIMESTAMP_PATTERN.matcher(sql).replaceAll("EXTRACT(EPOCH FROM $1)");
        
        // UNIX_TIMESTAMP() -> EXTRACT(EPOCH FROM SYSDATE)
        sql = UNIX_TIMESTAMP_NOW_PATTERN.matcher(sql).replaceAll("EXTRACT(EPOCH FROM SYSDATE)");
        
        // DATE_FORMAT(date, format) -> TO_CHAR(date, format)
        sql = DATE_FORMAT_PATTERN.matcher(sql).replaceAll("TO_CHAR($1, '$2')");
        
        return sql;
    }

    /**
     * 转换字符串函数
     */
    private static String convertStringFunctions(String sql) {
        // IFNULL(expr1, expr2) -> NVL(expr1, expr2)
        sql = IFNULL_PATTERN.matcher(sql).replaceAll("NVL($1,$2)");
        
        // CONCAT函数在达梦中也支持，但可以使用||操作符
        // 这里保持CONCAT不变，因为达梦支持
        
        return sql;
    }

    /**
     * 转换其他函数
     */
    private static String convertOtherFunctions(String sql) {
        // 这里可以添加其他函数的转换规则
        return sql;
    }

    /**
     * 转换DDL语句
     */
    private static String convertDdlStatements(String sql) {
        // 移除AUTO_INCREMENT关键字，达梦使用IDENTITY
        sql = AUTO_INCREMENT_PATTERN.matcher(sql).replaceAll("IDENTITY(1,1)");
        
        // 移除ENGINE子句
        sql = ENGINE_PATTERN.matcher(sql).replaceAll("");
        
        // 移除CHARSET子句
        sql = CHARSET_PATTERN.matcher(sql).replaceAll("");
        
        return sql;
    }

    /**
     * 检查SQL是否需要转换
     */
    public static boolean needsConversion(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }
        
        String upperSql = sql.toUpperCase();
        
        // 检查是否包含需要转换的MySQL特有语法
        return upperSql.contains("LIMIT") ||
               sql.contains("`") ||
               upperSql.contains("NOW()") ||
               upperSql.contains("UNIX_TIMESTAMP") ||
               upperSql.contains("IFNULL") ||
               upperSql.contains("DATE_FORMAT") ||
               upperSql.contains("AUTO_INCREMENT") ||
               upperSql.contains("ENGINE=") ||
               upperSql.contains("CHARSET=");
    }

    /**
     * 获取SQL类型
     */
    public static SqlType getSqlType(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return SqlType.UNKNOWN;
        }
        
        String trimmedSql = sql.trim().toUpperCase();
        
        if (trimmedSql.startsWith("SELECT")) {
            return SqlType.SELECT;
        } else if (trimmedSql.startsWith("INSERT")) {
            return SqlType.INSERT;
        } else if (trimmedSql.startsWith("UPDATE")) {
            return SqlType.UPDATE;
        } else if (trimmedSql.startsWith("DELETE")) {
            return SqlType.DELETE;
        } else if (trimmedSql.startsWith("CREATE")) {
            return SqlType.CREATE;
        } else if (trimmedSql.startsWith("ALTER")) {
            return SqlType.ALTER;
        } else if (trimmedSql.startsWith("DROP")) {
            return SqlType.DROP;
        } else {
            return SqlType.OTHER;
        }
    }

    /**
     * SQL类型枚举
     */
    public enum SqlType {
        SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, DROP, OTHER, UNKNOWN
    }
}
