package com.goodsogood.ows.dmconfig;

import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.plugin.Interceptor;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis拦截器配置
 * 使用ConfigurationCustomizer来确保拦截器在正确的时机注册
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Configuration
@Log4j2
public class MyBatisInterceptorConfig {


    @Autowired
    private StatementHandlerInterceptor statementHandlerInterceptor;

    /**
     * 使用ConfigurationCustomizer来注册拦截器
     * 这种方式可以确保在SqlSessionFactory创建时就注册拦截器
     */
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return new ConfigurationCustomizer() {
            @Override
            public void customize(org.apache.ibatis.session.Configuration configuration) {
                log.info("=== ConfigurationCustomizer被调用，开始注册拦截器 ===");


                // 注册StatementHandler拦截器（最重要的）
                log.info("注册StatementHandlerInterceptor...");
                configuration.addInterceptor(statementHandlerInterceptor);


                log.info("拦截器注册完成，当前拦截器数量: {}", configuration.getInterceptors().size());
                
                // 打印所有拦截器信息
                for (int i = 0; i < configuration.getInterceptors().size(); i++) {
                    Interceptor interceptor = configuration.getInterceptors().get(i);
                    log.info("拦截器 #{}: {}", i + 1, interceptor.getClass().getName());
                }
            }
        };
    }

    /**
     * 备用方案：直接创建拦截器数组
     * 如果ConfigurationCustomizer不工作，可以尝试这种方式
     */
    @Bean
    public Interceptor[] interceptors() {
        log.info("=== 创建拦截器数组 ===");
        return new Interceptor[]{
            statementHandlerInterceptor
        };
    }
}
