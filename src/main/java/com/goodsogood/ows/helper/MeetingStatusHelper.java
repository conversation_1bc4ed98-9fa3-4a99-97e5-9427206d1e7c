package com.goodsogood.ows.helper;

import com.goodsogood.ows.common.MeetingCanstant;
import org.joda.time.DateTime;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>Description: 活动状态转换</p>
 *
 * <AUTHOR>
 * @date 2018/11/7 10:01
 */
public class MeetingStatusHelper {

    public enum myMeetingStatus {
        SOON_START((short) 1, "待举办", Arrays.asList((short) 3, (short) 9)), // 1：待举办;展示状态为“活动待举办”、“已取消”的活动
        MEETING((short) 2, "进行中", Arrays.asList((short) 4, (short) 5, (short) 6)),// 2：进行中 展示状态为“待填报结果”、“填报审批中”、“填报未通过”的活动
        END((short) 3, "结束", Arrays.asList((short) 7, (short) 8, (short) 12, (short) 13)),;// 3：结束 展示状态为“已提交”、“退回”、“检查通过”、“待复核”的活动

        private Short fmStatus;
        private String remark;
        private List<Short> meetingStatus;

        public Short getFmStatus() {
            return fmStatus;
        }

        public void setFmStatus(Short fmStatus) {
            this.fmStatus = fmStatus;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public List<Short> getMeetingStatus() {
            return meetingStatus;
        }

        public void setMeetingStatus(List<Short> meetingStatus) {
            this.meetingStatus = meetingStatus;
        }


        myMeetingStatus(Short fmStatus, String remark, List<Short> meetingStatus) {
            this.fmStatus = fmStatus;
            this.remark = remark;
            this.meetingStatus = meetingStatus;
        }
    }

    /**
     * 状态转换为前端状态
     * 前端：
     * 1 发起审批中 活动发起后本组织内部审批中 活动详情、取消活动、修改
     * 2 发起未通过 活动发起后本组织内部审批不通过 活动详情、取消活动、修改
     * 3 活动待举办 活动已发起但尚未到举办时间 活动详情、取消活动、添加人员、发送活动通知
     * 4 待填报结果 状态为“活动待举办”的活动，到举办时间时，状态自动置为待填报结果  活动详情、取消活动、填写纪实情况表
     * 5 填报审批中 活动填报结果在本组织内部审批中 活动详情、填写纪实情况表
     * 6 填报未通过 活动填报结果在本组织内部审批未通过 活动详情、填写纪实情况表
     * 7 已提交 活动结果已提交到上级组织  活动详情
     * 8 退回  活动结果被上级组织退回 活动详情、填写纪实情况表、退回记录
     * 9 活动已取消 活动被取消后状态置为“活动已取消” 活动详情、删除
     * 12  待复核 存在退回记录的活动结果再次提交到上级组织  活动详情
     * 13 检查通过  活动结果被上级组织检查通过处理 活动详情
     * <p>
     * 数据库状态
     * 活动状态
     * 1：发起审批中
     * 2：发起未通过
     * 3：活动待举办（待填报结果）
     * 5：填报审查中（第一次）
     * 6：填报未通过（第一次）
     * 7：已提交
     * 8：退回
     * 9：活动已取消
     * 10：填报审查中（第一次之后）
     * 11：填报未通过（第一次之后）
     * 12：待复核（第二次提交并且审批通过）
     * 13：通过（第一次考核就通过）
     * 14：通过（第一次考核被退回后的通过）
     *
     * @param startTime 活动开始时间 为null时不会对status=3作转换
     */
    public static Short convertToFormStatus(Short status, Date startTime) {
        if (status == null) {
            return null;
        } else if (status == MeetingCanstant.MEETING_STATUS_SUBMIT_MORE.shortValue()) {// 填报审查中
            status = MeetingCanstant.MEETING_STATUS_SUBMIT_FIRST.shortValue();
        } else if (status == MeetingCanstant.MEETING_STATUS_SUBMIT_MORE_NOT_PSSS.shortValue()) {// 填报未通过
            status = MeetingCanstant.MEETING_STATUS_SUBMIT_FIRST_NOT_PASS.shortValue();
        } else if (status == MeetingCanstant.MEETING_STATUS_PASS_MORE.shortValue()) {//通过
            status = MeetingCanstant.MEETING_STATUS_PASS_FIRST.shortValue();
        } else if (startTime != null && status == 3 && startTime.getTime() < DateTime.now().toDate().getTime()) {//待举办；如果已经过了开会时间，为4->待填报结果
            status = MeetingCanstant.MEETING_STATUS_APPROVAL.shortValue();
        }
        return status;
    }

    public static Short convertToMyMeetingFormStatus(short status) {
        for (myMeetingStatus me : myMeetingStatus.values()) {
            if (me.getMeetingStatus().contains(status)) {
                status = me.getFmStatus();
                break;
            }
        }
        return status;
    }
}
