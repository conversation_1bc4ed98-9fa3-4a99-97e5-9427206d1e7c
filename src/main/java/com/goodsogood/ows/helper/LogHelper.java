package com.goodsogood.ows.helper;

import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.configuration.AspectConfiguration;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 生成tracker_id
 *
 * <p>由于定时任务没有走网关，所以无法传递tracker_id，在定时任务出问题后，无法定位到问题
 *
 * <p>在定时任务/异步方法重置下上下文
 *
 * <AUTHOR>
 * @date 2018-11-22 11:29
 * @since 1.0.3
 */
@Component
@Log4j2
public class LogHelper {

     private static final ThreadLocal<LogAspectHelper.SSLog> ssLogThreadLocal = new ThreadLocal<>();
     /**
      * 日志配置
      */
     private static AspectConfiguration aspectConfiguration;

     @Autowired
     public void setStaticFields(AspectConfiguration aspectConfiguration) {
          LogHelper.aspectConfiguration = aspectConfiguration;
     }

     /**
      * 定时任务日志
      *
      * @param schedulerName 任务名称
      * @param runnable      执行内容
      */
     public static void schedulerLog(String schedulerName, Runnable runnable) {
          try {
               LogHelper.reSetContext();
               if (StringUtils.isBlank(schedulerName)) {
                    schedulerName = "scheduler";
               }
               log.info("定时任务【" + schedulerName + "】开始！");
               runnable.run();
               log.info("定时任务【" + schedulerName + "】结束！");
          } finally {
               // 清除logger的上下文
               ThreadContext.clearAll();
          }
     }

     /**
      * 异步方法日志
      *
      * @param runnable 执行内容
      */
     public static void asyncLog(LogAspectHelper.SSLog ssLog, Runnable runnable) {
          try {
               LogHelper.reSetContext(ssLog);
               runnable.run();
          } finally {
               // 清除logger的上下文
               ThreadContext.clearAll();
          }
     }

     /**
      * 获取上下文数据
      *
      * @return SSLog
      */
     public static LogAspectHelper.SSLog getSSLog() {
          return ssLogThreadLocal.get();
     }

     /**
      * 重新设置下上下文
      *
      * @since 3.0.0
      */
     private static void reSetContext() {
          reSetContext(null);
     }

     /**
      * 重新设置下上下文
      *
      * @param ssLog LogAspectHelper.SSLog 日志信息类
      * @since 1.0.4
      */
     private static void reSetContext(LogAspectHelper.SSLog ssLog) {
          // 重新设置下上下文
          LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
          if (null == ssLog) {
               ssLog = getSSlog(helper);
          }
          ssLogThreadLocal.set(ssLog);
          helper.reSetContext(ssLog);
     }

     /**
      * 获取日志输出对象
      *
      * @param helper 日志切面帮助类
      * @since 3.0.0
      */
     private static LogAspectHelper.SSLog getSSlog(LogAspectHelper helper) {
          LogAspectHelper.SSLog ssLog = HttpLogAspect.getSSLog();
          if (ssLog == null) {
               ssLog =
                       helper.ssLogBulder(
                               System.currentTimeMillis(),
                               getThreadContextVal("tracker_id", UUID.randomUUID().toString()),
                               getThreadContextVal("span_id", aspectConfiguration.monitorProps().getSpanId()),
                               getThreadContextVal("p_span_id", "Scheduledr|Async"),
                               getThreadContextVal("node_name", aspectConfiguration.monitorProps().getNodeName()),
                               getThreadContextVal("type", "Scheduledr|Async"),
                               getThreadContextVal("name", "Scheduledr|Async"),
                               getThreadContextVal("env", "Scheduledr|Async"));
          }
          return ssLog;
     }


     public static LogAspectHelper.SSLog getSSlog(LogAspectHelper helper,String logName) {
          return helper.ssLogBulder(
                  System.currentTimeMillis(),
                  UUID.randomUUID().toString(),
                  aspectConfiguration.monitorProps().getSpanId(),
                  logName + " pSpanId",
                  aspectConfiguration.monitorProps().getNodeName(),
                  logName + " type",
                  aspectConfiguration.monitorProps().getAppName(),
                  aspectConfiguration.monitorProps().getPort()+"");
     }

     private static String getThreadContextVal(String key, String defaultVal) {
          String val = ThreadContext.get(key);
          if (StringUtils.isBlank(val)) {
               return defaultVal;
          }
          return val;
     }
}
