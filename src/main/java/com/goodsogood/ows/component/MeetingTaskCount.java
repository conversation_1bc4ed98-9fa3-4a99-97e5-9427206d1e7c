package com.goodsogood.ows.component;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 会议统计配置
 *
 * <AUTHOR>
 * @date 2019/1/16
 */
@Data
@Component
@ConfigurationProperties(prefix = MeetingTaskCount.MEETING_TASK_COUNT_PREFIX)
public class MeetingTaskCount {

    public final static String MEETING_TASK_COUNT_PREFIX = "meeting.task";

    private List<Count> counts;

    private AtomicBoolean isSort = new AtomicBoolean(false);

    public List<Count> sort() {
        if (!isSort.getAndSet(true)) {
            Collections.sort(counts, new Comparator<Count>() {
                @Override
                public int compare(Count o1, Count o2) {
                    return 0;
                }
            });
        }
        return counts;
    }

    @Data
    public static class Count {
        /**
         * 排序等级
         */
        private int level;

        /**
         * 会议类型名称
         */
        private String type;

        /**
         * 统计类型：0-查询本月，1-查询本季度
         */
        private int countType;
    }
}
