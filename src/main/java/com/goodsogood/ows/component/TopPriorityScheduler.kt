package com.goodsogood.ows.component

import com.goodsogood.ows.service.TopPriorityService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

/**
 * 爬取第一议题的定时器
 * <AUTHOR>
 * @date 2024/1/4
 * @description class TopPriorityScheduler
 */
@Component
class TopPriorityScheduler(val topPriorityService: TopPriorityService) {
    private val log = LoggerFactory.getLogger(TopPriorityScheduler::class.java)

    @Value("\${scheduler.top-priority-spider.run}")
    private var run: Boolean = false

    @Scheduled(cron = "\${scheduler.top-priority-spider.cron}")
    fun runner() {
        if (run) {
            topPriorityService.addBySpider(null)
        }
    }
}