package com.goodsogood.ows.component;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.Config;
import com.goodsogood.ows.configuration.SchedulerConfiguration;
import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.service.MeetingService;
import com.goodsogood.ows.utils.redisLock.RedisLockUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 有日程的活动结束后同步钉钉日程的签到状态定时任务
 * <AUTHOR>
 * @create 2022-03-03
 **/
@Component
@Log4j2
public class DingEventSyncScheduler {

    private final LogHelper logHelper;
    private final StringRedisTemplate redisTemplate;
    private final SchedulerConfiguration schedulerConfiguration;
    private final MeetingService meetingService;
    private final String LOGTXT="有日程的活动结束后同步钉钉日程的签到状态定时任务";

    @Autowired
    public DingEventSyncScheduler(LogHelper logHelper, StringRedisTemplate redisTemplate, SchedulerConfiguration schedulerConfiguration, MeetingService meetingService) {
        this.logHelper = logHelper;
        this.redisTemplate = redisTemplate;
        this.schedulerConfiguration = schedulerConfiguration;
        this.meetingService = meetingService;
        log.info(LOGTXT+"初始化成功！");
    }

    @Scheduled(cron = "${scheduler.dingEventSync.cron}")
    public void payGainScore() {
        if (!schedulerConfiguration.getDingEventSync().getRunFlag()) {
            return;
        }
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        LogAspectHelper.SSLog ssLog = logHelper.getSSlog(helper, "[有日程的活动结束后同步钉钉日程的签到状态定时任务]");
        helper.reSetContext(ssLog);
        //循环执行各区县定时器任务
            String requestId = UUID.randomUUID().toString();
            Long regionId = 19L;
            try {
                //获取分布式锁
                boolean lock = RedisLockUtil.tryGetDistributedLock(redisTemplate, Config.DING_EVENT_SYNC_LOCK_PREFIX+"_"+regionId, requestId, Config.SCHEDULER_LOCK_EXPIRE);
                if (lock) {
                    meetingService.dingEventSync(regionId);
                }
            } catch (Exception e) {
                log.error(LOGTXT+"报错! ", e);
            } finally {
                //解锁
                RedisLockUtil.releaseDistributedLock(redisTemplate, Config.DING_EVENT_SYNC_LOCK_PREFIX+"_"+regionId, requestId);
            }
    }
}
