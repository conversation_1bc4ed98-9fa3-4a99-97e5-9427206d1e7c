package com.goodsogood.ows.component;

import com.goodsogood.ows.service.VideoConferenceService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 视频会议状态-定时任务
 *
 */
@Component
@Log4j2
public class VideoConferenceScheduler {

    @Value("${scheduler.video-conference.run}")
    private boolean run;

    private final VideoConferenceService videoConferenceService;


    @Autowired
    public VideoConferenceScheduler(VideoConferenceService videoConferenceService) {
        this.videoConferenceService = videoConferenceService;
    }

    /**
     * 定时检测视频会议状态
     */
    @Scheduled(cron = "${scheduler.video-conference.cron}")
    public void videoConferenceStatus(){
        if (run){
            log.info("视频会议状态-定时任务开始："+System.currentTimeMillis());
            videoConferenceService.queryConferenceInfoBatchList(86L);
            log.info("视频会议状态-定时任务结束："+System.currentTimeMillis());
        }
    }
}
