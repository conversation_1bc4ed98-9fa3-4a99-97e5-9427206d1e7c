package com.goodsogood.ows.component;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingOrgChangeLogMapper;
import com.goodsogood.ows.model.db.MeetingOrgChangeLogEntity;
import com.goodsogood.ows.model.vo.MeetingOrgDebriefReviewStatisticsQueryVO;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.service.MeetingOrgDebriefReviewService;
import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.RestTemplateHelper;
import com.goodsogood.ows.service.UserCenterService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @describe 组织统计定时器
 * @date 2020-01-03
 */
@Component
@Log4j2
public class MeetingOrgDebriefReviewScheduler {

    public final static String MEETING_ORG_DEBRIEF_REVIEW_STATISTICS = "MEETING_ORG_DEBRIEF_REVIEW_STATISTICS";

    @Value("${scheduler.org-debrief-review.run}")
    private boolean run;

    private final MeetingOrgDebriefReviewService meetingOrgDebriefReviewService;
    private final StringRedisTemplate redisTemplate;
    private final UserCenterService userCenterService;
    private final MeetingOrgChangeLogMapper meetingOrgChangeLogMapper;
    private final ObjectMapper objectMapper;
    private final RegionService regionService;

    @Autowired
    public MeetingOrgDebriefReviewScheduler(MeetingOrgDebriefReviewService meetingOrgDebriefReviewService, StringRedisTemplate redisTemplate, UserCenterService userCenterService, MeetingOrgChangeLogMapper meetingOrgChangeLogMapper, ObjectMapper objectMapper, RegionService regionService) {
        this.meetingOrgDebriefReviewService = meetingOrgDebriefReviewService;
        this.redisTemplate = redisTemplate;
        this.userCenterService = userCenterService;
        this.meetingOrgChangeLogMapper = meetingOrgChangeLogMapper;
        this.objectMapper = objectMapper;
        this.regionService = regionService;
    }


    @Scheduled(cron = "${scheduler.org-debrief-review.cron}")
    public void executeScheduled() {
        if (!run) {
            return;
        }
        log.debug("开始基层述职评议结果统计定时器");
        List<Region.RegionData> regionDataList = regionService.getRegions();
        regionDataList.forEach(regionData -> {
            if (regionData.getRegionId() != null) {
                Long rootOrgId = regionService.bindingOrgId(regionData.getRegionId());
                if (rootOrgId != null) {
                    HeaderHelper.SysHeader sysHeader = RestTemplateHelper.getRegionIdLogHeader(regionData.getRegionId());
                    List<OrganizationBase> regionOrgList = userCenterService.findAllChildOrgInclude(sysHeader, rootOrgId);
                    if (CollectionUtils.isNotEmpty(regionOrgList)) {
                        Map<Long, HashSet<Org>> pidOrgMap = new HashMap<>(500);
                        Org root = new Org();

                        for (OrganizationBase organizationBase : regionOrgList) {
                            if (organizationBase.getOrgId().equals(rootOrgId)) {
                                root = new Org(organizationBase.getOrgId(), organizationBase.getOrgName(), organizationBase.getParentId(), null, new HashSet<>());
                            }

                            Org org = new Org(organizationBase.getOrgId(), organizationBase.getOrgName(), organizationBase.getParentId(), null, new HashSet<>());

                            if (!pidOrgMap.containsKey(org.getParentId())) {
                                pidOrgMap.put(org.getParentId(), new HashSet<>());
                            }
                            pidOrgMap.get(org.getParentId()).add(org);

                        }

                        rootOrg(pidOrgMap, root);
                        generateChildrenIds(root);

                        generateCache(rootOrgId, root);
                        log.debug("基层述职评议结果统计定时器执行完毕");

                        List<MeetingOrgChangeLogEntity> listUpdateOrg = meetingOrgChangeLogMapper.listUpdateOrg(LocalDate.now().minusDays(1).toString());

                        if (!CollectionUtils.isEmpty(listUpdateOrg)) {
                            log.debug("基层述职评议结果统计更新组织level size:{}", listUpdateOrg.size());


                            for (MeetingOrgChangeLogEntity meetingOrgChangeLogEntity : listUpdateOrg) {
                                this.meetingOrgDebriefReviewService.updateCurrentOrgLevelByOrgId(meetingOrgChangeLogEntity.getOrgId(), meetingOrgChangeLogEntity.getOrgLevel());
                            }
                        }
                    }
                }
            }
        });


    }

    private void generateCache(Long rootOrgId, Org root) {
        List<MeetingOrgDebriefReviewStatisticsQueryVO> list = new ArrayList<>(root.getChildren().size());

        for (Org child : root.getChildren()) {

            MeetingOrgDebriefReviewStatisticsQueryVO vo = new MeetingOrgDebriefReviewStatisticsQueryVO()
                    .setOrgId(child.getOrgId())
                    .setOrgName(child.getOrgName())
                    .setBasisOrgNum(child.getChildrenIds().size());

            list.add(vo);

        }


        try {
            redisTemplate.opsForHash().put(MEETING_ORG_DEBRIEF_REVIEW_STATISTICS, String.valueOf(rootOrgId), objectMapper.writeValueAsString(list));
        } catch (JsonProcessingException e) {
            log.error("基层述职评议结果统计执行失败", e);
        }
    }

    public void rootOrg(Map<Long, HashSet<Org>> pidOrgMap, Org pOrg) {


        for (Map.Entry<Long, HashSet<Org>> longOrgEntry : pidOrgMap.entrySet()) {

            if (longOrgEntry.getKey().equals(pOrg.getOrgId())) {

                if (pOrg.getChildren() == null) {
                    pOrg.setChildren(new ArrayList<>());
                }

                longOrgEntry.getValue().forEach(v -> rootOrg(pidOrgMap, v));
                pOrg.getChildren().addAll(longOrgEntry.getValue());

            }
        }

    }

    private Set<Long> generateChildrenIds(Org pOrg) {
        //先加入自己的id
        pOrg.getChildrenIds().add(pOrg.getOrgId());

        if (pOrg.getChildren() != null) {

            for (Org child : pOrg.getChildren()) {
                pOrg.getChildrenIds().add(child.getOrgId());
                generateChildrenIds(child);
                pOrg.getChildrenIds().addAll(child.getChildrenIds());
            }

        }

        return null;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class Org {
        private Long orgId;
        private String orgName;
        private Long parentId;
        private List<Org> children;
        //下级包含自己的orgIds
        @JsonIgnore
        private Set<Long> childrenIds;
    }
}
