package com.goodsogood.ows.component;

import com.goodsogood.ows.service.RegionService;
import com.goodsogood.ows.service.RestTemplateHelper;
import com.goodsogood.ows.service.UserCommentStatisticsService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 民主评议统计定时任务
 * @date 2020/1/9
 */
@Component
@Log4j2
public class UserCommentStatisticsScheduler {

    @Value("${scheduler.user-comment.run}")
    private boolean run;

    private final UserCommentStatisticsService userCommentStatisticsService;
    private final RegionService regionService;

    @Autowired
    public UserCommentStatisticsScheduler(UserCommentStatisticsService userCommentStatisticsService, RegionService regionService) {
        this.userCommentStatisticsService = userCommentStatisticsService;
        this.regionService = regionService;
    }

    @Scheduled(cron = "${scheduler.user-comment.cron}")
    public void userCommentStatisticsScheduler(){
        if (run) {
            log.debug("党员民主评议统计开始 -> [{}]", new Date());
            regionService.getRegions().forEach(regionData -> {
                this.userCommentStatisticsService
                        .generateUserCommentStatistics(RestTemplateHelper.getRegionIdLogHeader(regionData.getRegionId()), null);
            });
            log.debug("党员民主评议统计结束 -> [{}]", new Date());
        }
    }

}
