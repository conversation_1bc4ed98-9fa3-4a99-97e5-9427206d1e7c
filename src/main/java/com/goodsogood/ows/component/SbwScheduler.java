package com.goodsogood.ows.component;

import com.goodsogood.ows.mapper.SbwNewTaskMapper;
import com.goodsogood.ows.mapper.SbwShiftTaskMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/***
 * 南岸区网信办定时任务
 * <AUTHOR>
 * @date 2021.0803
 */
@Component
@Log4j2
public class SbwScheduler {

    @Value("${scheduled.sbwEnable}")
    private boolean enable;

    private final SbwNewTaskMapper sbwNewTaskMapper;
    private final SbwShiftTaskMapper sbwShiftTaskMapper;

    @Autowired
    public SbwScheduler(SbwNewTaskMapper sbwNewTaskMapper, SbwShiftTaskMapper sbwShiftTaskMapper) {
        this.sbwNewTaskMapper = sbwNewTaskMapper;
        this.sbwShiftTaskMapper = sbwShiftTaskMapper;
    }

    /**
     * 定时检测修改任务状态
     */
    @Scheduled(cron = "${scheduled.sbw}")
    public void taskStatus(){
        if (enable){
            log.debug("开始定时任务，更新任务状态");
            sbwShiftTaskMapper.taskStatus();
            sbwNewTaskMapper.taskStatus();
        }
    }
}
