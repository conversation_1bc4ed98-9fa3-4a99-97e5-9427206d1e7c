package com.goodsogood.ows.component

import com.goodsogood.ows.service.CommentService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDateTime

@Component
class CommentScheduler(@Autowired val commentService: CommentService) {

    private val log = LoggerFactory.getLogger(CommentScheduler::class.java)

    @Value("\${scheduler.comment.run}")
    private val enable = false

    @Value("\${scheduler.comment.region-id}")
    private val regionId: Long = 0

    @Scheduled(cron = "\${scheduler.comment.cron}")
    fun executeScheduled() {
        val year = LocalDateTime.now().year - 1
        if (enable) {
            log.debug("【民主评议】开始自动生成[${year}]年度<未开启民主评议的组织>数据 -- start")
            commentService.autoGeneraComment(regionId, year)
        }
    }
}