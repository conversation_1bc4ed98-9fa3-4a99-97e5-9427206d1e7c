package com.goodsogood.ows.component

import com.goodsogood.ows.service.MeetingWorkPointService
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @date 2023/10/10
 * @description class CreateMeetingWorkPointScheduler
 */
@Component
class CreateMeetingWorkPointScheduler(val meetingWorkPointService: MeetingWorkPointService) {

    @Value("\${scheduler.create-meeting-work-point.run}")
    private val enable = false

    @Value("\${scheduler.create-meeting-work-point.region-id}")
    private val regionId: Long = 0

    @Scheduled(cron = "\${scheduler.create-meeting-work-point.cron}")
    fun executeScheduled() {
        if (enable) {
            meetingWorkPointService.createByOwnerId(regionId)
        }
    }
}