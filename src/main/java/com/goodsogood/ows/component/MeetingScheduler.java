package com.goodsogood.ows.component;

import com.goodsogood.ows.common.pojo.MeetingPushParam;
import com.goodsogood.ows.configuration.SchedulerConfiguration;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.model.db.MeetingTaskEntity;
import com.goodsogood.ows.service.*;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 纪实系统定时任务所有的入口，到时候单个的任务，由service调用
 *
 * <AUTHOR>
 * @create 2018-10-19 8:56
 */
@Component
@Log4j2
public class MeetingScheduler {
    @Value("${scheduler.execute}")
    private boolean execute;
//    @Value("${scheduler.statsMeetingTask.run}")
//    private boolean statsMeetingTaskRun = false;

    @Value("${scheduler.refreshCacheOfIndexCollect.run}")
    private boolean refreshCacheOfIndexCollectRun;

    @Value("${scheduler.committeeAndGroupScore.run}")
    private boolean committeeAndGroupScoreRun;

    @Value("${scheduler.unfinishedGroupScore.run}")
    private boolean unfinishedGroupScoreRun;

    @Value("${scheduler.checkTaskComplete}")
    private boolean checkTaskComplete;

    private final MeetingTaskService meetingTaskService;
    private final MeetingNoticeService meetingNoticeService;
    private final IndexService indexService;
    private final RegionService regionService;
    private final MeetingScoreService meetingScoreService;

    private final SchedulerConfiguration schedulerConfiguration;

    public MeetingScheduler(
            MeetingTaskService meetingTaskService,
            MeetingNoticeService meetingNoticeService,
            IndexService indexService,
            RegionService regionService,
            MeetingScoreService meetingScoreService, SchedulerConfiguration schedulerConfiguration) {
        this.meetingTaskService = meetingTaskService;
        this.meetingNoticeService = meetingNoticeService;
        this.indexService = indexService;
        this.regionService = regionService;
        this.meetingScoreService = meetingScoreService;
        this.schedulerConfiguration = schedulerConfiguration;
    }

    /**
     * 定时派发任务，每天00:00执行
     */
    @Scheduled(cron = "${scheduler.cron.executeScheduled}")
    public void executeScheduled() {
        LogHelper.schedulerLog("派发任务", () -> {
            log.debug("scheduler.execute -> {}", execute);
            if (execute) {
                // 派发各区县信息
                regionService.getRegions().forEach(region -> {
                    HeaderHelper.SysHeader sysHeader = RestTemplateHelper.getRegionIdLogHeader(region.getRegionId());
                    Date now = DateTime.now().toDate();
                    // 定时派发任务，每天执行。不需要单独处理派发失败的任务和未处理的更新
                    this.meetingTaskService.createAllMeetingTask(sysHeader, now);
                    // 任务派发完后 刷新移动端首页汇总缓存
                    indexService.collectRedis(LogHelper.getSSLog());
                });
            }
        });
    }


    /**
     * 检查任务是否完成，每天凌晨执行
     */
    @Scheduled(cron = "${scheduler.cron.checkTaskComplete}")
    public void checkTaskComplete() {
        LogHelper.schedulerLog("检查任务是否完成", () -> {
            log.debug("scheduler.checkTaskComplete -> {}", checkTaskComplete);
            if (checkTaskComplete) {
                List<MeetingTaskEntity> meetingTaskList = this.meetingTaskService.findUnfinishedTask();
                meetingTaskList.size();
            }
        });
    }

    /**
     * 活动任务完成情况统计
     * 默认每2小时执行一次
     *
     */
//    @Scheduled(cron = "${scheduler.statsMeetingTask.cron}")
//    public void statsMeetingTask() {
//        log.debug("scheduler.statsMeetingTask.run -> {}", statsMeetingTaskRun);
//        if (statsMeetingTaskRun) {
//            log.debug("定时统计活动任务完成情况开始");
//            LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
//            helper.reSetContext(logHelper.getSSlog(helper));
//            try {
//                meetingTaskService.undoneMeetingTaskStatisticsScheduled();
//            } catch (Exception e) {
//                log.error("定时统计活动任务完成情况失败！", e);
//            }
//            log.debug("定时统计活动任务完成情况结束");
//        }
//    }

    /**
     * <p>纪实定时发送通知</p>
     *
     * <li>支部党员大会</li>
     * <li>支部委员会</li>
     * <li>党小组会</li>
     * <li>党课</li>
     * <li>主题党课</li>
     * <p></p>
     *
     *增加主题党日提醒  tc 2021-11-19
     * <p>每月25号 10点触发</p>
     */
    //@Scheduled(initialDelay=2 * 1000, fixedRate= 300 * 60 * 1000)
    @Scheduled(cron = "${scheduler.notice.cron}")
    public void notice() {
        LogHelper.schedulerLog("纪实自动发放通知", () -> {
            MeetingPushParam pushParam = new MeetingPushParam();
            pushParam.setPushModel(MeetingNoticeSupportService.PUSH_CURRENT);
            log.debug("纪实自动发送定时任务配置：{}, pushParm:{}", schedulerConfiguration, pushParam);
            log.debug("scheduler.notice.run -> {}", schedulerConfiguration.getNoticeRun());
            if (schedulerConfiguration.getNoticeRun()) {
                log.debug("纪实自动发放通知定时任务开始！");
                try {
                    this.meetingNoticeService.sendNotice(LogHelper.getSSLog(), pushParam);
                } catch (Exception e) {
                    log.error("纪实自动发放通知定时任务失败！", e);
                }
                log.debug("纪实自动发放通知定时任务结束");
            }
        });
    }


    /**
     * 主题推送
     */
    @Scheduled(cron = "${scheduler.topic-push.cron}")
    public void topic() {
        LogHelper.schedulerLog("主题推送", () -> {
            MeetingPushParam pushParam = new MeetingPushParam();
            pushParam.setPushModel(MeetingNoticeSupportService.PUSH_TOPIC);
            log.debug("纪实自动发送考核单位定时任务配置：{}, pushParm:{}", schedulerConfiguration, pushParam);
            log.debug("scheduler.notice.run -> {}", schedulerConfiguration.getTopicPushRun());
            if (schedulerConfiguration.getTopicPushRun()) {
                log.debug("主题推送-纪实自动发放通知定时任务开始！");
                try {
                    this.meetingNoticeService.sendNotice(LogHelper.getSSLog(), pushParam);
                } catch (Exception e) {
                    log.error("主题推送-纪实自动发放通知定时任务失败！", e);
                }
                log.debug("主题推送-纪实自动发放通知定时任务结束");
            }
        });
    }


    /**
     * 首页汇总缓存刷新
     */
    @Scheduled(cron = "${scheduler.refreshCacheOfIndexCollect.cron}")
    public void refreshCacheOfIndexCollectScheduled() {
        LogHelper.schedulerLog("首页汇总缓存刷新", () -> {
            log.debug("scheduler.refreshCacheOfIndexCollect.run -> {}", refreshCacheOfIndexCollectRun);
            if (refreshCacheOfIndexCollectRun) {
                indexService.collectRedis(LogHelper.getSSLog());
            }
        });
    }

    /**
     *  个支委会人员和党小组组长增加积分定时任务
     */
    @Scheduled(cron = "${scheduler.committeeAndGroupScore.cron}")
    public void committeeAndGroupScoreScheduled() {
        LogHelper.schedulerLog("个支委会人员和党小组组长增加积分定时任务", () -> {
            log.debug("scheduler.committeeAndGroupScore.run -> {}", committeeAndGroupScoreRun);
            if (committeeAndGroupScoreRun) {
                // 各区县信息
                regionService.getRegions().forEach(region -> {
                    //支部委员会成员每年基础分增加
                    meetingScoreService.basicsScoreAdd(region.getRegionId(),4,new Date());
                    //支党小组组长每年基础分增加
                    meetingScoreService.basicsScoreAdd(region.getRegionId(),5,new Date());
                });
            }
        });
    }

    /**
     *  党小组会未开展，组长扣分任务
     */
    @Scheduled(cron = "${scheduler.unfinishedGroupScore.cron}")
    public void unfinishedGroupScoreScheduled() {
        LogHelper.schedulerLog("个支委会人员和党小组组长增加积分定时任务", () -> {
            log.debug("scheduler.unfinishedGroupScore.run -> {}", unfinishedGroupScoreRun);
            if (unfinishedGroupScoreRun) {
                // 各区县信息
                regionService.getRegions().forEach(region -> {
                    try {
                        //未开展党小组会的组长扣分
                        //获取上月时间
                        String dt = DateUtils.dateFormat(new Date(),"yyyy-MM-dd");
                        Date queryDate = DateUtils.stringToDate(DateUtils.getDateByMonths(-1,"yyyy-MM-dd",dt),"yyyy-MM-dd");
                        meetingScoreService.unfinishedGroupLeader(region.getRegionId(),queryDate);
                    }catch (Exception e){
                        log.error("党小组会未开展，组长扣分任务 定时任务失败！ region={} ",region,e);
                    }
                });
            }
        });
    }

}
