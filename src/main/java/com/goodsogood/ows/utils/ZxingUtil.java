package com.goodsogood.ows.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.log4j.Log4j2;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import static com.google.zxing.client.j2se.MatrixToImageWriter.toBufferedImage;

/**
 * Description: 二维码生成工具类
 *
 * <AUTHOR>
 * @version 4.0.0
 * 2021-09-06
 */
@Log4j2
public class ZxingUtil {

    /**
     * 生成二维码
     *
     * @param content    二维码内容
     * @param width      图像宽度
     * @param height     图像高度
     * @param formatName 图像类型
     * @return String 二维码二进制字符串
     */
    public static String QREncode(String content, int width, int height, String formatName)
            throws WriterException, IOException {
        log.debug("二维码内容：{}", content);
        Map<EncodeHintType, Object> hints = new HashMap<>();
        // 内容编码格式
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        // 指定纠错等级
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        // 设置二维码边的空度，非负数
        hints.put(EncodeHintType.MARGIN, 1);
        BitMatrix bitMatrix =
                new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);

        // 生成二维码图片并将图片转为Base64字符串传递给前台
        // 1、读取文件转换为字节数组
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            BufferedImage image = toBufferedImage(bitMatrix);
            //2、将字节数组转为二进制
            ImageIO.write(image, formatName, out);
            byte[] bytes = out.toByteArray();

            //3.将二进制数组转换为Base64字符串
            Base64.Encoder encoder = Base64.getEncoder();
            return encoder.encodeToString(bytes);
        }
    }
}
