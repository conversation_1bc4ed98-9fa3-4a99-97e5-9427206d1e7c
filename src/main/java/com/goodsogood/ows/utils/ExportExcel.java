package com.goodsogood.ows.utils;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.model.vo.UploadFileResultForm;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;

/**
 * 导出统计分析excel文件接口
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface ExportExcel<T> {

    @Value("${temp-path}")
    String tempPath = "/data/tmp/ows-meeting";
    /**
     * 将字节数组写出到servlet输出流
     *
     * @param response http回应对象，为excel回应的目的地
     * @param list     要导出到 excel的数据集合
     * @param titles   excel的标题 通常取第一行作为excel的标题
     * @throws IOException 异常
     */
    default void exportExcel(HttpServletResponse response, List<T> list, String[] titles, String name) throws IOException, NoSuchFieldException {
        if (StringUtils.isBlank(name)) {
            name = "导出记录" + UUID.randomUUID() + ".xls";
        }

        byte[] bytes = selectExcel(list, titles);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setContentType("application/x-msdownload");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(URLEncoder.encode(name, "UTF-8").getBytes(), StandardCharsets.UTF_8));
        response.setContentLength(bytes.length);
        response.getOutputStream().write(bytes);
        response.getOutputStream().flush();
        response.getOutputStream().close();
    }

    default void exportExcel(HttpServletResponse response, List<T> list, String[] titles) throws IOException, NoSuchFieldException {
        exportExcel(response, list, titles, null);
    }

    /**
     * 选择要导出的文件 导出的excel 属于office 2007格式的文件
     *
     * @param list   excel文件内容
     * @param titles excel 文件的标题
     * @return 已经生成excel文件的字节数组
     * @throws IOException 异常
     */
    default byte[] selectExcel(List<T> list, String[] titles) throws IOException, NoSuchFieldException {
        //Workbook workbook = new HSSFWorkbook();
        //2007
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        generateExcelTitle(titles, sheet);
        if (list != null && list.size() > 0) {
            eachListAndCreateRow(list, sheet);
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        workbook.write(out);
        return out.toByteArray();
    }

    /**
     * 选择要导出的文件 导出的excel 属于office 2007格式的文件
     *
     * @param list              excel文件内容
     * @param titles            excel文件的标题
     * @param name              excel文件名
     * @param errors            错误
     * @param headers           请求头
     * @param restTemplate      请求
     * @param togServicesConf
     * @return 已经生成excel文件的字节数组
     * @throws IOException 异常
     */
    default UploadFileResultForm createExcelUploadFileCenter(List<T> list, List<String> titles, String name, Errors errors,
                                               HttpHeaders headers, RestTemplate restTemplate, TogServicesConfig togServicesConf) throws NoSuchFieldException, IOException {
        //Workbook workbook = new HSSFWorkbook();
        val fileName  = name + "_" + DateFormatUtils.format(System.currentTimeMillis(),"yyyyMMddHHmmss") + ".xlsx";
        val path = tempPath + File.separator + fileName;
        // 新建文件
        try (FileOutputStream fos = new FileOutputStream(path)) {
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet();
            generateExcelTitle(titles.toArray(new String[0]), sheet);
            eachListAndCreateRow(list, sheet);
            workbook.write(fos);
            final UploadFileResultForm resultForm = FileUtil.sendFileCenter(path,
                    name,
                    "file",
                    errors,
                    headers,
                    restTemplate,
                    togServicesConf);
            workbook.close();
            FileUtil.deleteFile(path);
            return resultForm;
        }
    }

    /**
     * 遍历集合，并创建单元格行
     *
     * @param list  数据集合
     * @param sheet 工作簿
     */
    default void eachListAndCreateRow(List<T> list, Sheet sheet) throws NoSuchFieldException {
        for (int i = 0, j = 1; i < list.size(); i++, j++) {
            T t = list.get(i);
            Row row = sheet.createRow(j);
            generateExcelForAs(t, row);
        }
    }

    /**
     * 生成excel文件的标题
     *
     * @param titles 数据集合
     * @param sheet  工作簿
     */
    default void generateExcelTitle(String[] titles, Sheet sheet) {
        Row row = sheet.createRow(0);
        for (int i = 0; i < titles.length; i++) {
            row.createCell(i, CellType.STRING).setCellValue(titles[i]);
        }
    }

    /**
     * 创建excel内容文件
     *
     * @param t   组装excel 文件的内容
     * @param row 当前excel 工作行
     */
    void generateExcelForAs(T t, Row row) throws NoSuchFieldException;

    /**
     * 当发生错误时如此回应信息
     *
     * @param response response
     */
    default void errorResponse(HttpServletResponse response) {
        byte[] message = "导出excel文件错误,请重试!".getBytes();
        response.setContentType("text/json;charset=UTF-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setContentLength(message.length);
        try {
            response.getOutputStream().write(message);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
