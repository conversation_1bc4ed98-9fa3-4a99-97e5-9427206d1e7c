package com.goodsogood.ows.utils;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.Escape;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.UploadFileResultForm;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

@Log4j2
public class FileUtil {

    /**
     * 模板文件 名称
     */
    private static final List<String> MODEL_FILE_NAMES = Arrays.asList(
            "model_one.doc",
            "model_two.doc",
            "model_three.doc",
            "model_four.doc",
            "model_five.doc",
            "talk_template.docx",
            "民主评议党员登记表_1.ftl",
            "民主评议党员登记表-有图.ftl");

    public static void download(HttpServletResponse response, File file) throws IOException {
        // 发送给客户端的数据
        OutputStream outputStream = response.getOutputStream();
        byte[] buff = new byte[1024];
        setHeader(response, file.getName());
        BufferedInputStream bis = null;
        // 读取filename
        bis = new BufferedInputStream(new FileInputStream(file));
        int i = bis.read(buff);
        while (i != -1) {
            outputStream.write(buff, 0, buff.length);
            outputStream.flush();
            i = bis.read(buff);
        }
    }

    /** 获取路径中的以指定后缀结尾的文件
     * @param path
     * @param endsName
     * @return 文件数组
     */
    public static File[] getEndsWithNameFile(String path, String endsName){
        File src = new File(path);
        File[] listFiles  = new File[]{};
        if(src.isDirectory()){
            // 存在并且为目录
            listFiles = src.listFiles((dir, name) -> {
                // dir 当前目录对象(sort)
                // name 文件全名称
                //  **规避类似temp.java 的文件目录**
                return new File(dir, name).isFile() && name.endsWith(endsName);
            });
        }
        return listFiles;
    }

    public static void deleteFile(File file){
        //判断文件不为null或文件目录存在
        if (file == null || !file.exists()){
            System.out.println("文件删除失败,请检查文件路径是否正确");
            return;
        }
        //取得这个目录下的所有子文件对象
        File[] files = file.listFiles();
        //遍历该目录下的文件对象
        if (null != files && files.length > 0) {
            for (File f : files) {
                //打印文件名
                String name = file.getName();
                System.out.println(name);
                //判断子目录是否存在子目录,如果是文件则删除
                if (f.isDirectory()) {
                    deleteFile(f);
                } else {
                    f.delete();
                }
            }
        }
        //删除空文件夹  for循环已经把上一层节点的目录清空。
        file.delete();
    }

    /**
     * 设置头信息
     * @param response
     * @param fileName
     */
    public static void setHeader(HttpServletResponse response, String fileName){
        try {
            // 设置响应头的文件名称信息
            String filename = URLEncoder.encode(fileName.replaceAll(" ", ""), "utf-8");
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename="
                    + new String(filename.getBytes(), StandardCharsets.UTF_8));
            // 设置响应头的文件名称信息
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            // 设置响应头导出文件格式vnd.ms-excel
            response.setContentType("application/vnd.ms-excel");
            // 设置响应头导出文件格式
            response.setCharacterEncoding("UTF-8");
        } catch (Exception e){
            log.error("导出设置头信息出错", e);
        }
    }

    public static UploadFileResultForm sendFileCenter(String path, String fileName, String fileType,
                                                      Errors errors, HttpHeaders headers, RestTemplate restTemplate, TogServicesConfig togServicesConfig) {
        final HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set(HeaderHelper.OPERATOR_REGION, header.getRegionId().toString());
        httpHeaders.set(HeaderHelper.OPERATOR_ID, header.getUserId() == null ? "-999" : header.getUserId().toString());
        httpHeaders.set(HeaderHelper.OPERATOR_NAME, header.getUserName() == null ? "-999" : header.getUserName().toString());
        MediaType type = MediaType.parseMediaType("multipart/form-data");
        // 设置请求的格式类型
        httpHeaders.setContentType(type);
        FileSystemResource fileSystemResource = new FileSystemResource(path);
        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
        form.add("upfile", fileSystemResource);
        form.add("upType", fileType);
        form.add("up_name", fileName);
        String url = "http://" + togServicesConfig.getFileCenter() + "/file/upload";
        try {
            List<UploadFileResultForm> formList = RemoteApiHelper.post(restTemplate, url, form, httpHeaders, new TypeReference<Result<List<UploadFileResultForm>>>() {
            });
            if (!formList.isEmpty()) {
                return formList.get(0);
            } else {
                throw new Exception();
            }
        } catch (Exception e) {
            log.error("上传文件到OSS服务器失败", e);
            throw new ApiException("上传文件到OSS服务器失败", new Result(errors, 9907, HttpStatus.OK.value()));
        }
    }

    /**
     * 删除目录（文件夹）以及目录下的文件
     * @param   sPath 被删除目录的文件路径
     * @return  目录删除成功返回true，否则返回false
     */
    public static boolean deleteDirectory(String sPath) {
        //如果sPath不以文件分隔符结尾，自动添加文件分隔符
        if (!sPath.endsWith(File.separator)) {
            sPath = sPath + File.separator;
        }
        File dirFile = new File(sPath);
        //如果dir对应的文件不存在，或者不是一个目录，则退出
        if (!dirFile.exists() || !dirFile.isDirectory()) {
            return false;
        }
        boolean flag = true;
        //删除文件夹下的所有文件(包括子目录)
        File[] files = dirFile.listFiles();
        for (File file : files) {
            //删除子文件
            if (file.isFile() && !MODEL_FILE_NAMES.contains(file.getName())) {
                flag = deleteFile(file.getAbsolutePath());
            } //删除子目录
             else {
                flag = deleteDirectory(file.getAbsolutePath());
            }
        }
        if (!flag) return false;
        //删除当前目录
        return dirFile.delete();
    }

    /**
     * 删除单个文件
     * @param   sPath    被删除文件的文件名
     * @return 单个文件删除成功返回true，否则返回false
     */
    public static boolean deleteFile(String sPath) {
        boolean flag = false;
        File file = new File(sPath);
        // 路径为文件且不为空则进行删除
        if (file.isFile() && file.exists()) {
            file.delete();
            flag = true;
        }
        return flag;
    }
}
