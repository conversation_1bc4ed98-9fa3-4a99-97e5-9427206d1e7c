package com.goodsogood.ows.utils;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.UploadFileResultForm;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 异步生成下载文件
 * <AUTHOR>
 */
@Log4j2
public class AsyncFileDownUtils {

	private static final String REDIS_FILE_PATH = "REDIS_FILE_PATH_";

	/**
	 * 生成Excel表格文件
	 * @param redisTemplate
	 * @param itemList
	 * @param tableName
	 * @param tempPath
	 * @param uuid
	 * @param redisRepeatKey
	 */
	public static void asyncGenerateExcel(StringRedisTemplate redisTemplate, RestTemplate restTemplate, TogServicesConfig togServicesConfig, Errors errors,
										  HttpHeaders headers, List<List<String>> itemList, String tableName, String tempPath, String uuid, String redisRepeatKey) {
		Map<String, List<List<String>>> data = new HashMap(1, 1);

		data.put(tableName, itemList);
		String fileName = uuid + "-" + DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss") + "." + ExcelUtils.XLSX_SUFFIX;
		String path = tempPath + File.separator + fileName;
		File file = new File(path);
		// 新建文件
		try (OutputStream ops = new FileOutputStream(file)) {
			if (!file.exists()) {
				file.createNewFile();
			}
			//生成excel文件
			Workbook wb = ExcelUtils.generateExcel(fileName, data);
			wb.write(ops);
			wb.close();
			UploadFileResultForm resultForm = FileUtil.sendFileCenter(path, tableName + "." + ExcelUtils.XLSX_SUFFIX, "file", errors, headers, restTemplate, togServicesConfig);
			if (Objects.nonNull(resultForm)) {
				Long id = resultForm.getId();
				redisTemplate.opsForValue().set(REDIS_FILE_PATH + uuid, id.toString());
			}
			log.debug("[{}], 文件上传成功！", fileName);
			FileUtil.deleteFile(file);
		} catch (Exception e) {
			redisTemplate.delete(redisRepeatKey);
			RateUtils.delete(uuid);
			log.error("生成excel失败! errorMasage {}", e.getMessage(), e);
		}
	}

	/**
	 * 下载文件
	 * @param redisTemplate
	 * @param uuid
	 * @param redisRepeatKey
	 * @return
	 * @throws IOException
	 */
	public static Object asyncDownFile(Errors errors, StringRedisTemplate redisTemplate, String uuid, String redisRepeatKey) {
		String filePathRedisKey = REDIS_FILE_PATH + uuid;
		String rate = RateUtils.getRate(uuid, 2);
		if (RateUtils.complete(uuid)) {
			if (Boolean.TRUE.equals(redisTemplate.hasKey(filePathRedisKey))) {
				String id = redisTemplate.opsForValue().get(filePathRedisKey);
				redisTemplate.delete(filePathRedisKey);
				redisTemplate.delete(redisRepeatKey);
				RateUtils.delete(uuid);
				Map<String, Object> resultMap = new HashMap<>();
				resultMap.put("file", id);
				return resultMap;
			} else {
				rate = "99";
			}
		} else if (Boolean.FALSE.equals(redisTemplate.hasKey(redisRepeatKey)) && StringUtils.isBlank(rate)) {
			throw new ApiException("生成文件失败", new Result(errors, 2005, HttpStatus.OK.value()));
		} else {
			rate = Objects.isNull(rate) ? "0" : rate;
		}
		return rate;
	}
}
