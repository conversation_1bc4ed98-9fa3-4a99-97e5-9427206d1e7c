package com.goodsogood.ows.utils;

public class NumConversionUtils {

    /**
     * 身份证脱敏
     */
    public static String numSecret(String num) {
        if (num == null || "".equals(num)) {
            return num;
        }
        //脱敏号码
        StringBuilder tm;
        //号码长度
        int len;
        String temp1, temp2;
        //身份证
        num = num.toUpperCase();
        len = num.length();
        temp1 = num.substring(0, 6);
        temp2 = num.substring(len - 4);
        tm = new StringBuilder(temp1);
        for (int i = 0; i < len - 10; i++) {
            tm.append("*");
        }
        tm.append(temp2);
        return tm.toString();
    }


    /**
     * 将阿拉伯数字转换成对应的汉字
     */
    public static String int2ChineseNumber(int num){
        String result="";
            String[] units = {"","十","百","千","万","十万","百万","千万","亿","十亿","百亿","千亿","万亿" };
            char[] numArray = {'零','一','二','三','四','五','六','七','八','九'};
            char[] val = String.valueOf(num).toCharArray();
            int len = val.length;
            StringBuilder sb = new StringBuilder();
            for (int j = 0; j < len; j++) {
                String m = val[j] + "";
                int n = Integer.valueOf(m);
                boolean isZero = n == 0;
                String unit = units[(len - 1) - j];
                if (isZero) {
                    if ('0' == val[j - 1]) {
                        continue;
                    } else {
                        sb.append(numArray[n]);
                    }
                } else {
                    sb.append(numArray[n]);
                    sb.append(unit);
                }
            }
            result =sb.toString();
            if(result.endsWith("零")){
                result = result.substring(0,result.length()-1);
            }
        return result;
    }
}
