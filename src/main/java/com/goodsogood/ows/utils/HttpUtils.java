package com.goodsogood.ows.utils;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;

/**
 *
 * http发送工具
 *
 * <AUTHOR>
 * @create 2018-04-08 16:10
 **/
@Log4j2
public class HttpUtils {

     /**
      * GET
      * @param restTemplate   RestTemplate
      * @param headers   HttpHeaders
      * @param url  url 用string.format()进行格式化后的数据
      * @return
      */
     public static Object doGet(RestTemplate restTemplate, HttpHeaders headers, String url){
          //构建头信息
          HttpEntity<String> entity = null;
          try {
               if(headers != null) {
                    HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);

                    headers = HeaderHelper.setMyHttpHeader(headers, sysHeader);

                    entity = new HttpEntity<>(null, headers);
               }
          } catch (NullPointerException e){
          } catch (Exception e){
               log.error("构建头信息出错", e);
          }
          try {
               ResponseEntity<Result> resultResponseEntity = restTemplate.exchange(
                       url, HttpMethod.GET, entity, Result.class);

               if (resultResponseEntity.getStatusCode() != HttpStatus.OK || (resultResponseEntity.getBody() != null && resultResponseEntity.getBody().getCode() != 0)) {
                    log.error("httputils error , error code : " + resultResponseEntity.getStatusCode());
               }
               return resultResponseEntity.getBody().getData();
          } catch (Exception e) {
               log.error("httputils doGet error", e);
          }
          return null;
     }

     /**
      * 下载资源
      * @param restTemplate
      * @param url
      * @return
      */
     public static ResponseEntity<byte[]> downloadFile(RestTemplate restTemplate, String url){
          try {
               restTemplate.getMessageConverters().add(
                       new ByteArrayHttpMessageConverter());

               HttpHeaders _headers = new HttpHeaders();
               _headers.setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM));

               HttpEntity<String> entity = new HttpEntity<String>(_headers);

               return restTemplate.exchange(
                       url, HttpMethod.GET, entity, byte[].class);
          } catch (Exception e) {
               log.error("httputils downloadFile error", e);
          }
          return null;
     }

}
