package com.goodsogood.ows.utils;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.cglib.beans.BeanCopier;

/**
 * 对象复制工具类
 *
 * <AUTHOR>
 * @create 2018/10/30 10:01
 */
public class BeanCopierUtils {

    /**
     * 复制对象属性，只会复制相同属性名且类型相同
     *
     * @param source 复制源
     * @param target 复制到哪个对象
     */
    public static void copy(Object source, Object target) throws Exception {
        if (!ObjectUtils.allNotNull(source, target)) {
            throw new Exception("源对象和目标对象都不能为空");
        }
        BeanCopier beanCopier = BeanCopier.create(source.getClass(),
                target.getClass(), false);
        beanCopier.copy(source, target, null);
    }
}
