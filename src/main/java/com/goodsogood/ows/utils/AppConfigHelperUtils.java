package com.goodsogood.ows.utils;

import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AppConfigHelper相关utils
 *
 * <AUTHOR>
 * @date 2020/04/16
 */
@Component
public class AppConfigHelperUtils {

    private final SimpleApplicationConfigHelper applicationConfigHelper;

    @Autowired
    public AppConfigHelperUtils(SimpleApplicationConfigHelper applicationConfigHelper) {
        this.applicationConfigHelper = applicationConfigHelper;
    }

    /**
     * 根据版本标签获取各区县编号集合
     */
    public List<Long> findRegionIds(){
        Region region = applicationConfigHelper.getRegions();
        List<Long> regionIdList = region.getRegions().stream().map(re->re.getRegionId()).collect(Collectors.toList());
        return regionIdList!=null?regionIdList:new ArrayList<>();
    }

    /**
     * 根据版本标签获取各区县顶层组织编号集合
     */
    public List<Long> findRegionTopOrgIds(){
        Region region = applicationConfigHelper.getRegions();
        List<Long> topOrgList = region.getRegions().stream().map(re->re.getOrgData().getOrgId()).collect(Collectors.toList());
        return topOrgList!=null?topOrgList:new ArrayList<>();
    }


    /**
     * 根据顶级组织编号获取区县编号
     */
    public Long findRegionIdByOrgId(Long orgId){
        Region.RegionData region = applicationConfigHelper.getRegionByOrgId(orgId);
        return region!=null?region.getRegionId():null;
    }

    /**
     * 根据区县编号获取顶级组织编号
     */
    public Long findTopOrgIdByRegionId(Long regionId){
        Region region = applicationConfigHelper.getRegions();
        return region.getRegions().stream().filter(r -> r.getRegionId().equals(regionId)).findFirst().map(r -> r.getOrgData().getOrgId()).orElse(null);
    }

    /**
     * 根据区县编号获取区县信息
     */
    public Region.RegionData findRegionByRegionId(Long regionId){
        Region region = applicationConfigHelper.getRegions();
        return region.getRegions().stream().filter(r -> r.getRegionId().equals(regionId)).findFirst().orElse(null);
    }

    /**
     * 根据区县编号判断微信公众号推送版本，判断是否为SAAS版本
     */
    public boolean wechatIsSaasVersion(Long regionId){
        return applicationConfigHelper.getRegionByRegionId(regionId).wechatIsSaasVersion();
    }

}
