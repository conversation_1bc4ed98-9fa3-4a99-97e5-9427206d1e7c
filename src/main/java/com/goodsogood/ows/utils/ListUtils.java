package com.goodsogood.ows.utils;

import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-06-12 15:51
 **/
public class ListUtils {

     /**
      * 判断list是否为空
      * @param list
      * @return
      */
     public static boolean isEmpty(List<?> list) {
         return null == list || list.size() <= 0;
     }

     /**
      * list分割
      * @param list
      * @param regex
      * @return
      */
     public static String listToString(List<String> list, String regex) {
          if(list == null || list.size() ==0 || StringUtils.isEmpty(regex)) {
               return "";
          }
          String str = "";
          for(int i = 0; i < list.size(); i++) {
               if(i == 0) {
                    str = list.get(i);
               } else {
                    str = str + regex + list.get(i) ;
               }
          }
          return str;
     }

}
