package com.goodsogood.ows.utils;

import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * 内部接口获取数据
 * <AUTHOR>
 * @create 2018-10-22 17:15
 **/
public class HttpInnerUtils {


     /**
      * 根据key获取组织信息
      * @param restTemplate
      * @param headers
      * @param server    用户中心
      * @param oid       组织id
      * @param key       需要获取的数据key
      * @return
      */
     public static Object getOrgInfo(RestTemplate restTemplate, HttpHeaders headers, String server, Long oid, String key){
          Map<String, Object> map = (Map<String, Object>) HttpUtils.doGet(restTemplate, headers,
                  String.format("http://%s/org/info?org_id=%s",
                          server, oid));
          return map.get(key);
     }

}
