package com.goodsogood.ows.utils.word;

import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.vo.*;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.Version;
import lombok.extern.log4j.Log4j2;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import com.goodsogood.ows.component.Errors;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>  导出word模板数据
 *
 */
    @Component
    @Log4j2
    public class ExportWordUtil {
        Configuration configuration;
        @Autowired
        Errors errors;
        public ExportWordUtil(){
            configuration  = new Configuration(new Version("2.3.28"));
            configuration.setDefaultEncoding("utf-8");
//        configuration.setDirectoryForTemplateLoading("/file");
            configuration.setClassForTemplateLoading(this.getClass(),"/file");
        }

        /**
         *
         * @param templateName  模板名称，模板需放在resource/file下
         * @param dirPath  导出文件路径
         * @param dirFileName  导出文件名
         * @param obj  导出的对象
         */
        public void exportWordAlone(String templateName,String dirPath,String dirFileName,Object obj) throws IOException, TemplateException {
            Template template =  configuration.getTemplate(templateName,"utf-8");
            Map<String,String> map = beanToMap(obj);
            File outFile = new File(dirPath,dirFileName);
            BufferedWriter bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), StandardCharsets.UTF_8));
            template.process(map,bufferedWriter);
            bufferedWriter.flush();
            bufferedWriter.close();
        }

        /**
         * 导出list数据：list的name固定为userList as user,word模板为user.属性
         * @param templateName  模板名
         * @param dirPath  导出文件路径
         * @param dirFileName  导出文件名
         * @param lists  要导出的list
         */
        public <T> void exportWordList(String templateName, String dirPath, String dirFileName, List<T> lists) throws IOException, TemplateException {
            Map<String,List<Map<String,String>>> map = new HashMap<>();
            List<Map<String,String>> listster = new ArrayList<>();
            lists.forEach(i-> listster.add(beanToMap(i)));
            map.put("userList",listster);
            Template template = configuration.getTemplate(templateName,"utf-8");
            File outFile = new File(dirPath,dirFileName);
            BufferedWriter bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), StandardCharsets.UTF_8));
            template.process(map,bufferedWriter);
            bufferedWriter.flush();
            bufferedWriter.close();
        }

        /**
         * 导出数据既有一般属性又有list里的属性
         * @param templateName  模板名
         * @param dirPath  导出路径
         * @param dirFileName  导出的文件名
         * @param obj  一般属性，封装到obj里
         * @param lists  list表格属性，直接put("userList",map<属性，属性值>)
         */
        public <T> void exportWordAll(String templateName, String dirPath, String dirFileName, Object obj,List<T> lists) throws IOException, TemplateException {
            Map<String,Object> map = new HashMap<>();
            List<Map<String,String>> listster = new ArrayList<>();
            lists.forEach(i-> listster.add(beanToMap(i)));
            map.put("userList",listster);
            map.putAll(beanToMap(obj));
            Template template = configuration.getTemplate(templateName,"utf-8");
            File outFile = new File(dirPath,dirFileName);
            BufferedWriter bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), StandardCharsets.UTF_8));
            template.process(map,bufferedWriter);
            bufferedWriter.flush();
            bufferedWriter.close();
        }

        /**
         * 导出数据既有一般属性又有list里的属性,map为一般属性
         * @param templateName  模板名
         * @param dirPath  导出路径
         * @param dirFileName  导出的文件名
         * @param map  一般属性，直接put属性及属性值
         * @param lists  list表格属性，直接put("userList",map<属性，属性值>)
         */
        public <T> void exportWordAll(String templateName, String dirPath, String dirFileName, Map<String,String> map,List<T> lists) throws IOException, TemplateException {
            Map<String,Object> mapAll = new HashMap<>();
            List<Map<String,String>> listster = new ArrayList<>();
            log.debug("mt处理成map前"+map+"_"+lists);
            lists.forEach(i-> listster.add(beanToMap(i)));
            log.debug("mt处理成map后"+lists);
            mapAll.put("userList",listster);
            mapAll.putAll(map);
            log.debug("mt处理成mapAll"+mapAll);
            Template template = configuration.getTemplate(templateName,"utf-8");
            BufferedWriter bufferedWriter = null;
            try{
                File outFile = new File(dirPath,dirFileName);
                if(!outFile.exists()){
                    File dir = new File(dirPath);
                    if(!dir.exists()){
                        dir.mkdirs();
                    }
                    outFile.createNewFile();
                }
                bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), StandardCharsets.UTF_8));
                template.process(mapAll,bufferedWriter);
                bufferedWriter.flush();
            }catch(Exception e){
                log.error("mt导出失败"+e);
            }finally {
                bufferedWriter.close();
            }
        }


        //将对象的属性与属性值存放map
        public Map<String,String> beanToMap(Object obj){
            Map<String,String> map = new HashMap<>();
            BeanMap beanMap = BeanMap.create(obj);
            beanMap.forEach((key,value)->{
                if(null==value){
                    throw new ApiException("导出的字段值不能为null,请检查!"+key,new Result<>(errors, 3173, HttpStatus.OK.value(),key.toString()));
                }
                map.put((String)key,(String)value);
            });
            return map;
        }
}
