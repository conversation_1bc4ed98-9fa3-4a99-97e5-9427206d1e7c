package com.goodsogood.ows.utils;

import com.github.pagehelper.Page;
import com.goodsogood.ows.model.db.PageNumber;

import java.util.List;

/**
 * <AUTHOR>
 * @describe ${describe}
 * @date 2020-01-03
 */
public class PageUtil {

    public static <T, R> Page<T> pageResult(List<R> all, Class<T> tClass, Integer pageNum, Integer pageSize) {

        PageNumber pageNumber = new PageNumber(pageNum, pageSize);
        Page<T> page = new Page<>(pageNumber.getPage(), pageNumber.getRows());
        int endIndex = pageNumber.getRows() * pageNumber.getPage();
        int startIndex = pageNumber.getRows() * (pageNumber.getPage() - 1);
        page.setTotal(all.size());

        if (all.size() > endIndex) {
            List<T> list = BeanUtil.copyList(all.subList(
                    pageNumber.getRows() * (pageNumber.getPage() - 1),
                    endIndex
            ), tClass);
            page.clear();
            page.addAll(list);
        } else {

            if (all.size() > startIndex) {
                List<T> list = BeanUtil.copyList(all.subList(
                        pageNumber.getRows() * (pageNumber.getPage() - 1),
                        endIndex > all.size() ? all.size() : endIndex
                ), tClass);
                page.clear();
                page.addAll(list);
            } else {
                page.clear();
            }
        }

        return page;
    }
}
