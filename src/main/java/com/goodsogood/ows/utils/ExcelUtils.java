package com.goodsogood.ows.utils;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.SharedStringsTable;
import org.apache.poi.xssf.usermodel.*;
import org.xml.sax.*;
import org.xml.sax.helpers.DefaultHandler;
import org.xml.sax.helpers.XMLReaderFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;


public class ExcelUtils {

    public List<List<IndexValue>> dataList = new ArrayList<List<IndexValue>>();
    private final int startRow;
    private final int endRow;
    private int currentRow = 0;
    private final String filename;
    private List<IndexValue> rowData;
    public static final String XLS_SUFFIX = "xls";

    public static final String XLSX_SUFFIX = "xlsx";

    public ExcelUtils(String filename, int startRow, int endRow) throws Exception{
        if(StringUtils.isBlank(filename)) {
            throw new Exception("文件名不能空");
        }
        this.filename = filename;
        this.startRow = startRow;
        this.endRow = endRow;
        processFirstSheet();
    }
    /**
     * 指定获取第一个sheet
     * @throws Exception
     */
    private void processFirstSheet() throws Exception {
        OPCPackage pkg = OPCPackage.open(filename);
        XSSFReader r = new XSSFReader( pkg );
        SharedStringsTable sst = r.getSharedStringsTable();

        XMLReader parser = fetchSheetParser(sst);

        InputStream sheet1 = r.getSheet("rId1");
        InputSource sheetSource = new InputSource(sheet1);
        parser.parse(sheetSource);
        sheet1.close();
    }

    private XMLReader fetchSheetParser(SharedStringsTable sst) throws SAXException {
        XMLReader parser =
                XMLReaderFactory.createXMLReader(
                        "com.sun.org.apache.xerces.internal.parsers.SAXParser"
                );
        ContentHandler handler = new PagingHandler(sst);
        parser.setContentHandler(handler);
        return parser;
    }

    /**
     * See org.xml.sax.helpers.DefaultHandler javadocs
     */
    private  class PagingHandler extends DefaultHandler {
        private final SharedStringsTable sst;
        private String lastContents;
        private boolean nextIsString;
        private String index = null;

        private PagingHandler(SharedStringsTable sst) {
            this.sst = sst;
        }
        /**
         * 每个单元格开始时的处理
         */
        @Override
        public void startElement(String uri, String localName, String name,
                                 Attributes attributes) {
            if(name.equals("c")) {
                index = attributes.getValue("r");
                //这是一个新行
                if(Pattern.compile("^A[0-9]+$").matcher(index).find()){
                    //存储上一行数据
                    if(rowData!=null&&isAccess()&&!rowData.isEmpty()){
                        dataList.add(rowData);
                    }
                    rowData = new ArrayList<>();//新行要先清除上一行的数据
                    currentRow++;//当前行+1
                }
                if(isAccess()){
                    // Figure out if the value is an index in the SST
                    String cellType = attributes.getValue("t");
                    nextIsString = cellType != null && cellType.equals("s");
                }
            }
            lastContents = "";
        }
        /**
         * 每个单元格结束时的处理
         */
        @Override
        public void endElement(String uri, String localName, String name) {
            if(isAccess()){
                if(nextIsString) {
                    int idx = Integer.parseInt(lastContents);
                    lastContents = new XSSFRichTextString(sst.getEntryAt(idx)).toString();
                    nextIsString = false;
                }
                if(name.equals("v")) {
                    rowData.add(new IndexValue(index,lastContents));
                }
            }
        }
        @Override
        public void characters(char[] ch, int start, int length) {
            if(isAccess()){
                lastContents += new String(ch, start, length);
            }
        }
        @Override
        public void endDocument() {
            if(rowData!=null&&isAccess()&&!rowData.isEmpty()){
                dataList.add(rowData);
            }
        }
    }
    private boolean isAccess(){
        return currentRow >= startRow && startRow <= endRow;
    }
    private class IndexValue{
        String v_index;
        String v_value;
        public IndexValue(String v_index, String v_value) {
            super();
            this.v_index = v_index;
            this.v_value = v_value;
        }
        @Override
        public String toString() {
            return "IndexValue [v_index=" + v_index + ", v_value="
                    + v_value + "]";
        }
        public int getLevel(IndexValue p){
            char[] other = p.v_index.replaceAll("[0-9]", "").toCharArray();
            char[] self = this.v_index.replaceAll("[0-9]", "").toCharArray();
            if(other.length!=self.length) return -1;
            for(int i=0;i<other.length;i++){
                if(i==other.length-1){
                    return self[i]-other[i];
                }else{
                    if(self[i]!=other[i]){
                        return -1;
                    }
                }
            }
            return -1;
        }
    }
    /**
     * 获取真实的数据（处理空格）
     * @return
     * @throws Exception
     */
    public List<List<String>> getMyDataList() throws Exception{

        List<List<String>> myDataList = new ArrayList<>();
        if(dataList==null||dataList.size()<=0) return myDataList;

        for(int i=0;i<dataList.size();i++){
            List<IndexValue> i_list = dataList.get(i);
            List<String> tem = new ArrayList<>();
            int j=0;
            for(;j< i_list.size()-1;j++){
                //获取当前值,并存储
                IndexValue current = i_list.get(j);
                tem.add(current.v_value);
                //预存下一个
                IndexValue next = i_list.get(j+1);
                //获取差值
                int level = next.getLevel(current);
                if(level<=0) throw new Exception("超出处理范围");
                for(int k = 0;k<level-1;k++){
                    tem.add(null);
                }
            }
            tem.add(i_list.get(j).v_value);
            myDataList.add(tem);

        }
        return myDataList;
    }

    public static Workbook generateExcel(String fileName, Map<String, List<List<String>>> data) throws Exception {
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        Workbook wb = null;
        try {
            if (XLS_SUFFIX.equals(suffix)) {
                wb = new HSSFWorkbook();
            } else if (XLSX_SUFFIX.equals(suffix)) {
                wb = new XSSFWorkbook();
            } else {
                throw new Exception("后缀名必须是 xls 或者 xlsx");
            }
            int[] index = new int[]{0};//标记第一行标题行
            Workbook finalWb = wb;
            // row cell counter
            int[] counter = new int[]{0, 0};
            data.keySet().stream().forEach(sheetName -> {
                Sheet sheet = finalWb.createSheet(sheetName);
                data.get(sheetName).forEach(rowItem -> {
                    Row row = sheet.createRow(counter[0]++);
                    rowItem.forEach(cellItem -> {
                        Cell cell = row.createCell(counter[1]++);
                        cell.setCellValue(cellItem);
                        if(index[0]==0){
                            cell.setCellStyle(getHeaderStyle(finalWb,(short) 14));//设置列标题单元格式
                        }
                    });
                    counter[1] = 0;
                    index[0]=1;
                });
                counter[0] = 0;
                for(int i =0;i<data.get(sheetName).get(0).size();i++){
//                    sheet.autoSizeColumn(i);//宽度自适应
                    sheet.setColumnWidth(i,data.get(sheetName).get(0).get(i).getBytes().length*2*256);//根据表标题计算宽度
                }
            });
        } catch (Exception e) {
            throw new Exception("生成excel失败!", e);
        }
        return wb;
    }

    /**
     * @description 标题的单元格（加粗）
     * @param wb 参数说明
     * @param fontPx 字体大小px
     */
    public static CellStyle getHeaderStyle(Workbook wb, short fontPx){

        CellStyle headerStyle = wb.createCellStyle(); //header的单元格样式
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);

        Font font = wb.createFont();
        font.setBold(true);                     //字体加粗
        font.setFontHeightInPoints( fontPx ); //大小设置为fonTpx
        headerStyle.setFont(font);

        return headerStyle;
    }

    /**
     * 读取excel文件数据到二维数组集合
     * @param excelFile  Excel文件
     * @param startRow   读取数据开始行(可以排除列标题读取数据)
     * @param titleName 标题列数组
     * @param isCheck 是否检查列标题(防止上传模板错误)
     * @return
     * @throws Exception
     */
    public static List<List<String>> getExcelData(File excelFile,int startRow,String [] titleName,boolean isCheck) throws Exception{
        XSSFWorkbook wb = new XSSFWorkbook(new FileInputStream(excelFile));
        if(wb==null){
            System.out.println("文件有误，未读取到内容！");
        }
        List<List<String>> excelData = new ArrayList<>();
        //遍历xlsx中的sheet
        for (int numSheet = 0; numSheet < wb.getNumberOfSheets(); numSheet++) {
            XSSFSheet xssfSheet = wb.getSheetAt(numSheet);
            if (xssfSheet == null) {
                continue;
            }
            //读取第一行列标题，判断是否使用了正确的模板
            if(isCheck){
                XSSFRow xssfRow = xssfSheet.getRow(0);//标题行
                if (xssfRow == null){
                    continue;
                }
                for (int k = 0;k<titleName.length;k++){
                    XSSFCell xssfCell = xssfRow.getCell(k);
                    if(!(trimStr(titleName[k]).equals(trimStr(getValue(xssfCell))))){//标题名称不匹配
                        throw new Exception("导入模板不匹配");
                    }
                }
            }
            // 对于每个sheet，读取其中的每一行
            for (int rowNum = startRow; rowNum <= xssfSheet.getLastRowNum(); rowNum++) {
                XSSFRow xssfRow = xssfSheet.getRow(rowNum);
                if (xssfRow == null){
                    continue;
                }
                List<String> rowData = new ArrayList<>(20);
                for (int i = 0;i<titleName.length;i++){
                    XSSFCell xssfCell = xssfRow.getCell(i);
                    rowData.add(trimStr(getValue(xssfCell)));//设置每个单元格数据
                }
                if(!(rowData.stream().allMatch(d->"".equals(d)))){
                    excelData.add(rowData);//设置每行数据
                }
            }
        }
        return excelData;
    }

    @SuppressWarnings("deprecation")
    private static String getValue(XSSFCell xssfCell) {
        if(xssfCell==null){
            return "";
        }
        if (xssfCell.getCellType() == xssfCell.CELL_TYPE_BOOLEAN) {
            return String.valueOf(xssfCell.getBooleanCellValue());
        }else if (xssfCell.getCellType() == xssfCell.CELL_TYPE_NUMERIC) {
            if(DateUtil.isCellDateFormatted(xssfCell)){
                return String.valueOf(xssfCell.getDateCellValue());
            }else {
                double cur = xssfCell.getNumericCellValue();
                long longVal = Math.round(cur);
                Object inputValue = null;
                if(Double.parseDouble(longVal + ".0") == cur){
                    inputValue = longVal;
                }else{
                    inputValue = cur;
                }
                return String.valueOf(inputValue);
            }

        }else if(xssfCell.getCellType() == xssfCell.CELL_TYPE_BLANK || xssfCell.getCellType() == xssfCell.CELL_TYPE_ERROR){
            return "";
        }else{
            return String.valueOf(xssfCell.getStringCellValue());
        }
    }

    //字符串修剪  去除所有空白符号 ， 问号 ， 中文空格
    static private String trimStr(String str){
        if(str==null||"".equals(str)){
            return "";
        }
        return str.replaceAll("[\\s\\?]", "").replace("　", "");
    }

    /*public static void main(String[] args) throws Exception {

        ExcelUtils reader = new ExcelUtils("D:/项目组报销表-模板.xlsx",1,100);
        List<List<String>> list = reader.getMyDataList();
        for (int i = 0; i < list.size(); i++) {
            List<String> cellList = list.get(i);
            for (int j = 0; j < cellList.size(); j++) {
                System.out.print("    " + cellList.get(j));
            }
            System.out.println();
        }
    }*/

}
