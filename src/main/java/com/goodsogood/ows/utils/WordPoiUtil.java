package com.goodsogood.ows.utils;


import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.POIXMLDocument;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.io.*;
import java.nio.file.Paths;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 替换word文档中的占位符
 */
@Log4j2
public class WordPoiUtil {
    /**
     *
     * @param srcPath word模板数据源路径
     * @param destPath word导出路径
     * @param fileName 生成的文件名称
     * @param map 关键字键值对映射
     * @throws Exception
     */
    public static void replaceWord(String srcPath, String destPath, String fileName, Map<String, String> map) throws IOException {
        FileOutputStream out = null;
        FileInputStream input = null;
        try {
            if("doc".equals(srcPath.split("\\.")[1])) {
                input = new FileInputStream(new File(srcPath));
                HWPFDocument document = new HWPFDocument(input);
                Range range = document.getRange();
                log.debug("获取的关键字键值对{}",map.entrySet());
                for(Map.Entry<String, String> entry : map.entrySet()) {
                    if (entry.getValue() == null) {
                        entry.setValue("  ");
                    }
                    range.replaceText(entry.getKey(), entry.getValue());
                }
                ByteArrayOutputStream ostream = new ByteArrayOutputStream();
                if (!destPath.endsWith("/")) {
                    destPath = destPath + File.separator;
                }
                File dir = new File(destPath);//没有则创建
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                String tmpPath = Paths.get(destPath,fileName).toString();//获取文件
                log.debug("文件路径->{}",tmpPath);
                out = new FileOutputStream(new File(tmpPath));
                document.write(out);
                out.write(ostream.toByteArray());
            }else {
                XWPFDocument document = new XWPFDocument(POIXMLDocument.openPackage(srcPath));
                // 替换段落中的指定文字
                Iterator<XWPFParagraph> itPara = document.getParagraphsIterator();
                while (itPara.hasNext()) {
                    XWPFParagraph paragraph = itPara.next();
                    List<XWPFRun> runs = paragraph.getRuns();
                    for (XWPFRun run : runs) {
                        String oneparaString = run.getText(run.getTextPosition());
                        if (StringUtils.isBlank(oneparaString)){
                            continue;
                        }
                        for (Map.Entry<String, String> entry :
                                map.entrySet()) {
                            oneparaString = oneparaString.replace(entry.getKey(), entry.getValue());
                        }
                        run.setText(oneparaString, 0);
                    }

                }
                ByteArrayOutputStream ostream = new ByteArrayOutputStream();
                out = new FileOutputStream(new File(destPath));
                document.write(out);
                out.write(ostream.toByteArray());
            }
            out.flush();
        }catch (Exception e) {
            e.printStackTrace();
        } finally {
            if(out != null) {
                out.close();
            }
            if(input != null) {
                input.close();
            }
        }
    }
}
