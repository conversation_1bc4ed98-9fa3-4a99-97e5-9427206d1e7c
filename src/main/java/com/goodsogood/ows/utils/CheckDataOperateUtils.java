package com.goodsogood.ows.utils;


import com.goodsogood.ows.annotation.OtherId;
import com.goodsogood.ows.model.db.BaseCheckEntity;
import org.apache.commons.collections4.CollectionUtils;

import javax.persistence.Id;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 一对多附表实体校验帮助类
 *
 * <AUTHOR>
 * @since v3.0.1
 */
public class CheckDataOperateUtils {
    /**
     * 校验新增、删除的数据
     *
     * @param newList 更新的数据
     * @param oldList 历史数据
     * @return List<T> 标记后合并的数据信息
     */
    public static <T extends BaseCheckEntity> List<T> check(List<T> newList, List<T> oldList, Class<T> clazz) {
        if (newList == null) {
            newList = new ArrayList<>();
        }
        Field[] fields = clazz.getDeclaredFields();
        Field idField = null;
        Field otherIdField = null;
        for (Field field : fields) {
            field.setAccessible(true);
            if (idField == null && field.getAnnotation(Id.class) != null) {
                idField = field;
            } else if (otherIdField == null && field.getAnnotation(OtherId.class) != null) {
                otherIdField = field;
            }
        }
        if (idField == null) {
            throw new RuntimeException("not found @Id annotation!");
        }
        List<T> addList;
        List<T> delList;
        if (oldList == null) {
            addList = newList;
            for (T t : addList) {
                t.setOperateTag(1);
            }
        } else {
            try {
                addList = addData(newList, oldList, idField, otherIdField);
                delList = delData(newList, oldList, idField, otherIdField);
                if (CollectionUtils.isNotEmpty(delList)) {
                    addList.addAll(delList);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        return addList;
    }

    private static <T extends BaseCheckEntity> List<T> delData(
            List<T> newList,
            List<T> oldList,
            Field idField, Field otherIdField) throws IllegalAccessException {
        List<T> delList = new ArrayList<>();
        // 新数据中的id
        List<Long> idList = new ArrayList<>();
        // 原有数据中的otherId
        List<Long> newOtherIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(newList)) {
            for (T n : newList) {
                Long id = (Long) idField.get(n);
                if (id != null) {
                    idList.add(id);
                }
                if (otherIdField != null) {
                    Long otherId = (Long) otherIdField.get(n);
                    if (otherId != null) {
                        newOtherIdList.add(otherId);
                    }
                }
            }
        }
        // 删除的数据
        for (T object : oldList) {
            Long id = (Long) idField.get(object);
            if (otherIdField == null && !idList.contains(id)) {
                object.setOperateTag(2);
                delList.add(object);
            } else if (otherIdField != null) {
                Long otherId = (Long) otherIdField.get(object);
                boolean del = (otherId == null && !idList.contains(id))
                        || (otherId != null && !newOtherIdList.contains(otherId));
                if (del) {
                    object.setOperateTag(2);
                    delList.add(object);
                }
            }
        }
        return delList;
    }

    private static <T extends BaseCheckEntity> List<T> addData(
            List<T> newList,
            List<T> oldList,
            Field idField, Field otherIdField) throws IllegalAccessException {
        List<T> addList = new ArrayList<>();
        // 原有数据中的otherId
        List<Long> oldOtherIdList = new ArrayList<>();
        if (otherIdField != null && CollectionUtils.isNotEmpty(oldList)) {
            for (T old : oldList) {
                Long otherId = (Long) otherIdField.get(old);
                if (otherId != null) {
                    oldOtherIdList.add(otherId);
                }
            }
        }
        // 添加的数据
        for (T object : newList) {
            Long id = (Long) idField.get(object);
            if (otherIdField == null && id == null) {
                object.setOperateTag(1);
                addList.add(object);
            } else if (id == null) {
                Long otherId = (Long) otherIdField.get(object);
                if (otherId == null || !oldOtherIdList.contains(otherId)) {
                    object.setOperateTag(1);
                    addList.add(object);
                }
            }
        }
        return addList;
    }
}
