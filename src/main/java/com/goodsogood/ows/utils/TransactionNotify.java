package com.goodsogood.ows.utils;

import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 在事务提交之后释放锁，否则并发情况下，redis锁过早的释放会导致，下一次事务读取不了本次事务的数据，导致重复插入的情况
 *
 * <AUTHOR>
 * @date 2018/12/14
 */
@Log4j2
public class TransactionNotify implements TransactionSynchronization {

    private final RedisTemplate redisTemplate;

    private final String lockKey;

    private final String requestId;

    private TransactionNotify(RedisTemplate redisTemplate, String lockKey, String requestId) {
        this.redisTemplate = redisTemplate;
        this.lockKey = lockKey;
        this.requestId = requestId;
    }

    @Override
    public void suspend() {

    }

    @Override
    public void resume() {

    }

    @Override
    public void flush() {

    }

    @Override
    public void beforeCommit(boolean b) {

    }

    @Override
    public void beforeCompletion() {

    }

    @Override
    public void afterCommit() {
        RedisLockUtils.releaseDistributedLock(redisTemplate, lockKey, requestId);
//        log.warn("release distributed lock ,userId = {}", requestId);
    }

    @Override
    public void afterCompletion(int i) {

    }

    public static void afterCommitReleaseDistributedLock(RedisTemplate redisTemplate, String lockKey, String requestId) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionNotify(redisTemplate, lockKey, requestId));
    }
}
