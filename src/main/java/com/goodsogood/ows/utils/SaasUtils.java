package com.goodsogood.ows.utils;

import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.helper.SpringContextUtil;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @describe saas工具类
 * @date 2020-04-17
 */
public class SaasUtils {

    /**
     * 获取登录的区县id
     */
    public static Long getRegionId() {

        HttpServletRequest request = RequestUtil.getRequest();

        String regionId = request.getHeader("_region_id");

        return Long.valueOf(regionId);
    }

    /**
     * 获取顶级组织id
     */
    public static Long getRegionRootId(Long regionId) {
        return ((SimpleApplicationConfigHelper) SpringContextUtil
                .getBean(SimpleApplicationConfigHelper.class))
                .getOrgByRegionId(regionId).getOrgId();
    }


}
