package com.goodsogood.ows.utils;

import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2018-10-24 10:44
 **/
public class DateUtils {

    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * yyyy-MM-dd 转换的日期和today比较
     *
     * @param date yyyy-MM-dd 转换的日期
     * @return date < today; date=null return false
     */
    public static boolean ltToday(Date date) {
        if (date == null) {
            return false;
        }
        Calendar compare = Calendar.getInstance();// 时分秒相同
        return conversion(compare, date).getTime() < conversion(compare, DateTime.now().toDate()).getTime();
    }

    /**
     * yyyy-MM-dd 转换的日期和today比较
     *
     * @param date yyyy-MM-dd 转换的日期
     * @return date <= today; date=null return false
     */
    public static boolean lteToday(Date date) {
        if (date == null) {
            return false;
        }
        Calendar compare = Calendar.getInstance();// 时分秒相同
        return conversion(compare, date).getTime() <= conversion(compare, DateTime.now().toDate()).getTime();
    }

    /**
     * yyyy-MM-dd 转换的日期和today比较
     *
     * @param date yyyy-MM-dd 转换的日期
     * @return date > today; date=null return false
     */
    public static boolean gtToday(Date date) {
        if (date == null) {
            return false;
        }
        Calendar compare = Calendar.getInstance();// 时分秒相同
        return conversion(compare, date).getTime() > conversion(compare, DateTime.now().toDate()).getTime();
    }

    /**
     * yyyy-MM-dd 转换的日期和today比较
     *
     * @param date yyyy-MM-dd 转换的日期
     * @return date >= today; date=null return false
     */
    public static boolean gteToday(Date date) {
        if (date == null) {
            return false;
        }
        Calendar compare = Calendar.getInstance();// 时分秒相同
        return conversion(compare, date).getTime() >= conversion(compare, DateTime.now().toDate()).getTime();
    }

    /**
     * 将输入date的时分秒转换为conversion的时分秒
     */
    private static Date conversion(Calendar conversion, Date date) {
        Calendar cdate = Calendar.getInstance();
        cdate.setTime(date);
        conversion.set(Calendar.YEAR, cdate.get(Calendar.YEAR));
        conversion.set(Calendar.MONTH, cdate.get(Calendar.MONTH));
        conversion.set(Calendar.DAY_OF_MONTH, cdate.get(Calendar.DAY_OF_MONTH));
        return conversion.getTime();
    }

    /**
     * @return Date yyyy-MM-dd 00:00:00.0
     */
    public static Date today() {
        Calendar cn = set0(DateTime.now().toDate());
        return cn.getTime();
    }

    /**
     * 当前日期的00:00:00
     *
     * @return Date yyyy-MM-dd 00:00:00 ; date=null return null
     */
    public static Date to0SecondOfDay(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cn = set0(date);
        return cn.getTime();
    }

    /**
     * 设置时分秒 00:00:00
     */
    private static Calendar set0(Date date) {
        Calendar cn = Calendar.getInstance();
        cn.setTime(date);
        cn.set(Calendar.HOUR_OF_DAY, 0);
        cn.set(Calendar.MINUTE, 0);
        cn.set(Calendar.SECOND, 0);
        cn.set(Calendar.MILLISECOND, 0);
        return cn;
    }

    /**
     * 设置秒 00.000
     */
    public static Date set0Second(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cn = Calendar.getInstance();
        cn.setTime(date);
        cn.set(Calendar.SECOND, 0);
        cn.set(Calendar.MILLISECOND, 0);
        return cn.getTime();
    }

    /**
     * 设置秒 59
     */
    public static Date set59Second(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cn = Calendar.getInstance();
        cn.setTime(date);
        cn.set(Calendar.SECOND, 59);
        cn.set(Calendar.MILLISECOND, 0);
        return cn.getTime();
    }

    /**
     * 当前日期的最后一秒
     *
     * @return Date yyyy-MM-dd 23:59:59.999 ; date=null return null
     */
    public static Date toLastSecondOfDay(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cn = Calendar.getInstance();
        cn.setTime(date);
        cn.set(Calendar.HOUR_OF_DAY, 23);
        cn.set(Calendar.MINUTE, 59);
        cn.set(Calendar.SECOND, 59);
        cn.set(Calendar.MILLISECOND, 999);
        return cn.getTime();
    }

    /**
     * 获取指定日期年份
     */
    public static Integer getYear(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cn = Calendar.getInstance();
        cn.setTime(date);
        return cn.get(Calendar.YEAR);
    }

    /**
     * 获取指定日期月份
     *
     * @return Calendar.MONTH 月份从0开始，返回时+1 ; date=null return null
     */
    public static Integer getMonth(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cn = Calendar.getInstance();
        cn.setTime(date);
        return cn.get(Calendar.MONTH) + 1;
    }

    /**
     * 获取指定日期天
     *
     * @return Calendar.DAY_OF_MONTH
     */
    public static Integer getDayOfMonth(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cn = Calendar.getInstance();
        cn.setTime(date);
        return cn.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取当前年份
     */
    public static int getYear() {
        Calendar cn = Calendar.getInstance();
        return cn.get(Calendar.YEAR);
    }

    /**
     * 获取当前月份
     *
     * @return Calendar.MONTH 月份从0开始，返回时+1
     */
    public static int getMonth() {
        Calendar cn = Calendar.getInstance();
        return cn.get(Calendar.MONTH) + 1;//Calendar 月份从0开始，返回时+1
    }

    /**
     * 获取当前月份的天
     *
     * @return Calendar.DAY_OF_MONTH
     */
    public static int getDayOfMonth() {
        Calendar cn = Calendar.getInstance();
        return cn.get(Calendar.DAY_OF_MONTH);//Calendar 月份从0开始，返回时+1
    }

    /**
     * 获取当前季度
     */
    public static int getQuarter() {
        Calendar cn = Calendar.getInstance();
        int month = cn.get(Calendar.MONTH) + 1;
        return (int) Math.ceil((double) month / 3);
    }

    /**
     * 日期格式化
     * yyyy
     * MM
     * dd
     * HH
     * mm
     * ss
     *
     * @return date=null return null
     */
    public static String dateFormat(Date date, String format) {
        if (date == null) {
            return "";
        }
        if (StringUtils.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        //设置要获取到什么样的时间
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        //获取String类型的时间
        return sdf.format(date);
    }

    /**
     * 获取当前日期 yyyy-MM-dd
     *
     * @return Date
     */
    public static Date getToday() {
        try {
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.parse(sdf.format(date));
        } catch (Exception e) {
            return new Date();
        }
    }

    /**
     * 获取当前年月 （2018年-12月）
     *
     * @return
     */
    public static String currentYearMonth() {
        Date date = org.joda.time.LocalDate.now().toDate();
        return currentYearMonth(date);
    }

    /**
     * 获取当前年月 （2018年-12月
     *
     * @return
     */
    public static String currentYearMonth(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月");
        return sdf.format(date);
    }
    /**
     * 获取当前年，季度（2018年第4季度）
     *
     * @return
     */
    public static String currentYearQuarter(Date date) {
        if (date == null) {
            return "";
        }
        int month = getMonth(date);
        int quarter;
        if (month >= 1 && month <= 3) {
            quarter = 1;
        } else if (month >= 4 && month <= 6) {
            quarter = 2;
        } else if (month >= 7 && month <= 9) {
            quarter = 3;
        } else {
            quarter = 4;
        }
        return getYear(date) + "年第" + quarter + "季度";
    }
    /**
     * 获取当前年，季度（2018年第4季度）
     *
     * @return
     */
    public static String currentYearQuarter() {
        Date date = org.joda.time.LocalDate.now().toDate();
        return currentYearQuarter(date);
    }
    /**
     * 获取当前季度开始时间
     *
     * @return string yyyy-MM-dd
     */
    public static String currentQuarterStartTime() {
        SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");
        return shortSdf.format(currentQuarterStartDate());
    }

    /**
     * 获取当前季度开始时间
     *
     * @return Date
     */
    private static Date currentQuarterStartDate() {
        Calendar c = Calendar.getInstance();
        int currentMonth = c.get(Calendar.MONTH) + 1;
        if (currentMonth >= 1 && currentMonth <= 3) {
            c.set(Calendar.MONTH, 0);
        } else if (currentMonth >= 4 && currentMonth <= 6) {
            c.set(Calendar.MONTH, 3);
        } else if (currentMonth >= 7 && currentMonth <= 9) {
            c.set(Calendar.MONTH, 6);
        } else if (currentMonth >= 10 && currentMonth <= 12) {
            c.set(Calendar.MONTH, 9);
        }
        c.set(Calendar.DATE, 1);
        return to0SecondOfDay(c.getTime());
    }
    /**
     * 获取当前季度结束时间
     *
     * @return string yyyy-MM-dd
     */
    public static String currentQuarterEndTime() {
        SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");
        return shortSdf.format(currentQuarterEndDate());
    }

    /**
     * 获取当前季度结束时间
     *
     * @return Date
     */
    private static Date currentQuarterEndDate() {
        Calendar c = Calendar.getInstance();
        int currentMonth = c.get(Calendar.MONTH) + 1;
        if (currentMonth >= 1 && currentMonth <= 3) {
            c.set(Calendar.MONTH, 2);
            c.set(Calendar.DATE, 31);
        } else if (currentMonth >= 4 && currentMonth <= 6) {
            c.set(Calendar.MONTH, 5);
            c.set(Calendar.DATE, 30);
        } else if (currentMonth >= 7 && currentMonth <= 9) {
            c.set(Calendar.MONTH, 8);
            c.set(Calendar.DATE, 30);
        } else if (currentMonth >= 10 && currentMonth <= 12) {
            c.set(Calendar.MONTH, 11);
            c.set(Calendar.DATE, 31);
        }
        return toLastSecondOfDay(c.getTime());
    }

    /**
     * 获取当前月第一天
     *
     * @return Date
     */
    public static Date firstDayOfMonth() {
        Calendar c = Calendar.getInstance();
        firstDayOfMonth(c);
        return c.getTime();
    }

    /**
     * 获取当前月最后一天
     *
     * @return Date
     */
    public static Date lastDayOfMonth() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));//设置为当前月最后一天
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTime();
    }


    /**
     * 如果date时间年份小于2019，在2019的基础上加100在开始;大于等于，开始时间年份+100
     *
     * @return Date yyyy-MM-dd 23:59:59.999 ; date=null return null
     */
    public static Date yearAdd100(Date date) {
        Calendar cn = Calendar.getInstance();
        if (date != null) {
            cn.setTime(date);
        }
        int year = cn.get(Calendar.YEAR);
        if (year < 2019) {
            cn.set(Calendar.YEAR, 2019);
        }
        cn.add(Calendar.YEAR, 100);
        return cn.getTime();
    }

    /**
     * 判断date是否在当前季度内
     *
     * @param date 日期
     * @return in返回true no返回false;date 为null返回false
     */
    public static boolean inCurrentQuarter(Date date) {
        if (date == null) {
            return false;
        }
        Date currentQuarterStartDate = currentQuarterStartDate();
        Date currentQuarterEndDate = currentQuarterEndDate();
        return date.after(currentQuarterStartDate) && date.before(currentQuarterEndDate);
    }

    /**
     * 判断date是否在当前月内
     *
     * @param date 日期
     * @return in返回true no返回false;date 为null返回false
     */
    public static boolean inCurrentMonth(Date date) {
        if (date == null) {
            return false;
        }
        Date now = new Date();
        return getYear(now).equals(getYear(date)) && getMonth(now).equals(getMonth(date));
    }

    /**
     * 季度 1,2,3,4
     */
    public enum Quarter {
        // 第一季度"
        ONE(1, 1, 3, "第一季度"),
        // 第二季度"
        TWO(2, 4, 6, "第二季度"),
        // 第三季度
        THREE(3, 7, 9, "第三季度"),
        // 第四季度
        FOUR(4, 10, 12, "第四季度");
        /**
         * 季度
         */
        private final int quarter;
        /**
         * 季度开始月份
         */
        private final int startMonth;
        /**
         * 季度截止月份
         */
        private final int endMonth;
        /**
         * 描述
         */
        private final String description;

        public int getQuarter() {
            return quarter;
        }

        public int getStartMonth() {
            return startMonth;
        }

        public int getEndMonth() {
            return endMonth;
        }

        public String getDescription() {
            return description;
        }

        /**
         * @param quarter     季度
         * @param startMonth  开始月份
         * @param endMonth    结束月份
         * @param description 中文描述
         */
        Quarter(int quarter, int startMonth, int endMonth, String description) {
            this.quarter = quarter;
            this.startMonth = startMonth;
            this.endMonth = endMonth;
            this.description = description;
        }

        /**
         * 季度
         *
         * @param quarter 季度
         * @return Quarter
         */
        public static Quarter quarter(int quarter) {
            Quarter currentQuarter = null;
            for (Quarter q : Quarter.values()) {
                if (q.getQuarter() == quarter) {
                    currentQuarter = q;
                    break;
                }
            }
            return currentQuarter;
        }

    }

    /**
     * 指定日期的季度
     *
     * @param date 指定日期为null，返回null
     * @return 未匹配季度 返回null
     */
    public static Quarter getQuarter(Date date) {
        if (date == null) {
            return null;
        }
        int currentMonth = getMonth(date);
        return getQuarterByMonth(currentMonth);
    }

    /**
     * 指定月份的季度
     *
     * @param currentMonth 月份
     * @return Quarter
     */
    public static Quarter getQuarterByMonth(int currentMonth) {
        Quarter currentQuarter = null;
        for (Quarter quarter : Quarter.values()) {
            if (quarter.startMonth <= currentMonth && quarter.endMonth >= currentMonth) {
                currentQuarter = quarter;
                break;
            }
        }
        return currentQuarter;
    }

    /**
     * 指定年份某个季度的最后一天
     *
     * @return 未匹配季度 返回null
     */
    public static Date lastDateOfQuarter(Integer year, Integer quarter) {
        if (year == null || quarter == null || quarter > 4) {
            return null;
        }
        Quarter q = Quarter.quarter(quarter);
        if (q == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        // Calendar 中月份0表示1月 Quarter 获取的月份需要减1
        c.set(Calendar.MONTH, q.getEndMonth() - 1);
        setLastDayOfMonth(c);
        return c.getTime();
    }

    /**
     * 设置为当前月最后一天
     *
     * @param c Calendar
     */
    private static void setLastDayOfMonth(Calendar c) {
        // 设置为当前月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        c.set(Calendar.HOUR_OF_DAY, c.getActualMaximum(Calendar.HOUR_OF_DAY));
        c.set(Calendar.MINUTE, c.getActualMaximum(Calendar.MINUTE));
        c.set(Calendar.SECOND, c.getActualMaximum(Calendar.SECOND));
        c.set(Calendar.MILLISECOND, c.getActualMaximum(Calendar.MILLISECOND));
    }

    /**
     * 指定年份某个季度的第一天
     *
     * @return 未匹配季度 返回null
     */
    public static Date firstDateOfQuarter(Integer year, Integer quarter) {
        if (year == null || quarter == null || quarter > 4) {
            return null;
        }
        Quarter q = Quarter.quarter(quarter);
        if (q == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        // Calendar 中月份0表示1月 Quarter 获取的月份需要减1
        c.set(Calendar.MONTH, q.getStartMonth() - 1);
        firstDayOfMonth(c);
        return c.getTime();
    }


    /**
     * 获取当前月第一天
     *
     * @return Date
     */
    private static Date firstDayOfMonth(Calendar c) {
        c.set(Calendar.DAY_OF_MONTH, c.getActualMinimum(Calendar.DAY_OF_MONTH)); // 设置为1号,当前日期既为本月第一天
        c.set(Calendar.HOUR_OF_DAY, c.getActualMinimum(Calendar.HOUR_OF_DAY));
        c.set(Calendar.MINUTE, c.getActualMinimum(Calendar.MINUTE));
        c.set(Calendar.SECOND, c.getActualMinimum(Calendar.SECOND));
        c.set(Calendar.MILLISECOND, c.getActualMinimum(Calendar.MILLISECOND));
        return c.getTime();
    }

    /**
     * LocalDateTime类型日期转String
     *
     * @param date
     * @return
     */
    public static String localDateTimeToString(LocalDate date, String format) {
        if (Objects.nonNull(date)) {
            if (StringUtils.isBlank(format)) {
                format = DATE_FORMAT;
            }
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern(format);
            return fmt.format(date);
        } else {
            return "";
        }
    }

    /**
     * LocalDateTime类型日期转String
     *
     * @param date
     * @return
     */
    public static String localDateTimeToString(LocalDateTime date, String format) {
        if (Objects.nonNull(date)) {
            if (StringUtils.isBlank(format)) {
                format = DATE_FORMAT;
            }
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern(format);
            return fmt.format(date);
        } else {
            return "";
        }
    }

    /**
     * String转LocalDateTime类型日期
     *
     * @param dateStr
     * @return
     */
    public static LocalDateTime stringToLocalDateTime(String dateStr, String format) {
        return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(format));
    }
    /**
     * 获取当前时间对应月数偏移量的时间
     * @param month
     * @param format 指定的格式
     * @param dt   指定的日期
     * @return
     */
    public static String getDateByMonths(Integer month,String format,String dt) throws Exception{
        if(org.springframework.util.StringUtils.isEmpty(format)){
            format = "yyyy-MM";
        }
        SimpleDateFormat df=new SimpleDateFormat(format);
        Date date;
        if(org.springframework.util.StringUtils.isEmpty(dt)){
            date = new Date();
        }else {
            date = df.parse(dt);
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //日期天偏移量
        c.add(Calendar.MONTH, month);
        date = c.getTime();
        return df.format(date);
    }

    /**
     * 获取当前时间对应天数偏移量的时间
     * @param day
     * @return
     */
    public static String getDateByDays(Integer day,String format,String dt) throws Exception{
        if(org.springframework.util.StringUtils.isEmpty(format)){
            format = "yyyy-MM-dd";
        }
        SimpleDateFormat df=new SimpleDateFormat(format);
        Date date;
        if(org.springframework.util.StringUtils.isEmpty(dt)){
            date = new Date();
        }else {
            date = df.parse(dt);
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //日期天偏移量
        c.add(Calendar.DATE, day);
        date = c.getTime();
        return df.format(date);
    }

    /**
     * 转换字符串到日期对象
     * @param dateString
     * @param dateFormat
     * @return
     */
    public static Date stringToDate(String dateString,String dateFormat){
        if(org.springframework.util.StringUtils.isEmpty(dateFormat)){
            dateFormat = "yyyy-MM-dd HH:mm:ss";
        }
        return DateUtil.parse(dateString,dateFormat);
    }

    /**
     * 判断是否在当前月之后
     * @param statsDate
     * @return
     */
    public static boolean afertThisMonth(String statsDate){
        SimpleDateFormat df=new SimpleDateFormat("yyyy-MM");
        Date date = new Date();

        return df.format(date).compareTo(statsDate)<0;
    }

    /**
     * 判断是否在当前月之前
     * @param statsDate
     * @return
     */
    public static boolean beforeThisMonth(String statsDate){
        SimpleDateFormat df=new SimpleDateFormat("yyyy-MM");
        Date date = new Date();

        return df.format(date).compareTo(statsDate)>0;
    }

}
