package com.goodsogood.ows.utils;

import com.goodsogood.ows.common.pojo.PageBean;

/**
 * <AUTHOR>
 * @create 2018-10-24 10:44
 **/
public class PageUtils {

     /**
      * 初始化分页信息
      * @param pageNo
      * @param pageSize
      * @return
      */
     public static PageBean page(Integer pageNo, Integer pageSize) {
          if(pageNo == null) {
               pageNo = 1;
          }
          if(pageSize == null) {
               pageSize = 10;
          }

          PageBean page = new PageBean();
          page.setPageNo(pageNo);
          page.setPageSize(pageSize);

          return page;
     }

}
