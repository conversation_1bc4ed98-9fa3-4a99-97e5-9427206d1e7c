package com.goodsogood.ows.utils;

import lombok.extern.log4j.Log4j2;
import org.apache.tomcat.util.http.fileupload.FileUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Log4j2
public class PackageUtil {
    /**
     * @param path   要压缩的文件路径
     * @param format 生成的格式（zip、rar）d
     */
    public static String generateFile(String path, String format) throws Exception {

        File file = new File(path);

        // 压缩文件的路径不存在
        if (!file.exists()) {

            throw new Exception("路径 " + path + " 不存在文件，无法进行压缩...");

        }
        // 用于存放压缩文件的文件夹
//        String generateFile = file.getParent() + File.separator + UUID.randomUUID();
        String generateFile = file.getParent();

        File compress = new File(generateFile);

        // 如果文件夹不存在，进行创建
        if (!compress.exists()) {

            compress.mkdirs();

        }

        // 存放地址
        String generateFileName = compress.getAbsolutePath() + File.separator + "活动材料." + format;

        // 输入流 表示从一个源读取数据
        // 输出流 表示向一个目标写入数据
        FileOutputStream outputStream;
        ZipOutputStream zipOutputStream = null;
        try {
            // 输出流
            outputStream = new FileOutputStream(generateFileName);

            // 压缩输出流
            zipOutputStream = new ZipOutputStream(new BufferedOutputStream(outputStream));

            generateFile(zipOutputStream, file, "");

            log.debug("目的压缩文件生成位置：{}", generateFileName);
            // 关闭 输出流
            FileUtils.deleteDirectory(new File(path));

        } catch (IOException e) {

            e.fillInStackTrace();

        } finally {

            if (null != zipOutputStream) {

                zipOutputStream.close();

            }
        }
        return generateFileName;
    }

    /**
     * @param out  输出流
     * @param file 目标文件
     * @param dir  文件夹
     * @throws Exception
     */
    private static void generateFile(ZipOutputStream out, File file, String dir) throws Exception {

        // 当前的是文件夹，则进行一步处理
        if (file.isDirectory()) {
            //得到文件列表信息
            File[] files = file.listFiles();

            //将文件夹添加到下一级打包目录

            out.putNextEntry(new ZipEntry(dir + "/"));

            dir = dir.length() == 0 ? "" : dir + "/";

            //循环将文件夹中的文件打包
            for (File value : files) {
                generateFile(out, value, dir + value.getName());
            }

        } else { // 当前是文件

            // 输入流
            FileInputStream inputStream = new FileInputStream(file);
            // 标记要打包的条目
            out.putNextEntry(new ZipEntry(dir));
            // 进行写操作
            int len = 0;
            byte[] bytes = new byte[1024];
            while ((len = inputStream.read(bytes)) > 0) {
                out.write(bytes, 0, len);
            }
            // 关闭输入流
            inputStream.close();
        }

    }
}
