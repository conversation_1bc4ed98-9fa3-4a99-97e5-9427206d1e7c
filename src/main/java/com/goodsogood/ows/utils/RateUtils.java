package com.goodsogood.ows.utils;

import cn.hutool.core.convert.Convert;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 进度
 *
 * <AUTHOR>
 */
@Component
@Log4j2
public class RateUtils {

    private static final int FULL_MARKS = 100;

    private static final String RATE_TOTAL_KEY = "RATE_TOTAL_";
    private static final String RATE_CURRENT_KEY = "RATE_CURRENT_";

    private static StringRedisTemplate redisTemplate;

    @Autowired
    public RateUtils(StringRedisTemplate redisTemplate) {
        RateUtils.redisTemplate = redisTemplate;
    }

    /**
     * 构建进度缓存
     *
     * @param total
     * @return 当前进度id
     */
    public static String build(Integer total) {
        String uuid = UUID.randomUUID().toString();
        redisTemplate.opsForValue().set(RateUtils.RATE_TOTAL_KEY + uuid, total.toString());
        redisTemplate.opsForValue().increment(RateUtils.RATE_CURRENT_KEY + uuid, 0L);
        return uuid;
    }

    /**
     * 构建进度缓存
     *
     * @param total
     * @return 当前进度id
     */
    public static String build(Long total) {
        String uuid = UUID.randomUUID().toString();
        redisTemplate.opsForValue().set(RateUtils.RATE_TOTAL_KEY + uuid, total.toString());
        redisTemplate.opsForValue().increment(RateUtils.RATE_CURRENT_KEY + uuid, 0L);
        return uuid;
    }

    /**
     * 构建进度缓存 - 固定uuid
     *
     * @param total
     * @return 当前进度id
     */
    public static String build(Integer total, String uuid) {
        redisTemplate.opsForValue().set(RateUtils.RATE_TOTAL_KEY + uuid, total.toString());
        redisTemplate.opsForValue().increment(RateUtils.RATE_CURRENT_KEY + uuid, 0L);
        return uuid;
    }

    /**
     * 自增长
     *
     * @param uuid
     */
    public static void auto(String uuid) {
        try {
            Long current = redisTemplate.opsForValue().increment(RateUtils.RATE_CURRENT_KEY + uuid, 1L);
            String s = redisTemplate.opsForValue().get(RateUtils.RATE_TOTAL_KEY + uuid);
            log.debug("当前值->[{}]， 目标值->[{}]", current, s);
            if (current.equals(Long.parseLong(s))) {
                redisTemplate.expire(RateUtils.RATE_CURRENT_KEY + uuid, 10, TimeUnit.MINUTES);
                redisTemplate.expire(RateUtils.RATE_TOTAL_KEY + uuid, 10, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            log.error("自增长报错[{}] -> ", uuid, e);
        }
    }

    /**
     * 手动增长
     *
     * @param uuid
     */
    public static void update(String uuid, long delta) {
        try {
            Long current = redisTemplate.opsForValue().increment(RateUtils.RATE_CURRENT_KEY + uuid, delta);
            String s = redisTemplate.opsForValue().get(RateUtils.RATE_TOTAL_KEY + uuid);
            if (current >= Long.parseLong(s)) {
                redisTemplate.expire(RateUtils.RATE_CURRENT_KEY + uuid, 10, TimeUnit.MINUTES);
                redisTemplate.expire(RateUtils.RATE_TOTAL_KEY + uuid, 10, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            log.error("手动增长报错[{}] -> ", uuid, e);
        }
    }

    /**
     * 是否完成
     *
     * @param uuid
     * @return
     */
    public static boolean complete(String uuid) {
        if (Boolean.TRUE.equals(redisTemplate.hasKey(RateUtils.RATE_TOTAL_KEY + uuid))) {
            Long current = redisTemplate.opsForValue().increment(RateUtils.RATE_CURRENT_KEY + uuid, 0L);
            String s = redisTemplate.opsForValue().get(RateUtils.RATE_TOTAL_KEY + uuid);
            return Convert.toStr(current).equals(s);
        } else {
            return false;
        }
    }

    /**
     * 获取进度
     *
     * @param uuid
     * @param type 1-原始数据, 2-百分比
     * @return
     */
    public static String getRate(String uuid, int type) {
        String result = null;
        try {
            if (Boolean.TRUE.equals(redisTemplate.hasKey(RateUtils.RATE_TOTAL_KEY + uuid))) {
                Long current = redisTemplate.opsForValue().increment(RateUtils.RATE_CURRENT_KEY + uuid, 0L);
                String total = redisTemplate.opsForValue().get(RateUtils.RATE_TOTAL_KEY + uuid);
                if (type == 1) {
                    result = String.format("%s-%s", current, total);
                } else {
                    assert current != null;
                    assert total != null;
                    double c = current.doubleValue();
                    double t = Double.parseDouble(total);
                    double f1 = (t == 0 ? 100 : Math.round(c * 100 / t));
                    int percent = Double.valueOf(f1).intValue();
                    result = String.valueOf(percent);
                }
            }
        } catch (Exception e) {
            log.error("获取进度报错[{}] -> ", uuid, e);
        }
        return result;
    }

    /**
     * 获取进度 - 自定义满分
     *
     * @param uuid
     * @param type      1-原始数据, 2-百分比
     * @param fullMarks 自定义满分
     * @return
     */
    public static String getRate(String uuid, int type, int fullMarks) {
        String result = null;
        try {
            if (Boolean.TRUE.equals(redisTemplate.hasKey(RateUtils.RATE_TOTAL_KEY + uuid))) {
                Long current = redisTemplate.opsForValue().increment(RateUtils.RATE_CURRENT_KEY + uuid, 0L);
                String total = redisTemplate.opsForValue().get(RateUtils.RATE_TOTAL_KEY + uuid);
                if (type == 1) {
                    result = String.format("%s-%s", current, total);
                } else {
                    assert current != null;
                    assert total != null;
                    double c = current.doubleValue();
                    double t = Double.parseDouble(total);
                    double f1 = Math.round(c / t * fullMarks);
                    int percent = Double.valueOf(f1).intValue();
                    result = String.valueOf(percent);
                }
            }
        } catch (Exception e) {
            log.error("获取进度报错[{}] -> ", uuid, e);
        }
        return result;
    }

    /**
     * 删除进度
     *
     * @param uuid
     * @return
     */
    public static boolean delete(String uuid) {
        redisTemplate.delete(RateUtils.RATE_CURRENT_KEY + uuid);
        redisTemplate.delete(RateUtils.RATE_TOTAL_KEY + uuid);
        return true;
    }

    /**
     * 是否存在uuid
     *
     * @param uuid
     * @return
     */
    public static boolean hasKey(String uuid) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(RateUtils.RATE_TOTAL_KEY + uuid));
    }

    /**
     * 获取缓存值
     *
     * @param uuid
     * @return
     */
    public static String get(String uuid) {
        if (RateUtils.hasKey(uuid)) {
            return redisTemplate.opsForValue().get(RateUtils.RATE_TOTAL_KEY + uuid);
        }
        return null;
    }

    /**
     * 是否增长 - 适合多线程监控
     *
     * @param uuid
     * @param divisor
     * @param parentUuid
     * @return
     */
    public static void incr(String uuid, Integer divisor, String parentUuid) {
        Integer after = 0;
        while (true) {
            try {
                String percentStr = RateUtils.getRate(uuid, 2);
                Integer percent = Integer.valueOf(percentStr);
                if (RateUtils.complete(uuid)) {
                    percent = Integer.valueOf(percentStr);
                    log.debug("已完成 -> [{}], 上一次完成度[{}]", percent, after);
                    RateUtils.update(parentUuid, (percent - after) / divisor);
                    return;
                }
                if (percent > after && percent % divisor == 0) {
                    log.debug("已完成 -> [{}], 上一次完成度[{}]", percent, after);
                    RateUtils.update(parentUuid, (percent - after) / divisor);
                    after = percent;
                }
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                log.error("等待committee数据生成时，报错", e);
            } catch (Exception e) {
                log.error("计数出现问题，报错", e);
            }
        }
    }

    /**
     * 是否增长
     *
     * @param uuid
     * @param after
     * @param fullMarks
     * @param parentUuid
     * @return
     */
    public synchronized static Integer incr(String uuid, Integer after, Integer fullMarks, String parentUuid) {
        String percentStr = RateUtils.getRate(uuid, 2, fullMarks);
        Integer percent = Integer.valueOf(percentStr);
        if (percent > after) {
            log.debug("已完成 -> [{}], 上一次完成度[{}]", percent, after);
            if (!after.equals(fullMarks)) {
                RateUtils.update(parentUuid, percent - after);
            }
            after = percent;
        }
        return after;
    }

    /**
     * 是否增长
     *
     * @param uuid
     * @param after
     * @param fullMarks
     * @param parentUuid
     * @return
     */
    public synchronized static void incr(String uuid, AtomicReference<Integer> after, Integer fullMarks, String parentUuid) {
        String percentStr = RateUtils.getRate(uuid, 2, fullMarks);
        Integer percent = Integer.valueOf(percentStr);
        if (percent > after.get()) {
            log.debug("已完成 -> [{}], 上一次完成度[{}]", percent, after.get());
            if (after.get().intValue() != fullMarks.intValue()) {
                RateUtils.update(parentUuid, percent - after.get());
            }
            after.set(percent);
        }
    }
}
