package com.goodsogood.ows.repository

import com.goodsogood.ows.model.db.MeetingWorkPointEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Repository

/**
 * <AUTHOR>
 * @date 2023/10/9
 * @description class MeetingWorkPointRepository
 */
@Repository
interface MeetingWorkPointRepository : JpaRepository<MeetingWorkPointEntity, Long>,
        JpaSpecificationExecutor<MeetingWorkPointEntity> {

    fun findByOwnerIdAndYear(ownerId: Long, year: Int): List<MeetingWorkPointEntity>
}