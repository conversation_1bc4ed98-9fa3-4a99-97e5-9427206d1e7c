package com.goodsogood.ows.repository

import com.goodsogood.ows.model.db.MeetingLeaderSurveyEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Repository

/**
 * <AUTHOR>
 * @date 2023/10/10
 * @description class MeetingLeaderSurveryRepository
 */
@Repository
interface MeetingLeaderSurveyRepository : JpaRepository<MeetingLeaderSurveyEntity, Long>,
        JpaSpecificationExecutor<MeetingLeaderSurveyEntity> {
    // 通过year和type分组汇总计数
    fun countByYearAndSurveryType(year: Int, type: Int): Int?

}
