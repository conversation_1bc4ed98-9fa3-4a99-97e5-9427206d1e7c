package com.goodsogood.ows.repository

import com.goodsogood.ows.model.mongo.TopPriorityEntity
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDate

/**
 * <AUTHOR>
 * @date 2023/9/22
 * @description class 第一议题管理
 */
@Repository
interface TopPriorityRepository : MongoRepository<TopPriorityEntity, String> {
    fun findByTitleAndSourceTime(title: String, sourceTime: LocalDate): List<TopPriorityEntity>

    fun findByOrgIdAndTitleAndSourceTime(
        orgId: Long, title: String, sourceTime: LocalDate
    ): List<TopPriorityEntity>
}