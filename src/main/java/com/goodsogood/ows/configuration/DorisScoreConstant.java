package com.goodsogood.ows.configuration;

import io.swagger.models.auth.In;

/**
 * 该数据本应从t_score_rule_explain的id进行获取，但因为辅助决策统计粒度不确定，但业务系统传入doris需要提前处理，因此使用该配置方便后续对其积分最细粒度id进行统计。
 * 后续如果统计粒度一致，可将值设为一致。
 * 该值在t_score_rule_explain的rule_explin_id中需存在，如不存在，需要添加，用以获取score_type以及parent_score_type。
 */
public class DorisScoreConstant {
    public static final Integer MEETING_BIGPARTY_USER = 10;//按时参加支部党员大会-人员
    public static final Integer MEETING_GROUP_LEADER_USER = 11;//党小组组长-人员
    public static final Integer MEETING_GROUP_USER = 12;//按时参加党小组会-人员
    public static final Integer MEETING_LECTURE_USER = 13;//按时参加党课-人员
    public static final Integer MEETING_GROUP_NOLEADER_USER = 14;//党小组组长未按时召开-人员
    public static final Integer MEETING_THEME_USER = 15;//按时参加主题党日-人员
    public static final Integer MEETING_TEACHER_USER = 16;//讲课人-人员
    public static final Integer MEETING_BRANCH_USER = 17;//支委会成员-人员
    public static final Integer MEETING_NOBRANCH_USER = 18;//支委会成员未按时参加支委会-人员


    public static final Integer MEETING_BIGPARTY_ORG = 41;//按时召开支部党员大会-机构
    public static final Integer MEETING_BRANCH_ORG = 42;//按时召开支委会
    public static final Integer MEETING_GROUP_ORG = 43;//按时召开党小组会
    public static final Integer MEETING_LECTURE_ORG = 44;//按时召开党课
    public static final Integer MEETING_THEME_ORG = 45;//按时召开主题党日

    /**
     *
     */
    public static final Integer MEETING_COMMEND_GOOD_USER = 19;//人员创先争优中的奖励部分
    public static final Integer MEETING_COMMEND_BAD_USER = 20;//人员创先争优中的惩罚部分
    public static final Integer MEETING_COMMEND_GOOD_ORG = 22;//组织创先争优中的奖励部分-下级支部
    public static final Integer MEETING_COMMEND_BAD_ORG = 21;//组织创先争优中的惩罚部分
    public static final Integer MEETING_COMMEND_GOOD_USER_ORG = 26;//组织创先争优中的奖励部分-党员







}
