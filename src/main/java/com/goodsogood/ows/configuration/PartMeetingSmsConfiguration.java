package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

/**
 * 短信推送业务配置：领导干部参加支部活动
 *
 * <AUTHOR>
 * @date 2019-05-21 11:03
 * @since 1.0.3
 **/
@Data
@Component
@Validated
@ConfigurationProperties(prefix = PartMeetingSmsConfiguration.PREFIX)
public class PartMeetingSmsConfiguration {

    public static final String PREFIX = "meeting.sms.leader";

    /**
     * 是否推送 true,推送 ， false 不推送
     */
    @NotNull
    private Boolean send = true;

    /**
     * 模板号
     */
    @NotNull
    private Integer msgTemplateId = 44;

    /**
     * 渠道类型 1:短信 2:微信
     */
    @NotNull
    private Integer msgChannelType = 1;

}
