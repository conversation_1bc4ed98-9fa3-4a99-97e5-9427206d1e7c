package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * @Author: mengting
 * @Date: 2022/4/1 16:48
 */
//@ConfigurationProperties(prefix = "model-dingding-url")
//@Data
//@Configuration
public class MeetingUrlConfig {
//    Map<Integer,String> orgLifeMap;
//    Map<Integer,String> lifeMap;
//
//    public String getOrgLifeModelUrl(Integer model){
//        if(null==orgLifeMap.get(model)){
//            return "";
//        }
//        return orgLifeMap.get(model);
//    }
//
//
//    public String getLifeModelUrl(Integer model){
//        if(null==lifeMap.get(model)){
//            return "";
//        }
//        return lifeMap.get(model);
//    }

}
