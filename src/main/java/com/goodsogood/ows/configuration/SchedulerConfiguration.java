package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

/**
 * 定时任务-配置文件
 * <AUTHOR>
 * @date 2019-05-13 11:08
 * @since 1.0.3
 **/
@Data
@Component
@Validated
@ConfigurationProperties(prefix = SchedulerConfiguration.PREFIX)
public class SchedulerConfiguration {

     public static final String PREFIX = "scheduler";

     private DingEventSync dingEventSync;

     @Value("${scheduler.notice.run}")
     @NotNull
     private Boolean noticeRun;

     @Value("${scheduler.notice.page-szie}")
     @NotNull
     private Integer pageSize;

     /**
      * 主题推送开关
      */
     @Value("${scheduler.topic-push.run}")
     @NotNull
     private Boolean topicPushRun;

     /**
      * 未完成通知跳转前缀
      */
     @Value("${scheduler.notice.prefix}")
     private String prefix;

     /**
      * 未完成通知跳转活动发起(需要urlEncoding)
      */
     @Value("${scheduler.notice.redirect-url-add}")
     private String redirectUrlAdd;

     /**
      * 未完成通知跳转活动发起(需要urlEncoding)
      */
     @Value("${scheduler.notice.szf-redirect-url-add}")
     private String szfRedirectUrlAdd;

     /**
      * 有日程的活动结束后同步钉钉日程的签到状态定时任务
      */
     @Data
     public static class DingEventSync {
          private Boolean runFlag;
     }

}
