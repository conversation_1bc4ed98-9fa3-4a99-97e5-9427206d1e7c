package com.goodsogood.ows.configuration;

import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2020/4/29
 * Description: v3.0.0  配置中心
 */
@Configuration
public class ApplicationConfiguration {

    @Value("${tog-services.user-center}")
    private String userCenterName;

    @Bean
    public SimpleApplicationConfigHelper applicationConfigHelper(StringRedisTemplate redisTemplate,
                                                                 RestTemplate restTemplate,
                                                                 SaasConf saasConf) {
        // 此处三个参数分别为，StringRedisTemplate
        // restTemplate，注意如果是内部应用直接使用spring cloud的restTemplate，如果是消息中心、微信中心这种独立的项目，请使用原生的restTemplate
        // 第三个参数为用户中心的服务名称，如果是内部应用根据环境自己配置即可，如果是微信中心、消息中心请填写网关地址
        // 第四个参数为默认label，不传默认使用此label
        return new SimpleApplicationConfigHelper(redisTemplate, restTemplate, userCenterName, saasConf.getLabel());
    }

}
