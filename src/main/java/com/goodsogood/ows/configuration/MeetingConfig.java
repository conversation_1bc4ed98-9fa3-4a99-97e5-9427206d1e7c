package com.goodsogood.ows.configuration;

import com.goodsogood.ows.model.vo.PushWechatTemplateForm;
import com.goodsogood.ows.push.template.wechat.WorkNoticeTemplate;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @date 08/03/2018
 * @description 读取yml 会议计划定时派发任务相关配置
 */
@Component
@ConfigurationProperties(prefix = "meeting")
@Validated
@Data
public class MeetingConfig {

    /**
     * 党组织编码
     */
    private int partyCode = 102803;

    /**
     * 新首页汇总
     */
    private boolean refreshIndexCollectRedis = true;

    /**
     * 缓存时间 单位TimeUnit.HOURS （默认2h）meeting-task-undone-org-stats-cache-time
     */
    private Integer meetingTaskUndoneOrgStatsCacheTime = 2;

    private TopicConfig topic = new TopicConfig();

    private NoticeConfig notice = new NoticeConfig();


    @Component
    @Data
    public static class NoticeConfig {
        /**
         * 是否发送通知
         */
        private Boolean enable = true;
        private MeetingConfig.NoticeConfig.TemplateConfig template = new MeetingConfig.NoticeConfig.TemplateConfig();

        @Component
        @Data
        public static class TemplateConfig {
            private Long id = 7L;
            PushWechatTemplateForm.DataForm msg = new PushWechatTemplateForm.DataForm();

            {
                msg.setTitle("参加${meetingTypeNames}的通知");
                msg.setName("${name}");
                msg.setAddress("${address}");
                msg.setTime("${time}");
                msg.setRemark("点击详情查看活动。");
            }

            /**
             * 业务系统的application.name，此处为meeting
             */
            private String source = "meeting";
            /**
             * 渠道类型 2:微信
             */
            private Byte channelType = 2;
            /**
             * 旧版本跳转地址
             *
             * @since v1.0.6
             */
            private String checkPageToDetailUri = "/ssr/check-page-to-detail/2/${meetingId}/{oid}/{openId}";
            /**
             * SAAS 跳转微信中心后重定向的url check-page uri
             *
             * @since v3.0.0
             */
            private String redirectCheckPageUri =
                    "/v2/check-page/2?oid=${bindingOrgId}&login_type=wxcode"
                            + "&region_id=${regionId}&appid=${appId}&MeetingId=${meetingId}&to_detail=1";
            /**
             * 批量推送的人数
             */
            private Integer batchNum = 100;
        }
    }


    @Component
    @Data
    public static class TopicConfig {

        private NoticeConfig notice = new NoticeConfig();


        @Component
        @Data
        public static class NoticeConfig {
            /**
             * 是否发送通知
             */
            private Boolean enable = true;
            private SmsTemplateConfig smsTemplate = new SmsTemplateConfig();
            private WechatTemplateConfig wechatTemplate = new WechatTemplateConfig();

            @Component
            @Data
            public static class SmsTemplateConfig {
                private Long id = 26L;
                private String msg = "您收到一条新的待办事项：${name}。";
                /**
                 * 业务系统的application.name，此处为meeting
                 */
                private String source = "meeting";
                /**
                 * 渠道类型 1:短信
                 */
                private Byte channelType = 1;
            }

            @Component
            @Data
            public static class WechatTemplateConfig {
                private Long id = 46L;
                WorkNoticeTemplate.WorkNotice msg = new WorkNoticeTemplate.WorkNotice();

                {
                    msg.setTitle("您收到一条新的待办事项");
                    msg.setContent("${name}");
                    msg.setTime("${startTime} 至 ${endTime}");
                    msg.setRemark("点击查看详情。");
                }

                /**
                 * 用户管理多个组织时的模板信息。无跳转链接
                 */
                WorkNoticeTemplate.WorkNotice msgMore = new WorkNoticeTemplate.WorkNotice();

                {
                    msgMore.setTitle("您管理的多个组织收到新的待办事项");
                    msgMore.setContent("${name}");
                    msgMore.setTime("${startTime} 至 ${endTime}");
                    msgMore.setRemark("请进入我的任务查看详情。");
                }

                /**
                 * 业务系统的application.name，此处为meeting
                 */
                private String source = "meeting";
                /**
                 * 渠道类型 2:微信
                 */
                private Byte channelType = 2;
                /**
                 * 旧版本跳转地址
                 *
                 * @since v1.0.6
                 */
                private String checkPageToDetailUri = "/ssr/check-page-to-detail/6/${topicId}/${topicOrgId}/${orgId}/{oid}/{openId}";
                /**
                 * SAAS 跳转微信中心后重定向的url check-page uri
                 *
                 * @since v3.0.0
                 */
                private String redirectCheckPageUri =
                        "/v2/check-page/6?oid=${bindingOrgId}&login_type=wxcode"
                                + "&region_id=${regionId}&appid=${appId}&TopicId=${topicId}&TopicOrgId=${topicOrgId}&OrgId=${orgId}&to_detail=1";
                /**
                 * 批量推送的人数
                 */
                private Integer batchNum = 100;
            }
        }
    }
}
