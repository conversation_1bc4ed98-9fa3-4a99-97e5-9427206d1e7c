package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * Description: 微信中信息、前端相关配置
 *
 * <AUTHOR>
 * @version 2020/6/15 11:44
 */
@Component
@ConfigurationProperties(prefix = WechatConfiguration.MEETING_WECHAT_PREFIX)
@Validated
@Data
public class WechatConfiguration {
    public static final String MEETING_WECHAT_PREFIX = "meeting.wechat";
    /**
     * 域名
     */
    private String domain = "";
    /**
     * 微信中心登录接口
     */
    private String loginPreRouterUri =
            "/redirect/login_pre_router?project_name=${projectName}&appid=${appId}"
                    + "&scope=${scope}&version=${version}&org_id=${bindingOrgId}&routerType=${routerType}&redirect=${redirect}";

    /**
     * 重定向的微信登录地址
     */
    private String wxCheckPage = "/v2/check-page";

    /**
     * 项目名
     */
    private String projectName = "owsszf";
    /**
     * 应用授权作用域，snsapi_base （不弹出授权页面，直接跳转，只能获取用户openid）， snsapi_userinfo （弹出授权页面，可通过openid拿到昵称、性别、所在地。
     * 并且， 即使在未关注的情况下，只要用户授权，也能获取其信息 ）
     */
    private String scope = "snsapi_base";

    /**
     * 公众号APPID
     */
    private String appid = "";

    private Integer routerType = -2;

    /**
     * 回调接口的版本
     */
    private Integer version = 4;
}
