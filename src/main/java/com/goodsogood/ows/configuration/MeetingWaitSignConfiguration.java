package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/11/11
 */
@Data
@Component
@ConfigurationProperties(prefix = MeetingWaitSignConfiguration.PREFIX)
public class MeetingWaitSignConfiguration {
    public static final String PREFIX = "wait-sign";

    private Map<Long, MeetingWaitSignConfigVO> region;

    @Data
    public static class MeetingWaitSignConfigVO {
        private String prefix;
        private String redirectUrl;
    }
}
