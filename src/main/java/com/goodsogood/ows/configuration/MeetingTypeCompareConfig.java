package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

@Data
@Component
@Validated
@ConfigurationProperties(prefix = MeetingTypeCompareConfig.PREFIX)
public class MeetingTypeCompareConfig {
    public static final String PREFIX = "meeting-type";

    private Map<Long,Long> typeCompare;


    //根据红岩魂的meeting_type获取烟草的meeting_type
    public Long getTobaccoType(Long szfType){
        if(typeCompare==null)
            return null;
        return typeCompare.get(szfType);
    }
}
