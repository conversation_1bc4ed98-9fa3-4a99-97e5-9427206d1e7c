package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : MeetingScoreConfig
 * <AUTHOR> tc
 * @Date: 2021/11/30 15:33
 * @Description : 组织生活积分
 */
@Component
@ConfigurationProperties(prefix = "meeting-score-conf-new")
@Data
public class MeetingScoreConfig {
    private Map<Long, ScoreConf> scoreConf;

    @Data
    public static class ScoreConf {

        /**
         * 基础分配置
         */
        private List<MeetingScore> basicsScore;

        /**
         * 用户积分配置
         */
        private List<MeetingScore> userScore;

        /**
         * 组织积分配置
         */
        private List<MeetingScore> orgScore;
    }
}
