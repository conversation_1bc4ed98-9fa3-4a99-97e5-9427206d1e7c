package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 模块名称配置
 *
 * <AUTHOR>
 * @create 2019-08-01
 **/
@Component
@ConfigurationProperties(prefix = RabbitmqQueueConfig.PREFIX)
@Data
public class RabbitmqQueueConfig {

     public static final String PREFIX = "rabbitmq-queue-sign";

     /** 组织生活积分操作队列 */
     private String meetingScore;

     /** 组织生活积分操作路由 */
     private String meetingScoreRouting;


}
