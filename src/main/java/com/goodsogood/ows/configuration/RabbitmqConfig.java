package com.goodsogood.ows.configuration;

import com.goodsogood.ows.common.Config;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;

/**
 * Rabbitmq配置
 *
 * <AUTHOR> tc
 * @date 2020/12/9
 */
@SpringBootConfiguration
public class RabbitmqConfig {

    private final RabbitmqQueueConfig rabbitmqQueueConfig;

    @Autowired
    public RabbitmqConfig(RabbitmqQueueConfig rabbitmqQueueConfig) {
        this.rabbitmqQueueConfig = rabbitmqQueueConfig;
    }

    /**
     * 构建DirectExchange交换机
     */
    @Bean
    public DirectExchange directExchange() {
        // 支持持久化，长期不用不删除
        return new DirectExchange(Config.RabbitmqConf.DIRECT_EXCHANGE_NAME, true, false);
    }

    /**
     * 配置一个工作模型队列,用户积分操作队列名
     */
    @Bean
    public Queue queueMeetingScore() {
        return new Queue(rabbitmqQueueConfig.getMeetingScore());
    }


    /**
     * 绑定交交换机和
     */
    @Bean
    public Binding meetingScoreBinding() {
        return BindingBuilder.bind(queueMeetingScore()).to(directExchange()).with(rabbitmqQueueConfig.getMeetingScoreRouting());
    }
}
