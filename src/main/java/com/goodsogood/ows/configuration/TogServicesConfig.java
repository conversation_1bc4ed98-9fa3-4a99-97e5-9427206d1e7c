package com.goodsogood.ows.configuration;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 模块名称配置
 *
 * <AUTHOR>
 * @create 2019-12-28
 **/
@Component
@ConfigurationProperties(prefix = TogServicesConfig.PREFIX)
@Data
public class TogServicesConfig {

     public static final String PREFIX = "tog-services";

     /** 文件中心 */
     @JsonProperty("file-center")
     private String fileCenter;

     /** 网宣平台 */
     @JsonProperty("cms-plat")
     private String cmsPlat;

     /** 工作流服务 */
     private String workflow;

     /** 活动平台 */
     @JsonProperty("activity-plat")
     private String activityPlat;

     /** 积分中心 */
     @JsonProperty("credit-center")
     private String creditCenter;

     /** 用户中心 */
     @JsonProperty("user-center")
     private String userCenter;

     /** 消息推送中心 */
     @JsonProperty("push-center")
     private String pushCenter;

     /** 统计中心 */
     @JsonProperty("sas")
     private String sas;

     /** 任务中心 */
     @JsonProperty("task-manager")
     private String taskManager;

     /** 任务插件中心 */
     @JsonProperty("pending-server")
     private String pendingServer;

     public String getFileCenter() {
          return fileCenter;
     }

     public void setFileCenter(String fileCenter) {
          this.fileCenter = fileCenter;
     }

     public String getCmsPlat() {
          return cmsPlat;
     }

     public void setCmsPlat(String cmsPlat) {
          this.cmsPlat = cmsPlat;
     }

     public String getWorkflow() {
          return workflow;
     }

     public void setWorkflow(String workflow) {
          this.workflow = workflow;
     }

     public String getActivityPlat() {
          return activityPlat;
     }

     public void setActivityPlat(String activityPlat) {
          this.activityPlat = activityPlat;
     }

     public String getCreditCenter() {
          return creditCenter;
     }

     public void setCreditCenter(String creditCenter) {
          this.creditCenter = creditCenter;
     }

     public String getUserCenter() {
          return userCenter;
     }

     public void setUserCenter(String userCenter) {
          this.userCenter = userCenter;
     }

     public String getPushCenter() {
          return pushCenter;
     }

     public void setPushCenter(String pushCenter) {
          this.pushCenter = pushCenter;
     }

     public String getSas() {
          return sas;
     }

     public void setSas(String sas) {
          this.sas = sas;
     }

     public String getTaskManager() {
          return taskManager;
     }

     public void setTaskManager(String taskManager) {
          this.taskManager = taskManager;
     }

     public String getPendingServer() {
          return pendingServer;
     }

     public void setPendingServer(String pendingServer) {
          this.pendingServer = pendingServer;
     }
}
