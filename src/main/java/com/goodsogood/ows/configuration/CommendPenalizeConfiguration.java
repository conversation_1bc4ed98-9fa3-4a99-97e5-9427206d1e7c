package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Component
@Validated
@ConfigurationProperties(prefix = CommendPenalizeConfiguration.PREFIX)
public class CommendPenalizeConfiguration {

    public static final String PREFIX = "commend-penalize";

    @NotNull
    private Long workflowIdNormal;

    @NotNull
    private Long workflowIdOther;

    @NotNull
    private Integer workflowTypeNormal;

    @NotNull
    private Integer workflowTypeOther;

    @NotNull
    private List<Score> userScoreList;

    @NotNull
    private List<Score> orgScoreList;

    private List<Score> changeOrgScore;

    @Data
    public static class Score {

        private Integer key;

        private Integer score;
    }
}
