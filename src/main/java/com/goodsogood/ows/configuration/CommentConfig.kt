package com.goodsogood.ows.configuration

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

/**
 *
 * <AUTHOR>
 * @createTime 2022年04月11日 19:22:00
 */
@Component
@ConfigurationProperties(prefix = "comment")
class CommentConfig {

    var msg: MsfInfo = MsfInfo()
}

data class MsfInfo(

    var channel: Int = 4,

    var url: String = "https://dangjian.cq.tobacco.gov.cn/html/ssr?redirect_url=%s&sel_org_id=%s",

    var noSelf: Msg = Msg(),

    var noAppraisal: Msg = Msg()
)

data class Msg(

    var templateId: Int = 0,

    var url: String? = null
)