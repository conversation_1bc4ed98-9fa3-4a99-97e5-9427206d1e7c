package com.goodsogood.ows.configuration;

import lombok.Data;

/**
 * @ClassName : MeetingScore
 * <AUTHOR> tc
 * @Date: 2021/11/30 15:33
 * @Description : 组织生活积分
 */
@Data
public class MeetingScore {

     /**
      * 活动类型
      * 1.党支部党员大会 2.党支部委员会会议 3.党小组会 4.党课  5.主题党日
      */
     private Integer meetingType;

     /**
      * 对应类型编号(数据里t_type表)
      */
     private Integer typeId;

     /**
      * 积分类型 1.党员考勤积分 2.讲课人积分 3.党小组长开展积分  4.支委会成员每年基础分 5.党小组长每年基础分
      */
     private Integer scoreType;

     /**
      * 积分周期 1 月度 2 季度 3 年度
      */
     private Integer cycle;

     /**
      * 单次加分值
      */
     private Long score;

     /**
      * 积分下限
      */
     private Integer min;

     /**
      * 积分上限
      */
     private Integer max;

     /**
      * 活动名称
      */
     private String logTxt;


     //积分中心要求增加，详见谭偲邮件：回复: 烟草积分任务完成情况提供接口
     private String remark;

     //1-只加积分  2-只同步到doris  3-既要加积分，又要同步doris
     private Integer type;

     //t_score_rule_explain中的主鍵
     private Integer ruleId;

}
