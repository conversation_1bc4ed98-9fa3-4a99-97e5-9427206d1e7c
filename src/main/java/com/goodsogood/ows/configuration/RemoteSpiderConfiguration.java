package com.goodsogood.ows.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 各区县可爬取新闻的类别配置文件
 *
 * <AUTHOR> ruoyu
 * @date : 2021/10/8
 */
@Component
@ConfigurationProperties(prefix = RemoteSpiderConfiguration.PREFIX)
public class RemoteSpiderConfiguration {
    public final static String PREFIX = "remote-spider";

    private String urlHost;

    private String timeCacheKey = "REMOTE_SPIDER_TIME_CACHE_KEY_";

    /**
     * 区县id/各个区县支持爬取的渠道
     * CpcLeaderPictures:习近平图片集 - 图片报道(http://cpc.people.com.cn/)
     * CpcImportant:党建要闻 - 新闻(http://dangjian.people.com.cn)
     * TobaccoIndustry:首页–行业资讯–烟草新闻–行业要闻(http://www.tobacco.gov.cn/)
     * TobaccoCqNews: 首页–烟草新闻–行业要闻渝烟新闻-渝烟新闻(https://cq.tobacco.gov.cn)
     * <p>
     * map<regionId,map<channel,ChannelColumn>>
     */
    private Map<Long, Map<String, ChannelColumn>> region;

    public static class ChannelColumn {

        /**
         * 该渠道来源str
         */
        private String source;

        /**
         * 该渠道所属字段名称
         */
        private List<String> columnName;

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public List<String> getColumnName() {
            return columnName;
        }

        public void setColumnName(List<String> columnName) {
            this.columnName = columnName;
        }
    }

    public String getUrlHost() {
        return urlHost;
    }

    public void setUrlHost(String urlHost) {
        this.urlHost = urlHost;
    }

    public String getTimeCacheKey() {
        return timeCacheKey;
    }

    public void setTimeCacheKey(String timeCacheKey) {
        this.timeCacheKey = timeCacheKey;
    }

    public Map<Long, Map<String, ChannelColumn>> getRegion() {
        return region;
    }

    public void setRegion(Map<Long, Map<String, ChannelColumn>> region) {
        this.region = region;
    }
}
