package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义发送通知的配置
 * <AUTHOR>
 * @date 2019-05-21 11:03
 * @since 1.0.3
 **/
@Data
@Component
@Validated
@ConfigurationProperties(prefix = MeetingNoticeConfiguration.PREFIX)
public class MeetingNoticeConfiguration {

     public static final String PREFIX = "scheduler.notice";

     @Valid
     private Map<Long, List<NoticeTypes>> types = new HashMap<>();

}
