package com.goodsogood.ows.configuration;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019-05-21 11:15
 * @since 1.0.3
 *
 * 增加主题党日提醒  tc 2021-11-19
 **/
@Data
public class NoticeTypes {

     /**
      * 消息号
      * 根据t_type表 主键
      * 党支部党员大会 1
      * 党支部委员会会议 2
      * 党小组会 3
      * 党课 4
      * 主题党日 5
      */
     @NotNull
     private Integer id;

     /**
      * 模板号
      */
     @NotNull
     private Integer msgTemplateId;

     /**
      * 渠道类型 1:短信 2:微信 3: 主题推送  4:钉钉
      */
     @NotNull
     private Integer msgChannelType;

     /**
      * 季度 1是2否
      */
     @NotNull
     private Integer quarter;

     /**
      * 业务描述
      */
     @NotNull
     private String biz;

     /**
      * 中文名
      */
     @NotNull
     private String name;
}
