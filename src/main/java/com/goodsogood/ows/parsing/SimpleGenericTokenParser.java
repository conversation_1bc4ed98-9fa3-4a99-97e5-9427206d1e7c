package com.goodsogood.ows.parsing;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.parsing.GenericTokenParser;
import org.apache.ibatis.parsing.ParsingException;
import org.apache.ibatis.parsing.TokenHandler;
import org.springframework.beans.BeanUtils;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * Description: 占位符解析 默认占位符 ${}
 *
 * <AUTHOR>
 * @version 2020/7/29 9:12
 */
public class SimpleGenericTokenParser extends GenericTokenParser {

  public SimpleGenericTokenParser(String openToken, String closeToken, TokenHandler handler) {
    super(openToken, closeToken, handler);
  }

  public static Builder builder() {
    return new Builder();
  }

  /**
   * 将属性值的占位符进行替换
   *
   * @param source 原始值
   * @param <T>    数据类型
   * @return 返回一个替换后的副本
   */
  public <T> T parse(T source) {
    if (source == null) {
      return null;
    }

    try {
      @SuppressWarnings("unchecked")
      T target = (T) source.getClass().newInstance();
      BeanUtils.copyProperties(source, target);
      Set<String> ignoreFieldSet = ignoreFields(target, ParseIgnoreField.class);
      BeanInfo beanInfo = Introspector.getBeanInfo(target.getClass());
      PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
      for (PropertyDescriptor property : propertyDescriptors) {
        String key = property.getName();
        if (key.compareToIgnoreCase("class") == 0 || ignoreFieldSet.contains(key)) {
          continue;
        }
        Method setter = property.getWriteMethod();
        if (setter == null) {
          continue;
        }
        Class<?>[] paramTypes = setter.getParameterTypes();
        if (paramTypes.length == 1 && paramTypes[0] == String.class) {
          // 获取原始值
          Method getter = property.getReadMethod();

          Object value = getter != null ? getter.invoke(target) : null;
          if (value != null) {
            setter.invoke(target, parse(value.toString()));
          }
        }
      }
      return target;
    } catch (Exception e) {
      throw new ParsingException(e);
    }
  }

  /**
   * 默认占位符 ${}
   */
  public static class Builder {
    private String openToken = "${";
    private String closeToken = "}";
    private final Map<String, String> variables = new HashMap<>();

    public SimpleGenericTokenParser build() {
      return new SimpleGenericTokenParser(openToken, closeToken, new SimpleTokenHandler(variables));
    }

    public Builder token(final String openToken, final String closeToken) {
      this.openToken = openToken;
      this.closeToken = closeToken;
      return this;
    }

    /**
     * 添加k,v
     *
     * @param source       资源类
     * @param ignoreFields 忽略属性名称
     * @return Builder
     */
    public Builder addFieldVal(final Object source, String... ignoreFields) {
      if (null != source) {
        Set<String> ignoreFieldSet = ignoreFields(source, AddIgnoreField.class);
        if (ArrayUtils.isNotEmpty(ignoreFields)) {
          ignoreFieldSet.addAll(Arrays.asList(ignoreFields));
        }
        if (source instanceof Map) {
          for (Map.Entry<?, ?> entry : ((Map<?, ?>) source).entrySet()) {
            Object k = entry.getKey();
            Object v = entry.getValue();
            String key;
            if (k == null) {
              variables.put(null, String.valueOf(v));
            } else if (!ignoreFieldSet.contains((key = String.valueOf(k)))) {
              variables.put(key, String.valueOf(v));
            }
          }
        } else {
          try {
            BeanInfo beanInfo = Introspector.getBeanInfo(source.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
              String key = property.getName();
              if (key.compareToIgnoreCase("class") == 0 || ArrayUtils.contains(ignoreFields, key)) {
                continue;
              }
              // 获取原始值
              Method getter = property.getReadMethod();
              Object value = getter != null ? getter.invoke(source) : null;
              variables.put(key, String.valueOf(value));
            }
          } catch (Exception e) {
            throw new ParsingException(e);
          }
        }
      }
      return this;
    }

    public Builder addFieldVal(final String key, final String v) {
      variables.put(key, v);
      return this;
    }
  }

  private static <T extends Annotation> Set<String> ignoreFields(
          Object source, Class<T> annotation) {
    Set<String> ignoreFields = new HashSet<>();
    if (null != source) {
      try {
        Field[] fields = source.getClass().getDeclaredFields();
        for (Field field : fields) {
          if (field.getAnnotation(annotation) != null) {
            ignoreFields.add(field.getName());
          }
        }
      } catch (Exception e) {
        throw new ParsingException(e);
      }
    }
    return ignoreFields;
  }

  /**
   * Description:
   *
   * <AUTHOR>
   * @version 2020/7/29 9:18
   */
  private static class SimpleTokenHandler implements TokenHandler {
    private final Map<String, String> variables;

    private SimpleTokenHandler(Map<String, String> variables) {
      this.variables = variables;
    }

    @Override
    public String handleToken(String content) {
      return variables.get(content);
    }
  }
}
