package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.TopicLogFileEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-29 18:17
 **/
@Repository
@Mapper
public interface TopicLogFileMapper extends tk.mybatis.mapper.common.Mapper<TopicLogFileEntity>, TopicLogFileInsertListMapper<TopicLogFileEntity> {
    /**
     * 查询答案
     *
     * @param topicLogId 组织参与任务具体日志id
     * @return List<TopicLogFileEntity>
     */
    @Select(" SELECT topic_log_file_id,name,path,file_name,size,is_del FROM t_topic_log_file" +
            " where topic_log_id = #{topicLogId}")
    @Results({
            @Result(property = "topicLogFileId", column = "topic_log_file_id"),
            @Result(property = "name", column = "name"),
            @Result(property = "path", column = "path"),
            @Result(property = "fileName", column = "file_name"),
            @Result(property = "size", column = "size"),
            @Result(property = "is_del", column = "is_del")
    })
    List<TopicLogFileEntity> findByTopicOrgId(@Param("topicLogId") Long topicLogId);
}