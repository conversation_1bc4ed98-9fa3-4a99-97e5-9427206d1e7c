package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.SbwShiftTaskEntity;
import com.goodsogood.ows.model.vo.SbwShiftTaskFrom;
import com.goodsogood.ows.model.vo.SbwShiftTaskListForm;
import com.goodsogood.ows.model.vo.SbwTaskForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface SbwShiftTaskMapper extends MyMapper<SbwShiftTaskEntity>{

    @Update("update t_meeting_sbw_shift_task set is_read = 1 where task_id = #{taskId} and is_read = 0")
    Integer isRead(@Param("taskId") Long taskId);

    /**
     * 更新任务状态
     * @return
     */
    @Update("update t_meeting_sbw_shift_task   " +
            "set status = 5 where status = 3 and end_time < date(now())")
    int taskStatus();


    /**
     * 代办列表
     * @param form
     * @return
     */
    @Select("<script>" +
            "select " +
            "t.shift_task_id shiftTaskId," +
            "t.task_id taskId," +
            "t.title," +
            "t.begin_time beginTime," +
            "t.end_time endTime," +
            "t.status," +
            "ifnull(san.num,0) submitNum," +
            "ifnull(vn.num,0) verifyNum," +
            "ifnull(un.num,0) unNum " +
            "from t_meeting_sbw_shift_task t " +
            "left join (select task_id,count(1) num from t_meeting_sbw_task_org where type in (1,2,4,5) group by task_id) san on t.task_id = san.task_id " +
            "left join (select task_id,count(1) num from t_meeting_sbw_task_org where type in (3,4,5) group by task_id) vn on t.task_id = vn.task_id " +
            "left join (select task_id,count(1) num from t_meeting_sbw_task_org where type in (0,3) and status = 4 group by task_id) un on t.task_id = un.task_id " +
            "where " +
            "t.is_del = 0 and t.org_id = #{orgId}" +
            "<if test =\"title != null and title !='' \"> and t.title like concat('%',#{title},'%')</if> " +
            "<if test = \"beginTime != null and endTime == null\"> and date(t.begin_time) <![CDATA[>= ]]> #{beginTime}</if> " +
            "<if test = \"beginTime == null and endTime != null\"> and date(t.end_time) <![CDATA[<= ]]> #{endTime}</if> " +
            "<if test = \"beginTime != null and endTime != null\"> and date(t.begin_time) <![CDATA[>= ]]> #{beginTime} and date(t.end_time) <![CDATA[<= ]]> #{endTime}</if> " +
            "order by t.task_id desc " +
            "</script>")
    List<SbwShiftTaskListForm> list(SbwShiftTaskListForm form);

    /**
     * 代办流水
     * @param taskId
     * @return
     */
    @Select("select " +
            "operate_org_name orgName," +
            "group_concat(type) typeLink," +
            "content checkContent," +
            "create_time time " +
            "from t_meeting_sbw_task_flow " +
            "where task_id = #{taskId} and type in (-1,-2,-3) group by org_id")
    List<SbwShiftTaskFrom.ExecutiveLogging> flow(@Param("taskId")Long taskId);

    @Update("update t_meeting_sbw_shift_task set is_del = 1 where shift_task_id = #{shiftTaskId} and status = 1")
    int delShiftTask(@Param("shiftTaskId") Long shiftTaskId);

    @Select("select status from t_meeting_sbw_shift_task where shift_task_id = #{shiftTaskId}")
    Integer checkShiftStatusIsNotNull(@Param("shiftTaskId") Long shiftTaskId);

    @Select("select count(*) from t_meeting_sbw_shift_task where task_id = #{taskId} and is_del = 0")
    int checkShiftTask(@Param("taskId") Long taskId);

    /**
     * 代办流水
     * @param taskId
     * @return
     */
    @Select("select " +
            "operate_org_name orgName," +
            "group_concat(type) typeLink," +
            "content checkContent," +
            "create_time time " +
            "from t_meeting_sbw_task_flow " +
            "where task_id = #{taskId} and type in (-1,-2,-3) group by org_id")
    List<SbwTaskForm.ExecutiveLogging> taskFlow(@Param("taskId")Long taskId);

    @Select("select " +
            "count(*) " +
            "from " +
            "t_meeting_sbw_shift_task " +
            "where " +
            "task_id = #{taskId} " +
            "and end_time < DATE_ADD(DATE_ADD(str_to_date(DATE_FORMAT(NOW(),'%Y-%m-%d'),'%Y-%m-%d %H:%i:%s'),INTERVAL 1 DAY),INTERVAL -1 SECOND)")
    Integer timeStatus(@Param("taskId") Long taskId);
}
