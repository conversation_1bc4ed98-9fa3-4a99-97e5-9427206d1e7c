package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingAgendaEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: tc
 * @Description  活动议程
 */
@Repository
@Mapper
public interface MeetingAgendaMapper extends MyMapper<MeetingAgendaEntity> {

    /**
     * 查询会议关联的议程
     *
     * @param meetingId 会议id
     * @return MeetingAgendaEntity
     */
    @Select(" SELECT agenda_id,meeting_id,agenda_title,agenda_content,top_priority," +
            " last_update_time,last_change_user FROM t_meeting_agenda " +
            " where meeting_id = #{meetingId} ")
    @Results({
            @Result(property = "agendaId", column = "agenda_id"),
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "agendaTitle", column = "agenda_title"),
            @Result(property = "agendaContent", column = "agenda_content"),
            @Result(property = "topPriority", column = "top_priority"),
            @Result(property = "lastUpdateTime", column = "last_update_time"),
            @Result(property = "lastChangeUser", column = "last_change_user"),
            //处理one to many 活动标签
            @Result(property = "meetingTag", column = "agenda_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingTagMapper.findByAgendaId"))
    })
    List<MeetingAgendaEntity> findByMeetingId(@Param("meetingId") long meetingId);

    /**
     * 查询会议议程标题
     * @param meetingId
     * @return
     */
    @Select("select agenda_title from t_meeting_agenda where meeting_id = #{meetingId}")
    List<String> agendaTitle(@Param("meetingId") Long meetingId);
}