package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingPlanLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-24 17:56
 **/
@Repository
@Mapper
public interface MeetingPlanLogMapper extends MyMapper<MeetingPlanLogEntity>{

    @Update("<script> UPDATE t_meeting_plan_log SET num = num+1 "
            + " where 1=1"
            + "             <if test =\"ids != null and ids.size() > 0 \">" // 尝试重发时查询需要带组织生活id
            + "               and meeting_plan_log_id  in "
            + "               <foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                          #{item}\n"
            + "               </foreach>"
            + "             </if> "
            + " </script>")
    int updateNum(@Param("ids") List<Long> ids);

}