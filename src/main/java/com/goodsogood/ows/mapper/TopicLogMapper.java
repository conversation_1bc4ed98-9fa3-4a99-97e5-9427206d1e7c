package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.TopicLogEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-24 16:32
 **/
@Repository
@Mapper
public interface TopicLogMapper extends tk.mybatis.mapper.common.Mapper<TopicLogEntity>, TopicLogInsertListMapper<TopicLogEntity> {


     /**
      * 获取任务是否被回答过 -- 统计
      *
      * @param topicId
      * @param meetingTopicId
      * @return
      */
     @Select("<script>" +
             "select count(1) count from t_topic_log where topic_id = #{topicId} "
             + "<if test=\" meetingTopicId != null  \">  and meeting_topic_id = #{meetingTopicId} </if>"
             + "<if test=\" topicOrgId != null  \">  and topic_org_id = #{topicOrgId} </if>"
             + "</script>")
     int getMeetingTopicCount(@Param("topicId") Long topicId,
                              @Param("meetingTopicId") Long meetingTopicId,
                              @Param("topicOrgId") Long topicOrgId);

     /**
      * 删除一个
      *
      * @param topicId
      * @param meetingTopicId
      */
     @Delete("<script>" + "delete from t_topic_log where topic_id = #{topicId} "
             + "<if test=\" meetingTopicId != null  \">  and meeting_topic_id = #{meetingTopicId} </if>"
             + "<if test=\" topicOrgId != null  \">  and topic_org_id = #{topicOrgId} </if>"
             + "</script>")
     void deleteLog(@Param("topicId") Long topicId,
                    @Param("meetingTopicId") Long meetingTopicId,
                    @Param("topicOrgId") Long topicOrgId);

     @Delete("<script>" + "delete from t_topic_log_file where topic_id = #{topicId} "
             + "<if test=\" meetingTopicId != null  \">  and meeting_topic_id = #{meetingTopicId} </if>"
             + "<if test=\" topicOrgId != null  \">  and topic_org_id = #{topicOrgId} </if>"
             + "</script>")
     void deleteLogFile(@Param("topicId") Long topicId,
                        @Param("meetingTopicId") Long meetingTopicId,
                        @Param("topicOrgId") Long topicOrgId);


     /**
      * 查询答案
      *
      * @param meetingTopicId 会议与议题的关联ID
      * @return List<TopicLogEntity>
      */
     @Select(" SELECT topic_log_id,meeting_topic_id,content_id,topic_id,ans_cnt,opts_id FROM t_topic_log" +
             " where meeting_topic_id = #{meetingTopicId}")
     @Results({
             @Result(property = "topicLogId", column = "topic_log_id"),
             @Result(property = "meetingTopicId", column = "meeting_topic_id"),
             @Result(property = "contentId", column = "content_id"),
             @Result(property = "topicId", column = "topic_id"),
             @Result(property = "ansCnt", column = "ans_cnt"),
             @Result(property = "optsId", column = "opts_id")
     })
     List<TopicLogEntity> findByMeetingTopicId(@Param("meetingTopicId") long meetingTopicId);


     /**
      * 查询答案
      *
      * @param topicOrgId 任务组织关联表id
      * @return List<TopicLogEntity>
      */
     @Select(" SELECT topic_log_id,meeting_topic_id,content_id,topic_id,ans_cnt,opts_id FROM t_topic_log" +
             " where topic_org_id = #{topicOrgId}")
     @Results({
             @Result(property = "topicLogId", column = "topic_log_id"),
             @Result(property = "meetingTopicId", column = "meeting_topic_id"),
             @Result(property = "contentId", column = "content_id"),
             @Result(property = "topicId", column = "topic_id"),
             @Result(property = "ansCnt", column = "ans_cnt"),
             @Result(property = "optsId", column = "opts_id"),
             //处理one to many 答案
             @Result(property = "files", column = "topic_log_id",
                     many = @Many(select = "com.goodsogood.ows.mapper.TopicLogFileMapper.findByTopicOrgId")),
     })
     List<TopicLogEntity> findByTopicOrgId(@Param("topicOrgId") Long topicOrgId);
}