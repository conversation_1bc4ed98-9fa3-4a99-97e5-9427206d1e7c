package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.CategoryEntity;
import com.goodsogood.ows.model.db.MeetingPlanEntity;
import com.goodsogood.ows.model.db.MeetingTaskEntity;
import com.goodsogood.ows.model.db.TypeEntity;
import com.goodsogood.ows.model.vo.*;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-24 09:42
 **/
@Repository
@Mapper
public interface MeetingTaskMapper extends tk.mybatis.mapper.common.Mapper<MeetingTaskEntity>, MeetingTaskInsertListMapper<MeetingTaskEntity> {

    // 2019年9月19日 14:04:16 chenanshun
    @Select("<script> " +
            "SELECT a.meeting_task_id, a.meeting_plan_id, a.meeting_require_id,a.type_id,a.category_id,c.code,c.has_lecturer,c.has_lecture_title,c.type_sys," +
            " a.org_id,a.p_org_id,a.type,a.category,a.name,a.meeting_num,a.status,a.create_user,a.start_time,a.end_time, " +
            "  CASE " +
            "     WHEN a.STATUS = 1  AND a.end_time <![CDATA[ >=  ]]> CURDATE() THEN 1 " +
            "     WHEN a.STATUS = 1 AND a.end_time <![CDATA[ <  ]]> CURDATE() THEN 3 " +
            "     ELSE a.STATUS " +
            "  END AS status_form " +
            " FROM t_meeting_task a,t_meeting_plan b,t_type c " +
            "WHERE a.meeting_plan_id = b.meeting_plan_id and a.type_id=c.type_id" +
            //发起活动下拉查询
            " <if test =\"tag == 1\"> " +
            "     <choose>" +
            "           <when test=\"startTime != null \">" +
            "                 and a.start_time <![CDATA[ <=  ]]>  DATE_FORMAT(#{startTime},'%Y-%m-%d') and a.end_time <![CDATA[ >= ]]>  DATE_FORMAT(#{startTime},'%Y-%m-%d') " +
            "           </when>" +
            "           <otherwise>" +
            "                 and a.start_time <![CDATA[ <=  ]]>  CURDATE() and a.end_time <![CDATA[ >= ]]>  CURDATE() " +
            "           </otherwise>" +
            "     </choose>" +
            "</if> " +
            //创建任务时校验是否有未结束的任务
            " <if test =\"tag == 3\">" +
            "            and a.end_time <![CDATA[ >= ]]>  CURDATE() " +
            "           <if test =\"orgIds != null and orgIds.size() > 0 \">" +
            "             and a.org_id  in " +
            "               <foreach collection=\"orgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "                          #{item}\n" +
            "               </foreach>" +
            "           </if> " +
            "</if> " +
            " <if test =\"ids != null and ids.size() > 0 \">" +
            "  and a.meeting_task_id in " +
            "    <foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "               #{item}\n" +
            "    </foreach>" +
            "</if> " +
            " <if test =\"planId != null\"> and a.meeting_plan_id = #{planId}</if> " +
            " <if test =\"planName != null and planName != ''\"> and a.name like  \"%\"#{planName}\"%\"</if> " +
            " <if test =\"pOrgId != null\"> and b.org_id = #{pOrgId}</if> " +
            " <if test =\"typeIds != null and typeIds.size() > 0 \">" +
            "  and a.type_id in " +
            "    <foreach collection=\"typeIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "               #{item}\n" +
            "    </foreach>" +
            "</if> " +
            " <if test =\"categoryId != null\"> and a.category_id = #{categoryId}</if> " +
            "<choose>" +
            "      <when test=\"isH5 != null and isH5 == 1\">" + // 移动端查询
            "           <if test =\"orgIds != null and orgIds.size() > 0 \">" +
            "             and a.org_id  in " +
            "               <foreach collection=\"orgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "                          #{item}\n" +
            "               </foreach>" +
            "           </if> " +
            "           <if test =\"status != null\"> and a.status = #{status} </if> " +
            "      </when>" +
            "      <otherwise>" + // PC端查询
            "            <if test =\"orgId != null\"> and a.org_id = #{orgId}</if> " +
            "            <choose>" +
            "                  <when test=\"status != null and status == 1\">" + // 未完成
            "                      and a.status = 1 and a.end_time <![CDATA[ >= ]]>  CURDATE() " +
            "                  </when>" +
            "                  <when test=\"status != null and status == 2\">" + // 已完成
            "                      and a.status = 2 " +
            "                  </when>" +
            "                  <when test=\"status != null and status == 3\">" + // 已逾期
            "                      and a.status = 1 and a.end_time <![CDATA[ <  ]]>  CURDATE() " +
            "                  </when>" +
            "            </choose>" +
            "      </otherwise>" +
            "</choose>" +
            " <if test =\"type != null and type != ''\"> and a.type like  \"%\"#{type}\"%\"</if> " +
            " <if test =\"sStartTime != null\"> and a.start_time <![CDATA[ >=  ]]> #{sStartTime}</if> " +
            " <if test =\"eStartTime != null\"> and a.start_time <![CDATA[ <=  ]]> #{eStartTime}</if> " +
            " <if test =\"sEndTime != null\"> and a.end_time <![CDATA[ >=  ]]> #{sEndTime}</if> " +
            " <if test =\"eEndTime != null\"> and a.end_time <![CDATA[ <=  ]]> #{eEndTime}</if> " +
            " ORDER BY a.meeting_num ASC,status_form ASC,a.start_time ASC,a.meeting_task_id " +
            " </script>")
    @Results({
            @Result(property = "meetingTaskId", column = "meeting_task_id"),
            @Result(property = "meetingPlanId", column = "meeting_plan_id"),
            @Result(property = "meetingRequireId", column = "meeting_require_id"),
            @Result(property = "typeId", column = "type_id"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "pOrgId", column = "p_org_id"),
            @Result(property = "type", column = "type"),
            @Result(property = "category", column = "category"),
            @Result(property = "name", column = "name"),
            @Result(property = "meetingNum", column = "meeting_num"),
            @Result(property = "status", column = "status"),
            @Result(property = "statusForm", column = "status_form"),
            @Result(property = "createUser", column = "create_user"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "code", column = "code"),
            @Result(property = "hasLecturer", column = "has_lecturer"),
            @Result(property = "hasLectureTitle", column = "has_lecture_title"),
            @Result(property = "typeSys", column = "type_sys"),
            //处理one to many
            @Result(property = "meetingRequireEntity", column = "meeting_require_id",
                    one = @One(select = "com.goodsogood.ows.mapper.MeetingRequireMapper.findById"))
    })
    List<MeetingTaskEntity> findByForm(MeetingTypeTaskListForm meetingTypeListForm);

    /**
     * 查询当前组织的任务所属组织
     *
     * @param orgId 查询组织
     */
    @Select("<script> " +
            " SELECT distinct b.org_id as orgId,b.org_name as orgName FROM t_meeting_task a,t_meeting_plan b " +
            " WHERE a.meeting_plan_id = b.meeting_plan_id " +
            "   and a.start_time <![CDATA[ <=  ]]>  CURDATE() " +
            "   and a.end_time <![CDATA[ >= ]]>  CURDATE() " +
            " <if test =\"orgId != null\"> and a.org_id = #{orgId}</if> " +
            " </script>")
    List<OrgForm> findPOrgIdByOrgId(@Param("orgId") Long orgId);

    /**
     * 查询当前组织的任务所属类别
     *
     * @param tag   1:发起活动下拉框；2:任务完成情况下拉框
     * @param orgId 查询组织
     */
    @Select("<script> " +
            " SELECT distinct a.category_id as categoryId,a.category as category  FROM t_meeting_task a,t_meeting_plan b " +
            " WHERE a.meeting_plan_id = b.meeting_plan_id " +
            " <if test =\"tag == 1\"> " +
            "   and a.start_time <![CDATA[ <=  ]]>  CURDATE() " +
            "   and a.end_time <![CDATA[ >= ]]>  CURDATE() " +
            " </if> " +
            " <if test =\"orgId != null\"> and a.org_id = #{orgId}</if> " +
            " </script>")
    List<CategoryEntity> findCategoryIdByOrgId(@Param("orgId") Long orgId, @Param("tag") short tag);

    /**
     * 统计各组织未完成的任务数量 不包含逾期任务
     */
    @Select("<script> "
            + "SELECT org_id as orgId,count(*) as num FROM t_meeting_task\n"
            + "WHERE `status`=1 AND end_time <![CDATA[ >= ]]> CURDATE()\n"
            + "GROUP BY org_id "
            + " </script>")
    List<UndoneTaskCountForm> undoneTaskCount();

    /**
     * 查询当前组织的任务所属组织生活
     *
     * @param orgId 查询组织
     * @param tag   1:发起活动下拉框；2:任务完成情况下拉框
     */
    @Select(
            "<script> "
                    + " SELECT distinct b.meeting_plan_id as meetingPlanId,b.name as name  FROM t_meeting_task a,t_meeting_plan b "
                    + " WHERE a.meeting_plan_id = b.meeting_plan_id "
                    + " <if test =\"tag == 1\"> "
                    + "   and a.start_time <![CDATA[ <=  ]]>  CURDATE() "
                    + "   and a.end_time <![CDATA[ >= ]]>  CURDATE() "
                    + " </if> "
                    + " <if test =\"orgId != null\"> and a.org_id = #{orgId}</if> "
                    + " </script>")
    List<MeetingPlanEntity> findPlanIdByOrgId(@Param("orgId") Long orgId, @Param("tag") short tag);

    /**
     * 查询当前组织的任务所属类型
     *
     * @param orgId 查询组织
     */
    @Select("<script> " +
            " SELECT distinct a.type_id as typeId,a.type as type,c.code,c.has_lecturer as hasLecturer,c.has_lecture_title as hasLectureTitle " +
            " FROM t_meeting_task a,t_meeting_plan b,t_type c " +
            " WHERE a.meeting_plan_id = b.meeting_plan_id and a.type_id=c.type_id " +
            " <if test =\"tag == 1\"> " +
            "   and a.start_time <![CDATA[ <=  ]]>  CURDATE() " +
            "   and a.end_time <![CDATA[ >= ]]>  CURDATE() " +
            " </if> " +
            " <if test =\"orgId != null\"> and a.org_id = #{orgId}</if> " +
            " </script>")
    List<TypeEntity> findTypeIdByOrgId(@Param("orgId") Long orgId, @Param("tag") short tag);

    /**
     * 更新活动任务的举办次数
     * @param addNum 添加次数
     */
    @Update("update t_meeting_task set meeting_num = (meeting_num + #{addNum}) where meeting_task_id = #{meetingTaskId}")
    void updateMeetingNumByTaskId(@Param("meetingTaskId") long meetingTaskId, @Param("addNum") int addNum);

    /**
     * 更新活动任务已完成
     */
    @Update("update t_meeting_task set status = #{status} where meeting_task_id = #{meetingTaskId}")
    void updateMeetingComplate(@Param("meetingTaskId") long meetingTaskId, @Param("status") int status);

    /**
     * 获取未完成的任务列表
     *
     * @return
     */
    @Select("SELECT\n" +
            "t.meeting_task_id meetingTaskId,\n" +
            "t.meeting_plan_id meetingPlanId,\n" +
            "t.meeting_require_id meetingRequireId,\n" +
            "t.type_id typeId,\n" +
            "t.category_id categoryId,\n" +
            "t.org_id orgId,\n" +
            "t.p_org_id pOrgId,\n" +
            "t.type type,\n" +
            "t.category category,\n" +
            "t.name name,\n" +
            "t.meeting_num meetingNum,\n" +
            "t.status status,\n" +
            "t.start_time startTime,\n" +
            "t.end_time endTime,\n" +
            "t.create_time createTime,\n" +
            "t.create_user createUser,\n" +
            "t.update_time updateTime,\n" +
            "t.last_change_user lastChangeUser\n" +
            "FROM\n" +
            "t_meeting_task t\n" +
            "WHERE\n" +
            "now() > end_time\n" +
            "AND STATUS = 1")
    List<MeetingTaskEntity> findUnfinishedTask();

    /**
     * 按月/季度统计未完成组织生活的组织
     *
     * @param orgList 组织集合把组织集合id用in串起来查询
     * @param typeId  活动类型id
     * @param model   0-查询本月，1-查询本季度
     * @return
     */
    @Select("<script> " +
                "SELECT\n" +
                    "t.org_id orgId\n" +
                    "<if test=\"orgType == 2\">\n" +
                        ", o.name orgName\n" +
                    "</if>\n" +
                    "FROM\n" +
                    "t_meeting_task t\n" +
                    "<if test=\"orgType == 2\">\n" +
                        "LEFT JOIN t_organization o ON t.org_id = o.organization_id\n" +
                    "</if>\n" +
                "WHERE\n" +
                    "t.`status` = 1\n" +
                    "<if test=\"model == 0\">\n" +
                        "AND DATE_FORMAT(t.start_time, '%Y%m') <![CDATA[ <=  ]]> DATE_FORMAT(CURDATE(), '%Y%m')\n" +
                        "AND DATE_FORMAT(t.end_time, '%Y%m') <![CDATA[ >=  ]]> DATE_FORMAT(CURDATE(), '%Y%m')\n" +
                    "</if>\n" +
                    "<if test=\"model == 1\">\n" +
                        "AND #{startTime} <![CDATA[ <=  ]]> t.end_time\n" +
                        "AND #{endTime}<![CDATA[ >=  ]]> start_time\n" +
                    "</if>\n" +
                    "AND t.type_id = #{typeId}\n" +
                    "AND t.org_id IN \n" +
                    "<foreach collection=\"orgList\" item=\"org\" open=\"(\" separator=\",\" close=\")\">\n" +
                        "#{org.orgId}\n" +
                    "</foreach>" +
                "GROUP BY\n" +
                    "t.org_id" +
            "</script> "
    )
    List<OrgStatus.UndoneOrg> meetingStatistics(@Param("orgList") List<Org> orgList,
                                                @Param("typeId") Long typeId,
                                                @Param("orgType") int orgType,
                                                @Param("model") int model,
                                                @Param("startTime") String startTime,
                                                @Param("endTime") String endTime);


    /**
     * 获取未完成的任务列表
     *
     * @return
     */
    @Select("<script> select org_id from t_meeting_task where status = 1 and type_id=#{typeId} and start_time &lt;= #{queryDate} and end_time &gt;= #{queryDate} </script>")
    List<Long> findUnfinishedTaskOid(@Param("typeId") Integer typeId,@Param("queryDate") String queryDate);

    /**
     * 根据类型编号获取未开展的活动会议列表
     *
     * @return
     */
    @Select("<script> select org_id from t_meeting_task where meeting_num = 0 and type_id=#{typeId} and start_time &lt;= #{queryDate} and end_time &gt;= #{queryDate} </script>")
    List<Long> findUnfinishedByTypeId(@Param("typeId") Integer typeId,@Param("queryDate") String queryDate);

    /**
     * 根据组织编号和类型，获取未完成的组织编号
     *
     * @return
     */
    @Select("<script> select org_id from t_meeting_task where meeting_num = 0 and type_id=#{typeId} and start_time &lt;= #{queryDate} and end_time &gt;= #{queryDate} " +
            " AND org_id IN " +
            " <foreach collection=\"orgIdList\" item=\"oid\" open=\"(\" separator=\",\" close=\")\"> #{oid} </foreach>" +
            " </script>")
    List<Long> findUnfinishedByOrgId(@Param("orgIdList") List<Long> orgIdList,@Param("typeId") Integer typeId,@Param("queryDate") String queryDate);


    /**
     * 根据类型编号、年份和组织编号获取活动开展情况
     *
     * @return
     */
    @Select("<script> select tmp1.org_id orgId,tmp1.type_id typeId,IFNULL(tmp2.meetingNum,0) meetingNum,DATE_FORMAT(tmp1.start_time,'%c') startMonth from ( " +
            " select meeting_task_id,org_id,type_id,start_time from t_meeting_task where type_id=#{typeId}" +
            " and org_id in <foreach collection=\"orgIdList\" item=\"oid\" open=\"(\" separator=\",\" close=\")\"> #{oid} </foreach>" +
            " and DATE_FORMAT(start_time,'%Y') &lt;= #{queryYear} and DATE_FORMAT(end_time,'%Y') &gt;= #{queryYear}) tmp1 LEFT JOIN (" +
            " select t3.meeting_task_id,t3.org_id orgId,count(1) meetingNum,DATE_FORMAT(t3.start_time,'%c') startMonth from t_meeting t1" +
            " INNER JOIN t_meeting_type t2 on t1.meeting_id = t2.meeting_id and t1.status in(7,12,13,14)" +
            " and t1.org_id in <foreach collection=\"orgIdList\" item=\"oid\" open=\"(\" separator=\",\" close=\")\"> #{oid} </foreach>" +
            " INNER JOIN t_meeting_task t3 on t2.meeting_task_id = t3.meeting_task_id and t3.type_id=#{typeId} and DATE_FORMAT(t3.start_time,'%Y') &lt;= #{queryYear}" +
            " and DATE_FORMAT(t3.end_time,'%Y') &gt;= #{queryYear} GROUP BY orgId,startMonth ORDER BY orgId,startMonth" +
            " ) tmp2 on tmp1.meeting_task_id = tmp2.meeting_task_id and tmp1.org_id = tmp2.orgId </script>")
    List<TaskQueryVo> findByTypeIdOrgIdQueryDate(@Param("typeId") Integer typeId, @Param("orgIdList") List<Long> orgIdList, @Param("queryYear") String queryYear);


    @Select("SELECT \n" +
            "round(count(mt_start_time)/count(*),4)*100 rate\n" +
            "FROM\n" +
            "(\n" +
            "SELECT \n" +
            "mt.org_id,\n" +
            "mt.type_id,\n" +
            "mt.type,\n" +
            "mt.start_time task_start_time,\n" +
            "mt.end_time task_end_time,\n" +
            "m.start_time mt_start_time,\n" +
            "m.submit_time mt_submit_time\n" +
            "FROM t_meeting_task mt \n" +
            "LEFT JOIN\n" +
            "(\n" +
            "SELECT m.org_id,\n" +
            "m.start_time,\n" +
            "mr.submit_time,\n" +
            "m.status,\n" +
            "ty.type_id \n" +
            "FROM t_meeting m \n" +
            "INNER JOIN t_meeting_type ty \n" +
            "ON m.meeting_id = ty.meeting_id \n" +
            "AND m.org_id = #{oid} \n" +
            "AND year(m.start_time) = year(now())\n" +
            "AND m.STATUS IN (7,12,13,14)\n" +
            "AND ty.type_id IN(1,2,3,4,5)\n" +
            "INNER JOIN t_meeting_result mr\n" +
            "ON m.meeting_id = mr.meeting_id\n" +
            ")m\n" +
            "ON mt.type_id = m.type_id\n" +
            "AND m.start_time >= mt.start_time\n" +
            "AND DATE_FORMAT(m.start_time,\"%Y-%m-%d\") <= mt.end_time\n" +
            "AND m.submit_time >= mt.start_time\n" +
            "AND DATE_FORMAT(m.submit_time,\"%Y-%m-%d\") <= mt.end_time\n" +
            "WHERE \n" +
            "year(mt.start_time)  = year(now()) \n" +
            "AND mt.org_id = #{oid}  \n" +
            "AND mt.type_id IN (1,2,3,4,5)\n" +
            "GROUP BY mt.meeting_task_id )a")
    BigDecimal statiscalOrgRate(@Param("oid") Long oid);
}