package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingResultEntity;
import com.goodsogood.ows.model.vo.MeetingLeaveForm;
import com.goodsogood.ows.model.vo.MeetingResultListForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-25 15:40
 **/
@Repository
@Mapper
public interface MeetingResultMapper extends MyMapper<MeetingResultEntity> {

    @SelectProvider(type = MeetingResultProvider.class, method = "getPageSql")
    List<MeetingResultListForm> page(@Param("oid") long oid,
                                     @Param("oper") int oper,
                                     @Param("orgName") String orgName,
                                     @Param("meetingClass") Integer meetingClass,
                                     @Param("meetingTypes") String meetingTypes,
                                     @Param("meetingStartTime") String meetingStartTime,
                                     @Param("meetingEndTime") String meetingEndTime,
                                     @Param("submitStartTime") String submitStartTime,
                                     @Param("submitEndTime") String submitEndTime);

}