package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.SbwTaskEntity;
import com.goodsogood.ows.model.db.SbwTaskOrgEntity;
import com.goodsogood.ows.model.vo.SbwNewTaskForm;
import com.goodsogood.ows.model.vo.SbwTaskListForm;
import com.goodsogood.ows.model.vo.SbwTaskOrgListForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 南岸区网信任务组织mapper
 * <AUTHOR>
 * @date 2021.07.29
 */
@Mapper
@Repository
public interface SbwTaskOrgMapper extends MyMapper<SbwTaskOrgEntity>{

    /**
     * 被分配任务列表
     * @param form
     * @return
     */
    @Select("<script>" +
            "select " +
            "o.task_id taskId," +
            "o.title," +
            "o.begin_time beginTime," +
            "o.end_time endTime," +
            "o.type, " +
            "o.status " +
            "from t_meeting_sbw_task_org o left join t_meeting_sbw_task t on o.task_id = t.task_id where " +
            "o.region_id = #{regionId} and o.org_id = #{orgId} and t.is_del = 0 and o.status > 2" +
            "<if test =\"title != null and title !='' \"> and o.title like concat('%',#{title},'%')</if> " +
            "<if test = \"beginTime != null and endTime == null\"> and date(o.begin_time) <![CDATA[>= ]]> #{beginTime}</if> " +
            "<if test = \"beginTime == null and endTime != null\"> and date(o.end_time) <![CDATA[<= ]]> #{endTime}</if> " +
            "<if test = \"beginTime != null and endTime != null\"> and date(o.begin_time) <![CDATA[>= ]]> #{beginTime} and date(o.end_time) <![CDATA[<= ]]> #{endTime}</if> " +
            "order by t.task_id desc " +
            "</script>")
    List<SbwTaskListForm> myOrgTask(SbwTaskListForm form);

    /**
     * 审核任务列表
     * @param form
     * @return
     */
    @Select("<script>" +
            "select " +
            "t.task_id taskId," +
            "t.title," +
            "t.begin_time beginTime," +
            "t.end_time endTime," +
            "t.status," +
            "ifnull(san.num,0) submitNum," +
            "ifnull(vn.num,0) verifyNum," +
            "ifnull(un.num,0) unNum " +
            "from t_meeting_sbw_task t " +
            "left join (select task_id,count(1) num from t_meeting_sbw_task_org where type > 0 and type != 3 group by task_id) san on t.task_id = san.task_id " +
            "left join (select task_id,count(1) num from t_meeting_sbw_task_org where type in (3,4,5) group by task_id) vn on t.task_id = vn.task_id " +
            "left join (select task_id,count(1) num from t_meeting_sbw_task_org where type in (0,3) and status = 4 group by task_id) un on t.task_id = un.task_id " +
            "where " +
            "t.region_id = #{regionId} and t.verify_org_id = #{orgId} and t.status >2 and is_del = 0 " +
            "<if test =\"title != null and title !='' \"> and t.title like concat('%',#{title},'%')</if> " +
            "<if test = \"beginTime != null and endTime == null\"> and date(t.begin_time) <![CDATA[>= ]]> #{beginTime}</if> " +
            "<if test = \"beginTime == null and endTime != null\"> and date(t.end_time) <![CDATA[<= ]]> #{endTime}</if> " +
            "<if test = \"beginTime != null and endTime != null\"> and date(t.begin_time) <![CDATA[>= ]]> #{beginTime} and date(t.end_time) <![CDATA[<= ]]> #{endTime}</if> " +
            "order by t.task_id desc " +
            "</script>")
    List<SbwTaskListForm> myVerifyTask(SbwTaskListForm form);

    /**
     * 任务下发列表
     * @param taskId
     * @return
     */
    @Select("select task_id taskId, org_id orgId, org_name orgName, type from t_meeting_sbw_task_org where task_id = #{taskId}")
    List<SbwTaskOrgListForm> orgList(@Param("taskId")Long taskId);

    /**
     * 任务发布改变状态
     * @param entity
     * @return
     */
    @Update("update t_meeting_sbw_task_org set status = #{status} where task_id = #{taskId} and region_id = #{regionId}")
    int updateTaskStatus(SbwTaskEntity entity);

    /**
     * 任务发布改变标题
     * @param title
     * @param beginTime
     * @param endTime
     * @param status
     * @return
     */
    @Update("update t_meeting_sbw_task_org set title = #{title},begin_time=#{beginTime},end_time=#{endTime},status=#{status},update_time = now() where task_id = #{taskId} and region_id = #{regionId}")
    Integer updateTaskTitle(@Param("taskId")Long taskId,
                            @Param("regionId")Long regionId,
                            @Param("title")String title,
                            @Param("beginTime")Date beginTime,
                            @Param("endTime")Date endTime,
                            @Param("status")Integer status);

    /**
     * 阅读状态
     * @param orgId
     * @param taskId
     * @return
     */
    @Update("update t_meeting_sbw_task_org set is_read = 1 where org_id = #{orgId} and task_id = #{taskId} and is_read = 0")
    Integer isRead(@Param("orgId")Long orgId,@Param("taskId")Long taskId);
}
