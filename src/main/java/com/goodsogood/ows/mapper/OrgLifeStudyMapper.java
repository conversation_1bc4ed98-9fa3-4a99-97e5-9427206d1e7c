package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.OrgLifeStudyEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface OrgLifeStudyMapper extends MyMapper<OrgLifeStudyEntity>{
    @Insert("<script>"+
            "insert ignore into t_meeting_org_life_study(life_study_id,life_id,study_id,step,create_user,create_time,has_direct_relate) values " +
            " <foreach collection=\"meetingIds\"  item=\"meetingId\" open=\"(\" separator=\"),(\" close=\")\">\n"
            + "NULL,#{lifeId},#{meetingId},#{step},#{userId},now(),1"
            + "</foreach>"
            + "</script>")
    void replaceOrInsert(@Param("lifeId") Long lifeId, List<Long> meetingIds, @Param("step") Integer step, @Param("userId") Long userId);

    @Delete("delete from t_meeting_org_life_study where life_id=#{lifeId}")
    void delByLifeId(Long lifeId);
}
