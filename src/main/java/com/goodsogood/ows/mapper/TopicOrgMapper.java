package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.TopicOrgEntity;
import com.goodsogood.ows.model.vo.MeetingTopicTaskListForm;
import com.goodsogood.ows.model.vo.TopicOrgForm;
import com.goodsogood.ows.model.vo.UndoneTopicCountForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-23 09:03
 **/
@Repository
@Mapper
public interface TopicOrgMapper extends tk.mybatis.mapper.common.Mapper<TopicOrgEntity>, TopicOrgInsertListMapper<TopicOrgEntity> {
    @Select("<script> " +
            "SELECT " +
            "      a.topic_org_id,a.topic_id ,b.name,b.description, " +
            "      CASE " +
            "         WHEN a.STATUS = 1  AND b.end_time <![CDATA[ >=  ]]> CURDATE() THEN 1 " +
            "         WHEN a.STATUS = 1 AND b.end_time <![CDATA[ <  ]]> CURDATE() THEN 3 " +
            "         ELSE a.STATUS " +
            "      END AS status, " +
            "      b.start_time , b.end_time, b.status as remark " +
            "from t_topic_org a,t_topic b  " +
            "WHERE a.topic_id = b.topic_id and b.is_del=0 " +
            // 2018年11月6日 10:46:26
            " <if test =\"tag == 1\"> and b.start_time <![CDATA[ <=  ]]>  CURDATE() and b.end_time <![CDATA[ >= ]]>  CURDATE() </if> " +//发起活动 查询有效期内的任务
            " <if test =\"tag == 2\"> and b.status = 1 </if> " +//任务完成情况中只查询派发的任务
            "<choose>" +
            "      <when test=\"isH5 != null and isH5 == 1\">" + // 移动端查询
            "           <if test =\"orgIds != null and orgIds.size() > 0 \">" +
            "             and a.org_id  in " +
            "               <foreach collection=\"orgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "                          #{item}\n" +
            "               </foreach>" +
            "           </if> " +
            "           <if test =\"status != null\"> and a.status = #{status} </if> " +
            "      </when>" +
            "      <otherwise>" + // PC端查询
            "          <if test =\"orgId != null\"> and a.org_id = #{orgId}</if> " +
            "          <choose>" +
            "                <when test=\"status != null and status == 1\">" + // 未完成
            "                    and a.status = 1 and b.end_time <![CDATA[ >= ]]>  CURDATE() " +
            "                </when>" +
            "                <when test=\"status != null and status == 2\">" + // 已完成
            "                    and a.status = 2 " +
            "                </when>" +
            "                <when test=\"status != null and status == 3\">" + // 已逾期
            "                    and a.status = 1 and b.end_time <![CDATA[ <  ]]>  CURDATE() " +
            "                </when>" +
            "          </choose>" +
            "      </otherwise>" +
            "</choose>" +
            " <if test =\"name != null and name != ''\"> and b.name like  \"%\"#{name}\"%\"</if> " +
            " <if test =\"sStartTime != null\"> and b.start_time <![CDATA[ >=  ]]> #{sStartTime}</if> " +
            " <if test =\"eStartTime != null\"> and b.start_time <![CDATA[ <=  ]]> #{eStartTime}</if> " +
            " <if test =\"sEndTime != null\"> and b.end_time <![CDATA[ >=  ]]> #{sEndTime}</if> " +
            " <if test =\"eEndTime != null\"> and b.end_time <![CDATA[ <=  ]]> #{eEndTime}</if> " +
            " ORDER BY status ASC,b.start_time DESC,a.topic_id  " +
            " </script>")
    @Results({
            @Result(property = "topicOrgId", column = "topic_org_id"),
            @Result(property = "topicId", column = "topic_id"),
            @Result(property = "name", column = "name"),
            @Result(property = "status", column = "status"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "description", column = "description"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            //处理one to many 任务
            @Result(property = "files", column = "topic_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.TopicFileMapper.selectByTopicId")),
            //处理one to many 任务
            @Result(property = "contents", column = "topic_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.TopicContentMapper.selectByTopicId")),
    })
    List<TopicOrgForm> list(MeetingTopicTaskListForm meetingTopicTaskListForm);


    // 2018年11月9日 16:32:36 chenanshun
    @Select("<script> " +
            "SELECT " +
            "     count(*) " +
            "from t_topic_org a,t_topic b  " +
            "WHERE a.topic_id = b.topic_id and b.is_del=0 " +
            "  and b.status = 1 " +//只查询派发的任务
            " <if test =\"orgIds != null and orgIds.size() > 0 \">" +
            "   and a.org_id  in " +
            "     <foreach collection=\"orgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "                #{item}\n" +
            "     </foreach>" +
            " </if> " +
            " <choose>" +
            "       <when test=\"status != null and status == 1\">" + // 未完成
            "           and a.status = 1 and b.end_time <![CDATA[ >= ]]>  CURDATE() " +
            "       </when>" +
            "       <when test=\"status != null and status == 2\">" + // 已完成
            "           and a.status = 2 " +
            "       </when>" +
            "       <when test=\"status != null and status == 3\">" + // 已逾期
            "           and a.status = 1 and b.end_time <![CDATA[ <  ]]>  CURDATE() " +
            "       </when>" +
            " </choose>" +
            " </script>")
    int taskCount(MeetingTopicTaskListForm meetingTopicTaskListForm);

    /**
     * 统计各组织未完成的任务数量 不包含逾期任务
     */
    @Select("<script> "
            + "  SELECT  a.org_id as orgId,count(*) num FROM  t_topic_org a,t_topic b "
            + "  WHERE b.status = 1 and a.topic_id = b.topic_id and b.is_del=0 "
            + "  and a.status = 1 and b.end_time <![CDATA[  >= ]]>  CURDATE() "
            + "  GROUP BY  a.org_id "
            + " </script>")
    List<UndoneTopicCountForm> undoneTopicCount();

    @Select("<script> " +
            "SELECT " +
            "      a.topic_org_id topicOrgId,a.topic_id topicId,a.org_id orgId,a.org_name orgName,a.status as originStatus, " +
            "      CASE " +
            "         WHEN a.STATUS = 1  AND b.end_time <![CDATA[ >=  ]]> CURDATE() THEN 1 " + // 未完成
            "         WHEN a.STATUS = 1 AND b.end_time <![CDATA[ <  ]]> CURDATE() THEN 3 " + // 逾期未完成
            "         ELSE a.STATUS " +
            "      END AS status " +
            "from t_topic_org a left join t_topic b on a.topic_id = b.topic_id " +
            "WHERE 1=1 " +
            " <if test =\"topicId != null\"> and a.topic_id = #{topicId}</if> " +
            " <if test =\"taskStatus != null\"> and a.status = #{taskStatus}</if> " +
            " <if test =\"orgName != null and orgName !='' \"> and a.org_name like CONCAT('%',#{orgName},'%')</if> " +
            "order by originStatus" +
            " </script>")
    @Results({
            @Result(property = "topicOrgId", column = "topicOrgId"),
            //处理one to many 答案
            @Result(property = "topicLogList", column = "topicOrgId",
                    many = @Many(select = "com.goodsogood.ows.mapper.TopicLogMapper.findByTopicOrgId")),
    })
    List<TopicOrgEntity> findByTopicId(@Param("topicId") Long topicId,@Param("taskStatus") Integer taskStatus,@Param("orgName") String orgName);


    @Select("SELECT\n" +
            "\tCOUNT( 1 ) AS total,\n" +
            "\tCOUNT( IF ( `status` = 1, TRUE, NULL ) ) AS notFinishTotal,\n" +
            "\tCOUNT( IF ( `status` = 2, TRUE, NULL ) ) AS finishTotal \n" +
            "FROM\n" +
            "\tt_topic_org \n" +
            "WHERE\n" +
            "\ttopic_id = #{topicId}")
    TopicOrgForm sasTotalByTopicId(@Param("topicId") Long topicId);

    /**
     * 查询任务信息
     *
     * @param topicOrgId  topic_org_id
     * @param topicStatus t_topic.status 1:派发 2：自有的
     * @return TopicOrgEntity
     */
    @Select("<script> "
            + "  SELECT a.topic_org_id,a.topic_id,a.org_id,a.org_name,a.status FROM t_topic_org a,t_topic b "
            + "  WHERE a.topic_id = b.topic_id and b.is_del=0 "
            + "  AND a.topic_org_id = #{topicOrgId} "
            + " <if test =\"topicStatus != null\"> and b.status = #{topicStatus}</if> "
            + " </script>")
    @Results({
            @Result(property = "topicOrgId", column = "topic_org_id"),
            @Result(property = "topicId", column = "topic_id"),
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "orgName", column = "org_name"),
            @Result(property = "status", column = "status"),
    })
    TopicOrgEntity findOne(@Param("topicOrgId") long topicOrgId, @Param("topicStatus") Integer topicStatus);
}
