package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.LifeEntity;
import com.goodsogood.ows.model.vo.LifeCheckForm;
import com.goodsogood.ows.model.vo.LifeFileVO;
import com.goodsogood.ows.model.vo.LifeFindForm;
import com.goodsogood.ows.model.vo.LifeForm;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
@Repository
public interface LifeMapper extends MyMapper<LifeEntity> {

    /**
     * 批量修改更新时间/人
     * @param ids
     * @param userId
     * @param time
     */
    @Update("<script> " +
            "update t_meeting_life set update_time = #{time}, last_change_user = #{userId} where life_id in " +
            "<foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            "   #{item} " +
            "</foreach> " +
            "</script>")
    void multiUpdate(@Param("ids")List<Long> ids, @Param("userId") Long userId, @Param("time")LocalDateTime time);

    /**
     * 查询民主生活会
     * @param form
     * @return
     */
    @Select("<script> " +
            "select " +
            "l.life_id lifeId, " +
            "l.title title, " +
            "l.years years, " +
            "l.org_name orgName, " +
            "l.status status, " +
            "ifnull(t.tag,'') tag " +
            "from t_meeting_life l " +
            "left join ( " +
            "   select life_id,group_concat(name separator '、') tag from t_meeting_life_tag lt " +
            "   inner join t_tag tag on lt.tag_id = tag.tag_id where tag.status != 2 group by lt.life_id" +
            ") t on l.life_id = t.life_id  " +
            "where l.is_del != 1 " +
            "<if test =\"form.title != null and form.title != ''\"> and l.title like concat('%',#{form.title},'%') </if> " +
            "<if test =\"form.orgIds == null or form.orgIds.size() == 0\"> and l.org_id = #{form.orgId} </if> " +
            "<if test =\"form.orgIds != null and form.orgIds.size() > 0\"> " +
            " and l.org_id in " +
            "   <foreach collection=\"form.orgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            "       #{item}" +
            "   </foreach> " +
            "</if> " +
            "<if test =\"form.tag != null and form.tag.size() > 0\"> " +
            "   and l.life_id in (select life_id from t_meeting_life_tag where tag_id in " +
            "   <foreach collection=\"form.tag\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            "       #{item}" +
            "   </foreach> " +
            "   )" +
            "</if> " +
            "<if test =\"form.years != null\"> and l.years = #{form.years} </if> " +
            "<if test =\"form.status != null and form.status.size() > 0\"> " +
            "   and l.status in " +
            "   <foreach collection=\"form.status\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            "       #{item} " +
            "   </foreach>" +
            "</if> " +
            "order by l.create_time desc " +
            "</script>")
    List<LifeForm> list(@Param("form") LifeFindForm form);


    /**
     * 同步组织信息
     * @param organizationBase
     * @return
     */
    @Update("update t_meeting_life " +
            "set org_name = #{base.name},org_level = #{base.orgLevel}" +
            "update_time = now(), last_change_user = -111 " +
            "where org_id = #{base.organizationId}")
    Integer updateOrgInfo(@Param("base")OrganizationBase organizationBase);

}
