package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.OrgLifeCheckEntity;
import com.goodsogood.ows.model.vo.OrgLifeCheckForm;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface OrgLifeCheckMapper extends MyMapper<OrgLifeCheckEntity>{

    @Select("select " +
            " ( @i := 27 ) modelType,  " +
            "check_id, life_id, username ,org_name, step " +
            "from t_meeting_org_check " +
            "where life_id = #{lifeId} and step = #{step} and is_del != 1 " +
            "order by create_time,check_id ")
    @Results({
            @Result(property = "checkId",column = "check_id"),
            @Result(property = "lifeId",column = "life_id"),
            @Result(property = "username",column = "username"),
            @Result(property = "orgName",column = "org_name"),
            @Result(property = "files",column = "{dataId = check_id,step = step,modelType = modelType}",
                    many = @Many(select = "com.goodsogood.ows.mapper.OrgLifeFileMapper.dataFile")),
            @Result(property = "uploader",column = "{dataId = check_id, modelType = modelType}",
                    one = @One(select = "com.goodsogood.ows.mapper.OrgLifeUploaderMapper.dataUploader"))

    })
    List<OrgLifeCheckForm> check(@Param("lifeId") Long lifeId, @Param("step") Integer step);


    /**
     * 批量逻辑删除
     * @param checkIds
     * @param uid
     * @return
     */
    @Update("<script>" +
            "update t_meeting_org_check set is_del = 1,update_time = now(),last_change_user = #{uid} where check_id in " +
            "<foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            "#{item}" +
            "</foreach>" +
            "</script>")
    Integer multiDel(@Param("ids") List<Long> checkIds,@Param("uid")Long uid);

    /**
     * 同步组织信息
     * @param organizationBase
     * @return
     */
    @Update("update t_meeting_org_check " +
            "set org_name = #{base.name}" +
            "update_time = now(), last_change_user = -111 " +
            "where org_id = #{base.organizationId}")
    Integer updateOrgInfo(@Param("base") OrganizationBase organizationBase);

    @Delete("delete from t_meeting_org_check where life_id=#{lifeId}")
    void delByLifeId(Long lifeId);
}
