package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingPlanLimitEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @program: ows-meeting
 * @description: ${description}
 * @author: taiqian.Luo
 * @create: 2019-04-18 15:07
 **/
@Repository
@Mapper
public interface MeetingPlanLimitMapper extends MyMapper<MeetingPlanLimitEntity>{


    @Select("SELECT meeting_plan_limit_id,meeting_plan_id,is_retire,party_group,period,create_time," +
            " create_user,update_time,last_change_user " +
            " FROM t_meeting_plan_limit WHERE meeting_plan_id = #{id} ")
    @Results({
            @Result(property = "meetingPlanLimitId", column = "meeting_plan_limit_id"),
            @Result(property = "meetingPlanId", column = "meeting_plan_id"),
            @Result(property = "isRetire", column = "is_retire"),
            @Result(property = "partyGroup", column = "party_group"),
            @Result(property = "period", column = "period"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "createUser", column = "create_user"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "lastChangeUser", column = "last_change_user"),
            @Result(property = "limitTypeEntityList", column = "meeting_plan_limit_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingPlanLimitTypeMapper.findByMeetingPlanLimitId"))
    })
    List<MeetingPlanLimitEntity> findByMeetingPlanId(@Param("id") Long id);



    @Select("SELECT meeting_plan_limit_id  meetingPlanLimitId,is_retire isRetire," +
            "party_group partyGroup,period FROM t_meeting_plan_limit WHERE meeting_plan_id = #{id}")
    MeetingPlanLimitEntity findByMeetingPlanLimit(@Param("id") long meetingPlanId);



}
