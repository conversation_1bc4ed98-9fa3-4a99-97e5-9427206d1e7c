package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.GroupEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:25
 **/
@Repository
@Mapper
public interface GroupMapper extends MyMapper<GroupEntity> {
    @Select("SELECT group_id,org_id,org_name,create_time,create_user,update_time,last_change_user FROM t_group where region_id=#{regionId} ")
    @Results({
            @Result(property = "groupId", column = "group_id"),
            //处理one to many
            @Result(property = "types", column = "group_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.TypeMapper.findByGroupId"))
    })
    List<GroupEntity> findAll(@Param("regionId") long regionId);
}