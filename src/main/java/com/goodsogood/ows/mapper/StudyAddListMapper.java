package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingWaitSignEntity;
import com.goodsogood.ows.model.vo.StudyAddListForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
@Mapper
public interface StudyAddListMapper extends MyMapper<MeetingWaitSignEntity>{
    @Select("<script>"+
            "select \n" +
            "t1.wait_sign_id as signId,"+
            "t1.meeting_id as meetingId,"+
            "t1.meeting_user_id as meetingUserId,"+
            "t1.user_id as userId,"+
            "t2.name as name,\n" +
            "group_concat(distinct  t4.type) as types,\n" +
            "group_concat(distinct t3.tag_name) as tagName,\n" +
            "t2.start_time as startTime,\n" +
            "case t1.type when 1 then '待补学' when 2 then '补学完成' when 3 then '超期补学' when 4 then '活动变更' else '待补学' end as statusName,\n" +
            "t1.sign_time as signTime\n" +
            "from t_meeting_wait_sign t1\n" +
            "inner join t_meeting t2 on t1.meeting_id=t2.meeting_id\n" +
            "left join t_meeting_tag t3 on t1.meeting_id = t3.meeting_id\n" +
            "left join t_meeting_type t4 on t1.meeting_id = t4.meeting_id\n"+
            "where t1.is_del=1 and t2.is_del=0 \n" +
            "<if test=\"userId!=null and userId!=''\"> and t1.user_id=#{userId} </if>" +
            "<if test=\"name!=null and name!=''\"> and t2.name like concat('%',#{name},'%') </if>" +
            "<if test=\"typesId!=null and typesId!=''\"> and  FIND_IN_SET(#{typesId},t4.type_id)>0 </if>\n" +
            "<if test=\"tagId!=null and tagId!=''\"> and t3.tag_id=#{tagId} </if>\n" +
            "<if test=\"status!=null and status!='' and status==1\"> and t1.type in(1,5) </if>\n" +
            "<if test=\"status!=null and status!='' and status==2\"> and t1.type in(2) </if>\n" +
            "<if test=\"status!=null and status!='' and status==3\"> and t1.type in(3) </if>\n" +
            "<if test=\"status!=null and status!='' and status==4\"> and t1.type in(4) </if>\n" +
            "<if test=\"startTimeStr!=null and startTimeStr!=''\"> and date_format(t2.start_time,'%Y-%m-%d')=#{startTimeStr} </if>\n" +
            " group by t1.wait_sign_id "+
            "order by ifnull(t1.update_time,t1.create_time) desc,t2.start_time desc "+
            "</script>")
    List<StudyAddListForm> selList(StudyAddListForm studyAddListForm);



}
