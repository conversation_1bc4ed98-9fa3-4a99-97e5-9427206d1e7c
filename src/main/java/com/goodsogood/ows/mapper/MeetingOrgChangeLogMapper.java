package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingOrgChangeLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-04-18 14:29
 **/
@Repository
@Mapper
public interface MeetingOrgChangeLogMapper extends MyMapper<MeetingOrgChangeLogEntity> {

    /**
     * 查询更新过组织
     */
    @Select("select org_id orgId,org_level orgLevel  from t_meeting_org_change_log where create_time > #{date} and type=2 GROUP BY org_id ORDER BY create_time desc")
    List<MeetingOrgChangeLogEntity> listUpdateOrg(@Param("date") String date);
}