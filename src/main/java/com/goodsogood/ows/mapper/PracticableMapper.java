package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.PracticableEntity;
import com.goodsogood.ows.model.vo.PracticableForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface PracticableMapper extends MyMapper<PracticableEntity>{
    @Select("""
<script>
SELECT practicable_id as practicableId,
leader_id as leaderId,unit_id as unitId,org_id as orgId,
leader_name as leaderName,unit_name as unitName,org_name as orgName,
substrate_name as substrateName,interview_time as interviewTime,attachment as attachment
FROM
t_meeting_practicable 
<where>
<if test="practicableForm.leaderName != null">
AND leader_name LIKE CONCAT('%', #{practicableForm.leaderName}, '%')
</if>
 <if test="practicableForm.orgName != null">
AND (org_name =#{practicableForm.orgName})
</if>
 <if test="practicableForm.interviewTime != null">
AND (interview_time =#{practicableForm.interviewTime})
</if>
 <if test="practicableForm.substrateName != null">
AND substrate_name LIKE CONCAT('%', #{practicableForm.substrateName}, '%')
</if>
</where>
ORDER BY create_time DESC
</script>
""")
    List<PracticableEntity> listPracticable(@Param("practicableForm")PracticableForm practicableForm);
    @Select("""
<script>
SELECT practicable_id as practicableId,
leader_id as leaderId,unit_id as unitId,org_id as orgId,
leader_name as leaderName,unit_name as unitName,org_name as orgName,
substrate_name as substrateName,interview_time as interviewTime,attachment as attachment
FROM
t_meeting_practicable 
<where>
<if test="practicableForm.unitId != null">
AND (unit_id =#{practicableForm.unitId})
</if>
<if test="practicableForm.leaderName != null">
AND leader_name LIKE CONCAT('%', #{practicableForm.leaderName}, '%')
</if>
 <if test="practicableForm.orgName != null">
AND (org_name =#{practicableForm.orgName})
</if>
 <if test="practicableForm.interviewTime != null">
AND (interview_time =#{practicableForm.interviewTime})
</if>
 <if test="practicableForm.substrateName != null">
AND substrate_name LIKE CONCAT('%', #{practicableForm.substrateName}, '%')
</if>
</where>
ORDER BY create_time DESC
</script>
""")
    List<PracticableEntity> listPracticableUnitId(@Param("practicableForm")PracticableForm practicableForm);
}
