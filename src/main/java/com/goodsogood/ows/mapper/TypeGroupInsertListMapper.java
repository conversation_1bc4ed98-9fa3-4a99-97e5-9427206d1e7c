package com.goodsogood.ows.mapper;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.provider.SpecialProvider;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:25
 **/
public interface TypeGroupInsertListMapper<T>{
    @Options(useGeneratedKeys = true, keyProperty = "typeGroupId")
    @InsertProvider(type = SpecialProvider.class, method = "dynamicSQL")
    int insertList(List<T> recordList);

}