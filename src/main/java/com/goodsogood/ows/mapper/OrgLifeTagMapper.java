package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.OrgLifeTagEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface OrgLifeTagMapper extends MyMapper<OrgLifeTagEntity>{

    @Insert("<script> " +
            "insert ignore into t_meeting_org_life_tag (`life_id`,`tag_id`,`create_user`,`create_time`)   values" +
            "<foreach collection=\"list\" index=\"index\" item=\"item\" separator=\",\">" +
            "(#{item.lifeId},#{item.tagId},#{item.createUser},#{item.createTime}) " +
            "</foreach>" +
            "</script>")
    Integer addTag(List<OrgLifeTagEntity> list);

    /**
     * 删除标签
     * @return
     */
    @Delete("<script>" +
            " <if test =\"life != null and tag != null\"> " +
            "  delete from t_meeting_org_life_tag where life_id in " +
            "   <foreach collection=\"life\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            "    #{item}" +
            "   </foreach>" +
            "  and tag_id in " +
            "   <foreach collection=\"tag\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{item}" +
            "   </foreach>" +
            "</if> " +
            "</script>")
    Integer delTag(@Param("life") List<Long> life, @Param("tag") List<Long>tag);

    @Delete("delete from t_meeting_org_life_tag where life_id=#{lifeId}")
    void delByLifeId(Long lifeId);
}
