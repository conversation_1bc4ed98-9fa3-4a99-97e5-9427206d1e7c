package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.OrgLifeCommentEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface OrgLifeCommentMapper extends MyMapper<OrgLifeCommentEntity>{

    /**
     * 取消关联民主评议
     * @param lifeId
     * @param commentId
     * @return
     */
    @Delete("delete from t_meeting_org_life_comment where life_id = #{lifeId} and comment_id = #{commentId} ")
    int deleteCommentLink(@Param("lifeId")Long lifeId, @Param("commentId")Long commentId);

    @Delete("delete from t_meeting_org_life_comment where life_id=#{lifeId}")
    void delByLifeId(Long lifeId);
}
