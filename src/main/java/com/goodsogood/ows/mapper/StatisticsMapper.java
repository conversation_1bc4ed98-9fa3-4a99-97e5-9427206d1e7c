package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.TopicContentEntity;
import com.goodsogood.ows.model.vo.SasUserOrgLifeForm;
import com.goodsogood.ows.model.vo.StatisticsMeetingForm;
import com.goodsogood.ows.model.vo.StatisticsUserOrgLifeForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @program: ows-meeting
 * @description: ${description}
 * @author: Mr.<PERSON>
 * @create: 2019-04-22 11:49
 */
@Repository
@Mapper
public interface StatisticsMapper extends MyMapper<TopicContentEntity> {

    @Select("<script>SELECT count(1) as countNum, DATE_FORMAT(start_time,'%Y-%m') as staTime , typeId, typeName \n" +
            " FROM (\n" +
            " SELECT  DISTINCT a.org_name , b.type_id as typeId,b.type as typeName ,a.start_time,a.meeting_id \n" +
            " FROM t_meeting as a\n" +
            " LEFT JOIN t_meeting_type as b\n" +
            " ON a.meeting_id=b.meeting_id\n" +
            " WHERE a.org_id=#{orgId} AND a.`status` in (${queryStatus}) \n" +
            " AND DATE_SUB(CURDATE(), ${queryCode}) <![CDATA[ <= ]]> date(a.start_time)\n" +
            " )as L\n" +
            " GROUP BY typeId,staTime</script>")
    List<StatisticsMeetingForm> statisticsOrganizeInfo(@Param("orgId")Long orgId,@Param("queryCode") String queryCode,@Param("queryStatus") String queryStatus);


    @Select("<script>" +
            "select \n" +
            " c_table.date as staTime,\n" +
            " c_table.activity_id as typeId,\n" +
            " c_table.type_name as typeName,\n" +
            " IFNULL(c.total,0) as countNum\n" +
            "FROM\n" +
            "(SELECT\n" +
            " temp.date,\n" +
            " ta.activity_id,\n" +
            " ta.type_name\n" +
            "FROM\n" +
            "(\n" +
            " SELECT left(adddate( DATE_SUB( CURDATE( ), ${queryCode} ), interval numlist.id month),7) AS 'date' FROM \n" +
            " (\n" +
            " SELECT * from \n" +
            " (SELECT n1.i + n10.i * 10 AS id FROM t_num n1 CROSS JOIN t_num AS n10) a \n" +
            " where a.id <![CDATA[ <= ]]> 11\n" +
            "  ) AS numlist\n" +
            "  WHERE adddate( DATE_SUB( CURDATE( ), ${queryCode} ), interval numlist.id month) <![CDATA[ <= ]]> CURDATE( )) temp INNER JOIN\n" +
            "  t_statistical_temp_activity ta) c_table LEFT JOIN \n" +
            "  (\n" +
            " SELECT DISTINCT\n" +
            "  c.type_id,\n" +
            "  DATE_FORMAT( a.start_time, '%Y-%m' ) as start_time,\n" +
            "  count(DISTINCT a.meeting_id) as total\n" +
            " FROM\n" +
            "  t_meeting AS a\n" +
            "  INNER JOIN t_meeting_user AS b ON a.meeting_id = b.meeting_id\n" +
            "  INNER JOIN t_meeting_type AS c ON a.meeting_id = c.meeting_id \n" +
            " WHERE\n" +
            "  a.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) \n" +
            "  AND b.user_id = #{userId} \n" +
            "<if test=\"orgId !=null\"> and a.org_id=#{orgId} </if>" +
            "<if test=\"signStatus !=null and signStatus != ''\"> and b.sign_status in (#{signStatus}) </if>\n" +
            " GROUP BY\n" +
            "  DATE_FORMAT( a.start_time, '%Y-%m' ), c.type_id\n" +
            " ) c on c_table.date = c.start_time AND c_table.activity_id = c.type_id" +
            "</script>")
    List<StatisticsMeetingForm> statisticsOrganizeUserInfo(@Param("orgId") Long orgId,@Param("userId") Long userId,@Param("queryCode") String queryCode,@Param("signStatus") String signStatus);

    /**
     * 统计用户每月参加各类型的组织生活次数
     *
     * @param sasUserOrgLifeForm 统计条件
     * @return List<StatisticsMeetingForm>
     */
    @Select(
            "<script>"
                    + " SELECT "
                    + "     YEAR(t2.start_time) AS statisticalYear,"
                    + "     MONTH(t2.start_time) AS statisticalMonth,"
                    + "     t1.user_id as userId,"
                    + "     t3.type_id as typeId,"
                    + "     t3.type as typeName,"
                    + "     count(1) as participateNum  "
                    + " FROM (\n"
                    + "      SELECT user_id,meeting_id FROM t_meeting_user\n"
                    + "      WHERE 1=1 \n"
                    + "         <if test =\"uids != null and uids.size() > 0 \">"
                    + "                  and user_id in "
                    + "            <foreach collection=\"uids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "                #{item}"
                    + "            </foreach>"
                    + "         </if> "
                    + "         <if test =\"signStatus != null and signStatus.size() > 0 \">"
                    + "                  and sign_status in "
                    + "            <foreach collection=\"signStatus\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "                #{item}"
                    + "            </foreach>"
                    + "         </if> "
                    + "         <if test =\"tags != null and tags.size() > 0 \">"
                    + "                  and tag in "
                    + "            <foreach collection=\"tags\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "                #{item}"
                    + "            </foreach>"
                    + "         </if> "
                    + "      GROUP BY user_id,meeting_id\n"
                    + "      ) t1,"
                    + "         t_meeting t2,"
                    + "         (SELECT meeting_id,type_id,type from t_meeting_type GROUP BY meeting_id,type_id ) t3 "
                    + " WHERE "
                    + "   t1.meeting_id = t2.meeting_id "
                    + "   AND t2.is_del=0"
                    + "   AND t1.meeting_id = t3.meeting_id\n"
                    + "   AND t2.`status` IN ( 7, 8, 10, 11, 12, 13, 14 ) \n"
                    + "   <if test=\"orgId !=null\"> AND t2.org_id=#{orgId} </if>"
                    + " GROUP BY "
                    + "    t1.user_id,t3.type_id,YEAR(t2.start_time),MONTH(t2.start_time)"
                    + "</script>")
    List<StatisticsUserOrgLifeForm> sasUserOrgLife(SasUserOrgLifeForm sasUserOrgLifeForm);
}
