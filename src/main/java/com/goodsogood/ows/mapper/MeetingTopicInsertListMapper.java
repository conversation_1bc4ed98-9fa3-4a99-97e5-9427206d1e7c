package com.goodsogood.ows.mapper;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Options;
import tk.mybatis.mapper.provider.SpecialProvider;

import java.util.List;

/**
 * 批量插入活动与任务（任务）关联表
 * <AUTHOR>
 * @create 2018-10-23 9:04
 **/
public interface MeetingTopicInsertListMapper<T> {

     @Options(useGeneratedKeys = true, keyProperty = "meetingTopicId")
     @InsertProvider(type = SpecialProvider.class, method = "dynamicSQL")
     int insertList(List<T> recordList);

}
