package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.CategoryEntity;
import com.goodsogood.ows.model.vo.CategoryListForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:25
 **/
@Repository
@Mapper
public interface CategoryMapper extends MyMapper<CategoryEntity> {
    @Select(" <script> " +
            "SELECT category_id,category FROM t_category " +
            "WHERE 1=1 " +
            " <if test =\"categoryId != null\"> and category_id = #{categoryId}</if> " +
            " <if test =\"regionId != null\"> and region_id = #{regionId}</if> " +
            " <if test =\"category != null and category !='' \"> and category like  \"%\"#{category}\"%\"</if> " +
            " </script>")
    @Results({
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "category", column = "category")
    })
    List<CategoryEntity> listAllCategory(CategoryListForm categoryListFrom);

    @Select(" SELECT category_id,category FROM t_category WHERE category = #{category} and region_id = #{regionId} ")
    @Results({
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "category", column = "category")
    })
    CategoryEntity findByName(@Param("category") String category, @Param("regionId") Long regionId);
}