package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.TopicEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:39
 **/
@Repository
@Mapper
public interface TopicMapper extends MyMapper<TopicEntity> {


    @Select("<script>" +
            "SELECT " +
            " t1.topic_id topicId, " +
            " t1.`name`, " +
            " t2.`status`, " +
            " t1.start_time startTime, " +
            " t1.end_time endTime " +
            "FROM " +
            " t_topic t1, " +
            " t_topic_org t2 " +
            "WHERE " +
            " t1.topic_id = t2.topic_id " +
            " AND t1.is_del = 0 " +
            "<if test=\" names != null \"><if test=\" names != '' \"> AND t1.name like CONCAT('%',#{names},'%') </if></if>" +
            "AND t2.org_id = #{oid} " +
            "AND t2.topic_id not in (select topic_id from t_topic where org_id = #{oid}) " +
            "</script>")
    List<TopicEntity> getTopicListFromOrg(@Param(value = "names") String names,
                                          @Param(value = "oid") Long oid);


    @Select("<script>" +
            " SELECT a.topic_id,a.name FROM t_topic a,t_topic_org b " +
            " WHERE " +
            "  a.topic_id = b.topic_id and a.start_time <![CDATA[ <= ]]>  now() and a.end_time   <![CDATA[ >=  ]]> now() and a.is_del = 0 " +
            " <if test=\" orgId != null \"> and b.org_id = #{orgId} </if>" +
            " <if test =\"ids != null and ids.size() > 0 \">" +
            "  and a.topic_id in " +
            "     <foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "                #{item}\n" +
            "     </foreach>" +
            " </if> " +
            " </script>")
    @Results({
            @Result(property = "topicId", column = "topic_id"),
            @Result(property = "name", column = "name")
    })
    List<TopicEntity> findByOrgIdAndIds(@Param(value = "orgId") Long orgId,
                                        @Param(value = "ids") List<Long> ids);

    @Select(
            "<script>"
                    + "SELECT "
                    + " t1.topic_id topicId, "
                    + " t1.`name`, "
                    + " t1.`status`, "
                    + " t1.start_time startTime, "
                    + " t1.end_time endTime, "
                    + " t2.orgNum, "
                    + " t2.finishOrgNum, "
                    + " if(t1.end_time &lt;= now(),2,1) endStatus "
                    + " FROM "
                    + " t_topic t1,"
                    + " ( SELECT topic_id,COUNT(org_id) as orgNum,SUM(IF(`status`=2,1,0)) as finishOrgNum FROM t_topic_org GROUP BY topic_id ) t2 "
                    + " WHERE t1.topic_id=t2.topic_id " +
                    "   and t1.is_del = 0 " +
                    "   and status = 1 "
                    + "<if test=\" name != null and  name != '' \"> AND t1.name like CONCAT('%',#{name},'%') </if>"
                    + "<if test=\" oid != null  \"> AND t1.org_id = #{oid} </if>"
                    + "<if test=\" sStartTime != null  \"> AND t1.start_time &gt;= #{sStartTime} </if>"
                    + "<if test=\" eStartTime != null  \"> AND t1.start_time &lt;= #{eStartTime} </if>"
                    + " order by t1.topic_id desc "
                    + "</script>")
    List<TopicEntity> find(
            @Param(value = "name") String name,
            @Param(value = "oid") Long oid,
            @Param(value = "sStartTime") Date sStartTime,
            @Param(value = "eStartTime") Date eStartTime);
}