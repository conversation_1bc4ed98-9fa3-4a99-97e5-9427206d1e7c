package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.SbwTaskEntity;
import com.goodsogood.ows.model.db.SbwTaskFlowEntity;
import com.goodsogood.ows.model.db.TopicContentEntity;
import com.goodsogood.ows.model.vo.SbwHandleForm;
import com.goodsogood.ows.model.vo.SbwTaskForm;
import com.goodsogood.ows.model.vo.SbwTaskListForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.code.ORDER;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-07-29
 **/
@Repository
@Mapper
public interface SbwTaskMapper extends MyMapper<SbwTaskEntity> {

//    SELECT
//    	( @i := @i + 1 ) AS num,
//    	`task_id` taskId,
//    	`title` title,
//    	`region_id` regionId,
//    	`org_id` orgId,
//    	`begin_time` beginTime,
//    	`end_time` endTime,
//    	`status` status
//    	FROM t_meeting_sbw_task,
//    	( SELECT @i := 0 ) AS t
//    	WHERE create_user = 53624
//    	AND title like CONCAT('%','打','%')
//    	AND begin_time >= '2021/7/2'
//    	AND end_time <= '2021/8/30'
//    	ORDER BY task_id ASC
    @Select("<script>"
            + "SELECT"
            + "`task_id` taskId,"
            + "`title` title,"
            + "`region_id` regionId,"
            + "`org_id` orgId,"
            + "`begin_time` beginTime,"
            + "`end_time` endTime,"
            + "`status` status "
            + "FROM t_meeting_sbw_task\n"
            + "WHERE org_id = #{orgId} and status not in(1,2) "
            + "<if test=\" title != null and title != '' \"> AND title like CONCAT('%',#{title},'%') </if>"
            + "<if test=\" beginTime != null \"> AND begin_time <![CDATA[>=]]> #{beginTime} </if>"
            + "<if test=\" endTime != null\"> AND end_time <![CDATA[<=]]> #{endTime} </if>"
            + "ORDER BY task_id ASC"
            + "</script>")
    List<SbwTaskListForm> find(
            @Param("orgId") Long orgId,
            @Param("title") String title,
            @Param("beginTime") Date beginTime,
            @Param("endTime") Date endTime);


    /**
     * 查询执行记录
     *
     * @param taskId
     * @param regionId
     * @return
     */
    @Select("SELECT task_id taskId,type," +
            "content," +
            "operate_org_name operateOrgName," +
            "create_time createTime " +
            "FROM t_meeting_sbw_task_flow " +
            "WHERE task_id = #{taskId} " +
            "AND region_id = #{regionId} "+
            "AND org_id = #{orgId} " +
            "AND type > 0 ")
    List<SbwTaskFlowEntity> findRecord(@Param("taskId") Long taskId,
                                       @Param("regionId") Long regionId,
                                       @Param("orgId")Long orgId);

    /**
     * 查询提交转办单的sql
     * @param taskId 任务id
     * @param regionId 区县id
     * @param flag 任务类型 1:我的任务-提交转办单 2:任务审核-审核转办单
     * @param status 状态 1:草稿 2:提交
     * @param orgId 支部id
     * @return
     */
    @Select("<script>"
            +"SELECT"
            + " handle_id handleId,"
            + " flag,"
            + " open_status openStatus,"
            + " private_reason privateReason,"
            + " handle_content handleContent,"
            + " file_id fileId,"
            + " filename,"
            + " status,"
            + " handle_status handleStatus,"
            + " handle_comment handleComment,"
            + " accept_user acceptUser,"
            + " accept_org acceptOrg,"
            + " phone phone,"
            + " accept_time acceptTime"
            + " FROM"
            + " `t_meeting_sbw_handle` "
            + " WHERE"
            + " \ttask_id = #{taskId}"
            + " AND region_id = #{regionId}"
            + " AND flag = #{flag}"
            + " <if test=\" orgId != null\"> AND org_id = #{orgId} </if>"
            + " <if test=\" status != null\"> AND status = #{status} </if>"
            + " </script>")
    SbwHandleForm findHandle(@Param("taskId") Long taskId,
                             @Param("regionId") Long regionId,
                             @Param("flag") Integer flag,
                             @Param("status")Integer status,
                             @Param("orgId")Long orgId);

    /**
     * 查询该任务下的接收组织
     * @param taskId
     * @param regionId
     * @return
     */
    @Select("select org_id from t_meeting_sbw_task_org where task_id = #{taskId} and region_id = #{regionId}")
    List<Long> findOrgIds(@Param("taskId") Long taskId,
                          @Param("regionId")Long regionId);

    @Delete("delete from t_meeting_sbw_task_org where org_id = #{orgId} and task_id = #{taskId}")
    void deleteOrg(@Param("orgId")Long orgId,
                   @Param("taskId")Long taskId);

    /**
     * 更新时如果删除文件同步更新文件信息 转办单
     * @param taskId
     */
    @Update("update t_meeting_sbw_task set file_id = null, file_name = null where task_id = #{taskId}")
    void updateFile(@Param("taskId") Long taskId);

    /**
     * 更新时如果删除文件同步更新文件信息 待办单
     * @param shiftTaskId
     */
    @Update("update t_meeting_sbw_shift_task set file_json = null where shift_task_id = #{shiftTaskId}")
    void updateShiftFile(@Param("shiftTaskId") Long shiftTaskId);

    @Update("update t_meeting_sbw_handle set file_id = null, filename = null where handle_id = #{handleId}")
    void updateHandleFile(@Param("handleId") Long handleId);
}
