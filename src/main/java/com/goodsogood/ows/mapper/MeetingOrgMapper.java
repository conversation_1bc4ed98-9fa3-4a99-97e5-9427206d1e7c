package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingOrgEntity;
import com.goodsogood.ows.model.vo.OrgForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-23 16:35
 **/
@Repository
@Mapper
public interface MeetingOrgMapper extends tk.mybatis.mapper.common.Mapper<MeetingOrgEntity>, MeetingOrgInsertListMapper<MeetingOrgEntity> {
    @Select("SELECT meeting_org_id as meetingOrgId ,meeting_plan_id as meetingPlanId,org_id as orgId,org_name as orgName FROM t_meeting_org WHERE meeting_plan_id = #{id} ")
    List<OrgForm> findByMeetingPlanId(@Param("id") Long id);

    @Select(" <script> " +
            "select org_id orgId,org_name orgName from t_meeting_org where meeting_plan_id=${meetingId}"+
            " <if test =\"orgName != null\"> and org_name like  concat('%',#{orgName},'%')</if> " +
            " </script>")
    List<MeetingOrgEntity> findExecuteOrg(@Param("meetingId") Long meetingId,@Param("orgName") String orgName);

    /*@Select("<script>SELECT org_id orgId,org_name orgName FROM t_meeting_org where meeting_plan_id = #{id} " +
            "<if test=\"orgName!=null\"> and  org_name like concat('%',#{orgName},'%') </if> </script>")
    List<OrgForm> executeOrgPage(@Param("id") long meetingPlanId, @Param("orgName") String orgName);*/
}