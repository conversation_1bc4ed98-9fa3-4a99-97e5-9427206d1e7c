package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingTypeEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-26 11:40
 **/
@Repository
@Mapper
public interface MeetingTypeMapper extends tk.mybatis.mapper.common.Mapper<MeetingTypeEntity>, MeetingTypeInsertListMapper<MeetingTypeEntity>{
    @Select(" SELECT a.meeting_type_id,a.meeting_task_id,a.meeting_id,a.type_id,a.category_id,a.type,a.category,d.code,d.has_lecturer,d.has_lecture_title, " +
            " b.meeting_plan_id,c.is_sign_in,t1.type_sys typeSys FROM " +
            " t_meeting_type a " +
            " left join t_type t1 on a.type_id=t1.type_id" +
            " left join t_meeting_task b on a.meeting_task_id = b.meeting_task_id " +
            " left join t_meeting_require c on b.meeting_require_id = c.meeting_require_id ,t_type d " +
            " where a.type_id=d.type_id and a.meeting_id = #{meetingId} ")
    @Results({
            @Result(property = "meetingTypeId", column = "meeting_type_id"),
            @Result(property = "isSignIn", column = "is_sign_in"),
            @Result(property = "meetingTaskId", column = "meeting_task_id"),
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "typeId", column = "type_id"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "type", column = "type"),
            @Result(property = "meetingPlanId", column = "meeting_plan_id"),
            @Result(property = "code", column = "code"),
            @Result(property = "hasLecturer", column = "has_lecturer"),
            @Result(property = "hasLectureTitle", column = "has_lecture_title"),
            @Result(property = "category", column = "category")
    })
    List<MeetingTypeEntity> findByMeetingId(@Param("meetingId") long meetingId);
}