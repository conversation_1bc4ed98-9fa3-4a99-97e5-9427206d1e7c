package com.goodsogood.ows.mapper;

import com.github.pagehelper.Page;
import com.goodsogood.ows.model.db.ReportEntity;
import com.goodsogood.ows.model.vo.ReportForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface ReportMapper extends MyMapper<ReportEntity> {

    @Insert("insert into t_meeting_work_report(report_type, leader, leader_names, leader_ids, department, reporter, report_time, year,half_year_interval," +
            "report_record, create_user, create_time, update_user, update_time,attachment)values (#{reportType}," +
            "#{leader},#{leaderNames},#{leaderIds},#{department},#{reporter},#{reportTime},#{year},#{halfYearInterval},#{reportRecord},#{createUser},#{createTime},#{updateUser},#{updateTime},#{attachment})")
    void addReport(ReportEntity reportEntity);

    @Update("update t_meeting_work_report set leader=#{leader}, leader_names =#{leaderNames}, leader_ids = #{leaderIds}, department = #{department}, reporter = #{reporter}, report_time = #{reportTime}, year = #{year},half_year_interval = #{halfYearInterval},report_record = #{reportRecord}, update_user = #{updateUser}, update_time = #{updateTime},attachment = #{attachment} " +
            "WHERE work_report_id = #{workReportId}")
    void updateReport(ReportEntity reportEntity);

    @Select("<script>" +
            "SELECT work_report_id as workReportId, leader,report_type as reportType, leader_names as leaderNames, leader_ids as leaderIds,unit_name as unitName,unit_id as unitId, department, reporter, report_time as reportTime, year,half_year_interval as halfYearInterval,report_record as reportRecord, create_user as createUser, create_time as createTime, update_user as updateUser, update_time as updateTime, attachment FROM t_meeting_work_report " +
            "<where>" +
            "<if test='reportForm.reportType != null and reportForm.reportType != \"\"'>AND report_type = #{reportForm.reportType} </if>" +
            "<if test='reportForm.unitName != null and reportForm.unitName != \"\"'>AND unit_name LIKE CONCAT('%', #{reportForm.unitName}, '%')</if>" +
            "<if test='reportForm.leaderName != null and reportForm.leaderName != \"\"'>AND leader_names LIKE CONCAT('%', #{reportForm.leaderName}, '%')</if>" +
            "<if test='reportForm.department != null and reportForm.department != \"\"'>AND department LIKE CONCAT('%', #{reportForm.department}, '%')</if>" +
            "<if test='reportForm.reporter != null and reportForm.reporter != \"\"'>AND reporter LIKE CONCAT('%', #{reportForm.reporter}, '%')</if>" +
            "<if test='reportForm.startTime != null and reportForm.endTime != null'>AND report_time BETWEEN #{reportForm.startTime} AND #{reportForm.endTime}</if>" +
            "</where>" +
            "ORDER BY create_time DESC" +
            "</script>")
    List<ReportEntity> listReport(@Param("reportForm") ReportForm reportForm);

}
