package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.UserCommentEntity;
import com.goodsogood.ows.model.vo.UserComment;
import com.goodsogood.ows.model.vo.UserInfoCommentVO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 党员民主评议Mapper
 * <AUTHOR>
 * @date 2019/12/27
 * @return
 */
@Repository
@Mapper
public interface UserCommentMapper extends MyMapper<UserCommentEntity> {

    /**
     * 根据等级查询评议列表
     * <AUTHOR>
     * @date 2019/12/30
     * @param year
     * @param commentLevel
     * @return java.util.List<com.goodsogood.ows.model.db.UserCommentEntity>
     */
    @Select("<script>" +
            "select user_comment_id,  user_id, user_name, rating \n" +
            "from t_meeting_user_comment \n" +
            "where status = 1 \n" +
            "and (org_id = #{orgId} or org_level like CONCAT('%-', #{orgId}, '-%')) \n" +
            "<if test=\"userName != null and userName != '' \">AND user_name like CONCAT('%', #{userName}, '%')\n</if>" +
            "and review_year = #{year} \n" +
            "and rating = #{level} \n" +
            "</script>")
    @Results({
            @Result(property = "userCommentId", column = "user_comment_id"),
            @Result(property = "userId", column = "user_id"),
            @Result(property = "userName", column = "user_name"),
            @Result(property = "commentLevel", column = "rating")
    })
    List<UserInfoCommentVO> selectUserListByLevel(@Param("orgId") Long orgId, @Param("year") Integer year,
                                                  @Param("level") Integer commentLevel, @Param("userName") String userName);

    @Select("<script>" +
            "SELECT rating, deal_opinion, count(0) as total from t_meeting_user_comment \n" +
            "WHERE `status` = 1 \n" +
            " AND (org_id = #{orgId} or org_level like CONCAT('%-', #{orgId}, '-%')) \n" +
            " AND review_year = #{year} \n" +
            "GROUP BY rating, deal_opinion\n" +
            "</script>")
    @Results({
            @Result(property = "rating", column = "rating"),
            @Result(property = "dealOpinion", column = "deal_opinion"),
            @Result(property = "number", column = "total")
    })
    List<UserComment> selectCommentCount(@Param("orgId") Long orgId, @Param("year") Integer year);

    @Select("<script>" +
            " select review_year from t_meeting_user_comment where (DATE_FORMAT(create_time, '%Y-%m-%d') = #{day} OR DATE_FORMAT(update_time, '%Y-%m-%d') = #{day} ) GROUP BY review_year; " +
            "</script>")
    List<Integer> getYearByUpdate(@Param("day") String day);
}
