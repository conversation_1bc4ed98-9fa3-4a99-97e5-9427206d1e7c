package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.SbwHandleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * 南岸区网信办任务处理mapper
 * <AUTHOR>
 * @date 2021.07.30
 */
@Mapper
@Repository
public interface SbwHandleMapper extends MyMapper<SbwHandleEntity>{

    @Select("SELECT count(*) FROM `t_meeting_sbw_handle` WHERE task_id = #{taskId} AND is_del = 0")
    Integer selectById(@Param("taskId")Long taskId);
}
