package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingOrgDebriefReviewEntity;
import com.goodsogood.ows.model.vo.MeetingOrgDebriefReviewQueryForm;
import com.goodsogood.ows.model.vo.MeetingOrgDebriefReviewQueryVO;
import com.goodsogood.ows.model.vo.MeetingOrgDebriefReviewStatisticsQueryVO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.provider.SpecialProvider;

import java.util.List;

/**
 * <AUTHOR>
 * @describe 组织述职评议sql
 * @date 2019-12-30
 */
@Repository
@Mapper
public interface MeetingOrgDebriefReviewMapper extends MyMapper<MeetingOrgDebriefReviewEntity> {

    @Options(useGeneratedKeys = true, keyProperty = "meetingOrgDebriefReviewId")
    @InsertProvider(type = SpecialProvider.class, method = "dynamicSQL")
    int insertUseGeneratedKeys(MeetingOrgDebriefReviewEntity record);

    /**
     * 查询组织述职评议信息列表
     */
    @Select("<script>" +
            "select" +
            "   org_id orgId," +
            "   meeting_org_debrief_review_id meetingOrgDebriefReviewId," +
            "   rating," +
            "   org_name orgName, " +
            "   org_type_child orgTypeChild " +
            "FROM   " +
            "   t_meeting_org_debrief_review t   " +
            "       <where> " +
            "           <if test=\"orgIds!=null and orgIds.size > 0\">" +
            "               and org_id in " +
            "               <foreach collection=\"orgIds\" separator=\",\" open=\"(\" close=\")\" item=\"orgId\">" +
            "                   #{orgId}" +
            "               </foreach>" +
            "           </if> " +
            "           <if test=\"orgName!=null and orgName!=''\">" +
            "               and t.org_name like concat('%',#{orgName},'%')" +
            "           </if> " +
            "           <if test=\"orgLevel!=null and orgLevel!=''\">" +
            "               and (t.current_org_level like concat(#{orgLevel},#{orgId},'-','%')   or t.org_id = #{orgId}) " +
            "           </if> " +
            "           <if test=\"status!=null\"> " +
            "               and t.status = #{status} " +
            "           </if> " +
            "           <if test=\"reviewYear!=null and reviewYear!=''\"> " +
            "               and t.review_year = #{reviewYear} " +
            "           </if> " +
            "           <if test=\"rating!=null\"> " +
            "               and t.rating = #{rating} " +
            "           </if> " +
            "       </where> " +
            "   ORDER BY t.create_time DESC" +
            "</script>")
    List<MeetingOrgDebriefReviewQueryVO> listMeetingOrgDebriefReviewQueryVO(MeetingOrgDebriefReviewQueryForm dto);

    /**
     * 查询组织统计数量
     */
    @Select("<script>" +
            "   SELECT" +
            "   CONCAT(current_org_level,org_id,\"-\") orgLevel,rating,count(1) count" +
            "   FROM" +
            "   t_meeting_org_debrief_review t" +
            "   WHERE" +
            "       <foreach collection=\"orgIds\" separator=\"or\" open=\"(\" close=\")\" item=\"orgId\">" +
            "           (t.current_org_level like concat('%','-',#{orgId},'-','%')   or t.org_id = #{orgId})" +
            "       </foreach>" +
            "   and `status` = #{status}" +
            "   and review_year = #{reviewYear}" +
            "   GROUP BY current_org_level,rating,org_id" +
            "</script>")
    List<MeetingOrgDebriefReviewStatisticsQueryVO.OrgDebriefReviewStatistics> listOrgDebriefReviewStatistics(
            MeetingOrgDebriefReviewStatisticsQueryVO.OrgDebriefReviewStatisticsDTO dto);


    @Update("update t_meeting_org_debrief_review set current_org_level =#{currentOrgLevel} where org_id= #{orgId}")
    int updateCurrentOrgLevelByOrgId(@Param("orgId") Long orgId, @Param("currentOrgLevel") String currentOrgLevel);
}
