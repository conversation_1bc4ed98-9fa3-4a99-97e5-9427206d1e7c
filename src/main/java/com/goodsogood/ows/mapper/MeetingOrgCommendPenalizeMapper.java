package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingOrgCommendPenalizeEntity;
import com.goodsogood.ows.model.vo.MeetingOrgCommendPenalizeQueryForm;
import com.goodsogood.ows.model.vo.MeetingOrgCommendPenalizeQueryVO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.provider.SpecialProvider;

import java.util.List;

/**
 * <AUTHOR>
 * @describe 组织奖惩sql
 * @date 2019-12-30
 */
@Repository
@Mapper
public interface MeetingOrgCommendPenalizeMapper extends MyMapper<MeetingOrgCommendPenalizeEntity> {


    /**
     * 查询组织奖惩信息列表
     */
    @Select("<script>" +
            "SELECT" +
            "   org_name orgName," +
            "   meeting_org_commend_penalize_id meetingOrgCommendPenalizeId," +
            "   `category`," +
            "   `level`," +
            "   `name`," +
            "   `content`," +
            "   `type`, " +
            "   ratify_time ratifyTime," +
            "   award_unit awardUnit," +
            "   related_file relatedFile," +
            "   basis_description basisDescription," +
            "   approval_status approvalStatus " +
            "FROM " +
            "   t_meeting_org_commend_penalize t" +
            "       <where> " +
            "           <if test=\"orgName!=null and orgName!=''\">" +
            "               and t.org_name like concat('%',#{orgName},'%')" +
            "           </if> " +
            "           <if test=\"orgLevel!=null and orgLevel!=''\">" +
            "               and (t.org_level like concat(#{orgLevel},#{orgId},'-','%')   or t.org_id = #{orgId}) " +
            "           </if> " +
            "           <if test=\"status!=null\"> " +
            "               and t.status = #{status} " +
            "           </if> " +
            "           <if test=\"category!=null and category!=''\">" +
            "               and t.category = #{category} " +
            "           </if> " +
            "           <if test=\"type!=null and type!=''\">" +
            "               and t.type = #{type} " +
            "           </if> " +
            "           <if test=\"level!=null and level!=''\">" +
            "               and t.level = #{level} " +
            "           </if> " +
            "           <if test=\"name!=null and name!=''\">" +
            "               and t.name = #{name} " +
            "           </if> " +
            "           <if test=\"ratifyStartTime!=null and ratifyStartTime!=''\">" +
            "           and t.ratify_time &gt;= #{ratifyStartTime} " +
            "           </if> " +
            "           <if test=\"ratifyEndTime!=null and ratifyEndTime!=''\">" +
            "           and t.ratify_time &lt;= concat(#{ratifyEndTime},' 23:59:59') " +
            "           </if> " +
            "       </where> " +
            "   ORDER BY t.ratify_time DESC" +
            "</script>")
    List<MeetingOrgCommendPenalizeQueryVO> listMeetingOrgCommendPenalizeQueryVO(MeetingOrgCommendPenalizeQueryForm dto);


    @Override
    @Options(useGeneratedKeys = true, keyProperty = "meetingOrgCommendPenalizeId")
    @InsertProvider(type = SpecialProvider.class, method = "dynamicSQL")
    int insertUseGeneratedKeys(MeetingOrgCommendPenalizeEntity record);

    @Select("<script>" +
            "SELECT" +
            "   meeting_org_commend_penalize_id meetingOrgCommendPenalizeId," +
            "   `category`," +
            "   `level`," +
            "   `name`," +
            "   ratify_time ratifyTime, " +
            "   basis_description basisDescription " +
            "FROM " +
            "   t_meeting_org_commend_penalize t" +
            "       <where> " +
            "             status = 1" +
            "             and approval_status = 2" +
            "             and type = 1" +
            "           <if test=\"orgId!=null\">" +
            "               and t.org_id = #{orgId}" +
            "           </if> " +
            "       </where> " +
            "   ORDER BY t.ratify_time DESC" +
            "</script>")
    List<MeetingOrgCommendPenalizeQueryVO> getMeetingOrgCommendPenalizeByOrgId(@Param("orgId") Long orgId);

}
