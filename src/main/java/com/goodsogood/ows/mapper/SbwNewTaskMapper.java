package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.SbwTaskEntity;
import com.goodsogood.ows.model.db.TopicContentEntity;
import com.goodsogood.ows.model.vo.SbwTaskForm;
import com.goodsogood.ows.model.vo.SbwTaskListForm;
import com.goodsogood.ows.model.vo.SbwTaskOrgListForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/08/03
 **/
@Repository
@Mapper
public interface SbwNewTaskMapper extends MyMapper<SbwTaskEntity> {


    /**
     * 发布任务列表
     */
    @Select("<script>" +
            "select " +
            "t.task_id taskId," +
            "t.task_type taskType," +
            "t.title," +
            "t.begin_time beginTime," +
            "t.end_time endTime," +
            "t.status," +
            "ifnull(san.num,0) submitNum," +
            "ifnull(vn.num,0) verifyNum," +
            "ifnull(un.num,0) unNum " +
            "from t_meeting_sbw_task t " +
            "left join (select task_id,count(1) num from t_meeting_sbw_task_org where is_read = 1 group by task_id) san on t.task_id = san.task_id " +
            "left join (select task_id,count(1) num from t_meeting_sbw_task_org where type = 2 group by task_id) vn on t.task_id = vn.task_id " +
            "left join (select task_id,count(1) num from t_meeting_sbw_task_org where is_read = 0 group by task_id) un on t.task_id = un.task_id " +
            "where " +
            "t.region_id = #{regionId} and t.org_id = #{orgId} and t.is_del = 0" +
            "<if test =\"title != null and title !='' \"> and t.title like concat('%',#{title},'%')</if> " +
            "<if test = \"beginTime != null\"> and t.begin_time <![CDATA[>= ]]> #{beginTime}</if> " +
            "<if test = \"beginTime != null\"> and t.end_time <![CDATA[<= ]]> #{endTime}</if> " +
            "order by t.task_id desc" +
            "</script>")
    List<SbwTaskListForm> releaseTaskList(
            @Param("regionId") Long regionId,
            @Param("orgId") Long orgId,
            @Param("title") String title,
            @Param("beginTime") Date beginTime,
            @Param("endTime") Date endTime);

    /**
     * 更新任务表任务状态
     *
     * @return
     */
    @Update("update t_meeting_sbw_task t,t_meeting_sbw_task_org o  " +
            "set t.status = ( " +
            "case when(t.begin_time <= date(now()) and t.end_time > date(now())) then 3  " +
            "when(t.end_time < date(now())) then 4  " +
            "else t.status end),  " +
            "o.status = ( " +
            "case when(o.begin_time <= date(now()) and o.end_time > date(now())) then 3  " +
            "when(o.end_time < date(now())) then 4  " +
            "else o.status end),  " +
            "t.update_time = now(), o.update_time = now() where t.status > 0")
    int taskStatus();

    /**
     * 删除任务
     */
    @Update("UPDATE t_meeting_sbw_task SET is_del = 1  WHERE task_id = #{taskId}")
    int updateIsDel(@Param("taskId") Long taskId);

    /**
     * 生成打印单
     *
     * @param taskId
     * @return
     */
    @Select("SELECT " +
            " title, " +
            " number, " +
            " source, " +
            " content, " +
            " t.org_id, " +
            " h.org_id, " +
            " begin_time beginTime, " +
            " open_status openStatus, " +
            " private_reason privateReason,"+
            " h.handle_content handleContent, " +
            " h.accept_time acceptTime, " +
            " h.accept_org acceptOrg, " +
            " h.accept_user acceptUser, " +
            " h.phone phone, " +
            " h.flag, " +
            " remark  " +
            "FROM " +
            " t_meeting_sbw_task t " +
            "LEFT JOIN t_meeting_sbw_handle h ON t.task_id = h.task_id " +
            "WHERE " +
            " t.task_id = #{taskId}  " +
            "AND t.is_del = 0 " +
            "AND h.org_id = #{orgId} " +
            "AND h.flag = 1")
    SbwTaskForm createPrintWord(@Param("taskId") Long taskId,
                                @Param("orgId") Long orgId);

    /**
     * 查询任务的执行状态是否是未开始
     *
     * @param taskId
     * @return
     */
    @Select("select count(*) from t_meeting_sbw_task where status in ${status} and task_id = #{taskId}")
    int findStartStatus(@Param("taskId") Long taskId,
                        @Param("status") String status);

    /**
     * 已接受任务组织 1
     * @param taskId
     * @return
     */
    @Select("SELECT task_id,org_id orgId, org_name orgName FROM t_meeting_sbw_task_org  WHERE task_id = #{taskId} and is_read = 1 GROUP BY org_id")
    List<SbwTaskOrgListForm> findAdoptOrg(@Param("taskId") Long taskId);

    /**
     * 已拒绝任务组织 2
     * @param taskId
     * @return
     */
    @Select("SELECT org_id orgId, org_name orgName FROM t_meeting_sbw_task_org  WHERE task_id = #{taskId} and type = 2  GROUP BY org_id")
    List<SbwTaskOrgListForm> findRejectOrg(@Param("taskId") Long taskId);

    /**
     * 未接受任务组织 3
     * @param taskId
     * @return
     */
    @Select("SELECT task_id,org_id orgId, org_name orgName FROM t_meeting_sbw_task_org  WHERE task_id = #{taskId} and is_read = 0 GROUP BY org_id")
    List<SbwTaskOrgListForm> findNotAdoptOrg(@Param("taskId") Long taskId);

    /**
     * 已反馈任务组织 4
     * @param taskId
     * @return
     */
    @Select("SELECT task_id,org_id orgId, org_name orgName FROM t_meeting_sbw_task_org  WHERE task_id = #{taskId} and type in (1,2,4,5) GROUP BY org_id")
    List<SbwTaskOrgListForm> findFeedbackOrg(@Param("taskId") Long taskId);

    /**
     * 已审核任务组织 5
     * @param taskId
     * @return
     */
    @Select("SELECT org_id orgId, org_name orgName FROM t_meeting_sbw_task_org  WHERE task_id = #{taskId} and type in(3,4,5) GROUP BY org_id")
    List<SbwTaskOrgListForm> findCheckOrg(@Param("taskId") Long taskId);

    /**
     * 未按时反馈任务组织 6
     * @param taskId
     * @return
     */
    @Select("SELECT " +
            "org_id orgId, " +
            "org_name orgName " +
            "FROM " +
            "t_meeting_sbw_task_org " +
            "WHERE " +
            "task_id = #{taskId} " +
            "and type in (0,3)" +
            "and end_time < DATE_ADD(DATE_ADD(str_to_date(DATE_FORMAT(NOW(),'%Y-%m-%d'),'%Y-%m-%d %H:%i:%s'),INTERVAL 1 DAY),INTERVAL -1 SECOND) " +
            "GROUP BY org_id")
    List<SbwTaskOrgListForm> findNotFeedbackOrg(@Param("taskId") Long taskId);

}
