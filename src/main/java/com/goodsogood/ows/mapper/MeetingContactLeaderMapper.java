package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingContactLeaderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-22 15:06
 * @since 1.0.6
 */
@Repository
@Mapper
public interface MeetingContactLeaderMapper
        extends tk.mybatis.mapper.common.Mapper<MeetingContactLeaderEntity>,
        MeetingContactLeaderInsertListMapper<MeetingContactLeaderEntity> {
    @Select(" SELECT " +
            "   a.meeting_contact_leader_id as meetingContactLeaderId," +
            "   a.meeting_id as meetingId," +
            "   a.user_id as userId," +
            "   a.org_id as orgId," +
            "   a.user_name as userName," +
            "   a.org_name as orgName" +
            " FROM " +
            "   t_meeting_contact_leader a " +
            " WHERE a.is_del=0 and a.meeting_id = #{meetingId} ")
    List<MeetingContactLeaderEntity> findByMeetingId(@Param("meetingId") long meetingId);
}
