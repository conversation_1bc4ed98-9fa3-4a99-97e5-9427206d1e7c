package com.goodsogood.ows.mapper;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Options;
import tk.mybatis.mapper.provider.SpecialProvider;

import java.util.List;

/**
 * 批量插入组织和任务的关系
 * <AUTHOR>
 * @create 2018-10-23 9:04
 **/
public interface TopicOrgInsertListMapper<T> {

     @Options(useGeneratedKeys = true, keyProperty = "topicOrgId")
     @InsertProvider(type = SpecialProvider.class, method = "dynamicSQL")
     int insertList(List<T> recordList);

}
