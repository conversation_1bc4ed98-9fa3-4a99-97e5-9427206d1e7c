package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingUserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-25 17:56
 **/
@Repository
@Mapper
public interface MeetingUserMapper extends tk.mybatis.mapper.common.Mapper<MeetingUserEntity>, MeetingUserInsertListMapper<MeetingUserEntity> {
    String SELECT_COLUMNS = "meeting_user_id AS meetingUserId," +
            "meeting_id AS meetingId," +
            "user_id AS userId," +
            "org_id AS orgId," +
            "user_name AS userName," +
            "org_name AS orgName," +
            "phone," +
            "cert_number AS certNumber," +
            "is_add AS isAdd," +
            "sign_status AS signStatus," +
            "old_sign_status AS oldSignStatus," +
            "reason," +
            "tag ";

    /**
     * 查询 记录人员
     * 查询会议详情时使用 {@link com.goodsogood.ows.mapper.MeetingMapper#findByIdAndOrg(long, Long)}
     *
     * @param meetingId 会议id
     * @return List<MeetingUserEntity>
     */
    @Select("select " + SELECT_COLUMNS + " from t_meeting_user where meeting_id = #{meetingId} and tag = " + MeetingUserEntity.TAG_RECORD)
    List<MeetingUserEntity> findRecordUserByMeetingId(@Param("meetingId") long meetingId);

    /**
     * 查询主持人
     * 查询会议详情时使用 {@link com.goodsogood.ows.mapper.MeetingMapper#findByIdAndOrg(long, Long)}
     *
     * @param meetingId 会议id
     * @return List<MeetingUserEntity>
     */
    @Select("select " + SELECT_COLUMNS + " from t_meeting_user where meeting_id = #{meetingId} and tag = " + MeetingUserEntity.TAG_HOST)
    List<MeetingUserEntity> findHostUserByMeetingId(@Param("meetingId") long meetingId);

    /**
     * 查询参会人员
     * 查询会议详情时使用 {@link com.goodsogood.ows.mapper.MeetingMapper#findByIdAndOrg(long, Long)}
     *
     * @param meetingId 会议id
     * @return List<MeetingUserEntity>
     */
    @Select("select " + SELECT_COLUMNS + " from t_meeting_user where meeting_id = #{meetingId} and tag = " + MeetingUserEntity.TAG_PART)
    List<MeetingUserEntity> findParticipantUsersByMeetingId(@Param("meetingId") long meetingId);

    /**
     * 查询列席人员
     * 查询会议详情时使用 {@link com.goodsogood.ows.mapper.MeetingMapper#findByIdAndOrg(long, Long)}
     *
     * @param meetingId 会议id
     * @return List<MeetingUserEntity>
     */
    @Select("select " + SELECT_COLUMNS + " from t_meeting_user where meeting_id = #{meetingId} and tag = " + MeetingUserEntity.TAG_ATTEND)
    List<MeetingUserEntity> findAttendUsersByMeetingId(@Param("meetingId") long meetingId);

    /**
     * 查询讲课人
     * 查询会议详情时使用 {@link com.goodsogood.ows.mapper.MeetingMapper#findByIdAndOrg(long, Long)}
     *
     * @param meetingId 会议id
     * @return List<MeetingUserEntity>
     */
    @Select("select " + SELECT_COLUMNS + " from t_meeting_user where meeting_id = #{meetingId} and tag = " + MeetingUserEntity.TAG_LECTURER)
    List<MeetingUserEntity> findLecturersByMeetingId(@Param("meetingId") long meetingId);

    @Update("<script>" +
            "UPDATE t_meeting_user SET <if test=\"signStatus = 1\"> sign_time = now() ,</if> sign_status = #{signStatus},reason = #{reason} WHERE meeting_id = #{meetingId} AND user_id = #{userId}" +
            "</script>")
    int updateMeetingUser(@Param("signStatus") int signStatus,
                          @Param("reason") String reason,
                          @Param("meetingId") long meetingId,
                          @Param("userId") long userId);

    /**
     * 签到列表
     *
     * @param meetingId
     * @return
     */
    @Select("SELECT" +
            " user_name userName," +
            " head_url headUrl," +
            " IF((GROUP_CONCAT(sign_status) LIKE '%1%'),1,2) signStatus," +
            " sign_time signTime " +
            " FROM" +
            " t_meeting_user " +
            " WHERE" +
            " tag in (3,4) and org_id<>-1 and meeting_id = #{meetingId} " +
            " GROUP BY user_name" +
            " ORDER BY" +
            " sign_time ASC")
    List<MeetingUserEntity> signList(@Param("meetingId") Long meetingId);

    /**
     * 签到列表
     *
     * @param meetingId
     * @return
     */
    @Select("SELECT" +
            " user_name userName," +
            " head_url headUrl," +
            " IFNULL(sign_status,2) signStatus," +
            " sign_time signTime " +
            " FROM" +
            " t_meeting_user " +
            " WHERE" +
//            " tag in (3,4) and org_id<>-1 and meeting_id = #{meetingId} " +
            " tag in (3,4) and meeting_id = #{meetingId} " +
            " GROUP BY user_name" +
            " ORDER BY" +
            " sign_time ASC")
    List<MeetingUserEntity> signList2(@Param("meetingId") Long meetingId);

    @Update("update t_meeting_user set old_sign_status=#{oldStatus},sign_status=6 where meeting_user_id=#{meetingUserId}")
   Integer updateMeetingUserOldStatus(Short oldStatus,Long meetingUserId);


    @Update("update t_meeting_user set sign_status = #{signStatus},old_sign_status=#{oldStatus},reason = #{reason} WHERE meeting_id = #{meetingId} AND user_id = #{userId} ")
    void updateSignStatus(@Param("meetingId") Long meetingId, @Param("userId") Long userId,
                          @Param("signStatus") Integer signStatus,@Param("oldStatus") Short oldStatus,
                          @Param("reason") String reason);

    @Update("update t_meeting_user set old_sign_status = #{signStatus},sign_status=#{oldStatus},reason = #{reason} " +
            "WHERE meeting_id = #{meetingId} AND user_id = #{userId} ")
    void updateSignStatusNew(@Param("meetingId") Long meetingId, @Param("userId") Long userId,
                          @Param("signStatus") Short signStatus, @Param("oldStatus") Short oldStatus,
                          @Param("reason") String reason);

}