package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingOrgPeriodEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-09-19 10:57
 **/
@Repository
@Mapper
public interface MeetingOrgPeriodMapper extends tk.mybatis.mapper.common.Mapper<MeetingOrgPeriodEntity>, MeetingOrgPeriodInsertListMapper<MeetingOrgPeriodEntity> {
    @Select(" SELECT " +
            "   a.meeting_org_period_id," +
            "   a.meeting_id," +
            "   a.period_id," +
            "   a.start_time," +
            "   a.end_time " +
            " FROM " +
            "   t_meeting_org_period a " +
            " WHERE status=1 and a.meeting_id = #{meetingId} ORDER BY end_time DESC,meeting_org_period_id  ASC ")
    @Results({
            @Result(property = "meetingOrgPeriodId", column = "meeting_org_period_id"),
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "periodId", column = "period_id"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time")
    })
    List<MeetingOrgPeriodEntity> findByMeetingId(@Param("meetingId") long meetingId);
}