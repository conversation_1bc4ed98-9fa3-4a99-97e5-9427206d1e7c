package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.OrgLifeUploaderEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
@Mapper
public interface OrgLifeUploaderMapper extends MyMapper<OrgLifeUploaderEntity>{

    @Delete("<script>" +
            "delete from t_meeting_org_uploader where life_id=#{lifeId} and type=#{type} "+
            " <if test=\" dataId != null \">AND data_id=#{dataId} </if> " +
            " and user_id=#{userId} "+
            "</script>")
    Integer deleteUploader(Long userId,Long lifeId,Integer type,Long dataId);


    /**
     * 查询子模块其他上传人
     * @param lifeId
     * @param modelType
     * @return
     */
    @Select("<script> " +
            "select group_concat(username separator '、') uploader from t_meeting_org_uploader " +
            "where life_id = #{lifeId} and type in " +
            "<foreach collection=\"modelType\" index=\"index\"  item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "   #{item} " +
            "</foreach> " +
            "</script>")
    String uploader(@Param("lifeId") Long lifeId, @Param("modelType") List<Integer> modelType);

    /**
     * 查询行数据其他上传人
     * @param dataId
     * @param modelType
     * @return
     */
    @Select("select ifnull(group_concat(username separator '  '),'') from t_meeting_org_uploader " +
            "where data_id = #{dataId} and type = #{modelType}")
    String dataUploader(@Param("dataId") Long dataId,@Param("modelType") Integer modelType);

    @Delete("delete from t_meeting_org_uploader where life_id=#{lifeId}")
    void delByLifeId(Long lifeId);
}
