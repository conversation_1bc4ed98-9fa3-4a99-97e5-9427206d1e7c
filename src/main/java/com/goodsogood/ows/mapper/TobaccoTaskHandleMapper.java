package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.TobaccoTaskHandleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface TobaccoTaskHandleMapper extends MyMapper<TobaccoTaskHandleEntity>{

    @Update("update t_meeting_tobacco_task_handle set " +
            "handle_status = #{handleStatus},update_time = #{updateTime},handle_content = #{handleContent},handle_file = #{handleFile} " +
            "where handle_id = #{handleId}")
    Integer update(TobaccoTaskHandleEntity entity);
}
