package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.VideoConferenceEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
@Mapper
public interface VideoConferenceMapper extends MyMapper<VideoConferenceEntity> {

    @Select("<script>" +
            "SELECT\n" +
            "\ttvc.*,tu.name createName\n" +
            "FROM\n" +
            "\tt_video_conference AS tvc\n" +
            "LEFT JOIN t_user AS tu ON tvc.create_id = tu.user_id " +
            "WHERE\n" +
            "\t tvc.region_id = #{regionId} \n" +
            "\t<if test=\"conferenceType != null\"> AND tvc.conference_type = #{conferenceType} \n</if>" +
            "\t<if test=\"conferenceName != null and conferenceName != '' \"> AND tvc.conference_name LIKE CONCAT( '%', #{conferenceName}, '%' ) </if>\n" +
            "\t<if test=\"conferenceTitle != null and conferenceTitle != '' \"> AND tvc.conference_title LIKE CONCAT( '%', #{conferenceTitle}, '%' ) </if>\n" +
            "\t<if test=\"roomCode != null and roomCode != '' \"> AND tvc.room_code LIKE CONCAT( '%', #{roomCode}, '%' ) </if>\n" +
            "\t<if test=\"minCreateTime != null \"> AND tvc.create_time &gt;= #{minCreateTime} </if>\n" +
            "\t<if test=\"maxCreateTime != null \"> AND tvc.create_time &lt;= #{maxCreateTime} </if>\n" +
            "\t<if test=\"isHistory != null and isHistory == 0\"> AND tvc.status IN(0,1) </if>\n" +
            "\t<if test=\"isHistory != null and isHistory == 1\"> AND tvc.status IN(2,3) </if>\n" +
            "ORDER BY tvc.create_time DESC"+
            "</script>")
    @Results({
            @Result(property = "conferenceId", column = "conference_id"),
            @Result(property = "conferenceType", column = "conference_type"),
            @Result(property = "conferenceName", column = "conference_name"),
            @Result(property = "conferenceTitle", column = "conference_title"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "conferencePassword", column = "conference_password"),
            @Result(property = "hostPassword", column = "host_password"),
            @Result(property = "externalLinkUrl", column = "external_link_url"),
            @Result(property = "phoneNumbers", column = "phone_numbers"),
            @Result(property = "roomCode", column = "room_code"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "createName", column = "createName")
    })
    List<VideoConferenceEntity> selectList(@Param("conferenceType") Integer conferenceType,
                                           @Param("conferenceName") String conferenceName,
                                           @Param("conferenceTitle") String conferenceTitle,
                                           @Param("roomCode") String roomCode,
                                           @Param("minCreateTime") Date minCreateTime,
                                           @Param("maxCreateTime") Date maxCreateTime,
                                           @Param("regionId") Long regionId,
                                           @Param("isHistory") Integer isHistory);
}
