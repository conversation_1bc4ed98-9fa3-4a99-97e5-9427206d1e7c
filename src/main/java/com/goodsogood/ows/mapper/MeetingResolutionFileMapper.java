package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingResolutionFileEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-25 18:01
 **/
@Repository
@Mapper
public interface MeetingResolutionFileMapper extends tk.mybatis.mapper.common.Mapper<MeetingResolutionFileEntity>, MeetingResolutionFileInsertListMapper<MeetingResolutionFileEntity>{
    @Select("SELECT meeting_resolution_file_id,meeting_id,name,path,file_name,size " +
            " FROM t_meeting_resolution_file where meeting_id = #{id} ")
    @Results({
            @Result(property = "meetingResolutionFileId", column = "meeting_resolution_file_id"),
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "name", column = "name"),
            @Result(property = "path", column = "path"),
            @Result(property = "fileName", column = "file_name"),
            @Result(property = "size", column = "size")
    })
    List<MeetingResolutionFileEntity> findByMeetingId(@Param("meetingId") long meetingId);
}