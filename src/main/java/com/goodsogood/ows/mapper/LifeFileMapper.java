package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.LifeFileEntity;
import com.goodsogood.ows.model.db.LifeUploaderEntity;
import com.goodsogood.ows.model.vo.LifeFileVO;
import com.goodsogood.ows.model.vo.SaveAttachForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
@Repository
public interface LifeFileMapper extends MyMapper<LifeFileEntity>{

    @Select("select t1.life_file_id as lifeFileId,t1.file_id as fileId,t1.file_name as fileName,ifnull(t1.file_name_down,t1.file_name) as fileNameDown,t1.url as fileUrl,t2.type,t1.is_submit as isSubmit from t_meeting_life_file t1\n" +
            "join t_meeting_file_model t2 on t1.type=t2.model_id\n" +
            "where t1.life_id=#{lifeId} and t1.step=#{step}  and t1.type!=6 and t1.is_del=0 order by t1.create_time desc")
     List<LifeFileVO> queryFileInfo(@Param(value ="lifeId") Long lifeId, @Param(value ="step") Integer step);

    @Select("select t1.life_file_id as lifeFileId,t1.file_id,t1.file_name as fileName,ifnull(t1.file_name_down,t1.file_name) as fileNameDown,t1.url as fileUrl,t2.type from t_meeting_life_file t1\n" +
            "join t_meeting_file_model t2 on t1.type=t2.model_id\n" +
            "where t1.life_id=#{lifeId} and t1.is_submit=1 and is_del=0 order by t1.create_time desc")
     List<LifeFileVO> queryFileSubmit(@Param("lifeId") Long lifeId);

    @Update("<script>" +
            " update t_meeting_life_file set is_submit=1,update_time=now(),last_change_user=#{userId} where step=2 and life_file_id in" +
            " <foreach collection=\"lifeFileId\" item=\"fileId\" open=\"(\" close=\")\" separator=\",\">" +
             "#{fileId}"+
            " </foreach>" +
             " and is_del=0\n"+
            "</script>")
     Integer updateSubmit(@Param("lifeFileId") List<Long> lifeFileId,@Param("userId")Long userId);

    @Select("<script>" +
            "select count(0) from t_meeting_life_file where life_id=#{lifeId} and type=#{type} and step=#{step}  "+
            " <if test=\" dataId != null \">AND data_id=#{dataId} </if> " +
            " and is_del=0"+
            "</script>"
    )
    Integer selectNum(SaveAttachForm saveAttachForm);

    /**
     * 模块文件查询
     * @param lifeId
     * @param step
     * @param modelType
     * @return
     */
    @Select("<script>" +
            "select " +
            "life_file_id lifeFileId, " +
            "file_id fileId, " +
            "file_name fileName, " +
            "url fileUrl ," +
            "username username, " +
            "is_direct isDirect " +
            "from t_meeting_life_file " +
            "where life_id = #{lifeId} and type in " +
            "<foreach collection=\"modelType\" index=\"index\"  item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "   #{item} " +
            "</foreach> " +
            "and step = #{step} " +
            "and is_del != 1 " +
            "order by create_time,life_file_id" +
            "</script>")
    List<LifeFileVO> onlyFile(@Param("lifeId") Long lifeId, @Param("step") Integer step, @Param("modelType") List<Integer> modelType);

    /**
     * 行数据文件查询
     * @param dataId
     * @param step
     * @return
     */
    @Select("select " +
            "life_file_id lifeFileId, " +
            "file_id fileId, " +
            "file_name fileName, " +
            "url fileUrl ," +
            "username username, " +
            "type type, " +
            "is_direct isDirect " +
            "from t_meeting_life_file " +
            "where data_id = #{dataId} and step = #{step} and find_in_set(type,#{modelType}) and is_del !=1 " +
            "order by create_time,life_file_id "
    )
    List<LifeFileVO> dataFile(@Param("dataId") Long dataId,@Param("step") Integer step,@Param("modelType") String modelType);

//    /**
//     * 批量删除
//     * @param lifeFileIds
//     * @return
//     */
//    @Delete("<script>" +
//            "delete from t_meeting_life_file where life_file_id in " +
//            "<foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
//            "#{item}" +
//            "</foreach>" +
//            "</script>")
//    Integer multiDel(@Param("ids") List<Long> lifeFileIds);
//
//    /**
//     * 被删除关联的附件
//     * @param lifeId
//     * @param type
//     * @param packageType
//     * @return
//     */
//    @Select("<script>" +
//            "select f.life_file_id from t_meeting_life_file f " +
//            "<if test=\" packageType == 3 \"> left join t_meeting_check d on f.life_id = d.life_id </if> " +
//            "<if test=\" packageType == 4 \"> left join t_meeting_advice d on f.life_id = d.life_id </if> " +
//            "<if test=\" packageType == 5 \"> left join t_meeting_talk d on f.life_id = d.source_id and d.source = 1</if> " +
//            "where f.type in " +
//            "<foreach collection=\"type\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
//            "#{item}" +
//            "</foreach> " +
//            "and f.data_id != " +
//            "<if test=\" packageType == 3 \"> d.check_id </if> " +
//            "<if test=\" packageType == 4 \"> d.advice_id </if> " +
//            "<if test=\" packageType == 5 \"> d.talk_id</if> " +
//            "and f.step = 2 " +
//            "</script>")
//    List<Long> toDel(@Param("lifeId")Long lifeId,@Param("type")List<Integer> type,@Param("packageType") Integer packageType);
//
//    /**
//     * 被逻辑删除关联的附件
//     * @param lifeId
//     * @param type
//     * @param packageType
//     * @return
//     */
//    @Select("<script>" +
//            "select f.life_file_id from t_meeting_life_file f " +
//            "<if test=\" packageType == 3 \"> left join t_meeting_check d on f.life_id = d.life_id </if> " +
//            "<if test=\" packageType == 4 \"> left join t_meeting_advice d on f.life_id = d.life_id </if> " +
//            "<if test=\" packageType == 5 \"> left join t_meeting_talk d on f.life_id = d.source_id and d.source = 1</if> " +
//            "where f.type in " +
//            "<foreach collection=\"type\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
//            "#{item}" +
//            "</foreach> " +
//            "and f.is_del = 0 and f.step = 2 and d.is_del = 1 " +
//            "</script>")
//    List<Long> toLogicDel(@Param("lifeId")Long lifeId,@Param("type")List<Integer> type,@Param("packageType") Integer packageType);

    @Update("<script> " +
            " update t_meeting_life_file set is_del=1,update_time=now(),last_change_user=#{userId} where life_file_id in " +
            " <foreach collection=\"lifeFileId\" item=\"fileId\" open=\"(\" close=\")\" separator=\",\">" +
            "#{fileId}"+
            " </foreach>"+
            "</script>")
    void deleteAll(@Param("lifeFileId") List<Long> lifeFileId,Long userId);

    @Update("<script>" +
            "update t_meeting_life_file set is_del = 1,update_time = now(),last_change_user = #{userId} "+
            " where life_id=#{lifeId} and type in " +
            " <foreach collection=\"type\" item=\"typeid\" open=\"(\" close=\")\" separator=\",\">" +
            " #{typeid} "+
            "</foreach>" +
            " and data_id=#{dataId}" +
            "</script>")
    void deleteTalkTemplate(@Param("lifeId") Long lifeId,@Param("type") List<Integer> type,@Param("dataId") Long dataId,@Param("userId") Long userId);


    @Update("<script>" +
            "update t_meeting_life_file set is_del = 1,update_time = now(),last_change_user = #{userId} "+
            " where life_id=#{lifeId} and data_id in " +
            " <foreach collection=\"dataIds\" item=\"dataId\" open=\"(\" close=\")\" separator=\",\">" +
            " #{dataId} "+
            "</foreach>" +
            " and type in " +
            " <foreach collection=\"type\" item=\"typeid\" open=\"(\" close=\")\" separator=\",\">" +
            " #{typeid} "+
            "</foreach>" +
            "</script>")
    void deleteByIds(@Param("lifeId") Long lifeId,@Param("type") List<Integer> type,@Param("dataIds") List<Long> dataIds,@Param("userId") Long userId);



    @Update("<script>"+
            "update t_meeting_life_file set is_del = 1,update_time = now(),last_change_user = #{userId} "+
            " where life_id=#{lifeId} and type=#{type}" +
            " and data_id=#{dataId} and file_name in " +
            " <foreach collection=\"fileName\" item=\"name\" open=\"(\" close=\")\" separator=\",\">" +
            " #{name} "+
            "</foreach>" +
            " and url in "+
            " <foreach collection=\"url\" item=\"fileUrl\" open=\"(\" close=\")\" separator=\",\">" +
            " #{fileUrl} "+
            "</foreach>" +
            " and is_del=0"+
            "</script>")
    void deleteTalkFile(@Param("lifeId") Long lifeId,@Param("type") Integer type,
                        @Param("dataId") Long dataId,@Param("userId") Long userId,
                        @Param("fileName") List<String> fileName,@Param("url") List<String> url);

    @Update("update t_meeting_life_file set is_del=1 where life_id=#{lifeId} and type=7 and step=#{step}")
    void deleteAdviceFile(@Param("lifeId") Long lifeId,@Param("step") Integer step);

    @Update("update t_meeting_life_file set is_del = 1,update_time = now(),last_change_user = #{userId} where life_id=#{lifeId} and type=#{type} and data_id=#{dataId}")
    void deleteTalkFileAll(@Param("lifeId") Long lifeId, @Param("type") Integer type, @Param("dataId") Long dataId,@Param("userId") Long userId);

    @Delete("delete from t_meeting_life_file where life_id=#{lifeId}")
    void delByLifeId(Long lifeId);
}
