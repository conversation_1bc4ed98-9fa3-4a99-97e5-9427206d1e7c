package com.goodsogood.ows.mapper;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2018/10/31 16:59
 */
public class MeetingResultProvider {

    public String getPageColumn() {
        return "DISTINCT\n" +
                "(r.meeting_reslut_id) meetingReslutId,\n" +
                "m.meeting_id meetingId,\n" +
                "m.org_name orgName,\n" +
                "m.`name` NAME,\n" +
                "t.category category,\n" +
                "m.types types,\n" +
                "m.start_time startTime,\n" +
                "m.status status,\n" +
                "r.submit_time submitTime";
    }

    public String getPageSql(Map<String, Object> params) {
        long oid = MapUtils.getLong(params, "oid");
        int oper = MapUtils.getIntValue(params, "oper");
        String orgName = MapUtils.getString(params, "orgName");
        Integer meetingClass = MapUtils.getInteger(params, "meetingClass");
        String meetingTypes = MapUtils.getString(params, "meetingTypes");
        String meetingStartTime = MapUtils.getString(params, "meetingStartTime");
        String meetingEndTime = MapUtils.getString(params, "meetingEndTime");
        String submitStartTime = MapUtils.getString(params, "submitStartTime");
        String submitEndTime = MapUtils.getString(params, "submitEndTime");
        meetingTypes = StringUtils.removeStart(meetingTypes, ",");
        meetingTypes = StringUtils.removeEnd(meetingTypes, ",");
        String finalTypes = meetingTypes;
        return new SQL() {{
            SELECT(getPageColumn());
            FROM("t_meeting_result r")
                    .LEFT_OUTER_JOIN("t_meeting m ON r.meeting_id = m.meeting_id")
                    .LEFT_OUTER_JOIN("t_meeting_type t ON r.meeting_id = t.meeting_id");
            WHERE("r.is_del = 0");
            WHERE("m.p_org_id = " + oid);
            if (oper == 1) {
                WHERE("m.`status` IN (7, 12, 13, 14)");
            } else if (oper == 2) {
                WHERE("m.`status` = 12");
            }
            if (StringUtils.isNotBlank(orgName)) {
                WHERE("m.org_name like CONCAT('%',#{orgName},'%')");
            }
            if (meetingClass != null) {
                WHERE("t.category_id = " + meetingClass);
            }
            if (StringUtils.isNotBlank(finalTypes)) {
                WHERE("t.type_id in (" + finalTypes + ")");
            }

            if (StringUtils.isNotBlank(meetingStartTime)) {
                WHERE("m.start_time >='" + meetingStartTime + " 00:00:00'");
            }
            if (StringUtils.isNotBlank(meetingEndTime)) {
                WHERE("m.start_time <='" + meetingEndTime + " 23:59:59'");
            }

            if (StringUtils.isNotBlank(submitStartTime)) {
                WHERE("r.submit_time >='" + submitStartTime + " 00:00:00'");
            }
            if (StringUtils.isNotBlank(submitEndTime)) {
                WHERE("r.submit_time <='" + submitEndTime + " 23:59:59'");
            }
            ORDER_BY("r.submit_time desc");
        }}.toString();
    }
}
