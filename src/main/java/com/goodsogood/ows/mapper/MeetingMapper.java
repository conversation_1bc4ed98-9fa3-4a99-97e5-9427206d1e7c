package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingTaskEntity;
import com.goodsogood.ows.model.dto.MeetingReportDto;
import com.goodsogood.ows.model.vo.*;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2018-10-25 10:53
 **/
@Repository
@Mapper
public interface MeetingMapper extends MyMapper<MeetingEntity> {
    /**
     * @param id  活动id
     * @param oid 活动所属组织id 或 上级检查组织的id
     */
    @Select(" SELECT meeting_id,szf_meeting_id,types,name,org_id,org_name,address,gps_type,lng,lat, " +
            " start_time,is_sign_in,is_w_resolution,resolution,sign_start_time,sign_end_time," +
            " must_approve,workflow_id,workflow_name,workflow_task_id,status,add_type,sel_contact_leaders," +
            " lecture_title,has_lecturer,has_lecture_title,content,end_time,theory_learn,notify_type," +
            " notify_way,notify_time,sign_in_way,record_type,can_sign,sign_time,life_id,model_id,ding_event_id,ding_event_sync,ding_event_create_user,create_user,last_change_user " +
            " FROM t_meeting " +
            " where meeting_id = #{id} " +
//            " and (org_id = #{oid} or p_org_id = #{oid} ) " +
            " and is_del = 0 ")
    @Results({
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "szfMeetingId", column = "szf_meeting_id"),
            @Result(property = "lifeId", column = "life_id"),
            @Result(property = "modelId", column = "model_id"),
            @Result(property = "types", column = "types"),
            @Result(property = "name", column = "name"),
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "orgName", column = "org_name"),
            @Result(property = "address", column = "address"),
            @Result(property = "gpsType", column = "gps_type"),
            @Result(property = "lng", column = "lng"),
            @Result(property = "lat", column = "lat"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "isSignIn", column = "is_sign_in"),
            @Result(property = "isWResolution", column = "is_w_resolution"),
            @Result(property = "resolution", column = "resolution"),
            @Result(property = "signStartTime", column = "sign_start_time"),
            @Result(property = "signEndTime", column = "sign_end_time"),
            @Result(property = "mustApprove", column = "must_approve"),
            @Result(property = "workflowId", column = "workflow_id"),
            @Result(property = "workflowName", column = "workflow_name"),
            @Result(property = "workflowTaskId", column = "workflow_task_id"),
            @Result(property = "status", column = "status"),
            @Result(property = "addType", column = "add_type"),
            @Result(property = "selContactLeaders", column = "sel_contact_leaders"),
            @Result(property = "lectureTitle", column = "lecture_title"),
            @Result(property = "hasLecturer", column = "has_lecturer"),
            @Result(property = "hasLectureTitle", column = "has_lecture_title"),
            @Result(property = "content", column = "content"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "theoryLearn", column = "theory_learn"),
            @Result(property = "notifyType", column = "notify_type"),
            @Result(property = "notifyWay", column = "notify_way"),
            @Result(property = "notifyTime", column = "notify_time"),
            @Result(property = "signInWay", column = "sign_in_way"),
            @Result(property = "recordType", column = "record_type"),
            @Result(property = "canSign", column = "can_sign"),
            @Result(property = "signTime", column = "sign_time"),
            @Result(property = "dingEventId", column = "ding_event_id"),
            @Result(property = "dingEventSync", column = "ding_event_sync"),
            @Result(property = "dingEventCreateUser", column = "ding_event_create_user"),
            @Result(property = "createUser", column = "create_user"),
            @Result(property = "lastChangeUser", column = "last_change_user"),

            //处理one to one 记录人
            @Result(property = "recordUser", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingUserMapper.findRecordUserByMeetingId")),
            //处理one to one 主持人
            @Result(property = "hostUser", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingUserMapper.findHostUserByMeetingId")),
            //处理one to many 活动类型
            @Result(property = "meetingTypes", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingTypeMapper.findByMeetingId")),
            //处理one to many 任务
            @Result(property = "topics", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingTopicMapper.findByMeetingId")),
            //处理one to many 参会人员
            @Result(property = "participantUsers", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingUserMapper.findParticipantUsersByMeetingId")),
            //处理one to many 列席人员
            @Result(property = "attendUsers", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingUserMapper.findAttendUsersByMeetingId")),
            //处理one to many 讲课人
            @Result(property = "lecturers", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingUserMapper.findLecturersByMeetingId")),
            //处理one to many 决议附件
            @Result(property = "files", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingResolutionFileMapper.findByMeetingId")),
            //处理one to many 党小组
            @Result(property = "orgGroups", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingOrgGroupMapper.findByMeetingId")),
            //处理one to many 支委会届次
            @Result(property = "orgPeriods", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingOrgPeriodMapper.findByMeetingId")),
            //处理one to many 联系领导
            @Result(property = "contactLeaders", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingContactLeaderMapper.findByMeetingId")),
            //处理one to many 报告附件
            @Result(property = "resultFiles", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingResultFileMapper.findByMeetingId")),
            //处理one to many 活动议程
            @Result(property = "agenda", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingAgendaMapper.findByMeetingId")),
            //处理one to many 活动标签
            @Result(property = "meetingTag", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingTagMapper.findByMeetingIdNew"))

    })
    MeetingEntity findByIdAndOrg(@Param("id") long id, @Param("oid") Long oid);

    /**
     * 查询列表
     */
    @Select(
            " <script> "
                    + " SELECT a.meeting_id,a.szf_meeting_id,a.types,a.name,e.tags,a.org_id,a.org_name,a.start_time,a.address,a.status,"
                    + " r.submit_time as submit_time, "
                    + " b.category,b.category_id FROM t_meeting a "
                    + " LEFT JOIN t_meeting_result r ON a.meeting_id = r.meeting_id and r.is_del = 0  "
                    + " LEFT JOIN t_meeting_type b ON a.meeting_id = b.meeting_id  "
                    + " LEFT JOIN t_organization o ON a.org_id = o.organization_id"
                    + " LEFT JOIN (select meeting_id,group_concat( agenda_title separator ' ')title from t_meeting_agenda group by meeting_id) d ON a.meeting_id = d.meeting_id "
                    + " LEFT JOIN (select meeting_id,group_concat( distinct tag_name separator ',') tags from t_meeting_tag group by meeting_id) e ON a.meeting_id = e.meeting_id "
                    + " <if test =\" tagIds !=null and tagIds.size() > 0 \"> INNER JOIN (select meeting_id from t_meeting_tag where tag_id in "
                    + " <foreach collection=\"tagIds\" index=\"index\" item=\"tid\" open=\"(\" separator=\",\" close=\")\">"
                    + " #{tid} </foreach> GROUP BY meeting_id) f ON a.meeting_id=f.meeting_id </if>"
                    + " where a.is_del = 0 "
                    // 2019年2月12日 14:10:39 chenanshun 纪实回顾支持pc端查询 不查询“退回”的活动
                    //2021年4月08日 huangbinyang 纪实回顾支持pc端查询 增加查询“退回”活动和“组织状态”查询条件
                    //组织状态选择
                    + " <if test =\"orgStatus !=null\">"
                    + "     and o.status in"
                    + "        <foreach collection=\"orgStatus\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "                   #{item}\n"
                    + "        </foreach>"
                    + " </if>"
                    + " <if test =\"submitStartTime != null\"> and r.submit_time <![CDATA[ >= ]]> #{submitStartTime} </if> "
                    + " <if test =\"submitEndTime != null\"> and r.submit_time <![CDATA[ <= ]]> #{submitEndTime} </if> "
                    + " <if test =\"meetingName != null and meetingName != '' \"> and a.name like \"%\"#{meetingName}\"%\"</if> "
                    + " <if test =\"agenda != null and agenda != '' \"> and d.title like \"%\"#{agenda}\"%\"</if> "
                    + " <if test =\"keyWord != null and keyWord != '' \"> and (a.name like \"%\"#{keyWord}\"%\" or d.title like \"%\"#{keyWord}\"%\")</if> "
                    + " <if test =\"meetingClass != null\"> and b.category_id = #{meetingClass} </if> "
                    + " <if test =\"name != null and name != '' \"> and a.name like \"%\"#{name}\"%\"</if> "
                    + " <if test =\"startTime != null\"> and a.start_time <![CDATA[ >= ]]> #{startTime} </if> "
                    + " <if test =\"endTime != null\"> and a.start_time <![CDATA[ <= ]]> #{endTime} </if> "
                    + " <if test =\"typeIds != null and typeIds.size() > 0 \">"
                    + "   and b.type_id in "
                    + "        <foreach collection=\"typeIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "                   #{item}\n"
                    + "        </foreach>"
                    + "</if> "
                    + " <if test =\"key != null and key != '' \"> "
                    + "    and (a.name like \"%\"#{key}\"%\" or  a.org_name like \"%\"#{key}\"%\")"
                    + "</if> "
                    + "<if test =\"qOrgIds != null and qOrgIds.size() > 0 \">"
                    + "  and a.org_id  in "
                    + "    <foreach collection=\"qOrgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "               #{item}\n"
                    + "    </foreach>"
                    + "</if> "
                    + "<if test =\"isReview == 1\">"
                    + // 活动回顾 “已提交”、“检查通过”、“待复核”的活动
                    "    and a.status in (7,12,13,14)  "
                    + "    <if test =\"status != null  \">"
                    + "        <choose>"
                    + "              <when test=\"status == 13\">"
                    + // 检查通过
                    "                  and a.status in (13,14)  "
                    + "              </when>"
                    + "              <otherwise>"
                    + "                  and a.status = #{status} "
                    + "              </otherwise>"
                    + "        </choose>"
                    + "    </if> "
                    + "</if> "
                    + " <choose>"
                    + "        <when test=\"isH5 == 1\">"
                    + // 移动端活动管理查询
                    //  活动类型筛选
                    "                 <if test = \"typeIds != null \">"
                    + "                   and b.type_id in"
                    + "                   <foreach collection = \"typeIds\" item = \"item\" separator=\",\" open=\"(\" close=\")\">"
                    + "                       #{item}"
                    + "                   </foreach>"
                    + "              </if>"
                    + "              <if test =\"status != null  \">"
                    + "                   <choose>"
                    + "                         <when test=\"status == 3\">"
                    + // 活动待举办 “活动待举办”、“发起审批中”、“发起未通过”、“活动已取消”的活动
                    "                             and ((a.status=3 and a.start_time <![CDATA[ > ]]> now()) or  a.status in (1,2,9))"
                    + "                         </when>"
                    + "                         <when test=\"status == 4\">"
                    + // 待填写 “活动待填报”、“填报审批中”、“填报未通过”的活动
                    "                             and ( a.status in (5,6,10,11) or (  a.status=3 and a.start_time <![CDATA[ <= ]]> now())) "
                    + "                         </when>"
                    + "                         <when test=\"status == 7\">"
                    + // 已提交 “已提交”、“检查通过”、“待复核”的活动
                    "                             and a.status in (7,12,13,14)  "
                    + "                         </when>"
                    + "                         <when test=\"status == 8\">"
                    + // 已退回
                    "                             and a.status=8 "
                    + "                         </when>"
                    + "                         <otherwise>"
                    + "                             and a.status = #{status} "
                    + "                         </otherwise>"
                    + "                   </choose>"
                    + "              </if> "
                    + "        </when>"
                    + "        <when test=\"isH5 == 0\">"
                    // PC端活动管理查询
                    + "              <if test =\"status != null  \">"
                    + "                  <choose>"
                    + "                        <when test=\"status == 3\">"
                    + // 活动待举办
                    "                            and (a.status=3 and a.start_time <![CDATA[ > ]]> now())"
                    + "                        </when>"
                    + "                        <when test=\"status == 4\">"
                    + // 待填报结果
                    "                            and (a.status=3 and a.start_time <![CDATA[ <= ]]> now())"
                    + "                        </when>"
                    + "                        <when test=\"status == 5\">"
                    + // 填报审批中
                    "                            and a.status in (5,10)  "
                    + "                        </when>"
                    + "                        <when test=\"status == 6\">"
                    + // 填报未通过
                    "                            and a.status in (6,11)  "
                    + "                        </when>"
                    + "                        <when test=\"status == 13\">"
                    + // 检查通过
                    "                            and a.status in (13,14)  "
                    + "                        </when>"
                    + "                        <when test=\"status == 8\">"
                    + // 已退回
                    "                            and a.status=8 "
                    + "                        </when>"
                    + "                        <otherwise>"
                    + "                            and a.status = #{status} "
                    + "                        </otherwise>"
                    + "                  </choose>"
                    + "              </if> "
                    + "        </when>"
                    + "  </choose>"
                    + " GROUP BY meeting_id  "
                    + " ORDER BY  "
                    + " <if test =\"orderType == null\"> start_time DESC, meeting_id ASC </if> "
                    + " <if test =\"orderType != null and orderType == 2\"> start_time DESC, meeting_id DESC </if> "
                    + " <if test =\"orderType != null and orderType == 1\"> start_time ASC, meeting_id ASC </if> "
                    + " </script>")
    @Results({
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "szfMeetingId", column = "szf_meeting_id"),
            @Result(property = "types", column = "types"),
            @Result(property = "tags", column = "tags"),
            @Result(property = "name", column = "name"),
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "orgName", column = "org_name"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "submitTime", column = "submit_time"),
            @Result(property = "status", column = "status"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "category", column = "category"),
            @Result(property = "address", column = "address")
    })
    List<MeetingEntity> findAll(MeetingListForm meetingListForm);

    /**
     * 查询列表
     */
    @Select(" <script> "
            + " SELECT a.meeting_id,a.szf_meeting_id,a.types,e.tags,a.name,a.org_id,a.org_name,a.is_sign_in,a.sign_in_way,a.start_time,a.address,a.status,a.end_time,a.record_type,"
            + " r.submit_time as submit_time, "
            + " b.category,b.category_id FROM t_meeting a "
            + " LEFT JOIN t_meeting_result r ON a.meeting_id = r.meeting_id and r.is_del = 0  "
            + " LEFT JOIN t_meeting_type b ON a.meeting_id = b.meeting_id  "
            + " LEFT JOIN t_organization o ON a.org_id = o.organization_id"
            + " LEFT JOIN (select meeting_id,group_concat( agenda_title separator ' ')title from t_meeting_agenda group by meeting_id) d ON a.meeting_id = d.meeting_id "
            + " LEFT JOIN (select meeting_id,group_concat( tag_name separator ',') tags from t_meeting_tag group by meeting_id) e ON a.meeting_id = e.meeting_id "
            + " <if test =\" tagIds !=null and tagIds.size() > 0 \"> INNER JOIN (select meeting_id from t_meeting_tag where tag_id in "
            + " <foreach collection=\"tagIds\" index=\"index\" item=\"tid\" open=\"(\" separator=\",\" close=\")\">"
            + " #{tid} </foreach> GROUP BY meeting_id) f ON a.meeting_id=f.meeting_id </if>"
            + " where a.is_del = 0 "
            + " <if test =\"orgStatus !=null\">"
            + "     and o.status in"
            + "        <foreach collection=\"orgStatus\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                   #{item}\n"
            + "        </foreach>"
            + " </if>"
            + " <if test =\"submitStartTime != null\"> and r.submit_time <![CDATA[ >= ]]> #{submitStartTime} </if> "
            + " <if test =\"submitEndTime != null\"> and r.submit_time <![CDATA[ <= ]]> #{submitEndTime} </if> "
            + " <if test =\"meetingName != null and meetingName != '' \"> and a.name like \"%\"#{meetingName}\"%\"</if> "
            + " <if test =\"agenda != null and agenda != '' \"> and d.title like \"%\"#{agenda}\"%\"</if> "
            + " <if test =\"meetingClass != null\"> and b.category_id = #{meetingClass} </if> "
            + " <if test =\"name != null and name != '' \"> and a.name like \"%\"#{name}\"%\"</if> "
            + " <if test =\"startTime != null\"> and a.start_time <![CDATA[ >= ]]> #{startTime} </if> "
            + " <if test =\"endTime != null\"> and a.start_time <![CDATA[ <= ]]> #{endTime} </if> "
            + " <if test =\"typeIds != null and typeIds.size() > 0 \">"
            + "   and b.type_id in "
            + "        <foreach collection=\"typeIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                   #{item}\n"
            + "        </foreach>"
            + "</if> "
            + " <if test =\"key != null and key != '' \"> "
            + "    and (a.name like \"%\"#{key}\"%\" or  a.org_name like \"%\"#{key}\"%\")"
            + "</if> "
            + "<if test =\"qOrgIds != null and qOrgIds.size() > 0 \">"
            + "  and a.org_id  in "
            + "    <foreach collection=\"qOrgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "               #{item}\n"
            + "    </foreach>"
            + "</if> "
            + "<if test =\"filterMeetingIds != null and filterMeetingIds.size() > 0 \">"
            + "  and a.meeting_id  in "
            + "    <foreach collection=\"filterMeetingIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "               #{item}\n"
            + "    </foreach>"
            + "</if> "
            + "                <if test =\"recordType == 0  \">"
            + "                    and a.record_type = #{recordType} "
            + "                </if>"
            + "              <if test =\"status != null and  recordType == 0 \">"
            + "                  <choose>"
            + "                        <when test=\"status == -1\">"
            +                           // 未开始
            "                            and (a.status = 3 and a.start_time <![CDATA[ > ]]> now())"
            + "                        </when>"
            + "                        <when test=\"status == -2\">"
            +                           // 进行中
            "                            and (a.status = 3 and a.start_time <![CDATA[ <= ]]> now() and a.end_time <![CDATA[ > ]]> now() )"
            + "                        </when>"
            + "                        <when test=\"status == -3\">"
            +                           // 已结束
            "                            and a.status != 9 and (a.status = 3 and a.end_time <![CDATA[ < ]]> now()) "
            + "                        </when>"
            + "                        <when test=\"status == -4\">"
            +                           // 已取消
            "                            and a.status = 9  "
            + "                        </when>"
            + "                        <otherwise>"
            + "                            and 1 = 1 "
            + "                        </otherwise>"
            + "                  </choose>"
            + "              </if> "
            + "              <if test =\"status != null and  recordType == 1 \">"
            + "                  <choose>"
            + "                        <when test=\"status == -1\">"
            +                           // 待录入
            "                            and a.status = 3"
            + "                        </when>"
            + "                        <when test=\"status == -2\">"
            +                           // 已取消
            "                            and a.status = 9 "
            + "                        </when>"
            + "                        <when test=\"status == -3\">"
            +                           // 待复核
            "                            and a.status = 12 "
            + "                        </when>"
            + "                        <when test=\"status == -4\">"
            +                           // 检查通过
            "                            and a.status in (13,14)  "
            + "                        </when>"
            + "                        <when test=\"status == -5\">"
            +                           // 已退回
            "                            and a.status = 8  "
            + "                        </when>"
            + "                        <when test=\"status == -6\">"
            +                           // 已提交
            "                            and a.status = 7  "
            + "                        </when>"
            + "                        <when test=\"status == -7\">"
            +                           // 已提交
            "                            and a.status = 15  "
            + "                        </when>"
            + "                        <otherwise>"
            + "                            and 1 = 1 "
            + "                        </otherwise>"
            + "                  </choose>"
            + "              </if> "
            + " GROUP BY meeting_id  "
            + " ORDER BY start_time DESC ,meeting_id ASC  "
            + " </script>")
    @Results({
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "szfMeetingId", column = "szf_meeting_id"),
            @Result(property = "types", column = "types"),
            @Result(property = "tags", column = "tags"),
            @Result(property = "name", column = "name"),
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "orgName", column = "org_name"),
            @Result(property = "isSignIn", column = "is_sign_in"),
            @Result(property = "signInWay", column = "sign_in_way"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "submitTime", column = "submit_time"),
            @Result(property = "status", column = "status"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "category", column = "category"),
            @Result(property = "address", column = "address"),
            @Result(property = "recordType", column = "record_type")

    })
    List<MeetingEntity> findAllV2(MeetingListForm meetingListForm);


    /**
     * 查询列表-组织生活会-过滤已被关联的
     */
    @Select(" <script> "
            + " SELECT a.meeting_id,a.types,e.tags,a.name,a.org_id,a.org_name,a.is_sign_in,a.sign_in_way,a.start_time,a.address,a.status,a.end_time,a.record_type,"
            + " r.submit_time as submit_time, "
            + " b.category,b.category_id FROM t_meeting a "
            + " LEFT JOIN t_meeting_result r ON a.meeting_id = r.meeting_id and r.is_del = 0  "
            + " LEFT JOIN t_meeting_type b ON a.meeting_id = b.meeting_id  "
            + " LEFT JOIN t_organization o ON a.org_id = o.organization_id"
            + " LEFT JOIN (select meeting_id,group_concat( agenda_title separator ' ')title from t_meeting_agenda group by meeting_id) d ON a.meeting_id = d.meeting_id "
            + " LEFT JOIN (select meeting_id,group_concat( tag_name separator ',') tags from t_meeting_tag group by meeting_id) e ON a.meeting_id = e.meeting_id "
            + " <if test =\" tagIds !=null and tagIds.size() > 0 \"> INNER JOIN (select meeting_id from t_meeting_tag where tag_id in "
            + " <foreach collection=\"tagIds\" index=\"index\" item=\"tid\" open=\"(\" separator=\",\" close=\")\">"
            + " #{tid} </foreach> GROUP BY meeting_id) f ON a.meeting_id=f.meeting_id </if>"
            + " where a.is_del = 0 "
            + " <if test =\"meetingIds !=null\">"
            + " and a.meeting_id not in"
            + "        <foreach collection=\"meetingIds\"  item=\"meetingId\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                   #{meetingId}\n"
            + "        </foreach>"
            + " </if>"
            + " <if test =\"orgStatus !=null\">"
            + "     and o.status in"
            + "        <foreach collection=\"orgStatus\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                   #{item}\n"
            + "        </foreach>"
            + " </if>"
            + " <if test =\"submitStartTime != null\"> and r.submit_time <![CDATA[ >= ]]> #{submitStartTime} </if> "
            + " <if test =\"submitEndTime != null\"> and r.submit_time <![CDATA[ <= ]]> #{submitEndTime} </if> "
            + " <if test =\"meetingName != null and meetingName != '' \"> and a.name like \"%\"#{meetingName}\"%\"</if> "
            + " <if test =\"agenda != null and agenda != '' \"> and d.title like \"%\"#{agenda}\"%\"</if> "
            + " <if test =\"meetingClass != null\"> and b.category_id = #{meetingClass} </if> "
            + " <if test =\"name != null and name != '' \"> and a.name like \"%\"#{name}\"%\"</if> "
            + " <if test =\"startTime != null\"> and a.start_time <![CDATA[ >= ]]> #{startTime} </if> "
            + " <if test =\"endTime != null\"> and a.start_time <![CDATA[ <= ]]> #{endTime} </if> "
            + " <if test =\"typeIds != null and typeIds.size() > 0 \">"
            + "   and b.type_id in "
            + "        <foreach collection=\"typeIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                   #{item}\n"
            + "        </foreach>"
            + "</if> "
            + " <if test =\"key != null and key != '' \"> "
            + "    and (a.name like \"%\"#{key}\"%\" or  a.org_name like \"%\"#{key}\"%\")"
            + "</if> "
            + "<if test =\"qOrgIds != null and qOrgIds.size() > 0 \">"
            + "  and a.org_id  in "
            + "    <foreach collection=\"qOrgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "               #{item}\n"
            + "    </foreach>"
            + "</if> "
            + "<if test =\"filterMeetingIds != null and filterMeetingIds.size() > 0 \">"
            + "  and a.meeting_id  in "
            + "    <foreach collection=\"filterMeetingIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "               #{item}\n"
            + "    </foreach>"
            + "</if> "
            + "              <if test =\"status != null and  recordType == 0 \">"
            + "                  <choose>"
            + "                        <when test=\"status == -1\">"
            +                           // 未开始
            "                            and (a.status = 3 and a.start_time <![CDATA[ > ]]> now())"
            + "                        </when>"
            + "                        <when test=\"status == -2\">"
            +                           // 进行中
            "                            and (a.status = 3 and a.start_time <![CDATA[ <= ]]> now() and a.end_time <![CDATA[ > ]]> now() )"
            + "                        </when>"
            + "                        <when test=\"status == -3\">"
            +                           // 已结束
            "                            and a.status != 9 and (a.status = 3 and a.end_time <![CDATA[ < ]]> now()) "
            + "                        </when>"
            + "                        <when test=\"status == -4\">"
            +                           // 已取消
            "                            and a.status = 9  "
            + "                        </when>"
            + "                        <otherwise>"
            + "                            and 1 = 1 "
            + "                        </otherwise>"
            + "                  </choose>"
            + "              </if> "
            + "              <if test =\"status != null and  recordType == 1 \">"
            + "                  <choose>"
            + "                        <when test=\"status == -1\">"
            +                           // 待录入
            "                            and a.status = 3"
            + "                        </when>"
            + "                        <when test=\"status == -2\">"
            +                           // 已取消
            "                            and a.status = 9 "
            + "                        </when>"
            + "                        <when test=\"status == -3\">"
            +                           // 待复核
            "                            and a.status = 12 "
            + "                        </when>"
            + "                        <when test=\"status == -4\">"
            +                           // 检查通过
            "                            and a.status in (13,14)  "
            + "                        </when>"
            + "                        <when test=\"status == -5\">"
            +                           // 已退回
            "                            and a.status = 8  "
            + "                        </when>"
            + "                        <when test=\"status == -6\">"
            +                           // 已提交
            "                            and a.status = 7  "
            + "                        </when>"
            + "                        <when test=\"status == -7\">"
            +                           // 已提交
            "                            and a.status = 15  "
            + "                        </when>"
            + "                        <otherwise>"
            + "                            and 1 = 1 "
            + "                        </otherwise>"
            + "                  </choose>"
            + "              </if> "
            + " GROUP BY meeting_id  "
            + " ORDER BY start_time DESC ,meeting_id ASC  "
            + " </script>")
    @Results({
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "szfMeetingId", column = "szf_meeting_id"),
            @Result(property = "types", column = "types"),
            @Result(property = "tags", column = "tags"),
            @Result(property = "name", column = "name"),
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "orgName", column = "org_name"),
            @Result(property = "isSignIn", column = "is_sign_in"),
            @Result(property = "signInWay", column = "sign_in_way"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "submitTime", column = "submit_time"),
            @Result(property = "status", column = "status"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "category", column = "category"),
            @Result(property = "address", column = "address"),
            @Result(property = "recordType", column = "record_type")

    })
    List<MeetingEntity> findAllV3(MeetingListForm meetingListForm);


    /**
     * 查询列表-组织生活会-查询指定的id
     */
    @Select(" <script> "
            + " SELECT a.meeting_id,a.types,e.tags,a.name,a.org_id,a.org_name,a.is_sign_in,a.sign_in_way,a.start_time,a.address,a.status,a.end_time,a.record_type,"
            + " r.submit_time as submit_time, "
            + " b.category,b.category_id FROM t_meeting a "
            + " LEFT JOIN t_meeting_result r ON a.meeting_id = r.meeting_id and r.is_del = 0  "
            + " LEFT JOIN t_meeting_type b ON a.meeting_id = b.meeting_id  "
            + " LEFT JOIN t_organization o ON a.org_id = o.organization_id"
            + " LEFT JOIN (select meeting_id,group_concat( agenda_title separator ' ')title from t_meeting_agenda group by meeting_id) d ON a.meeting_id = d.meeting_id "
            + " LEFT JOIN (select meeting_id,group_concat( tag_name separator ',') tags from t_meeting_tag group by meeting_id) e ON a.meeting_id = e.meeting_id "
            + " <if test =\" tagIds !=null and tagIds.size() > 0 \"> INNER JOIN (select meeting_id from t_meeting_tag where tag_id in "
            + " <foreach collection=\"tagIds\" index=\"index\" item=\"tid\" open=\"(\" separator=\",\" close=\")\">"
            + " #{tid} </foreach> GROUP BY meeting_id) f ON a.meeting_id=f.meeting_id </if>"
            + " where a.is_del = 0 "
            + " <if test =\"meetingIds !=null\">"
            + " and a.meeting_id  in"
            + "        <foreach collection=\"meetingIds\"  item=\"meetingId\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                   #{meetingId}\n"
            + "        </foreach>"
            + " </if>"
            + " <if test =\"orgStatus !=null\">"
            + "     and o.status in"
            + "        <foreach collection=\"orgStatus\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                   #{item}\n"
            + "        </foreach>"
            + " </if>"
            + " <if test =\"submitStartTime != null\"> and r.submit_time <![CDATA[ >= ]]> #{submitStartTime} </if> "
            + " <if test =\"submitEndTime != null\"> and r.submit_time <![CDATA[ <= ]]> #{submitEndTime} </if> "
            + " <if test =\"meetingName != null and meetingName != '' \"> and a.name like \"%\"#{meetingName}\"%\"</if> "
            + " <if test =\"agenda != null and agenda != '' \"> and d.title like \"%\"#{agenda}\"%\"</if> "
            + " <if test =\"meetingClass != null\"> and b.category_id = #{meetingClass} </if> "
            + " <if test =\"name != null and name != '' \"> and a.name like \"%\"#{name}\"%\"</if> "
            + " <if test =\"startTime != null\"> and a.start_time <![CDATA[ >= ]]> #{startTime} </if> "
            + " <if test =\"endTime != null\"> and a.start_time <![CDATA[ <= ]]> #{endTime} </if> "
            + " <if test =\"typeIds != null and typeIds.size() > 0 \">"
            + "   and b.type_id in "
            + "        <foreach collection=\"typeIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                   #{item}\n"
            + "        </foreach>"
            + "</if> "
            + " <if test =\"key != null and key != '' \"> "
            + "    and (a.name like \"%\"#{key}\"%\" or  a.org_name like \"%\"#{key}\"%\")"
            + "</if> "
            + "<if test =\"qOrgIds != null and qOrgIds.size() > 0 \">"
            + "  and a.org_id  in "
            + "    <foreach collection=\"qOrgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "               #{item}\n"
            + "    </foreach>"
            + "</if> "
            + "<if test =\"filterMeetingIds != null and filterMeetingIds.size() > 0 \">"
            + "  and a.meeting_id  in "
            + "    <foreach collection=\"filterMeetingIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "               #{item}\n"
            + "    </foreach>"
            + "</if> "
            + "              <if test =\"status != null and  recordType == 0 \">"
            + "                  <choose>"
            + "                        <when test=\"status == -1\">"
            +                           // 未开始
            "                            and (a.status = 3 and a.start_time <![CDATA[ > ]]> now())"
            + "                        </when>"
            + "                        <when test=\"status == -2\">"
            +                           // 进行中
            "                            and (a.status = 3 and a.start_time <![CDATA[ <= ]]> now() and a.end_time <![CDATA[ > ]]> now() )"
            + "                        </when>"
            + "                        <when test=\"status == -3\">"
            +                           // 已结束
            "                            and a.status != 9 and (a.status = 3 and a.end_time <![CDATA[ < ]]> now()) "
            + "                        </when>"
            + "                        <when test=\"status == -4\">"
            +                           // 已取消
            "                            and a.status = 9  "
            + "                        </when>"
            + "                        <otherwise>"
            + "                            and 1 = 1 "
            + "                        </otherwise>"
            + "                  </choose>"
            + "              </if> "
            + "              <if test =\"status != null and  recordType == 1 \">"
            + "                  <choose>"
            + "                        <when test=\"status == -1\">"
            +                           // 待录入
            "                            and a.status = 3"
            + "                        </when>"
            + "                        <when test=\"status == -2\">"
            +                           // 已取消
            "                            and a.status = 9 "
            + "                        </when>"
            + "                        <when test=\"status == -3\">"
            +                           // 待复核
            "                            and a.status = 12 "
            + "                        </when>"
            + "                        <when test=\"status == -4\">"
            +                           // 检查通过
            "                            and a.status in (13,14)  "
            + "                        </when>"
            + "                        <when test=\"status == -5\">"
            +                           // 已退回
            "                            and a.status = 8  "
            + "                        </when>"
            + "                        <when test=\"status == -6\">"
            +                           // 已提交
            "                            and a.status = 7  "
            + "                        </when>"
            + "                        <when test=\"status == -7\">"
            +                           // 已提交
            "                            and a.status = 15  "
            + "                        </when>"
            + "                        <otherwise>"
            + "                            and 1 = 1 "
            + "                        </otherwise>"
            + "                  </choose>"
            + "              </if> "
            + " GROUP BY meeting_id  "
            + " ORDER BY start_time DESC ,meeting_id ASC  "
            + " </script>")
    @Results({
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "types", column = "types"),
            @Result(property = "tags", column = "tags"),
            @Result(property = "name", column = "name"),
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "orgName", column = "org_name"),
            @Result(property = "isSignIn", column = "is_sign_in"),
            @Result(property = "signInWay", column = "sign_in_way"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "submitTime", column = "submit_time"),
            @Result(property = "status", column = "status"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "category", column = "category"),
            @Result(property = "address", column = "address"),
            @Result(property = "recordType", column = "record_type")

    })
    List<MeetingEntity> findAllV4(MeetingListForm meetingListForm);



    /**
     * “已退回”、“活动待填报”、“填报审批中”、“填报未通过”的活动数量
     */
    @Select(" <script> "
            + " SELECT  a.org_id as orgId ,count(*) as num  FROM t_meeting a \n"
            + " where a.is_del = 0 "
            + " and ( a.status in (5,6,8,10,11) or (  a.status=3 and a.start_time <![CDATA[ <= ]]>  now()))"
            + " GROUP BY  a.org_id "
            + " </script>")
    List<MeetingH5CountForm> meetingH5Count();

    /**
     * 查询列表
     */
    @Select(" <script> " +
            "SELECT a.meeting_id,a.types,a.name,a.start_time,a.status FROM t_meeting a ,t_meeting_user b " +
            " <if test =\"typeIds != null and typeIds.size() > 0 \">" +
            " ,t_meeting_type c " +
            "</if> " +
            " where " +
            "   a.is_del = 0 " +
            "   and a.meeting_id = b.meeting_id and b.user_id = #{userId} and (b.tag=3 or  b.tag=4 )" +
            "  <if test =\"meetingId != null  \"> and a.meeting_id = #{meetingId} </if> " +
            "  <if test =\"regionId != null  \"> and a.region_id = #{regionId} </if> " +
            "  <if test =\"orgId != null  \"> and a.org_id = #{orgId} </if> " +
            "  <if test =\"name != null and name != '' \"> and a.name like \"%\"#{name}\"%\" </if> " +
            "  <if test =\"startTime != null\"> and a.start_time <![CDATA[ >= ]]> #{startTime} </if> " +
            "  <if test =\"endTime != null\"> and a.start_time <![CDATA[ <= ]]> #{endTime} </if> " +
            "  <if test =\"typeIds != null and typeIds.size() > 0 \">" +
            "       and  a.meeting_id = c.meeting_id  " +
            "       and c.type_id in " +
            "           <foreach collection=\"typeIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "                      #{item}\n" +
            "           </foreach>" +
            "  </if> " +
            "  <choose>" +
            "      <when test=\"status != null and status == 1\">" + // 1：待举办;展示状态为“活动待举办”、“已取消”的活动
            "          and  ( a.status=9 or (a.status=3 and a.start_time <![CDATA[ > ]]> now()))" +
            "      </when>" +
            "      <when test=\"status != null and status == 2\">" + // 2：进行中 展示状态为“待填报结果”、“填报审批中”、“填报未通过”的活动
            "          and ( a.status in (5,6,10,11) or (  a.status=3 and a.start_time <![CDATA[ <= ]]> now())) " +
            "      </when>" +
            "      <when test=\"status != null and status == 3\">" + // 3：结束 展示状态为“已提交”、“退回”、“检查通过”、“待复核”的活动
            "          and a.status in (7,8,12,13,14)  " +
            "      </when>" +
            "  </choose>" +
            " GROUP BY a.meeting_id " +
            " ORDER BY a.start_time ASC ,a.meeting_id ASC " +
            " </script>")
    @Results({
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "types", column = "types"),
            @Result(property = "name", column = "name"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "meetingStatus", column = "status")
    })
    List<MyMeetingResultListForm> findMyMeetingAll(MyMeetingListForm meetingListForm);

    /**
     * 查询包含指定任务且状态为已提交的活动id
     * "活动状态 1：发起审批中、2：发起未通过、3：活动待举办（待填报结果）、5：填报审查中（第一次）、
     * 6：填报未通过（第一次）、7：已提交、8：退回、9：活动已取消、10：填报审查中（第一次之后）、
     * 11：填报未通过（第一次之后）12：待复核（第二次提交并且审批通过）13：通过（第一次考核就通过）14：通过（第一次考核被退回后的通过）")
     *
     * @param topicIds 任务id
     * @return
     */
    @Select(" <script> " +
            " SELECT m.meeting_id FROM t_meeting m" +
            " LEFT JOIN t_meeting_topic mt ON m.meeting_id = mt.meeting_id" +
            " WHERE m.is_del = 0 and m.status in (7,8,10,11,12,13,14) " +
            " <if test =\"orgId != null  \"> and m.org_id = #{orgId} </if> " +
            " <if test =\"topicIds != null and topicIds.size() > 0 \">" +
            "  and  mt.topic_id in " +
            "        <foreach collection=\"topicIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "                   #{item}\n" +
            "        </foreach>" +
            " </if> " +
            " </script>")
    Set<Long> findByOrgIdAndTopicIds(
            @Param("topicIds") List<Long> topicIds, @Param("orgId") Long orgId);

    @Select("<script> "
            + "  SELECT b.user_id as userId,count(DISTINCT a.meeting_id) num FROM t_meeting a ,t_meeting_user b "
            + "  where a.is_del = 0 and a.meeting_id = b.meeting_id and (b.tag=3 or  b.tag=4 )"
            + "  and a.region_id=#{regionId}"
            + "  and  ( a.status=9 or (a.status=3 and a.start_time <![CDATA[ > ]]>  now()))"
            + " <if test =\"userIdSet != null and userIdSet.size() > 0 \">"
            + "    and b.user_id in "
            + "    <foreach collection=\"userIdSet\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "        #{item}"
            + "    </foreach>"
            + " </if> "
            + "  GROUP BY b.user_id"
            + " </script>")
    List<WaitDoMeetingCountForm> waitDoMeetingCount(@Param("regionId") Long regionId, @Param("userIdSet") Set<Long> userIdSet);

    /**
     * 活动回顾报表
     * @param meetingListForm
     * @return
     */
    @Select("<script>" +
            "SELECT "
            + " a.meeting_id,a.types,a.name,a.address,a.start_time,a.content,a.end_time,a.sign_in_way,a.org_name,a.status,a.theory_learn,"
            + " t.label, "
            + " r.submit_time as submit_time,"
            + " b.category"
            + " FROM t_meeting a "
            + " LEFT JOIN t_meeting_result r ON a.meeting_id = r.meeting_id and r.is_del = 0  "
            + " LEFT JOIN t_meeting_type b ON a.meeting_id = b.meeting_id  "
            + " LEFT JOIN t_organization o ON a.org_id = o.organization_id "
            + " LEFT JOIN (select meeting_id,group_concat( agenda_title separator ' ') title from t_meeting_agenda group by meeting_id) d ON a.meeting_id = d.meeting_id "
            + " LEFT JOIN (select meeting_id,group_concat(distinct tag_name separator '，') label,concat(',',group_concat(distinct tag_id separator ','),',') tag from t_meeting_tag group by meeting_id) t ON a.meeting_id = t.meeting_id "
            + " where a.is_del = 0 "
            + " <if test =\"orgStatus !=null\">"
            + "     and o.status in"
            + "        <foreach collection=\"orgStatus\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                   #{item}\n"
            + "        </foreach>"
            + " </if>"
            + " <if test =\"submitStartTime != null\"> and r.submit_time <![CDATA[ >= ]]> #{submitStartTime} </if> "
            + " <if test =\"submitEndTime != null\"> and r.submit_time <![CDATA[ <= ]]> #{submitEndTime} </if> "
            + " <if test =\"meetingName != null and meetingName != '' \"> and a.name like \"%\"#{meetingName}\"%\"</if> "
            + " <if test =\"agenda != null and agenda != '' \"> and d.title like \"%\"#{agenda}\"%\"</if> "
            + " <if test =\"keyWord != null and keyWord != '' \"> and (a.name like \"%\"#{keyWord}\"%\" or d.title like \"%\"#{keyWord}\"%\")</if> "
            + " <if test =\"tagIds != null \"> "
            + "        <foreach collection=\"tagIds\" index=\"index\" item=\"item\">\n"
            + "                   and t.tag like concat('%,',#{item},',%')\n"
            + "        </foreach>"
            + "</if> "
            + " <if test =\"meetingClass != null\"> and b.category_id = #{meetingClass} </if> "
            + " <if test =\"name != null and name != '' \"> and a.name like \"%\"#{name}\"%\"</if> "
            + " <if test =\"startTime != null\"> and a.start_time <![CDATA[ >= ]]> #{startTime} </if> "
            + " <if test =\"endTime != null\"> and a.start_time <![CDATA[ <= ]]> #{endTime} </if> "
            + " <if test =\"typeIds != null and typeIds.size() > 0 \">"
            + "   and b.type_id in "
            + "        <foreach collection=\"typeIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "                   #{item}\n"
            + "        </foreach>"
            + "</if> "
            + " <if test =\"key != null and key != '' \"> "
            + "    and (a.name like \"%\"#{key}\"%\" or  a.org_name like \"%\"#{key}\"%\")"
            + "</if> "
            + "<if test =\"qOrgIds != null and qOrgIds.size() > 0 \">"
            + "  and a.org_id  in "
            + "    <foreach collection=\"qOrgIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "               #{item}\n"
            + "    </foreach>"
            + "</if> "
            + "<if test =\"isReview == 1\">"
            + // 活动回顾 “已提交”、“检查通过”、“待复核”的活动
            "    and a.status in (7,12,13,14)  "
            + "    <if test =\"status != null  \">"
            + "        <choose>"
            + "              <when test=\"status == 13\">"
            + // 检查通过
            "                  and a.status in (13,14)  "
            + "              </when>"
            + "              <otherwise>"
            + "                  and a.status = #{status} "
            + "              </otherwise>"
            + "        </choose>"
            + "    </if> "
            + "</if> "
            + " <choose>"
            + "        <when test=\"isH5 == 1\">"
            + // 移动端活动管理查询
            //  活动类型筛选
            "                 <if test = \"typeIds != null \">"
            + "                   and b.type_id in"
            + "                   <foreach collection = \"typeIds\" item = \"item\" separator=\",\" open=\"(\" close=\")\">"
            + "                       #{item}"
            + "                   </foreach>"
            + "              </if>"
            + "              <if test =\"status != null  \">"
            + "                   <choose>"
            + "                         <when test=\"status == 3\">"
            + // 活动待举办 “活动待举办”、“发起审批中”、“发起未通过”、“活动已取消”的活动
            "                             and ((a.status=3 and a.start_time <![CDATA[ > ]]> now()) or  a.status in (1,2,9))"
            + "                         </when>"
            + "                         <when test=\"status == 4\">"
            + // 待填写 “活动待填报”、“填报审批中”、“填报未通过”的活动
            "                             and ( a.status in (5,6,10,11) or (  a.status=3 and a.start_time <![CDATA[ <= ]]> now())) "
            + "                         </when>"
            + "                         <when test=\"status == 7\">"
            + // 已提交 “已提交”、“检查通过”、“待复核”的活动
            "                             and a.status in (7,12,13,14)  "
            + "                         </when>"
            + "                         <when test=\"status == 8\">"
            + // 已退回
            "                             and a.status=8 "
            + "                         </when>"
            + "                         <otherwise>"
            + "                             and a.status = #{status} "
            + "                         </otherwise>"
            + "                   </choose>"
            + "              </if> "
            + "        </when>"
            + "        <when test=\"isH5 == 0\">"
            // PC端活动管理查询
            + "              <if test =\"status != null  \">"
            + "                  <choose>"
            + "                        <when test=\"status == 3\">"
            + // 活动待举办
            "                            and (a.status=3 and a.start_time <![CDATA[ > ]]> now())"
            + "                        </when>"
            + "                        <when test=\"status == 4\">"
            + // 待填报结果
            "                            and (a.status=3 and a.start_time <![CDATA[ <= ]]> now())"
            + "                        </when>"
            + "                        <when test=\"status == 5\">"
            + // 填报审批中
            "                            and a.status in (5,10)  "
            + "                        </when>"
            + "                        <when test=\"status == 6\">"
            + // 填报未通过
            "                            and a.status in (6,11)  "
            + "                        </when>"
            + "                        <when test=\"status == 13\">"
            + // 检查通过
            "                            and a.status in (13,14)  "
            + "                        </when>"
            + "                        <when test=\"status == 8\">"
            + // 已退回
            "                            and a.status=8 "
            + "                        </when>"
            + "                        <otherwise>"
            + "                            and a.status = #{status} "
            + "                        </otherwise>"
            + "                  </choose>"
            + "              </if> "
            + "        </when>"
            + "  </choose>"
            + " GROUP BY meeting_id  "
            + " ORDER BY start_time DESC ,meeting_id ASC  "
            + "</script>")
    @Results({
            @Result(property = "name", column = "name"),
            @Result(property = "category", column = "category"),
            @Result(property = "types", column = "types"),
            @Result(property = "orgName", column = "org_name"),
            @Result(property = "address", column = "address"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "theoryLearn", column = "theory_learn"),
            @Result(property = "content", column = "content"),
            @Result(property = "signInWay", column = "sign_in_way"),
            @Result(property = "submitTime", column = "submit_time"),
            @Result(property = "status", column = "status"),

            //处理one to one 记录人
            @Result(property = "recordUser", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingUserMapper.findRecordUserByMeetingId")),
            //处理one to one 主持人
            @Result(property = "hostUser", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingUserMapper.findHostUserByMeetingId")),
            //处理one to many 参会人员
            @Result(property = "participantUsers", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingUserMapper.findParticipantUsersByMeetingId")),
            //处理one to many 列席人员
            @Result(property = "attendUsers", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingUserMapper.findAttendUsersByMeetingId")),
            //处理one to many 活动议程
            @Result(property = "agenda", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingAgendaMapper.findByMeetingId")),
            //处理one to many 活动议程
            @Result(property = "signList", column = "meeting_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingUserMapper.signList2"))
    })
    List<MeetingReportDto> meetingReport(MeetingListForm meetingListForm);


    /**
     * 删除钉钉日程的相关信息
     * @param meetingId
     */
    @Update("update t_meeting set ding_event_id=null,ding_event_sync=null where meeting_id= #{meetingId}")
    void delDingDingEventInfo(@Param("meetingId") Long meetingId);

    @Select("select t2.sign_status  from t_meeting t1\n" +
            "\tjoin t_meeting_user t2 on t1.meeting_id=t2.meeting_id and t2.user_id=#{userId}\n" +
            "\twhere t1.is_del=0 and date_format(t1.start_time,'%Y')=#{year} and t1.status in(7,12,13,14)\n" +
            "\tand t2.tag=3")
    List<Integer> statisticalUserJoinTimes(@Param("year") int year, @Param("userId") Long userId);


    @Select(" select count(distinct t1.meeting_id) from t_meeting t1\n" +
            " join t_meeting_type t2 on t1.meeting_id=t2.meeting_id\t\n" +
            " where t1.is_del=0 and date_format(t1.start_time,'%Y')=#{year} and t1.status in(7,12,13,14)\n" +
            " and org_id=#{oid}" )
    Integer statisticalOrgJoinTimes(Integer year, Long oid);




    @Select("select t2.type_id as typeId,if(t1.status in (7,12,13,14),1,2) as status from t_meeting t1 " +
            "join t_meeting_type t2 on t1.meeting_id=t2.meeting_id " +
            "and t1.type_id in(1,2,3,4,5) " +
            "group by t2.type_id and t1.org_id =#{oid}\n")
    List<MeetingTaskEntity> statiscalOrgRate(@Param("oid") Long oid);


    @Select("select distinct t1.meeting_id as meetingId,t1.name,t1.start_time as startTime,t1.end_time as endTime,t1.types," +
            "if(now()>=t1.start_time,16,15) as status from t_meeting t1\n" +
            "join t_meeting_user t2 on t1.meeting_id=t2.meeting_id and t1.region_id=#{regionId} " +
            "and t2.user_id=#{userId} and t1.status in(3) and t1.is_del=0\n" +
            "and t1.meeting_id not in(select meeting_id from t_meeting_leave t3  " +
            "where user_id=#{userId} and t3.del_status=0 and t3.status  in(0,1,2,4,6)\n" +
            " ) order by t1.start_time desc")
    List<LeaveMeetingForm> queryCanLeaveMeeting(@Param("regionId") long regionId, @Param("userId") long userId);
}