package com.goodsogood.ows.mapper;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2018/10/30 13:31
 */
@Log4j2
public class MeetingLeaveProvider {

    public String getLeaveListColumn() {
        return "m.meeting_id as meetingId,u.user_name userName,\n" +
                "m. NAME NAME,\n" +
                "m.types types,\n" +
                "m.start_time startTime,\n" +
                "l.`status` STATUS,\n" +
                "l.meeting_leave_id meetingLeaveId,\n" +
                "l.create_time meetingLeaveTime";
    }

    public String getLeaveListSql(Map<String, Object> params) {
        long userId = MapUtils.getLongValue(params, "userId");
        int type = MapUtils.getIntValue(params, "type");
        String types = MapUtils.getString(params, "types");//会议类型
        String startTime = MapUtils.getString(params, "startTime");
        String endTime = MapUtils.getString(params, "endTime");
        String keyWord = MapUtils.getString(params, "keyWord");
        types = StringUtils.removeStart(types, ",");
        types = StringUtils.removeEnd(types, ",");
        String finalTypes = types;
        return new SQL() {{
            SELECT(getLeaveListColumn());
            FROM("t_meeting_leave l")
                    .LEFT_OUTER_JOIN("t_meeting m ON l.meeting_id = m.meeting_id")
                    .LEFT_OUTER_JOIN("t_meeting_user u ON l.meeting_user_id = u.meeting_user_id");
            if (StringUtils.isNotBlank(finalTypes)) {
                LEFT_OUTER_JOIN("t_meeting_type t ON t.meeting_id = l.meeting_id");
                WHERE("t.type_id in (" + finalTypes + ")");
            }
            WHERE("m.region_id = #{regionId}");
            if (type == 1) {
                WHERE("l.status = 1");
            } else if (type == 2) {
                WHERE("l.status in(2,3,4)");
            }
            WHERE("l.check_user_id = " + userId);
            if (StringUtils.isNotBlank(keyWord)) {
                WHERE("u.user_name like CONCAT('%',#{keyWord},'%')");
            }
            if (StringUtils.isNotBlank(startTime)) {
                WHERE("m.start_time >='" + startTime + " 00:00:00'");
            }
            if (StringUtils.isNotBlank(endTime)) {
                WHERE("m.start_time <='" + endTime + " 23:59:59'");
            }
            ORDER_BY("l.create_time asc");
        }}.toString();
    }

    public String getDetailColumn() {
        return "u.user_name userName,\n" +
                "u.phone phone,\n" +
                "m.`name` NAME,\n" +
                "m.types types,\n" +
                "m.start_time startTime,\n" +
                "m.meeting_id meetingId,\n" +
                "l.`status` STATUS,\n" +
                "l.type type,\n" +
                "l.reason reason,\n" +
                "l.meeting_leave_id meetingLeaveId";
    }

    public String getDetailSql(@Param("meetingLeaveId") long meetingLeaveId) {
        return new SQL() {{
            SELECT(getDetailColumn());
            FROM("t_meeting_leave l")
                    .LEFT_OUTER_JOIN("t_meeting m ON l.meeting_id = m.meeting_id")
                    .LEFT_OUTER_JOIN("t_meeting_user u ON l.meeting_user_id = u.meeting_user_id");
            WHERE("l.meeting_leave_id = " + meetingLeaveId);
        }}.toString();
    }

}
