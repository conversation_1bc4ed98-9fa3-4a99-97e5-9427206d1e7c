package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingOrgScoreDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: tc
 * @Description  组织生活组织积分记录流水表
 */
@Repository
@Mapper
public interface MeetingOrgScoreDetailMapper extends MyMapper<MeetingOrgScoreDetailEntity> {

    /**
     * 验证组织是否有加分记录
     * @param orgIdList 组织编号集合
     * @param scoreType 1.党员考勤积分 2.讲课人积分 3.党小组长开展积分  4.支委会成员每年基础分 5.党小组长每年基础分
     * @param start 周期开始
     * @param end 周期结束
     * @return MeetingOrgScoreDetailEntity
     */
    @Select("<script> select org_id orgId,sum(score) score from t_meeting_org_score_detail where score_type = #{scoreType} and meeting_type = #{meetingType}" +
            " and score_sign &gt;= #{start} and score_sign &lt;= #{end} <if test=\"isRollBack !=null and isRollBack != 0\"> and meeting_id = #{meetingId} </if>" +
            " and org_id in <foreach collection = \"orgIdList\" item = \"orgId\" separator=\",\" open=\"(\" close=\")\"> #{orgId} </foreach> group by org_id </script>")
    List<MeetingOrgScoreDetailEntity> findOrgScoreCycle(@Param("orgIdList") List<Long> orgIdList,@Param("meetingType") Integer meetingType,
                                                        @Param("scoreType") Integer scoreType,
                                                        @Param("start") Integer start, @Param("end") Integer end,
                                                        @Param("isRollBack") Integer isRollBack,@Param("meetingId") Long meetingId);

}