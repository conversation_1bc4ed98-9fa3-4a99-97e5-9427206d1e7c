package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.SbwTypeEntity;
import com.goodsogood.ows.model.vo.SbwTaskTypeForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface SbwTaskTypeMapper extends MyMapper<SbwTypeEntity>{

    @Select("select type_id typeId,type_name typeName from t_meeting_sbw_type")
    List<SbwTaskTypeForm> list();
}
