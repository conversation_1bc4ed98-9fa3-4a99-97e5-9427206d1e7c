package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingLifeTalkEntity;
import com.goodsogood.ows.model.vo.LifeTalkForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface LifeTalkMapper extends MyMapper<MeetingLifeTalkEntity>{

//    /**
//     * 民主生活会-谈心谈话列表查询
//     * @param lifeId
//     * @param talkType
//     * @param step
//     * @return
//     */
//    @Select("select " +
//            "lt.life_talk_id lifeTalkId," +
//            "lt.life_id lifeId," +
//            "lt.talk_id talkId," +
//            "lt.talk_type talkType," +
//            "mt.begin_time beginTime," +
//            "mt.end_time endTime," +
//            "mt.is_submit isSubmit," +
//            "mlt.username talkUser," +
//            "mlto.username toTalkUser " +
//            "from t_meeting_life_talk lt " +
//            "inner join t_meeting_talk mt on lt.talk_id = mt.talk_id " +
//            "inner join (select talk_id,group_concat(username) username from t_meeting_talk_link where type = 1 group by talk_id) mlt on lt.talk_id = mlt.talk_id " +
//            "inner join (select talk_id,group_concat(username) username from t_meeting_talk_link where type = 2 group by talk_id) mlto on lt.talk_id = mlto.talk_id " +
//            "where lt.life_id = #{lifeId} and lt.talk_type = #{talkType} and lt.step = #{step}")
//    List<LifeTalkForm> lifeTalk(@Param("lifeId")Long lifeId,@Param("talkType")Integer talkType,@Param("step") Integer step);

//    /**
//     * 批量删除
//     * @param talkIds
//     * @return
//     */
//    @Delete("<script>" +
//            "delete from t_meeting_life_talk where step = 2 and talk_id in " +
//            "<foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
//            "#{item}" +
//            "</foreach>" +
//            "</script>")
//    Integer multiDel(@Param("ids") List<Long> talkIds);
}
