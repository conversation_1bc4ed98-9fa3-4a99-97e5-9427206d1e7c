package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingOrgGroupEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-09-19 10:57
 **/
@Repository
@Mapper
public interface MeetingOrgGroupMapper extends tk.mybatis.mapper.common.Mapper<MeetingOrgGroupEntity>, MeetingOrgGroupInsertListMapper<MeetingOrgGroupEntity> {
    @Select(" SELECT " +
            "   a.meeting_org_group_id," +
            "   a.meeting_id," +
            "   a.org_group_id," +
            "   a.org_group_name," +
            "   a.org_group_create_date" +
            " FROM " +
            "   t_meeting_org_group a " +
            " WHERE status=1 and a.meeting_id = #{meetingId} ORDER BY org_group_create_date ASC,meeting_org_group_id ASC ")
    @Results({
            @Result(property = "meetingOrgGroupId", column = "meeting_org_group_id"),
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "orgGroupId", column = "org_group_id"),
            @Result(property = "orgGroupName", column = "org_group_name"),
            @Result(property = "orgGroupCreateDate", column = "org_group_create_date")
    })
    List<MeetingOrgGroupEntity> findByMeetingId(@Param("meetingId") long meetingId);
}