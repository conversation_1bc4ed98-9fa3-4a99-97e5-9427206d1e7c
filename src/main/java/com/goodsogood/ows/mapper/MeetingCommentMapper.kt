package com.goodsogood.ows.mapper

import com.goodsogood.ows.model.db.*
import com.goodsogood.ows.model.vo.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Repository

@Repository
@Mapper
interface MeetingCommentMapper : MyMapper<MeetingCommentEntity> {

    /**
     * 查询民主评议是否可以编辑
     * @return 1-可以编辑 0-不可以编辑
     */
    @Select("<script>" +
            " SELECT CASE WHEN `status` = 0 THEN -1 WHEN `status` in " +
            "  <foreach collection=\"statusList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "       #{item}" +
            "  </foreach>" +
            " THEN 0 ELSE 1 END as isEdit FROM t_meeting_comment where comment_id = #{commentId}" +
            "</script>")
    fun isEdit(@Param("commentId") commentId: Long?,
               @Param("statusList") statusListL: List<Int>): Int?

    @Select("<script>" +
            "SELECT\n" +
            "mcm.comment_member_id\n" +
            "FROM\n" +
            "t_meeting_comment mc\n" +
            "LEFT JOIN t_meeting_comment_member mcm ON mc.comment_id = mcm.comment_id\n" +
            "WHERE mc.`status` in (0,1,3,5)\n" +
            "AND mcm.user_id = #{userId}" +
            "</script>")
    fun editableCommentMember(@Param("userId") userId: Long): Long?

    @Select("<script>" +
            "SELECT\n" +
            "mcm.user_id as userId, mc.year, mcm.create_time as createTime\n" +
            "FROM\n" +
            "t_meeting_comment mc\n" +
            "LEFT JOIN t_meeting_comment_member mcm ON mcm.comment_id = mc.comment_id\n" +
            "LEFT JOIN t_meeting_comment_member_complex mcmc on mcmc.comment_member_id = mcm.comment_member_id\n" +
            "WHERE mc.`status` = 6\n" +
            "AND mcmc.complex_rating = #{rating}\n" +
            "<if test =\"userId != null\"> AND mcm.user_id = #{userId}\n</if>" +
            "</script>")
    fun selectCommentMember(@Param("userId") userId: Long? = null,
                            @Param("rating") rating: Int) : MutableList<MeetingCommentMemberEntity>

    @Select("<script>" +
            "SELECT\n" +
            " mcm.user_name as username,\n" +
            " mcm.self_rating as selfRating,\n" +
            " count(case when mcma.appraisal_rating = 1 then 1 end) as 'excellent',\n" +
            " count(case when mcma.appraisal_rating = 2 then 1 end) as 'qualified',\n" +
            " count(case when mcma.appraisal_rating = 3 then 1 end) as 'basic',\n" +
            " count(case when mcma.appraisal_rating = 4 then 1 end) as 'unqualified'\n" +
            "FROM\n" +
            " t_meeting_comment mc\n" +
            " LEFT JOIN t_meeting_comment_member mcm ON mcm.comment_id = mc.comment_id\n" +
            " LEFT JOIN t_meeting_comment_member_appraisal mcma ON mcma.comment_member_id = mcm.comment_member_id\n" +
            "WHERE\n" +
            " mc.comment_id = #{commentId}\n" +
            "AND mcm.political_type = 1\n" +
            "GROUP BY\n" +
            " mcm.comment_member_id" +
            "</script>")
    fun selectCountMember(@Param("commentId") commentId: Long) : MutableList<UserDataForm>
}

@Repository
@Mapper
interface MeetingCommentApproveMapper : MyMapper<MeetingCommentApproveEntity>

@Repository
@Mapper
interface MeetingCommentMemberMapper : MyMapper<MeetingCommentMemberEntity> {

    @Select("<script>" +
            "SELECT\n" +
            "  mcm.comment_member_id as commentMemberId,\n" +
            "  mcm.user_name as userName,\n" +
            "  mcm.phone_secret as phone,\n" +
            "  IF(mcm.is_draft=1, mcm.self_rating, null) as selfRating,\n" +
            "  (select count(0) from t_meeting_comment_member_appraisal where comment_member_id = mcm.comment_member_id and is_draft=1 ) as existAppraisal," +
            "  mcmc.complex_rating as complexRating\n" +
            " FROM t_meeting_comment_member mcm\n" +
            " LEFT JOIN t_meeting_comment_member_complex mcmc ON mcmc.comment_member_id = mcm.comment_member_id \n" +
            " WHERE comment_id = #{commentId}\n" +
            "  AND mcm.political_type = 1" +
            "  <if test =\"name != null\"> AND mcm.user_name like CONCAT('%', #{name},'%')\n</if>" +
            "  <if test =\"phone != null\"> AND mcm.phone = #{phone}\n</if>" +
            "  <if test =\"selfRating != null\"> AND mcm.self_rating = #{selfRating}\n</if>" +
            "  <if test =\"complexRating != null\"> AND mcmc.complex_rating = #{complexRating}\n</if>" +
            "  <if test =\"userIdList != null and userIdList.size() > 0 \"> AND mcm.user_id in " +
            "  <foreach collection=\"userIdList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "       #{item}" +
            "  </foreach>" +
            "  order by mcm.user_id" +
            "  </if>" +
            "</script>")
    fun getCommentMemberList(@Param("commentId") commentId: Long,
                             @Param("name") name: String? = null,
                             @Param("phone") phone: String? = null,
                             @Param("selfRating") selfRating: Int? = null,
                             @Param("complexRating") complexRating: Int? = null,
                             @Param("userIdList") userIdList: MutableList<Long>? = null): MutableList<CommentMemberResultForm>

    @Select("<script>" +
            "SELECT #{commentMemberId} as commentMemberId, mcm.user_id as userId, mcm.user_name as userName, mcm.phone as phone\n" +
            " FROM t_meeting_comment mc\n" +
            " LEFT JOIN t_meeting_comment_member mcm ON mcm.comment_id = mc.comment_id \n" +
            " where mc.`status` <![CDATA[ > ]]> 0 AND mc.comment_id = #{commentId} AND mcm.political_type = 1\n" +
            " and mcm.comment_member_id <![CDATA[ <> ]]> #{commentMemberId}" +
            " order by mcm.user_id" +
            "</script>")
    fun getCommentMember(@Param("commentId") commentId: Long,
                         @Param("commentMemberId") commentMemberId: Long?): MutableList<CommentMemberAppraisalResultForm>

    @Select("<script>" +
            " SELECT mcm.comment_member_id as commentMemberId\n" +
            " FROM t_meeting_comment mc\n" +
            " LEFT JOIN t_meeting_comment_member mcm ON mcm.comment_id = mc.comment_id\n" +
            " WHERE mc.comment_id = #{commentId}\n" +
            "  <if test =\"commentMemberId != null\"> AND mcm.comment_member_id = #{commentMemberId}\n </if>" +
            "  AND mcm.user_id = #{userId}\n" +
            "  AND mcm.political_type = 1\n" +
            "</script>")
    fun getCommentMemberByUserId(@Param("commentId") commentId: Long? = null,
                                 @Param("commentMemberId") commentMemberId: Long? = null,
                                 @Param("userId") userId: Long): Long? = null

    @Select("<script>" +
            "SELECT\n" +
            " SUM(IF( self_rating = 1, 1, 0 )) AS excellent,\n" +
            " SUM(IF( self_rating = 2, 1, 0 )) AS qualified,\n" +
            " SUM(IF( self_rating = 3, 1, 0 )) AS basicQualified,\n" +
            " SUM(IF( self_rating = 4, 1, 0 )) AS unqualified,\n" +
            " SUM(IF( self_rating IS NULL || self_rating not in (1,2,3,4), 1, 0 )) AS other \n" +
            "FROM t_meeting_comment_member \n" +
            "WHERE\n" +
            " comment_id = #{commentId} \n" +
            " AND political_type = 1" +
            "</script>")
    fun selfStatistical(@Param("commentId") commentId: Long): CommentStatisticalDataForm?
}

@Repository
@Mapper
interface MeetingCommentMemberAppraisalMapper : MyMapper<MeetingCommentMemberAppraisalEntity> {

    /**
     * 获取自己评价别人的列表
     */
    @Select("<script>" +
            "SELECT mcm.comment_member_id as commentMemberId,\n" +
            " mcm.user_id as userId, \n" +
            " mcm.user_name as userName, \n" +
            " mcm.phone_secret as phone, \n" +
            " mcma.appraisal_rating as rating, \n" +
            " mcma.comment_member_appraisal_id as commentMemberAppraisalId\n" +
            " FROM t_meeting_comment mc\n" +
            " LEFT JOIN t_meeting_comment_member mcm ON mcm.comment_id = mc.comment_id\n" +
            " LEFT JOIN t_meeting_comment_member_appraisal mcma ON mcma.comment_member_id = mcm.comment_member_id AND mcma.user_id = #{userId} <if test =\"type == 2\"> AND mcma.is_draft = 1 </if>\n" +
            "WHERE mc.`status` <![CDATA[ > ]]> 0\n" +
            "  AND mc.comment_id = #{commentId} \n" +
            "  AND mcm.user_id <![CDATA[ <> ]]> #{userId} \n" +
            "  AND mcm.political_type = 1\n" +
            "  order by mcm.user_id\n" +
            "</script>")
    fun getMemberAppraisalListBySelf(@Param("commentId") commentId: Long,
                               @Param("type") type: Int,
                               @Param("userId") userId: Long) : MutableList<CommentMemberAppraisalResultForm>

    /**
     * 获取别人评价的列表
     */
    @Select("<script>" +
            "SELECT mcm.comment_member_id as commentMemberId, mcma.user_id as userId, mcma.appraisal_rating as rating, " +
            " mcma.comment_member_appraisal_id as commentMemberAppraisalId\n" +
            " FROM t_meeting_comment mc\n" +
            " LEFT JOIN t_meeting_comment_member mcm ON mcm.comment_id = mc.comment_id\n" +
            " LEFT JOIN t_meeting_comment_member_appraisal mcma ON mcma.comment_member_id = mcm.comment_member_id <if test =\"type == 2\"> AND mcma.is_draft = 1 </if>\n" +
            "WHERE mc.`status` <![CDATA[ > ]]> 0\n" +
            "  AND mc.comment_id = #{commentId} \n" +
            "<if test =\"commentMemberId != null\">" +
            "  AND mcm.comment_member_id = #{commentMemberId}\n" +
            "</if>" +
            "  AND mcm.political_type = 1\n" +
            "  order by mcm.user_id\n" +
            "</script>")
    fun getMemberAppraisalList(@Param("commentId") commentId: Long,
                               @Param("commentMemberId") commentMemberId: Long?,
                               @Param("type") type: Int) : MutableList<CommentMemberAppraisalResultForm>

    @Select("<script>" +
            "SELECT\n" +
            " IFNULL(SUM(IF( mcma.appraisal_rating = 1, 1, 0 )),0) AS excellent,\n" +
            " IFNULL(SUM(IF( mcma.appraisal_rating = 2, 1, 0 )),0) AS qualified,\n" +
            " IFNULL(SUM(IF( mcma.appraisal_rating = 3, 1, 0 )),0) AS basicQualified,\n" +
            " IFNULL(SUM(IF( mcma.appraisal_rating = 4, 1, 0 )),0) AS unqualified,\n" +
            " IFNULL(SUM(IF( mcma.appraisal_rating IS NULL || mcma.appraisal_rating not in (1,2,3,4), 1, 0 )),0) AS other \n" +
            "FROM t_meeting_comment_member_appraisal mcma \n" +
            "WHERE\n" +
            " mcma.comment_member_id = #{commentMemberId} \n" +
            " AND mcma.is_draft = 1" +
            "</script>")
    fun appraisalStatistical(@Param("commentMemberId") commentMemberId: Long?): CommentStatisticalDataForm

    @Select("<script>" +
            "SELECT\n" +
            "\tmcma.comment_member_appraisal_id \n" +
            "FROM\n" +
            "\tt_meeting_comment_member mcm\n" +
            "\tLEFT JOIN t_meeting_comment_member_appraisal mcma ON mcm.comment_member_id = mcma.comment_member_id \n" +
            "WHERE\n" +
            "\tmcm.comment_id = #{commentId} \n" +
            "\tAND ( mcma.comment_member_id = #{commentMemberId} OR mcma.user_id = #{userId} )" +
            "</script>")
    fun selectAppraisalData(@Param("commentMemberId") commentMemberId: Long,
                         @Param("userId") userId: Long?,
                         @Param("commentId") commentId: Long?): MutableList<Long>
}

@Repository
@Mapper
interface MeetingCommentMemberComplexMapper : MyMapper<MeetingCommentMemberComplexEntity> {
    @Select("select t1.org_name as orgName,t2.user_name as userName,t3. complex_rating as  complexRating from t_meeting_comment t1\n" +
            "left join t_meeting_comment_member t2 on t1.comment_id= t2.comment_id\n" +
            "left join t_meeting_comment_member_complex t3 on t2.comment_member_id = t3.comment_member_id\n" +
            "where t1.comment_id=#{commentId}\n" +
            "  AND t2.political_type = 1\n")
    fun getComplexFormInfo(@Param("commentId") commentId: Long) : MutableList<CommentOrgGreadVO>

    @Select("<script>" +
            "SELECT\n" +
            " SUM(IF( mcmc.complex_rating = 1, 1, 0 )) AS excellent,\n" +
            " SUM(IF( mcmc.complex_rating = 2, 1, 0 )) AS qualified,\n" +
            " SUM(IF( mcmc.complex_rating = 3, 1, 0 )) AS basicQualified,\n" +
            " SUM(IF( mcmc.complex_rating = 4, 1, 0 )) AS unqualified,\n" +
            " SUM(IF( mcmc.complex_rating IS NULL || mcmc.complex_rating not in (1,2,3,4), 1, 0 )) AS other \n" +
            "FROM t_meeting_comment_member mcm \n" +
            "LEFT JOIN t_meeting_comment_member_complex mcmc ON mcm.comment_member_id = mcmc.comment_member_id \n" +
            "WHERE\n" +
            " mcm.comment_id = #{commentId} \n" +
            " AND mcm.political_type = 1" +
            "</script>")
    fun complexStatistical(@Param("commentId") commentId: Long): CommentStatisticalDataForm?
}

@Repository
@Mapper
interface MeetingCommentStatisticsMapper : MyMapper<MeetingCommentStatisticsEntity> {
    @Select(
        """<script> select comment_id as commentId,org_id as orgId,org_name as orgName,year,party_number as partyNumber,join_number as joinNumber,sum(A)  as level1,sum(B)  as level2,sum(C)  as level3,sum(D)  as level4 from(
select t1.comment_id, t1.org_id,t1.org_level,t1.year as year,t1.org_name,t1.party_number,t1.join_number,
case when t1.rating=1  then t1.rating_number else 0 end as 'A',
case when t1.rating=2 then rating_number else 0 end as 'B',
case when t1.rating=3 then rating_number else 0 end as 'C',
case when t1.rating=4 then rating_number else 0 end as 'D'
 from t_meeting_comment_statistics t1 where t1.region_id=86
 ) a 
 where 1=1 
 <if test ="orgId != null and orgId != ''"> and (a.org_id=#{orgId} or a.org_level like concat('%-',#{orgId},'-%')) </if> 
 <if test ="year != null and year != ''"> and a.year = #{year} </if> 
 group by a.comment_id,a.org_id,a.year,a.org_name,a.party_number,a.join_number
order by a.org_id,a.year desc</script> """
    )
    fun queryStatics(@Param("year")year: Int?, @Param("orgId")orgId:Long?) : MutableList<StaticsForm>
}

@Repository
@Mapper
interface MeetingCommentMemberComplexHistoryMapper : MyMapper<MeetingCommentMemberComplexHistoryEntity> {

}
