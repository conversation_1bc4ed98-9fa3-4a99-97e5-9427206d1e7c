package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingPlanLimitTypeEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @program: ows-meeting
 * @description: ${description}
 * @author: taiqian.Luo
 * @create: 2019-04-18 15:07
 **/
@Repository
@Mapper
public interface MeetingPlanLimitTypeMapper extends MyMapper<MeetingPlanLimitTypeEntity> {

    @Select("SELECT meeting_plan_limit_type_id,meeting_plan_limit_id,org_type_child,org_type_child_name FROM t_meeting_plan_limit_type WHERE meeting_plan_limit_id = #{id} ")
    @Results({
            @Result(property = "meetingPlanLimitTypeId", column = "meeting_plan_limit_type_id"),
            @Result(property = "meetingPlanLimitId", column = "meeting_plan_limit_id"),
            @Result(property = "orgTypeChild", column = "org_type_child"),
            @Result(property = "orgTypeChildName", column = "org_type_child_name")
    })
    List<MeetingPlanLimitTypeEntity> findByMeetingPlanLimitId(@Param("id") Long id);


    @Select("SELECT meeting_plan_limit_type_id meetingPlanLimitTypeId,org_type_child orgTypeChild, " +
            "org_type_child_name orgTypeChildName FROM t_meeting_plan_limit_type WHERE meeting_plan_limit_id = #{id} ")
    List<MeetingPlanLimitTypeEntity> findByMeetingPlanLimitType(@Param("id") long meetingPlanLimitId);
}
