package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingAgendaEntity;
import com.goodsogood.ows.model.db.MeetingTagEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: tc
 * @Description  活动标签
 */
@Repository
@Mapper
public interface MeetingTagMapper extends MyMapper<MeetingTagEntity> {

    /**
     * 查询会议关联的标签
     *
     * @param meetingId 会议id
     * @return MeetingTagEntity
     */
    @Select(" SELECT meeting_tag_id,meeting_id,tag_id,tag_name," +
            " last_update_time,last_change_user FROM t_meeting_tag " +
            " where meeting_id = #{meetingId} and agenda_id is null ")
    @Results({
            @Result(property = "meetingTagId", column = "meeting_tag_id"),
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "tagId", column = "tag_id"),
            @Result(property = "tagName", column = "tag_name"),
            @Result(property = "lastUpdateTime", column = "last_update_time"),
            @Result(property = "lastChangeUser", column = "last_change_user")
    })
    List<MeetingTagEntity> findByMeetingIdNew(@Param("meetingId") long meetingId);


    /**
     * 查询议程关联的标签
     *
     * @param agendaId 议程id
     * @return MeetingTagEntity
     */
    @Select(" SELECT meeting_tag_id,meeting_id,agenda_id,tag_id,tag_name," +
            " last_update_time,last_change_user FROM t_meeting_tag " +
            " where agenda_id = #{agendaId}")
    @Results({
            @Result(property = "meetingTagId", column = "meeting_tag_id"),
            @Result(property = "agendaId", column = "agenda_id"),
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "tagId", column = "tag_id"),
            @Result(property = "tagName", column = "tag_name"),
            @Result(property = "lastUpdateTime", column = "last_update_time"),
            @Result(property = "lastChangeUser", column = "last_change_user")
    })
    List<MeetingTagEntity> findByAgendaId(@Param("agendaId") Long agendaId);

    /**
     * 查询会议关联的标签
     *
     * @param meetingId 会议id
     * @return MeetingTagEntity
     */
    @Select(" SELECT meeting_tag_id,meeting_id,tag_id,tag_name," +
            " last_update_time,last_change_user FROM t_meeting_tag " +
            " where meeting_id = #{meetingId} ")
    @Results({
            @Result(property = "meetingTagId", column = "meeting_tag_id"),
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "tagId", column = "tag_id"),
            @Result(property = "tagName", column = "tag_name"),
            @Result(property = "lastUpdateTime", column = "last_update_time"),
            @Result(property = "lastChangeUser", column = "last_change_user")
    })
    List<MeetingTagEntity> findByMeetingId(@Param("meetingId") long meetingId);

    /**
     * 根据活动编号和标签编号查询活动标签信息
     *
     * @param meetingIds 会议id
     * @param tagIds 标签id
     *
     * @return MeetingTagEntity
     */
    @Select("<script> SELECT meeting_tag_id,meeting_id,tag_id,tag_name," +
            " last_update_time,last_change_user FROM t_meeting_tag " +
            " where meeting_id in <foreach collection = \"meetingIds\" item = \"mid\" separator=\",\" open=\"(\" close=\")\"> " +
            " #{mid} </foreach> and tag_id in <foreach collection = \"tagIds\" item = \"tid\" separator=\",\" open=\"(\" close=\")\"> " +
            " #{tid} </foreach> </script>")
    @Results({
            @Result(property = "meetingTagId", column = "meeting_tag_id"),
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "tagId", column = "tag_id"),
            @Result(property = "tagName", column = "tag_name"),
            @Result(property = "lastUpdateTime", column = "last_update_time"),
            @Result(property = "lastChangeUser", column = "last_change_user")
    })
    List<MeetingTagEntity> findByMeetingIdsAndTagIds(@Param("meetingIds") List<Long> meetingIds,@Param("tagIds")  List<Long> tagIds);

    @Delete("<script>"+
            " delete from t_meeting_tag where meeting_id=#{meetingId} and agenda_id in "+
            " <foreach collection = \"agendaIds\" item = \"agendaId\" separator=\",\" open=\"(\" close=\")\"> " +
            " #{agendaId} </foreach> "+
            "</script>")

    void deleteByAgendaId(@Param("meetingId") Long meetingId,@Param("agendaIds")List<Long> agendaIds);
}