package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingRequireEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-22 15:52
 **/
@Repository
@Mapper
public interface MeetingRequireMapper extends tk.mybatis.mapper.common.Mapper<MeetingRequireEntity>, MeetingRequireInsertListMapper<MeetingRequireEntity>{
    @Select("SELECT meeting_require_id,meeting_plan_id,type_id,category_id,type,category,meeting_num,deduct,is_w_resolution,is_sign_in " +
            " FROM t_meeting_require WHERE meeting_plan_id = #{id} ")
    @Results({
            @Result(property = "meetingRequireId", column = "meeting_require_id"),
            @Result(property = "meetingPlanId", column = "meeting_plan_id"),
            @Result(property = "typeId", column = "type_id"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "type", column = "type"),
            @Result(property = "category", column = "category"),
            @Result(property = "meetingNum", column = "meeting_num"),
            @Result(property = "deduct", column = "deduct"),
            @Result(property = "isWResolution", column = "is_w_resolution"),
            @Result(property = "isSignIn", column = "is_sign_in")
    })
    List<MeetingRequireEntity> findByMeetingPlanId(@Param("id") Long id);

    @Select("SELECT meeting_require_id,meeting_plan_id,type_id,category_id,type,category,meeting_num,deduct,is_w_resolution,is_sign_in " +
            " FROM t_meeting_require WHERE meeting_require_id = #{id} ")
    @Results({
            @Result(property = "meetingRequireId", column = "meeting_require_id"),
            @Result(property = "meetingPlanId", column = "meeting_plan_id"),
            @Result(property = "typeId", column = "type_id"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "type", column = "type"),
            @Result(property = "category", column = "category"),
            @Result(property = "meetingNum", column = "meeting_num"),
            @Result(property = "deduct", column = "deduct"),
            @Result(property = "isWResolution", column = "is_w_resolution"),
            @Result(property = "isSignIn", column = "is_sign_in")
    })
    List<MeetingRequireEntity> findById(@Param("id") Long id);
}