package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingTopicEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-23 11:38
 **/
@Repository
@Mapper
public interface MeetingTopicMapper extends tk.mybatis.mapper.common.Mapper<MeetingTopicEntity>, MeetingTopicInsertListMapper<MeetingTopicEntity>{
    /**
     * 查询会议关联的议题 包括议题信息和议题答案
     *
     * @param meetingId 会议id
     * @return MeetingTopicEntity
     */
    @Select(" SELECT a.meeting_topic_id,a.meeting_id,a.topic_id,a.status," +
            " b.`name`,b.description,b.start_time,b.end_time FROM t_meeting_topic a " +
            " LEFT JOIN t_topic b ON a.topic_id = b.topic_id" +
            " where a.meeting_id = #{meetingId} ")
    @Results({
            @Result(property = "meetingTopicId", column = "meeting_topic_id"),
            @Result(property = "meetingId", column = "meeting_id"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "topicId", column = "topic_id"),
            @Result(property = "topicName", column = "name"),
            @Result(property = "name", column = "name"),
            @Result(property = "description", column = "description"),
            @Result(property = "status", column = "status"),
            //处理one to many 任务
            @Result(property = "contents", column = "topic_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.TopicContentMapper.selectByTopicId")),
            //处理one to many 任务
            @Result(property = "logList", column = "meeting_topic_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.TopicLogMapper.findByMeetingTopicId")),
            //处理one to many 任务
            @Result(property = "files", column = "topic_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.TopicFileMapper.selectByTopicId"))
    })
    List<MeetingTopicEntity> findByMeetingId(@Param("meetingId") long meetingId);
}