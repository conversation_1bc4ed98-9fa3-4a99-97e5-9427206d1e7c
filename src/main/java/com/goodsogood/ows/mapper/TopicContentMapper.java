package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.TopicContentEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-22 14:10
 **/
@Repository
@Mapper
public interface TopicContentMapper extends MyMapper<TopicContentEntity>{
    @Select("SELECT content_id,topic_id,type,name,description FROM t_topic_content where topic_id = #{topicId} ")
    @Results({
            @Result(property = "contentId", column = "content_id"),
            @Result(property = "topicId", column = "topic_id"),
            @Result(property = "type", column = "type"),
            @Result(property = "name", column = "name"),
            @Result(property = "description", column = "description"),
            //处理one to many 任务
            @Result(property = "opts", column = "content_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.TopicOptsMapper.selectByContentId")),
    })
    List<TopicContentEntity> selectByTopicId(Long topicId);
}