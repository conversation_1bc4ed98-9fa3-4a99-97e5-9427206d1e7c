package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingLeaveEntity;
import com.goodsogood.ows.model.db.MeetingUserEntity;
import com.goodsogood.ows.model.vo.LeaveMeetingForm;
import com.goodsogood.ows.model.vo.MeetingLeaveForm;
import com.goodsogood.ows.model.vo.WaitApprovalCountForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2018/10/30 8:31
 */
@Repository
@Mapper
public interface MeetingLeaveMapper extends MyMapper<MeetingLeaveEntity> {

    @SelectProvider(type = MeetingLeaveProvider.class, method = "getLeaveListSql")
    List<MeetingLeaveForm> leaveList(@Param("regionId") long regionId,
                                     @Param("userId") long userId,
                                     @Param("type") int type,
                                     @Param("types") String types,
                                     @Param("startTime") String startTime,
                                     @Param("endTime") String endTime,
                                     @Param("keyWord") String keyWord);

    @SelectProvider(type = MeetingLeaveProvider.class, method = "getDetailSql")
    MeetingLeaveForm detail(@Param("meetingLeaveId") long meetingLeaveId);

    // 2018-11-16 10:19 zhanchuanhao修改
    @Update("UPDATE t_meeting_leave SET `status` = 5 WHERE meeting_id = #{meetingId} AND user_id = #{userId} AND `status` IN(0,1,2,4,6)")
    int signInAfterCancelLeave(@Param("meetingId") long meetingId, @Param("userId") long userId);

    /**
     * 查询用户待审批的请假数量
     */
    @Select(
            "<script> "
                    + " SELECT a.check_user_id as userId,count(*) num FROM t_meeting_leave a,t_meeting b "
                    + " WHERE a.`status` = 1 and a.meeting_id=b.meeting_id and b.region_id=#{regionId} "
                    + " <if test =\"userIdSet != null and userIdSet.size() > 0 \">"
                    + "    and a.check_user_id in "
                    + "    <foreach collection=\"userIdSet\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
                    + "        #{item}"
                    + "    </foreach>"
                    + " </if> "
                    + " GROUP BY a.check_user_id "
                    + " </script>")
    List<WaitApprovalCountForm> waitApprovalCount(@Param("regionId") Long regionId, @Param("userIdSet") Set<Long> userIdSet);

    @Insert("insert into t_meeting_leave(meeting_id,user_id,type,reason,status,create_time,last_change_user,del_status) values (" +
            "#{meetingId},#{userId},#{type},#{reason},1,now(),#{userId},0) on duplicate key " +
            "update status=1,reason=#{reason},type=#{type},check_user_id=null,check_user_name=null,check_time=null,check_result=null," +
            "off_leave_reason=null,off_check_user_id=null,off_check_user_name=null,off_check_result=null,off_check_time=null,create_time=now()" )
    void addLeave(MeetingLeaveEntity meetingLeaveEntity);


    @Select("<script>"+
            "select distinct t1.meeting_id as meetingId,t3.meeting_leave_id as meetingLeaveId,t1.name,t2.user_name as userName," +
            " t1.start_time as startTime,t1.types,t3.status as status\n" +
            " from  t_meeting_leave t3 join t_meeting t1 on t3.meeting_id=t1.meeting_id \n" +
            " join t_meeting_type b on t1.meeting_id=b.meeting_id "+
            " join t_meeting_user t2 on t3.meeting_id=t2.meeting_id and t3.user_id=t2.user_id \n" +
            " where (t3.check_user_id=#{userId} or t3.off_check_user_id=#{userId}) and t3.status!=4 \n" +
            " and t3.del_status=0 and t1.is_del=0"+
            " <if test =\"keyWord != null and keyWord !='' \"> and t1.name like  \"%\"#{keyWord}\"%\"</if> " +
            " <if test =\"userName != null and userName !='' \"> and t2.user_name like   concat('%',#{userName},'%')</if> " +
            " <if test =\"startTime != null and startTime !='' \"> and date_format(t1.start_time,'%Y-%m-%d')<![CDATA[ >= ]]>#{startTime} </if> " +
            " <if test =\"endTime != null and endTime !='' \"> and date_format(t1.start_time,'%Y-%m-%d')<![CDATA[ <= ]]>#{endTime} </if> " +
            " <if test =\"status != null and status !='' \"> and t3.status=#{status} </if> " +
            "                 <if test = \"typeIds!=null and typeIds.size()>0 \">"
            + "                   and b.type_id in"
            + "                   <foreach collection = \"typeIds\" item = \"item\" separator=\",\" open=\"(\" close=\")\">"
            + "                       #{item}"
            + "                   </foreach>"
            + "              </if>"+
            "</script>"
    )
    List<LeaveMeetingForm> queryApprove(@Param("userId") Long userId,  @Param("userName") String userName,
                                        @Param("startTime")String startTime, @Param("endTime")String endTime,
                                        @Param("keyWord")String keyWord, @Param("status") Integer status,
                                        @Param("typeIds") List<Integer> typeIds);

    @Select("select t1.meeting_id as meetingId,t3.meeting_leave_id as meetingLeaveId,t1.name,t2.user_id as userId," +
            " t2.user_name as userName,t1.start_time as startTime,\n" +
            "t3.type,ifnull(t3.off_check_result,t3.check_result) as resolution, t3.reason,t2.phone,t1.types," +
            "t3.off_leave_reason as offLeaveReason,t3.status as status\n" +
            " from  t_meeting_leave t3 join t_meeting t1 on t3.meeting_id=t1.meeting_id \n" +
            " join t_meeting_user t2 on t3.meeting_id=t2.meeting_id and t3.user_id=t2.user_id \n" +
            " where t3.meeting_leave_id = #{meetingLeaveId}\n" +
            " and t3.del_status=0 and t1.is_del=0 limit 1\n" )
    LeaveMeetingForm queryOneDetail(Long meetingLeaveId);

    @Select("<script>"+
            "select distinct t1.meeting_id as meetingId,t3.meeting_leave_id as meetingLeaveId,t1.name,t1.start_time as startTime," +
            " t2.user_id as userId,t2.user_name as userName,t1.types,t3.status as status " +
            " from  t_meeting_leave t3 join t_meeting t1 on t3.meeting_id=t1.meeting_id \n" +
            " join t_meeting_type b on t1.meeting_id=b.meeting_id "+
            " join t_meeting_user t2 on t1.meeting_id=t2.meeting_id and t2.user_id=t3.user_id"+
            " where  \n" +
            "<if test =\"orgIds != null and  orgIds.size() != 0\"> t1.org_id in </if> " +
            "<foreach collection = \"orgIds\" item = \"orgId\" separator=\",\" open=\"(\" close=\")\"> #{orgId} </foreach>"+
            " <if test =\"keyWord != null and keyWord !='' \"> and t1.name like  \"%\"#{keyWord}\"%\"</if> " +
            " <if test =\"userName != null and userName !='' \"> and t2.user_name like   \"%\"#{userName}\"%\"</if> " +
            " <if test =\"startTime != null and startTime !='' \"> and date_format(t1.start_time,'%Y-%m-%d')<![CDATA[ >= ]]>#{startTime} </if> " +
            " <if test =\"endTime != null and endTime !='' \"> and date_format(t1.start_time,'%Y-%m-%d')<![CDATA[ <= ]]>#{endTime} </if> " +
            "                 <if test = \"typeIds!=null and typeIds.size()>0 \">"
            + "                   and b.type_id in"
            + "                   <foreach collection = \"typeIds\" item = \"item\" separator=\",\" open=\"(\" close=\")\">"
            + "                       #{item}"
            + "                   </foreach>"
            + "              </if>"
            + " and t3.del_status=0 and t1.is_del=0\n" +
            " and t3.status in(0,1,4)"+
            "</script>"
    )
    List<LeaveMeetingForm> queryNotApprove(@Param("orgIds") List<Long> orgIds, @Param("userName") String userName,
                                           @Param("startTime") String startTime, @Param("endTime") String endTime,
                                           @Param("keyWord") String keyWord, @Param("typeIds") List<Integer> typeIds);

    @Select("<script>"+
            "select distinct t1.meeting_id as meetingId,t3.meeting_leave_id as meetingLeaveId,t3.meeting_leave_id as meetingLeaveId," +
            "t1.name,t1.start_time as startTime,t1.end_time as endTime,t1.types,t3.status as status,\n" +
            " if(t3.status=3,t3.check_result,if(t3.status=6,t3.off_check_result,'')) as resolution "+
            " from  t_meeting_leave t3 join t_meeting t1 on t3.meeting_id=t1.meeting_id \n" +
            " join t_meeting_type b on t1.meeting_id=b.meeting_id "+
            " where t3.user_id=#{userId}  and t3.del_status=0 and t1.is_del=0 and t1.region_id=#{regionId} "+
//            " <if test =\"keyWord != null and keyWord !='' \"> and t1.name like  \"%\"#{keyWord}\"%\"</if> " +
            " <if test =\"status != null and status !='' \">"
            + " <choose>"
            + "      <when test=\"status==5\">" // 已撤销
            + "             and t3.status in(5,7)"
            + "      </when>"
            + "      <otherwise>"
            + "        and t3.status=#{status} "
            + "      </otherwise>"
            + " </choose>"+
            "</if> " +
            "                 <if test = \"typeIds!=null and typeIds.size()>0 \">"
            + "                   and b.type_id in"
            + "                   <foreach collection = \"typeIds\" item = \"item\" separator=\",\" open=\"(\" close=\")\">"
            + "                       #{item}"
            + "                   </foreach>"
            + "              </if>"+
            " <if test =\"keyWord != null and keyWord !='' \"> and t1.name like  \"%\"#{keyWord}\"%\"</if> " +
            " <if test =\"startTime != null and startTime !='' \"> and date_format(t1.start_time,'%Y-%m-%d')<![CDATA[ >= ]]>#{startTime} </if> " +
            " <if test =\"endTime != null and endTime !='' \"> and date_format(t1.start_time,'%Y-%m-%d')<![CDATA[ <= ]]>#{endTime} </if> " +
            " order by t3.create_time desc" +
            "</script>"
    )
    List<LeaveMeetingForm> queryAllLeave(@Param("regionId") long regionId, @Param("userId")long userId,  @Param("typeIds")List<Integer> typeIds,
                                         @Param("startTime")String startTime, @Param("endTime")String endTime,
                                         @Param("keyWord")String keyWord,  @Param("status")Integer status);

    @Select("<script>"+
            "select distinct t1.meeting_id as meetingId,t3.meeting_leave_id as meetingLeaveId,t1.name,t1.start_time as startTime," +
            "t1.end_time as endTime,t1.types,t3.status as status\n" +
            " from  t_meeting_leave t3 join t_meeting t1 on t3.meeting_id=t1.meeting_id \n" +
            " join t_meeting_type b on t1.meeting_id=b.meeting_id "+
            " where t3.user_id=#{userId} and t3.status in(0,1,2,6)  and t1.status=3 and t3.del_status=0 and t1.is_del=0 "+
            " <if test =\"keyWord != null and keyWord !='' \"> and t1.name like  \"%\"#{keyWord}\"%\"</if> " +
            " <if test =\"status != null and status !='' \"> and t3.status=#{status} </if> " +
            "                 <if test = \"typeIds!=null and typeIds.size()>0 \">"
            + "                   and b.type_id in"
            + "                   <foreach collection = \"typeIds\" item = \"item\" separator=\",\" open=\"(\" close=\")\">"
            + "                       #{item}"
            + "                   </foreach>"
            + "              </if>"+
            " <if test =\"startTime != null and startTime !='' \"> and date_format(t1.start_time,'%Y-%m-%d')<![CDATA[ >= ]]>#{startTime} </if> " +
            " <if test =\"endTime != null and endTime !='' \"> and date_format(t1.start_time,'%Y-%m-%d')<![CDATA[ <= ]]>#{endTime} </if> " +
            " order by t1.create_time desc" +
            "</script>"
    )
    List<LeaveMeetingForm> queryCanOffLeave(@Param("userId") long userId,
                                            @Param("typeIds") List<Integer> typeIds, @Param("startTime") String startTime,
                                            @Param("endTime") String endTime, @Param("keyWord") String keyWord, @Param("status") Integer status);

    @Update("<script>"
            + "    <foreach collection=\"list\"  item=\"item\" open=\" \" separator=\";\" close=\" \">\n"
            + " update t_meeting_leave set status=#{item.status} where meeting_leave_id=#{item.meetingLeaveId}"
            + "    </foreach>"
            + "</script>")
    Integer updateStatus(List<MeetingLeaveEntity> list);

    @Select("select user_id as userId, type from t_meeting_leave where status in(2,4,6) and meeting_id=#{meetingId} and del_status=0")
    List<MeetingLeaveEntity> queryLeaveUser(@Param("meetingId") Long meetingId);
}
