package com.goodsogood.ows.mapper;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Options;
import tk.mybatis.mapper.provider.SpecialProvider;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-22 15:07
 * @since 1.0.6
 **/

public interface MeetingResultFileInsertListMapper<T> {
    @Options(useGeneratedKeys = true, keyProperty = "meetingResultFileId")
    @InsertProvider(type = SpecialProvider.class, method = "dynamicSQL")
    int insertList(List<T> recordList);
}