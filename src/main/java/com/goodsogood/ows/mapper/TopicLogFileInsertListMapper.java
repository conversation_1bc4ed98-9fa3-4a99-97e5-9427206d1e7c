package com.goodsogood.ows.mapper;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.provider.SpecialProvider;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-29 18:17
 **/
@Repository
@Mapper
public interface TopicLogFileInsertListMapper<T> {

     @Options(useGeneratedKeys = true, keyProperty = "topicLogFileId")
     @InsertProvider(type = SpecialProvider.class, method = "dynamicSQL")
     int insertList(List<T> recordList);

}