package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.UserCommendPenalizeEntity;
import com.goodsogood.ows.model.vo.UserCommendPenalizeVO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 党员奖惩Mapper
 * <AUTHOR>
 * @date 2019/12/27
 * @return
 */
@Repository
@Mapper
public interface UserCommendPenalizeMapper extends MyMapper<UserCommendPenalizeEntity> {

    @Select("<script>" +
            "select meeting_user_commend_penalize_id, user_id, user_name, cert_number, org_id, org_name, effective_time, " +
            "`category`, `level`, `name`, `content`, reason, reward_type, basis_description, office_name, office_level, attachment, type, approval_status, award_unit, related_file\n" +
            "from t_meeting_user_commend_penalize \n" +
            "where `status` = 1 \n" +
            "and (org_id = #{orgId} OR org_level like CONCAT('%-', #{orgId}, '-%'))" +
            "<if test=\"userName != null and userName != '' \">AND user_name like CONCAT('%', #{userName}, '%')\n</if>" +
            "<if test=\"category != null \"> and category = #{category} </if>" +
            "<if test=\"level != null \"> and level = #{level} </if>" +
            "<if test=\"name != null \"> and name = #{name} </if>" +
            "<if test=\"type != null \"> and type = #{type} </if>" +
            "<if test=\"startTime != null and startTime != '' \">AND STR_TO_DATE(effective_time,\"%Y-%m-%d\") <![CDATA[>=]]> #{startTime}\n</if>" +
            "<if test=\"endTime != null and endTime != '' \">AND STR_TO_DATE(effective_time,\"%Y-%m-%d\") <![CDATA[<=]]> #{endTime}\n</if>" +
            "<if test=\"regionId != null \">AND region_id = #{regionId}\n</if>" +
            "<if test=\"approvalStatus != null \">AND approval_status = #{approvalStatus}\n</if>" +
            "ORDER BY effective_time DESC \n" +
            "</script>")
    @Results({
            @Result(property = "meetingUserCommendPenalizeId", column = "meeting_user_commend_penalize_id"),
            @Result(property = "userId", column = "user_id"),
            @Result(property = "userName", column = "user_name"),
            @Result(property = "certNumber", column = "cert_number"),
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "orgName", column = "org_name"),
            @Result(property = "effectiveTime", column = "effective_time"),
            @Result(property = "category", column = "category"),
            @Result(property = "level", column = "level"),
            @Result(property = "name", column = "name"),
            @Result(property = "content", column = "content"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "rewardType", column = "reward_type"),
            @Result(property = "basisDescription", column = "basis_description"),
            @Result(property = "awardUnit", column = "award_unit"),
            @Result(property = "relatedFile", column = "related_file"),
            @Result(property = "officeName", column = "office_name"),
            @Result(property = "officeLevel", column = "office_level"),
            @Result(property = "attachment", column = "attachment"),
            @Result(property = "type", column = "type"),
            @Result(property = "approvalStatus", column = "approval_status")
    })
    List<UserCommendPenalizeVO> queryUserCommendPenalizeList(@Param("orgId") Long orgId, @Param("userName") String userName,
                                                             @Param("category") Integer category, @Param("level") Integer level,
                                                             @Param("name") Integer name, @Param("startTime") String startTime,
                                                             @Param("endTime") String endTime, @Param("regionId") Long regionId,
                                                             @Param("type") Integer type, @Param("approvalStatus") Integer approvalStatus);

    @Select("<script>" +
            "select meeting_user_commend_penalize_id, user_id, user_name, cert_number, org_id, org_name, effective_time, " +
            "category, level, `name`, reason, reward_type, basis_description, office_name, office_level, attachment, type \n" +
            "from t_meeting_user_commend_penalize \n" +
            "where `status` = 1 \n" +
            "and approval_status = 2\n" +
            "and user_id = #{userId} \n" +
            "and type = 1 \n" +
            "ORDER BY effective_time DESC \n" +
            "</script>")
    @Results({
            @Result(property = "meetingUserCommendPenalizeId", column = "meeting_user_commend_penalize_id"),
            @Result(property = "userId", column = "user_id"),
            @Result(property = "userName", column = "user_name"),
            @Result(property = "certNumber", column = "cert_number"),
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "orgName", column = "org_name"),
            @Result(property = "effectiveTime", column = "effective_time"),
            @Result(property = "category", column = "category"),
            @Result(property = "level", column = "level"),
            @Result(property = "name", column = "name"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "rewardType", column = "reward_type"),
            @Result(property = "basisDescription", column = "basis_description"),
            @Result(property = "officeName", column = "office_name"),
            @Result(property = "officeLevel", column = "office_level"),
            @Result(property = "attachment", column = "attachment"),
            @Result(property = "type", column = "type"),
    })
    List<UserCommendPenalizeVO> selectUserCommendPenalizeListByUserId(@Param("userId") Long userId);


}
