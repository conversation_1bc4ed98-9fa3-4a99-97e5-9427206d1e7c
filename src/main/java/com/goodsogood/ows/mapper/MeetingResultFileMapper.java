package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingResultFileEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-22 15:07
 * @since 1.0.6
 */
@Repository
@Mapper
public interface MeetingResultFileMapper
        extends MyMapper<MeetingResultFileEntity> {
    @Select(" SELECT " +
            "   a.meeting_result_file_id as meetingContactLeaderId," +
            "   a.meeting_id as meetingId," +
            "   a.id," +
            "   a.name," +
            "   a.path," +
            "   a.file_name as fileName," +
            "   a.size," +
            "   a.is_del as isDel," +
            "   a.type" +
            "   FROM " +
            "   t_meeting_result_file a " +
            " WHERE a.is_del=0 and a.meeting_id = #{meetingId} ")
    List<MeetingResultFileEntity> findByMeetingId(@Param("meetingId") long meetingId);

    @Update("UPDATE t_meeting_result_file SET is_del = 1 WHERE meeting_id = #{meetingId}")
    int deleteByMeetingId(@Param("meetingId") Long meetingId);
}
