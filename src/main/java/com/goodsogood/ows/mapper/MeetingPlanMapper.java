package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingPlanEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-23 13:55
 */
@Repository
@Mapper
public interface MeetingPlanMapper extends MyMapper<MeetingPlanEntity> {
    @Select(
            " <script> "
                    + "SELECT meeting_plan_id,name,execute_type,start_time,end_time,is_execute,send_type FROM t_meeting_plan "
                    + "WHERE is_del=0 "
                    + " <if test =\"name != null and name !='' \"> and name like  \"%\"#{name}\"%\"</if> "
                    + " <if test =\"oid != null \"> and org_id = #{oid}</if> "
                    + " ORDER BY meeting_plan_id  ASC "
                    + " </script>")
    @Results({
            @Result(property = "meetingPlanId", column = "meeting_plan_id"),
            @Result(property = "name", column = "name"),
            @Result(property = "executeType", column = "execute_type"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "isExecute", column = "is_execute"),
            @Result(property = "sendType", column = "send_type")
    })
    List<MeetingPlanEntity> findAll(@Param("name") String name, @Param("oid") Long oid);

    @Select(" SELECT meeting_plan_id,org_id,name,execute_type,start_time,end_time,start_year, " +
            " start_month,start_quarter,create_user,is_execute,send_type " +
            " FROM t_meeting_plan WHERE is_del=0 and meeting_plan_id = #{id}")
    @Results({
            @Result(property = "meetingPlanId", column = "meeting_plan_id"),
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "name", column = "name"),
            @Result(property = "executeType", column = "execute_type"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "startYear", column = "start_year"),
            @Result(property = "startMonth", column = "start_month"),
            @Result(property = "startQuarter", column = "start_quarter"),
            @Result(property = "createUser", column = "create_user"),
            @Result(property = "isExecute", column = "is_execute"),
            @Result(property = "sendType", column = "send_type"),
            // 处理one to many
            @Result(
                    property = "executeOrgs",
                    column = "meeting_plan_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingOrgMapper.findByMeetingPlanId")),
            @Result(
                    property = "meetingTypes",
                    column = "meeting_plan_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingRequireMapper.findByMeetingPlanId")),
            @Result(
                    property = "planLimitEntity",
                    column = "meeting_plan_id",
                    one = @One(select = "com.goodsogood.ows.mapper.MeetingPlanLimitMapper.findByMeetingPlanLimit"))
    })
    MeetingPlanEntity findById(long id);

    /**
     * 定时派发任务查询启用中计划
     *
     * @return List<MeetingPlanEntity>
     */
    @Select(" <script> "
            + " SELECT region_id,meeting_plan_id,name,org_id,execute_type,start_time,end_time,start_year,start_month,start_quarter,is_execute,create_user,send_type "
            + " FROM t_meeting_plan "
            + " WHERE is_del=0 and region_id=#{regionId} "
            + " and is_execute = 1 "
            + " </script>")
    @Results({
            @Result(property = "meetingPlanId", column = "meeting_plan_id"),
            @Result(property = "name", column = "name"),
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "executeType", column = "execute_type"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "startYear", column = "start_year"),
            @Result(property = "startMonth", column = "start_month"),
            @Result(property = "startQuarter", column = "start_quarter"),
            @Result(property = "isExecute", column = "is_execute"),
            @Result(property = "createUser", column = "create_user"),
            @Result(property = "regionId", column = "region_id"),
            @Result(property = "sendType", column = "send_type"),
            // 处理one to one
            @Result(
                    property = "planLimitEntity",
                    column = "meeting_plan_id",
                    one =
                    @One(select = "com.goodsogood.ows.mapper.MeetingPlanLimitMapper.findByMeetingPlanId")),
            // 处理one to many
            @Result(
                    property = "executeOrgs",
                    column = "meeting_plan_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingOrgMapper.findByMeetingPlanId")),
            @Result(
                    property = "meetingTypes",
                    column = "meeting_plan_id",
                    many = @Many(select = "com.goodsogood.ows.mapper.MeetingRequireMapper.findByMeetingPlanId"))
    })
    List<MeetingPlanEntity> findWithCreateTask(@Param("regionId") long regionId);
}
