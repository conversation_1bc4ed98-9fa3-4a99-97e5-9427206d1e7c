package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.TopicFileEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-22 14:05
 **/
@Repository
@Mapper
public interface TopicFileMapper extends MyMapper<TopicFileEntity>{
    @Select("SELECT topic_file_id,name,path,size,topic_id,file_name FROM t_topic_file where topic_id = #{topicId} ")
    @Results({
            @Result(property = "topicFileId", column = "topic_file_id"),
            @Result(property = "name", column = "name"),
            @Result(property = "path", column = "path"),
            @Result(property = "size", column = "size"),
            @Result(property = "topicId", column = "topic_id"),
            @Result(property = "fileName", column = "file_name")
    })
    List<TopicFileEntity> selectByTopicId(Long topicId);

}