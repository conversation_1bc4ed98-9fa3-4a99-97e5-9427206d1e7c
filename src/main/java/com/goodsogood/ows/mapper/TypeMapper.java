package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.TypeEntity;
import com.goodsogood.ows.model.vo.TypeListForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:21
 **/
@Repository
@Mapper
public interface TypeMapper extends MyMapper<TypeEntity> {
    @Select(" <script> " +
            " SELECT a.type_id,a.type,a.category_id,a.category,a.code,a.has_lecturer,a.has_lecture_title,a.type_sys FROM t_type a,t_category b " +
            " WHERE a.category_id = b.category_id and b.region_id=#{regionId}" +
            " <if test =\"typeId != null\"> and a.type_id = #{typeId} </if> " +
            " <if test =\"categoryId != null\"> and a.category_id = #{categoryId} </if> " +
            " <if test =\"type != null and type !='' \"> and a.type like  \"%\"#{type}\"%\"</if> " +
            " </script>")
    @Results({
            @Result(property = "typeId", column = "type_id"),
            @Result(property = "type", column = "type"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "category", column = "category"),
            @Result(property = "hasLecturer", column = "has_lecturer"),
            @Result(property = "hasLectureTitle", column = "has_lecture_title"),
            @Result(property = "typeSys", column = "type_sys")
    })
    List<TypeEntity> listAll(TypeListForm typeListFrom);


    @Select(" SELECT a.type_id,a.type,a.category_id,a.category FROM t_type a,t_category b " +
            " WHERE a.category_id = b.category_id and b.region_id=#{regionId} and a.type = #{type} ")
    @Results({
            @Result(property = "typeId", column = "type_id"),
            @Result(property = "type", column = "type"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "category", column = "category")
    })
    TypeEntity findByName(@Param("regionId") long regionId, @Param("type") String type);

    @Select("SELECT type_id,type,category_id,category FROM t_type WHERE type_id = #{id} ")
    @Results({
            @Result(property = "typeId", column = "type_id"),
            @Result(property = "type", column = "type"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "category", column = "category")
    })
    List<TypeEntity> findById(@Param("id") Long id);

    @Select("SELECT b.type_id,b.type,b.category_id,b.category,b.type_sys  FROM t_type_group a,t_type b WHERE group_id = #{id} and a.type_id=b.type_id ")
    @Results({
            @Result(property = "typeId", column = "type_id"),
            @Result(property = "type", column = "type"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "category", column = "category"),
            @Result(property = "typeSys", column = "type_sys")
    })
    List<TypeEntity> findByGroupId(@Param("id") Long id);

    @Update("update t_type set `code`=#{code} ,has_lecturer=#{hasLecturer}, has_lecture_title=#{hasLectureTitle} where type_id=#{typeId}")
    void updateUserRule(@Param("typeId") Long typeId, @Param("code") Integer code, @Param("hasLecturer") Integer hasLecturer, @Param("hasLectureTitle") Integer hasLectureTitle);

    @Select("select t1.type_id typeId,t1.type_sys typeSys\n" +
            "from t_meeting_type mt\n" +
            "inner join t_type t1 on mt.type_id=t1.type_id\n" +
            "where mt.meeting_id=#{meetingId} and t1.type_sys in (${typeSys})")
    TypeEntity findTypesByTypeSys(@Param("meetingId") Long meetingId, @Param("typeSys") String typeSys);
}