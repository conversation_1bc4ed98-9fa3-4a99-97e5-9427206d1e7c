package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.UserCommentStatisticsEntity;
import com.goodsogood.ows.model.vo.UserCommentStatisticsVO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 民主评议统计Mapper
 * <AUTHOR>
 * @date 2020/1/6
 * @return
 */
@Repository
@Mapper
public interface UserCommentStatisticsMapper extends MyMapper<UserCommentStatisticsEntity>{


    @Select("<script>" +
            "SELECT\n" +
            "org_id, org_name, party_number, join_comment_number \n" +
            "FROM t_meeting_user_comment_statistics \n" +
            "WHERE review_year = #{year}\n" +
            "<if test=\"isChild == 1 \"> AND org_parent_id = #{orgId} </if>\n" +
            "<if test=\"isChild == 0 \"> AND org_level like CONCAT('%-', #{orgId}, '-%')" +
            "AND org_type_child in " +
            "    <foreach collection=\"orgTypeList\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "           #{item}\n" +
            "    </foreach>" +
            "</if>\n" +
            "<if test=\"name != null and name != '' \">AND org_name like CONCAT('%', #{name}, '%')</if> \n" +
            "GROUP BY\n" +
            "org_id" +
            "</script>")
    @Results({
            @Result(property = "orgId", column = "org_id"),
            @Result(property = "orgName", column = "org_name"),
            @Result(property = "partyNumber", column = "party_number"),
            @Result(property = "joinCommentNumber", column = "join_comment_number")
    })
    List<UserCommentStatisticsVO> selectUserCommentStatisticsGroupOrg(@Param("orgId") Long orgId, @Param("name") String name,
                                                                      @Param("year") Integer year, @Param("isChild") Integer isChild,
                                                                      @Param("orgTypeList") List<Integer> orgTypeList);
}
