package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.LifeStudyEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface LifeStudyMapper extends MyMapper<LifeStudyEntity>{
    @Delete("delete from t_meeting_life_study where life_id=#{lifeId}")
    void delByLifeId(Long lifeId);
}
