package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingUserScoreDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: tc
 * @Description  组织生活个人积分记录流水表
 */
@Repository
@Mapper
public interface MeetingUserScoreDetailMapper extends MyMapper<MeetingUserScoreDetailEntity> {

    /**
     * 验证组织是否有加分记录
     * @param userIdList 用户编号集合
     * @param scoreType 1.党员考勤积分 2.讲课人积分 3.党小组长开展积分  4.支委会成员每年基础分 5.党小组长每年基础分
     * @param start 周期开始
     * @param end 周期结束
     * @return userId,score
     */
    @Select("<script> select user_id userId,sum(score) score from t_meeting_user_score_detail where score_type = #{scoreType}" +
            " <if test=\"meetingType !=null \">and meeting_type = #{meetingType} </if> and score_sign &gt;= #{start}" +
            " and score_sign &lt;= #{end} <if test=\"isRollBack !=null and isRollBack != 0\"> and meeting_id = #{meetingId} </if>" +
            " and user_id in <foreach collection = \"userIdList\" item = \"userId\" separator=\",\" open=\"(\" close=\")\"> #{userId} </foreach> group by user_id </script>")
    List<MeetingUserScoreDetailEntity> findUserScoreCycle(@Param("userIdList") List<Long> userIdList,@Param("meetingType") Integer meetingType,
                                                          @Param("scoreType") Integer scoreType,
                                                          @Param("start") Integer start, @Param("end") Integer end,
                                                          @Param("isRollBack") Integer isRollBack,@Param("meetingId") Long meetingId);

    /**
     * 根据活动编号和日期查询党小组组长积分记录
     * @param meetingId 活动编号
     * @param start 周期开始
     * @param end 周期结束
     * @return userId,score
     */
    @Select("<script> select user_id userId,sum(score) score from t_meeting_user_score_detail where meeting_type =3 and score_type=3" +
            " and meeting_id=#{meetingId} and score_sign &gt;= #{start} and score_sign &lt;= #{end} group by user_id </script>")
    List<MeetingUserScoreDetailEntity> findGroupLeaderScoreInfo(@Param("meetingId") Long meetingId,
                                                          @Param("start") Integer start, @Param("end") Integer end);

}