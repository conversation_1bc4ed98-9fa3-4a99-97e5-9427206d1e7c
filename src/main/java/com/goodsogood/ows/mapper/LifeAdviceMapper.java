package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.LifeAdviceEntity;
import com.goodsogood.ows.model.vo.LifeAdviceForm;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface LifeAdviceMapper extends MyMapper<LifeAdviceEntity>{

    @Select("select " +
            "advice_id, life_id, data_id, advice_type, step  " +
            "from t_meeting_advice " +
            "where life_id = #{lifeId} and step = #{step} and is_del !=1 " +
            "order by create_time,advice_id")
    @Results({
            @Result(property = "adviceId", column = "advice_id"),
            @Result(property = "lifeId", column = "life_id"),
            @Result(property = "dataId",column = "data_id"),
            @Result(property = "adviceType",column = "advice_type")
    })
    List<LifeAdviceForm.Advice> adviceFind(@Param("lifeId")Long lifeId,@Param("step")Integer step);

//    /**
//     * 批量删除
//     * @param adviceIds
//     * @return
//     */
//    @Delete("<script>" +
//            "delete from t_meeting_advice where advice_id in " +
//            "<foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
//            "#{item}" +
//            "</foreach>" +
//            "</script>")
//    Integer multiDel(@Param("ids") List<Long> adviceIds);


    @Update("update t_meeting_advice set is_del=1 where advice_id=#{id} and advice_type=#{advice_type}")
    Integer oneDel(@Param("id") Long adviceId,@Param("advice_type")Integer adviceType);

    @Update("update t_meeting_advice set is_del=1 where life_id=#{lifeId} and step=#{step} and advice_type=1")
    Integer oneDirectFile(@Param("lifeId") Long lifeId,@Param("step")Integer step);


    @Update("update t_meeting_advice set is_del=1 where life_id=#{lifeId} and data_id=#{dataId} and advice_type=4")
    void oneDelTalk(@Param("lifeId") Long lifeId,@Param("dataId") Long dataId);

    @Delete("delete from t_meeting_advice where life_id=#{lifeId}")
    void delByLifeId(Long lifeId);
}
