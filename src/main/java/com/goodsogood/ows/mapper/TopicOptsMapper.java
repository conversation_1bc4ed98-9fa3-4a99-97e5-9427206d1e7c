package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.TopicOptsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-22 14:16
 **/
@Repository
@Mapper
public interface TopicOptsMapper extends MyMapper<TopicOptsEntity>{
    @Select("SELECT opts_id,content_id,topic_id,opts_name,seq FROM t_topic_opts where content_id = #{contentId} ")
    @Results({
            @Result(property = "contentId", column = "opts_id"),
            @Result(property = "contentId", column = "content_id"),
            @Result(property = "topicId", column = "topic_id"),
            @Result(property = "optsName", column = "opts_name"),
            @Result(property = "seq", column = "seq")
    })
    List<TopicOptsEntity> selectByContentId(Long contentId);
}