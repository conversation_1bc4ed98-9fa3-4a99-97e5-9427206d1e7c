package com.goodsogood.ows.mapper

import com.goodsogood.ows.model.db.*
import com.goodsogood.ows.model.vo.CountMeetingTalkVo
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Repository

@Repository
@Mapper
interface MeetingTalkMapper : MyMapper<MeetingTalkEntity?>

@Repository
@Mapper
interface MeetingTalkContentMapper : MyMapper<MeetingTalkContentEntity?> {

    @Select(
        "<script>" +
                " select talk_content_id talkContentId, talk_id talkId, title, content, status " +
                " from t_meeting_talk_content " +
                " where talk_id = #{talkId}" +
                "   and status = 1" +
                "</script>"
    )
    fun getMeetingTalkContent(@Param("talkId") talkId: Long?): MutableList<MeetingTalkContentEntity>

}

@Repository
@Mapper
interface MeetingTalkLinkMapper : MyMapper<MeetingTalkLinkEntity?> {

    @Select(
        "<script>" +
                " select talk_link_id talkLinkId, talk_id talkId, user_id userId, username, type, status " +
                " from t_meeting_talk_link " +
                " where talk_id = #{talkId}" +
                "   and status = 1" +
                "</script>"
    )
    fun getMeetingTalkLink(@Param("talkId") talkId: Long?): MutableList<MeetingTalkLinkEntity>
}

@Repository
@Mapper
interface MeetingLifeTalkMapper : MyMapper<MeetingLifeTalkEntity?>

@Repository
@Mapper
interface CountMeetingTalkMapper : MyMapper<CountMeetingTalkVo?> {

//    @Select("SELECT e.id, e.name, t.type, t.count " +
//            "FROM entity e " +
//            "INNER JOIN type_count t ON e.id = t.entity_id " +
//            "WHERE e.id = #{id}")
//    @Results(value = [
//        Result(property = "id", column = "id"),
//        Result(property = "name", column = "name"),
//        Result(property = "typeCountMap", column = "type",
//            javaType = Map::class, many = Many(select = "getTypeCountMap"))
//    ])
//    fun getEntityById(@Param("id") id: Int): Entity
//
//    @Select("SELECT type, count FROM type_count WHERE entity_id = #{id}")
//    fun getTypeCountMap(@Param("id") id: Int): Map<String, Int>SELECT
//                        M.org_id AS orgId,CASE WHEN N.total IS NULL THEN 0 ELSE N.total END AS 'peopleNum',
//                        SUM(CASE WHEN M.talk_type = 0 THEN A_NUM ELSE 0 END) AS 'type0',
//                        SUM( CASE WHEN M.talk_type = 1 THEN A_NUM ELSE 0 END ) AS 'type1',
//                        SUM( CASE WHEN M.talk_type = 2 THEN A_NUM ELSE 0 END ) AS 'type2',
//                        SUM( CASE WHEN M.talk_type = 3 THEN A_NUM ELSE 0 END ) AS 'type3',
//                        SUM( CASE WHEN M.talk_type = 4 THEN A_NUM ELSE 0 END ) AS 'type4',
//                        SUM( CASE WHEN M.talk_type = 5 THEN A_NUM ELSE 0 END ) AS 'type5',
//                        SUM( CASE WHEN M.talk_type = 6 THEN A_NUM ELSE 0 END ) AS 'type6',
//                        SUM( CASE WHEN M.talk_type = 7 THEN A_NUM ELSE 0 END ) AS 'type7',
//                        SUM( CASE WHEN M.talk_type = 8 THEN A_NUM ELSE 0 END ) AS 'type8',
//                        SUM( CASE WHEN M.talk_type = 9 THEN A_NUM ELSE 0 END ) AS 'type9',
//                        SUM( CASE WHEN M.talk_type = 10 THEN A_NUM ELSE 0 END ) AS 'type10',
//                        SUM( CASE WHEN M.talk_type = 11 THEN A_NUM ELSE 0 END ) AS 'type11',
//                        SUM( CASE WHEN M.talk_type = 12 THEN A_NUM ELSE 0 END ) AS 'type12',
//
//                FROM (
//                    SELECT
//                        A.talk_id,
//                        A.org_id,
//                        A.talk_type,
//                        A.org_level,
//                        COUNT( talk_type ) AS A_NUM
//                    FROM
//                        t_meeting_talk A
//                    WHERE
//                            A.`status` = 1
//                      AND A.is_submit = 1
//                    GROUP BY
//                        A.org_id,
//                        A.talk_type
//                )M
//                LEFT JOIN
//                (
//                    SELECT
//                        A.org_id,
//                        COUNT(*) AS total,
//                        B.talk_id
//                    FROM t_meeting_talk A
//                    LEFT JOIN t_meeting_talk_link B ON A.talk_id = B.talk_id
//                    WHERE A.talk_type IN (7,9,10,11,12)
//                    AND A.`status` = 1
//                    AND A.is_submit = 1
//                    GROUP BY A.org_id
//                )N
//                ON M.org_id = N.org_id
//                WHERE (M.org_id = #{oid} OR M.org_level LIKE CONCAT('%-',#{oid},'-%'))
//                GROUP BY M.org_id
    @Select(
        """
        <script>
            SELECT 
                M.org_id AS orgId,
                CASE WHEN N.total IS NULL THEN 0 ELSE N.total END AS 'peopleNum',
                SUM(CASE WHEN M.talk_type = 0 THEN A_NUM ELSE 0 END) AS 'type0',
                SUM(CASE WHEN M.talk_type = 1 THEN A_NUM ELSE 0 END) AS 'type1',
                SUM(CASE WHEN M.talk_type = 2 THEN A_NUM ELSE 0 END) AS 'type2',
                SUM(CASE WHEN M.talk_type = 3 THEN A_NUM ELSE 0 END) AS 'type3',
                SUM(CASE WHEN M.talk_type = 4 THEN A_NUM ELSE 0 END) AS 'type4',
                SUM(CASE WHEN M.talk_type = 5 THEN A_NUM ELSE 0 END) AS 'type5',
                SUM(CASE WHEN M.talk_type = 6 THEN A_NUM ELSE 0 END) AS 'type6',
                SUM(CASE WHEN M.talk_type = 7 THEN A_NUM ELSE 0 END) AS 'type7',
                SUM(CASE WHEN M.talk_type = 8 THEN A_NUM ELSE 0 END) AS 'type8',
                SUM(CASE WHEN M.talk_type = 9 THEN A_NUM ELSE 0 END) AS 'type9',
                SUM(CASE WHEN M.talk_type = 10 THEN A_NUM ELSE 0 END) AS 'type10',
                SUM(CASE WHEN M.talk_type = 11 THEN A_NUM ELSE 0 END) AS 'type11',
                SUM(CASE WHEN M.talk_type = 12 THEN A_NUM ELSE 0 END) AS 'type12'
            FROM (
                SELECT
                    A.talk_id,
                    A.org_id,
                    A.talk_type,
                    A.org_level,
                    COUNT(talk_type) AS A_NUM
                FROM
                    t_meeting_talk A
                WHERE
                    A.`status` = 1
                    AND A.is_submit = 1
                GROUP BY
                    A.org_id,
                    A.talk_type
            ) M
            LEFT JOIN (
                SELECT 
                    A.org_id,
                    COUNT(*) AS total,
                    B.talk_id 
                FROM t_meeting_talk A 
                LEFT JOIN t_meeting_talk_link B ON A.talk_id = B.talk_id 
                WHERE A.talk_type IN (7, 9, 10, 11, 12)  
                    AND A.`status` = 1
                    AND A.is_submit = 1
                GROUP BY A.org_id
            ) N ON M.org_id = N.org_id 
            WHERE (M.org_id = #{oid} OR M.org_level LIKE CONCAT('%-', #{oid}, '-%'))
            GROUP BY M.org_id; 
        </script>
    """
    )
    fun countMeetingTalkByCondition(
        @Param("oid") oid: Long
    ): List<CountMeetingTalkVo>

    @Select("""
        <script>
            SELECT COALESCE
	                ( COUNT( talk_type ), 0 ) 
                FROM
                    t_meeting_talk A
                WHERE
                    A.`status` = 1
                    AND A.is_submit = 1
                    AND A.org_id = #{oid}
                    AND A.talk_type = #{type}
        </script>
    """)
    fun countType(@Param("oid")orgId:Long,@Param("type")type:Int):Int

    @Select(
        """
        <script>
                SELECT 
                        M.org_id AS orgId, CASE WHEN N.total IS NULL THEN 0 ELSE N.total END AS 'peopleNum',
                        SUM(CASE WHEN M.talk_type = 0 THEN A_NUM ELSE 0 END) AS 'type0',
                        SUM( CASE WHEN M.talk_type = 1 THEN A_NUM ELSE 0 END ) AS 'type1',
                        SUM( CASE WHEN M.talk_type = 2 THEN A_NUM ELSE 0 END ) AS 'type2',
                        SUM( CASE WHEN M.talk_type = 3 THEN A_NUM ELSE 0 END ) AS 'type3',
                        SUM( CASE WHEN M.talk_type = 4 THEN A_NUM ELSE 0 END ) AS 'type4',
                        SUM( CASE WHEN M.talk_type = 5 THEN A_NUM ELSE 0 END ) AS 'type5',
                        SUM( CASE WHEN M.talk_type = 6 THEN A_NUM ELSE 0 END ) AS 'type6',
                        SUM( CASE WHEN M.talk_type = 7 THEN A_NUM ELSE 0 END ) AS 'type7',
                        SUM( CASE WHEN M.talk_type = 8 THEN A_NUM ELSE 0 END ) AS 'type8',
                        SUM( CASE WHEN M.talk_type = 9 THEN A_NUM ELSE 0 END ) AS 'type9',
                        SUM( CASE WHEN M.talk_type = 10 THEN A_NUM ELSE 0 END ) AS 'type10',
                        SUM( CASE WHEN M.talk_type = 11 THEN A_NUM ELSE 0 END ) AS 'type11',
                        SUM( CASE WHEN M.talk_type = 12 THEN A_NUM ELSE 0 END ) AS 'type12'
                FROM (
                    SELECT
                        A.talk_id,
                        A.org_id,
                        A.talk_type,
                        A.org_level,
                        COUNT( talk_type ) AS A_NUM
                    FROM
                        t_meeting_talk A
                    WHERE
                            A.`status` = 1
                      AND A.is_submit = 1
                    GROUP BY
                        A.org_id,
                        A.talk_type
                )M
                LEFT JOIN
                (
                    SELECT 
                        A.org_id,
                        COUNT(*) AS total,
                        B.talk_id 
                    FROM t_meeting_talk A 
                    LEFT JOIN t_meeting_talk_link B ON A.talk_id = B.talk_id 
                    WHERE A.talk_type IN (7,9,10,11,12)  
                    AND A.`status` = 1
                    AND A.is_submit = 1
                    GROUP BY A.org_id
                )N 
                ON M.org_id = N.org_id 
                WHERE M.org_id IN 
                <foreach item="id" collection="oids" open="(" separator="," close=")">
                       #{id}
                </foreach>
                GROUP BY M.org_id
        </script>
    """
    )
    fun countMeetingTalkByIds(
        @Param("oids") oids:List<Long>
    ): List<CountMeetingTalkVo>

}