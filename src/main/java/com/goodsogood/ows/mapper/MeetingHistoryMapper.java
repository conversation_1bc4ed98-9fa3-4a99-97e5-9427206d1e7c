package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.MeetingHistoryEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-25 18:24
 **/
@Repository
@Mapper
public interface MeetingHistoryMapper extends MyMapper<MeetingHistoryEntity> {

    @Select("SELECT " +
            "h.meeting_history_id meetingHistoryId, " +
            "h.`status` status, " +
            "h.reason reason, " +
            "h.create_time createTime " +
            "FROM " +
            "t_meeting_history h " +
            "WHERE " +
            "meeting_id = #{meetingId} " +
            "AND NOW() >= h.create_time " +
            "ORDER BY " +
            "h.create_time DESC ")
    List<MeetingHistoryEntity> getMeetingHistoryList(@Param("meetingId") long meetingId);

}