package com.goodsogood.ows.mapper;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.TobaccoTaskOrgEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface TobaccoTaskOrgMapper extends MyMapper<TobaccoTaskOrgEntity>{

//    @Select("<script>" +
//            "select " +
//            "to_id toId," +
//            "task_id taskId," +
//            "begin_time beginTime," +
//            "end_time endTime," +
//            "label label," +
//            "handle_status handleStatus " +
//            "from t_meeting_tobacco_task_org " +
//            "where is_del = 0 " +
//            "<if>" +
//            "<if>" +
//            "<if>" +
//            "and (accept_scope = 2 and accept_id = #{header.userId})" +
//            "<if> or (accept_scope = 1 and accept_id = #{header.oid}) </if> " +
//            "" +
//            "</script>")
//    List myTaskList(@Param("header") HeaderHelper.SysHeader header);描述
}