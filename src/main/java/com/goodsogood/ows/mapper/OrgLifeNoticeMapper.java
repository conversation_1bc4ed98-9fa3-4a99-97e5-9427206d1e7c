package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.OrgLifeNoticeEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface OrgLifeNoticeMapper extends MyMapper<OrgLifeNoticeEntity>{
    @Delete("delete from t_meeting_org_life_notice where life_id=#{lifeId}")
    void delByLifeId(Long lifeId);
}
