package com.goodsogood.ows.mapper;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Options;
import tk.mybatis.mapper.provider.SpecialProvider;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-09-19 10:57
 **/
public interface MeetingOrgPeriodInsertListMapper<T> {
    @Options(useGeneratedKeys = true, keyProperty = "meetingOrgPeriodId")
    @InsertProvider(type = SpecialProvider.class, method = "dynamicSQL")
    int insertList(List<T> recordList);
}