package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.LifeCheckEntity;
import com.goodsogood.ows.model.vo.LifeCheckForm;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface LifeCheckMapper extends MyMapper<LifeCheckEntity>{

    @Select("select " +
            " ( @i := 5 ) origin, ( @j := 6 ) sign,  ( @k := '5,6' ) modelType, " +
            "check_id, life_id, username ,org_name, step " +
            "from t_meeting_check " +
            "where life_id = #{lifeId} and step = #{step} and is_del != 1 " +
            "order by create_time,check_id ")
    @Results({
            @Result(property = "checkId",column = "check_id"),
            @Result(property = "lifeId",column = "life_id"),
            @Result(property = "username",column = "username"),
            @Result(property = "orgName",column = "org_name"),
            @Result(property = "originFiles",column = "{dataId = check_id,step = step,modelType = origin}",
                    many = @Many(select = "com.goodsogood.ows.mapper.LifeFileMapper.dataFile")),
            @Result(property = "signFiles",column = "{dataId = check_id,step = step,modelType = sign}",
                    many = @Many(select = "com.goodsogood.ows.mapper.LifeFileMapper.dataFile")),
            @Result(property = "originUploader",column = "{dataId = check_id, modelType = origin}",
                    one = @One(select = "com.goodsogood.ows.mapper.LifeUploaderMapper.dataUploader")),
            @Result(property = "signUploader",column = "{dataId = check_id, modelType = sign}",
                    one = @One(select = "com.goodsogood.ows.mapper.LifeUploaderMapper.dataUploader"))

    })
    List<LifeCheckForm> checkSelf(@Param("lifeId") Long lifeId, @Param("step") Integer step);

//    /**
//     * 批量删除
//     * @param checkIds
//     * @return
//     */
//    @Delete("<script>" +
//            "delete from t_meeting_check where check_id in " +
//            "<foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
//            "#{item}" +
//            "</foreach>" +
//            "</script>")
//    Integer multiDel(@Param("ids") List<Long> checkIds);
//
    /**
     * 批量逻辑删除
     * @param checkIds
     * @param uid
     * @return
     */
    @Update("<script>" +
            "update t_meeting_check set is_del = 1,update_time = now(),last_change_user = #{uid} where check_id in " +
            "<foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            "#{item}" +
            "</foreach>" +
            "</script>")
    Integer multiDel(@Param("ids") List<Long> checkIds,@Param("uid")Long uid);

    /**
     * 同步组织信息
     * @param organizationBase
     * @return
     */
    @Update("update t_meeting_check " +
            "set org_name = #{base.name}" +
            "update_time = now(), last_change_user = -111 " +
            "where org_id = #{base.organizationId}")
    Integer updateOrgInfo(@Param("base") OrganizationBase organizationBase);

    @Delete("delete from t_meeting_check where life_id=#{lifeId}")
    void delByLifeId(Long lifeId);
}
