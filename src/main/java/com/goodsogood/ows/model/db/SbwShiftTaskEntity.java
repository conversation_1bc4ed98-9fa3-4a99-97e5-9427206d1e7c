package com.goodsogood.ows.model.db;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 南岸区网信办任务处理实体
 * <AUTHOR>
 * @date 2021.07.29
 */
@Data
@ApiModel
@Table(name = "t_meeting_sbw_shift_task")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SbwShiftTaskEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @ApiModelProperty("主键")
    private Long shiftTaskId;

    /**
     * 任务id
     */
    @Column(name = "task_id")
    @ApiModelProperty("任务id")
    private Long taskId;

    /**
     * 区县id
     */
    @Column(name = "region_id")
    @ApiModelProperty("区县id")
    private Long regionId;

    /**
     * 发起组织id
     */
    @Column(name = "org_id")
    @ApiModelProperty("发起组织id")
    private Long orgId;

    /**
     * 发起组织名称
     */
    @Column(name = "org_name")
    @ApiModelProperty("发起组织名称")
    private String orgName;

    /**
     * 任务名称
     */
    @Column(name = "title")
    @ApiModelProperty("任务名称")
    private String title;

    /**
     * 任务编号
     */
    @Column(name = "number")
    @ApiModelProperty("任务编号")
    private String number;

    @Column(name = "time_type")
    @ApiModelProperty("时间类型  1:本月内 2:本季度内 3:本年内 4:自定义时间")
    private Integer timeType;

    /**
     * 任务开始时间
     */
    @Column(name = "begin_time")
    @ApiModelProperty("任务开始时间")
    private Date beginTime;

    /**
     * 任务结束时间
     */
    @Column(name = "end_time")
    @ApiModelProperty("任务结束时间")
    private Date endTime;

    /**
     * 舆情来源
     */
    @Column(name = "source")
    @ApiModelProperty("舆情来源")
    private String source;

    /**
     * 舆情分类
     */
    @Column(name = "type_id")
    @ApiModelProperty("舆情分类")
    private Long typeId;

    /**
     * 舆情概要
     */
    @Column(name = "content")
    @ApiModelProperty("舆情概要")
    private String content;

    /**
     * 文件json
     */
    @Column(name = "file_json")
    @ApiModelProperty("文件json")
    private String fileJson;

    /**
     * 备注
     */
    @Column(name = "remark")
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 1：草稿，2：已提交，3：处理中，4：不予处理 5，已结束
     */
    @Column(name = "status")
    @ApiModelProperty("1：草稿，2：已提交，3：处理中，4：不予处理 5，已结束")
    private Integer status;

    /**
     * 是否删除rn0：否，1：是
     */
    @Column(name = "is_del")
    @ApiModelProperty("是否删除rn0：否，1：是")
    private Integer isDel;

    /**
     * 0:未读 1:已读
     */
    @Column(name = "is_read")
    @ApiModelProperty("0:未读 1:已读")
    private Integer isRead;

    /**
     * 创建人id
     */
    @Column(name = "create_user")
    @ApiModelProperty("创建人id")
    private Long createUser;

    /**
     * 最后更新用户id
     */
    @Column(name = "update_user")
    @ApiModelProperty("最后更新用户id")
    private Long updateUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
