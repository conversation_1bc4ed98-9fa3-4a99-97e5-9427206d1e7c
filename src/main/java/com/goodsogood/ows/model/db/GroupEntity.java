package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 *
 * t_group 实体类
 *
 * <AUTHOR>
 * @create 2018-10-19 16:25
*/
@Data
@ApiModel
@Table(name = "t_group")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GroupEntity{
	
	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("id")
	@JsonProperty(value = "group_id")
	@Column(name = "group_id")
	private Long groupId;
	
	
	@ApiModelProperty("创建人所属组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;
	
	
	@ApiModelProperty("创建人所属组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;


	@ApiModelProperty(value = "组包含类型")
	@JsonProperty("types")
	@Valid
	private List<TypeEntity> types;

	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("创建人id")
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;
	
	
	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;


	@ApiModelProperty("最后更新人id")
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;
	
	/**
	 * 3.0.0新增
	 */
	@ApiModelProperty("区县id")
	@JsonProperty("region_id")
	@Column(name = "region_id")
	private Long regionId;

}

