package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@ApiModel
@Table(name = "t_cem_unique_list")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CemUniqueListEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "list_id")
    @Column(name = "list_id")
    private Long listId;

    @Column(name = "rule_id")
    private Integer ruleId;

    @Column(name = "unique_code")
    private String uniqueCode;

    public Long getListId() {
        return listId;
    }

    public void setListId(Long listId) {
        this.listId = listId;
    }

    public Integer getRuleId() {
        return ruleId;
    }

    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    public String getUniqueCode() {
        return uniqueCode;
    }

    public void setUniqueCode(String uniqueCode) {
        this.uniqueCode = uniqueCode;
    }
    public CemUniqueListEntity() {
    }
    public CemUniqueListEntity(Long listId, Integer ruleId, String uniqueCode) {
        this.listId = listId;
        this.ruleId = ruleId;
        this.uniqueCode = uniqueCode;
    }
}
