package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.joda.time.DateTime;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * t_meeting_history 实体类
 * <p>
 * 活动流转历史表
 *
 * <AUTHOR>
 * @create 2018-10-25 18:24
 */
@Data
@ApiModel
@Table(name = "t_meeting_history")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingHistoryEntity {

    @Id
    @JsonProperty(value = "meeting_history_id")
    @Column(name = "meeting_history_id")
    private Long meetingHistoryId;


    @JsonProperty(value = "meeting_id")
    @Column(name = "meeting_id")
    private Long meetingId;


    @ApiModelProperty("活动状态 1：发起审批中、2：发起未通过、3：活动待举办（待填报结果）、5：填报审查中（第一次）、6：填报未通过（第一次）、7：已提交、8：退回、9：活动已取消、10：填报审查中（第一次之后）、11：填报未通过（第一次之后）12：待复核（第二次提交并且审批通过）")
    @Column(name = "status")
    private Integer status;


    @ApiModelProperty("退回原因")
    @Column(name = "reason")
    private String reason;


    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;


    /**
     * 构造活动历史对象
     *
     * @param meetingId
     * @param uid
     * @param status
     * @param reason
     * @return
     */
    public MeetingHistoryEntity(long meetingId, long uid, Integer status, String reason) {
        this.createTime = DateTime.now().toDate();
        this.createUser = uid;
        this.status = status;
        this.meetingId = meetingId;
        this.reason = reason;
    }

    public MeetingHistoryEntity() {

    }


}

