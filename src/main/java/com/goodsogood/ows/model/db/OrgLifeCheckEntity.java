package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 组织生活会检视剖析实体
 * <AUTHOR>
 * @date 2021.11.23
 */

@Data
@ApiModel
@Table(name = "t_meeting_org_check")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgLifeCheckEntity {
    /**
     * check_id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "check_id")
    @ApiModelProperty("check_id")
    private Long checkId;

    /**
     * 民主生活会id
     */@Column(name = "life_id")
    @ApiModelProperty("组织生活会id")
    private Long lifeId;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 姓名
     */
    @Column(name = "username")
    @ApiModelProperty("姓名")
    private String username;

    /**
     * 用户所属组织
     */
    @Column(name = "org_id")
    @ApiModelProperty("用户所属组织")
    private Long orgId;

    /**
     * 所属组织名称
     */
    @Column(name = "org_name")
    @ApiModelProperty("所属组织名称")
    private String orgName;

    /**
     * 1：会前，2：会后
     */
    @Column(name = "step")
    @ApiModelProperty("1：会前，2：会后")
    private Integer step;

    /**
     * 是否删除 0：否，1：是
     */
    @Column(name = "is_del")
    @ApiModelProperty("是否删除 0：否，1：是")
    private Integer isDel;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;

    /**
     * last_change_user
     */
    @Column(name = "last_change_user")
    @ApiModelProperty("last_change_user")
    private Long lastChangeUser;
}
