package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 *
 * t_meeting_tag 实体类
 *
 * <AUTHOR>
 * @create 2021-11-08
*/
@Data
@ApiModel
@Table(name = "t_meeting_tag")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MeetingTagEntity {
	
	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("meeting_tag_id")
	@JsonProperty(value = "meeting_tag_id")
	@Column(name = "meeting_tag_id")
	private Long meetingTagId;

	@ApiModelProperty("活动编号")
	@JsonProperty(value = "meeting_id")
	@Column(name = "meeting_id")
	private Long meetingId;

	@JsonProperty(value = "tag_id")
	@Column(name = "tag_id")
	@ApiModelProperty("用户中心的标签编号")
	private Long tagId;

	@JsonProperty(value = "tag_name")
	@Column(name = "tag_name")
	@ApiModelProperty("用户中心的标签名称")
	private String tagName;

	@JsonProperty(value = "agenda_id")
	@Column(name = "agenda_id")
	@ApiModelProperty("关联的议程id")
	private Long agendaId;

	@JsonProperty(value = "last_update_time")
	@Column(name = "last_update_time")
	@ApiModelProperty(name = "最后一次操作时间")
	private Date lastUpdateTime;

	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	@ApiModelProperty("最后一次操作人")
	private Long lastChangeUser;


}

