package com.goodsogood.ows.model.db;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.joda.time.DateTime;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_meeting_result_hs 实体类
 *
 * <AUTHOR>
 * @create 2018-10-25 09:12
*/
@Data
@ApiModel
@Table(name = "t_meeting_result_hs")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingResultHsEntity {
	
	@Id
	@JsonProperty(value = "meeting_result_hs_id")
	@Column(name = "meeting_result_hs_id")
	private Long meetingResultHsId;
	
	
	@JsonProperty(value = "meeting_id")
	@Column(name = "meeting_id")
	private Long meetingId;
	
	
	@ApiModelProperty("修改的内容（json报文）")
	@JsonProperty(value = "update_content")
	@Column(name = "update_content")
	private String updateContent;
	
	
	@ApiModelProperty("修改之前的结果记录（json）")
	@JsonProperty(value = "update_before")
	@Column(name = "update_before")
	private String updateBefore;
	
	
	@Column(name = "remark")
	private String remark;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;


	/**
	 * 构建纪实修改历史
	 * @param meetingId
	 * @param uid
	 * @param beforeJson
	 * @param updateJson
	 */
	public MeetingResultHsEntity (long meetingId, long uid, String beforeJson, String updateJson) {
		this.createTime = DateTime.now().toDate();
		this.meetingId = meetingId;
		this.updateBefore = beforeJson;
		this.updateContent = updateJson;
		this.createUser = uid;
	}

	public MeetingResultHsEntity() {

	}
	
}

