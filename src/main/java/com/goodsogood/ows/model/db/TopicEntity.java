package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * 工作任务 实体类
 *
 * <AUTHOR>
 * @create 2018-10-19 16:39
*/
@Data
@ApiModel
@Table(name = "t_topic")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopicEntity {

	@Id
	@ApiModelProperty("任务id")
	@JsonProperty(value = "topic_id")
	@Column(name = "topic_id")
	private Long topicId;

	@Transient
	private Long id;

	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;

	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;


	@ApiModelProperty("任务名称")
	@Column(name = "name")
	@Length(min = 1, max = 100, message = "{Length.topic.name}")
	@NotNull(message = "{NotNull.topic.name}")
	private String name;


	@ApiModelProperty("描述")
	@Column(name = "description")
	@Length(min = 0, max = 5000, message = "{Length.topic.description}")
//	@NotNull(message = "{NotNull.topic.description}")
	private String description;

	@ApiModelProperty("完成期限类型 1：本月内；2：本季度内；3：本年内；4：自定义时间")
	@JsonProperty(value = "tlt")
	@Column(name = "time_limit_type")
	@NotNull(message = "{Range.topic.timeLimitType}")
	@Range(message = "{Range.topic.timeLimitType}")
	private Integer timeLimitType;


	@ApiModelProperty("开始时间")
	@JsonProperty(value = "start_time")
	@Column(name = "start_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@NotNull(message = "{NotNull.topic.startTime}")
	private Date startTime;


	@ApiModelProperty("结束时间")
	@JsonProperty(value = "end_time")
	@Column(name = "end_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@NotNull(message = "{NotNull.topic.endTime}")
	private Date endTime;


	@ApiModelProperty("是否删除 （0：默认 1：删除）")
	@JsonProperty(value = "is_del")
	@Column(name = "is_del")
	private Integer isDel;


	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;


	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;


	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;

	@NotNull(message = "{NotNull.topic.status}")
	@Range(min = 1, max = 2, message = "{Range.topic.status}")
	private Integer status;

	//************以下为对象***************

	/**
	 * 任务的附件
	 */
//	@Size(max = 9, message = "{Size.result.files}")
	private List<TopicFileEntity> files = new ArrayList<>();

	/**
	 * 任务的内容
	 */
	@Valid
	private List<TopicContentEntity> contents = new ArrayList<>();

	/**
	 * 组织
	 */
	private List<TopicOrgEntity> orgs = new ArrayList<>();

	/**
	 * 修改组织的时候，删除的组织
	 */
	@JsonProperty(value = "del_orgs")
	private List<TopicOrgEntity> delOrgs;


	/**
	 * 通知方式: 1.短信通知 2.微信通知（派发任务时的默认值）。自己添加时为空
	 *
	 * @since v3.0.1
	 */
	@ApiModelProperty("通知方式: 1.短信通知 2.微信通知（派发任务时的默认值）。自己添加时为空")
	@Range(min = 1, max = 2, message = "{Range.topic.noticeType}")
	@JsonProperty(value = "notice_type")
	@Column(name = "notice_type")
	private Integer noticeType;

	@ApiModelProperty("结束状态 1.进行中 2.已结束")
	@JsonProperty(value = "end_status")
	@Transient
	private Integer endStatus;

	@ApiModelProperty("接收组织总数")
	@JsonProperty(value = "org_num")
	@Transient
	private Integer orgNum;

	@ApiModelProperty("已完成组织数量")
	@JsonProperty(value = "finish_org_num")
	@Transient
	private Integer finishOrgNum;


	@ApiModelProperty("组织与任务关联id")
	@JsonProperty(value = "topic_org_id")
	@Transient
	private Long topicOrgId;

	@ApiModelProperty("组织任务完成情况（1：未完成 2：已完成 3:逾期未完成）")
	@JsonProperty(value = "to_status")
	@Transient
	private Integer toStatus;
}

