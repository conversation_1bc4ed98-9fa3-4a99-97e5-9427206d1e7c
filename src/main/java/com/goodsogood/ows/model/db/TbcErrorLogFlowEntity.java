package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 同步烟草信息不匹配流水表
 */
@Data
@ApiModel
@Table(name = "t_tbc_error_log_flow")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TbcErrorLogFlowEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("主键id")
    private Long errorId;
    @ApiModelProperty("活动id")
    private Long meetingId;
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("报错原因")
    private String reason;
    @ApiModelProperty("请求信息")
    private String request;
    @ApiModelProperty("请求内容")
    private String content;//请求参数
    @ApiModelProperty("请求头")
    private String header;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
