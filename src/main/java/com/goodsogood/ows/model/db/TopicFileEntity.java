package com.goodsogood.ows.model.db;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 *
 * t_topic_file 实体类
 *
 * <AUTHOR>
 * @create 2018-10-22 14:05
 */
@Data
@ApiModel
@Table(name = "t_topic_file")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopicFileEntity extends BaseCheckEntity {

	@Id
	@ApiModelProperty("附件id")
	@JsonProperty(value = "topic_file_id")
	@Column(name = "topic_file_id")
	private Long topicFileId;


	@ApiModelProperty("任务id")
	@JsonProperty(value = "topic_id")
	@Column(name = "topic_id")
	private Long topicId;
	
	
	@ApiModelProperty("附件名称")
	@Column(name = "name")
	private String name;
	
	
	@ApiModelProperty("附件路径")
	@Column(name = "path")
	private String path;
	
	
	@ApiModelProperty("文件原名称")
	@JsonProperty(value = "file_name")
	@Column(name = "file_name")
	private String fileName;
	
	
	@ApiModelProperty("文件大小（byte）")
	@Column(name = "size")
	private Long size;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;
	
	
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;

	/** 用于返回给前端 */
	@Transient
	@JsonProperty(value = "is_del")
	private Integer isDel = 0;
	
}

