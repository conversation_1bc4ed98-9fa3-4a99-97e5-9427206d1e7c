package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 党员民主评议
 * @date 2019/12/26
 */
@Data
@ApiModel
@Table(name = "t_meeting_user_comment")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCommentEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "user_comment_id")
    @Column(name = "user_comment_id")
    private Long userCommentId;

    @ApiModelProperty("评议年度")
    @JsonProperty(value = "review_year")
    @Column(name = "review_year")
    private Integer reviewYear;

    @ApiModelProperty("党员用户ID")
    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    private Long userId;

    @ApiModelProperty("党员用户姓名")
    @JsonProperty(value = "user_name")
    @Column(name = "user_name")
    private String userName;

    @ApiModelProperty("党员所属组织ID")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("党员所属组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;

    @ApiModelProperty("党员所属组织层级关系")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String orgLevel;

    @ApiModelProperty("评议等级(1-优秀、2-合格、3-基本合格、4-不合格)")
    @JsonProperty(value = "rating")
    @Column(name = "rating")
    private Integer rating;

    @ApiModelProperty("处理意见(1-限期整改，2-除名)")
    @JsonProperty(value = "deal_opinion")
    @Column(name = "deal_opinion")
    private Integer dealOpinion;

    @ApiModelProperty("附加说明")
    @JsonProperty(value = "additional_information")
    @Column(name = "additional_information")
    private String additionalInformation;

    @ApiModelProperty("数据状态(1-正常，2-删除)")
    @JsonProperty(value = "status")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    private Long lastChangeUser;

    @ApiModelProperty("修改时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "region_id")
    @JsonProperty("region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;
}
