package com.goodsogood.ows.model.db

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.*
import javax.persistence.Column
import javax.persistence.GeneratedValue
import javax.persistence.Id
import javax.persistence.Table

/**
 * 谈心谈话主表
 */
@ApiModel
@Table(name = "t_meeting_talk")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingTalkEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("谈心谈话主键ID")
    @JsonProperty(value = "talk_id")
    @Column(name = "talk_id")
    var talkId: Long? = null

    @ApiModelProperty("组织ID")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    var orgId: Long? = null

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    var orgName: String? = null

    @ApiModelProperty("组织层级关系")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    var orgLevel: String? = null

    @ApiModelProperty("""
        0：默认
        1：成员之间
        2：成员与部门
        3：成员与支部
        4：成员与联系点
        5：个人访谈
        6：党支部书记与班子成员之间
        7：党支部班子成员之间（支部委员之间）
        8：班子成员与党员之间
        9：党员之间（党员与党员之间）
        10：班子成员之间
        11：班子和党员之间
        12：委员和党员之间
        13: 家庭发生重大变故
        14: 出现重大困难
        15: 身心健康存在突出问题
        16: 受到处分处置
        17: 收到不良反应
        18: 其他
    """)
    @JsonProperty(value = "talk_type")
    @Column(name = "talk_type")
    var talkType: Int = 0

    @ApiModelProperty("谈心谈话地址")
    var location: String? = null

    @ApiModelProperty("谈心谈话是否提交： 1 - 是，0 - 否")
    @JsonProperty(value = "is_submit")
    @Column(name = "is_submit")
    var isSubmit: Int? = null

    @ApiModelProperty("开始时间")
    @JsonProperty(value = "begin_time")
    @Column(name = "begin_time")
    var beginTime: LocalDateTime? = null

    @ApiModelProperty("结束时间")
    @JsonProperty(value = "end_time")
    @Column(name = "end_time")
    var endTime: LocalDateTime? = null

    @ApiModelProperty("状态： 1-正常， 0-删除")
    @JsonProperty(value = "status")
    @Column(name = "status")
    var status: Int? = null

    @ApiModelProperty("来源：0 - 无来源 1 - 民族生活会会前，2 - 民主生活会会后 3 - 组织生活会会前 4 - 组织生活会会后")
    @JsonProperty(value = "source")
    @Column(name = "source")
    var source: Int = 0

    @ApiModelProperty("来源第三方ID")
    @JsonProperty(value = "source_id")
    @Column(name = "source_id")
    var sourceId: Long? = null

    @ApiModelProperty("复制源ID")
    @JsonProperty(value = "copy_id")
    @Column(name = "copy_id")
    var copyId: Long? = 0

    @ApiModelProperty("区县ID")
    @JsonProperty(value = "region_id")
    @Column(name = "region_id")
    var regionId: Long? = null

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    var createTime: Date? = null

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    var updateTime: Date? = null

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    var createUser: Long? = null

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    var lastChangeUser: Long? = null

    override fun toString(): String {
        return "MeetingTalkEntity(talkId=$talkId, orgId=$orgId, orgName=$orgName, orgLevel=$orgLevel, talkType=$talkType, location=$location, isSubmit=$isSubmit, beginTime=$beginTime, endTime=$endTime, status=$status, source=$source, sourceId=$sourceId, copyId=$copyId, regionId=$regionId, createTime=$createTime, updateTime=$updateTime, createUser=$createUser, lastChangeUser=$lastChangeUser)"
    }

}

/**
 * 谈心谈话关联内容
 */
@ApiModel
@Table(name = "t_meeting_talk_content")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingTalkContentEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("谈心谈话内容主键ID")
    @JsonProperty(value = "talk_content_id")
    @Column(name = "talk_content_id")
    var talkContentId: Long? = null

    @ApiModelProperty("谈心谈话主键ID")
    @JsonProperty(value = "talk_id")
    @Column(name = "talk_id")
    var talkId: Long? = null

    @ApiModelProperty("谈心谈话标题")
    var title: String? = null

    @ApiModelProperty("谈心谈话内容")
    var content: String? = null

    @ApiModelProperty("状态： 1-正常， 0-删除")
    var status: Int? = null

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    var createTime: Date? = null

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    var updateTime: Date? = null

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    var createUser: Long? = null

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    var lastChangeUser: Long? = null

    constructor()
    constructor(talkId: Long?, title: String?, content: String?, status: Int?, createTime: Date?, createUser: Long?) {
        this.talkId = talkId
        this.title = title
        this.content = content
        this.status = status
        this.createTime = createTime
        this.createUser = createUser
    }


}

/**
 * 谈心谈话关联人员
 */
@ApiModel
@Table(name = "t_meeting_talk_link")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingTalkLinkEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("谈心谈话关联主键ID")
    @JsonProperty(value = "talk_link_id")
    @Column(name = "talk_link_id")
    var talkLinkId: Long? = null

    @ApiModelProperty("谈心谈话主键ID")
    @JsonProperty(value = "talk_id")
    @Column(name = "talk_id")
    var talkId: Long? = null

    @ApiModelProperty("谈话用户ID")
    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    var userId: Long? = null

    @ApiModelProperty("谈话用户姓名")
    @JsonProperty(value = "username")
    @Column(name = "username")
    var username: String? = null

    @ApiModelProperty("类型：1-谈话人，2-被谈话人")
    @JsonProperty(value = "type")
    @Column(name = "type")
    var type: Int? = null

    @ApiModelProperty("状态： 1-正常， 0-删除")
    var status: Int? = null

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    var createTime: Date? = null

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    var updateTime: Date? = null

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    var createUser: Long? = null

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    var lastChangeUser: Long? = null



    constructor(
        talkId: Long?,
        userId: Long?,
        username: String?,
        type: Int?,
        status: Int?,
        createTime: Date?,
        createUser: Long?
    ) {
        this.talkId = talkId
        this.userId = userId
        this.username = username
        this.type = type
        this.status = status
        this.createTime = createTime
        this.createUser = createUser
    }

    constructor()
}

/**
 * 谈心谈话与民主生活会的关联关系表
 */
@ApiModel
@Table(name = "t_meeting_life_talk")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingLifeTalkEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("谈心谈话与民主生活会的关联关系表主键ID")
    @JsonProperty(value = "life_talk_id")
    @Column(name = "life_talk_id")
    var lifeTalkId: Long? = null

    @ApiModelProperty("民主生活会主键ID")
    @JsonProperty(value = "life_id")
    @Column(name = "life_id")
    var lifeId: Long? = null

    @ApiModelProperty("谈心谈话主键ID")
    @JsonProperty(value = "talk_id")
    @Column(name = "talk_id")
    var talkId: Long? = null

    @ApiModelProperty("谈心谈话主键ID")
    @JsonProperty(value = "step")
    @Column(name = "step")
    var step: Long? = null

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    var createTime: Date? = null

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    var updateTime: Date? = null

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    var createUser: Long? = null

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    var lastChangeUser: Long? = null
}