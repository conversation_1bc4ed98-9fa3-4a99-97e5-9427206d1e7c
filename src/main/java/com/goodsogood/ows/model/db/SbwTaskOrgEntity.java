package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 南岸区网信办任务组织关联实体
 * <AUTHOR>
 * @date 2021.07.29
 */
@Data
@ApiModel
@Table(name = "t_meeting_sbw_task_org")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SbwTaskOrgEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @Column(name = "to_id")
    private Long toId;

    @ApiModelProperty("id")
    @Column(name = "task_id")
    private Long taskId;

    @ApiModelProperty("区县id")
    @Column(name = "region_id")
    private Long regionId;

    @ApiModelProperty("组织id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name")
    private String orgName;

    @ApiModelProperty("任务标题")
    @Column(name = "title")
    private String title;

    @ApiModelProperty("操作类型" +
            "1：已提交接受\n" +
            "2：已提交拒绝\n" +
            "3：已退回\n" +
            "4：已拒绝\n" +
            "5：已同意")
    @Column(name = "type")
    private Integer type;

    @ApiModelProperty("任务状态。2：未开始，3：进行中，4：已结束，5：已拒绝")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty("0:未读 1:已读")
    @Column(name = "is_read")
    private Integer isRead;

    @ApiModelProperty("开始时间")
    @Column(name = "begin_time")
    private Date beginTime;

    @ApiModelProperty("截至时间")
    @Column(name = "end_time")
    private Date endTime;

    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @Column(name = "update_time")
    private Date updateTime;
}
