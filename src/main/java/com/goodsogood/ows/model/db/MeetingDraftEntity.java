package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 记实草稿
 *
 * <AUTHOR>
 * @date 2020/12/14
 */
@Data
@ApiModel
@Table(name = "t_meeting_draft")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class MeetingDraftEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("draft_id")
    @JsonProperty(value = "draft_id")
    @Column(name = "draft_id")
    private Long draftId;

    @ApiModelProperty("组织id")
    @Column(name = "org_id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("用户id")
    @Column(name = "user_id")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("区县id")
    @Column(name = "region_id")
    @JsonProperty(value = "region_id")
    private Long regionId;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("类型 0-发起活动，1-已有活动之后录入活动，2-直接录入活动")
    private Integer type;

    @ApiModelProperty("会议id")
    @Column(name = "meeting_id")
    private Long meetingId;

    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    @JsonProperty(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @Column(name = "update_time")
    @JsonProperty(value = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
