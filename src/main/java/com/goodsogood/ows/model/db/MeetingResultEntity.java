package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * t_meeting_result 实体类
 *
 * 活动纪实表
 *
 * <AUTHOR>
 * @create 2018-10-25 15:40
*/
@Data
@ApiModel
@Table(name = "t_meeting_result")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingResultEntity {

	public Date getUpdateTime() {
		return updateTime==null? createTime: updateTime;
	}

	@Id
	@ApiModelProperty("纪实情况id")
	@JsonProperty(value = "meeting_reslut_id")
	@Column(name = "meeting_reslut_id")
	private Long meetingReslutId;


    @ApiModelProperty("活动id")
	@JsonProperty(value = "meeting_id")
	@Column(name = "meeting_id")
	private Long meetingId;
	
	
	@ApiModelProperty("当前组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;
	
	
	@ApiModelProperty("0：默认 1：删除")
	@JsonProperty(value = "is_del")
	@Column(name = "is_del")
	private Integer isDel;
	
	
	@ApiModelProperty("是否需要审批（0：不需要，1：需要）")
	@JsonProperty(value = "must_approve")
	@Column(name = "must_approve")
	@Min(value = 0, message = "{Min.result.approve-status}")
	@Max(value = 1, message = "{Max.result.approve-status}")
	@NotNull(message = "{NotNull.result.must-approve}")
	private Integer mustApprove;
	
	
	@ApiModelProperty("审批状态（1:正常;2:审批中,3:审批不通过）")
	@JsonProperty(value = "approve_status")
	@Column(name = "approve_status")
	private Integer approveStatus;
	
	
	@ApiModelProperty("工作流id")
	@JsonProperty(value = "workflow_id")
	@Column(name = "workflow_id")
	private Long workflowId;
	
	
	@ApiModelProperty("工作流任务id")
	@JsonProperty(value = "workflow_task_id")
	@Column(name = "workflow_task_id")
	private Long workflowTaskId;
	
	
	@ApiModelProperty("工作流名称")
	@JsonProperty(value = "workflow_name")
	@Column(name = "workflow_name")
	private String workflowName;
	
	
	@ApiModelProperty("提交时间")
	@JsonProperty(value = "submit_time")
	@Column(name = "submit_time")
	private Date submitTime;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;


	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;


	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;

	/**
	 * 决议
	 */
	@Transient
	@Length(max = 5000, message = "{Length.result.resolution}")
	private String resolution;

	/**
	 * 用于接收决议的附件
	 */
//	@Size(max = 9, message = "{Size.result.files}")
	@Transient
	private List<MeetingResolutionFileEntity> files = new ArrayList<>();


	/**
	 * 纪实结果附件附件
	 *
	 * @since v3.0.1
	 */
	@JsonProperty(value = "result_files")
	private List<MeetingResultFileEntity> resultFiles = new ArrayList<>();

}

