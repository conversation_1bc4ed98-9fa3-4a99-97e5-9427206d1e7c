package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * t_meeting_leave 请假记录
 *
 * <AUTHOR>
 * @create 2018/10/29 15:19
 */
@Data
@ApiModel
@Table(name = "t_meeting_leave")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingLeaveEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "meeting_leave_id")
    @Column(name = "meeting_leave_id")
    private Long meetingLeaveId;


    @ApiModelProperty("活动id")
    @JsonProperty(value = "meeting_id")
    @Column(name = "meeting_id")
    private Long meetingId;


    @ApiModelProperty("请假用户id")
    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    private Long userId;


    @ApiModelProperty("请假类型（1：因公请假 2：因私请假）")
    @JsonProperty(value = "type")
    @Column(name = "type")
    private Short type;

    @ApiModelProperty("请假原因")
    @JsonProperty(value = "reason")
    @Column(name = "reason")
    private String reason;

    @ApiModelProperty("审批类型（0:初始  1：待审批/审批中/提交 2：请假审批通过 3：请假审批不通过  4：销假审批中 5：已撤销  6：销假审批不通过 7.销假审批通过）")
    @JsonProperty(value = "status")
    @Column(name = "status")
    private Short status;


    @ApiModelProperty("审批人")
    @JsonProperty(value = "check_user_id")
    @Column(name = "check_user_id")
    private Long checkUserId;

    @ApiModelProperty("审批人")
    @JsonProperty(value = "check_user_name")
    @Column(name = "check_user_name")
    private String checkUserName;

    @ApiModelProperty("请假审批时间")
    @JsonProperty(value = "check_time")
    @Column(name = "check_time")
    private LocalDateTime checkTime;


    @ApiModelProperty("请假审批不通过原因")
    @JsonProperty(value = "check_result")
    @Column(name = "check_result")
    private String checkResult;

    @ApiModelProperty("销假原因")
    @JsonProperty(value = "off_leave_reason")
    @Column(name = "off_leave_reason")
    private String offLeaveReason;

    @ApiModelProperty("销假审批人")
    @JsonProperty(value = "off_check_user_id")
    @Column(name = "off_check_user_id")
    private Long offCheckUserId;

    @ApiModelProperty("销假审批人")
    @JsonProperty(value = "off_check_user_name")
    @Column(name = "off_check_user_name")
    private String offCheckUserName;

    @ApiModelProperty("销假审批时间")
    @JsonProperty(value = "off_check_time")
    @Column(name = "off_check_time")
    private LocalDateTime offCheckTime;


    @ApiModelProperty("销假审批不通过原因")
    @JsonProperty(value = "off_check_result")
    @Column(name = "off_check_result")
    private String offCheckResult;


    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private LocalDateTime createTime;



    @ApiModelProperty("修改时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人id")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    private Long lastChangeUser;

    @ApiModelProperty("删除标志 0-正常 1-删除")
    @JsonProperty(value = "del_status")
    @Column(name = "del_status")
    private Integer delStatus = 0;

}
