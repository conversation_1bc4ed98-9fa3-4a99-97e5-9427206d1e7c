package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * t_meeting_org_period 实体类
 * <p>
 * 会议关联的支委会届次
 *
 * <AUTHOR>
 * @create 2019-09-19 10:57
 */
@Data
@ApiModel
@Table(name = "t_meeting_org_period")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingOrgPeriodEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "meeting_org_period_id")
    @Column(name = "meeting_org_period_id")
    private Long meetingOrgPeriodId;


    @JsonProperty(value = "meeting_id")
    @Column(name = "meeting_id")
    private Long meetingId;


    @ApiModelProperty("届次ID")
    @JsonProperty(value = "period_id")
    @Column(name = "period_id")
    @NotNull(message = "{NotNull.OrgPeriod.periodId}")
    private Long periodId;


    @ApiModelProperty("状态 1.有效; 2.删除")
    @Column(name = "status")
    private Short status;


    @ApiModelProperty("届次开始时间")
    @JsonProperty(value = "start_time")
    @Column(name = "start_time")
    @JsonFormat(pattern = "yyyy年M月d日", timezone = "GMT+8")
    @NotNull(message = "{NotNull.OrgPeriod.startTime}")
    private Date startTime;


    @ApiModelProperty("届次结束时间")
    @JsonProperty(value = "end_time")
    @Column(name = "end_time")
    @JsonFormat(pattern = "yyyy年M月d日", timezone = "GMT+8")
    @NotNull(message = "{NotNull.OrgPeriod.endTime}")
    private Date endTime;


    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;


    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;

}

