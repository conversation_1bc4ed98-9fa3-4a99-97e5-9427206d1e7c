package com.goodsogood.ows.model.db;

import cn.hutool.core.date.DateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.goodsogood.ows.model.vo.FileForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * t_meeting_work_report 实体类
 */

@Data
@ApiModel
@Table(name = "t_meeting_work_report")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReportEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "work_report_id")
    @Column(name = "work_report_id")
    private Long workReportId;

    @ApiModelProperty("汇报类型(1.工作，2.意识形态)")
    @JsonProperty(value = "report_type")
    @Column(name = "report_type")
    private Integer reportType;

    @ApiModelProperty("领导信息")
    @JsonProperty(value = "leader")
    @Column(name = "leader")
    private String leader;

    @ApiModelProperty("领导姓名")
    @JsonProperty(value = "leader_names")
    @Column(name = "leader_names")
    private String leaderNames;

    @ApiModelProperty("领导id")
    @JsonProperty(value = "leader_ids")
    @Column(name = "leader_ids")
    private String leaderIds;

    @ApiModelProperty("汇报部门")
    @JsonProperty(value = "department")
    @Column(name = "department")
    private String department;

    @ApiModelProperty("汇报人")
    @JsonProperty(value = "reporter")
    @Column(name = "reporter")
    private String reporter;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty("汇报时间")
    @JsonProperty(value = "report_time")
    @Column(name = "report_time")
    private LocalDateTime reportTime;

    @ApiModelProperty("年度")
    @JsonProperty(value = "year")
    @Column(name = "year")
    private Integer year;

    @ApiModelProperty("半年区间")
    @JsonProperty(value = "half_year_interval")
    @Column(name = "half_year_interval")
    private Integer halfYearInterval;

    @ApiModelProperty("汇报记录")
    @JsonProperty(value = "report_record")
    @Column(name = "report_record")
    private String reportRecord;

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    @JsonProperty(value = "update_user")
    @Column(name = "update_user")
    private Long updateUser;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("文件")
    @JsonProperty(value = "attachment")
    @Column(name = "attachment")
    private String attachment;

    @ApiModelProperty("单位id")
    @JsonProperty(value = "unit_id")
    @Column(name = "unit_id")
    private String unitId;

    @ApiModelProperty("单位名称")
    @JsonProperty(value = "unit_name")
    @Column(name = "unit_name")
    private String unitName;
}
