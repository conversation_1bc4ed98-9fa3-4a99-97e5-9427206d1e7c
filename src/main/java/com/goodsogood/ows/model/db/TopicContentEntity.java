package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * t_topic_content 实体类
 *
 * <AUTHOR>
 * @create 2018-10-22 14:10
*/
@Data
@ApiModel
@Table(name = "t_topic_content")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopicContentEntity {
	
	@Id
	@ApiModelProperty("内容id")
	@JsonProperty(value = "content_id")
	@Column(name = "content_id")
	private Long contentId;

	@Transient
	private Long id;

	@ApiModelProperty("任务id")
	@JsonProperty(value = "topic_id")
	@Column(name = "topic_id")
	private Long topicId;
	
	
	@ApiModelProperty("内容类型（1 ：文本内容  2：单项选择  3：多项选择）")
	@Column(name = "type")
	private Integer type;
	
	
	@ApiModelProperty("内容名称")
	@Column(name = "name")
	@NotNull(message = "{NotNull.topic.content.name}")
	@Length(min = 1, max = 500, message = "{Length.topic.content.name}")
	private String name;
	
	
	@ApiModelProperty("内容描述")
	@Column(name = "description")
	private String description;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;
	
	
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;

	/** 选项 */
	private List<TopicOptsEntity> opts = new ArrayList<>();

	/** 用于返回给前端 */
	@Transient
	@JsonProperty(value = "is_del")
	private Integer isDel = 0;

     /** 内容回答的详情 */
     @Transient
     @JsonProperty(value = "ans_cnt")
	 @Length(max = 5000, message = "{Length.topic.report.content}")
     private String ansCnt;

     /** 内容回答的选项 */
     @Transient
     private List<Long> answer = new ArrayList<>();

	/** 问答题的附件 */
	@Transient
	@JsonProperty(value = "files")
//	@Size(max = 9, message = "{Size.result.files}")
     private List<TopicLogFileEntity> ansFiles = new ArrayList<>();

}

