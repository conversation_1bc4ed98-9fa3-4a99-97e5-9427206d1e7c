package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.io.Serializable;
import java.util.Map;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.GeneratedValue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @description 补签列表
 * @date 2021-11-10
 */
@Entity
@Data
@Table(name = "t_meeting_wait_sign")
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MeetingWaitSignEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "wait_sign_id")
    private Long waitSignId;

    /**
     * meeting_id
     */
    @Column(name = "meeting_id")
    private Long meetingId;

    /**
     * meeting_user_id
     */
    @Column(name = "meeting_user_id")
    private Long meetingUserId;

    /**
     * user_id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 是否有效 1:有效 0:无效
     */
    @Column(name = "is_del")
    private Short isDel;

    /**
     * 数据状态 1:待补学 2:补学完成 3:超期补学 4:活动变更 5:草稿
     */
    @Column(name = "type")
    private Short type;

    /**
     * 是否是该用户最新的补学记录 1:是 0:否
     */
    @Column(name = "is_now")
    private Short isNow;

    /**
     * 补学内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 照片文件地址 英文逗号分隔
     */
    @Column(name = "img_file")
    private String imgFile;

    /**
     * 附件地址 英文逗号分隔
     */
    @Column(name = "file")
    private String file;

    /**
     * create_time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update_time
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "sign_time")
    private Date signTime;

    public MeetingWaitSignEntity() {
    }

}