package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * t_meeting_org_change_log 实体类
 * <p>
 * 自动发放的活动类型 限制的组织类型
 *
 * <AUTHOR>
 * @create 2019-04-18 14:29
 */
@Data
@ApiModel
@Table(name = "t_meeting_org_change_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingOrgChangeLogEntity {
    /**
     * 是否处理 0 未处理（默认）
     */
    public static final short PROCESS_TAG_NO = 0;

    /**
     * 是否处理 1 已处理
     */
    public static final short PROCESS_TAG_YES = 1;

    /**
     * 操作类型： 1:新增　 2:修改 3:删除
     */
    public static final short TYPE_ADD = 1;

    /**
     * 操作类型： 1:新增　 2:修改 3:删除
     */
    public static final short TYPE_UPDATE = 2;

    /**
     * 操作类型： 1:新增　 2:修改 3:删除
     */
    public static final short TYPE_DEL = 3;
    
    /**
     * 1:是
     */
    public static final short YES = 1;

    /**
     * 2:否
     */
    public static final short NO = 2;

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "meeting_org_change_log_id")
    @Column(name = "meeting_org_change_log_id")
    private Long meetingOrgChangeLogId;


    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;


    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;


    @ApiModelProperty("对应的组织类型")
    @JsonProperty(value = "org_type_child")
    @Column(name = "org_type_child")
    private Integer orgTypeChild;

    @ApiModelProperty("操作类型： 1:新增　 2:修改 3:删除")
    @JsonProperty(value = "type")
    @Column(name = "type")
    private Short type;


    @ApiModelProperty("是否离退休 　　1-是 2-否")
    @JsonProperty(value = "is_retire")
    @Column(name = "is_retire")
    private Short isRetire;


    @ApiModelProperty("当前月是否有党小组是否存在 　1:是　2:否")
    @Column(name = "party_group")
    private Short group;


    @ApiModelProperty("当前月是否有支委会是否存在　 1:是 2:否")
    @Column(name = "period")
    private Short period;


    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;


    @ApiModelProperty("是否处理 0 未处理（默认）1 已处理")
    @JsonProperty(value = "process_tag")
    @Column(name = "process_tag")
    private Short processTag;

    @ApiModelProperty("组织树父级路径")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String orgLevel;
}

