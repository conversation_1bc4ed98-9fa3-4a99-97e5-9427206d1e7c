package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 南岸网信办任务操作流水实体
 * <AUTHOR>
 * @date 2021.07.29
 */
@Data
@ApiModel
@Table(name = "t_meeting_sbw_task_flow")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SbwTaskFlowEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @Column(name = "flow_id")
    private Long flowId;

    @ApiModelProperty("id")
    @Column(name = "task_id")
    private Long taskId;

    @ApiModelProperty("区县id")
    @Column(name = "region_id")
    private Long regionId;

    @ApiModelProperty("组织id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("操作用户id")
    @Column(name = "user_id")
    private Long userId;

    @ApiModelProperty("操作用户所属组织id")
    @Column(name = "operate_org_id")
    private Long operateOrgId;

    @ApiModelProperty("操作用户所属组织名")
    @Column(name = "operate_org_name")
    private String operateOrgName;

    @ApiModelProperty("操作类型" +
            "1：已提交接受\n" +
            "2：已提交拒绝\n" +
            "3：已退回\n" +
            "4：已拒绝\n" +
            "5：已同意")
    @Column(name = "type")
    private Integer type;

    @ApiModelProperty("拒绝原因/审核意见")
    @Column(name = "content")
    private String content;

    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    private Date createTime;
}
