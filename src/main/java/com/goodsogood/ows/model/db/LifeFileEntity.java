package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 民主生活会附件表实体
 * <AUTHOR>
 * @date 2021.11.23
 */

@Data
@ApiModel
@Table(name = "t_meeting_life_file")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeFileEntity {

    /**
     * life_file_id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "life_file_id")
    @ApiModelProperty("life_file_id")
    private Long lifeFileId;

    /**
     * 文件id
     */
    @Column(name = "file_id")
    @ApiModelProperty("文件id")
    private Long fileId;

    /**
     * 文件名
     */
    @Column(name = "file_name")
    @ApiModelProperty("文件名")
    private String fileName;

    /**
     * 下载文件名-重命名后的fileName
     */
    @Column(name = "file_name_down")
    @ApiModelProperty("下载文件名")
    private String fileNameDown;

    /**
     * 民主生活会id
     */
    @Column(name = "life_id")
    @ApiModelProperty("民主生活会id")
    private Long lifeId;

    /**
     * 是否上报 0：否，1：是
     */
    @Column(name = "is_submit")
    @ApiModelProperty("是否上报 0：否，1：是")
    private Integer isSubmit;

    /**
     * 上传人id
     */
    @Column(name = "user_id")
    @ApiModelProperty("上传人id")
    private Long userId;

    /**
     * 上传人姓名
     */
    @Column(name = "username")
    @ApiModelProperty("上传人姓名")
    private String username;

    /**
     * type
     */
    @Column(name = "type")
    @ApiModelProperty("model_id")
    private Integer type;

    /**
     * 精确数据id
     */
    @Column(name = "data_id")
    @ApiModelProperty("精确数据id")
    private Long dataId;

    /**
     * 1：会前，2：会后
     */
    @Column(name = "step")
    @ApiModelProperty("1：会前，2：会后")
    private Integer step;

    /**
     * 是否直接上传 0：否，1：是
     */
    @Column(name = "is_direct")
    @ApiModelProperty("是否直接上传 0：否，1：是")
    private Integer isDirect;

    /**
     * url
     */
    @Column(name = "url")
    @ApiModelProperty("url")
    private String url;

    /**
     * 是否删除 0：否，1：是
     */
    @Column(name = "is_del")
    @ApiModelProperty("是否删除 0：否，1：是")
    private Integer isDel;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * last_change_user
     */
    @Column(name = "last_change_user")
    @ApiModelProperty("last_change_user")
    private Long lastChangeUser;

}
