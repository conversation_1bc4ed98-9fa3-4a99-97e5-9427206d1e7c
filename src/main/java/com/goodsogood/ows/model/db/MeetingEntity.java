package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.persistence.*;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * t_meeting 实体类
 * <p>
 * 活动表
 *
 * <AUTHOR>
 * @create 2018-10-25 10:53
 */
@Data
@ApiModel
@Table(name = "t_meeting")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "meeting_id")
    @Column(name = "meeting_id")
    private Long meetingId;


    @ApiModelProperty("创建人所属组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;


    @ApiModelProperty("活动所属组织生活的派发组织id")
    @JsonProperty(value = "p_org_id")
    @Column(name = "p_org_id")
    private Long pOrgId;

    @ApiModelProperty("对应的审批流程类型id 如果传-999则为不需要审批")
    @JsonProperty(value = "workflow_id")
    @Column(name = "workflow_id")
    private Long workflowId;


    @ApiModelProperty("实际的审批任务流的id")
    @JsonProperty(value = "workflow_task_id")
    @Column(name = "workflow_task_id")
    private Long workflowTaskId;


    @ApiModelProperty("对应的审批流程类型名称 默认名称为自定义")
    @JsonProperty(value = "workflow_name")
    @Column(name = "workflow_name")
    private String workflowName;


    @ApiModelProperty("创建人所属组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;


    @ApiModelProperty("活动名称")
    @Column(name = "name")
    @NotBlank(message = "{NotBlank.meeting.name}")
    @Length(max = 50, message = "{Length.meeting.name}")
    private String name;


    @ApiModelProperty("活动类型")
    @Column(name = "types")
    @NotBlank(message = "{NotBlank.meeting.types}")
    private String types;

    @ApiModelProperty("活动总时长")
    @JsonProperty(value = "total_hours")
    @Column(name = "total_hours")
    private Double totalHours;

    @ApiModelProperty("理论学习时长")
    @JsonProperty(value = "theory_learn")
    @Column(name = "theory_learn")
    private Double theoryLearn;

    @ApiModelProperty("活动地点")
    @Column(name = "address")
    @NotBlank(message = "{NotBlank.meeting.address}")
    @Length(max = 200, message = "{Length.meeting.address}")
    private String address;


    @ApiModelProperty("gps来源类型:1.高德/微信（GCJ02）、2.百毒（百毒坐标体系）、3.google（真实坐标）、4.gps设备（真实坐标）")
    @JsonProperty(value = "gps_type")
    @Column(name = "gps_type")
//	@NotNull(message = "{NotNull.meeting.gpsType}")
    @Range(min = 1, max = 4, message = "{Range.meeting.gpsType}")
    private Short gpsType;


    @ApiModelProperty("经度")
    @Column(name = "lng")
//	@NotNull(message = "{NotNull.meeting.lng}")
    private Double lng;


    @ApiModelProperty("纬度")
    @Column(name = "lat")
//	@NotNull(message = "{NotNull.meeting.lat}")
    private Double lat;


    @ApiModelProperty("实际的经度（纠偏以后）")
    @JsonProperty(value = "real_lng")
    @Column(name = "real_lng")
    private Double realLng;


    @ApiModelProperty("实际纬度（纠偏以后）")
    @JsonProperty(value = "real_lat")
    @Column(name = "real_lat")
    private Double realLat;


    @ApiModelProperty("举办时间")
    @JsonProperty(value = "start_time")
    @Column(name = "start_time")
    @NotNull(message = "{NotNull.meeting.startTime}")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("是否需要签到。0：不需要；1：需要")
    @JsonProperty(value = "is_sign_in")
    @Column(name = "is_sign_in")
    @NotNull(message = "{NotNull.meeting.isSignIn}")
    @Range(min = 0, max = 1, message = "{Range.meeting.isSignIn}")
    private Short isSignIn;

    @ApiModelProperty("签到方式。0：手写签到；1：扫码签到；3：GPS定位签到；4：人脸识别")
    @JsonProperty(value = "sign_in_way")
    @Column(name = "sign_in_way")
    private Short signInWay;

    @ApiModelProperty("是否需要填写决议 0：不需要；1：需要")
//	@NotNull(message = "{NotNull.meeting.isWResolution}")
//	@Range(min = 0, max = 1, message = "{Range.meeting.isWResolution}")
    @JsonProperty(value = "is_w_resolution")
    @Column(name = "is_w_resolution")
    private Short isWResolution;


    @ApiModelProperty("签到开始时间")
    @JsonProperty(value = "sign_start_time")
    @Column(name = "sign_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date signStartTime;


    @ApiModelProperty("签到截止时间")
    @JsonProperty(value = "sign_end_time")
    @Column(name = "sign_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date signEndTime;


    @ApiModelProperty("是否需要审批：0：不需要，1：需要")
    @JsonProperty(value = "must_approve")
    @Column(name = "must_approve")
    @NotNull(message = "{NotNull.meeting.mustApprove}")
    @Range(min = 0, max = 1, message = "{Range.meeting.mustApprove}")
    private Short mustApprove;


    @ApiModelProperty("决议")
    @Column(name = "resolution")
    private String resolution;


    @ApiModelProperty("活动状态 1：发起审批中、2：发起未通过、3：活动待举办（待填报结果）、5：填报审查中（第一次）、6：填报未通过（第一次）、7：已提交、8：退回、9：活动已取消、10：填报审查中（第一次之后）、11：填报未通过（第一次之后）12：待复核（第二次提交并且审批通过）13：通过（第一次考核就通过）14：通过（第一次考核被退回后的通过）")
    @Column(name = "status")
    private Short status;

    @ApiModelProperty("活动状态 1：发起审批中、2：发起未通过、3：活动待举办（待填报结果）、5：填报审查中（第一次）、6：填报未通过（第一次）、7：已提交、8：退回、9：活动已取消、10：填报审查中（第一次之后）、11：填报未通过（第一次之后）12：待复核（第二次提交并且审批通过）13：通过（第一次考核就通过）14：通过（第一次考核被退回后的通过）")
    @JsonProperty(value = "meeting_status")
    @Transient
    private Short meetingStatus;

    @ApiModelProperty("是否删除：0 正常（默认） 1 删除")
    @JsonProperty(value = "is_del")
    @Column(name = "is_del")
    private Short isDel;

    @ApiModelProperty("添加来源：1.活动管理页面添加 2.记实情况页面添加")
    @JsonProperty(value = "add_type")
    @Column(name = "add_type")
    private Short addType;

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;


    @ApiModelProperty("创建人id")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;


    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private Date updateTime;


    @ApiModelProperty("最后更新人id")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    private Long lastChangeUser;


    @ApiModelProperty("状态更新时间")
    @JsonProperty(value = "status_update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Transient
    private Date statusUpdateTime;


    @ApiModelProperty("打回或撤回原因")
    @JsonProperty(value = "reason")
    @Transient
    private String reason;

    @ApiModelProperty("记录人")
    @JsonProperty(value = "record_user")
//    @NotNull(message = "{NotNull.meeting.recordUser}")
//    @Size(min = 1, message = "{NotNull.meeting.recordUser}")
//    @Valid
    private List<MeetingUserEntity> recordUser;

    @ApiModelProperty("主持人")
    @JsonProperty(value = "host_user")
    @NotNull(message = "{NotNull.meeting.hostUser}")
    @Size(min = 1, message = "{NotNull.meeting.hostUser}")
    @Valid
    private List<MeetingUserEntity> hostUser;

    @ApiModelProperty("活动类型")
    @JsonProperty(value = "meeting_types")
    @NotNull(message = "{NotBlank.meeting.types}")
    @Size(min = 1, message = "{NotBlank.meeting.types}")
    @Valid
    private List<MeetingTypeEntity> meetingTypes;

    @ApiModelProperty("参加人员")
    @JsonProperty(value = "participant_users")
    @NotNull(message = "{NotNull.meeting.participantUsers}")
    @Size(min = 1, message = "{NotNull.meeting.participantUsers}")
    @Valid
    private List<MeetingUserEntity> participantUsers;

    @ApiModelProperty("列席人员")
    @JsonProperty(value = "attend_users")
//	@NotNull(message = "{NotNull.meeting.attendUsers}")
//	@Size(min = 1,message = "{NotNull.meeting.attendUsers}")
    @Valid
    private List<MeetingUserEntity> attendUsers;

    @ApiModelProperty("任务")
    @JsonProperty(value = "topics")
//    @NotNull(message = "{NotNull.meeting.topics}")
//    @Size(min = 1,message = "{NotNull.meeting.topics}")
    @Valid
    private List<MeetingTopicEntity> topics;

    @ApiModelProperty("决议附件")
    @JsonProperty(value = "files")
    private List<MeetingResolutionFileEntity> files;


    @ApiModelProperty("是否填写结果 0：没有填写 1：填写")
    @Transient
    @JsonProperty(value = "result_status")
    private Short resultStatus = 0;

    @ApiModelProperty("meeting_leave_id")
    @JsonProperty(value = "meeting_leave_id")
    @Transient
    private Long meetingLeaveId;

    @ApiModelProperty("是否可以签到:is_sign_in=1时有。1：签到未开始 2：签到中 3：签到已结束")
    @JsonProperty(value = "sign_in_status")
    @Transient
    private Integer signInStatus;

    @ApiModelProperty("当前用户签到情况:1：已签到 2：未签到 3：因公请假 4：因私请假 5: 缺席")
    @JsonProperty(value = "sign_status")
    @Transient
    private Integer signStatus;

    @ApiModelProperty("会议提交时间")
    @JsonProperty(value = "submit_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Transient
    private Date submitTime;

    @ApiModelProperty("类别id")
    @JsonProperty(value = "category_id")
    @Transient
    private Long categoryId;

    @ApiModelProperty("类别")
    @Transient
    private String category;

    /**
     * 添加、修改活动时 新增任务
     */
    @Valid
    @JsonProperty(value = "add_topics")
    private List<TopicEntity> addTopics;


    /**
     * 党小组
     * 选择类型必须选择党小组时，不能为空
     */
    @Valid
    @JsonProperty(value = "org_groups")
    private List<MeetingOrgGroupEntity> orgGroups;

    /**
     * 支委会届次
     * 选择类型必须选择党届次时，不能为空
     */
    @Valid
    @JsonProperty(value = "org_periods")
    private List<MeetingOrgPeriodEntity> orgPeriods;


    @Column(name = "region_id")
    @JsonProperty("region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    /**
     * @since v3.0.1
     */
    @ApiModelProperty("是否选择联系领导 0：不选择，1：选择")
    @JsonProperty(value = "sel_contact_leaders")
    @Column(name = "sel_contact_leaders")
    @NotNull(message = "{NotNull.meeting.selContactLeaders}")
    @Range(min = 0, max = 1, message = "{Range.meeting.selContactLeaders}")
    private Integer selContactLeaders;

    /**
     * @since v3.0.1
     */
    @ApiModelProperty("联系领导")
    @JsonProperty(value = "contact_leaders")
    @Valid
    private List<MeetingContactLeaderEntity> contactLeaders;

    /**
     * 纪实结果附件附件
     *
     * @since v3.0.1
     */
    @ApiModelProperty("纪实结果附件")
    @JsonProperty(value = "result_files")
    @Valid
    private List<MeetingResultFileEntity> resultFiles;


    @ApiModelProperty("讲课标题(限制50字)")
    @JsonProperty(value = "lecture_title")
    @Column(name = "lecture_title")
    @Length(max = 50, message = "{Length.meeting.lectureTitle}")
    private String lectureTitle;


    @ApiModelProperty("是否需要填写讲课人 0 否(默认)；1 是")
    @JsonProperty(value = "has_lecturer")
    @Column(name = "has_lecturer")
    private Integer hasLecturer;

    @ApiModelProperty("是否需要填写讲课标题 0 否（默认）；1 是")
    @JsonProperty(value = "has_lecture_title")
    @Column(name = "has_lecture_title")
    private Integer hasLectureTitle;

    @ApiModelProperty("讲课人")
    @JsonProperty(value = "lecturers")
    @Valid
    private List<MeetingUserEntity> lecturers;

    @ApiModelProperty("结束时间")
    @JsonProperty(value = "end_time")
    @Column(name = "end_time")
    @NotNull(message = "{NotNull.meeting.endTime}")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("通知类型 0-不通知，1-钉钉")
    @JsonProperty(value = "notify_type")
    @Column(name = "notify_type")
    @NotNull(message = "{NotNull.meeting.notifyType}")
    private Integer notifyType;

    @ApiModelProperty("提醒方式 0-应用内提醒\n" +
            "1-短信提醒\n" +
            "2-电话提醒")
    @JsonProperty(value = "notify_way")
    @Column(name = "notify_way")
    private Integer notifyWay;

    @ApiModelProperty("提醒时间 0、开始时提醒\n" +
            "1、5分钟前\n" +
            "2、15分钟前\n" +
            "3、30分钟前\n" +
            "4、1小时前\n" +
            "5、1天前")
    @JsonProperty(value = "notify_time")
    @Column(name = "notify_time")
    private String notifyTime;

    @ApiModelProperty("钉钉日程编号")
    @JsonProperty(value = "ding_event_id")
    @Column(name = "ding_event_id")
    private String dingEventId;

    @ApiModelProperty("钉钉日程是否同步  0 未同步 1已同步")
    @JsonProperty(value = "ding_event_sync")
    @Column(name = "ding_event_sync")
    private Integer dingEventSync;

    @ApiModelProperty("钉钉日程创建人编号")
    @JsonProperty(value = "ding_event_create_user")
    @Column(name = "ding_event_create_user")
    private Long dingEventCreateUser;

    @ApiModelProperty("活动内容")
    private String content;

    @ApiModelProperty("录入类型 0-发起活动，1-活动录入")
    @JsonProperty(value = "record_type")
    @Column(name = "record_type")
    private Integer recordType;

    @ApiModelProperty("二维码唯一hash值")
    @JsonProperty(value = "qr_code_hash")
    @Column(name = "qr_code_hash")
    private String qrCodeHash;

    @ApiModelProperty("允许未收到邀请的人签到 0-否，1-是")
    @JsonProperty(value = "allow_all_sign")
    @Column(name = "allow_all_sign")
    private Integer allowAllSign;

    /**
     * 活动议程
     * tc 2021-09-13
     */
    @ApiModelProperty("议程")
    @JsonProperty(value = "agenda")
    @Valid
    private List<MeetingAgendaEntity> agenda;

    /**
     * 活动标签
     * tc 2021-11-08
     */
    @ApiModelProperty("标签")
    @JsonProperty(value = "meeting_tag")
    private List<MeetingTagEntity> meetingTag;

    @ApiModelProperty("标签文本，逗号连接")
    @Transient
    private String tags;

    @ApiModelProperty("组织简称")
    @Transient
    @JsonProperty(value = "org_short_name")
    private String orgShortName;

    @ApiModelProperty("是否补学 1:是 0:否")
    @JsonProperty(value = "can_sign")
    @Column(name = "can_sign")
//    @NotNull(message = "请选择是否补学")
    @Range(min = 0, max = 1, message = "是否补学超出选择范围")
    private Short canSign;

    @ApiModelProperty("正常补学截止时间")
    @JsonProperty(value = "sign_time")
    @Column(name = "sign_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signTime;

    /* 民主生活会添加 ruoyu */
    @ApiModelProperty("民主生活会跳转过来时传入主键id")
    @JsonProperty(value = "life_id")
    @Column(name = "life_id")
    private Long lifeId;

    /**
     * 点击来源模块(life_id不为空时必传)
     * 3:会前学习-组织学习 meeting_types需要包含type_sys:3的数据
     * 10:征求意见-座谈会 meeting_types需要包含type_sys:4的数据
     * 18:会前准备完毕-发起民主生活会 meeting_types需要包含type_sys:2的数据
     * 21:会议通报-会议通报 meeting_types需要包含type_sys:2的数据
     */
    @ApiModelProperty("点击来源模块(life_id不为空时必传)")
    @JsonProperty(value = "model_id")
    @Column(name = "model_id")
    private Long modelId;
    /* 民主生活会添加 ruoyu */

    /* 组织生活会添加 */
    @ApiModelProperty("组织生活会/民主生活会存在 1:属于民主生活会 2:属于组织生活会")
    @JsonProperty(value = "source_type")
    @Column(name = "source_type")
    private Integer sourceType;
    /* 组织生活会添加 */

    /**
     * 市值同步过来的meeting_id
     */
    @ApiModelProperty("市值同步过来的meeting_id")
    @JsonProperty(value = "szf_meeting_id")
    @Column(name = "szf_meeting_id")
    private Long szfMeetingId;

    // ******************* 2023-09-25 添加第一议题 start ****************** //

    // ******************* 2023-09-25 添加第一议题 end ****************** //

    public Date getUpdateTime() {
        return updateTime==null? createTime: updateTime;
    }
}

