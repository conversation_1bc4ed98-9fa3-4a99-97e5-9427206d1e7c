package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 民主生活会谈心谈话人员实体
 * <AUTHOR>
 * @date 2021.11.23
 */

@Data
@ApiModel
@Table(name = "t_meeting_talk_link")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeTalkLinkEntity {

    /**
     * talk_link_id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "talk_link_id")
    @ApiModelProperty("talk_link_id")
    private Long talkLinkId;

    /**
     * talk_id
     */
    @Column(name = "talk_id")
    @ApiModelProperty("talk_id")
    private Long talkId;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 用户名
     */
    @Column(name = "username")
    @ApiModelProperty("用户名")
    private String username;

    /**
     * 1:谈话人，2：被谈话人
     */
    @Column(name = "talk_type")
    @ApiModelProperty("1:谈话人，2：被谈话人")
    private Integer talkType;

    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
