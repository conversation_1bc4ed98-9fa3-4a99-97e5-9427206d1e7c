package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

/**
 * t_meeting_agenda 实体类
 *
 * <AUTHOR>
 * @create 2021-09-13
 */
@Data
@ApiModel
@Table(name = "t_meeting_agenda")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingAgendaEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("agenda_id")
    @JsonProperty(value = "agenda_id")
    @Column(name = "agenda_id")
    private Long agendaId;

    @ApiModelProperty("活动编号")
    @JsonProperty(value = "meeting_id")
    @Column(name = "meeting_id")
    private Long meetingId;

    @JsonProperty(value = "agenda_title")
    @Column(name = "agenda_title")
    @ApiModelProperty("议程标题")
    @Length(max = 100, message = "{Length.meeting.agendaTitle}")
    private String agendaTitle;

    @JsonProperty(value = "agenda_content")
    @Column(name = "agenda_content")
    @ApiModelProperty("议程内容")
    private String agendaContent;

    @JsonProperty(value = "last_update_time")
    @Column(name = "last_update_time")
    @ApiModelProperty(name = "最后一次操作时间")
    private Date lastUpdateTime;

    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    @ApiModelProperty("最后一次操作人")
    private Long lastChangeUser;

    /**
     * 议程标签
     * tc 2021-11-08
     */
    @ApiModelProperty("标签")
    @JsonProperty(value = "meeting_tag")
    private List<MeetingTagEntity> meetingTag;

    /*
     * 添加第一题相关信息
     *top_priority
     */
    @ApiModelProperty("第一议题ids")
    @JsonProperty(value = "top_priority")
    @Column(name = "top_priority")
    private String topPriority;


}

