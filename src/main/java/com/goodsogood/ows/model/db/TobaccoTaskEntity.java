package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 烟草任务实体
 * <AUTHOR>
 * @date 2021.08.23
 */
@Data
@ApiModel
@Table(name = "t_meeting_tobacco_task")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TobaccoTaskEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @Column(name = "task_id")
    private Long taskId;

    @Column(name = "derive")
    @ApiModelProperty("派生于（周期父任务id）")
    private Long derive;

    @Column(name = "region_id")
    @ApiModelProperty("区县id")
    private Long regionId;

    @Column(name = "org_id")
    @ApiModelProperty(name = "发布组织id")
    private Long orgId;

    @Column(name = "org_name")
    @ApiModelProperty("发布组织名称")
    private String orgName;

    @Column(name = "task_title")
    @ApiModelProperty("任务标题")
    private String taskTitle;

    @Column(name = "label")
    @ApiModelProperty("标签")
    private String label;

    @Column(name = "task_level")
    @ApiModelProperty("任务级别(1~4[A~D])")
    private Integer taskLevel;

    @Column(name = "assess")
    @ApiModelProperty("考核任务（0：否，1：是）")
    private Integer assess;

    @Column(name = "task_content")
    @ApiModelProperty("任务内容")
    private String taskContent;

    @Column(name = "accept_scope")
    @ApiModelProperty("接收范围（1：组织，2：人员）")
    private Integer acceptScope;

    @Column(name = "accept_json")
    @ApiModelProperty("接收人员/组织json")
    private String acceptJson;

    @Column(name = "verify_org_id")
    @ApiModelProperty("审核组织id")
    private Long verifyOrgId;

    @Column(name = "verify_org_name")
    @ApiModelProperty("审核组织名称")
    private String verifyOrgName;

    @Column(name = "begin_time")
    @ApiModelProperty("任务开始时间")
    private Date beginTime;

    @Column(name = "end_time")
    @ApiModelProperty("任务结束时间")
    private Date endTime;

    @Column(name = "cycle_type")
    @ApiModelProperty("周期类型（1：不重复，2：每天，3：每周，4：每月）")
    private Integer cycleType;

    @Column(name = "cycle_date")
    @ApiModelProperty("循环日期，可多选，,号拼接\n" +
            "cycle_type = 3,cycle_date = 1~7[周一~周日]\n" +
            "cycle_type = 4,cycle_date = 0~31[每月第几日,0表示月末，对28~31的特殊处理结果]")
    private String cycleDate;

    @Column(name = "notice")
    @ApiModelProperty("是否钉钉通知（1：是，0：否）")
    private Integer notice;

    @Column(name = "remind")
    @ApiModelProperty("提醒渠道（1：钉钉，2：电话，3：钉钉、电话，4：不提醒）")
    private Integer remind;

    @Column(name = "remind_pre")
    @ApiModelProperty("提醒提前时间（1：5分钟前，2：15分钟前，3：30分钟前，4：1小时前，5：一天前，6：开始时）")
    private Integer remindPre;

    @Column(name = "task_file")
    @ApiModelProperty("任务附件json")
    private String taskFile;

    @Column(name = "task_status")
    @ApiModelProperty("任务状态（1：草稿，2：未开始，3：进行中，4：已结束，5：已取消）")
    private Integer taskStatus;

    @Column(name = "is_del")
    @ApiModelProperty("是否删除（0：否，1是）")
    private Integer isDel;

    @Column(name = "create_user_id")
    @ApiModelProperty("创建用户id")
    private Long createUserId;

    @Column(name = "create_user")
    @ApiModelProperty("创建用户")
    private String createUser;

    @Column(name = "update_user_id")
    @ApiModelProperty("更新用户id")
    private Long updateUserId;

    @Column(name = "update_user")
    @ApiModelProperty("更新用户")
    private String updateUser;

    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @Column(name = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;

}
