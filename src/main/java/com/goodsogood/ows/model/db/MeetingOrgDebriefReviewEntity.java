package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe 组织述职评议表
 * @date 2019-12-26
 */

@Data
@ApiModel
@Table(name = "t_meeting_org_debrief_review")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingOrgDebriefReviewEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("主键id")
    @JsonProperty(value = "meeting_org_debrief_review_id")
    @Column(name = "meeting_org_debrief_review_id")
    private Long meetingOrgDebriefReviewId;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;

    @ApiModelProperty("评议等级 1:好 2:较好 3:一般 4:差")
    @JsonProperty(value = "rating")
    @Column(name = "rating")
    private Integer rating;

    @ApiModelProperty("附件")
    @JsonProperty(value = "attachment")
    @Column(name = "attachment")
    private String attachment;

    @ApiModelProperty("附加说明")
    @JsonProperty(value = "additional_information")
    @Column(name = "additional_information")
    private String additionalInformation;

    @ApiModelProperty("评议年度")
    @JsonProperty(value = "review_year")
    @Column(name = "review_year")
    private String reviewYear;

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("最后更新人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    private Long lastChangeUser;

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private Date updateTime;

    @ApiModelProperty("组织类型")
    @JsonProperty(value = "org_type_child")
    @Column(name = "org_type_child")
    private Integer orgTypeChild;

    @ApiModelProperty("添加时候的组织层级")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String orgLevel;

    @ApiModelProperty("当前组织层级")
    @JsonProperty(value = "current_org_level")
    @Column(name = "current_org_level")
    private String currentOrgLevel;

    @ApiModelProperty("数据状态 1 正常 2删除")
    @JsonProperty(value = "status")
    @Column(name = "status")
    private Integer status;

    @Column(name = "region_id")
    @JsonProperty("region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;
}
