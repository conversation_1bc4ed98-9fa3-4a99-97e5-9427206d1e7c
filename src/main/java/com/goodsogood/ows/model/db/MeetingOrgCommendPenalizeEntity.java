package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.validate.group.Add;
import com.goodsogood.ows.validate.group.Edit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe 组织奖惩表
 * @date 2019-12-26
 */

@Data
@ApiModel
@Table(name = "t_meeting_org_commend_penalize")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingOrgCommendPenalizeEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("主键id")
    @JsonProperty(value = "meeting_org_commend_penalize_id")
    @Column(name = "meeting_org_commend_penalize_id")
    private Long meetingOrgCommendPenalizeId;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;

    @ApiModelProperty("批准日期")
    @JsonProperty(value = "ratify_time")
    @Column(name = "ratify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ratifyTime;

    @ApiModelProperty("奖惩类别")
    @JsonProperty(value = "category")
    private Integer category;

    @ApiModelProperty("奖惩级别")
    @JsonProperty(value = "level")
    private Integer level;

    @ApiModelProperty("奖励或者惩罚名称")
    @JsonProperty(value = "name")
    private Integer name;

    @ApiModelProperty("奖惩名称内容")
    @JsonProperty(value = "content")
    @Length(max = 200,message = "奖惩名称内容不能超过200字")
    private String content;

    @ApiModelProperty("奖励或者惩罚原因")
    @JsonProperty(value = "reason")
    @Column(name = "reason")
    private Integer reason;

    @ApiModelProperty("奖励类型")
    @JsonProperty(value = "reward_type")
    @Column(name = "reward_type")
    private Integer rewardType;

    @ApiModelProperty("颁奖单位")
    @JsonProperty(value = "award_unit")
    @Column(name = "award_unit")
    private String awardUnit;

    @ApiModelProperty("相关文件")
    @JsonProperty(value = "related_file")
    @Column(name = "related_file")
    private String relatedFile;

    @ApiModelProperty("依据说明")
    @JsonProperty(value = "basis_description")
    @Column(name = "basis_description")
    private String basisDescription;

    @ApiModelProperty("附件")
    @JsonProperty(value = "attachment")
    @Column(name = "attachment")
    private String attachment;

    @ApiModelProperty("1:奖励 2:惩罚")
    @JsonProperty(value = "type")
    @Column(name = "type")
    private Integer type;

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("最后更新人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    private Long lastChangeUser;

    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty("组织层级")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String orgLevel;

    @ApiModelProperty("数据状态 1 正常 2删除")
    @JsonProperty(value = "status")
    @Column(name = "status")
    private Integer status;

    @Column(name = "region_id")
    @JsonProperty("region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;

    @ApiModelProperty("审核状态 1-待审核、2-审核通过、3-审核不通过")
    @JsonProperty(value = "approval_status")
    @Column(name = "approval_status")
    private Integer approvalStatus;

    @Column(name = "workflow_task_id")
    @JsonProperty("workflow_task_id")
    @ApiModelProperty("审核流ID")
    private Long workflowTaskId;
}
