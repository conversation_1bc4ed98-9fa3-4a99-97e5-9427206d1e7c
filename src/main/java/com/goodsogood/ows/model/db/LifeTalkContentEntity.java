package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 民主生活会谈心谈话内容实体
 * <AUTHOR>
 * @date 2021.11.23
 */

@Data
@ApiModel
@Table(name = "t_meeting_talk_content")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeTalkContentEntity {

    /**
     * talk_content_id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "talk_content_id")
    @ApiModelProperty("talk_content_id")
    private Long talkContentId;

    /**
     * talk_id
     */
    @Column(name = "talk_id")
    @ApiModelProperty("talk_id")
    private Long talkId;

    /**
     * 标题
     */
    @Column(name = "title")
    @ApiModelProperty("标题")
    private String title;

    /**
     * 内容
     */
    @Column(name = "content")
    @ApiModelProperty("内容")
    private String content;

    /**
     * 是否删除 0：否，1：是
     */
    @Column(name = "is_del")
    @ApiModelProperty("是否删除 0：否，1：是")
    private Integer isDel;

    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;

    /**
     * last_change_user
     */
    @Column(name = "last_change_user")
    @ApiModelProperty("last_change_user")
    private Long lastChangeUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
}
