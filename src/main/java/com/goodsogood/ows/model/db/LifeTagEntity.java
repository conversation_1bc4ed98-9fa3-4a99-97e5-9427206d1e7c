package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * t_meeting_life_tag实体
 * <AUTHOR>
 * @date 2021.11.23
 */

@Data
@ApiModel
@Table(name = "t_meeting_life_tag")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeTagEntity {

    /**
     * life_tag_id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "life_tag_id")
    @ApiModelProperty("life_tag_id")
    private Long lifeTagId;

    /**
     * 民主会议id
     */
    @Column(name = "life_id")
    @ApiModelProperty("民主会议id")
    private Long lifeId;

    /**
     * 标签id
     */
    @Column(name = "tag_id")
    @ApiModelProperty("标签id")
    private Long tagId;

    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

}
