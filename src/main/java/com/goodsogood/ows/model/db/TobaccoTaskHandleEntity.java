package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 烟草任务处理实体
 * <AUTHOR>
 * @date 2021.08.23
 */
@Data
@ApiModel
@Table(name = "t_meeting_tobacco_task_handle")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TobaccoTaskHandleEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "handle_id")
    @ApiModelProperty("id")
    private Long handleId;

    @Column(name = "task_id")
    @ApiModelProperty("任务id")
    private Long taskId;

    @Column(name = "region_id")
    @ApiModelProperty("区县id")
    private Long regionId;

    @Column(name = "accept_id")
    @ApiModelProperty("接收 人员/组织id")
    private Long acceptId;

    @Column(name = "flag")
    @ApiModelProperty("1:任务提交，2:任务审核")
    private Integer flag;

    @Column(name = "handle_status")
    @ApiModelProperty("执行状态（1：草稿，2：已提交，3：已通过，4：未通过，5：已退回）")
    private Integer handleStatus;

    @Column(name = "forward_json")
    @ApiModelProperty("转发组织json")
    private String forwardJson;

    @Column(name = "handle_content")
    @ApiModelProperty("执行描述/审核意见")
    private String handleContent;

    @Column(name = "handle_file")
    @ApiModelProperty("附件json")
    private String handleFile;

    @Column(name = "handle_user")
    @ApiModelProperty("执行用户")
    private String handleUser;

    @Column(name = "handle_org")
    @ApiModelProperty("执行组织")
    private String handleOrg;

    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @Column(name = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
