package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * t_video_conference 实体类
 *
 */
@Data
@ApiModel
@Table(name = "t_video_conference")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoConferenceEntity {

	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("会议id")
	@Column(name = "conference_id")
	private Long conferenceId;

	@ApiModelProperty("会议类型（0-快速会议 1-预约会议）")
	@Column(name = "conference_type")
	private Integer conferenceType;

	@ApiModelProperty("会议名称")
	@Column(name = "conference_name")
	private String conferenceName;

	@ApiModelProperty("会议标题")
	@Column(name = "conference_title")
	private String conferenceTitle;

	@ApiModelProperty("开始时间")
	@Column(name = "start_time")
	private Date startTime;

	@ApiModelProperty("结束时间")
	@Column(name = "end_time")
	private Date endTime;

	@ApiModelProperty("状态（0-未开始 1-已开始 2-已结束 3-已取消）")
	@Column(name = "status")
	private Integer status;

	@ApiModelProperty("天子钉-会议id")
	@Column(name = "tzd_conference_id")
	private String tzdConferenceId;

	@ApiModelProperty("会议密码")
	@Column(name = "conference_password")
	private String conferencePassword;

	@ApiModelProperty("主持人密码")
	@Column(name = "host_password")
	private String hostPassword;

	@ApiModelProperty("入会链接")
	@Column(name = "external_link_url")
	private String externalLinkUrl;

	@ApiModelProperty("PSTN呼入号码(Array字符串)")
	@Column(name = "phone_numbers")
	private String phoneNumbers;

	@ApiModelProperty("会议Code")
	@Column(name = "room_code")
	private String roomCode;

	@ApiModelProperty("创建者的天子钉unionid")
	@Column(name = "create_union_id")
	private String createUnionId;

	@ApiModelProperty("创建人")
	@Column(name = "create_id")
	private Long createId;

	@ApiModelProperty("更新人")
	@Column(name = "update_id")
	private Long updateId;

	@ApiModelProperty("创建时间")
	@Column(name = "create_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@ApiModelProperty("更新时间")
	@Column(name = "update_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

	@ApiModelProperty("区县ID")
	@Column(name = "region_id")
	private Long regionId;

	@Transient
	private String createName;


}
