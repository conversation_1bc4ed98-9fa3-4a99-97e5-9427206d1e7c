package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.vo.OrgForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

/**
 * t_meeting_plan 实体类
 *
 * <AUTHOR>
 * @create 2018-10-23 13:55
 */
@Data
@ApiModel
@Table(name = "t_meeting_plan")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingPlanEntity {

	/**
	 * 执行方式:1.自定义间段(默认值);
	 */
	public static final short EXECUTE_TYPE_CUSTOMIZE = 1;
	/**
	 * 执行方式:2自然月周期;
	 */
	public static final short EXECUTE_TYPE_MONTH = 2;
	/**
	 * 执行方式:3.季度周期;
	 */
	public static final short EXECUTE_TYPE_QUARTER = 3;
	/**
	 * 执行方式:4.年度周期
	 */
	public static final short EXECUTE_TYPE_YEAR = 4;

	/**
	 * 停、启用状态。0：停用；
	 */
	public static final short IS_EXECUTE_NO = 0;

	/**
	 * 停、启用状态。1：启用
	 */
	public static final short IS_EXECUTE_YES = 1;

	/**
	 * 发放任务类型：1 自动发放;
	 */
	public static final short SEND_TYPE_AUTO = 1;

	/**
	 * 发放任务类型：2 手动发放(默认)
	 */
	public static final short SEND_TYPE_MANUAL = 2;

	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("id")
	@JsonProperty(value = "meeting_plan_id")
	@Column(name = "meeting_plan_id")
	private Long meetingPlanId;

	@ApiModelProperty("创建人所属组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;

	@ApiModelProperty("创建人所属组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;

	@ApiModelProperty("组织生活名称")
	@Column(name = "name")
	private String name;

	@ApiModelProperty("执行方式:1.自定义间段(默认值);2自然月周期;3.季度周期;4.年度周期")
	@JsonProperty(value = "execute_type")
	@Column(name = "execute_type")
	private Short executeType;

	@ApiModelProperty("开始时间")
	@JsonProperty(value = "start_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Column(name = "start_time")
	private Date startTime;

	@ApiModelProperty("结束时间")
	@JsonProperty(value = "end_time")
	@Column(name = "end_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date endTime;

	@ApiModelProperty("开始年份")
	@JsonProperty(value = "start_year")
	@Column(name = "start_year")
	private Integer startYear;

	@ApiModelProperty("开始月份")
	@JsonProperty(value = "start_month")
	@Column(name = "start_month")
	private Integer startMonth;

	@ApiModelProperty("	开始季度")
	@JsonProperty(value = "start_quarter")
	@Column(name = "start_quarter")
	private Integer startQuarter;

	@ApiModelProperty("停、启用状态。0：停用；1：启用")
	@JsonProperty(value = "is_execute")
	@Column(name = "is_execute")
	private Short isExecute;

	@ApiModelProperty("发放任务类型：1 自动发放; 2 手动发放(默认)")
	@JsonProperty(value = "send_type")
	@Column(name = "send_type")
	private Short sendType;

	@ApiModelProperty("是否删除：0 正常（默认） 1 删除")
	@JsonProperty(value = "is_del")
	@Column(name = "is_del")
	private Short isDel;

	@ApiModelProperty("执行组织")
	@JsonProperty(value = "execute_orgs")
	private List<OrgForm> executeOrgs;

	@ApiModelProperty("	活动要求")
	@JsonProperty(value = "meeting_types")
	private List<MeetingRequireEntity> meetingTypes;


	@ApiModelProperty("自动发放任务限制条件")
	@JsonProperty(value = "limit")
	private MeetingPlanLimitEntity planLimitEntity;

	/**
	 * 自动发放组织类型
	 */
	@JsonProperty(value = "limit_type_list")
	private List<MeetingPlanLimitTypeEntity> limitTypeList;

	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;

	@ApiModelProperty("创建人id")
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;

	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;

	@ApiModelProperty("最后更新人id")
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;

	/**
	 * 3.0.0新增
	 */
	@ApiModelProperty("区县id")
	@JsonProperty("region_id")
	@Column(name = "region_id")
	private Long regionId;

}
