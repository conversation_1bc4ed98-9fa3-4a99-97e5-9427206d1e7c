package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * t_meeting_life_study实体
 * <AUTHOR>
 * @date 2021.11.23
 */

@Data
@ApiModel
@Table(name = "t_meeting_life_study")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeStudyEntity {

    /**
     * life_study_id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "life_study_id")
    @ApiModelProperty("life_study_id")
    private Long lifeStudyId;

    /**
     * 民主生活会id
     */
    @Column(name = "life_id")
    @ApiModelProperty("民主生活会id")
    private Long lifeId;

    /**
     * 组织生活id
     */
    @Column(name = "study_id")
    @ApiModelProperty("组织生活id")
    private Long studyId;

    /**
     * 1：会前，2：会后
     */
    @Column(name = "step")
    @ApiModelProperty("1：会前，2：会后")
    private Integer step;

    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
