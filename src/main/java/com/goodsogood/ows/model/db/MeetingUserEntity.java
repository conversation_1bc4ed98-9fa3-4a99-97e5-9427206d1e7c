package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * t_meeting_user 实体类
 * <p>
 * 参会人员
 *
 * <AUTHOR>
 * @create 2018-10-25 17:56
 */
@Data
@ApiModel
@Table(name = "t_meeting_user")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingUserEntity {

    /**
     * 记录人员
     */
    public static final short TAG_RECORD = 1;

    /**
     * 主持人
     */
    public static final short TAG_HOST = 2;

    /**
     * 参会人员
     */
    public static final short TAG_PART = 3;

    /**
     * 列席人员
     */
    public static final short TAG_ATTEND = 4;

    /**
     * 讲课人
     */
    public static final short TAG_LECTURER = 5;


    /**
     * 活动管理添加
     */
    public static final short MEETING_ADD = 0;

    /**
     * 记实结果添加
     */
    public static final short REPORT_ADD = 1;

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "meeting_user_id")
    @Column(name = "meeting_user_id")
    private Long meetingUserId;


    @ApiModelProperty("活动id")
    @JsonProperty(value = "meeting_id")
    @Column(name = "meeting_id")
    private Long meetingId;


    @ApiModelProperty("用户id.为null或是负数表示手动录入")
    @JsonProperty(value = "user_id")
    @Column(name = "user_id")
    private Long userId;


    @ApiModelProperty("用户所属组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;


    @ApiModelProperty("用户名")
    @JsonProperty(value = "user_name")
    @Column(name = "user_name")
    @NotBlank(message = "{NotBlank.meeting.userName}")
    @Length(max = 100, message = "{Length.meeting.userName}")
    private String userName;


    @ApiModelProperty("用户所属组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;


    @ApiModelProperty("电话号码")
    @Column(name = "phone")
    @Length(max = 20, message = "{Length.phone}")
    private String phone;


    @ApiModelProperty("身份证号码")
    @JsonProperty(value = "cert_number")
    @Column(name = "cert_number")
    private String certNumber;


    @ApiModelProperty("是否是通过填写记实情况中的“添加参会人员”、“添加列席人员”两个功能添加的人员 0:活动管理添加；1：记实添加")
    @JsonProperty(value = "is_add")
    @Column(name = "is_add")
    private Short isAdd;


	@ApiModelProperty("签到情况1：已签到（默认） 2：未签到 3：因公请假 4：因私请假 5: 缺席 6：补学")
	@JsonProperty(value = "sign_status")
	@Column(name = "sign_status")
	private Short signStatus;

    @ApiModelProperty("签到情况1：已签到（默认） 2：未签到 3：因公请假 4：因私请假 5: 缺席")
    @JsonProperty(value = "old_sign_status")
    @Column(name = "old_sign_status")
    private Short oldSignStatus;


    @ApiModelProperty("参会人员与列席人员标识（1：记录人员，2：主持人，3：参与人员 ，4：列席人员，5：讲课人")
    @Column(name = "tag")
    private Short tag;


    @ApiModelProperty("请假原因")
    @Column(name = "reason")
    private String reason;

    @ApiModelProperty("签到hash值")
    @Column(name = "sign_hash")
    @JsonProperty(value = "sign_hash")
    private String signHash;

    @ApiModelProperty("签到时间")
    @Column(name = "sign_time")
    @JsonProperty(value = "sign_time")
    private Date signTime;

    @ApiModelProperty("头像地址")
    @Column(name = "head_url")
    @JsonProperty(value = "head_url")
    private String headUrl;

    @Transient
    @JsonProperty(value = "open_id")
    private String openId;

    @ApiModelProperty("手机号，用来匹配烟草")
    @JsonProperty(value = "real_phone")
    @Transient
    private String realPhone;
}

