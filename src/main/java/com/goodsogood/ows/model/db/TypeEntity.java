package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * t_type 实体类
 *
 * <AUTHOR>
 * @create 2018-10-19 16:21
 */
@Data
@ApiModel
@Table(name = "t_type")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TypeEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "type_id")
    @Column(name = "type_id")
    private Long typeId;


    @ApiModelProperty("所属类别id")
    @JsonProperty(value = "category_id")
    @Column(name = "category_id")
    @NotNull(message = "{NotNull.category.categoryId}")
    private Long categoryId;


    @ApiModelProperty("创建人所属组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;


    @ApiModelProperty("创建人所属组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;


    @ApiModelProperty("类型")
    @Column(name = "type")
    @NotBlank(message = "{NotBlank.type.type}")
    @Length(max = 50,message = "{Length.type.type}")
    private String type;


    @ApiModelProperty("类别。冗余")
    @Column(name = "category")
    private String category;


    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;


    @ApiModelProperty("创建人id")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;


    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private Date updateTime;


    @ApiModelProperty("最后更新人id")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    private Long lastChangeUser;

    @ApiModelProperty("规则类型：-1 未指定 1.必须选择党小组 2.必须选择支委会届次 3.必须选择组织所有成员")
    @JsonProperty(value = "code")
    @Column(name = "code")
    private Integer code;

    @ApiModelProperty("是否需要填写讲课人 0 否(默认)；1 是")
    @JsonProperty(value = "has_lecturer")
    @Column(name = "has_lecturer")
    private Integer hasLecturer;

    @ApiModelProperty("是否需要填写讲课标题 0 否（默认）；1 是")
    @JsonProperty(value = "has_lecture_title")
    @Column(name = "has_lecture_title")
    private Integer hasLectureTitle;

    @ApiModelProperty("特殊类型 1:历史组织生活类型  2:民主生活会 3:民主生活会会前学习 4:民主生活会座谈会")
    @JsonProperty(value = "type_sys")
    @Column(name = "type_sys")
    private Short typeSys;
}

