package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 烟草任务组织对应实体
 * <AUTHOR>
 * @date 2021.08.23
 */
@Data
@ApiModel
@Table(name = "t_meeting_tobacco_task_org")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TobaccoTaskOrgEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "to_id")
    @ApiModelProperty("id")
    private Long toId;

    @Column(name = "region_id")
    @ApiModelProperty("区县id")
    private Long regionId;

    @Column(name = "task_id")
    @ApiModelProperty("id")
    private Long taskId;

    @Column(name = "task_title")
    @ApiModelProperty("任务标题")
    private String taskTitle;

    @Column(name = "accept_scope")
    @ApiModelProperty("接收范围（1：组织，2：人员）")
    private Integer acceptScope;

    @Column(name = "accept_id")
    @ApiModelProperty("接收 人员/组织id")
    private Long acceptId;

    @Column(name = "accept_name")
    @ApiModelProperty("接收 人员/组织 名称")
    private String acceptName;

    @Column(name = "label")
    @ApiModelProperty("标签")
    private String label;

    @Column(name = "begin_time")
    @ApiModelProperty("任务开始时间")
    private Date beginTime;

    @Column(name = "end_time")
    @ApiModelProperty("任务结束时间")
    private Date endTime;

    @Column(name = "handle_status")
    @ApiModelProperty("执行状态（1：未提交，2：已填报，3：已通过，4：未通过，5：已退回）")
    private Integer handleStatus;

    @Column(name = "task_status")
    @ApiModelProperty("任务状态（3：进行中，4：已结束，5：已取消）")
    private Integer taskStatus;

    @Column(name = "is_del")
    @ApiModelProperty("是否删除（0：否，1是）")
    private Integer isDel;

    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @Column(name = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
