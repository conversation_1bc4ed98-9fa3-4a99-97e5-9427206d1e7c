package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.annotation.OtherId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * t_meeting_contact_leader 实体类
 * <p>
 * 联系领导信息表
 *
 * <AUTHOR>
 * @create 2020-07-22 15:06
 */
@Data
@ApiModel
@Table(name = "t_meeting_contact_leader")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingContactLeaderEntity extends BaseCheckEntity {

	@Id
	@GeneratedValue(generator = "JDBC")
	@JsonProperty(value = "meeting_contact_leader_id")
	@Column(name = "meeting_contact_leader_id")
	private Long meetingContactLeaderId;


	@JsonProperty(value = "meeting_id")
	@Column(name = "meeting_id")
	private Long meetingId;


	@ApiModelProperty("用户id")
	@JsonProperty(value = "user_id")
	@Column(name = "user_id")
	@OtherId
	private Long userId;


	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;


	@ApiModelProperty("用户名")
	@JsonProperty(value = "user_name")
	@Column(name = "user_name")
	private String userName;


	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;


	/**
	 * 逻辑删除 0:未删除 1.删除
	 */
	@JsonProperty(value = "is_del")
	@Column(name = "is_del")
	private Integer isDel;


	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;


	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;


	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;


	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;

	@Transient
	@JsonProperty(value = "open_id")
	private String openId;


	@Transient
	@JsonProperty(value = "real_phone")
	private String realPhone;

}

