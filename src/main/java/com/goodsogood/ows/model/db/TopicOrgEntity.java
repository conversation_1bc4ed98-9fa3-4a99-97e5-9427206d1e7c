package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.annotation.OtherId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 *
 * t_topic_org 实体类
 *
 * <AUTHOR>
 * @create 2018-10-23 09:03
 */
@Data
@ApiModel
@Table(name = "t_topic_org")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopicOrgEntity extends BaseCheckEntity {

	@Id
	@ApiModelProperty("任务组织关联表id")
	@JsonProperty(value = "topic_org_id")
	@Column(name = "topic_org_id")
	private Long topicOrgId;


	@JsonProperty(value = "topic_id")
	@Column(name = "topic_id")
	private Long topicId;


	@ApiModelProperty("组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	@OtherId
	private Long orgId;


	@ApiModelProperty("组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;


	@ApiModelProperty("状态（1：未完成 2：已完成 3:逾期未完成）")
	@Column(name = "status")
	private Integer status;


	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;


	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;


	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;


	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;


	/**
	 * @since v3.0.0
	 */
	@ApiModelProperty("完成情况描述")
	@JsonProperty(value = "ans_cnts")
	@Transient
	private String ansCnts;
	/**
	 * @since v3.0.0
	 */
	@ApiModelProperty("文件名称")
	@JsonProperty(value = "file_names")
	@Transient
	private String fileNames;
	private List<TopicLogEntity> topicLogList;

	private List<TopicLogFileEntity> files;
}

