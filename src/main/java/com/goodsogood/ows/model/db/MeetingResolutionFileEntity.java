package com.goodsogood.ows.model.db;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_meeting_resolution_file 实体类
 *
 * 决议附件表
 *
 * <AUTHOR>
 * @create 2018-10-29 15:48
*/
@Data
@ApiModel
@Table(name = "t_meeting_resolution_file")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingResolutionFileEntity {
	
	@Id
	@ApiModelProperty("id")
	@JsonProperty(value = "meeting_resolution_file_id")
	@Column(name = "meeting_resolution_file_id")
	private Long meetingResolutionFileId;


	@ApiModelProperty("活动id")
	@JsonProperty(value = "meeting_id")
	@Column(name = "meeting_id")
	private Long meetingId;
	
	
	@ApiModelProperty("附件名称")
	@Column(name = "name")
	private String name;
	
	
	@ApiModelProperty("附件路径")
	@Column(name = "path")
	private String path;
	
	
	@ApiModelProperty("文件原名称")
	@JsonProperty(value = "file_name")
	@Column(name = "file_name")
	private String fileName;
	
	
	@ApiModelProperty("文件大小(Byte)")
	@Column(name = "size")
	private String size;
	
	
	@ApiModelProperty("0：正常 1：删除")
	@JsonProperty(value = "is_del")
	@Column(name = "is_del")
	private Integer isDel;
	
	
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;
	
}

