package com.goodsogood.ows.model.db;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * t_topic_log 实体类
 *
 * <AUTHOR>
 * @create 2018-10-24 16:32
*/
@Data
@ApiModel
@Table(name = "t_topic_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopicLogEntity {
	
	@Id
	@ApiModelProperty("日志id")
	@JsonProperty(value = "topic_log_id")
	@Column(name = "topic_log_id")
	private Long topicLogId;

	@Transient
	private Long id;


	@ApiModelProperty("工作任务关联id")
	@JsonProperty(value = "meeting_topic_id")
	@Column(name = "meeting_topic_id")
	private Long meetingTopicId;


	@ApiModelProperty("内容id")
	@JsonProperty(value = "content_id")
	@Column(name = "content_id")
	private Long contentId;


	@ApiModelProperty("任务id")
	@JsonProperty(value = "topic_id")
	@Column(name = "topic_id")
	private Long topicId;


	@ApiModelProperty("问答题的回答内容")
	@JsonProperty(value = "ans_cnt")
	@Column(name = "ans_cnt")
	private String ansCnt;


	@ApiModelProperty("选项id")
	@JsonProperty(value = "opts_id")
	@Column(name = "opts_id")
	private Long optsId;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;


	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;


	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;

	/**
	 * @since v3.0.0
	 */
	@ApiModelProperty("任务组织关联表id")
	@JsonProperty(value = "topic_org_id")
	@Column(name = "topic_org_id")
	private Long topicOrgId;


	private List<TopicLogFileEntity> files = new ArrayList<>();

}

