package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * t_meeting_file 文件实体类
 *
 * <AUTHOR>
 * @create 2020-01-06
 */
@Data
@ApiModel
@Table(name = "t_meeting_file")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingFileEntity {

    @Id
    @ApiModelProperty("主键id")
    @JsonProperty(value = "t_meeting_file_id")
    @Column(name = "t_meeting_file_id")
    private Long tMeetingFileId;

    @ApiModelProperty("文件ID")
    @JsonProperty(value = "file_id")
    @Column(name = "file_id")
    private Long fileId;

    /**
     * {@link com.goodsogood.ows.common.FileSourceEnum}
     */
    @ApiModelProperty("文件来源 1 组织奖惩 2组织评议 3 党员奖惩")
    @Column(name = "source")
    private Integer source;

    @ApiModelProperty("附件名称")
    @Column(name = "name")
    private String name;


    @ApiModelProperty("附件路径")
    @Column(name = "path")
    private String path;


    @ApiModelProperty("文件原名称")
    @JsonProperty(value = "file_name")
    @Column(name = "file_name")
    private String fileName;


    @ApiModelProperty("文件大小（byte）")
    @Column(name = "size")
    private Long size;

    @ApiModelProperty("关联表的主键id")
    @Column(name = "link_id")
    private Long linkId;


}

