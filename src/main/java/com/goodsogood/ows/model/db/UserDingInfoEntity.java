package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 天子钉信息
 *
 *
 */
@Data
@ApiModel
@Table(name = "t_user_ding_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserDingInfoEntity {

    @ApiModelProperty(value = "主键ID")
    @Id
    @Column(name = "user_id")
    @GeneratedValue(generator = "JDBC")
    private Long userId;

    @ApiModelProperty(value = "天子钉userId")
    @Column(name = "ding_user_id")
    private String dingUserId;

    @ApiModelProperty(value = "union_id")
    @Column(name = "union_id")
    private String unionId;

}