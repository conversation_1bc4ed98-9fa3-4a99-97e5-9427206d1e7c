package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 *
 * t_meeting_topic 实体类
 *
 * <AUTHOR>
 * @create 2018-10-23 11:38
*/
@Data
@ApiModel
@Table(name = "t_meeting_topic")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingTopicEntity {
	
	@Id
	@ApiModelProperty("id")
	@JsonProperty(value = "meeting_topic_id")
	@Column(name = "meeting_topic_id")
	private Long meetingTopicId;

	@Transient
	private Long id;


	@ApiModelProperty("活动id")
	@JsonProperty(value = "meeting_id")
	@Column(name = "meeting_id")
	private Long meetingId;


	@ApiModelProperty("任务id")
	@JsonProperty(value = "topic_id")
	@Column(name = "topic_id")
//	@NotNull(message = "{NotNull.meetingTopic.topicId}")
	private Long topicId;

	@ApiModelProperty("任务名称")
	@JsonProperty(value = "topic_name")
	@Column(name = "topic_name")
	private String topicName;

	
	@ApiModelProperty("状态（1：未完成 2：已完成）")
	@Column(name = "status")
	private Integer status;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;
	
	
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;

	@ApiModelProperty("任务名称")
	@JsonProperty(value = "name")
	@Transient
	private String name;

	@ApiModelProperty("描述")
	@JsonProperty(value = "description")
	@Transient
	private String description;

	@ApiModelProperty("开始时间")
	@JsonProperty(value = "start_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Transient
	private Date startTime;


	@ApiModelProperty("结束时间")
	@JsonProperty(value = "end_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Transient
	private Date endTime;

	@ApiModelProperty("标记是否是发起活动或补录时新增")
	@JsonProperty(value = "is_new_add")
	@Transient
	private Boolean isNewAdd = false;

	/**
	 * 任务的附件
	 */
	private List<TopicFileEntity> files;

	/**
	 * 任务内容及选择答案
	 */
	private List<TopicContentEntity> contents;

	/**
	 * 任务的答案
	 */
	private List<TopicLogEntity> logList;
}

