package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * t_meeting_result_file 实体类
 * <p>
 * 纪实结果附件表
 *
 * <AUTHOR>
 * @create 2020-07-22 15:07
 */
@Data
@ApiModel
@Table(name = "t_meeting_result_file")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingResultFileEntity extends BaseCheckEntity {
	@Id
	@GeneratedValue(generator = "JDBC")
	@JsonProperty(value = "meeting_result_file_id")
	@Column(name = "meeting_result_file_id")
	private Long meetingResultFileId;
	
	@JsonProperty(value = "meeting_id")
	@Column(name = "meeting_id")
	private Long meetingId;

	@ApiModelProperty("文件id")
	@Column(name = "id")
	private Long id;

	@ApiModelProperty("附件名称")
	@Column(name = "name")
	private String name;


	@ApiModelProperty("附件路径")
	@Column(name = "path")
	private String path;


	@ApiModelProperty("文件原名称")
	@JsonProperty(value = "file_name")
	@Column(name = "file_name")
	private String fileName;


	@ApiModelProperty("文件大小（byte）")
	@Column(name = "size")
	private Long size;


	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;


	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;


	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;


	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;

	/**
	 * 逻辑删除 0:未删除 1.删除
	 */
	@JsonProperty(value = "is_del")
	@Column(name = "is_del")
	private Integer isDel;

	/**
	 * 类型 0-图片，1-附件
	 */
	@Column(name = "type")
	private Integer type;

}

