package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 组织生活会他人上传记录表实体
 * <AUTHOR>
 * @date 2021.11.23
 */

@Data
@ApiModel
@Table(name = "t_meeting_org_uploader")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgLifeUploaderEntity {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "uploader_id")
    @ApiModelProperty("主键")
    private Long uploaderId;

    /**
     * 民主生活会id
     */
    @Column(name = "life_id")
    @ApiModelProperty("民主生活会id")
    private Long lifeId;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 姓名
     */
    @Column(name = "username")
    @ApiModelProperty("姓名")
    private String username;

    /**
     * 关联的模块
     */
    @Column(name = "type")
    @ApiModelProperty("model_id")
    private Integer type;

    /**
     * 精确数据id
     */
    @Column(name = "data_id")
    @ApiModelProperty("精确数据id")
    private Long dataId;

    /**
     * 上传截至时间
     */
    @Column(name = "end_time")
    @ApiModelProperty("上传截至时间")
    private LocalDateTime endTime;

    /**
     * create_time
     */
    @Column(name = "create_time")
    @ApiModelProperty("create_time")
    private LocalDateTime createTime;

    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;
}
