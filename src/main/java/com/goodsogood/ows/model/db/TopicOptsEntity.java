package com.goodsogood.ows.model.db;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 *
 * t_topic_opts 实体类
 *
 * <AUTHOR>
 * @create 2018-10-22 14:16
*/
@Data
@ApiModel
@Table(name = "t_topic_opts")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopicOptsEntity {
	
	@Id
	@ApiModelProperty("选项id")
	@JsonProperty(value = "opts_id")
	@Column(name = "opts_id")
	private Long optsId;
	
	
	@ApiModelProperty("内容id")
	@JsonProperty(value = "content_id")
	@Column(name = "content_id")
	private Long contentId;


    @ApiModelProperty("任务id")
	@JsonProperty(value = "topic_id")
	@Column(name = "topic_id")
	private Long topicId;
	
	
	@ApiModelProperty("选项名称")
	@JsonProperty(value = "opts_name")
	@Column(name = "opts_name")
	private String optsName;
	
	
	@ApiModelProperty("排序")
	@Column(name = "seq")
	private Integer seq;
	
	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;
	
	
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;

	/** 用于返回给前端 */
	@Transient
	@JsonProperty(value = "is_del")
	private Integer isDel = 0;
	
}

