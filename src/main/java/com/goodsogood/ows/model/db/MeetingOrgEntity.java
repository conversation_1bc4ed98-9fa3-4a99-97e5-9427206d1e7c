package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 *
 * t_meeting_org 实体类
 *
 * <AUTHOR>
 * @create 2018-10-23 16:35
*/
@Data
@ApiModel
@Table(name = "t_meeting_org")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingOrgEntity{
	
	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("id")
	@JsonProperty(value = "meeting_org_id")
	@Column(name = "meeting_org_id")
	private Long meetingOrgId;


	@ApiModelProperty("组织生活id")
	@JsonProperty(value = "meeting_plan_id")
	@Column(name = "meeting_plan_id")
	private Long meetingPlanId;
	
	
	@ApiModelProperty("执行组织id")
	@JsonProperty(value = "org_id")
	@Column(name = "org_id")
	private Long orgId;

	@ApiModelProperty("执行组织名称")
	@JsonProperty(value = "org_name")
	@Column(name = "org_name")
	private String orgName;
	
}

