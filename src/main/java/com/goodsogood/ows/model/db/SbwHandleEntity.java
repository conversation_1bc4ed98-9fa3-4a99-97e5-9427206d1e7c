package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 南岸区网信办任务处理实体
 * <AUTHOR>
 * @date 2021.07.29
 */
@Data
@ApiModel
@Table(name = "t_meeting_sbw_handle")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SbwHandleEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @Column(name = "handle_id")
    private Long handleId;

    @ApiModelProperty("任务id")
    @Column(name = "task_id")
    private Long taskId;

    @ApiModelProperty("区县id")
    @Column(name = "region_id")
    private Long regionId;

    @ApiModelProperty("组织id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("操作用户id")
    @Column(name = "user_id")
    private Long userId;

    @ApiModelProperty("操作用户组织id")
    @Column(name = "user_org_id")
    private Long userOrgId;

    @ApiModelProperty("操作用户组织名称")
    @Column(name = "user_org_name")
    private String userOrgName;

    @ApiModelProperty("是否建议公开回复。0：否，1：是")
    @Column(name = "open_status")
    private Integer openStatus;

    @ApiModelProperty("不公开理由")
    @Column(name = "private_reason")
    private String privateReason;

    @ApiModelProperty("处理类型。1：提交，2：审核")
    @Column(name = "flag")
    private Integer flag;

    @ApiModelProperty("处置内容/完成任务情况/不公开理由")
    @Column(name = "handle_content")
    private String handleContent;

    @ApiModelProperty("处理状态：提交(1:接受，2:拒绝 )，审核(3:回退，4:拒绝，5:同意)")
    @Column(name = "handle_status")
    private Integer handleStatus;

    @ApiModelProperty("拒绝原因/审核意见")
    @Column(name = "handle_comment")
    private String handleComment;

    @ApiModelProperty("处理状态。1：草稿，2：提交")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty("附件id。逗号分隔")
    @Column(name = "file_id")
    private String fileId;

    @ApiModelProperty("附件名称。逗号分隔")
    @Column(name = "filename")
    private String filename;

    @ApiModelProperty("转办人")
    @Column(name = "accept_user")
    private String acceptUser;

    @ApiModelProperty("转办组织")
    @Column(name = "accept_org")
    private String acceptOrg;

    @ApiModelProperty("联系电话")
    @Column(name = "phone")
    private String phone;

    @ApiModelProperty("转办时间")
    @Column(name = "accept_time")
    private Date acceptTime;

    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @Column(name = "update_time")
    private Date updateTime;
}
