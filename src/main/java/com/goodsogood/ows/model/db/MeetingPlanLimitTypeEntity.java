package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * t_meeting_plan_limit_type 实体类
 * <p>
 * 自动发放的活动类型 限制的组织类型
 *
 * <AUTHOR>
 * @create 2019-04-18 11:28
 */
@Data
@ApiModel
@Table(name = "t_meeting_plan_limit_type")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingPlanLimitTypeEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "meeting_plan_limit_type_id")
    @Column(name = "meeting_plan_limit_type_id")
    private Long meetingPlanLimitTypeId;


    @JsonProperty(value = "meeting_plan_limit_id")
    @Column(name = "meeting_plan_limit_id")
    @ApiModelProperty("条件限制id")
    private Long meetingPlanLimitId;


    @ApiModelProperty("组织实际类型")
    @JsonProperty(value = "org_type_child")
    @Column(name = "org_type_child")
    private Integer orgTypeChild;


    @ApiModelProperty("对应的组织类型名称")
    @JsonProperty(value = "org_type_child_name")
    @Column(name = "org_type_child_name")
    private String orgTypeChildName;

}

