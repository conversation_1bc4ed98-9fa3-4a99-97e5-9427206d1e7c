package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 组织生活会征求见实体
 * <AUTHOR>
 * @date 2021.11.23
 */

@Data
@ApiModel
@Table(name = "t_meeting_org_advice")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgLifeAdviceEntity {

    /**
     * advice_id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "advice_id")
    @ApiModelProperty("advice_id")
    private Long adviceId;

    /**
     * 民主生活会id
     */
    @Column(name = "life_id")
    @ApiModelProperty("民主生活会id")
    private Long lifeId;

    /**
     * 征求类型 1：直接上传，2：问卷调查，3：座谈会，4：个别访谈
     */
    @Column(name = "advice_type")
    @ApiModelProperty("征求类型 1：直接上传，2：问卷调查，3：座谈会，4：个别访谈")
    private Integer adviceType;

    /**
     * 问卷/座谈会/个别访谈 id
     */
    @Column(name = "data_id")
    @ApiModelProperty("问卷/座谈会/个别访谈 id")
    private Long dataId;

    /**
     * 1：会前，2：会后
     */
    @Column(name = "step")
    @ApiModelProperty("1：会前，2：会后")
    private Integer step;

    /**
     * 是否删除 0：否，1：是
     */
    @Column(name = "is_del")
    @ApiModelProperty("是否删除 0：否，1：是")
    private Integer isDel;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;

    /**
     * last_change_user
     */
    @Column(name = "last_change_user")
    @ApiModelProperty("last_change_user")
    private Long lastChangeUser;

}
