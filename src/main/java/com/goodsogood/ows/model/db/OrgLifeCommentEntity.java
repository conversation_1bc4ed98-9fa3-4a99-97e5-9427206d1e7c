package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 民主评议与组织生活会关联关系表
 */
@Data
@ApiModel
@Table(name = "t_meeting_org_life_comment")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgLifeCommentEntity {
    /**
     * life_comment_id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "life_comment_id")
    @ApiModelProperty("life_comment_id")
    private Long lifeCommentId;

    /**
     * 组织生活会id
     */
    @Column(name = "life_id")
    @ApiModelProperty("民主生活会id")
    private Long lifeId;

    /**
     * 民主评议id
     */
    @Column(name = "comment_id")
    @ApiModelProperty("民主评议id")
    private Long commentId;


    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
