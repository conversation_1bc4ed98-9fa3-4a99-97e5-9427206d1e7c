package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_meeting_user_score_detail 实体类
 *
 * <AUTHOR>
 * @create 2021-11-30
*/
@Data
@ApiModel
@Table(name = "t_meeting_user_score_detail")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MeetingUserScoreDetailEntity {

	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("user_score_detail_id")
	@JsonProperty(value = "user_score_detail_id")
	@Column(name = "user_score_detail_id")
	private Long userScoreDetailId;

	@ApiModelProperty("活动编号")
	@JsonProperty(value = "meeting_id")
	@Column(name = "meeting_id")
	private Long meetingId;

	/**
	 * 1.党支部党员大会 2.党支部委员会会议 3.党小组会 4.党课  5.主题党日
	 */
	@JsonProperty(value = "meeting_type")
	@Column(name = "meeting_type")
	@ApiModelProperty("活动类型")
	private Integer meetingType;

	/**
	 * 积分数量 正负值
	 */
	@JsonProperty(value = "score")
	@Column(name = "score")
	@ApiModelProperty("积分数量")
	private Long score;

	/**
	 * 操作类型 0:添加  1:扣分
	 */
	@JsonProperty(value = "operation_type")
	@Column(name = "operation_type")
	@ApiModelProperty("操作类型")
	private Integer operationType;

	/**
	 * 用户编号
	 */
	@JsonProperty(value = "user_id")
	@Column(name = "user_id")
	@ApiModelProperty("用户编号")
	private Long userId;

	/**
	 * 积分类型  1.党员考勤积分 2.讲课人积分 3.党小组长开展积分  4.支委会成员每年基础分 5.党小组长每年基础分
	 */
	@JsonProperty(value = "score_type")
	@Column(name = "score_type")
	@ApiModelProperty("积分类型")
	private Integer scoreType;

	/**
	 * 积分时间标记:年或年月数值 例:(2021,202109,202111)
	 */
	@JsonProperty(value = "score_sign")
	@Column(name = "score_sign")
	@ApiModelProperty("积分时间标记")
	private Integer scoreSign;

	/**
	 * 积分token
	 */
	@JsonProperty(value = "score_token")
	@Column(name = "score_token")
	@ApiModelProperty("积分token")
	private String scoreToken;

	/**
	 * 备注
	 */
	@JsonProperty(value = "remark")
	@Column(name = "remark")
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 状态  1 成功  2 失败
	 */
	@JsonProperty(value = "status")
	@Column(name = "status")
	@ApiModelProperty("状态")
	private Integer status;


	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	@ApiModelProperty(name = "创建时间")
	private Date createTime;

}

