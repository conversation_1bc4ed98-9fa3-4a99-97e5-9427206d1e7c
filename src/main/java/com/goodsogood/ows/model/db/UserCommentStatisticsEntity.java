package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 党员评议统计
 * @date 2020/1/3
 */
@Data
@ApiModel
@Table(name = "t_meeting_user_comment_statistics")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCommentStatisticsEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "user_comment_statistics_id")
    @Column(name = "user_comment_statistics_id")
    private Long userCommentStatisticsId;

    @ApiModelProperty("党员所属组织ID")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("党员所属组织父级ID")
    @JsonProperty(value = "org_parent_id")
    @Column(name = "org_parent_id")
    private Long orgParentId;

    @ApiModelProperty("党员所属组织名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;

    @ApiModelProperty("组织类型")
    @JsonProperty(value = "org_type_child")
    @Column(name = "org_type_child")
    private Integer orgTypeChild;

    @ApiModelProperty("党员所属组织层级关系")
    @JsonProperty(value = "org_level")
    @Column(name = "org_level")
    private String orgLevel;

    @ApiModelProperty("评议年度")
    @JsonProperty(value = "review_year")
    @Column(name = "review_year")
    private Integer reviewYear;

    @ApiModelProperty("党员数量")
    @JsonProperty(value = "party_number")
    @Column(name = "party_number")
    private Integer partyNumber;

    @ApiModelProperty("参加评议的党员数")
    @JsonProperty(value = "join_comment_number")
    @Column(name = "join_comment_number")
    private Integer joinCommentNumber;

    @ApiModelProperty("评议等级(1-优秀、2-合格、3-基本合格、4-不合格、41-不合格中限期整改的党员数、42-不合格中除名的党员数)")
    @JsonProperty(value = "rating")
    @Column(name = "rating")
    private Integer rating;

    @ApiModelProperty("评议等级对应党员数")
    @JsonProperty(value = "rating_number")
    @Column(name = "rating_number")
    private Integer ratingNumber;

    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;

    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;

    @ApiModelProperty("最后修改人")
    @JsonProperty(value = "last_change_user")
    @Column(name = "last_change_user")
    private Long lastChangeUser;

    @ApiModelProperty("修改时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "region_id")
    @JsonProperty("region_id")
    @ApiModelProperty("区县编号")
    private Long regionId;
}
