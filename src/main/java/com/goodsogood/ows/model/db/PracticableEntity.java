package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel
@Table(name = "t_meeting_practicable")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PracticableEntity {
    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "practicable_id")
    @Column(name = "practicable_id")
    private Long practicableId;

    @ApiModelProperty("领导id")
    @JsonProperty(value = "leader_id")
    @Column(name = "leader_id")
    private Long leaderId;

    @ApiModelProperty("单位id")
    @JsonProperty(value = "unit_id")
    @Column(name = "unit_id")
    private Long unitId;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("领导姓名")
    @JsonProperty(value = "leader_name")
    @Column(name = "leader_name")
    private String leaderName;

    @ApiModelProperty("单位名称")
    @JsonProperty(value = "unit_name")
    @Column(name = "unit_name")
    private String unitName;

    @ApiModelProperty("所属支部名称")
    @JsonProperty(value = "org_name")
    @Column(name = "org_name")
    private String orgName;

    @ApiModelProperty("基层联系点名称")
    @JsonProperty(value = "substrate_name")
    @Column(name = "substrate_name")
    private String substrateName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @ApiModelProperty("走访时间")
    @JsonProperty(value = "interview_time")
    @Column(name = "interview_time")
    private LocalDate interviewTime;


    @ApiModelProperty("创建人")
    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    @JsonProperty(value = "update_user")
    @Column(name = "update_user")
    private Long updateUser;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty("更新时间")
    @JsonProperty(value = "update_time")
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("文件")
    @JsonProperty(value = "attachment")
    @Column(name = "attachment")
    private String attachment;
}
