package com.goodsogood.ows.model.db;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

/**
 *
 * t_meeting_plan_limit 实体类
 *
 * 自动发放的活动类型 组织条件限制 
 *
 * <AUTHOR>
 * @create 2019-04-18 08:43
*/
@Data
@ApiModel
@Table(name = "t_meeting_plan_limit")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingPlanLimitEntity {

	/**
	 * 1 包含;
	 */
	public static final short CONTAINS_YES = 1;
	
	/**
	 * 2 不包含;
	 */
	public static final short CONTAINS_NO = 2;

	/**
	 * 3 仅包含
	 */
	public static final short CONTAINS_ONLY = 3;

	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("id")
	@JsonProperty(value = "meeting_plan_limit_id")
	@Column(name = "meeting_plan_limit_id")
	private Long meetingPlanLimitId;
	
	
	@ApiModelProperty("活动计划id")
	@JsonProperty(value = "meeting_plan_id")
	@Column(name = "meeting_plan_id")
	private Long meetingPlanId;


	@ApiModelProperty("离退休组织：1 包含; 2 不包含; 3 仅包含")
	@JsonProperty(value = "is_retire")
	@Column(name = "is_retire")
	private Short isRetire;
	
	
	@ApiModelProperty("已成立党小组 1 包含; 2 不包含; 3 仅包含")
	@Column(name = "party_group")
	@JsonProperty(value = "party_group")
	private Short partyGroup;
	
	
	@ApiModelProperty("已成立支委会 1 包含; 2 不包含; 3 仅包含")
	@Column(name = "period")
	private Short period;


	// TODO
	@ApiModelProperty("限制的组织类型")
	@JsonProperty(value = "org_type_childes")
	private List<MeetingPlanLimitTypeEntity> limitTypeEntityList;

	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("创建用户")
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;
	
	
	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@ApiModelProperty("更新用户")
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;
	
}

