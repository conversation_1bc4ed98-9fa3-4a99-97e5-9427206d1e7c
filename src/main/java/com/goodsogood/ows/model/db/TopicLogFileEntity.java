package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 *
 * t_topic_log_file 实体类
 *
 * 任务问答题回答的附件
 *
 * <AUTHOR>
 * @create 2018-10-30 10:25
*/
@Data
@ApiModel
@Table(name = "t_topic_log_file")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopicLogFileEntity {
	
	@Id
	@JsonProperty(value = "topic_log_file_id")
	@Column(name = "topic_log_file_id")
	private Long topicLogFileId;

	@Transient
	private Long id;
	
	
	@JsonProperty(value = "topic_log_id")
	@Column(name = "topic_log_id")
	private Long topicLogId;
	
	
	@JsonProperty(value = "meeting_topic_id")
	@Column(name = "meeting_topic_id")
	private Long meetingTopicId;
	
	
	@JsonProperty(value = "topic_id")
	@Column(name = "topic_id")
	private Long topicId;
	
	
	@Column(name = "name")
	private String name;
	
	
	@Column(name = "path")
	private String path;
	
	
	@JsonProperty(value = "file_name")
	@Column(name = "file_name")
	private String fileName;
	
	
	@Column(name = "size")
	private Long size;


	@Column(name = "is_del")
	@JsonProperty(value = "is_del")
	@ApiModelProperty(value = "0正常 1删除")
	private Long is_del;

	
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;
	
	
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;


	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;
	
	/**
	 * @since v3.0.0
	 */
	@ApiModelProperty("任务组织关联表id")
	@JsonProperty(value = "topic_org_id")
	@Column(name = "topic_org_id")
	private Long topicOrgId;
}

