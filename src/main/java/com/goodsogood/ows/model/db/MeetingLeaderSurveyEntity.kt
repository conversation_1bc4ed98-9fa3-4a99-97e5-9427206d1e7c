package com.goodsogood.ows.model.db

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer
import org.hibernate.validator.constraints.Range
import java.time.LocalDate
import java.time.LocalDateTime
import javax.persistence.*
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotNull
import javax.validation.constraints.Size
import kotlin.jvm.Transient

@Entity
@Table(name = "t_meeting_leader_survey")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
class MeetingLeaderSurveyEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "leader_survey_id", nullable = false)
    var id: Long? = null

    // org_id
    @Column(name = "org_id", nullable = false)
    var orgId:Long? = null

    // 领导名称
    @Transient
    var leader: String? = null

    // 调研信息
    @Column(name = "information", nullable = false)
    var information:String? = null

    // 调研方式 1.座谈访谈、2.随机走访、3.问卷调查（轻流）、4.问卷调查、5.专家调查、6.抽样调查、7.统计分析、8.其他
    @get:NotBlank(message = "调研方式不能为空")
//    @get:Range(
//        min = 1,
//        max = 8,
//        message = "调研方式只能为1.座谈访谈、2.随机走访、3.问卷调查（轻流）、4.问卷调查、5.专家调查、6.抽样调查、7.统计分析、8.其他"
//    )
    @Column(name = "interview", nullable = false)
    var interview: String? = null

    // 调研主体(1.领导、2.部门)
    @get:NotNull(message = "调研主体不能为空")
    @get:Range(min = 1, max = 2, message = "调研主体只能为1或2")
    @Column(name = "subject", nullable = false)
    var subject: Int? = null

    // 调研对象名称
    @get: Size(max = 100, message = "调研对象名称长度为1-100个字")
    @Column(name = "target")
    var target: String? = null

    // 调研对象id
//    @get:NotBlank(message = "调研对象id不能为空")
//    @field: NotBlank(message = "调研对象id不能为空")
    @Column(name = "target_id")
    var targetId: String? = null

    // 调研时间
    @Column(name = "survery_time")
    var surveryTime: LocalDate? = null

    // 年度
    @Column(name = "year")
    var year: Int? = null

    // 人员数量
    @Column(name = "num_people")
    var numPeople: Int? = null

    // 调研问题
    @Column(name = "question")
    var question: String? = null

    // 调研地点
    @get: Size(max = 900)
    @Column(name = "location", length = 900)
    var location: String? = null

    // 客户数量
    @Column(name = "num_customer")
    var numCustomer: Int? = null

    // 基本情况
    @Column(name = "basic_info")
    var basicInfo: String? = null

    // 调研类型（1. 日常调研、2.年度调研））
    @Column(name = "survery_type")
    var surveryType: Int? = null

    // 调研报告 附件
    @Column(name = "attachment")
    var attachment: String? = null

    @Column(name = "create_user", nullable = false)
    var createUser: Long? = null

    @Column(name = "create_time", nullable = false)
    var createTime: LocalDateTime? = null

    @Column(name = "update_user")
    var updateUser: Long? = null

    @get: JsonFormat
        (shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer::class)
    @JsonDeserialize(using = LocalDateTimeDeserializer::class)
    @Column(name = "update_time")
    var updateTime: LocalDateTime? = null
}