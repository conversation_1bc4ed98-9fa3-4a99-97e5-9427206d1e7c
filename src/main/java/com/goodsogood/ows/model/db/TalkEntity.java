package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 谈心谈话实体
 */

@Data
@ApiModel
@Table(name = "t_meeting_talk")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TalkEntity {

    /**
     * talk_id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "talk_id")
    @ApiModelProperty("talk_id")
    private Long lifeTalkId;

    /**
     * 民主生活会id
     */
    @Column(name = "life_id")
    @ApiModelProperty("民主生活会id")
    private Long lifeId;

    /**
     * 谈心谈话id
     */
    @Column(name = "talk_id")
    @ApiModelProperty("谈心谈话id")
    private Long talk_id;

    /**
     * 1：成员之间，2：成员与部门，3：成员与支部，4：成员与联系点，5：个人访谈
     */
    @Column(name = "talk_type")
    @ApiModelProperty("1：成员之间，2：成员与部门，3：成员与支部，4：成员与联系点，5：个人访谈")
    private Integer talkType;

    /**
     * 1：会前，2：会后
     */
    @Column(name = "step")
    @ApiModelProperty("1：会前，2：会后")
    private Integer step;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;

}
