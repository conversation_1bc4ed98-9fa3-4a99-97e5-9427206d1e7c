package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 烟草任务流水实体
 * <AUTHOR>
 * @date 2021.08.23
 */
@Data
@ApiModel
@Table(name = "t_meeting_tobacco_task_flow")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TobaccoTaskFlowEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "flow_id")
    @ApiModelProperty("id")
    private Long flowId;

    @Column(name = "task_id")
    @ApiModelProperty("id")
    private Long taskId;

    @Column(name = "region_id")
    @ApiModelProperty("区县id")
    private Long regionId;

    @Column(name = "accept_id")
    @ApiModelProperty("接收 人员/组织id")
    private Long acceptId;

    @Column(name = "handle_status")
    @ApiModelProperty("执行状态（2：已提交，3：已通过，4：未通过，5：已退回）")
    private Integer handleStatus;

    @Column(name = "content")
    @ApiModelProperty("审核意见")
    private String content;

    @Column(name = "handle_user")
    @ApiModelProperty("执行用户")
    private String handleUser;

    @Column(name = "handle_org")
    @ApiModelProperty("执行组织")
    private String handleOrg;

    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

}
