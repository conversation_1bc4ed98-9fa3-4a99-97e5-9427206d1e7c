package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 南岸网信办任务实体
 * <AUTHOR>
 * @date 2021.07.29
 */
@Data
@ApiModel
@Table(name = "t_meeting_sbw_task")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SbwTaskEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @Column(name = "task_id")
    private Long taskId;

    @ApiModelProperty("区县id")
    @Column(name = "region_id")
    private Long regionId;

    @ApiModelProperty("组织id")
    @Column(name = "org_id")
    private Long orgId;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name")
    private String orgName;

    @ApiModelProperty("任务类型。1：工作任务，2：转办单")
    @Column(name = "task_type")
    private Integer taskType;

    @ApiModelProperty("任务标题")
    @Column(name = "title")
    private String title;

    @ApiModelProperty("任务编号")
    @Column(name = "number")
    private String number;

    @ApiModelProperty("时间类型 1:本月内 2:本季度内 3:本年内 4:自定义时间")
    @Column(name = "time_type")
    private Integer timeType;

    @ApiModelProperty("开始时间")
    @Column(name = "begin_time")
    private Date beginTime;

    @ApiModelProperty("截至时间")
    @Column(name = "end_time")
    private Date endTime;

    @ApiModelProperty("舆情来源")
    @Column(name = "source")
    private String source;

    @ApiModelProperty("舆情分类")
    @Column(name = "type_id")
    private Long typeId;

    @ApiModelProperty("舆情概要")
    @Column(name = "content")
    private String content;

    @ApiModelProperty("审核组织id")
    @Column(name = "verify_org_id")
    private Long verifyOrgId;

    @ApiModelProperty("审核组织名")
    @Column(name = "verify_org_name")
    private String verifyOrgName;

    @ApiModelProperty("附件id。逗号分隔")
    @Column(name = "file_id")
    private String fileId;

    @ApiModelProperty("附件名称。逗号分隔")
    @Column(name = "filename")
    private String filename;

    @ApiModelProperty("通知类型。1：短信，2：微信")
    @Column(name = "notice")
    private Integer notice;

    @ApiModelProperty("备注")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty("任务状态。-2: 不予处理 -1: 待处理  1：草稿，2：未开始，3：进行中，4：已结束")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty("是否删除。0：否，1：是")
    @Column(name = "is_del")
    private Integer isDel;

    @ApiModelProperty("0:不予处理 1:处理")
    @Column(name = "is_handle")
    private Integer isHandle;

    @ApiModelProperty("不予处理原因")
    @Column(name = "no_handle_content")
    private String noHandleContent;

    @ApiModelProperty("创建用户id")
    @Column(name = "create_user")
    private Long createUser;

    @ApiModelProperty("更新用户id")
    @Column(name = "update_user")
    private Long updateUser;

    @ApiModelProperty("创建时间")
    @Column(name = "create_time")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @Column(name = "update_time")
    private Date updateTime;
}
