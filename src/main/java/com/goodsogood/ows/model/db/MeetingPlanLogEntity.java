package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * t_meeting_plan_log 实体类
 *
 * <AUTHOR>
 * @create 2018-10-24 17:56
*/
@Data
@ApiModel
@Table(name = "t_meeting_plan_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingPlanLogEntity{
	
	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("id")
	@JsonProperty(value = "meeting_plan_log_id")
	@Column(name = "meeting_plan_log_id")
	private Long meetingPlanLogId;


	@ApiModelProperty("组织生活id")
	@JsonProperty(value = "meeting_plan_id")
	@Column(name = "meeting_plan_id")
	private Long meetingPlanId;
	
	
	@ApiModelProperty("重试次数")
	@Column(name = "num")
	private Integer num;
	
	
	@ApiModelProperty("创建时间")
	@JsonProperty(value = "create_time")
	@Column(name = "create_time")
	private Date createTime;
	
	
	@ApiModelProperty("创建人id")
	@JsonProperty(value = "create_user")
	@Column(name = "create_user")
	private Long createUser;
	
	
	@ApiModelProperty("更新时间")
	@JsonProperty(value = "update_time")
	@Column(name = "update_time")
	private Date updateTime;
	
	
	@ApiModelProperty("最后更新人id")
	@JsonProperty(value = "last_change_user")
	@Column(name = "last_change_user")
	private Long lastChangeUser;
	
}

