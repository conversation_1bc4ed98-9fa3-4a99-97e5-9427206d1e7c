package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 组织生活会配置表实体
 * <AUTHOR>
 * @date 2021.11.23
 */

@Data
@ApiModel
@Table(name = "t_meeting_file_org_model")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgLifeFileModelEntity {
    /**
     * model_id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "model_id")
    @ApiModelProperty("model_id")
    private Long modelId;

    /**
     * 模块名
     */
    @Column(name = "name")
    @ApiModelProperty("模块名")
    private String name;

    /**
     * 打包分类
     */
    @Column(name = "pack_name")
    @ApiModelProperty("打包分类名称")
    private String packName;

    /**
     * type
     */
    @Column(name = "type")
    @ApiModelProperty("民主生活会的打包分类，该字段沿用民主生活会分类")
    private Integer type;

    /**
     * org_life_type
     */
    @Column(name = "org_life_type")
    @ApiModelProperty("组织生活会的打包分类")
    private Integer orgLifeType;


    /**
     * 是否需要精确数据id 0：否，1：是
     */
    @Column(name = "`check`")
    @ApiModelProperty("是否需要精确数据id 0：否，1：是")
    private Integer check;
}
