package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * t_meeting_org_group 实体类
 * <p>
 * 会议关联的党小组
 *
 * <AUTHOR>
 * @create 2019-09-19 10:57
 */
@Data
@ApiModel
@Table(name = "t_meeting_org_group")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingOrgGroupEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @JsonProperty(value = "meeting_org_group_id")
    @Column(name = "meeting_org_group_id")
    private Long meetingOrgGroupId;


    @JsonProperty(value = "meeting_id")
    @Column(name = "meeting_id")
    private Long meetingId;


    @ApiModelProperty("党小组ID")
    @JsonProperty(value = "org_group_id")
    @Column(name = "org_group_id")
    @NotNull(message = "{NotNull.OrgGroup.orgGroupId}")
    private Long orgGroupId;


    @ApiModelProperty("党小组名称")
    @JsonProperty(value = "org_group_name")
    @Column(name = "org_group_name")
    @NotEmpty(message = "{NotNull.OrgGroup.orgGroupName}")
    private String orgGroupName;


    @ApiModelProperty("状态 1.有效; 2.删除")
    @Column(name = "status")
    private Short status;


    @ApiModelProperty("党小组创建时间")
    @JsonProperty(value = "org_group_create_date")
    @Column(name = "org_group_create_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "{NotNull.OrgGroup.orgGroupCreateDate}")
    private Date orgGroupCreateDate;


    @JsonProperty(value = "create_time")
    @Column(name = "create_time")
    private Date createTime;


    @JsonProperty(value = "create_user")
    @Column(name = "create_user")
    private Long createUser;

}

