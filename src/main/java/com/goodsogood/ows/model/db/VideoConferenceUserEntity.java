package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * t_video_conference_user 实体类
 *
 */
@Data
@ApiModel
@Table(name = "t_video_conference_user")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoConferenceUserEntity {

	@ApiModelProperty("会议id")
	@Column(name = "conference_id")
	private Long conferenceId;

	@ApiModelProperty("参会人员id")
	@Column(name = "user_id")
	private Long userId;

	@ApiModelProperty("参会人员名称")
	@Column(name = "name")
	private String name;

}
