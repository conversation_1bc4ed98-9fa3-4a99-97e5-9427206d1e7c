package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 民主生活会实体
 * <AUTHOR>
 * @date 2021.11.23
 */

@Data
@ApiModel
@Table(name = "t_meeting_life")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeEntity {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "life_id")
    @ApiModelProperty("主键")
    private Long lifeId;

    /**
     * 会议名称
     */
    @Column(name = "title")
    @ApiModelProperty("会议名称")
    private String title;

    /**
     * 年度
     */
    @Column(name = "years")
    @ApiModelProperty("年度")
    private Integer years;

    /**
     * 活动id
     */
    @Column(name = "meeting_id")
    @ApiModelProperty("活动id")
    private Long meetingId;

    /**
     * 区县id
     */
    @Column(name = "region_id")
    @ApiModelProperty("区县id")
    private Long regionId;

    /**
     * 所属组织id
     */
    @Column(name = "org_id")
    @ApiModelProperty("所属组织id")
    private Long orgId;

    /**
     * 所属组织名称
     */
    @Column(name = "org_name")
    @ApiModelProperty("所属组织名称")
    private String orgName;

    /**
     * 所属组织层级
     */
    @Column(name = "org_level")
    @ApiModelProperty("所属组织层级")
    private String orgLevel;



    /**
     * 1：新建，2：保存了准备草稿，3：已结束，4：保存会后梳理草稿，5：已上报或归档
     */
    @Column(name = "status")
    @ApiModelProperty("1：新建，2：保存了准备草稿，3：已结束，4：保存会后梳理草稿，5：已上报或归档")
    private Integer status;

    /**
     * 是否删除 0：否，1：是
     */
    @Column(name = "is_del")
    @ApiModelProperty("是否删除 0：否，1：是")
    private Integer isDel;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * create_user
     */
    @Column(name = "create_user")
    @ApiModelProperty("create_user")
    private Long createUser;

    /**
     * last_change_user
     */
    @Column(name = "last_change_user")
    @ApiModelProperty("last_change_user")
    private Long lastChangeUser;

}
