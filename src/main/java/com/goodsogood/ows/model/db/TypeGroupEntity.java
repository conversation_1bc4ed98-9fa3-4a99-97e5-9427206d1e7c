package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * t_type_group 实体类
 *
 * <AUTHOR>
 * @create 2018-10-19 16:25
 */
@Data
@ApiModel
@Table(name = "t_type_group")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TypeGroupEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    @ApiModelProperty("id")
    @JsonProperty(value = "type_group_id")
    @Column(name = "type_group_id")
    private Long typeGroupId;


    @ApiModelProperty("组合id")
    @JsonProperty(value = "group_id")
    @Column(name = "group_id")
    private Long groupId;


    @ApiModelProperty("类型id")
    @JsonProperty(value = "type_id")
    @Column(name = "type_id")
    private Long typeId;

}

