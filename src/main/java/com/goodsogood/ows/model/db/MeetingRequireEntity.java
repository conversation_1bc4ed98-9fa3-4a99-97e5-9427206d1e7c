package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 *
 * t_meeting_require 实体类
 *
 * <AUTHOR>
 * @create 2018-10-22 15:52
*/
@Data
@ApiModel
@Table(name = "t_meeting_require")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingRequireEntity{
	
	@Id
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty("id")
	@JsonProperty(value = "meeting_require_id")
	@Column(name = "meeting_require_id")
	private Long meetingRequireId;


	@ApiModelProperty("组织生活id")
	@JsonProperty(value = "meeting_plan_id")
	@Column(name = "meeting_plan_id")
	private Long meetingPlanId;
	
	
	@ApiModelProperty("类型id")
	@JsonProperty(value = "type_id")
	@Column(name = "type_id")
	private Long typeId;
	
	
	@ApiModelProperty("所属类别id。冗余")
	@JsonProperty(value = "category_id")
	@Column(name = "category_id")
	private Long categoryId;
	
	
	@ApiModelProperty("类型。冗余")
	@Column(name = "type")
	private String type;
	
	
	@ApiModelProperty("类别。冗余")
	@Column(name = "category")
	private String category;


	@ApiModelProperty("要求举办次数。")
	@JsonProperty(value = "meeting_num")
	@Column(name = "meeting_num")
	private Integer meetingNum;
	
	
	@ApiModelProperty("未执行扣分。")
	@Column(name = "deduct")
	private Float deduct;
	
	
	@ApiModelProperty("是否需要签到。0：不需要；1：需要")
	@JsonProperty(value = "is_sign_in")
	@Column(name = "is_sign_in")
	private Short isSignIn;
	
	
	@ApiModelProperty("是否需要填写决议 0：不需要；1：需要")
	@JsonProperty(value = "is_w_resolution")
	@Column(name = "is_w_resolution")
	private Short isWResolution;
	
}

