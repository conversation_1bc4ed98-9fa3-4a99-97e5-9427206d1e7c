package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.TypeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/1/16
 */
@Data
@ApiModel
public class TypeForm {

    @ApiModelProperty("id")
    @JsonProperty(value = "type_id")
    private Long typeId;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("所属类别id")
    @JsonProperty(value = "category_id")
    private Long categoryId;

    @ApiModelProperty("类别。冗余")
    private String category;

    @ApiModelProperty("类型")
    private Integer countType;

    public TypeForm(TypeEntity typeEntity, Integer model) {
        this.typeId = typeEntity.getTypeId();
        this.type = typeEntity.getType();
        this.categoryId = typeEntity.getCategoryId();
        this.category = typeEntity.getCategory();
        this.countType = model;
    }
}
