package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
data class CommentExcellentForm(
    // 党员数量
    var partyMemberNum: Int? = 0,
    // 优秀党员数量
    var excellentNum: Int? = 0
)

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
data class CommentUserAppraisalDataForm(

    var commentId: Long? = null,

    var orgName: String? = null,

    var year: String? = null,

    var time: String = "",

    var userList: MutableList<UserDataForm> = mutableListOf()
) {
    override fun toString(): String {
        return "CommentUserAppraisalDataForm(orgName=$orgName, year=$year, time=$time)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
data class UserDataForm (

    var index: Int = 0,

    var selfRating: Int? = null,

    var username: String? = null,

    var excellent: Int = 0,

    var qualified: Int = 0,

    var basic: Int = 0,

    var unqualified: Int = 0
) {
    override fun toString(): String {
        return "UserDataForm(index=$index, username=$username, excellent=$excellent, qualified=$qualified, basic=$basic, unqualified=$unqualified)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
data class CommentStatisticalDataForm (

    @ApiModelProperty("优秀")
    var excellent: Int = 0,

    @ApiModelProperty("合格")
    var qualified: Int = 0,

    @ApiModelProperty("基本合格")
    var basicQualified: Int = 0,

    @ApiModelProperty("不合格")
    var unqualified: Int = 0,

    @ApiModelProperty("未录入")
    var other: Int = 0,
) {
    override fun toString(): String {
        return "CommentStatisticalDataForm(excellent=$excellent, qualified=$qualified, basicQualified=$basicQualified, unqualified=$unqualified, other=$other)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
data class SendCommentSelfMsg(

    var userName: String? = null,

    var year: Int? = null
) : SendMsgForm() {

    override fun toString(): String {
        return "SendCommentSelfMsg( userName=$userName, year=$year, ${super.toString()})"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
data class SendCommentAppraisalMsg(

    var userName: String? = null,

    var num: Int? = null
) : SendMsgForm() {

    override fun toString(): String {
        return "SendCommentSelfMsg(userName=$userName, num=$num, ${super.toString()})"
    }
}