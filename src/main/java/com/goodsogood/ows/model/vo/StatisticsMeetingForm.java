package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.ToString;

/**
 * @program: ows-meeting
 * @description: 统计组织信息
 * @author: <PERSON><PERSON>
 * @create: 2019-04-22 14:03
 **/
@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticsMeetingForm {
    /**
     * 参与次数
     */
    private  Long countNum;

    /**
     * 统计类型Id
     */
    private Long typeId;

    /**
     * 统计类型名称
     */
    private String typeName;

    /**
     * 统计时间 分成yyyy-MM-dd 数据
     */
    private String staTime;
}
