package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 任务完成情况统计vo
 *
 * <AUTHOR>
 * @create 2018年11月9日 16:08:21
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskStatsForm {

    @ApiModelProperty(value = "活动任务（未完成）总数")
    @JsonProperty("meeting_task_count")
    private int meetingTaskCount;

    @ApiModelProperty(value = "任务任务（未完成）总数")
    @JsonProperty("topic_task_count")
    private int topicTaskCount;
}
