package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.db.MeetingAgendaEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-08 9:51:00
 * @description UpdateAgendaForm
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UpdateAgendaForm {

    @ApiModelProperty("meetingId")
    private Long meetingId;

    @ApiModelProperty("议程")
    @JsonProperty(value = "agenda")
    @Valid
    private List<MeetingAgendaEntity> agenda = new ArrayList<>();
}
