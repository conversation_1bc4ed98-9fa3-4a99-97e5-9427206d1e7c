package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Description: 双重组织生活统计条件
 *
 * <AUTHOR>
 * @version 2019/7/25 15:12
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SasUserOrgLifeForm {
    @JsonProperty("uids")
    @ApiModelProperty("用户ids")
    private List<Long> uids;

    @JsonProperty("org_id")
    @ApiModelProperty("举办会议的组织 ")
    private Long orgId;

    @JsonProperty("sign_status")
    @ApiModelProperty("用户签到状态 1：已签到（默认） 2：未签到 3：因公请假 4：因私请假 5: 缺席")
    private List<Integer> signStatus;

    @JsonProperty("tags")
    @ApiModelProperty("会议用户标识 1：记录人员，2：主持人，3：参与人员 ，4：列席人员")
    private List<Integer> tags;
}
