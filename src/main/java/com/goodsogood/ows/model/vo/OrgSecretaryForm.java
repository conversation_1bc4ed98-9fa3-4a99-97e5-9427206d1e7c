package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgSecretaryForm {

	@ApiModelProperty("组织ID")
	@JsonProperty(value = "org_id")
	private Long orgId;

	@ApiModelProperty("组织书记Id")
	@JsonProperty(value = "user_id")
	private String userId;

	@ApiModelProperty("组织书记名称")
	@JsonProperty(value = "user_name")
	private String userName;
}
