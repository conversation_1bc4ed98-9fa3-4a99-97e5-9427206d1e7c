package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * 活动审批
 *
 * <AUTHOR>
 * @create 2018/10/31 9:11
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingLeaveCheckForm {

    @ApiModelProperty("请假id")
    @JsonProperty(value = "meeting_leave_id")
    @NotNull(message = "{NotNull.meetingLeave.meetingLeaveId}")
    private Long meetingLeaveId;

    @ApiModelProperty("审批类型（2：同意 3：不同意）")
    @JsonProperty(value = "status")
    @NotNull(message = "{NotNull.meetingLeave.status}")
    @Range(min = 2, max = 3, message = "{Range.meetingLeave.status}")
    private Integer status;

    @ApiModelProperty("不同意的原因")
    @JsonProperty(value = "result")
    private String result;
}
