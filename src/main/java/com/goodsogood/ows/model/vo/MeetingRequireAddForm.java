package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.MeetingRequireEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2018-10-22 15:52
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingRequireAddForm {

    @ApiModelProperty("类型id")
    @JsonProperty(value = "type_id")
    @NotNull(message = "{NotNull.type.typeId}")
    private Long typeId;


    @ApiModelProperty("所属类别id。冗余")
    @JsonProperty(value = "category_id")
    private Long categoryId;

    @ApiModelProperty("类型。冗余")
    private String type;

    @ApiModelProperty("类别。冗余")
    private String category;


    @ApiModelProperty("要求举办次数。")
    @JsonProperty(value = "meeting_num")
    @Range(min = 1, message = "{Range.meetingRequire.meetingNum}")
    private Integer meetingNum = 1;


    @ApiModelProperty("未执行扣分。")
    @NotNull(message = "{NotNull.meetingRequire.deduct}")
    @Digits(integer = 3, fraction = 2, message = "{Range.meetingRequire.deduct}")
    @DecimalMax(value = "100", message = "{Range.meetingRequire.deduct}")
    @DecimalMin(value = "0", message = "{Range.meetingRequire.deduct}")
    private Float deduct;


    @ApiModelProperty("是否需要签到。0：不需要；1：需要")
    @NotNull(message = "{NotNull.meetingRequire.isSignIn}")
    @Range(min = 0, max = 1, message = "{Range.meetingRequire.isSignIn}")
    @JsonProperty(value = "is_sign_in")
    private Short isSignIn;


    @ApiModelProperty("是否需要填写决议 0：不需要；1：需要")
    @JsonProperty(value = "is_w_resolution")
    // 2018-12-26 17:53:15 是否填写决议默认为 0
//    @NotNull(message = "{NotNull.meetingRequire.isWResolution}")
//    @Range(min = 0, max = 1, message = "{Range.meetingRequire.isWResolution}")
    private Short isWResolution;


    public MeetingRequireEntity toEntity() {
        MeetingRequireEntity meetingRequireEntity = new MeetingRequireEntity();
        meetingRequireEntity.setTypeId(this.getTypeId());
        meetingRequireEntity.setType(this.getType());
        meetingRequireEntity.setCategory(this.getCategory());
        meetingRequireEntity.setCategoryId(this.getCategoryId());
        meetingRequireEntity.setMeetingNum(this.getMeetingNum());
        meetingRequireEntity.setDeduct(this.getDeduct());
        meetingRequireEntity.setIsSignIn(this.getIsSignIn());
        meetingRequireEntity.setIsWResolution(this.getIsWResolution());
        return meetingRequireEntity;
    }

}

