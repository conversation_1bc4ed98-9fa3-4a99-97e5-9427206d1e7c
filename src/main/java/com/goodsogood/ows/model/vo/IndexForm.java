package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 首页汇总vo
 *
 * <AUTHOR>
 * @create 2018/10/31 13:43
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IndexForm {

    @ApiModelProperty(value = "任务管理")
    @JsonProperty("task_count")
    private int taskCount;

    @ApiModelProperty(value = "活动管理")
    @JsonProperty("meeting_count")
    private int meetingCount;

    @ApiModelProperty(value = "请假管理")
    @JsonProperty("leave_count")
    private int leaveCount;

    @ApiModelProperty(value = "我的活动")
    @JsonProperty("my_meeting_count")
    private int myMeetingCount;
}
