package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * <p>Description:请假管理</p>
 *
 * <AUTHOR>
 * @date 2018/11/5 9:13
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LeaveListForm {

    @ApiModelProperty("请假id")
    @JsonProperty(value = "meeting_leave_id")
    private Long meetingLeaveId;


    @ApiModelProperty("用户名")
    @JsonProperty(value = "user_name")
    private String userName;

    @ApiModelProperty("电话号码")
    @Column(name = "phone")
    private String phone;

    @ApiModelProperty("请假类型（1：因公请假 2：因私请假）")
    @JsonProperty(value = "type")
    private Short type;

    @ApiModelProperty("请假原因")
    @JsonProperty(value = "reason")
    private String reason;

    @ApiModelProperty("审批类型（1：待审批 2：同意 3：不同意 4：取消）")
    @JsonProperty(value = "status")
    private Short status;


    @ApiModelProperty("创建时间（请假时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty(value = "create_time")
    private Date createTime;


    // 活动信息
    @ApiModelProperty("活动id")
    @JsonProperty(value = "meeting_id")
    @Column(name = "meeting_id")
    private Long meetingId;

    @ApiModelProperty("活动名称")
    @JsonProperty(value = "meeting_name")
    private String meetingName;

    @ApiModelProperty("活动类型")
    private String types;

    @ApiModelProperty("举办时间")
    @JsonProperty(value = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("活动状态 1：发起审批中、2：发起未通过、3：活动待举办（待填报结果）、5：填报审查中（第一次）、6：填报未通过（第一次）、7：已提交、8：退回、9：活动已取消、10：填报审查中（第一次之后）、11：填报未通过（第一次之后）12：待复核（第二次提交并且审批通过）13：通过（第一次考核就通过）14：通过（第一次考核被退回后的通过）")
    @JsonProperty(value = "meeting_status")
    private Short meetingStatus;

    // 查询条件
    @ApiModelProperty("活动类型")
    private List<Long> typeIds;

    @ApiModelProperty("审批状态：1.待审批的；2.以审批的")
    private Short checkStatus;

    @ApiModelProperty("审批人id")
    private Long checkUserId;

    @ApiModelProperty("用户所属组织")
    private Long orgId;

    @ApiModelProperty("活动举办时间的起止范围-起始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sstartTime;

    @ApiModelProperty("活动举办时间的起止范围-截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    public LeaveListForm(List<Long> typeIds, Short checkStatus, Long checkUserId, Long orgId, Date sstartTime) {
        this.typeIds = typeIds;
        this.checkStatus = checkStatus;
        this.checkUserId = checkUserId;
        this.orgId = orgId;
        this.sstartTime = sstartTime;
    }
}
