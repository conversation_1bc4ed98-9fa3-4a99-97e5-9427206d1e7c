package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import org.hibernate.validator.constraints.Range
import javax.validation.constraints.NotNull

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = SnakeCaseStrategy::class)
class CommentMemberAppraisalResultForm {

    @ApiModelProperty(value = "民主评议党员表主键")
    var commentMemberId: Long? = null

    @ApiModelProperty(value = "用户ID")
    var userId: Long? = null

    @ApiModelProperty(value = "用户姓名")
    var userName: String? = null

    @ApiModelProperty(value = "用户手机号")
    var phone: String? = null

    @ApiModelProperty(value = "互评等级")
    var rating: Int? = null

    @ApiModelProperty(value = "互评表主键")
    var commentMemberAppraisalId: Long? = null
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = SnakeCaseStrategy::class)
class MemberAppraisalQueryVO {

    @ApiModelProperty(value = "民主评议主键ID")
    @get: NotNull(message = "未查到民主评议相关数据")
    var commentId: Long = 0

    @ApiModelProperty(value = "被评价人memberId")
    var commentMemberId: Long? = null

    @ApiModelProperty(value = "类型 1-普通党员查看互评列表 2-管理员查看互评列表")
    var type: Int = 2

    @ApiModelProperty(value = "页码")
    var page: Int = 0

    @ApiModelProperty(value = "一页大小")
    var pageSize: Int = 10
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = SnakeCaseStrategy::class)
class MemberAppraisalInfoForm {

    @ApiModelProperty(value = "互评表主键")
    var commentMemberAppraisalId: Long? = null

    @ApiModelProperty(value = "评价人ID")
    var appraisalUserId: Long? = null

    @ApiModelProperty(value = "评价人姓名")
    var appraisalUserName: String? = null

    @ApiModelProperty(value = "被评价人Id")
    var commentUserId: Long? = null

    @ApiModelProperty(value = "被评价人姓名")
    var commentUserName: String? = null

    @ApiModelProperty(value = "互评等级")
    var rating: Int? = null

    @ApiModelProperty(value = "互评内容")
    var content: String? = null
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = SnakeCaseStrategy::class)
class MemberAppraisalForm {

    @ApiModelProperty(value = "民主评议党员表主键")
    @get: NotNull(message = "民主评议党员表主键不能为空")
    var commentMemberId: Long? = null

    @ApiModelProperty(value = "互评表主键")
    var commentMemberAppraisalId: Long? = null

    @ApiModelProperty(value = "互评等级")
    @get: NotNull(message = "评价等级不能为空")
    var rating: Int? = null

    @ApiModelProperty(value = "互评内容")
    var content: String? = null
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = SnakeCaseStrategy::class)
class MemberAppraisalBatchForm {

    @ApiModelProperty(value = "批量民主评议党员表ID")
    var memberIds: List<Long> = mutableListOf()

    @ApiModelProperty(value = "互评等级")
    @get: NotNull(message = "评价等级不能为空")
    var rating: Int? = null

    @ApiModelProperty(value = "互评内容")
    var content: String? = null
}