package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 党员奖惩查询表单
 * @date 2019/12/27
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCommendPenalizeUpdateVO {

    @ApiModelProperty("id")
    @JsonProperty(value = "meeting_user_commend_penalize_id")
    @NotNull(message = "{NotNull.commend.id}")
    private Long meetingUserCommendPenalizeId;

    @ApiModelProperty("生效日期")
    @JsonProperty(value = "effective_time")
    @NotNull(message = "{NotNull.Effective.Time}")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String effectiveTime;

    @ApiModelProperty("奖惩类别")
    @JsonProperty(value = "category")
    @NotNull(message = "{NotNull.Commend.Category}")
    private Integer category;

    @ApiModelProperty("奖惩级别")
    @JsonProperty(value = "level")
    private Integer level;

    // 当级别为其他时，奖惩内容不能为空
    @ApiModelProperty("奖惩内容")
    @JsonProperty(value = "content")
    private String content;

    @ApiModelProperty("奖惩名称")
    @JsonProperty(value = "name")
    @NotNull(message = "{NotNull.Commend.Name}")
    private Integer name;

   /* @ApiModelProperty("奖惩原因")
    @JsonProperty(value = "reason")
    @NotNull(message = "{NotNull.Commend.Reason}")
    private String reason;

    @ApiModelProperty("奖励类型(1-及时性表彰、2-定期集中性表彰)")
    @JsonProperty(value = "reward_type")
    private Integer rewardType;

    @ApiModelProperty("受奖批准机关名称")
    @JsonProperty(value = "office_name")
    @NotNull(message = "{NotNull.Office.Name}")
    private String officeName;

    @ApiModelProperty("受奖批准机关级别")
    @JsonProperty(value = "office_level")
    @NotNull(message = "{NotNull.Office.Level}")
    private Integer officeLevel;*/

    @ApiModelProperty("颁奖单位")
    @JsonProperty(value = "award_unit")
    private String awardUnit;

    @ApiModelProperty("相关文件")
    @JsonProperty(value = "related_file")
    private String relatedFile;

    @ApiModelProperty("荣誉图片")
    @JsonProperty(value = "honor_pic")
    private List<MeetingFileForm> honorPic;

    @ApiModelProperty("依据说明")
    @JsonProperty(value = "basis_description")
    @Length(max = 1000, message = "{Length.Commend.Basis}")
    private String basisDescription;

    @ApiModelProperty("文件")
    private List<MeetingFileForm> files;
}
