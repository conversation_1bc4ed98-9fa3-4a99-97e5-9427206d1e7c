package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * meeting 首页汇总vo
 *
 * <AUTHOR>
 * @create 2018/10/31 13:43
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingIndexForm {

    @ApiModelProperty(value = "待举办")
    @JsonProperty("soon_start_count")
    private int soonStartCount;

    @ApiModelProperty(value = "待填报结果")
    @JsonProperty("approval_count")
    private int approvalCount;

    @ApiModelProperty(value = "已提交")
    @JsonProperty("submit_count")
    private int submitCount;

    @ApiModelProperty(value = "退回")
    @JsonProperty("back_count")
    private int backCount;
}
