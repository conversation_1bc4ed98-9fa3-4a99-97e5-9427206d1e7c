package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.MeetingOrgChangeLogEntity;
import lombok.Data;

/**
 * <p>Description:组织变更信息</p>
 *
 * <AUTHOR>
 * @date 2018/11/5 9:13
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OrgChangeForm extends MeetingOrgChangeLogEntity {

    @JsonProperty(value = "org_name")
    private Integer orgType;

}
