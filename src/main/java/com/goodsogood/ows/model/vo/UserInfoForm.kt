package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import lombok.Data
import javax.persistence.Column
import javax.validation.constraints.Min
import javax.validation.constraints.NotNull

/**
 * 用户表单
 *
 * <AUTHOR>
 * @date 2018-03-21
 */
@ApiModel
@JsonIgnoreProperties(ignoreUnknown = true)
class UserInfoForm {
    @ApiModelProperty("id")
    @JsonProperty("user_id")
    var userId: @Min(value = 1, message = "{Min.user.id}") Long? = null

    @ApiModelProperty(value = "标签ID")
    @JsonProperty("tag_id")
    var tagId: String? = null

    @ApiModelProperty("用户组信息")
    @JsonProperty("group_info")
    var groupInfo: String? = null

    @ApiModelProperty("用户名")
    var name: String? = null

    @ApiModelProperty(value = "用户手机号")
    var phone: String? = null

    @ApiModelProperty(value = "脱敏用户手机号")
    @JsonProperty("phone_secret")
    var phoneSecret: String? = null

    @ApiModelProperty(value = "证件类型")
    @JsonProperty("cert_type")
    var certType: Int? = null

    @ApiModelProperty(value = "证件号码")
    @JsonProperty("cert_number")
    var certNumber: String? = null

    @ApiModelProperty(value = "脱敏证件号码")
    @JsonProperty("cert_number_secret")
    var certNumberSecret: String? = null

    @ApiModelProperty(value = "工号")
    @JsonProperty("job_number")
    var jobNumber: String? = null

    @ApiModelProperty(value = "职务")
    var position: String? = null

    @ApiModelProperty(value = "Email")
    var email: String? = null

    @ApiModelProperty(value = "入职日期")
    @JsonProperty("entry_date")
    var entryDate: String? = null

    @ApiModelProperty(value = "性别")
    var gender: Int? = null

    @ApiModelProperty(value = "户籍类型")
    @JsonProperty("census_type")
    var censusType: Int? = null

    @ApiModelProperty(value = "国籍")
    var nationality: Int? = null

    @ApiModelProperty(value = "籍贯省/直辖市")
    @JsonProperty("native_province")
    var nativeProvince: Int? = null

    @ApiModelProperty(value = "籍贯市/区")
    @JsonProperty("native_city")
    var nativeCity: Int? = null

    @ApiModelProperty(value = "婚姻")
    var marriage: Int? = null

    @ApiModelProperty(value = "学历")
    var education: Int? = null

    @ApiModelProperty(value = "政治面貌")
    @JsonProperty("political_type")
    var politicalType: Int? = null

    @ApiModelProperty(value = "民族")
    var ethnic: Int? = null

    @ApiModelProperty(value = "地址")
    var address: String? = null

    @ApiModelProperty(value = "部门ID")
    @JsonProperty("department_id")
    var departmentId: Long? = null

    @ApiModelProperty(value = "部门名称")
    @JsonProperty("department_name")
    var departmentName: String? = null

    @JsonProperty("dep_name")
    var depName: List<String>? = null

    @JsonProperty("dep_id")
    var depId: List<Long>? = null

    @ApiModelProperty("技术等级")
    @JsonProperty("job_grade")
    var jobGrade: Int? = null

    @ApiModelProperty("党组织")
    var communist: Int? = null

    @ApiModelProperty("'团组织")
    @JsonProperty("youth_league")
    var youthLeague: Int? = null

    @ApiModelProperty("'工会组织")
    @JsonProperty("union_member")
    var unionMember: Int? = null

    @ApiModelProperty("'妇女组织")
    @JsonProperty("women_league")
    var womenLeague: Int? = null

    @ApiModelProperty(value = "部门信息")
    var dep: List<Long>? = null

    @ApiModelProperty(value = "新增或修改标志")
    var flag: String? = null

    @ApiModelProperty(value = "短信验证码")
    var captcha: String? = null

    @ApiModelProperty(value = "激活组织ID")
    var oid: Long? = null

    @ApiModelProperty("是否失联 1-是 2-否")
    @JsonProperty("is_lose")
    var isLose: Int? = null

    @ApiModelProperty(value = "工作岗位code")
    @JsonProperty("position_code")
    var positionCode: Long? = null

    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    var birthday: String? = null

    @ApiModelProperty(value = "进入支部时间")
    @JsonProperty("join_party_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    var joinPartyTime: String? = null

    @ApiModelProperty(value = "转正时间")
    @JsonProperty("joining_positive_time")
    var joiningPostiveTime: String? = null

    @ApiModelProperty("进入支部方式 1-市内转入 2-市内转出")
    @JsonProperty("join_party_type")
    var joinPartyType: Int? = null

    @ApiModelProperty("进入系统方式1045")
    @JsonProperty("join_type")
    var joinType: Int? = null

    @ApiModelProperty(value = "原支部ID")
    @JsonProperty("old_org_id")
    var oldOrgId: Long? = null

    @ApiModelProperty(value = "原支部名称")
    @JsonProperty("old_org_name")
    var oldOrgName: String? = null

    @ApiModelProperty(value = "入党时间")
    @JsonProperty("joining_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    var joiningTime: String? = null

    @ApiModelProperty(value = "转正时间")
    @JsonProperty("positive_join_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    var positiveJoinTime: String? = null

    @ApiModelProperty("是否绑定 1-是 2-否")
    @JsonProperty("is_bind")
    var isBind: Int? = null

    @ApiModelProperty(value = "所在支部ID")
    @JsonProperty("org_id")
    var orgId: Long? = null

    @ApiModelProperty(value = "所在支部名称")
    @JsonProperty("org_name")
    var orgName: String? = null

    @ApiModelProperty("是否流动党员 1-是 2-否")
    @JsonProperty("is_flow")
    var isFlow: Int? = null

    @ApiModelProperty("职位")
    var title: String? = null

    @ApiModelProperty("头像地址")
    @JsonProperty("head_url")
    var headUrl: String? = null
}