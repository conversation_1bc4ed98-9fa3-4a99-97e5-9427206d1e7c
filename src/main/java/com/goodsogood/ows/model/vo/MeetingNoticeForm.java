package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @date 2018/8/2 15:25
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MeetingNoticeForm {
    @ApiModelProperty(value = "活动id")
    @JsonProperty("meeting_id")
    @NotNull(message = "{NotNull.meeting.meetingId}")
    private Long meetingId;

    @ApiModelProperty(value = "{{first.DATA}}——参加【活动名称】的通知（可编辑）")
    @Length(max = 120, message = "{Length.Notice.name}")
    @JsonProperty("name")
    private String name;

    @ApiModelProperty("活动地点")
    @Length(max = 120, message = "{Length.Notice.address}")
    private String address;
}
