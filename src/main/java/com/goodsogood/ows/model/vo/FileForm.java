package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 文件上传接收类
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FileForm {
    /**
     * 文件id
     */
    private Long id;
    /**
     * 文件id + 文件后缀
     */
    private String name;
    /**
     * 文件地址
     */
    private String path;

    /**
     * 原文件名
     */
    @JsonProperty("file_name")
    private String fileName;

    /**
     * 文件大小
     */
    private Long size;
}
