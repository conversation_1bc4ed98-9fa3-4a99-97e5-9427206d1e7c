package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.goodsogood.ows.common.pojo.PageBean;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>Description: 查询任务任务条件</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MeetingTopicTaskListForm {
    private Long orgId;//当前组织
    private List<Long> orgIds;//当前登录用户所属全部组织
    private Short status;
    private Short tag;//请求标记:1、发起活动时查询；2、任务完成情况页面查询
    private String name;
    private Date sStartTime;
    private Date eStartTime;
    private Date sEndTime;
    private Date eEndTime;
    private Short isH5;//是否是移动端查询。0：不是；1：是
    private PageBean pageBean;
}
