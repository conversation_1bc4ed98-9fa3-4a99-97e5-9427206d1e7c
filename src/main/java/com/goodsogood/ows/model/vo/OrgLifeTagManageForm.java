package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgLifeTagManageForm {

    @ApiModelProperty("组织生活会id")
    @NotEmpty(message = "{NotEmpty.life.tag.lifeIds}")
    private List<Long> lifeIds;

    @ApiModelProperty("标签id")
    @NotEmpty(message = "{NotEmpty.life.tag.tagIds}")
    private List<Long> tagIds;

    @ApiModelProperty("1：增，2：减")
    @Range(min = 1,max = 2)
    private Integer flag;

}
