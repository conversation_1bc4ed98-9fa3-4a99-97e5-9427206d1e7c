package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.db.MeetingAgendaEntity;
import com.goodsogood.ows.model.db.MeetingResultFileEntity;
import com.goodsogood.ows.model.db.MeetingUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 组织生活-导出报告
 *
 * <AUTHOR>
 * @date 2021/10/11
 */
public class MeetingEventForm {

    @Data
    @ApiModel(value = "基本信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class BasicInformation {
        @ApiModelProperty("活动类型")
        private String types;

        @ApiModelProperty("活动名称")
        private String name;

        @ApiModelProperty("开始时间")
        private String startTime;

        @ApiModelProperty("结束时间")
        private String endTime;

        @ApiModelProperty("活动总时长")
        private String totalHours;

        @ApiModelProperty("理论学习时长")
        private String theoryLearn;

        @ApiModelProperty("活动地点")
        private String address;

        @ApiModelProperty("活动议程标题,按照顿号分隔")
        private String agendaTitles;

        @ApiModelProperty("主持人")
        private List<MeetingUserEntity> hostUser;

        @ApiModelProperty("主持人用户名,顿号分隔")
        private String hostUserName;

        @ApiModelProperty("记录人")
        private List<MeetingUserEntity> recordUser;

        @ApiModelProperty("记录人用户名,顿号分隔")
        private String recordUserName;

        @ApiModelProperty("讲课人")
        private List<MeetingUserEntity> lecturers;

        @ApiModelProperty("讲课人用户名,顿号分隔")
        private String lecturersName;

        @ApiModelProperty("讲课标题")
        private String lectureTitle;

        @ApiModelProperty("应到x人: 0")
        private Integer number1;

        @ApiModelProperty("实到x人: 1")
        private Integer number2;

        @ApiModelProperty("请假x人: 2")
        private Integer number3;

        @ApiModelProperty("未签到x人: 3")
        private Integer number4;

        @ApiModelProperty("补学x人: 4")
        private Integer number5;

        @ApiModelProperty("参加人员")
        private List<MeetingUserEntity> participantUsers;

        @ApiModelProperty("参与人员名称列表 顿号分隔")
        private String participantUserNames;

        @ApiModelProperty("列席人员")
        private List<MeetingUserEntity> attendUsers;

        @ApiModelProperty("列席人员名称列表 顿号分隔")
        private String attendUserNames;

    }

    @Data
    @ApiModel(value = "签到表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class SignInForm {

        @ApiModelProperty("序号")
        private Integer number;

        @ApiModelProperty("用户姓名")
        private String userName;

        @ApiModelProperty("签到情况 1：已签到 2：未签到 3：因公请假 4：因私请假 5: 缺席")
        private String signStatus;

    }

    @Data
    @ApiModel(value = "活动记录（议程）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class ActivationRecord {

        @ApiModelProperty("活动名称")
        private String name;

        @ApiModelProperty("议程标题")
        private String agendaTitle;

        @ApiModelProperty("议程内容")
        private String agendaContent;


    }

    @Data
    @ApiModel(value = "活动附件 + 活动照片")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class ResultFile {
        private List<MeetingResultFileEntity> resultFiles;
    }
}
