package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * t_video_conference 实体类
 *
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoConferenceForm {

	private Integer pageNo;

	private Integer pageSize;

	@ApiModelProperty("会议类型（0-快速会议 1-预约会议）")
	private Integer conferenceType;

	@ApiModelProperty("会议名称")
	private String conferenceName;

	@ApiModelProperty("会议标题")
	private String conferenceTitle;

	@ApiModelProperty("会议Code")
	private String roomCode;

	@ApiModelProperty("最小创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date minCreateTime;

	@ApiModelProperty("最大创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date maxCreateTime;

	@ApiModelProperty("是否查询历史会议（0-否 1-是）")
	private Integer isHistory;

}
