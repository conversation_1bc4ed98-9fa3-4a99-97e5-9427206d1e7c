package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 新建第三方推送任务
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class ThirdAddForm {

    @ApiModelProperty("开始时间")
    private Date beginTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("任务标题")
    private String taskTitle;

    @ApiModelProperty("第三方任务id")
    private String sourceId;

    @ApiModelProperty("第三方标签")
    private String sourceMark;

    @ApiModelProperty("对应服务网关前缀")
    private String sourceUri;

    @ApiModelProperty("创建组织id")
    private Long createOrg;

    /**
     * 是否需要回调确认结束  0：到点自动完成，1：点击完成，2：第三方回调完成
     */
    @ApiModelProperty("是否需要回调结束")
    private Integer callback;

    @ApiModelProperty("接收用户id")
    private List<Long> users;
}
