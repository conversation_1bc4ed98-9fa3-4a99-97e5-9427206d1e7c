package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 党员评议统计展示类
 * @date 2020/1/3
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCommentStatisticsVO {

    @ApiModelProperty("党员所属组织ID")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("党员所属组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("党员数量")
    @JsonProperty(value = "party_number")
    private Integer partyNumber;

    @ApiModelProperty("参加评议的党员数")
    @JsonProperty(value = "join_comment_number")
    private Integer joinCommentNumber;

    @ApiModelProperty("是否存在是下级（1-是，0-否）")
    @JsonProperty(value = "is_exist_child")
    private Integer isExistChild;

    @ApiModelProperty("民主评议统计详情")
    @JsonProperty(value = "comment_info_list")
    private List<UserCommentInfo> commentInfoList;

    @Data
    @ApiModel
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public class UserCommentInfo {

        @ApiModelProperty("评议等级")
        @JsonProperty(value = "comment_level")
        private int commentLevel;

        /*@ApiModelProperty("处理意见")
        @JsonProperty(value = "deal_opinion")
        private int dealOpinion;*/

        @ApiModelProperty("评议等级数量")
        @JsonProperty(value = "comment_level_number")
        public int commentLevelNumber;
    }
}
