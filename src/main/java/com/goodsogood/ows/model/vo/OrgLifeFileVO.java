package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
//该类用来返回
public class OrgLifeFileVO {
    @ApiModelProperty("附件id")
    @JsonProperty(value = "life_file_id")
    private Long lifeFileId;

    @ApiModelProperty("文件服务器id")
    @JsonProperty(value = "file_id")
    private Long fileId;

    @ApiModelProperty("附件真实name")
    @JsonProperty(value = "file_name")
    private String fileName;

    @ApiModelProperty("附件下载name")
    @JsonProperty(value = "file_name_down")
    private String fileNameDown;

    @ApiModelProperty("附件url")
    @JsonProperty(value = "file_url")
    private String fileUrl;

    @Column(name = "user_id")
    @ApiModelProperty("上传人id")
    private Long userId;

    @ApiModelProperty("上传用户名")
    private String username;

    @ApiModelProperty("modelId")
    private Integer type;

    @ApiModelProperty("是否直接上传 0：否，1：是")
    private Integer isDirect;

    @ApiModelProperty("是否上报 0：否，1：是")
    @JsonProperty(value = "is_submit")
    private Integer isSubmit;

    @ApiModelProperty("数据行id")
    @JsonProperty(value = "data_id")
    private Long dataId;
}
