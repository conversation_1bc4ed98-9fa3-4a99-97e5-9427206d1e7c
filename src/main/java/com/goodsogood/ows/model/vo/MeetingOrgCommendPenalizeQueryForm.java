package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.vo.request.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @describe 查询组织奖惩信息列表
 * @date 2019-12-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class MeetingOrgCommendPenalizeQueryForm extends PageRequest implements Serializable {

    @ApiModelProperty("奖惩类别")
    @JsonProperty(value = "category")
    private Integer category;

    @ApiModelProperty("奖惩级别")
    @JsonProperty(value = "level")
    private Integer level;

    @ApiModelProperty("奖励或者惩罚名称")
    @JsonProperty(value = "name")
    private Integer name;

    @ApiModelProperty("奖惩类型（1:奖励 2:惩罚）")
    @JsonProperty(value = "type")
    private Integer type;


    @ApiModelProperty("批准开始日期")
    @JsonProperty(value = "ratify_start_time")
    private String ratifyStartTime;


    @ApiModelProperty("批准结束日期")
    @JsonProperty(value = "ratify_end_time")
    private String ratifyEndTime;


    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;


    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;


    @ApiModelProperty("组织level")
    @JsonProperty(value = "org_level")
    private String orgLevel;

    @ApiModelProperty("数据状态 1 正常 2删除")
    private Integer status = 1;

    @ApiModelProperty("奖励字段")
    @JsonProperty(value = "award_fields")
    private List<String> awardFields;

    @ApiModelProperty("惩罚字段")
    @JsonProperty(value = "penalize_fields")
    private List<String> penalizeFields;
}
