package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 组织的实体
 * <AUTHOR>
 * @create 2018-10-22 14:13
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgForm {
     /**
      * id
      */
     @JsonIgnore
     private Long meetingOrgId;

     /**
      * 组织生活id
      */
     @JsonIgnore
     private Long meetingPlanId;

     /** 组织id */
     @JsonProperty(value = "org_id")
     private Long orgId;

     /** 组织名称 */
     @JsonProperty(value = "org_name")
     private String orgName;
}
