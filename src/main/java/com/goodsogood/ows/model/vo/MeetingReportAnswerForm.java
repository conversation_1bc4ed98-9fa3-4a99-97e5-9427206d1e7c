package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.TopicLogFileEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 任务的具体答案，包含了单选和多选的被选项
 * <AUTHOR>
 * @create 2018-10-24 15:43
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingReportAnswerForm {


     /**  */
     @JsonProperty(value = "content_id")
     @NotNull(message = "{NotNull.topic.report.content-id}")
     private Long contentId;

     /** 选项的类型 */
     @NotNull(message = "{NotNull.topic.report.type}")
     @Min(value = 1, message = "{Min.topic.type}")
     @Max(value = 3, message = "{Max.topic.type}")
     private Integer type;

     /** 回答的文本内容 */
     @Length(max = 5000, message = "{Length.topic.report.content}")
     @JsonProperty(value = "ans_cnt")
     private String content;

     @JsonProperty(value = "answer")
     private List<Integer> opts;

     /** 回到问答题的附件 */
//     @Size(max = 9, message = "{Size.result.files}")
     private List<TopicLogFileEntity> files = new ArrayList<>();

}
