package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: mengting
 * @Date: 2022/4/2 9:10
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SendMsgFormMeeting {
    @JsonProperty("user_id")
    private Long userId;

    //用户名称
    @JsonProperty("user_name")
    private String userName;

    @JsonProperty("task_name")
    private String taskName;

    @JsonProperty("source_name")
    private String sourceName;

//    @JsonProperty("target_url")
//    private String targetUrl;

}
