package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 *
 * t_meeting 查询条件
 *
 * 活动表
 *
 * <AUTHOR>
 * @create 2018-10-25 10:53
*/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MyMeetingListForm {
	@ApiModelProperty("创建人所属组织id")
	@JsonProperty(value = "org_id")
	private Long orgId;

    @ApiModelProperty("活动id")
	@JsonProperty(value = "meeting_id")
	private Long meetingId;

	@ApiModelProperty("当前用户id")
	@JsonProperty(value = "user_id")
	private Long userId;

    @ApiModelProperty("活动名称")
	@Column(name = "name")
	@NotBlank(message = "{NotBlank.meeting.name}")
	@Length(max = 50, message = "{Length.meeting.name}")
	private String name;


    @ApiModelProperty("举办时间")
	@JsonProperty(value = "start_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date startTime;

    @ApiModelProperty("举办时间")
	@JsonProperty(value = "end_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date endTime;

	@ApiModelProperty("活动状态。1：待举办；2：进行中：3：结束")
	private Short status;


	@ApiModelProperty("活动类型")
	private List<Long> typeIds;

	@JsonProperty("region_id")
	@ApiModelProperty("区县编号")
	private Long regionId;

}

