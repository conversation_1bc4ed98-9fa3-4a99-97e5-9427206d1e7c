package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 纪实草稿
 *
 * <AUTHOR>
 * @date 2020/12/14
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingDraftSaveForm {

    @ApiModelProperty("内容")
    @NotBlank(message = "{NotBlank.MeetingDraftSaveForm.content}")
    private String content;

    @ApiModelProperty("类型 0-发起活动，1-已有活动之后录入活动，2-直接录入活动")
    @NotNull(message = "{NotNull.MeetingDraftSaveForm.type}")
    private Integer type;

    @ApiModelProperty("会议id")
    @JsonProperty(value = "meeting_id")
    private Long meetingId;

}
