package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import org.hibernate.annotations.Formula
import org.hibernate.annotations.NamedNativeQuery
import javax.persistence.Column
import javax.persistence.ColumnResult
import javax.persistence.ConstructorResult
import javax.persistence.SqlResultSetMapping

/**
 * 谈心谈话主表
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class CountMeetingTalkVo @JvmOverloads constructor(

    @ApiModelProperty("组织ID")
    @JsonProperty(value = "org_id")
    @Column(name = "org_id")
    var orgId: Long? = null,

    @ApiModelProperty("单位ID")
    @JsonProperty(value = "unit_id")
    var unitId :Long? = null,

    @ApiModelProperty("单位名称")
    @JsonProperty(value = "unit_name")
    @Column(name = "name")
    var unitName: String? = null,

    @ApiModelProperty("谈话人次")
    @Column(name = "people_num")
    @JsonProperty(value = "people_num")
    var peopleNum: Int = 0,


    @JsonProperty(value = "type_and_number")
    var typeAndNumber: MutableMap<String, Int>? = mutableMapOf(),

)


@SqlResultSetMapping(
    name = "TypeAndNumberMapping",
    classes = [
        ConstructorResult(
            targetClass = TypeAndNumberDTO::class,
            columns = [
                ColumnResult(name = "talk_type"),
                ColumnResult(name = "A_NUM")
            ]
        )
    ]
)
data class TypeAndNumberDTO(
    val talkType: String,
    val count: Int
)
enum class MeetingTalkType {
    TYPE0, TYPE1, TYPE2, TYPE3, TYPE4, TYPE5, TYPE6, TYPE7, TYPE8, TYPE9, TYPE10, TYPE11, TYPE12
}