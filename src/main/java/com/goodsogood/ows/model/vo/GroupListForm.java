package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.common.pojo.PageBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Description: 活动类型</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class GroupListForm {
    private PageBean pageBean;

    /**
     * 3.0.0新增
     */
    @ApiModelProperty("区县id")
    @JsonProperty("region_id")
    private Long regionId;
}
