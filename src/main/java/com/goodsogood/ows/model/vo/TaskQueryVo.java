package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName : TaskQueryVo
 * <AUTHOR> tc
 * @Date: 2022/2/16 13:53
 * @Description : 活动完成情况
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskQueryVo {
    @ApiModelProperty(value = "执行组织编号")
    @JsonProperty("org_id")
    private Long orgId;

    @ApiModelProperty(value = "活动类型编号")
    @JsonProperty("type_id")
    private Integer typeId;

    @ApiModelProperty(value = "开展次数")
    @JsonProperty("meeting_num")
    private Integer meetingNum;

    @ApiModelProperty(value = "开始月份")
    @JsonProperty("start_month")
    private Integer startMonth;

}
