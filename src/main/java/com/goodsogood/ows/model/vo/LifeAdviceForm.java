package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeAdviceForm {

    @ApiModelProperty("征求意见")
    private List<Advice> advice = new ArrayList<>(1);

    @ApiModelProperty("直接上传时的附件")
    private List<LifeFileVO> files = new ArrayList<>(1);

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Advice{

        @ApiModelProperty("advice_id")
        private Long adviceId;

        @ApiModelProperty("life_id")
        private Long lifeId;

        @ApiModelProperty("关联数据id")
        private Long dataId;

        @ApiModelProperty("征求类型 1：直接上传，2：问卷调查，3：座谈会，4：个别访谈")
        private Integer adviceType;

    }
}
