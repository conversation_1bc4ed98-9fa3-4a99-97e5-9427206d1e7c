package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: mengting
 * @Date: 2022/4/1 13:55
 */

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SendMsgFormTest {

    @JsonProperty("user_id")
    private Long userId;

    //组织名称
    @JsonProperty("task_name")
    private String taskName;

    //用户名称
    @JsonProperty("user_name")
    private String userName;

    @JsonProperty("source_name")
    private String sourceName;

    //跳转地址
    @JsonProperty("target_url")
    private String targetUrl;



}
