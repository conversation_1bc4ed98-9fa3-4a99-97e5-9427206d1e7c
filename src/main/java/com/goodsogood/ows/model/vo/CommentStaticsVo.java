package com.goodsogood.ows.model.vo;

import com.goodsogood.ows.common.Constant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.poi.ss.usermodel.CellType;

import java.util.ArrayList;
import java.util.List;

/**
 * 民主评议统计表导出字段
 */
@AllArgsConstructor
@Getter
public enum CommentStaticsVo {
    FIELFD1(1,"序号",CellType.NUMERIC,null),
    FIELFD2(2,"组织名称",CellType.STRING,"getOrgName"),
    FIELFD3(3,"评议年度",CellType.NUMERIC,"getYear"),
    FIELFD4(4,"党员数",CellType.NUMERIC,"getPartyNumber"),
    FIELFD5(5,"参加评议党员数",CellType.NUMERIC,"getJoinNumber"),
    FIELFD6(6,"优秀人数",CellType.NUMERIC,"getLevel1"),
    FIELFD7(7,"合格人数",CellType.NUMERIC,"getLevel2"),
    FIELFD8(8,"基本合格人数",CellType.NUMERIC,"getLevel3"),
    FIELFD9(9,"不合格人数",CellType.NUMERIC,"getLevel4");
    private Integer code;
    private String name;
    private CellType type;
    private String methodName;


    public static String getName(Integer code) {
        for ( CommentStaticsVo vo : CommentStaticsVo.values()) {
            if (vo.getCode().equals(code)) {
                return vo.getName();
            }
        }
        return " ";
    }

    public static String[] getNames(List<Integer> code) {
        String[] names = new String[code.size()];
        int i = 0;
        for(Integer c: code){
          names[i++] = getName(c);
      }
        return names;
    }


    public static String getMethodNames(Integer code) {
        for ( CommentStaticsVo vo : CommentStaticsVo.values()) {
            if (vo.getCode().equals(code)) {
                return vo.getMethodName();
            }
        }
        return " ";
    }
    public static CellType getCellType(Integer code) {
        for ( CommentStaticsVo vo : CommentStaticsVo.values()) {
            if (vo.getCode().equals(code)) {
                return vo.getType();
            }
        }
        return null;
    }
}


