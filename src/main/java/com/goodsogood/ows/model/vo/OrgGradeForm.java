package com.goodsogood.ows.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgGradeForm {
    String orgName = "";
    String date = "";//yyyy年mm月dd日
    List<OrgForm> orgForm = Arrays.asList(new OrgForm());

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrgForm{
        private String num = "";
        private String username = "";
        private String level1 = "";
        private String level2 = "";
        private String level3 = "";
        private String level4 = "";
    }


}