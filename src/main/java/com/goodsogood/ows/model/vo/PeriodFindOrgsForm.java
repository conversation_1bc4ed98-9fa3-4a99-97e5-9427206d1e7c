package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Auther: ruoyu
 * Date: 19-5-22
 * Description:
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PeriodFindOrgsForm {

    @NotNull
    @JsonProperty("org_id")
    private Long orgId;

    @NotNull
    @JsonProperty("period_query_time")
    private String periodQueryTime;

}
