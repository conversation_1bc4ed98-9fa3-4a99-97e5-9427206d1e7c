package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import javax.validation.constraints.NotNull

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
class CommentApproveResultForm {

    @ApiModelProperty(value = "民主评议ID")
    var commentId: Long? = null

    @ApiModelProperty(value = "组织ID")
    var orgId: Long? = null

    @ApiModelProperty(value = "组织名称")
    var orgName: String?= null

    @ApiModelProperty(value = "年度")
    var year: Int? = null

    @ApiModelProperty(value = "状态 2-待审查，3-审查未通过，4-待审定，5-审定未通过，6-审定通过")
    var status: Int? = null
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
class CommentApproveQueryForm {

    @ApiModelProperty(value = "组织")
    var orgId: Long? = null

    @ApiModelProperty(value = "年度")
    var year: Int? = null

    @ApiModelProperty(value = "状态")
    var status: Int? = null

    @ApiModelProperty(value = "页码")
    var page: Int = 0

    @ApiModelProperty(value = "一页大小")
    var pageSize: Int = 10
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
class ApproveCommentInfoForm {

    @ApiModelProperty(value = "民主评议ID")
    @get: NotNull(message = "民主评议党员表主键不能为空")
    var commentId: Long? = null

    @ApiModelProperty(value = "类型： 1-通过， 2-退回")
    @get: NotNull(message = "类型不为空")
    var type: Int? = null

    @ApiModelProperty(value = "状态")
    var status: Int? = null

    @ApiModelProperty(value = "意见")
    @get: NotNull(message = "意见不能为空")
    var content: String? = null

    constructor()

    constructor(commentId: Long?, status: Int?, content: String?) {
        this.commentId = commentId
        this.status = status
        this.content = content
    }


}