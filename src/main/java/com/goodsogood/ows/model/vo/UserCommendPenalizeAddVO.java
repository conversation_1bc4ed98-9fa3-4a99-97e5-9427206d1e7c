package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 党员奖惩查询表单
 * @date 2019/12/27
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCommendPenalizeAddVO {

    @ApiModelProperty("党员用户ID")
    @JsonProperty(value = "user_id")
    @NotNull(message = "{NotNull.User.Id}")
    private Long userId;

    @ApiModelProperty("生效日期")
    @JsonProperty(value = "effective_time")
    @NotNull(message = "{NotNull.Effective.Time}")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String effectiveTime;

    @ApiModelProperty("奖惩类别")
    @JsonProperty(value = "category")
    @NotNull(message = "{NotNull.Commend.Category}")
    private Integer category;

    @ApiModelProperty("奖惩级别")
    @JsonProperty(value = "level")
    private Integer level;

    @ApiModelProperty("奖惩名称")
    @JsonProperty(value = "name")
    @NotNull(message = "{NotNull.Commend.Name}")
    private Integer name;

    @ApiModelProperty("奖惩名称内容")
    @JsonProperty(value = "content")
    @Length(max = 200,message = "奖惩名称内容不能超过200字")
    private String content;

    @ApiModelProperty("颁奖单位")
    @JsonProperty(value = "award_unit")
    private String awardUnit;

    @ApiModelProperty("相关文件")
    @JsonProperty(value = "related_file")
    private String relatedFile;

    @ApiModelProperty("荣誉图片")
    @JsonProperty(value = "honor_pic")
    private List<MeetingFileForm> honorPic;

    @ApiModelProperty("奖惩类型（1:奖励 2:惩罚）")
    @JsonProperty(value = "type")
    @NotNull(message = "{NotNull.Commend.type}")
    private Integer type;

    @ApiModelProperty("审核状态 1-待审核、2-审核通过、3-审核不通过")
    @JsonProperty(value = "approval_status")
    @Column(name = "approval_status")
    private Integer approvalStatus;

    @ApiModelProperty("依据说明")
    @JsonProperty(value = "basis_description")
    @Length(max = 1000, message = "{Length.Commend.Basis}")
    private String basisDescription;

    @ApiModelProperty("文件")
    private List<MeetingFileForm> files;

    /*@ApiModelProperty("奖惩原因")
    @JsonProperty(value = "reason")
    //@NotNull(message = "{NotNull.Commend.Reason}")
    private String reason;

    @ApiModelProperty("奖励类型(1-及时性表彰、2-定期集中性表彰)")
    @JsonProperty(value = "reward_type")
    private Integer rewardType;

    @ApiModelProperty("受奖批准机关名称")
    @JsonProperty(value = "office_name")
    @NotNull(message = "{NotNull.Office.Name}")
    private String officeName;

    @ApiModelProperty("受奖批准机关级别")
    @JsonProperty(value = "office_level")
    @NotNull(message = "{NotNull.Office.Level}")
    private Integer officeLevel;*/
}
