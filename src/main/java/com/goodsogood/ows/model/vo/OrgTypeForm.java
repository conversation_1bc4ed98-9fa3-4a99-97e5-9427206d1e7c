package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 组织的实体
 * <AUTHOR>
 * @create 2018-10-22 14:13
 **/
@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgTypeForm extends OrgForm{

     /** 组织详细类型 */
     @JsonProperty(value = "org_type_child")
     private Integer orgTypeChild;
}
