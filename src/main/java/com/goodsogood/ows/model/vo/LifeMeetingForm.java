package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 会中详情
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeMeetingForm {

    @ApiModelProperty("民主生活会id")
    private Long lifeId;

    @ApiModelProperty("会议议程(标题)")
    private List<String> agenda;

    @ApiModelProperty("没有议程时的附加提示")
    private String noAgenda;

    @ApiModelProperty("会前文件")
    private Map<Integer,List<LifeFileVO>> files;
}
