package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgLifeCheckForm {

    @ApiModelProperty("id")
    private Long checkId;

    @ApiModelProperty("life_id")
    private Long lifeId;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("所属组织")
    private String orgName;

    @ApiModelProperty("附件")
    private List<OrgLifeFileVO> files;

    @ApiModelProperty("其他上传人")
    private String uploader;

}
