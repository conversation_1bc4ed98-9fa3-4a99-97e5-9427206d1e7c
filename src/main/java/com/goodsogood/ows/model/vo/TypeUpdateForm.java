package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.TypeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>Description: 活动类型</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TypeUpdateForm extends TypeAddForm{

    @ApiModelProperty("id")
    @JsonProperty(value = "type_id")
    @NotNull(message = "{NotNull.type.typeId}")
    private Long typeId;

//    @ApiModelProperty("所属类别id")
//    @JsonProperty(value = "category_id")
//    @NotNull(message = "{NotNull.category.categoryId}")
//    private Long categoryId;
//
//    @ApiModelProperty("类型")
//    @NotBlank(message = "{NotBlank.type.type}")
//    @Length(max = 50,message = "{Length.type.type}")
//    private String type;
    public TypeEntity toEntity() {
        TypeEntity typeEntity = new TypeEntity();
        typeEntity.setTypeId(this.getTypeId());
        typeEntity.setType(this.getType());
        typeEntity.setCategoryId(this.getCategoryId());
        return typeEntity;
    }
}
