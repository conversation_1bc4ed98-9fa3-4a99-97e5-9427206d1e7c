package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Description: 活动类型</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class GroupAllResultForm {

    @ApiModelProperty("id")
    @JsonProperty(value = "group_id")
    private Long groupId;

    @ApiModelProperty("组合id")
    @JsonProperty(value = "type_ids")
    private List<Long> typeIds;

    @ApiModelProperty("组合")
    @JsonProperty(value = "types")
    private String types;
}
