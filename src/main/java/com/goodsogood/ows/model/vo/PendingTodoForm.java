package com.goodsogood.ows.model.vo;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class PendingTodoForm {

    /**
     * 业务系统侧的唯一标识ID，即业务ID,当待办创建成功后，需要更换新的sourceId，保持一个待办任务对应一个sourceId
     * 本平台生成的唯一id
     */
    private String sourceId;

    /**
     * 待办标题
     */
    private String subject;

    /**
     * 创建者的userId
     */
    private String creatorId;

    /**
     * 待办备注描述
     */
    private String description;

    /**
     * 截止时间，Unix时间戳，单位毫秒
     */
    private Long dueTime;

    /**
     * 执行者的userId
     */
    private List<Long> executorIds;

    /**
     * 参与者的userId
     */
    private List<Long> participantIds;

    /**
     * 详情页url跳转地址
     */
    private DetailUrl detailUrl;

    /**
     * 详情页url跳转地址  对象
     */
    @Data
    private class DetailUrl{
        /**
         * APP端详情页url跳转地址
         */
        private String appUrl;

        /**
         * PC端详情页url跳转地址
         */
        private String pcUrl;
    }
}
