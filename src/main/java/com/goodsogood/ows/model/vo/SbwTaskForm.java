package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * 任务展示通用类
 *
 * <AUTHOR>
 * @date 2021/08/02
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SbwTaskForm {

    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("区县id")
    private Long regionId;

    @ApiModelProperty("创建用户id")
    private Long createUser;

    @ApiModelProperty("创建任务的组织id")
    private Long orgId;

    @ApiModelProperty("创建任务的组织名称")
    private String orgName;

    @ApiModelProperty("任务类型。1：工作任务，2：转办单")
    private Integer taskType;

    @ApiModelProperty("任务标题")
    @Length(max = 2000,message = "标题长度超过2000")
    private String title;

    @ApiModelProperty("任务编号")
    @Length(max = 2000,message = "编号长度超过2000")
    private String number;

    @ApiModelProperty("时间类型,1:本月 2:本季度 3:本年 4:自定义")
    private Integer timeType;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("截至时间")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("舆情分类id")
    private Long typeId;

    @ApiModelProperty("舆情来源")
    @Length(max = 2000,message = "舆情来源长度超过2000")
    private String source;

    @ApiModelProperty("舆情概要")
    @Length(max = 2000,message = "舆情概要长度超过2000")
    private String content;

    @ApiModelProperty("审核组织id")
    private Long verifyOrgId;

    @ApiModelProperty("审核组织名")
    private String verifyOrgName;

    @ApiModelProperty("任务状态。1：草稿，2：未开始，3：进行中，4：已结束")
    private Integer status;

    @ApiModelProperty("任务下发组织列表")
    private List<SbwTaskOrgListForm> orgList;

    @ApiModelProperty("主任务信息文件类")
    private List<File> files;

    @ApiModelProperty("任务回执信息文件类")
    private List<ReceiptFile> receiptFiles;

    @ApiModelProperty("转办人")
    private String acceptUser;

    @ApiModelProperty("转办组织")
    private String acceptOrg;

    @ApiModelProperty("联系电话")
    private String phone;

    @ApiModelProperty("转办时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date acceptTime;

    @ApiModelProperty("公开状态")
    private Integer openStatus;

    @ApiModelProperty("不公开理由")
    private String privateReason;

    @ApiModelProperty("处置内容")
    private String handleContent;

    @ApiModelProperty("0:不予处理 1:处理")
    private Integer isHandle;

    @ApiModelProperty("不予处理原因")
    private String noHandleContent;

    @ApiModelProperty("备注")
    @Length(max = 2000,message = "备注长度超过2000")
    private String remark;

    @ApiModelProperty("任务信息展示")
    private SbwHandleForm handle;

    @ApiModelProperty("审核信息展示")
    private CheckMessage checkHandle;

    @ApiModelProperty("执行记录")
    private List<ExecutiveLogging> record;

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CheckMessage {
        @ApiModelProperty("审核id")
        @JsonProperty("check_handle_id")
        private Long handleId;

        @ApiModelProperty("审核(3:回退，4:拒绝，5:同意)")
        @JsonProperty("check_status")
        private Integer handleStatus;

        @ApiModelProperty("拒绝原因/审核意见")
        @JsonProperty("check_comment")
        private String handleComment;

    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class ExecutiveLogging {
        @ApiModelProperty("任务id")
        private Long taskId;

        @ApiModelProperty("执行状态")
        private Integer type;

        @ApiModelProperty("执行状态链")
        private String typeLink;

        @ApiModelProperty("任务执行组织")
        private String orgName;

        @ApiModelProperty("拒绝原因/审核意见 回显")
        private String checkContent;

        @ApiModelProperty("执行时间")
        private Date time;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class File {
        @ApiModelProperty("附件id组")
        private Long fileId;

        @ApiModelProperty("附件名称组")
        private String filename;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReceiptFile {
        @ApiModelProperty("附件id组")
        @JsonProperty("file_id")
        private Long receiptFileId;

        @ApiModelProperty("附件名称组")
        @JsonProperty("filename")
        private String receiptFilename;
    }

}
