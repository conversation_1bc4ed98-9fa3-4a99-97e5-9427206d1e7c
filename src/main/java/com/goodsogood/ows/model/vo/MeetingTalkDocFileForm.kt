package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingTalkDocFileForm {

    @ApiModelProperty("谈心谈话标题")
    var title: String? = null

    @ApiModelProperty("谈心谈话类型")
    var type: String? = null

    @ApiModelProperty("谈话人")
    var username: String? = null

    @ApiModelProperty("被谈话人")
    var toUsername: String? = null

    @ApiModelProperty("开始时间")
    var beginTime: String? = null

    @ApiModelProperty("结束时间")
    var endTime: String? = null

    @ApiModelProperty("谈话地址")
    var location: String? = null

    @ApiModelProperty("谈话内容")
    var list: MutableList<TalkDocContent> = mutableListOf()

    override fun toString(): String {
        return "MeetingTalkDocFileForm(title=$title, type=$type, username=$username, toUsername=$toUsername, beginTime=$beginTime, endTime=$endTime, location=$location, list=$list)"
    }

}

class TalkDocContent {

    var name: String? = "主要内容"

    @ApiModelProperty("谈话标题")
    var title: String? = ""

    @ApiModelProperty("谈话内容")
    var content: String? = ""



    constructor(title: String?, content: String?) {
        this.title = title
        this.content = content
    }

    constructor()

    override fun toString(): String {
        return "TalkDocContent(name=$name, title=$title, content=$content)"
    }
}