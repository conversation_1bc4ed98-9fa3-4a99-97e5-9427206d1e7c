package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 *
 * 纪实结果填报内容
 * <AUTHOR>
 * @create 2018-10-24 15:24
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingReportForm {

     @JsonProperty(value = "meeting_id")
     //@NotNull(message = "{NotNull.report.meeting-id}")
     private Long meetingId;

    /**
     * 用于接收填写任务的答案
     */
     @Valid
     private List<MeetingReportTopicContentForm> topics;

}
