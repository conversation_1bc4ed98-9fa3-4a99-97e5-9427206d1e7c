package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LeaderForm {


    @ApiModelProperty("user_id")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("username")
    @JsonProperty(value = "username")
    private String username;

    @ApiModelProperty("org_id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("org_name")
    @JsonProperty(value = "org_name")
    private String orgName;
}
