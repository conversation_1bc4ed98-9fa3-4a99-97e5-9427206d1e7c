package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 南岸区网信办任务处理vo
 * <AUTHOR>
 * @date 2021.07.30
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SbwHandleForm {

    @ApiModelProperty("id")
    private Long handleId;

    @ApiModelProperty("任务类型。1：工作任务，2：转办单")
    private Integer taskType;

    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("区县id")
    private Long regionId;

    @ApiModelProperty("组织id")
    private Long orgId;

    @ApiModelProperty("操作用户id")
    private Long userId;

    @ApiModelProperty("操作用户组织id")
    private Long userOrgId;

    @ApiModelProperty("操作用户组织名称")
    private String userOrgName;

    @ApiModelProperty("是否建议公开回复。0：否，1：是")
    private Integer openStatus;

    @ApiModelProperty("不公开理由")
    private String privateReason;

    @ApiModelProperty("处理类型。1：我的任务-提交转办单，2：任务审核-审核转办单")
    private Integer flag;

    @ApiModelProperty("处置内容/完成任务情况/不公开理由")
    private String handleContent;

    @ApiModelProperty("处理状态：提交(1:接受，2:拒绝 )，审核(3:回退，4:拒绝，5:同意)")
    private Integer handleStatus;

    @ApiModelProperty("拒绝原因/审核意见")
    private String handleComment;

    @ApiModelProperty("处理状态。1：草稿，2：提交")
    private Integer status;

    @ApiModelProperty("附件id。逗号分隔")
    private String fileId;

    @ApiModelProperty("附件名称。逗号分隔")
    private String filename;

    @ApiModelProperty("转办人")
    private String acceptUser;

    @ApiModelProperty("转办组织")
    private String acceptOrg;

    @ApiModelProperty("联系电话")
    private String phone;

    @ApiModelProperty("转办时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date acceptTime;

    @ApiModelProperty("创建时间")
    private Date createTime;
}
