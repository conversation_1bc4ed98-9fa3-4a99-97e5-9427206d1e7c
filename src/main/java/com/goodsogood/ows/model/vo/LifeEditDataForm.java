package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 会后修改会前材料
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeEditDataForm {

    @ApiModelProperty("民主生活会id")
    @NotNull(message = "{NotNull.life.afterEdit.id}")
    private Long lifeId;

    @ApiModelProperty("增加的文件")
    private List<EditData> add;

    @ApiModelProperty("删除的文件")
    private List<EditData> del;

    @ApiModelProperty("是否提交。0：取消，1：确认修改")
    @NotNull(message = "{NotNull.life.afterEdit.submit}")
    @Range(min = 0,max = 1,message = "{Range.life.afterEdit.submit}")
    private Integer isSubmit;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class EditData{

        @ApiModelProperty("对应数据的主键")
        private Long DataId;

        @ApiModelProperty("所属模块")
        private Integer modelType;

        @ApiModelProperty("是否为文件")
        private Integer isFile;
    }
}
