package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 民主评议更新实体类
 * @date 2019/12/30
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCommentUpdateVO {

    @ApiModelProperty("党员评议ID")
    @JsonProperty(value = "user_comment_id")
    @NotNull(message = "{NotNull.commend.id}")
    private Long userCommentId;

    @ApiModelProperty("评议等级(1-优秀、2-合格、3-基本合格、4-不合格 5-不确定等级)")
    @JsonProperty(value = "rating")
    @NotNull(message = "{NotNull.Comment.Rating}")
    @Range(min = 1, max = 5, message = "{Range.Comment.Rating}")
    private Integer rating;

    @ApiModelProperty("处理意见(1-限期整改，2-除名)")
    @JsonProperty(value = "deal_opinion")
    @Range(min = 1, max = 2, message = "{Range.Comment.Option}")
    private Integer dealOpinion;

    @ApiModelProperty("附加说明")
    @JsonProperty(value = "additional_information")
    @Length(max = 1000, message = "{Length.Comment.Information}")
    private String additionalInformation;

}
