package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeForm {

    @ApiModelProperty("民主生活会id")
    private Long lifeId;

    @ApiModelProperty("会议名称")
    private String title;

    @ApiModelProperty("所属组织")
    private String orgName;

    @ApiModelProperty("标签")
    private String tag;

    @ApiModelProperty("年度")
    private Integer years;

    @ApiModelProperty("状态")
    private Integer status;
}
