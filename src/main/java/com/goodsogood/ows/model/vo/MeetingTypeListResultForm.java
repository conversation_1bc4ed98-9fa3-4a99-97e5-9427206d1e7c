package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.goodsogood.ows.model.db.MeetingTaskEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Description: 发起活动查询活动任务</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MeetingTypeListResultForm {
    @ApiModelProperty("类型组合")
    private List<GroupAllResultForm> groups;

    @ApiModelProperty("活动任务")
    private List<MeetingTaskEntity> tasks;
}
