package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 民族评议展示类
 * @date 2019/12/27
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInfoCommentVO {

    @ApiModelProperty("用户ID")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("用户名称")
    @JsonProperty(value = "user_name")
    private String userName;

    @ApiModelProperty("加密身份证")
    @JsonProperty(value = "cert_number")
    private String certNumber;

    @ApiModelProperty("组织ID")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("性别")
    @JsonProperty(value = "sex")
    private String sex;

    @ApiModelProperty("民族")
    @JsonProperty(value = "ethnic")
    private String ethnic;

    @ApiModelProperty("出生日期")
    @JsonProperty(value = "birthday")
    private String birthday;

    @ApiModelProperty("入党时间")
    @JsonProperty(value = "joining_time")
    private String joiningTime;

    @ApiModelProperty("学历")
    @JsonProperty(value = "education")
    private String education;

    @ApiModelProperty("党内职务")
    @JsonProperty(value = "position_name")
    private String positionName;

    @ApiModelProperty("行政职务")
    @JsonProperty(value = "grade_name")
    private String gradeName;

    @ApiModelProperty("党员评议ID")
    @JsonProperty(value = "user_comment_id")
    private Long userCommentId;

    @ApiModelProperty("评议等级")
    @JsonProperty(value = "comment_level")
    private Integer commentLevel;

}
