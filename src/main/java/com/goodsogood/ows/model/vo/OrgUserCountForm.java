package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName OrgUserCountForm
 * @description
 * @date 2019-07-02 10:28
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgUserCountForm {

    @JsonProperty(value = "org_id")
    private Long orgId;

    @JsonProperty(value = "org_name")
    private String orgName;

    @JsonProperty(value = "user_count")
    private Integer userCount = 0;

    @JsonProperty(value = "party_id")
    private Long partyId;

    @JsonProperty(value = "party_name")
    private String partyName;

}
