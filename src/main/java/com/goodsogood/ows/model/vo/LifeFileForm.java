package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
public class LifeFileForm {
    @ApiModelProperty("文件服务器的附件id")
    @JsonProperty(value = "file_id")
    private Long fileId;

    @ApiModelProperty("附件name")
    @JsonProperty(value = "file_name")
    private String fileName;

    @ApiModelProperty("附件下载name")
    @JsonProperty(value = "file_name_down")
    private String fileNameDown;

    @ApiModelProperty("附件path")
    @JsonProperty(value = "url")
    private String url;

    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public LifeFileForm() {
    }

    public LifeFileForm(Long fileId, String fileName, String url) {
        this.fileId = fileId;
        this.fileName = fileName;
        this.url = url;
    }
}
