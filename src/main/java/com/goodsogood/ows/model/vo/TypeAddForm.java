package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.TypeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>Description: 活动类型</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TypeAddForm {
    @ApiModelProperty("所属类别id")
    @JsonProperty(value = "category_id")
    @NotNull(message = "{NotNull.category.categoryId}")
    private Long categoryId;

    @NotBlank(message = "{NotBlank.type.type}")
    @Length(max = 50,message = "{Length.type.type}")
    @ApiModelProperty("类型")
    @JsonProperty(value = "type")
    private String type;

    /**
     * 特殊类型，默认为 1
     */
    @ApiModelProperty("特殊类型 1:历史组织生活类型  2:民主生活会 3:民主生活会会前学习 4:民主生活会座谈会")
    @JsonProperty(value = "type_sys")
    private Short typeSys;

    public TypeEntity toEntity() {
        TypeEntity typeEntity = new TypeEntity();
        typeEntity.setCategoryId(this.getCategoryId());
        typeEntity.setType(this.getType());
        typeEntity.setTypeSys(this.getTypeSys());
        return typeEntity;
    }
}
