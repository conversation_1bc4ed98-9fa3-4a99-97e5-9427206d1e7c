package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.common.pojo.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * t_meeting 查询条件
 * <p>
 * 活动表
 *
 * <AUTHOR>
 * @create 2018-10-25 10:53
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingListForm {

    private PageBean pageBean;

//	@ApiModelProperty("创建人所属组织id")
//	@JsonProperty(value = "org_id")
//	private Long orgId;
//
//	@ApiModelProperty("当前用户所属所有组织")
//	@JsonProperty(value = "org_ids")
//	private List<Long> orgIds;

    @ApiModelProperty("活动名称")
    @Column(name = "name")
    @NotBlank(message = "{NotBlank.meeting.name}")
    @Length(max = 50, message = "{Length.meeting.name}")
    private String name;

    @ApiModelProperty("议程名称")
    private String agenda;

    @ApiModelProperty("标签id")
    private List<Long> tagIds;

    @ApiModelProperty("举办时间范围 起始时间")
    @JsonProperty(value = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;


    @ApiModelProperty("举办时间范围 截止时间")
    @JsonProperty(value = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;


    @ApiModelProperty("活动状态 ：\n" +
            "1 发起审批中 活动发起后本组织内部审批中 活动详情、取消活动、修改\n" +
            "2 发起未通过 活动发起后本组织内部审批不通过 活动详情、取消活动、修改\n" +
            "3 活动待举办 活动已发起但尚未到举办时间 活动详情、取消活动、添加人员、发送活动通知\n" +
            "4 待填报结果 状态为“活动待举办”的活动，到举办时间时，状态自动置为待填报结果 活动详情、取消活动、填写纪实情况表\n" +
            "5 填报审批中 活动填报结果在本组织内部审批中 活动详情、填写纪实情况表\n" +
            "6 填报未通过 活动填报结果在本组织内部审批未通过 活动详情、填写纪实情况表\n" +
            "7 已提交 活动结果已提交到上级组织 活动详情\n" +
            "8 退回 活动结果被上级组织退回 活动详情、填写纪实情况表、退回记录\n" +
            "9 活动已取消 活动被取消后状态置为“活动已取消” 活动详情、删除\n" +
            "13 检查通过 活动结果被上级组织检查通过处理 活动详情\n" +
            "12 待复核 存在退回记录的活动结果再次提交到上级组织 活动详情")
    private Short status;


    private List<Long> typeIds;

    /**
     * 是否是移动端查询。0：不是；1：是
     */
    private Short isH5;

    @ApiModelProperty("组织名称或活动名称")
    @Column(name = "name")
    @NotBlank(message = "{NotBlank.meeting.name}")
    @Length(max = 50, message = "{Length.meeting.name}")
    private String key;


    @ApiModelProperty("下级所有组织")
    private List<Long> cOrgIds;

    @ApiModelProperty("查询组织")
    private List<Long> qOrgIds;

    /**
     * 是否是活动回顾查询。0：不是；1：是
     */
    private Short isReview;

    /**
     * 所属类别
     */
    private Integer meetingClass;
    /**
     * 提交时间（开始）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitStartTime;
    /**
     * 提交时间（结束）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitEndTime;
    /**
     * 活动名称(模糊匹配)
     */
    private String meetingName;

    @ApiModelProperty("活动回顾的组织状态（1：开启，2：关闭）")
    private List<Short> orgStatus;

    @ApiModelProperty("录入类型 0-发起活动，1-活动录入")
    private Integer recordType;

    @ApiModelProperty("查询指定meetingId的数据")
    private List<Long> filterMeetingIds;


    @ApiModelProperty("活动名称或议程名称")
    private String keyWord;

    @ApiModelProperty("组织生活会新增需求： 关联状态 null:所有  0:过滤掉被关联了的  1：活动发起的  2：直接关联的")
    private Integer relateType;

    private List<Long> meetingIds = null;//要指定过滤或者要指定查询的-组织生活会

    @ApiModelProperty("排序类型1-正序 2-倒叙")
    private Integer orderType;

}

