package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TobaccoTaskListForm {

    private Long toId;

    @ApiModelProperty("区县id")
    private Long regionId;

    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("任务标题")
    private String taskTitle;

    @ApiModelProperty("标签")
    private String label;

    @ApiModelProperty("开始时间")
    private Date beginTime;

    @ApiModelProperty("截至时间")
    private Date endTime;

    @ApiModelProperty("任务状态（1：草稿，2：未开始，3：进行中，4：已结束，5：已取消）")
    private Integer taskStatus;

    @ApiModelProperty("执行状态（1：未提交，2：已提交，3：已通过，4：未通过，5：已退回）")
    private Integer handleStatus;
}
