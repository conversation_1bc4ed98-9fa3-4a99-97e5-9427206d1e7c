package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 纪实结果检查审批
 *
 * <AUTHOR>
 * @create 2018/11/1 11:01
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingResultCheckForm {

    @ApiModelProperty("活动id")
    @JsonProperty(value = "meeting_id")
    @NotNull(message = "{NotNull.report.meeting-id}")
    private long meetingId;

    @ApiModelProperty("审批类型（1：通过 2：退回）")
    @JsonProperty(value = "status")
    @NotNull(message = "{NotNull.meetingLeave.status}")
    @Range(min = 1, max = 2, message = "{Range.report.oper}")
    private short oper;

    @ApiModelProperty("退回原因")
    @JsonProperty(value = "reason")
    @NotBlank(message = "{NotBlank.MeetingResultCheck.reason}")
    @Length(max = 4500, message = "{Length.MeetingResultCheck.reason}")
    private String reason;

}
