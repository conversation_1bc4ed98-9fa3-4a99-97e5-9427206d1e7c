package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LifeFileTaskForm {
    @ApiModelProperty("民主生活会id")
    @JsonProperty(value = "life_id")
    Long lifeId;

    @ApiModelProperty("模块")
    @JsonProperty(value = "type")
    Integer type;

    @ApiModelProperty("数据行id")
    @JsonProperty(value = "data_id")
    Long dataId;

    @ApiModelProperty("截止日期yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty(value = "end_time")
    LocalDateTime endTime;

    @ApiModelProperty("指定的上传人")
    @JsonProperty(value = "users")
    List<UploaderVO> users;//指定的上传人

    @ApiModelProperty("任务标题")
    @JsonProperty(value = "title")
    String title;



    @ApiModelProperty("任务id")
    @JsonProperty(value = "task_id")
    String taskId;



}
