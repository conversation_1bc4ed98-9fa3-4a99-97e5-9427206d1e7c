package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;


/**
 * about 任务创建类
 *
 * <AUTHOR>
 * @date 2021/07/29
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SbwNewTaskForm {

    @ApiModelProperty("1:提交 , 2:保存")
    private Integer flag;

    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("区县id")
    private Long regionId;

    @ApiModelProperty("组织信息")
    private List<Org> org;

    @ApiModelProperty("任务类型。1：工作任务，2：转办单")
    private Integer taskType;

    @ApiModelProperty("任务标题")
    @Length(max = 2000,message = "标题长度超过2000")
    private String title;

    @ApiModelProperty("任务编号")
    @Length(max = 2000,message = "编号长度超过2000")
    private String number;

    @ApiModelProperty("时间类型  1:本月内 2:本季度内 3:本年内 4:自定义时间")
    private Integer timeType;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("截至时间")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("舆情分类id")
    private Long typeId;

    @ApiModelProperty("舆情来源")
    @Length(max = 2000,message = "舆情来源长度超过2000")
    private String source;

    @ApiModelProperty("舆情概要")
    @Length(max = 2000,message = "舆情概要长度超过2000")
    private String content;

    @ApiModelProperty("审核组织id")
    private Long verifyOrgId;

    @ApiModelProperty("审核组织名")
    private String verifyOrgName;

    @ApiModelProperty("工作单通知类型,1:短信 2:微信 ")
    private Integer notice;

    @ApiModelProperty("附件id。逗号分隔")
    private String fileId;

    @ApiModelProperty("附件名称。逗号分隔")
    private String filename;

    @ApiModelProperty("备注")
    @Length(max = 2000,message = "备注长度超过2000")
    private String remark;

    @ApiModelProperty("任务状态。1：草稿，2：未开始，3：进行中，4：已结束")
    private Integer status;

    @ApiModelProperty("0:不予处理 1:处理")
    private Integer isHandle;

    @ApiModelProperty("不予处理原因")
    private String noHandleContent;

    @ApiModelProperty("创建用户id")
    private Long createUser;

    @ApiModelProperty("更新用户id")
    private Long updateUser;

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Org {

        @ApiModelProperty("组织id")
        private Long orgId;

        @ApiModelProperty("组织名称")
        private String orgName;
    }
}
