package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty
import org.springframework.format.annotation.DateTimeFormat
import javax.persistence.Column
import javax.validation.constraints.NotNull

/**
 *
 * <AUTHOR>
 * @createTime 2022年04月07日 17:43:00
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
data class CommendPenalizeFields (

    @ApiModelProperty("奖励字段")
    var awardFields: MutableList<String> = mutableListOf(),

    @ApiModelProperty("惩罚字段")
    var penalizeFields: MutableList<String> = mutableListOf()
) {
    override fun toString(): String {
        return "CommendPenalizeFields(awardFields=$awardFields, penalizeFields=$penalizeFields)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
data class UserCommendPenalizeExportQueryForm(

    @ApiModelProperty("组织ID")
    @get: NotNull(message = "{NotNull.Org.Id}")
    var orgId: Long? = null,

    @ApiModelProperty("用户名字")
    var userName: String? = null,

    @ApiModelProperty("奖惩类型（1:奖励 2:惩罚）")
    var type: Int? = null,

    @ApiModelProperty("奖惩名称")
    var name: Int? = null,

    @ApiModelProperty("开始时间")
    @get: DateTimeFormat(pattern = "yyyy-MM-dd")
    var startTime: String? = null,

    @ApiModelProperty("结束时间")
    @get: DateTimeFormat(pattern = "yyyy-MM-dd")
    var endTime: String? = null,

    @ApiModelProperty("审核状态 1-待审核、2-审核通过、3-审核不通过")
    var approvalStatus: Int? = null,

    @ApiModelProperty("奖励字段")
    var awardFields: MutableList<String> = mutableListOf(),

    @ApiModelProperty("惩罚字段")
    var penalizeFields: MutableList<String> = mutableListOf()
) {
    override fun toString(): String {
        return "UserCommendPenalizeExportQueryForm(orgId=$orgId, userName=$userName, type=$type, name=$name, startTime=$startTime, endTime=$endTime, approvalStatus=$approvalStatus, awardFields=$awardFields, penalizeFields=$penalizeFields)"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
data class CommendPenalizeExportForm(

    var objectName: String? = null,

    var date: String? = null,

    var category: String? = null,

    var level: String? = null,

    var name: String? = null,

    var relatedFile: String? = null,

    var awardUnit: String? = null,

    var basisDescription: String? = null,

    var type: Int? = null
) {
    override fun toString(): String {
        return "CommendPenalizeExportForm(objectName=$objectName, date=$date, category=$category, level=$level, name=$name, relatedFile=$relatedFile, awardUnit=$awardUnit, basisDescription=$basisDescription, type=$type)"
    }
}

/**
 * type 1-人员奖励, 2-人员惩罚,3-组织奖励,4-组织惩罚
 */
enum class CommendPenalizeDownloadFieldsEnum(val type: Int, val key: String, val value: String) {

    // 人员奖励字段
    USER_AWARD_USER_NAME(1, "objectName", "党员姓名"),
    USER_AWARD_DATE(1, "date", "获奖日期"),
    USER_AWARD_CATEGORY(1, "category", "奖励类别"),
    USER_AWARD_LEVEL(1, "level", "奖励级别"),
    USER_AWARD_NAME(1, "name", "奖励名称"),
    USER_AWARD_FILE(1, "relatedFile", "表彰文件"),
    USER_AWARD_UNIT(1, "awardUnit", "颁奖单位"),
    USER_AWARD_REMARK(1, "basisDescription", "备注"),

    // 人员惩罚字段
    USER_PENALIZE_USER_NAME(2, "objectName", "党员姓名"),
    USER_PENALIZE_DATE(2, "date", "生效日期"),
    USER_PENALIZE_CATEGORY(2, "category", "惩罚类别"),
    USER_PENALIZE_NAME(2, "name", "惩罚名称"),
    USER_PENALIZE_FILE(2, "relatedFile", "相关文件"),
    USER_PENALIZE_UNIT(2, "awardUnit", "处罚单位"),
    USER_PENALIZE_REMARK(2, "basisDescription", "备注"),

    // 组织奖励字段
    ORG_AWARD_USER_NAME(3, "objectName", "组织名称"),
    ORG_AWARD_DATE(3, "date", "获奖日期"),
    ORG_AWARD_CATEGORY(3, "category", "奖励类别"),
    ORG_AWARD_LEVEL(3, "level", "奖励级别"),
    ORG_AWARD_NAME(3, "name", "奖励名称"),
    ORG_AWARD_FILE(3, "relatedFile", "表彰文件"),
    ORG_AWARD_UNIT(3, "awardUnit", "颁奖单位"),
    ORG_AWARD_REMARK(3, "basisDescription", "备注"),

    // 组织惩罚字段
    ORG_PENALIZE_USER_NAME(4, "objectName", "组织名称"),
    ORG_PENALIZE_DATE(4, "date", "生效日期"),
    ORG_PENALIZE_NAME(4, "name", "惩罚名称"),
    ORG_PENALIZE_FILE(4, "relatedFile", "相关文件"),
    ORG_PENALIZE_UNIT(4, "awardUnit", "处罚单位"),
    ORG_PENALIZE_REMARK(4, "basisDescription", "备注");

    companion object {

        fun getFieldsByType(type: Int): MutableList<CommendPenalizeDownloadFieldsEnum> {
            return values().filter { it.type == type }.toMutableList()
        }

        fun getFieldByTypeAndValue(type: Int, value: String) : CommendPenalizeDownloadFieldsEnum? {
            return values().firstOrNull { it.type == type && it.value == value }
        }
    }
}