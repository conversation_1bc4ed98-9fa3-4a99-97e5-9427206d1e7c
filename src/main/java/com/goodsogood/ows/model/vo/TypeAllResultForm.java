package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.TypeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Description: 活动类型</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TypeAllResultForm {

    @ApiModelProperty("类别id")
    @JsonProperty(value = "category_id")
    private Long categoryId;

    @ApiModelProperty("类别")
    @JsonProperty(value = "category")
    private String category;

    @ApiModelProperty("类型")
    @JsonProperty(value = "types")
    private List<TypeEntity> types;

    @ApiModelProperty("规则类型：-1 未指定 1.必须选择党小组 2.必须选择支委会届次 3.必须选择组织所有成员")
    @JsonProperty(value = "code")
    private Integer code;
}
