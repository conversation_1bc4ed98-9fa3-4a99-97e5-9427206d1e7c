package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class SelectOrgSecretaryQueryForm {

	@ApiModelProperty(value = "组织IDList")
	@JsonProperty("org_ids")
	private List<Long> orgIds;

	@ApiModelProperty(value = "年度")
	private String year;
}
