package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgLifeAddEditForm {

    @ApiModelProperty("组织生活会id")
    private Long lifeId;

    @NotBlank(message = "{NotBlank.life.title}")
    @Length(message = "{Length.life.title}",min = 1,max = 50)
    @ApiModelProperty("会议名称")
    private String title;

    @NotNull(message = "{NotNull.life.years}")
    @ApiModelProperty("年度")
    private Integer years;
}
