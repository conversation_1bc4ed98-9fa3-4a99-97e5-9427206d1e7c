package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 活动签到
 *
 * <AUTHOR>
 * @create 2018/11/2 14:59
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingSignInForm {

    @ApiModelProperty(value = "签到hash")
    @NotNull(message = "{NotNull.meeting.hash}")
    @JsonProperty("qr_code_hash")
    private String qrCodeHash;

    @ApiModelProperty(value = "是否为动态二维码")
    @JsonProperty("is_dynamic")
    /**
     * 是否为动态二维码	0:否(默认); 1:是
     */
    private Integer isDynamic=0;


}
