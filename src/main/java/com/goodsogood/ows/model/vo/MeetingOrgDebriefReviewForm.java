package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.validate.group.Add;
import com.goodsogood.ows.validate.group.Delete;
import com.goodsogood.ows.validate.group.Detail;
import com.goodsogood.ows.validate.group.Edit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @describe 组织述职评议列表
 * @date 2019-12-26
 */

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingOrgDebriefReviewForm {

    @ApiModelProperty("主键id")
    @JsonProperty(value = "meeting_org_debrief_review_id")
    @NotNull(message = "id不能为空", groups = {Delete.class, Edit.class, Detail.class,})
    private Long meetingOrgDebriefReviewId;

    @ApiModelProperty("评议年度")
    @JsonProperty(value = "review_year")
    @NotBlank(message = "评议年度不能为空", groups = {Add.class, Edit.class})
    private String reviewYear;

    @ApiModelProperty("附加说明")
    @JsonProperty(value = "additional_information")
    @Length(message = "附加说明长度最大1000", max = 1000, groups = {Add.class, Edit.class})
    private String additionalInformation;

    @ApiModelProperty("附件")
    @JsonProperty(value = "attachment")
    private String attachment;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @NotNull(message = "组织id不能为空", groups = {Add.class, Edit.class})
    private Long orgId;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @NotBlank(message = "组织名称不能为空", groups = {Add.class, Edit.class})
    private String orgName;

    @ApiModelProperty("评议等级 1:好 2:较好 3:一般 4:差")
    @JsonProperty(value = "rating")
    @NotNull(message = "评议等级不能为空", groups = {Add.class, Edit.class})
    private Integer rating;


    @ApiModelProperty("组织类型")
    @JsonProperty(value = "org_type_child")
    private Long orgTypeChild;


    @ApiModelProperty("文件")
    private List<MeetingFileForm> files;
}
