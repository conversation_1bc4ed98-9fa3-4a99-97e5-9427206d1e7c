package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.Api
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingCommentForm {

    @ApiModelProperty("民主评议主键ID")
    @JsonProperty(value = "comment_id")
    var commentId: Long? = null

    @ApiModelProperty("组织ID")
    @JsonProperty(value = "org_id")
    var orgId: Long? = null

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    var orgName: String? = null

    @ApiModelProperty("评议年度")
    var year: Int? = null

    @ApiModelProperty("评议状态 0-未开启，1-已开启，2-待审查，3-审查未通过，4-待审定，5-审定未通过，6-审定通过")
    var status: Int? = null

    @ApiModelProperty("状态翻译")
    var statusValue: String? = null

    constructor()

    constructor(commentId: Long?, orgId: Long?, orgName: String?, year: Int?, status: Int?, statusValue: String?) {
        this.commentId = commentId
        this.orgId = orgId
        this.orgName = orgName
        this.year = year
        this.status = status
        this.statusValue = statusValue
    }

}