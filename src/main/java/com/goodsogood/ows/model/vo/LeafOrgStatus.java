package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 支部orgStatus
 *
 * <AUTHOR>
 * @date 2018/12/21
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LeafOrgStatus {
    @ApiModelProperty(value = "活动类型id")
    @JsonProperty(value = "type_id")
    private Long typeId;

    @ApiModelProperty(value = "活动类型名称")
    private String type;

    @ApiModelProperty(value = "时间,字符串 如：2018年12月或2018年第4季度")
    private String time;

    @ApiModelProperty(value = "活动类型完成情况：1 未完成 2 完成")
    @JsonProperty(value = "status")
    private Integer status;

    public LeafOrgStatus(Long typeId, String type, String time, Integer status) {
        this.typeId = typeId;
        this.type = type;
        this.time = time;
        this.status = status;
    }
}
