package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * MeetingPlanForm 计划查询条件
 *
 * <AUTHOR>
 * @create 2018-10-23 13:55
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingPlanForm {


    @ApiModelProperty("执行方式:1.自定义间段(默认值);2自然月周期;3.季度周期;4.年度周期")
    @JsonProperty(value = "execute_type")
    private Short executeType;

    @ApiModelProperty("排除的执行方式:1.自定义间段(默认值);2自然月周期;3.季度周期;4.年度周期")
    @JsonProperty(value = "exclude_execute_type")
    private Short excludeExecuteType;

    @ApiModelProperty("停、启用状态。0：停用；1：启用")
    @JsonProperty(value = "is_execute")
    private Short isExecute;


    @ApiModelProperty("发放任务类型：1 自动发放; 2 手动发放(默认)")
    @JsonProperty(value = "send_type")
    private Short sendType;

    @ApiModelProperty("是否删除：0 正常（默认） 1 删除")
    @JsonProperty(value = "is_del")
    private Short isDel;

}

