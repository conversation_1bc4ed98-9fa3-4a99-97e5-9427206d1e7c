package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-14 10:57
 * @since 1.0.3
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class MsgTemplateForm {

     @JsonProperty("data")
     private List data;

     @ApiModelProperty("模板id")
     @JsonProperty("template_id")
     private Integer templateId;


     @ApiModelProperty("渠道类型 1:短信 2:微信")
     @JsonProperty("channel_type")
     private Integer channelType;

     @ApiModelProperty("传递了就绑定到指定的批次，否则新建批次")
     @JsonProperty("push_id")
     private Long pushId;

     @ApiModelProperty("业务系统的application.name，例如党费ppmd")
     @JsonProperty("source")
     private String source;

     @ApiModelProperty("推送模式 1-只推当前渠道，2-只推主题消息，3-全部推送 默认为1")
     @JsonProperty("push_model")
     private Integer pushModel;

     @JsonProperty("is_pull_user")
     private Integer isPullUser;



}
