package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Table;
import java.time.LocalDateTime;
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IndexOrgScoreForm {
    private Integer ruleId;
    private Integer dataMonth;
    private Long orgId;

    //1.党树组织  2.党组
    private Integer scoreOrgType;
    private Integer scoreType;

    private Integer parentScoreType;

    private String orgName;
    private Long score;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime createTime;

    public IndexOrgScoreForm() {
    }

    public IndexOrgScoreForm(Integer ruleId, Integer dataMonth, Long orgId, Integer scoreOrgType, Long score) {
        this.ruleId = ruleId;
        this.dataMonth = dataMonth;
        this.orgId = orgId;
        this.scoreOrgType = scoreOrgType;
        this.score = score;
    }

    public Integer getScoreType() {
        return scoreType;
    }

    public void setScoreType(Integer scoreType) {
        this.scoreType = scoreType;
    }

    public Integer getParentScoreType() {
        return parentScoreType;
    }

    public void setParentScoreType(Integer parentScoreType) {
        this.parentScoreType = parentScoreType;
    }

    public Integer getDataMonth() {
        return dataMonth;
    }

    public void setDataMonth(Integer dataMonth) {
        this.dataMonth = dataMonth;
    }



    public Integer getScoreOrgType() {
        return scoreOrgType;
    }

    public void setScoreOrgType(Integer scoreOrgType) {
        this.scoreOrgType = scoreOrgType;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getScore() {
        return score;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
