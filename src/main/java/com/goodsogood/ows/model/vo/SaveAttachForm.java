package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 保存附件前端传入参数
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SaveAttachForm {
    @ApiModelProperty("组织生活会id")
    @JsonProperty(value = "life_id")
    @NotNull(message = "life_id不能为空")
    private Long lifeId;

    @ApiModelProperty("附件信息")
    @JsonProperty(value = "life_file")
    List<LifeFileForm> lifeFile;

    @ApiModelProperty("上传人id")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("上传人")
    @JsonProperty(value = "username")
    private String username;

    @ApiModelProperty("模块")
    @JsonProperty(value = "type")
    @NotNull(message = "type不能为空")
    private Integer type;

    @ApiModelProperty("数据行id")
    @JsonProperty(value = "data_id")
    private Long dataId;

    @ApiModelProperty("所在页面1：会前  2：会后  3：上报")
    @JsonProperty(value = "step")
    @NotNull(message = "step不能为空")
    private Integer step;

    @ApiModelProperty("是否自己上传 0:否 1：是")
    @JsonProperty(value = "is_direct")
    @NotNull(message = "is_direct不能为空")
    private Integer isDirect;

    @ApiModelProperty("任务的唯一性标志")
    @JsonProperty(value = "task_id")
    private String taskId;//任务的唯一性标志,他人上传时提供，用以更新任务状态

    public Long getLifeId() {
        return lifeId;
    }

    public void setLifeId(Long lifeId) {
        this.lifeId = lifeId;
    }

    public List<LifeFileForm> getLifeFile() {
        return lifeFile;
    }

    public void setLifeFile(List<LifeFileForm> lifeFile) {
        this.lifeFile = lifeFile;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public Integer getStep() {
        return step;
    }

    public void setStep(Integer step) {
        this.step = step;
    }

    public Integer getIsDirect() {
        return isDirect;
    }

    public void setIsDirect(Integer isDirect) {
        this.isDirect = isDirect;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
}
