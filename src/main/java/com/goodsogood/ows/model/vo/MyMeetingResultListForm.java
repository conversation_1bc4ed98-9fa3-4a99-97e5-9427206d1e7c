package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * t_meeting 实体类
 *
 * <p>活动表
 *
 * <AUTHOR>
 * @create 2018-10-25 10:53
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MyMeetingResultListForm {

    @ApiModelProperty("id")
    @JsonProperty(value = "meeting_id")
    private Long meetingId;

    @ApiModelProperty("活动名称")
    private String name;

    @ApiModelProperty("活动类型")
    private String types;

    @ApiModelProperty("举办时间")
    @JsonProperty(value = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(
            "1:待审批;2:已通过;3:未通过;4:取消;5:未请假")
    private Short status;

    @ApiModelProperty("请假id")
    @JsonProperty(value = "meeting_leave_id")
    private Long meetingLeaveId;


    @ApiModelProperty(
            "     * 前端活动状态：\n"
                    + "     * 1 发起审批中 活动发起后本组织内部审批中 活动详情、取消活动、修改\n"
                    + "     * 2 发起未通过 活动发起后本组织内部审批不通过 活动详情、取消活动、修改\n"
                    + "     * 3 活动待举办 活动已发起但尚未到举办时间 活动详情、取消活动、添加人员、发送活动通知\n"
                    + "     * 4 待填报结果 状态为“活动待举办”的活动，到举办时间时，状态自动置为待填报结果  活动详情、取消活动、填写纪实情况表\n"
                    + "     * 5 填报审批中 活动填报结果在本组织内部审批中 活动详情、填写纪实情况表\n"
                    + "     * 6 填报未通过 活动填报结果在本组织内部审批未通过 活动详情、填写纪实情况表\n"
                    + "     * 7 已提交 活动结果已提交到上级组织  活动详情\n"
                    + "     * 8 退回  活动结果被上级组织退回 活动详情、填写纪实情况表、退回记录\n"
                    + "     * 9 活动已取消 活动被取消后状态置为“活动已取消” 活动详情、删除\n"
                    + "     * 12  待复核 存在退回记录的活动结果再次提交到上级组织  活动详情\n"
                    + "     * 13 检查通过  活动结果被上级组织检查通过处理 活动详情")
    @JsonProperty(value = "meeting_status")
    private Short meetingStatus;

    @ApiModelProperty("在我的活动中显示状态 1：待举办；2：进行中：3：结束")
    @JsonProperty(value = "fm_meeting_status")
    private Short fmMeetingStatus;
}
