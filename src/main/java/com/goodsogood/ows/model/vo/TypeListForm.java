package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.common.pojo.PageBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.annotation.Nonnull;

/**
 * <p>Description: 活动类型</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TypeListForm {

    @ApiModelProperty("区县id")
    @JsonProperty(value = "region_id")
    @Nonnull
    private Long regionId;

    @ApiModelProperty("类型id")
    @JsonProperty(value = "type_id")
    private Long typeId;

    @ApiModelProperty("类别id")
    @JsonProperty(value = "category_id")
    private Long categoryId;

    @JsonProperty("type")
    @ApiModelProperty(value = "类别")
    @Length(max = 50, message = "{Length.type}")
    private String type;

    private PageBean pageBean;
}
