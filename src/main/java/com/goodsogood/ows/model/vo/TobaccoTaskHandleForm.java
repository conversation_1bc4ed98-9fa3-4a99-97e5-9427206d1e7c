package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TobaccoTaskHandleForm {

    @ApiModelProperty("id")
    private Long handleId;

    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("接收范围（1：组织，2：人员）")
    private Integer acceptScope;

    @ApiModelProperty("接收 人员/组织id")
    private Long acceptId;

    @ApiModelProperty("执行状态（1：草稿，2：已填报，3：已通过，4：未通过，5：已退回）")
    private Integer handleStatus;

    @ApiModelProperty("执行描述/审核意见")
    private String handleContent;

    @ApiModelProperty("附件json")
    private List<HandleFile> handleFile;

    @ApiModelProperty("转发组织json")
    private List<forwardOrg> forwardOrg;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class HandleFile{
        private Long fileId;
        private String fileName;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class forwardOrg{
        private Long forwardId;
        private String forwardName;
    }
}
