package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@ApiModel
//@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StudyAddListForm {

    @ApiModelProperty("补学表主键")
    @JsonProperty("sign_id")
    private Long signId;

    @ApiModelProperty("会议id")
    @JsonProperty("meeting_id")
    private Long meetingId;

    @ApiModelProperty("meetingUserId")
    @JsonProperty("meeting_user_id")
    private Long meetingUserId;



    @ApiModelProperty("用户id")
    @JsonProperty("user_id")
    @NotNull(message = "用户id不能为空！")
    private Long userId;

    @ApiModelProperty("活动名称")
    @JsonProperty("name")
    private String name;

    @JsonProperty("types_id")
    @ApiModelProperty("活动类型id")
    private Long typesId;

    @JsonProperty("types")
    @ApiModelProperty("活动类型")
    private String types;

    @JsonProperty("tag_id")
    @ApiModelProperty("标签id")
    private Long tagId;

    @JsonProperty("tag_name")
    @ApiModelProperty("标签")
    private String tagName;

    @JsonProperty("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty("活动开始时间")
    private Date startTime;

    @JsonProperty("start_time_str")
    @ApiModelProperty("活动开始时间字符")
    private String startTimeStr;

    @JsonProperty("status")
    @ApiModelProperty("状态")
    private Short status;//1:待补学 2:补学完成 3:超期补学 4:活动变更

    @JsonProperty("status_name")
    @ApiModelProperty("状态")
    private String statusName;//1:待补学 2:补学完成 3:超期补学 4:活动变更

    @ApiModelProperty("补学时间")
    @JsonProperty(value = "sign_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date signTime;

    @JsonProperty("page_size")
    @ApiModelProperty("每页条数")
    @NotNull(message = "每页显示条数不能为空!")
    private Integer pageSize = 5;

    @JsonProperty("page_num")
    @ApiModelProperty("当前页数")
    @NotNull(message = "当前页数不能为空!")
    private Integer pageNum = 1;



}
