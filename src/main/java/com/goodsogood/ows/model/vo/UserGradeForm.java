package com.goodsogood.ows.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserGradeForm {
    private String username = "";
    private String orgName = "";
    private String sex = "";
    private String birthDay = "";
    private String joinTime = "";
    private String education = "";
    private String profession = "";//职务
    private String date = "";//填表时间  yyyy年MM月dd日
    private String identifytion = "";//自我鉴定
    private String level1 = "";//优秀
    private String level2 = "";//合格
    private String level3 = "";//基本合格
    private String level4 = "";//不合格
    private String selfIdentifytion = "";//自我人格
    private String year1 = "";
    private String month1 = "";
    private String day1 = "";
    private String orgIdentifytion = "";//党支部评议意见：一条一行？
    private String year2 = "";
    private String month2 = "";
    private String day2 = "";
    private String partyIdentifytion = "";//党总支审查意见
    private String year3 = "";
    private String month3 = "";
    private String day3 = "";
    private String commitIdentifytion = "";//机关党委审计意见
    private String year4 = "";
    private String month4 = "";
    private String day4 = "";
    private String imgStr = "";

}
