package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.TopicEntity;
import com.goodsogood.ows.model.db.TopicOrgEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * t_topic_org 实体类
 *
 * <AUTHOR>
 * @create 2018-10-23 09:03
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopicOrgForm extends TopicEntity {
	@ApiModelProperty("状态（1：未完成；2：完成；3：已逾期）")
	private Integer status;

	@ApiModelProperty("任务组织关联表id")
	@JsonProperty(value = "topic_org_id")
	private Long topicOrgId;

	@ApiModelProperty("1：非本组织添加（上级派发）2:本组织添加 ")
	private Integer remark;

	/**
	 * 组织
	 */
	private List<TopicOrgEntity> orgs;

	//组织总数量
    @JsonProperty(value = "total")
	private Long total;
	//未完成的组织数量
    @JsonProperty(value = "no_finish_total")
	private Long notFinishTotal;
	//已完成组织总数
    @JsonProperty(value = "finish_total")
	private Long finishTotal;

}

