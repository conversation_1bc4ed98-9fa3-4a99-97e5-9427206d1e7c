package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @program: ows-meeting
 * @description:  TODO 自动发放调用用户中心条件查询组织   实体
 * @author: taiqian.Luo
 * @create: 2019-04-18 17:36
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public final class MeetingAutoDistributionReqForm {

    @JsonProperty(value = "org_id")
    private long orgId;

    /**
     * 离退休 1:包含 2:不包含　3:仅包含
     */
    @JsonProperty(value = "is_retire")
    private short isRetire;
    /**
     * //党小组 1:包含 2:不包含　3:仅包含
     */
    @JsonProperty(value = "is_groups")
    private short isGroups;

    /**
     * 支委会 1:包含 2:不包含　3:仅包含
     */
    @JsonProperty(value = "is_period")
    private short isPeriod;

    /**
     * 组织类型	包括的详细组织类型
     */
    @JsonProperty(value = "org_type_childs")
    private List<Integer> orgTypeChilds;

    @JsonProperty(value = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @JsonProperty(value = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
