package com.goodsogood.ows.model.vo

import io.swagger.annotations.ApiModel
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.annotations.ApiModelProperty
import com.fasterxml.jackson.annotation.JsonProperty
import lombok.Data
import java.io.Serial
import java.io.Serializable

/**
 * t_meeting_file 文件实体类
 *
 * <AUTHOR>
 * @create 2020-01-06
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class MeetingFileForm @JvmOverloads constructor(
    @ApiModelProperty("文件ID")
    @JsonProperty(value = "file_id")
    var fileId: Long? = null,
    /**
     * [com.goodsogood.ows.common.FileSourceEnum]
     */
    @ApiModelProperty("文件来源 详情见上面枚举类")
    var source: Int? = null,

    @ApiModelProperty("附件名称")
    var name: String? = null,

    @ApiModelProperty("附件路径")
    var path: String? = null,

    @ApiModelProperty("文件原名称")
    @JsonProperty(value = "file_name")
    var fileName: String? = null,

    @ApiModelProperty("文件大小（byte）")
    var size: Long? = null,
) : Serializable {

    override fun toString(): String {
        return "MeetingFileForm(fileId=$fileId, source=$source, name=$name, path=$path, fileName=$fileName, size=$size)"
    }

    companion object {
        @Serial
        private const val serialVersionUID: Long = 2460847685239702917L
    }
}
