package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 民族评议查询表单
 * @date 2019/12/27
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCommentQueryForm {

    @ApiModelProperty("组织ID")
    @JsonProperty(value = "org_id")
    @NotNull(message = "{NotNull.Org.Id}")
    private Long orgId;

    @ApiModelProperty("评议年度")
    @JsonProperty(value = "year")
    @NotNull(message = "{NotNull.Comment.Year}")
    private Integer year;

    @ApiModelProperty("用户名字")
    @JsonProperty(value = "user_name")
    private String userName;

    @ApiModelProperty("评议等级")
    @JsonProperty(value = "comment_level")
    private Integer commentLevel;

    @ApiModelProperty("页码")
    @JsonProperty("page")
    private Integer page = 1;

    @ApiModelProperty("每页大小")
    @JsonProperty("page_size")
    private Integer pageSize = 10;
}
