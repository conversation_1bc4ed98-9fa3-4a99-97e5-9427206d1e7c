package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 活动统计vo
 *
 * <AUTHOR>
 * @date 2018/12/21
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UndoneMeeting {

    @ApiModelProperty(value = "组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty(value = "组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty(value = "1 root组织,2 非root且有下级组织的组织,3 支部")
    @JsonProperty(value = "org_type")
    private Integer orgType;

    @JsonProperty(value = "org_stats")
    private List<OrgStatus> orgStatus;

    @JsonProperty(value = "task_status")
    private List<LeafOrgStatus> leafOrgStatus;
}
