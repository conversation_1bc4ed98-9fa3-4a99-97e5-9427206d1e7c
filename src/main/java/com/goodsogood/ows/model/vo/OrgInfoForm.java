package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 组织信息实体类
 * @date 2020/1/3
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgInfoForm {

    @JsonProperty("org_id")
    private Long orgId;

    @JsonProperty("parent_id")
    private Long parentId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("org_name")
    private String orgName;

    @JsonProperty("tag_id")
    private String tagId;

    @JsonProperty("owner_tree")
    private Integer ownerTree;

    @JsonProperty("org_type")
    private Integer orgType;

    @JsonProperty("org_type_child")
    private Integer orgTypeChild;

    @JsonProperty("org_level")
    private String orgLevel;

    @JsonProperty("owner_id")
    private Long ownerId;

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public Integer getOwnerTree() {
        return ownerTree;
    }

    public void setOwnerTree(Integer ownerTree) {
        this.ownerTree = ownerTree;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public Integer getOrgTypeChild() {
        return orgTypeChild;
    }

    public void setOrgTypeChild(Integer orgTypeChild) {
        this.orgTypeChild = orgTypeChild;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }
}
