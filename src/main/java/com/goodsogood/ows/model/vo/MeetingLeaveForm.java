package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 移动端请假VO
 *
 * <AUTHOR>
 * @create 2018/10/30 13:10
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingLeaveForm implements Serializable {

    @ApiModelProperty("用户名")
    @JsonProperty(value = "user_name")
    private String userName;

    @ApiModelProperty("活动名称")
    @JsonProperty(value = "name")
    private String name;

    @ApiModelProperty("活动类型")
    @JsonProperty(value = "types")
    private String types;

    @ApiModelProperty("活动开始时间")
    @JsonProperty(value = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("审批状态")
    @JsonProperty(value = "status")
    private Short status;

    @ApiModelProperty("请假id")
    @JsonProperty(value = "meeting_leave_id")
    private Long meetingLeaveId;

    @ApiModelProperty("请假时间")
    @JsonProperty(value = "meeting_leave_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date meetingLeaveTime;

    @ApiModelProperty("活动id")
    @JsonProperty(value = "meeting_id")
    private Long meetingId;

    @ApiModelProperty("请假类型（1：因公请假 2：因私请假）")
    @JsonProperty(value = "type")
    private Short type;

    @ApiModelProperty("请假原因")
    @JsonProperty(value = "reason")
    private String reason;

    @ApiModelProperty("联系电话")
    @JsonProperty(value = "phone")
    private String phone;

}
