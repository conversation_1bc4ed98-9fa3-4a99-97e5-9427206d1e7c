package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@AllArgsConstructor
public class LifeFileUserVO {
    @ApiModelProperty("data_id")
    @JsonProperty(value = "data_id")
    private Long dataId;

    @ApiModelProperty("user_id")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("name")
    @JsonProperty(value = "name")
    private String name;

}
