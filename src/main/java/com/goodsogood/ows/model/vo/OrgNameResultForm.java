package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 *
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgNameResultForm {

    @JsonProperty("org_id")
    private Long orgId;

    @JsonProperty("parent_id")
    private Long parentId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("short_name")
    private String shortName;

    @JsonProperty("org_name")
    private String orgName;

    @JsonProperty("org_type")
    private Integer orgType;

    @JsonProperty("org_type_child")
    private Integer orgTypeChild;

    @JsonProperty("org_level")
    private String orgLevel;

    @JsonProperty("tag_id")
    private String tagId;

    @JsonProperty("owner_tree")
    private Integer ownerTree;

    @JsonProperty("owner_id")
    private Long ownerId;

    @JsonProperty("party_leader")
    private String partyLeader;

    @JsonProperty("is_consistent")
    private Integer isConsistent;
}
