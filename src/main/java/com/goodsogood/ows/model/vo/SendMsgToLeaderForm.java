package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019-07-08 15:15
 * @since 1.0.3
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SendMsgToLeaderForm extends SendMsgForm {
    
    @ApiModelProperty("党员姓名")
    @JsonProperty("name")
    private String name;


    @JsonProperty("oraname")
    @ApiModelProperty("举办组织名称")
    public String oraname;


    @JsonProperty("datetime")
    @ApiModelProperty("活动开始时间")
    public String datetime;


    @JsonProperty("type")
    @ApiModelProperty("活动类型")
    public String type;

}
