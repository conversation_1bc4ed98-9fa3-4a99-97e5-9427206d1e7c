package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeAndMeetingVO {

    @JsonProperty(value = "data_id")
    @ApiModelProperty("数据行id")
    private Long dataId;

    @JsonProperty(value = "meeting_id")
    @ApiModelProperty("活动Id")
    private Long meetingId;

    @JsonProperty(value = "meeting_files")
    @ApiModelProperty("数据文件")
    private List<LifeAndMeetingFile> meetingFiles = new ArrayList<>();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LifeAndMeetingFile {
        @JsonProperty(value = "file_name")
        @ApiModelProperty("附件名")
        private String fileName;

        @JsonProperty(value = "file_id")
        @ApiModelProperty("文件服务器的附件id")
        private Long fileId;

        @JsonProperty(value = "path")
        @ApiModelProperty("路径")
        private String path;
    }
}
