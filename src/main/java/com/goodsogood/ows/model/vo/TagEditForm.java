package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.MeetingTagEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @ClassName : TagEditForm
 * <AUTHOR> tc
 * @Date: 2021/11/9 14:57
 * @Description : 活动标签编辑表单
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TagEditForm {

    /**
     * 编辑类型   1 新增 2删除
     */
    @ApiModelProperty(value = "编辑类型")
    @JsonProperty("edit_type")
    @NotNull(message = "{NotNull.meeting.tag.editType}")
    private Integer editType;

    @ApiModelProperty(value = "会议编号集合")
    @JsonProperty("meeting_ids")
    @NotNull(message = "{NotNull.meeting.tag.meetingIds}")
    @Size(min = 1, message = "{NotNull.meeting.meetingIds}")
    private List<Long> meetingIds;

    @ApiModelProperty(value = "标签信息集合")
    @JsonProperty("meeting_tag")
    @NotNull(message = "{NotNull.meeting.tag.meetingTag}")
    @Size(min = 1, message = "{NotNull.meeting.meetingTag}")
    private List<MeetingTagEntity> meetingTag;

}
