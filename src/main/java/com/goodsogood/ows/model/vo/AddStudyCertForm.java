package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AddStudyCertForm {
    @ApiModelProperty("新增类型 1 补学 2 草稿")
    @JsonProperty(value = "type")
    @NotNull(message="补学类型不能为空")
    private Short type;

    @ApiModelProperty("补学主键")
    @JsonProperty(value = "wait_sign_id")
    @NotNull(message="补学主键不能为空")
    private Long waitSignId;

    @ApiModelProperty("内容")
    @JsonProperty(value = "content")
    private String content;

    @ApiModelProperty("图片文件")
    @JsonProperty(value = "img_file")
    private Object imgFile;

    @ApiModelProperty("附件文件")
    @JsonProperty(value = "file")
    private Object file;
}
