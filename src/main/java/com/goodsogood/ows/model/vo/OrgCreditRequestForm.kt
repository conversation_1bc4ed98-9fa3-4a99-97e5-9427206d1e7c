package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel

@ApiModel
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = SnakeCaseStrategy::class)
class OrgCreditRequestForm {

    // 区县
    var regionId: Long? = null

    // 接受积分组织
    var scoreOrgId: Long? = null

    // 发送积分组织
    var orgId: Long? = null

    // 积分值
    var score: Int? = null

    // 积分类型(16 - 奖惩登记)
    var scoreType: Int? = null

    // 操作类型(0：新增，1：扣分)
    var operType: Int? = null

    // 防重复提交
    var token: String? = null

    // 备注
    var remark: String? = null

    // 说明
    var explainTxt: String? = null

}

@ApiModel
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = SnakeCaseStrategy::class)
class UserCreditRequestForm {

    // 区县
    var regionId: Long? = null

    // 用户ID
    var userId: Long? = null

    // 用户姓名
    var userName: String? = null

    // 发送积分组织
    var orgId: Long? = null

    // 积分值
    var score: Int? = null

    // 积分类型(16 - 奖惩登记)
    var scoreType: Int? = null

    // 操作类型(0：新增，1：扣分)
    var operType: Int? = null

    // 防重复提交
    var token: String? = null

    // 备注
    var remark: String? = null

    // 说明
    var explainTxt: String? = null

}