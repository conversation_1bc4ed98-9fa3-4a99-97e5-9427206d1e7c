package com.goodsogood.ows.model.vo;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;


@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserHighlightForm {

    @Id
    @JsonProperty(value = "highlight_id")
    private Long userHighlightId;

    @ApiModelProperty("用户ID")
    private Integer userId;

    @ApiModelProperty("1.个人成绩轨迹 ")
    private Integer type;


    @ApiModelProperty("简介")
    private String desc;


    @ApiModelProperty("图片或者视频链接地址，多个以逗号分隔")
    private String url;


    @ApiModelProperty("支部发展史获取时间")
    @JsonProperty(value = "node_time")
    private String nodeTime;

}
