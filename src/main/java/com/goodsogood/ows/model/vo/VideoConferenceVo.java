package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.goodsogood.ows.model.db.VideoConferenceUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * t_video_conference 实体类
 *
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoConferenceVo {

	@ApiModelProperty("会议id")
	private Long conferenceId;

	@ApiModelProperty("会议类型（0-快速会议 1-预约会议）")
	private Integer conferenceType;

	@ApiModelProperty("会议名称")
	private String conferenceName;

	@ApiModelProperty("会议标题")
	private String conferenceTitle;

	@ApiModelProperty("开始时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date startTime;

	@ApiModelProperty("结束时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date endTime;

	@ApiModelProperty("参会人员")
	private List<VideoConferenceUserEntity> users;

}
