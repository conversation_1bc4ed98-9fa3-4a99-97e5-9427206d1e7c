package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 纪实检查列表vo
 *
 * <AUTHOR>
 * @create 2018/10/31 17:39
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingResultListForm {

    @ApiModelProperty(value = "活动id")
    @JsonProperty(value = "meeting_id")
    private Long meetingId;

    @ApiModelProperty(value = "组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty(value = "活动名称")
    @JsonProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "活动类别")
    @JsonProperty(value = "category")
    private String category;

    @ApiModelProperty(value = "活动类型")
    @JsonProperty(value = "types")
    private String types;

    @ApiModelProperty(value = "会开举办时间")
    @JsonProperty(value = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "活动提交时间")
    @JsonProperty(value = "submit_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    @ApiModelProperty(value = "状态")
    @JsonProperty(value = "status")
    private Short status;

}
