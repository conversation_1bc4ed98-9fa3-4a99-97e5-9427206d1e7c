package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName : TaskQueryForm
 * <AUTHOR> tc
 * @Date: 2022/2/16 13:53
 * @Description : 活动完成情况
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskQueryForm {

    @ApiModelProperty(value = "活动类型编号")
    @JsonProperty("type_id")
    private Integer typeId;

    @ApiModelProperty(value = "执行组织编号集合")
    @JsonProperty("org_id_list")
    private List<Long> orgIdList;

    @ApiModelProperty(value = "查询年份")
    @JsonProperty("query_year")
    private String queryYear;

}
