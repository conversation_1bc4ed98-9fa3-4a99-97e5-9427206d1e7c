package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingResultEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.Valid;

/**
 * 有活动的情况，填写决议
 * <AUTHOR>
 * @create 2018-10-31 11:22
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingResultForm {


     /**
      * 活动
      */
     @Valid
     private MeetingEntity meeting;

     /**
      * 决议
      * 包含：决议、决议附件、审批
      */
     @Valid
     private MeetingResultEntity result;


     /**
      * 用于接收填写任务的答案
      */
     @Valid
     private MeetingReportForm topic;

}
