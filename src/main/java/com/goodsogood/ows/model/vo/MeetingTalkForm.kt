package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import org.hibernate.validator.constraints.Length
import java.time.LocalDateTime
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotEmpty
import javax.validation.constraints.NotNull

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingTalkForm {

    @ApiModelProperty("谈心谈话主键id")
    @JsonProperty(value = "talk_id")
    var talkId: Long? = null

    @ApiModelProperty("创建组织id")
    @JsonProperty(value = "org_id")
    @get: NotNull(message = "创建组织ID不能为空")
    var orgId: Long? = null

    @ApiModelProperty("来源：0 - 无来源， 1 - 民主生活会会前， 2 - 民主生活会会后")
    var source: Int = 0

    @ApiModelProperty("来源第三方ID")
    @JsonProperty(value = "source_id")
    var sourceId: Long? = null

    @ApiModelProperty("谈话人")
    @JsonProperty(value = "talk_user")
    @get:NotEmpty(message = "谈话人不能为空")
    var talkUser: MutableList<TalkUser> = mutableListOf()

    @ApiModelProperty("被谈话人")
    @JsonProperty(value = "to_talk_user")
    @get:NotEmpty(message = "被谈话人不能为空")
    var toTalkUser: MutableList<TalkUser> = mutableListOf()

    @ApiModelProperty("是否编辑 1 - 已编辑, 0 - 未编辑")
    @JsonProperty(value = "is_submit")
    var isSubmit: Int = 1

    @ApiModelProperty("谈心谈话类型 0：默认" +
            "1：成员之间\n" +
            "2：成员与部门\n" +
            "3：成员与支部\n" +
            "4：成员与联系点\n" +
            "5：个人访谈")
    @JsonProperty(value = "talk_type")
    var talkType: Int = 0

    @ApiModelProperty("谈话地址")
    @get: Length(max = 500, message = "地址字数不能超过200")
    @get:NotBlank(message = "谈话地址不能为空")
    var location: String? = null

    @ApiModelProperty("谈话时间 - 开始时间")
    @JsonProperty(value = "begin_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @get:NotNull(message = "开始时间不能为空")
    var beginTime: LocalDateTime? = null

    @ApiModelProperty("谈话时间 - 结束时间")
    @JsonProperty(value = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @get:NotNull(message = "结束时间不能为空")
    var endTime: LocalDateTime? = null

    @ApiModelProperty("谈话内容")
    @JsonProperty(value = "talk_content")
    var talkContent: MutableList<TalkContent> = mutableListOf()

    @ApiModelProperty("文件")
    var files: MutableList<MeetingFileForm> = mutableListOf()

    constructor()

    constructor(
        talkId: Long? = null,
        source: Int = 0,
        sourceId: Long? = null,
        talkUser: MutableList<TalkUser> = mutableListOf(),
        toTalkUser: MutableList<TalkUser>  = mutableListOf(),
        talkType: Int = 0,
        location: String? = "",
        beginTime: LocalDateTime? = null,
        endTime: LocalDateTime? = null,
        talkContent: MutableList<TalkContent> = mutableListOf(),
        files: MutableList<MeetingFileForm> = mutableListOf()
    ) {
        this.talkId = talkId
        this.source = source
        this.sourceId = sourceId
        this.talkUser = talkUser
        this.toTalkUser = toTalkUser
        this.talkType = talkType
        this.location = location
        this.beginTime = beginTime
        this.endTime = endTime
        this.talkContent = talkContent
        this.files = files
    }

    override fun toString(): String {
        return "MeetingTalkForm(talkId=$talkId, orgId=$orgId, source=$source, sourceId=$sourceId, talkUser=$talkUser, toTalkUser=$toTalkUser, isSubmit=$isSubmit, talkType=$talkType, location=$location, beginTime=$beginTime, endTime=$endTime, talkContent=$talkContent, files=$files)"
    }

}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class TalkUser {

    @ApiModelProperty("用户ID")
    @JsonProperty(value = "user_id")
    var userId: Long? = null

    @ApiModelProperty("用户姓名")
    var username: String? = null

    constructor()

    constructor(userId: Long?, username: String?) {
        this.userId = userId
        this.username = username
    }

    override fun toString(): String {
        return "TalkUser(userId=$userId, username=$username)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class TalkContent {

    @ApiModelProperty("谈话标题")
    @get: Length(max = 500, message = "谈话标题过长")
    var title: String? = null

    @ApiModelProperty("谈话内容")
    var content: String? = null

    constructor()

    constructor(title: String?, content: String?) {
        this.title = title
        this.content = content
    }

    override fun toString(): String {
        return "TalkContent(title=$title, content=$content)"
    }

}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingTalkVO {

    @ApiModelProperty("谈话主键ID")
    @JsonProperty(value = "talk_id")
    var talkId: Long? = null

    @ApiModelProperty("谈话类型")
    @JsonProperty(value="talk_type")
    var talkType: Int? = null

    @ApiModelProperty("谈话人")
    @JsonProperty(value = "talk_user")
    var talkUser: String? = null

    @ApiModelProperty("被谈话人")
    @JsonProperty(value = "to_talk_user")
    var toTalkUser: String? = null

    @ApiModelProperty("谈话时间")
    @JsonProperty(value = "talk_time")
    var talkTime: String? = null

    @ApiModelProperty("是否编辑 1 - 已编辑, 0 - 未编辑")
    @JsonProperty(value = "is_submit")
    var isSubmit: Int? = null

    @ApiModelProperty("来源id")
    @JsonProperty(value = "source_id")
    var sourceId:Long?=null

    @ApiModelProperty("来源：0 - 无来源 1 - 民族生活会会前，2 - 民主生活会会后")
    @JsonProperty(value = "source")
    var source:Int?=null

    constructor()

    constructor(talkId: Long?, talkUser: String?, toTalkUser: String?, talkTime: String?,sourceId: Long?) {
        this.talkId = talkId
        this.talkUser = talkUser
        this.toTalkUser = toTalkUser
        this.talkTime = talkTime
        this.sourceId = sourceId
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class PartyGroupUserVO {

    @ApiModelProperty("用户ID")
    @JsonProperty(value = "user_id")
    var userId: Long? = null

    @ApiModelProperty("用户姓名")
    @JsonProperty(value = "user_name")
    var userName: String? = null

    @ApiModelProperty("类型")
    var type: String? = null

    override fun toString(): String {
        return "PartyGroupUserVO(userId=$userId, userName=$userName, type=$type)"
    }
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class GeneraLeaderAndUserTalkForm {

    @ApiModelProperty("组织ID")
    @JsonProperty(value = "org_id")
    @get: NotNull(message = "创建组织ID不能为空")
    var orgId: Long? = null

    @ApiModelProperty("人员列表")
    @JsonProperty(value = "user_list")
    @get: NotEmpty(message = "创建组织ID不能为空")
    var userList: MutableList<TalkUser> = mutableListOf()

    @ApiModelProperty("谈心谈话类型 0：默认" +
            "1：成员之间\n" +
            "2：成员与部门\n" +
            "3：成员与支部\n" +
            "4：成员与联系点\n" +
            "5：个人访谈\n"+
            "6：书记与班子之间\n" +
            "7：班子之间\n" +
            "8：班子与党员之间\n" +
            "9：党员之间\n")
    @JsonProperty(value = "talk_type")
    var talkType: Int = 0

    @ApiModelProperty("来源：0 - 无来源， 1 - 民主生活会会前， 2 - 民主生活会会后")
    @get: NotNull(message = "来源不能为空")
    var source: Int = 0

    @ApiModelProperty("来源第三方ID")
    @JsonProperty(value = "source_id")
    @get: NotNull(message = "第三方ID不能为空")
    var sourceId: Long? = null

    override fun toString(): String {
        return "GeneraLeaderAndUserTalkForm(orgId=$orgId, userList=$userList, talkType=$talkType, source=$source, sourceId=$sourceId)"
    }

}

enum class TalkTypeEnum(val key: Int, val value: String, val template: Int, val attach: Int) {

    DEFAULT(0, "默认", 0, 0),
    LEADER(1, "班子成员之间", 23, 11),
    LEADER_DEP(2, "班子成员与分管部门负责同志之间", 24, 12),
    LEADER_BRANCH(3, "班子成员与所在支部党员代表之间", 25, 13),
    LEADER_LINK(4, "班子成员与联系点单位代表之间", 26, 14),
    PERSONAL(5, "个人座谈", 22, 9),
    LEADER_TEAM(6, "党支部书记和班子成员之间", 23, 11),
    TEAM(7, "班子成员之间", 24, 12),
    TEAM_PARTY(8, "班子成员与党员之间", 25, 13),
    PARTY(9, "党员之间", 26, 14),
    // TODO 暂时把template和attach设置为一样，后续更改
    PARTY_10(10, "班子成员之间", 26, 14),
    PARTY_11(11, "班子和党员之间", 26, 14),
    PARTY_12(12, "委员和党员之间", 26, 14),
    PARTY_13(13, "家庭发生重大变故", 0, 0),
    PARTY_14(14, "出现重大困难", 0, 0),
    PARTY_15(15, "身心健康出现突出问题", 0, 0),
    PARTY_16(16, "受到处分处置", 0, 0),
    PARTY_17(17, "收到不良反应", 0, 0),
    PARTY_18(18, "其他", 0, 0);


    companion object {
        fun getTalkTypeEnum(key: Int): TalkTypeEnum? {
            values().forEach {
                if (it.key == key) {
                    return it
                }
            }
            return null
        }
    }
}