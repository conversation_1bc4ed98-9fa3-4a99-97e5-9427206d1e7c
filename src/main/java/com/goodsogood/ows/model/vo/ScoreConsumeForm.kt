package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import lombok.extern.log4j.Log4j2

/**
 * 积分对象
 *
 * <AUTHOR>
 * @create 2021-08-06
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@Log4j2
class ScoreConsumeForm {
    @ApiModelProperty("区县编号")
    var regionId: Long? = null

    @JsonProperty(value = "user_id")
    @ApiModelProperty("用户id")
    var userId: Long? = null

    @JsonProperty(value = "user_name")
    @ApiModelProperty("用户姓名")
    var userName: String? = null

    @ApiModelProperty(name = "提交数据唯一标识")
    @JsonProperty(value = "token")
    var token: String? = null

    @ApiModelProperty("分数")
    var score: Long? = null

    /**
     * 6 优考试积分 7 新闻学习积分 8 打卡积分 9 答题积分 10 出题积分
     * 11 党费积极交纳积分 12 组织生活积分 13 党建任务积分 14 创新任务积分
     * 15 业务任务积分 16 创先争优积分 17 理论武装积分
     */
    @JsonProperty(value = "score_type")
    @ApiModelProperty("积分类型")
    var scoreType: Int? = null

    @ApiModelProperty("操作类型：0：新增 1：扣分")
    @JsonProperty(value = "oper_type")
    var operType: Int? = null

    /**
     * 积分所属的组织编号
     */
    @ApiModelProperty("积分所属的组织id")
    @JsonProperty(value = "org_id")
    var orgId: Long? = null

    /**
     * 备注
     */
    var remark: String? = null

    /**
     * 积分组织编号
     */
    @JsonProperty(value = "score_org_id")
    @ApiModelProperty("积分组织编号")
    var scoreOrgId: Long? = null

    /**
     * 说明 放发或扣减组织积分时的说明
     */
    @ApiModelProperty("放发或扣减组织积分时的说明")
    @JsonProperty(value = "explain_txt")
    var explainTxt: String? = null


    /**
     * 消费时间
     */
    @ApiModelProperty(name = "消费时间")
    @JsonProperty(value = "consume_time")
    var consumeTime: String? = null


    constructor() : super()
    constructor(
        regionId: Long?,
        userId: Long?,
        token: String?,
        score: Long?,
        scoreType: Int?,
        operType: Int?,
        orgId: Long?,
        explainTxt: String?
    ) {
        this.regionId = regionId
        this.userId = userId
        this.token = token
        this.score = score
        this.scoreType = scoreType
        this.operType = operType
        this.orgId = orgId
        this.explainTxt = explainTxt
    }

    constructor(
        regionId: Long?,
        userId: Long?,
        token: String?,
        score: Long?,
        scoreType: Int?,
        operType: Int?,
        orgId: Long?,
        explainTxt: String?,
        remark: String?
    ) {
        this.regionId = regionId
        this.userId = userId
        this.token = token
        this.score = score
        this.scoreType = scoreType
        this.operType = operType
        this.orgId = orgId
        this.explainTxt = explainTxt
        this.remark = remark
    }

    constructor(
        regionId: Long?,
        userId: Long?,
        token: String?,
        score: Long?,
        scoreType: Int?,
        operType: Int?,
        orgId: Long?,
        explainTxt: String?,
        remark: String?,
        consumeTime: String?
    ) {
        this.regionId = regionId
        this.userId = userId
        this.token = token
        this.score = score
        this.scoreType = scoreType
        this.operType = operType
        this.orgId = orgId
        this.explainTxt = explainTxt
        this.remark = remark
        this.consumeTime = consumeTime
    }
    constructor(
        regionId: Long?,
        token: String?,
        scoreOrgId: Long?,
        score: Long?,
        scoreType: Int?,
        operType: Int?,
        orgId: Long?,
        explainTxt: String?,
        remark: String?
    ) {
        this.regionId = regionId
        this.scoreOrgId = scoreOrgId
        this.token = token
        this.score = score
        this.scoreType = scoreType
        this.operType = operType
        this.orgId = orgId
        this.explainTxt = explainTxt
        this.remark = remark
    }

    constructor(
        regionId: Long?,
        token: String?,
        scoreOrgId: Long?,
        score: Long?,
        scoreType: Int?,
        operType: Int?,
        orgId: Long?,
        explainTxt: String?,
        remark: String?,
        consumeTime: String?
    ) {
        this.regionId = regionId
        this.scoreOrgId = scoreOrgId
        this.token = token
        this.score = score
        this.scoreType = scoreType
        this.operType = operType
        this.orgId = orgId
        this.explainTxt = explainTxt
        this.remark = remark
        this.consumeTime = consumeTime
    }
}