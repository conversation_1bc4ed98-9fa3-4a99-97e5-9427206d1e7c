package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @program: ows-meeting
 * @description: 统计组织信息
 * @author: Mr.<PERSON>
 * @create: 2019-04-22 14:03
 **/
@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatisticsUserOrgLifeForm {

    @ApiModelProperty("用户ID")
    @JsonProperty(value = "user_id")
    private Long userId;


    @ApiModelProperty("参与次数")
    @JsonProperty(value = "participate_num")
    private Integer participateNum;

    @ApiModelProperty("活动类型id")
    @JsonProperty(value = "type_id")
    private String typeId;

    @ApiModelProperty("活动类型名称")
    @JsonProperty(value = "type_name")
    private String typeName;


    @JsonProperty(value = "statistical_year")
    private Integer statisticalYear;


    @ApiModelProperty("统计的月（1-12）")
    @JsonProperty(value = "statistical_month")
    private Integer statisticalMonth;
}
