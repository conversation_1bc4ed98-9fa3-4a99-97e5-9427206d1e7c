package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
data class UploadFileResultForm @JvmOverloads constructor(

    @ApiModelProperty("文件ID")
    var id: Long? = null,

    @ApiModelProperty("名称")
    var name: String? = null,

    @ApiModelProperty("文件路径")
    var path: String? = null,

    @ApiModelProperty("文件名称")
    var fileName: String? = null,

    @ApiModelProperty("文件大小")
    var size: Long? = null
)