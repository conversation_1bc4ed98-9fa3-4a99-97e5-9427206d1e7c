package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <p>Description: 活动类型</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class GroupAddForm {
    @JsonProperty(value = "type_ids")
    @NotEmpty(message = "{NotEmpty.group.typeIds}")
    @Size(min = 2,message = "{Length.group.typeIds}")
    @ApiModelProperty("类型")
    private List<Long> typeIds;
}
