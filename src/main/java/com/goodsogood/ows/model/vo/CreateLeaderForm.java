package com.goodsogood.ows.model.vo;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version 2019/9/10 15:47
 */

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Auther: ruoyu
 * Date: 19-5-7
 * Description:成立领导班子时间返回结果集
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateLeaderForm {

    @ApiModelProperty(value = "用户所在组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty(value = "用户所在组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty(value = "用户所在组织父路径")
    @JsonProperty(value = "org_level")
    private String orgLevel;

    @ApiModelProperty(value = "用户id")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    @JsonProperty(value = "user_name")
    private String userName;

    @ApiModelProperty(value = "任职开始时间")
    @JsonProperty(value = "work_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date workDate;
}