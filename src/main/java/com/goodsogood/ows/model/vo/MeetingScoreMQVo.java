package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.MeetingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @ClassName : MeetingScoreMQVo
 * <AUTHOR> tc
 * @Date: 2022/1/25 14:33
 * @Description : 组织生活积分队列实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingScoreMQVo {
    @JsonProperty("regionId")
    private Long regionId;
    @JsonProperty("meeting")
    private MeetingEntity meeting;
    //是否为取消活动，取消活动需要回退组织积分
    @JsonProperty("isCancel")
    private Boolean isCancel;
    // type 为 3.规定时间内补学添加个党员积分 时  参数map对象
    @JsonProperty("param")
    private Map<String,String> param;
    //操作类型 1.组织生活增加积分 2.撤回/退回和取消组织生活时，积分操作  3.规定时间内补学添加个党员积分
    @JsonProperty("type")
    private Integer type;
}
