package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Auther: ruoyu
 * Date: 19-5-22
 * Description:
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PeriodFindOrgsResultForm {

    @JsonProperty("org_id")
    private Long orgId;

    @ApiModelProperty("支委会届次开始时间")
    @JsonProperty("period_create_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date periodCreateTime;

    @ApiModelProperty("当前组织所属支委会的数量")
    @JsonProperty("period_size")
    private Integer periodSize;

    @ApiModelProperty("创建创建党小组数量")
    @JsonProperty("create_org_groups")
    private Integer createOrgGroups;

}
