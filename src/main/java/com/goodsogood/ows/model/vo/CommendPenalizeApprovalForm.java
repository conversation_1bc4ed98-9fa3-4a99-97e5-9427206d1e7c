package com.goodsogood.ows.model.vo;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

@Data
@Log4j2
public class CommendPenalizeApprovalForm {

    public static final Integer USER = 1;

    public static final Integer ORG = 2;

    // 1-用户， 2-组织
    private Integer type;

    // 申请对象
    private String appObject;

    // 奖惩类型
    private String category;

    // 奖惩级别
    private String level;

    // 奖惩名称
    private String name;

    public CommendPenalizeApprovalForm() {
    }

    public CommendPenalizeApprovalForm(Integer type, String appObject, String category, String level, String name) {
        log.debug("创建奖惩登记审批body参数body:[{}], appObject:[{}],category:[{}],level:[{}],name:[{}]", type, appObject, category, level, name);
        this.type = type;
        this.appObject = appObject;
        this.category = category;
        this.level = level;
        this.name = name;
    }
}
