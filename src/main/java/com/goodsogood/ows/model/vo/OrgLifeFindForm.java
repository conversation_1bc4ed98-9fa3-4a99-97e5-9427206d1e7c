package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgLifeFindForm {

    @ApiModelProperty("区县id")
    private Long regionId;

    @ApiModelProperty("会议名称")
    private String title;

    @ApiModelProperty("所属组织")
    private Long orgId;

    @ApiModelProperty("所属组织及下级组织")
    private List<Long> orgIds;

    @ApiModelProperty("标签")
    private List<Long> tag;

    @ApiModelProperty("年度")
    private Integer years;

    @ApiModelProperty("状态")
    private List<Integer> status;

}
