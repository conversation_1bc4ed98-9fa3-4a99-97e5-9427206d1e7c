package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.vo.request.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @describe 基层述职评议结果统计查询参数
 * @date 2019-12-26
 */

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
public class MeetingOrgDebriefReviewStatisticsQueryForm extends PageRequest implements Serializable {

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("评议年度")
    @JsonProperty(value = "review_year")
    private Integer reviewYear;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;
}
