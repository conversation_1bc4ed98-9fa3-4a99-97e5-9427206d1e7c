package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * 钉钉推送对象
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PushDingDingTemplateForm {
    private static String dataTemplate = "mengting的测试消息";
    @JsonProperty("data")
    private DataForm data;

    @ApiModelProperty("模板id")
    @JsonProperty("template_id")
    private Long templateId;

    @ApiModelProperty("渠道类型 1:短信 2:微信 3:主题推送 4:钉钉")
    @JsonProperty("channel_type")
    private Byte channelType;

    @ApiModelProperty("业务系统的application.name，例如纪实 meeting")
    @JsonProperty("source")
    private String source;

    @ApiModelProperty("推送类型,0-即时推送，1-定时推送，此处传0即可")
    @JsonProperty("push_type")
    private Integer pushType;

    @ApiModelProperty("需要推送的组织id 如果不是全量推送org_ids，user_ids不能同时为空")
    @JsonProperty("org_ids")
    private Set<Long> orgIds;


    @ApiModelProperty("需要推送的用户id 如果不是全量推送org_ids，user_ids不能同时为空")
    @JsonProperty("user_ids")
    private Set<Long> userIds;

    @ApiModelProperty("推送时间,定时推送时不能为空")
    @JsonProperty("push_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pushTime;

    @ApiModelProperty("是否全量推送 0-否，1-是")
    @JsonProperty("is_full")
    private Integer full;


    @ApiModelProperty("推送公众号关联的组织id")
    @JsonProperty("org_id")
    private Long orgId;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class DataForm {
        @ApiModelProperty("username")
        @JsonProperty("user_name")
        private String userName;

        @ApiModelProperty("组织生活会 民主生活会")
        @JsonProperty("source_name")
        private String sourceName;

        @ApiModelProperty("任务名称")
        @JsonProperty("task_name")
        private String taskName;

        @ApiModelProperty("跳转地址")
        @JsonProperty("target_url")
        private String targetUrl;

        @ApiModelProperty("userid")
        @JsonProperty("user_id")
        private Long userId;
    }

}
