package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * root或者 非支部OrgStatus
 *
 * <AUTHOR>
 * @date 2018/12/21
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgStatus {

    @ApiModelProperty(value = "活动类型id")
    @JsonProperty(value = "type_id")
    private Long typeId;

    @ApiModelProperty(value = "活动类型名称")
    private String type;

    @ApiModelProperty(value = "时间,字符串 如：2018年12月或2018年第4季度")
    private String time;

    @ApiModelProperty(value = "未完成组织总数")
    @JsonProperty(value = "undone_org_count")
    private Integer undoneOrgCount;

    @ApiModelProperty(value = "未完成组织列表")
    @JsonProperty(value = "undone_orgs")
    private List<UndoneOrg> undoneOrgList;

    public OrgStatus(Long typeId, String type, Integer undoneOrgCount) {
        this.typeId = typeId;
        this.type = type;
        this.undoneOrgCount = undoneOrgCount;
    }

    public OrgStatus(Long typeId, String type, String time, Integer undoneOrgCount) {
        this(typeId, type, undoneOrgCount);
        this.time = time;
    }

    public OrgStatus(Long typeId, String type, String time, Integer undoneOrgCount, List<UndoneOrg> undoneOrgList) {
        this.typeId = typeId;
        this.type = type;
        this.time = time;
        this.undoneOrgCount = undoneOrgCount;
        this.undoneOrgList = undoneOrgList;
    }

    @Data
    @ApiModel
    public static class UndoneOrg {
        @ApiModelProperty(value = "组织id")
        @JsonProperty(value = "org_id")
        private Long orgId;

        @ApiModelProperty(value = "组织名称")
        @JsonProperty(value = "org_name")
        private String orgName;
    }
}
