package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 党员奖惩查询表单
 * @date 2019/12/27
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCommendPenalizeQueryForm {

    @ApiModelProperty("组织ID")
    @JsonProperty(value = "org_id")
    @NotNull(message = "{NotNull.Org.Id}")
    private Long orgId;

    @ApiModelProperty("用户名字")
    @JsonProperty(value = "user_name")
    private String userName;

    @ApiModelProperty("奖惩类别")
    @JsonProperty(value = "category")
    private Integer category;

    @ApiModelProperty("奖惩级别")
    @JsonProperty(value = "level")
    private Integer level;

    @ApiModelProperty("奖惩类型（1:奖励 2:惩罚）")
    @JsonProperty(value = "type")
    private Integer type;

    @ApiModelProperty("审核状态 1-待审核、2-审核通过、3-审核不通过")
    @JsonProperty(value = "approval_status")
    private Integer approvalStatus;

    @ApiModelProperty("奖惩名称")
    @JsonProperty(value = "name")
    private Integer name;

    @ApiModelProperty("开始时间")
    @JsonProperty(value = "start_time")
    private String startTime;

    @ApiModelProperty("结束时间")
    @JsonProperty(value = "end_time")
    private String endTime;

    @ApiModelProperty("页码")
    @JsonProperty("page")
    private Integer page = 1;

    @ApiModelProperty("每页大小")
    @JsonProperty("page_size")
    private Integer pageSize = 10;
}
