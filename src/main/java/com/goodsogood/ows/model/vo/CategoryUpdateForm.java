package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.CategoryEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>Description: 活动类型</p>
 *
 * <AUTHOR>
 * @date 2018/10/19 10:27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CategoryUpdateForm extends CategoryAddForm{

    @ApiModelProperty("id")
    @NotNull(message = "{NotNull.category.categoryId}")
    @JsonProperty(value = "category_id")
    private Long categoryId;

//    @JsonProperty("category")
//    @ApiModelProperty(value = "类别")
//    @NotBlank(message = "{NotBlank.category.category}")
//    @Length(max = 50,message = "{Length.category}")
//    private String category;

    public CategoryEntity toEntity() {
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setCategoryId(this.getCategoryId());
        categoryEntity.setCategory(this.getCategory());
        return categoryEntity;
    }
}
