package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.vo.request.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @describe 查询组织述职评议列表
 * @date 2019-12-26
 */

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class MeetingOrgDebriefReviewQueryForm extends PageRequest implements Serializable {

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("评议等级 1:好 2:较好 3:一般 4:差")
    @JsonProperty(value = "rating")
    private Integer rating;

    @ApiModelProperty("评议年度")
    @JsonProperty(value = "review_year")
    private Integer reviewYear;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("组织level")
    @JsonProperty(value = "org_level")
    private String orgLevel;

    @JsonIgnore
    private List<Long> orgIds;

    @ApiModelProperty("数据状态 1 正常 2删除")
    private Integer status;
}