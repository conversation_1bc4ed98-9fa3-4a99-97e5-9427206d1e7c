package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @describe 组织述职评议列表
 * @date 2019-12-26
 */

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingOrgDebriefReviewQueryVO {

    @ApiModelProperty("主键id")
    @JsonProperty(value = "meeting_org_debrief_review_id")
    private Long meetingOrgDebriefReviewId;

    @ApiModelProperty("组织书记")
    @JsonProperty(value = "party_leader")
    private String partyLeader;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("评议等级 1:好 2:较好 3:一般 4:差")
    @JsonProperty(value = "rating")
    private Integer rating;

    @ApiModelProperty("组织类型")
    private Integer orgTypeChild;

}
