package com.goodsogood.ows.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 报表vo，列序号：0~25
 * <AUTHOR>
 * @date 2021.10.11
 */

@Data
public class MeetingReportVo {

    @ExcelProperty("活动序号")
    private Integer number;

    @ExcelProperty("活动名称")
    private String name;

    @ExcelProperty("所属类别")
    private String category;

    @ExcelProperty("活动类型")
    private String types;

    @ExcelProperty("标签")
    private String label;

    @ExcelProperty("所属组织")
    private String orgName;

    @ExcelProperty("活动地点")
    private String address;

    @ExcelProperty("开始时间")
    private String startTime;

    @ExcelProperty("结束时间")
    private String endTime;

    @ExcelProperty("活动总时长")
    private Double totalHours;

    @ExcelProperty("理论学习时长")
    private Double theoryLearn;

    @ExcelProperty("活动内容")
    private String content;

    @ExcelProperty("主持人")
    private String hostUser;

    @ExcelProperty("记录人")
    private String recordUser;

    @ExcelProperty("参与人")
    private String participantUsers;

    @ExcelProperty("列席人员")
    private String attendUsers;

    @ExcelProperty("签到方式")
    private String signInWay;

    @ExcelProperty("应到人数")
    private Integer shouldAttend;

    @ExcelProperty("实到人数")
    private Integer resultAttend;

    @ExcelProperty("已签到人数")
    private Integer signNum;

    @ExcelProperty("补学人数")
    private Integer makeUpNum;

    @ExcelProperty("请假人数")
    private Integer leaveNum;

    @ExcelProperty("未签到人数")
    private Integer unSignNum;

    @ExcelProperty("议程")
    private String agenda;

    @ExcelProperty("提交时间")
    private String submitTime;

    @ExcelProperty("状态")
    private String meetingStatus;
}
