package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Auther: ruoyu
 * Date: 19-5-13
 * Description: 用于获取组织与领导班子　相关数据返回结果集
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserLeaderCallBackForm {

    @ApiModelProperty(value = "组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty(value = "返回的结果集")
    @JsonProperty(value = "values")
    private List<CreateLeaderForm> values;
}
