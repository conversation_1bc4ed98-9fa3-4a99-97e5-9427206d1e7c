package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 *
 * 绑定任务与该任务的答案集合
 *
 * <AUTHOR>
 * @create 2018-10-24 15:38
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingReportTopicContentForm {


    /**
     * 任务的id
     */
//    @NotNull(message = "{NotNull.topic.topic-id}")
    @JsonProperty(value = "topic_id")
    private Long topicId;

    /**
     * 组织id
     */
    @JsonProperty(value = "org_id")
    private Long orgId;
    /**
     * 任务组织关联表id
     */
    @JsonProperty(value = "topic_org_id")
    private Long topicOrgId;

    /**
     * 工作任务的关联id
     */
    @JsonProperty(value = "meeting_topic_id")
    //@NotNull(message = "{NotNull.report.meeting-topic-id}")
    private Long meetingTopicId;

    /**
     * 封装的任务的答案
     */
     @NotNull(message = "{NotNull.topic.answer.contents}")
     @Valid
     private List<MeetingReportAnswerForm> contents;

}
