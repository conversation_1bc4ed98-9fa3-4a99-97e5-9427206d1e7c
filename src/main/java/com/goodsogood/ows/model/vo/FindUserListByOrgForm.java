package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName FindOrgListForm
 * @description
 * @date 2018-12-11 16:08
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FindUserListByOrgForm {

    @JsonProperty("org_id")
    private Long orgId;

    @JsonProperty("user_ids")
    private List<Long> userIds;

    @ApiModelProperty("是否过滤非党员 1-是，2-否，默认为2")
    @JsonProperty("is_filter")
    private Integer isFilter = 1;

    @ApiModelProperty("是否包含12371序列号为空，1-是，2-否，默认为2")
    @JsonProperty("is_include")
    private Integer isInclude = 2;

    @ApiModelProperty("用户姓名")
    @JsonProperty("user_name")
    private String userName;

    @ApiModelProperty("页码")
    @JsonProperty("page")
    private Integer page = 1;

    @ApiModelProperty("每页大小")
    @JsonProperty("page_size")
    private Integer pageSize = 10;
}
