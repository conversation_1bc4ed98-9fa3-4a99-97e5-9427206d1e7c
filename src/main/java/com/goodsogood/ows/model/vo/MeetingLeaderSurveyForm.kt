package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import javax.validation.constraints.Size

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class MeetingLeaderSurveyForm(
    // 领导名称 模糊查询
    var leader: String? = null,

    // 部门 模糊查询
    var orgName: String? = null,

    // 调研方式
    var interview: List<Int>? = null,

    // 日期
    @get:Size(max = 2, message = "日期长度不能超过2")
    var surveryTime: List<String?>? = null,

    // 调研类型 1. 日常调研、2.年度调研
    var surveryType: Int? = null,
)
