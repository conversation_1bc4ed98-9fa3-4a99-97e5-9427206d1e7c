package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PendingForm {

    /**
     * 用户编号
     */
    @JsonProperty(value = "user_id")
    private Long userId;

    /**
     * 清理类型 1:业务清理 2:到期自动清理 3:不处理清理
     */
    @JsonProperty(value = "clear_type")
    private  Integer clearType;

    /**
     * 1只需要待办 2只需要任务 3待办和任务都需要
     */
    @JsonProperty(value = "status")
    private Integer status;

    /**
     * 钉钉代办任务
     */
    @JsonProperty(value = "create_todo_task_request")
    private PendingTodoForm pendingTodoForm;

    /**
     *  任务系统任务
     */
    @JsonProperty(value = "third_add_form")
    private PendingTaskForm pendingTaskForm;

}
