package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class PendingTaskForm {

    /**
     * 区县编号
     */
    @JsonProperty(value = "region_id")
    private Long regionId;

    /**
     * 业务系统侧的唯一标识ID，即业务ID,当待办创建成功后，需要更换新的sourceId，保持一个待办任务对应一个sourceId
     * 本平台生成的唯一id
     */
    @JsonProperty(value = "source_id")
    private String sourceId;

    /**
     * 开始时间
     */
    @JsonProperty(value = "begin_time")
    private Date beginTime;

    /**
     * 结束时间   没有结束时间时为开始时间
     */
    @JsonProperty(value = "end_time")
    private Date endTime;

    /**
     * 是否需要回调确认结束    0：否，1：是
     */
    @JsonProperty(value = "callback")
    private Integer callback;

    /**
     * 任务标题
     */
    @JsonProperty(value = "task_title")
    private String taskTitle;

    /**
     *    第三方标签   ~~ 分隔
     */
    @JsonProperty(value = "source_mark")
    private String sourceMark;

    /**
     *    第三方uri  对应服务网关前缀
     */
    @JsonProperty(value = "source_uri")
    private String sourceUri;

    /**
     * 创建组织id
     */
    @JsonProperty(value = "create_org")
    private Long createOrg;

    /**
     * 接收用户id
     */
    @JsonProperty(value = "users")
    private List<Long> users;
}
