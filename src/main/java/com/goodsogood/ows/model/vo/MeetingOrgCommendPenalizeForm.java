package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.validate.group.Add;
import com.goodsogood.ows.validate.group.Delete;
import com.goodsogood.ows.validate.group.Detail;
import com.goodsogood.ows.validate.group.Edit;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @describe 添加修改组织奖惩信息请求参数
 * @date 2019-12-26
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingOrgCommendPenalizeForm {

    @ApiModelProperty("主键id")
    @JsonProperty(value = "meeting_org_commend_penalize_id")
    @NotNull(message = "id不能为空", groups = {Delete.class, Edit.class, Detail.class})
    private Long meetingOrgCommendPenalizeId;

    @ApiModelProperty("1:奖励 2:惩罚")
    @JsonProperty(value = "type")
    @NotNull(message = "奖励类型不能为空", groups = {Add.class, Edit.class})
    private Integer type;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    @NotNull(message = "组织id不能为空", groups = {Add.class, Edit.class})
    private Long orgId;

    @ApiModelProperty("批准日期")
    @JsonProperty(value = "ratify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "批准日期不能为空", groups = {Add.class, Edit.class})
    private Date ratifyTime;

    @ApiModelProperty("奖惩类别")
    @JsonProperty(value = "category")
    private Integer category;

    @ApiModelProperty("奖惩级别")
    @JsonProperty(value = "level")
    private Integer level;

    @ApiModelProperty("奖励或者惩罚名称")
    @JsonProperty(value = "name")
    @NotNull(message = "奖惩名称不能为空", groups = {Add.class, Edit.class})
    private Integer name;

    @ApiModelProperty("奖惩名称内容")
    @JsonProperty(value = "content")
    @Length(max = 200,message = "奖惩名称内容不能超过200字")
    private String content;

    @ApiModelProperty("颁奖单位")
    @JsonProperty(value = "award_unit")
    private String awardUnit;

    @ApiModelProperty("相关文件")
    @JsonProperty(value = "related_file")
    private String relatedFile;

    @ApiModelProperty("荣誉图片")
    @JsonProperty(value = "honor_pic")
    private List<MeetingFileForm> honorPic;

    @ApiModelProperty("依据说明")
    @JsonProperty(value = "basis_description")
    @Length(message = "依据说明长度最大1000", max = 1000, groups = {Add.class, Edit.class})
    private String basisDescription;

    @ApiModelProperty("附件")
    @JsonProperty(value = "attachment")
    private String attachment;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    @NotBlank(message = "组织名称不能为空", groups = {Add.class, Edit.class})
    private String orgName;

    @ApiModelProperty("文件")
    private List<MeetingFileForm> files;

    // 2021-03-15 隐藏。
    /*@ApiModelProperty("奖励或者惩罚原因")
    @JsonProperty(value = "reason")
    @NotBlank(message = "奖惩原因不能为空", groups = {Add.class, Edit.class})
    private String reason;

    @ApiModelProperty("奖励类型")
    @JsonProperty(value = "reward_type")
    private Integer rewardType;*/

}
