package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @Author: mengting
 * @Date: 2022/6/7 10:23
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ManageOrgForm {
    @JsonProperty("org_id")
    private Long organizationId;

    @JsonProperty("parent_id")
    private Long parentId;

    @JsonProperty("org_name")
    private String name;

    @JsonProperty("org_type")
    private Integer orgType;

    @JsonProperty("org_type_child")
    private Integer orgTypeChild;

    @JsonProperty("is_leaf")
    private Integer isLeaf;

}
