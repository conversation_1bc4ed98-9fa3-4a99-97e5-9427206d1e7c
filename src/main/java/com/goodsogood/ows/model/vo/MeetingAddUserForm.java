package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.MeetingUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 *
 * t_meeting 实体类
 *
 * 活动表
 *
 * <AUTHOR>
 * @create 2018-10-25 10:53
*/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingAddUserForm {
    @ApiModelProperty("活动id")
	@JsonProperty(value = "meeting_id")
	@NotNull(message = "{NotNull.meeting.meetingId}")
	private Long meetingId;

	@ApiModelProperty("参加人员")
	@JsonProperty(value = "participant_users")
	private List<MeetingUserEntity> participantUsers;

	@ApiModelProperty("列席人员")
	@JsonProperty(value = "attend_users")
	private List<MeetingUserEntity> attendUsers;

}

