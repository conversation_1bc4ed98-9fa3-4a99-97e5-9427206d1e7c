package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * t_meeting_plan 实体类
 *
 * <AUTHOR>
 * @create 2018-10-23 13:55
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingExecuteForm {


    @ApiModelProperty("id")
    @NotNull(message = "{NotNull.meetingPlan.meetingPlanId}")
    @JsonProperty(value = "meeting_plan_id")
    private Long meetingPlanId;

    @ApiModelProperty("停、启用状态。0：停用；1：启用")
    @JsonProperty(value = "is_execute")
    @NotNull(message = "{NotNull.meetingPlan.isExecute}")
    @Range(min = 0, max = 1, message = "{Range.meetingPlan.isExecute}")
    private Short isExecute;

}

