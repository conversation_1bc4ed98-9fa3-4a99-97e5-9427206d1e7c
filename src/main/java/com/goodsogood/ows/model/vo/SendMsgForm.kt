package com.goodsogood.ows.model.vo

import lombok.AllArgsConstructor
import lombok.NoArgsConstructor
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModelProperty
import lombok.Data

/**
 * <AUTHOR>
 * @date 2019-07-08 15:15
 * @since 1.0.3
 *
 * 增加主题党日提醒  tc 2021-11-19
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
open class SendMsgForm {
    @JsonProperty("org_name")
    var orgName: String? = null

    //跳转地址
    @JsonProperty("target_url")
    var targetUrl: String? = null

    @JsonProperty("user_id")
    @ApiModelProperty("用户编号")
    var userId: Long? = null

    override fun toString(): String {
        return "SendMsgForm(orgName=$orgName, targetUrl=$targetUrl, userId=$userId)"
    }

}