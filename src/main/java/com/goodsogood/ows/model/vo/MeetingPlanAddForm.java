package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.goodsogood.ows.model.db.MeetingPlanEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * t_meeting_plan 实体类
 *
 * <AUTHOR>
 * @create 2018-10-23 13:55
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingPlanAddForm {

    @NotBlank(message = "{NotBlank.meetingPlan.name}")
    @Length(max = 50, message = "{Length.meetingPlan.name}")
    @ApiModelProperty("组织生活名称")
    @JsonProperty(value = "name")
    private String name;


    @ApiModelProperty("执行方式:1.自定义间段(默认值);2自然月周期;3.季度周期;4.年度周期")
    @JsonProperty(value = "execute_type")
    @NotNull(message = "{NotNull.meetingPlan.executeType}")
    @Range(max = 4, min = 1, message = "{Range.meetingPlan.executeType}")
    private Short executeType;


    @ApiModelProperty("开始时间")
    @JsonProperty(value = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;


    @ApiModelProperty("结束时间")
    @JsonProperty(value = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;


    @ApiModelProperty("开始年份")
    @JsonProperty(value = "start_year")
    @Range(max = 9999, min = 2000, message = "{Range.meetingPlan.startYear}")
    private Integer startYear;


    @ApiModelProperty("开始月份")
    @JsonProperty(value = "start_month")
    @Range(max = 12, min = 1, message = "{Range.meetingPlan.startMonth}")
    private Integer startMonth;


    @ApiModelProperty("	开始季度")
    @JsonProperty(value = "start_quarter")
    @Range(max = 4, min = 1, message = "{Range.meetingPlan.startQuarter}")
    private Integer startQuarter;


    @ApiModelProperty("	执行组织")
    @JsonProperty(value = "execute_orgs")
//    @Nullable
    @Valid
    private List<OrgForm> executeOrgs;

    @ApiModelProperty("	活动类型")
    @JsonProperty(value = "meeting_types")
    @NotEmpty(message = "{NotEmpty.meetingPlan.meetingTypes}")
    @Valid
    private List<MeetingRequireAddForm> meetingTypes;

    // 新增发放任务类型：1 自动发放; 2 手动发放 --luoTaiQian
    @Range(min = 1,max = 2,message = "{Range.meetingPlan.sendType}")
    @JsonProperty(value ="send_type" )
    private short sendType;
    //自动发放的活动类型
//    @Nullable
    @Valid
    @JsonProperty(value = "auto_send_type_form")
    private MeetingPlanLimitAddForm autoSendTypeForm;

    public MeetingPlanEntity toEntity() {
        MeetingPlanEntity meetingPlanEntity = new MeetingPlanEntity();
        meetingPlanEntity.setName(this.getName());
        meetingPlanEntity.setExecuteType(this.getExecuteType());
        if (executeType != 1) {
            Calendar ca = Calendar.getInstance();
            if (executeType == 2) {
                ca.set(startYear, startMonth - 1, 1);// Calendar 月份从0开始
            } else if (executeType == 3) {
                switch (startQuarter) {
                    case 1:
                        ca.set(startYear, Calendar.JANUARY, 1);
                        break;
                    case 2:
                        ca.set(startYear, Calendar.APRIL, 1);
                        break;
                    case 3:
                        ca.set(startYear, Calendar.JULY, 1);
                        break;
                    case 4:
                        ca.set(startYear, Calendar.OCTOBER, 1);
                        break;
                    default:
                        break;
                }
            } else if (executeType == 4) {
                ca.set(startYear, Calendar.JANUARY, 1);
            }
            meetingPlanEntity.setStartTime(ca.getTime());
            meetingPlanEntity.setEndTime(null);
        } else {
            meetingPlanEntity.setStartTime(this.getStartTime());
            meetingPlanEntity.setEndTime(this.getEndTime());
        }

        meetingPlanEntity.setStartYear(this.getStartYear());
        meetingPlanEntity.setStartMonth(this.getStartMonth());
        meetingPlanEntity.setStartQuarter(this.getStartQuarter());
        meetingPlanEntity.setMeetingTypes(this.getMeetingTypes().stream().map(MeetingRequireAddForm::toEntity).collect(Collectors.toList()));
        meetingPlanEntity.setExecuteOrgs(this.getExecuteOrgs());
        return meetingPlanEntity;
    }

    /**
     * TODO 新增 --luoTaiQian
     * 自动发放的活动类型
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public final static class MeetingPlanLimitAddForm{
        //组织类型
        @Valid
        @NotEmpty(message = "{NotEmpty.meetingPlanLimit.orgTypeList}")
        private List<MeetingPlanLimitTypeAddForm> orgTypeList;
        //离退休组织：1 包含; 2 不包含; 3 仅包含
        @Range(min = 1,max = 3,message = "{Range.meetingPlanLimit.isRetire}")
        private short isRetire;
        //已成立党小组 1 包含; 2 不包含; 3 仅包含
        @Range(min = 1,max = 3,message = "{Range.meetingPlanLimit.group}")
        private short partyGroup;
        //已成立支委会 1 包含; 2 不包含; 3 仅包含
        @Range(min = 1,max = 3,message = "{Range.meetingPlanLimit.period}")
        private short period;
    }

    /**
     * TODO 新增 --luoTaiQian
     * 自动发放的组织类型
     */
    @Data
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public final static class MeetingPlanLimitTypeAddForm{
        //组织实际类型
        @Range(min = 1,max = 999999999,message = "{Range.MeetingPlanLimitType.orgTypeChild}")
        private int orgTypeChild;
        //组织类型名称
        @NotBlank(message = "{NotBlack.MeetingPlanLimitType.orgTypeChildName}")
        @Length(max = 255, message = "{Length.MeetingPlanLimitType.orgTypeChildName}")
        private String orgTypeChildName;
    }

}

