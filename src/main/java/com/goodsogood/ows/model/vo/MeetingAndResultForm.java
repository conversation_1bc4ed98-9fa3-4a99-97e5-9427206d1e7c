package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingResultEntity;
import com.goodsogood.ows.model.db.TopicEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>用于接收一次性提交纪实报告</p>
 * <p>包含：</p>
 * <li>活动</li>
 * <li>任务</li>
 * <li>决议</li>
 * <AUTHOR>
 * @create 2018-10-30 11:25
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingAndResultForm {

     /**
      * 活动
      */
     @Valid
     private MeetingEntity meeting;

     /**
      * 决议
      * 包含：决议、决议附件、审批
      */
     @Valid
     private MeetingResultEntity result;


     /**
      * 用于接收填写任务的答案
      */
     @Valid
     private MeetingReportForm topic;

     /**
      * 新增任务 补录时包含对应的答案
      */
     @Valid
     @JsonProperty(value = "add_topics")
     private List<TopicEntity> addTopics;

}
