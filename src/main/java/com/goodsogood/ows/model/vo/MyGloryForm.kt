package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty
import java.time.LocalDateTime
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotNull

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class MyGloryForm(

    @ApiModelProperty("用户ID")
    var userId: Long? = null,

    @ApiModelProperty("我的荣誉类型 1-党员奖励，2-民主评议")
    var type: Int? = null,

    @ApiModelProperty("时间")
    var time: LocalDateTime? = null,

    @ApiModelProperty("标题")
    var title: String? = null,

    @ApiModelProperty("描述")
    var remark: String? = null,

    @ApiModelProperty("图片")
    var path: String? = null
) {
    override fun toString(): String {
        return "MyGloryForm(userId=$userId, type=$type, time=$time, remark=$remark, path=$path)"
    }
}

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class DelGloryForm(

    @ApiModelProperty("用户ID")
    @get:NotNull(message = "用户ID不能为空")
    var userId: Long? = null,

    @get:NotNull(message = "type不能为空")
    var type: Int? = null,

    @ApiModelProperty("标题")
    @get:NotNull(message = "标题不能为空")
    var title: String? = null,

    @ApiModelProperty("描述")
    @get:NotBlank(message = "描述不能为空")
    var remark: String? = null,

    @ApiModelProperty("时间")
    @get:NotNull(message = "时间不能为空")
    var time: LocalDateTime? = null
) {
    override fun toString(): String {
        return "DelForm(userId=$userId, type=$type, remark=$remark, time=$time)"
    }
}


enum class GloryType(val key: Int, val value: String) {

    MEMBER_REWARD(1, "党员奖励"),
    COMMENT(2, "民主评议")
}
