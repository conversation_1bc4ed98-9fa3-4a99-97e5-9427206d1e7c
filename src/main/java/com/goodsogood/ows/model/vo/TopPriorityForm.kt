package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModelProperty
import org.hibernate.validator.constraints.Length
import org.hibernate.validator.constraints.Range
import java.io.Serial
import java.io.Serializable
import javax.validation.constraints.NotNull
import javax.validation.constraints.Size

/**
 * http://wiki.aidangqun.com/project/4?p=2060
 * http://wiki.aidangqun.com/project/4?p=2063
 * 对应的查询请求对象
 * <AUTHOR>
 * @date 2023/9/23
 * @description class TopPriorityForm
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class TopPriorityForm @JvmOverloads constructor(
    @ApiModelProperty(value = "查询关键字	标题、摘要、内容外链搜索")
    @get:Length(max = 200, message = "查询关键字长度不能超过200")
    var keyword: String? = null,

    @ApiModelProperty(value = "发布类型	0或者null代表全部， 1：市局发布，2：本单位发布")
    @get:Range(min = 0, max = 2, message = "发布类型只能是0或者1或者2")
    var type: Int? = null,

    @ApiModelProperty(value = "本组织是否已经关联	1：是，0：否")
    @get:Range(min = 0, max = 1, message = "本组织是否已经关联只能是0或者1")
    var associated: Int? = null,

    @ApiModelProperty(value = "媒体发布时间	数组：[‘2023-01-01’,’2023-12-31’]")
    @get:Size(max = 2, message = "媒体发布时间长度不能超过2")
    var sourceTime: List<String?>? = null,

    @ApiModelProperty(value = "学习单位名称")
    @get:Length(max = 200, message = "学习单位名称长度不能超过200")
    var associatedOrgName: String? = null,

    @ApiModelProperty(value = "会议类型	数组:[1,2,3,4,5]")
    var meetingType: List<Int>? = null,

    @ApiModelProperty(value = "学习时间	数组：[开始时间,结束时间]，可以用null填充")
    @get:Size(max = 2, message = "学习时间长度不能超过2")
    var learningTime: List<String?>? = null,

    @ApiModelProperty(value = "发布时间	数组：[开始时间,结束时间]，可以用null填充")
    @get:Size(max = 2, message = "发布时间长度不能超过2")
    var publishTime: List<String?>? = null,

    // 单位id|登录的时候会获得
    @ApiModelProperty(value = "单位id")
    @get:NotNull(message = "单位id不能为空")
    var unitOrgId: Long? = null,
    // | unit_org_name | string | 20 | Y | 单位名称|登录的时候会获得|
    var unitOrgName: String? = null,
    // | unit_short_name | string | 20 | Y | 单位简称|登录的时候会获得|
    var unitShortName: String? = null,

    // 按议题统计-第一议题标题
    var title: String? = null,
    // 按议题统计-间隔时间（天）
    var intervalTime: List<Int?>? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = -868948819322608186L
    }
}