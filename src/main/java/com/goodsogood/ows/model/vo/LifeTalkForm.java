package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LifeTalkForm {

    @ApiModelProperty("谈心谈话与民主生活会的关联关系表主键ID")
    private Long lifeTalkId;

    @ApiModelProperty("民主生活会主键ID")
    private Long lifeId;

    @ApiModelProperty("谈心谈话主键ID")
    private Long talkId;

    @ApiModelProperty("谈话类型")
    private Integer talkType;

    @ApiModelProperty("谈话人")
    private String talkUser;

    @ApiModelProperty("被谈话人")
    private String toTalkUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty("开始时间")
    private LocalDateTime beginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("是否提交 0：否，1：是")
    private Integer isSubmit;
}
