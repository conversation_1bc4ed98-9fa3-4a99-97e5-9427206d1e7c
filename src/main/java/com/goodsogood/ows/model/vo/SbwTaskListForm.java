package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 组织任务概要vo
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SbwTaskListForm {

    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("任务操作进度")
    private Integer type = 0;

    @ApiModelProperty("任务类型")
    private Integer taskType;

    @ApiModelProperty("任务题目")
    private String title;

    @ApiModelProperty("区县id")
    private Long regionId;

    @ApiModelProperty("组织id")
    private Long orgId;

    @ApiModelProperty("任务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("任务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("1：草稿，2：未开始，3：进行中，4：已结束")
    private Integer status;

    @ApiModelProperty("已提交数量")
    private Integer submitNum;

    @ApiModelProperty("已审核数量")
    private Integer verifyNum;

    @ApiModelProperty("未按时回复数量")
    private Integer unNum;

}
