package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @describe 基层述职评议结果统计查询返回参数
 * @date 2019-12-26
 */

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MeetingOrgDebriefReviewStatisticsQueryVO {

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("基础党组织数")
    @JsonProperty(value = "basis_org_num")
    private Integer basisOrgNum;

    @ApiModelProperty("参与组织数")
    @JsonProperty(value = "participate_org_num")
    private Integer participateOrgNum = 0;

    @ApiModelProperty("好审核组织数")
    @JsonProperty(value = "good_org_num")
    private Integer goodOrgNum = 0;

    @ApiModelProperty("较好审核组织数")
    @JsonProperty(value = "better_org_num")
    private Integer betterOrgNum = 0;

    @ApiModelProperty("一般审核组织数")
    @JsonProperty(value = "general_org_num")
    private Integer generalOrgNum = 0;

    @ApiModelProperty("差审核组织数")
    @JsonProperty(value = "differ_org_num")
    private Integer differOrgNum = 0;

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @Data
    public static class OrgDebriefReviewStatistics {

        private String orgLevel;

        private Integer rating;

        private Integer count;
    }

    @Data
    public static class OrgDebriefReviewStatisticsDTO {

        private List<Long> orgIds;

        @ApiModelProperty("数据状态 1 正常 2删除")
        private Integer status;

        @ApiModelProperty("评议年度")
        @JsonProperty(value = "review_year")
        private Integer reviewYear;
    }
}
