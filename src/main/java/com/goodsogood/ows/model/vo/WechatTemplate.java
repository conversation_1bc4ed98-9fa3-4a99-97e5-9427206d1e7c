package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * User: R
 * Date: 2018/7/30
 * Time: 16:55
 * Created with IntelliJ IDEA.
 */
@Data
public class WechatTemplate {
    @ApiModelProperty("用户openid")
    private String touser;
    
    @ApiModelProperty("模板ID")
    @JsonProperty("template_id")
    private String templateId;

    @ApiModelProperty("模板跳转链接")
    private String url;

    @ApiModelProperty("消息数据")
    private Map<String, TemplateData> data;
}
