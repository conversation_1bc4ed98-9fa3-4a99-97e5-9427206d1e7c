package com.goodsogood.ows.model.vo

import io.swagger.annotations.ApiModel
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.annotations.ApiModelProperty
import lombok.Data
import java.util.ArrayList
import javax.persistence.Column

/**
 * <AUTHOR>
 * @ClassName FindOrgListForm
 * @description
 * @date 2018-12-11 16:08
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
class FindOrgListForm {
    @JsonProperty("id_list")
    var idList: MutableList<Long> = mutableListOf()

    @JsonProperty("is_employee")
    var isEmployee: Int? = null

    @ApiModelProperty("是否过滤非党员 1-是，2-否，默认为2")
    @JsonProperty("is_filter")
    var isFilter = 2

    @ApiModelProperty("是否包含12371序列号为空，1-是，2-否，默认为2")
    @JsonProperty("is_include")
    var isInclude = 2

    @ApiModelProperty(value = "区县ID")
    @JsonProperty("region_id")
    var regionId: Long? = null

    @JsonProperty("org_type")
    var orgType: Int? = null

    @ApiModelProperty("是否查询行政单位党员 1-是，0-否，默认为0")
    @JsonProperty("is_unit")
    var isUnit = 0

    @ApiModelProperty("是否仅查询政治面貌为党员的数据 1-是 0-否，默认0\n")
    @JsonProperty("is_party")
    var isParty = 0
}