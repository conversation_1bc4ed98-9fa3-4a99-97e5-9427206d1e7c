package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.pagehelper.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 返回对象
 *
 * <AUTHOR>
 * @date 2019/4/25 14:05
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SasOrgLifeReponse {

    @JsonProperty("list_page")
    @ApiModelProperty(value = "看板列表")
    private Result<Page<StatisticalOrgLifeEntity>> resultPage;

    @JsonProperty("data_month")
    @ApiModelProperty(value = "统计月份数据")
    private StaMonth staMonth;
}
