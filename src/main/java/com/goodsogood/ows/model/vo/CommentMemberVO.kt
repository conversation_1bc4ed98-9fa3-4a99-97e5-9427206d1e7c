package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import org.hibernate.validator.constraints.Range
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotNull

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class CommentMemberVO {

    @ApiModelProperty("民主评议主键ID")
    @JsonProperty(value = "comment_id")
    @get:NotNull(message = "民主评议不能为空")
    var commentId: Long = 0

    @ApiModelProperty("姓名")
    @JsonProperty(value = "name")
    var name: String? = null

    @ApiModelProperty("手机号")
    @JsonProperty(value = "phone")
    var phone: String? = null

    @ApiModelProperty("自评等级")
    @JsonProperty(value = "self_rating")
    var selfRating: Int? = null

    @ApiModelProperty("综合评定等级")
    @JsonProperty(value = "complex_rating")
    var complexRating: Int? = null

    @ApiModelProperty("页码")
    @JsonProperty(value = "page")
    var page: Int = 1

    @ApiModelProperty("一页大小")
    @JsonProperty(value = "page_size")
    var pageSize: Int = 10

}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class CommentMemberResultForm {

    @ApiModelProperty("民主评议党员主键ID")
    @JsonProperty(value = "comment_member_id")
    var commentMemberId: Long? = null

    @ApiModelProperty("用户ID")
    @JsonProperty(value = "user_id")
    var userId: Long? = null

    @ApiModelProperty("用户姓名")
    @JsonProperty(value = "user_name")
    var userName: String? = null

    @ApiModelProperty("组织ID")
    @JsonProperty(value = "org_id")
    var orgId: Long? = null

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    var orgName: String? = null

    @ApiModelProperty("脱敏手机号")
    @JsonProperty(value = "phone")
    var phone: String? = null

    @ApiModelProperty("自评等级")
    @JsonProperty(value = "self_rating")
    var selfRating: Int? = 0

    @ApiModelProperty("是否存在互评")
    @JsonProperty(value = "exist_appraisal")
    var existAppraisal: Int? = null

    @ApiModelProperty("互评数据")
    @JsonProperty(value = "appraisal_data")
    var appraisalData: CommentStatisticalDataForm? = CommentStatisticalDataForm()

    @ApiModelProperty("综合评定等级")
    @JsonProperty(value = "complex_rating")
    var complexRating: Int = 0
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class CommentMemberForm {

    @ApiModelProperty("民主评议党员主键ID")
    @JsonProperty(value = "comment_member_id")
    var commentMemberId: Long? = null

    @ApiModelProperty("年度")
    @JsonProperty(value = "year")
    var year: Int? = null

    @ApiModelProperty("用户ID")
    @JsonProperty(value = "user_id")
    var userId: Long? = null

    @ApiModelProperty("用户姓名")
    @JsonProperty(value = "user_name")
    var userName: String? = null

    @ApiModelProperty("组织ID")
    @JsonProperty(value = "org_id")
    var orgId: Long? = null

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    var orgName: String? = null

    @ApiModelProperty("脱敏手机号")
    @JsonProperty(value = "phone")
    var phone: String? = null

    @ApiModelProperty("自评等级")
    @JsonProperty(value = "self_rating")
    @get:NotNull(message = "自评等级不能为空")
    var selfRating: Int? = 0

    @ApiModelProperty("自评内容")
    @JsonProperty(value = "self_content")
    @get:NotBlank(message = "自评内容不能为空")
    var selfContent: String? = null

    @ApiModelProperty("操作 1-保存，2-提交")
    @JsonProperty(value = "operate")
    @get:Range(min = 1, max = 2, message = "操作只能是1,2")
    var operate: Int? = 1

    @ApiModelProperty("是否存在互评")
    @JsonProperty(value = "exist_appraisal")
    var existAppraisal: Int? = null

    @ApiModelProperty("综合评定等级")
    @JsonProperty(value = "complex_rating")
    var complexRating: Int = 0

    @ApiModelProperty("是否是草稿")
    @JsonProperty(value = "is_draft")
    var isDraft: Int? = 0
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MobileCommentMemberVO {

    @ApiModelProperty("是否开启")
    @JsonProperty(value = "is_start")
    var isStart: Int = 0

    @ApiModelProperty("民主评议ID")
    @JsonProperty(value = "comment_id")
    var commentId: Long? = null

    @ApiModelProperty("是否可以编辑 0-不可以，1-可以")
    @JsonProperty(value = "is_edit")
    var isEdit: Int? = 0

    @ApiModelProperty("自评是否完成")
    @JsonProperty(value = "self_finish")
    var selfFinish: Int = 0

    @ApiModelProperty("互评是否完成")
    @JsonProperty(value = "appraisal_finish")
    var appraisalFinish: Int = 0
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
data class DealMemberVO(

    @ApiModelProperty("用户ID列表")
    @get:NotNull(message = "用户不能为空")
    var userIds : MutableList<Long> = mutableListOf(),

    @ApiModelProperty("民主评议ID")
    @get:NotNull(message = "民主评议不能为空")
    var commentId: Long = 0
) {
    override fun toString(): String {
        return "DealMemberVO(userIds=$userIds, commentId=$commentId)"
    }
}