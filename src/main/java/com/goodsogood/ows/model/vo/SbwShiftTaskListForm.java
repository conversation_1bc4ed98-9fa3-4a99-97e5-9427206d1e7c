package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SbwShiftTaskListForm {


    @ApiModelProperty("代办任务id")
    private Long shiftTaskId;

    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("任务题目")
    private String title;

    @ApiModelProperty("区县id")
    private Long regionId;

    @ApiModelProperty("组织id")
    private Long orgId;

    @ApiModelProperty("任务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("任务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("1：草稿，2：已提交，3：处理中，4：不予处理，5：已结束")
    private Integer status;

    @ApiModelProperty("已反馈数量")
    private Integer submitNum;

    @ApiModelProperty("已审核数量")
    private Integer verifyNum;

    @ApiModelProperty("未按时反馈")
    private Integer unNum;
}
