package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.model.db.MeetingLeaveEntity;
import com.goodsogood.ows.utils.BeanCopierUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.persistence.Column;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 添加请假
 *
 * <AUTHOR>
 * @create 2018/10/29 15:19
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingLeaveAddForm {

    @ApiModelProperty(value = "活动id", required = true)
    @JsonProperty(value = "meeting_id")
    @NotNull(message = "{NotNull.meetingLeave.meetingId}")
    private Long meetingId;

    @ApiModelProperty(value = "请假类型（1：因公请假 2：因私请假）", required = true)
    @JsonProperty(value = "type")
    @NotNull(message = "{NotNull.meetingLeave.type}")
    @Range(min = 1, max = 2, message = "{Range.meetingLeave.type}")
    private Short type;

    @ApiModelProperty(value = "请假原因", required = true)
    @JsonProperty(value = "reason")
    @NotEmpty(message = "{NotEmpty.meetingLeave.reason}")
    @Length(max = 200, message = "{Length.meetingLeave.reason}")
    private String reason;

//    @ApiModelProperty(value = "审批人", required = true)
//    @JsonProperty(value = "check_user_id")
//    @Column(name = "check_user_id")
//    @NotNull(message = "{NotNull.meetingLeave.checkUserId}")
//    private Long checkUserId;

    public MeetingLeaveEntity getMeetingLeave() throws Exception {
        MeetingLeaveEntity target = new MeetingLeaveEntity();
        BeanCopierUtils.copy(this, target);
        return target;
    }

}
