package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import org.hibernate.validator.constraints.Range
import javax.validation.constraints.Size

/**
 * <AUTHOR>
 * @date 2023/10/9
 * @description class MeetingWorkPointForm
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class MeetingWorkPointForm(
    // 当前登录的单位id
    var ownerId: Long? = null,
    // 单位名称
    var ownerName: String? = null,
    // 文件名称
    var fileName: String? = null,
    // 年度
    var year: Int? = null,
    // 上下半年 1.上半年。2.下半年
    @get: Range(min = 1, max = 2, message = "上下半年只能是上半年（1）或者下半年（2）")
    var halfYear: Int? = null,
    // 日期
    @get:Size(max = 2, message = "日期长度不能超过2")
    var publishTime: List<String?>? = null,
    // 上传状态, 1:已上传，0：未上传
    @get: Range(min = 0, max = 1, message = "上传状态只能是未上传（0）或者已上传（1）")
    var status: Int? = null,
)