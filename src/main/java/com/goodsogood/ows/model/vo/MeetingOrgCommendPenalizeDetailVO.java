package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @describe 查询组织奖惩信息返回参数
 * @date 2019-12-26
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class MeetingOrgCommendPenalizeDetailVO {

    @ApiModelProperty("组织id")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("批准日期")
    @JsonProperty(value = "ratify_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date ratifyTime;

    @ApiModelProperty("奖励或者惩罚类别")
    @JsonProperty(value = "category")
    private Integer category;

    @ApiModelProperty("奖励或者惩罚类别 翻译")
    @JsonProperty(value = "category_value")
    private String categoryValue;

    @ApiModelProperty("奖励或者惩罚级别")
    @JsonProperty(value = "level")
    private Integer level;

    @ApiModelProperty("奖励或者惩罚级别 翻译")
    @JsonProperty(value = "level_value")
    private String levelValue;

    @ApiModelProperty("奖励或者惩罚名称")
    @JsonProperty(value = "name")
    private Integer name;

    @ApiModelProperty("奖惩名称内容")
    @JsonProperty(value = "content")
    private String content;

    @ApiModelProperty("奖励或者惩罚原因")
    @JsonProperty(value = "reason")
    private Integer reason;

    @ApiModelProperty("奖励类型")
    @JsonProperty(value = "reward_type")
    private Integer rewardType;

    @ApiModelProperty("颁奖单位")
    @JsonProperty(value = "award_unit")
    private String awardUnit;

    @ApiModelProperty("相关文件")
    @JsonProperty(value = "related_file")
    private String relatedFile;

    @ApiModelProperty("荣誉图片")
    @JsonProperty(value = "honor_pic")
    private List<MeetingFileVO> honorPic;

    @ApiModelProperty("依据说明")
    @JsonProperty(value = "basis_description")
    private String basisDescription;

    @ApiModelProperty("附件")
    @JsonProperty(value = "attachment")
    private String attachment;

    @ApiModelProperty("1:奖励 2:惩罚")
    @JsonProperty(value = "type")
    private Integer type;

    @ApiModelProperty("文件")
    private List<MeetingFileVO> files;

    @ApiModelProperty("审核状态 1-待审核、2-审核通过、3-审核不通过")
    @JsonProperty(value = "approval_status")
    private Integer approvalStatus;

}
