package com.goodsogood.ows.model.vo

import io.swagger.annotations.ApiModel
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.annotations.ApiModelProperty
import com.fasterxml.jackson.annotation.JsonProperty
import lombok.Data

/**
 * t_meeting_file 文件实体类
 *
 * <AUTHOR>
 * @create 2020-01-06
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
class MeetingFileVO {

    @ApiModelProperty("文件ID")
    @JsonProperty(value = "file_id")
    var fileId: Long? = null

    @ApiModelProperty("附件名称")
    var name: String? = null

    @ApiModelProperty("附件路径")
    var path: String? = null

    @ApiModelProperty("文件原名称")
    @JsonProperty(value = "file_name")
    var fileName: String? = null

    @ApiModelProperty("文件大小（byte）")
    var size: Long? = null

    constructor()
    constructor(fileId: Long?, name: String?, path: String?, fileName: String?, size: Long?) {
        this.fileId = fileId
        this.name = name
        this.path = path
        this.fileName = fileName
        this.size = size
    }


}