package com.goodsogood.ows.model.vo

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import org.hibernate.validator.constraints.Range
import javax.validation.constraints.NotNull

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
class CommentMemberComplexAddForm {

    @ApiModelProperty(value = "民主评议党员表主键")
    @get: NotNull(message = "民主评议党员表主键不能为空")
    var commentMemberId: Long = 0

    @ApiModelProperty(value = "综合评定等级")
    @get:Range(min = 1, max = 4, message = "操作只能是1,2,3,4")
    var rating: Int? = null

    @ApiModelProperty(value = "意见建议")
    var suggestion: MutableList<String>? = mutableListOf()

    @ApiModelProperty(value = "附件")
    var attachment: MutableList<MeetingFileForm> = mutableListOf()

    constructor()

    constructor(
        commentMemberId: Long,
        rating: Int?,
        suggestion: MutableList<String>?,
        attachment: MutableList<MeetingFileForm>
    ) {
        this.commentMemberId = commentMemberId
        this.rating = rating
        this.suggestion = suggestion
        this.attachment = attachment
    }


}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
class CommentMemberComplexBatchAddForm {

    @ApiModelProperty(value = "民主评议党员表主键")
    @get: NotNull(message = "民主评议党员表主键不能为空")
    var commentMemberIds: MutableList<Long> = mutableListOf()

    @ApiModelProperty(value = "综合评定等级")
    @get:Range(min = 1, max = 4, message = "操作只能是1,2,3,4")
    var rating: Int? = null

    @ApiModelProperty(value = "意见建议")
    var suggestion: MutableList<String>? = mutableListOf()

    @ApiModelProperty(value = "附件")
    var attachment: MutableList<MeetingFileForm> = mutableListOf()
}

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy::class)
class MemberComplexInfoForm {

    @ApiModelProperty(value = "民主评议综合评定表主键")
    var commentMemberComplexId: Long? = null

    @ApiModelProperty(value = "民主评议党员表主键")
    var commentMemberId: Long? = null

    @ApiModelProperty(value = "用户ID")
    var userId: Long? = null

    @ApiModelProperty(value = "用户姓名")
    var userName: String? = null

    @ApiModelProperty(value = "手机脱敏")
    var phoneSecret: String? = null

    @ApiModelProperty(value = "年度")
    var year: Int? = null

    @ApiModelProperty(value = "评定等级")
    var rating: Int? = null

    @ApiModelProperty(value = "意见建议")
    var suggestion: MutableList<String>? = mutableListOf()

    @ApiModelProperty(value = "附件")
    var attachment: MutableList<MeetingFileVO> = mutableListOf()
}