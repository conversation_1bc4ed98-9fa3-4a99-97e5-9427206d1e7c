package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.List;

/**
 * 数据字典entity
 *
 * <AUTHOR>
 * @date 2018-03-29
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OptionForm {

    @ApiModelProperty("字典code")
    private String code;

    @ApiModelProperty("字典key")
    @JsonProperty("op_key")
    private Integer opKey;

    @ApiModelProperty("字典value")
    @JsonProperty("op_value")
    private String opValue;

    @ApiModelProperty("排序值")
    @JsonProperty("seq")
    private Integer seq;

    @ApiModelProperty("是否有子级")
    @JsonProperty("has_child")
    private Integer hasChild;

    @ApiModelProperty("下级列表")
    @JsonProperty("child_option")
    private List<OptionForm> childOption;
}
