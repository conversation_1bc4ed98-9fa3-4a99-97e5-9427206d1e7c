package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

/**
 * <p>Description: 校验状态 </p>
 *
 * <AUTHOR>
 * @version 2020/9/3 16:25
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopicSubmitPreconditionStatus {
    @ApiModelProperty("状态 1.正常 2.无权限 3.任务已撤销 4.任务已完成")
    private Integer status;

    /**
     * status=1.正常
     *
     * <p>status=2.暂无查看权限，请联系组织管理员
     *
     * <p>status=3.该任务已撤销
     *
     * <p>status=4.该任务已完成
     */
    @ApiModelProperty("提示信息")
    @JsonProperty(value = "err_msg")
    private String errMsg;


    public TopicSubmitPreconditionStatus(STATUS status) {
        this.status = status.getStatus();
        this.errMsg = status.getErrMsg();
    }

    @Getter
    public enum STATUS {
        /**
         * 状态码
         */
        OK(1, "正常"),
        NO_PERMISSION(2, "暂无查看权限，请联系组织管理员"),
        REVOKED(3, "该任务已撤销"),
        DONE(4, "任务已完成"),
        ERROR(999, "参数错误，请联系组织管理员");

        private final Integer status;

        private final String errMsg;

        STATUS(int status, String errMsg) {
            this.status = status;
            this.errMsg = errMsg;
        }
    }
}
