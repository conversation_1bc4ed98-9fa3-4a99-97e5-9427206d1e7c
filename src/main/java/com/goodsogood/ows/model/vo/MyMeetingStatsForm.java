package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 任务完成情况统计vo
 *
 * <AUTHOR>
 * @create 2018年11月9日 16:08:21
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MyMeetingStatsForm {

    @ApiModelProperty(value = "待举办的活动数")
    @JsonProperty("soon_start_count")
    private int soonStartCount;

    @ApiModelProperty(value = "进行中的活动数")
    @JsonProperty("on_going_count")
    private int onGoingCount;

    @ApiModelProperty(value = "已结束的活动数")
    @JsonProperty("end_count")
    private int endCount;
}
