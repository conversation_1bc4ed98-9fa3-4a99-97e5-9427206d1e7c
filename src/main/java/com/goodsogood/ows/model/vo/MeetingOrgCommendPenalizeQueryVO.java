package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @describe 查询组织奖惩信息返回参数
 * @date 2019-12-26
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingOrgCommendPenalizeQueryVO {

    @ApiModelProperty("主键id")
    @JsonProperty(value = "meeting_org_commend_penalize_id")
    private Long meetingOrgCommendPenalizeId;

    @ApiModelProperty("组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("批准日期")
    @JsonProperty(value = "ratify_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date ratifyTime;

    @ApiModelProperty("奖励或者惩罚类别")
    @JsonProperty(value = "category")
    private Integer category;

    @ApiModelProperty("奖励或者惩罚类别 翻译")
    @JsonProperty(value = "category_value")
    private String categoryValue;

    @ApiModelProperty("奖励或者惩罚级别")
    @JsonProperty(value = "level")
    private Integer level;

    @ApiModelProperty("奖励或者惩罚级别 翻译")
    @JsonProperty(value = "level_value")
    private String levelValue;

    @ApiModelProperty("奖励或者惩罚名称")
    @JsonProperty(value = "name")
    private Integer name;

    @ApiModelProperty("奖惩名称内容")
    @JsonProperty(value = "content")
    private String content;

    @ApiModelProperty("奖惩类型（1:奖励 2:惩罚）")
    @JsonProperty(value = "type")
    private Integer type;

    @ApiModelProperty("奖励或者惩罚名称 列表")
    @JsonProperty(value = "name_op_list")
    private List<Integer> nameOpList;

    @ApiModelProperty("奖励或者惩罚名称 翻译")
    @JsonProperty(value = "name_value")
    private String nameValue;

    @ApiModelProperty("依据说明")
    @JsonProperty(value = "basis_description")
    private String basisDescription;

    @ApiModelProperty("颁奖单位")
    @JsonProperty(value = "award_unit")
    private String awardUnit;

    @ApiModelProperty("相关文件")
    @JsonProperty(value = "related_file")
    private String relatedFile;

    @ApiModelProperty("文件列表")
    private List<MeetingFileVO> files;

    @ApiModelProperty("荣誉图片")
    @JsonProperty(value = "honor_pic")
    private List<MeetingFileVO> honorPic;

    @ApiModelProperty("审核状态 1-待审核、2-审核通过、3-审核不通过")
    @JsonProperty(value = "approval_status")
    private Integer approvalStatus;

}
