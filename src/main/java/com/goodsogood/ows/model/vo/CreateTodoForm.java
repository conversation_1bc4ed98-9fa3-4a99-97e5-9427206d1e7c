package com.goodsogood.ows.model.vo;

import com.aliyun.dingtalktodo_1_0.models.CreateTodoTaskRequest;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CreateTodoForm {
    @JsonProperty(value = "create_todo_task_request")
    private CreateTodoTaskRequest createTodoTaskRequest;

    @JsonProperty(value = "third_add_form")
    private ThirdAddForm thirdAddForm;
}
