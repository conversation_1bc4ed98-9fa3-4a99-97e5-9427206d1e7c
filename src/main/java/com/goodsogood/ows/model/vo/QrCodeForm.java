package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 二维码
 *
 * <AUTHOR>
 * @version 2021-09-06
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
public class QrCodeForm {

    @ApiModelProperty("活动编号")
    @JsonProperty(value = "meeting_id")
    private Long meetingId;

    @ApiModelProperty("二维码字符串")
    @JsonProperty(value = "qr_code")
    private String qrCode;

    @ApiModelProperty("二维码hash")
    @JsonProperty(value = "qr_code_hash")
    private String qrCodeHash;


    @ApiModelProperty("过期时间，单位毫秒")
    @JsonProperty(value = "dynamic_expire")
    private Long dynamicExpire;


    @ApiModelProperty("是否使用动态二维码. 0:不使用 1:使用")
    @JsonProperty(value = "is_dynamic")
    private Integer isDynamic;

}
