package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 党员奖惩实体类
 * @date 2019/12/27
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCommendPenalizeVO {

    @ApiModelProperty("id")
    @JsonProperty(value = "meeting_user_commend_penalize_id")
    private Long meetingUserCommendPenalizeId;

    @ApiModelProperty("党员用户ID")
    @JsonProperty(value = "user_id")
    private Long userId;

    @ApiModelProperty("党员用户姓名")
    @JsonProperty(value = "user_name")
    private String userName;

    @ApiModelProperty("党员用户身份证")
    @JsonProperty(value = "cert_number")
    private String certNumber;

    @ApiModelProperty("党员所属组织ID")
    @JsonProperty(value = "org_id")
    private Long orgId;

    @ApiModelProperty("党员所属组织名称")
    @JsonProperty(value = "org_name")
    private String orgName;

    @ApiModelProperty("生效日期")
    @JsonProperty(value = "effective_time")
    private String effectiveTime;

    @ApiModelProperty("奖惩类别")
    @JsonProperty(value = "category")
    private Integer category;

    @ApiModelProperty("奖惩类别 翻译")
    @JsonProperty(value = "category_value")
    private String categoryValue;

    @ApiModelProperty("奖惩级别")
    @JsonProperty(value = "level")
    private Integer level;

    @ApiModelProperty("奖惩级别 翻译")
    @JsonProperty(value = "level_value")
    private String levelValue;

    // 当级别为其他时，奖惩内容不能为空
    @ApiModelProperty("奖惩内容")
    @JsonProperty(value = "content")
    private String content;

    @ApiModelProperty("奖惩名称")
    @JsonProperty(value = "name")
    private Integer name;

    @ApiModelProperty("奖惩名称 翻译")
    @JsonProperty(value = "name_value")
    private String nameValue;

    @ApiModelProperty("奖励或者惩罚名称 列表")
    @JsonProperty(value = "name_op_list")
    private List<Integer> nameOpList;

    @ApiModelProperty("奖惩原因")
    @JsonProperty(value = "reason")
    private String reason;

    @ApiModelProperty("奖励类型(1-及时性表彰、2-定期集中性表彰)")
    @JsonProperty(value = "reward_type")
    private Integer rewardType;

    @ApiModelProperty("奖惩类型（1:奖励 2:惩罚）")
    @JsonProperty(value = "type")
    private Integer type;

    @ApiModelProperty("受奖批准机关名称")
    @JsonProperty(value = "office_name")
    private String officeName;

    @ApiModelProperty("受奖批准机关级别")
    @JsonProperty(value = "office_level")
    private Integer officeLevel;

    @ApiModelProperty("颁奖单位")
    @JsonProperty(value = "award_unit")
    private String awardUnit;

    @ApiModelProperty("相关文件")
    @JsonProperty(value = "related_file")
    private String relatedFile;

    @ApiModelProperty("荣誉图片")
    @JsonProperty(value = "honor_pic")
    private List<MeetingFileVO> honorPic;

    @ApiModelProperty("依据说明")
    @JsonProperty(value = "basis_description")
    private String basisDescription;

    @ApiModelProperty("文件")
    private List<MeetingFileVO> files;

    @ApiModelProperty("审核状态 1-待审核、2-审核通过、3-审核不通过")
    @JsonProperty(value = "approval_status")
    private Integer approvalStatus;

}
