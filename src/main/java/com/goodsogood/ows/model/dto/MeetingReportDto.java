package com.goodsogood.ows.model.dto;

import com.goodsogood.ows.model.db.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MeetingReportDto {

    @ApiModelProperty("序号")
    private Long number;

    @ApiModelProperty("活动名称")
    private String name;

    @ApiModelProperty("类别")
    private String category;

    @ApiModelProperty("活动类型")
    private String types;

    @ApiModelProperty("活动标签")
    private String label;

    @ApiModelProperty("所属组织名称")
    private String orgName;

    @ApiModelProperty("活动地点")
    private String address;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("活动总时长")
    private Double totalHours;

    @ApiModelProperty("理论学习时长")
    private Double theoryLearn;

    @ApiModelProperty("活动内容")
    private String content;

    @ApiModelProperty("主持人")
    private List<MeetingUserEntity> hostUser;

    @ApiModelProperty("记录人")
    private List<MeetingUserEntity> recordUser;

    @ApiModelProperty("参加人员")
    private List<MeetingUserEntity> participantUsers;

    @ApiModelProperty("列席人员")
    private List<MeetingUserEntity> attendUsers;

    @ApiModelProperty("签到方式。0：手写签到；1：扫码签到；3：GPS定位签到；4：人脸识别")
    private Short signInWay;

    @ApiModelProperty("签到信息")
    List<MeetingUserEntity> signList;

    @ApiModelProperty("议程")
    private List<MeetingAgendaEntity> agenda;

    @ApiModelProperty("会议提交时间")
    private Date submitTime;

    @ApiModelProperty("活动状态 1：发起审批中、2：发起未通过、3：活动待举办（待填报结果）、5：填报审查中（第一次）、6：填报未通过（第一次）、7：已提交、8：退回、9：活动已取消、10：填报审查中（第一次之后）、11：填报未通过（第一次之后）12：待复核（第二次提交并且审批通过）13：通过（第一次考核就通过）14：通过（第一次考核被退回后的通过）")
    private Short status;
}
