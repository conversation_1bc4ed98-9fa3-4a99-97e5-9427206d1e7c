package com.goodsogood.ows.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 补签列表
 * @date 2021-11-10
 */
@Data
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MeetingWaitSignVo implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long waitSignId;

    /**
     * meeting_id
     */

    private Long meetingId;

    /**
     * meeting_user_id
     */

    private Long meetingUserId;

    /**
     * user_id
     */

    private Long userId;

    /**
     * 是否有效 1:有效 0:无效
     */

    private Short isDel;

    /**
     * 数据状态 1:待补学 2:补学完成 3:超期补学 4:活动变更 5:草稿
     */

    private Short type;

    /**
     * 是否是该用户最新的补学记录 1:是 0:否
     */

    private Short isNow;

    /**
     * 补学内容
     */

    private String content;

    /**
     * 照片文件地址 英文逗号分隔
     */
    private  List imgFile;

    /**
     * 附件地址 英文逗号分隔
     */
    private List file;

    /**
     * create_time
     */

    private Date createTime;

    /**
     * update_time
     */

    private Date updateTime;


    private Date signTime;

    public MeetingWaitSignVo() {
    }

}