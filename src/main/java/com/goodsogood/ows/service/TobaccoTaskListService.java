package com.goodsogood.ows.service;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.TobaccoTaskOrgMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 烟草任务列表
 */
@Service
@Log4j2
public class TobaccoTaskListService {


    private final TobaccoTaskOrgMapper tobaccoTaskOrgMapper;

    @Autowired
    public TobaccoTaskListService(TobaccoTaskOrgMapper tobaccoTaskOrgMapper) {
        this.tobaccoTaskOrgMapper = tobaccoTaskOrgMapper;
    }

//    public List myTaskList(HeaderHelper.SysHeader header, Date beginTime,Date endTime,String key){
//    }
}
