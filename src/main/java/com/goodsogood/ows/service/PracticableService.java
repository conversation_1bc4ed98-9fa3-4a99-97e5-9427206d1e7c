package com.goodsogood.ows.service;

import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.PracticableMapper;
import com.goodsogood.ows.model.db.PracticableEntity;
import com.goodsogood.ows.model.vo.PracticableForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Log4j2
public class PracticableService {
private final PracticableMapper practicableMapper;

@Autowired
public PracticableService(PracticableMapper practicableMapper) {
    this.practicableMapper = practicableMapper;
}

    /**
     * 新增落实基层联系点制度
     * @param practicableEntity
     * @return
     */
    public Long addPracticable(PracticableEntity practicableEntity) {
        int ok = practicableMapper.insert(practicableEntity);
        log.debug("新增落实基层联系点制度成功");
        return (long) ok;
    }

    /**
     * 更新落实基层联系点制度
     * @param practicableEntity
     * @return
     */
    public Long updatePracticable(PracticableEntity practicableEntity) {
        if (practicableEntity.getPracticableId() == null){
            return -1L;
        }
        int ok = practicableMapper.updateByPrimaryKeySelective(practicableEntity);
        log.debug("更新落实基层联系点制度成功");
        return (long) ok;
    }

    /**
     * 删除落实基层联系点制度
     * @param practicableId
     * @return
     */
    public Integer deletePracticable(Long practicableId) {
        int ok = practicableMapper.deleteByPrimaryKey(practicableId);
        log.debug("删除落实基层联系点制度成功");
        return ok;
    }

    public List<PracticableEntity> listPracticable(HttpHeaders headers, PracticableForm practicableForm, Integer page, Integer pageSize) {
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        Long oid = sysHeader.getUoid() == null ? sysHeader.getOid() : sysHeader.getUoid();
        if (oid == 3) {
            return PageHelper.startPage(page,pageSize).doSelectPage(()->practicableMapper.listPracticable(practicableForm));
        }else {
            return PageHelper.startPage(page,pageSize).doSelectPage(()->practicableMapper.listPracticableUnitId(practicableForm));
        }
    }
}
