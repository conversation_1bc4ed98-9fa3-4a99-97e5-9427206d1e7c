package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.MeetingCanstant;
import com.goodsogood.ows.mapper.MeetingMapper;
import com.goodsogood.ows.mapper.TopicOrgMapper;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.MeetingTopicTaskListForm;
import com.goodsogood.ows.model.vo.TopicOrgForm;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-23 09:03
 **/
@Service
@Log4j2
public class TopicOrgService {

    private final TopicOrgMapper topicOrgMapper;
    private final MeetingMapper meetingMapper;
    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public TopicOrgService(TopicOrgMapper topicOrgMapper, MeetingMapper meetingMapper, StringRedisTemplate stringRedisTemplate) {
        this.topicOrgMapper = topicOrgMapper;
        this.meetingMapper = meetingMapper;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 根据任务返回该任务的所有组织
     *
     * @param topicId
     * @return
     */
    public List<TopicOrgEntity> getTopicOrgList(Long topicId,Integer taskStatus,String orgName) {
        if (topicId == null || topicId == 0) {
            return new ArrayList<>();
        }
        List<TopicOrgEntity> topicOrgEntityList = this.topicOrgMapper.findByTopicId(topicId, taskStatus, orgName);
        if (CollectionUtils.isNotEmpty(topicOrgEntityList)) {
            for (TopicOrgEntity topicOrgEntity : topicOrgEntityList) {
                List<String> ansCntList = new ArrayList<>();
                List<TopicLogFileEntity> files = new ArrayList<>();
                List<TopicLogEntity> topicLogList = topicOrgEntity.getTopicLogList();
                if (CollectionUtils.isNotEmpty(topicLogList)) {
                    for (TopicLogEntity topicLogEntity : topicLogList) {
                        if (StringUtils.isNotBlank(topicLogEntity.getAnsCnt())) {
                            ansCntList.add(topicLogEntity.getAnsCnt());
                        }
                        if (CollectionUtils.isNotEmpty(topicLogEntity.getFiles())) {
                            files.addAll(topicLogEntity.getFiles());
                        }
                    }
                }
                topicOrgEntity.setAnsCnts(StringUtils.join(ansCntList, ","));
                topicOrgEntity.setFiles(files);
                topicOrgEntity.setFileNames(
                        files.stream().map(TopicLogFileEntity::getFileName).collect(Collectors.joining(",")));
                topicOrgEntity.setTopicLogList(null);
            }
        }
        return topicOrgEntityList;
    }


    /**
     * 统计执行任务组织的总数量、已完成、未完成数量
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2019/11/12 2:20 下午
     */

    public TopicOrgForm sasTotal(Long topicId){
        TopicOrgForm orgForm = this.topicOrgMapper.sasTotalByTopicId(topicId);
        if (orgForm != null) {
            return orgForm;
        }
        return new TopicOrgForm();
    }

    /**
     * 删除任务派发的组织
     *
     * @param topicId
     * @param orgId
     */
    public void delTopicOrg(Long topicId, Long orgId) {
        Example example = new Example(TopicOrgEntity.class);
        example.createCriteria().andEqualTo("topicId", topicId)
                .andEqualTo("orgId", orgId);
        this.topicOrgMapper.deleteByExample(example);
    }


    /**
     * 查询任务完成情况
     */
    public List<TopicOrgForm> list(MeetingTopicTaskListForm meetingTopicTaskListForm) {
        return topicOrgMapper.list(meetingTopicTaskListForm);
    }

    /**
     * 查询任务完成情况 page
     */
    public Page<TopicOrgForm> page(MeetingTopicTaskListForm meetingTopicTaskListForm) {
        return PageHelper.startPage(meetingTopicTaskListForm.getPageBean().getPageNo(), meetingTopicTaskListForm.getPageBean().getPageSize())
                .doSelectPage(() -> topicOrgMapper.list(meetingTopicTaskListForm));
    }

    /**
     * 更新任务与组织的关联状态
     *
     * @param
     */
    public void updateAllTopicOrgStatus(long meetingId, Short stat) {
        MeetingEntity meetingEntity = meetingMapper.findByIdAndOrg(meetingId, null);
        if (meetingEntity != null && meetingEntity.getOrgId() != null && meetingEntity.getTopics() != null && !meetingEntity.getTopics().isEmpty()) {
            for (MeetingTopicEntity meetingTopicEntity : meetingEntity.getTopics()) {
                Example example = new Example(TopicOrgEntity.class);
                example.createCriteria().andEqualTo("topicId", meetingTopicEntity.getTopicId()).andEqualTo("orgId", meetingEntity.getOrgId());
                TopicOrgEntity entity = new TopicOrgEntity();
                if (stat.equals(MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue())) {// 提交
                    entity.setStatus(2);
                    this.topicOrgMapper.updateByExampleSelective(entity, example);
                } else if (stat.equals(MeetingCanstant.MEETING_STATUS_CANCEL.shortValue())) {// 取消活动
                    // 1.查询任务完成情况 有多少个已提交且未取消的会议
                    Set<Long> set = meetingMapper.findByOrgIdAndTopicIds(Collections.singletonList(meetingTopicEntity.getTopicId()), meetingEntity.getOrgId());
                    // 2.判断当前活动取消后是否还有其它已提交的活动，没有，任务更新为未完成
                    if (set == null || set.isEmpty() || (set.size() == 1 && set.contains(meetingId))) {
                        entity.setStatus(1);
                        this.topicOrgMapper.updateByExampleSelective(entity, example);
                    }
                }
            }
        }
    }
}
