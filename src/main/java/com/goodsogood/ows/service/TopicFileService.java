package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.TopicFileMapper;
import com.goodsogood.ows.model.db.TopicFileEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-22 14:05
 **/
@Service
@Log4j2
public class TopicFileService {

    private final TopicFileMapper topicFileMapper;

    @Autowired
    public TopicFileService(TopicFileMapper topicFileMapper) {
        this.topicFileMapper = topicFileMapper;
    }

    /**
     * 根据任务id获取任务的所有文件
     * @param topicId
     * @return
     */
    public List<TopicFileEntity> getTopicFileList(Long topicId) {
        if(topicId == null) {
            return new ArrayList<>();
        }
        return this.topicFileMapper.selectByTopicId(topicId);
    }

}