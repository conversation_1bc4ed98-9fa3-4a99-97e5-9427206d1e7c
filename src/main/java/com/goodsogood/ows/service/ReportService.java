package com.goodsogood.ows.service;

import cn.hutool.core.date.DateTime;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.mapper.MeetingTopicMapper;
import com.goodsogood.ows.mapper.ReportMapper;
import com.goodsogood.ows.mapper.SbwTaskTypeMapper;
import com.goodsogood.ows.model.db.ReportEntity;
import com.goodsogood.ows.model.vo.ReportForm;
import lombok.extern.log4j.Log4j2;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Service
@Log4j2
public class ReportService {

    private final ReportMapper reportMapper;

    @Autowired
    public ReportService(ReportMapper reportMapper) {
        this.reportMapper = reportMapper;
    }

    /**
     * 半年听取工作汇报新增
     */

    public Long addReport(ReportEntity reportEntity) {
        reportEntity.setCreateTime(LocalDateTime.now());
        reportEntity.setUpdateTime(LocalDateTime.now());
        int ok = reportMapper.insert(reportEntity);
        log.debug("半年听取工作汇报新增成功");
        return (long) ok;//保存成功
    }

    /**
     * 半年听取工作汇报修改
     */

    public Long updateReport(ReportEntity reportEntity) {
        reportEntity.setUpdateTime(LocalDateTime.now());
        int ok = reportMapper.updateByPrimaryKeySelective(reportEntity);
        log.debug("半年听取工作汇报修改成功");
        return (long) ok;//修改成功
    }

    /**
     * 半年听取工作汇报列表条件查询
     */

    public List<ReportEntity> listReport(ReportForm reportForm,Integer page,Integer pageSize){
       return PageHelper.startPage(page,pageSize).doSelectPage(()->reportMapper.listReport(reportForm));
    }

    /**
     *半年听取工作汇报删除
     */
    public Integer deleteReport(Long id) {
        var i = reportMapper.deleteByPrimaryKey(id);
        log.debug("半年听取工作汇报删除成功");
        return i;
    }

    /**
     * 新增半年听取意识形态工作
     */
    public Long addReportAwareness(ReportEntity reportEntity) {
        int ok = reportMapper.insert(reportEntity);
        log.debug("新增半年听取意识形态工作成功");
        return (long) ok;
    }

    /**
     * 修改半年听取意识形态工作
     */
    public Long changeReportAwareness(ReportEntity reportEntity) {
        int ok = reportMapper.updateByPrimaryKeySelective(reportEntity);
        log.debug("修改半年听取意识形态工作成功");
        return (long) ok;
    }
}
