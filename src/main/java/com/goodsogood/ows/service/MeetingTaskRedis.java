package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.common.RedisConstant;
import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.mapper.TypeMapper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2018-10-24 09:42
 * 活动任务缓存处理类
 **/
@Service
@Log4j2
public class MeetingTaskRedis {
    private static final ObjectMapper OBJECTMAPPER = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    static {
        OBJECTMAPPER.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        OBJECTMAPPER.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        OBJECTMAPPER.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        OBJECTMAPPER.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
    }

    private final StringRedisTemplate stringRedisTemplate;
    private final TypeMapper typeMapper;
    private final MeetingConfig meetingConfig;

    @Autowired
    public MeetingTaskRedis(StringRedisTemplate stringRedisTemplate, TypeMapper typeMapper, MeetingConfig meetingConfig) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.typeMapper = typeMapper;
        this.meetingConfig = meetingConfig;
    }


    /**
     * 添加缓存三会一课统计缓存
     */
    <T> void setSHYK(String key, T t) {
        this.set(key, t, meetingConfig.getMeetingTaskUndoneOrgStatsCacheTime(), TimeUnit.HOURS);
    }

    /**
     * 添加缓存
     */
    public <T> void set(String key, T t, long timeout, TimeUnit unit) {
        if (StringUtils.isBlank(key)) {
            log.debug("添加redis缓存失败！: key->{}", key);
            return;
        }
        try {
            String content = OBJECTMAPPER.writeValueAsString(t);
            log.debug("添加redis缓存: key->{};val->{}", key, content);
            this.stringRedisTemplate.opsForValue().set(key, content, timeout, unit);
        } catch (JsonProcessingException e) {
            log.error("添加缓存失败！", e);
        }
    }

    /**
     * 添加缓存
     */
    public <T> void set(String key, T t) {
        if (StringUtils.isBlank(key)) {
            log.debug("添加redis缓存失败: key->{}", key);
            return;
        }
        try {
            String content = OBJECTMAPPER.writeValueAsString(t);
            log.debug("添加redis缓存: key->{};val->{}", key, content);
            this.stringRedisTemplate.opsForValue().set(key, content);
        } catch (JsonProcessingException e) {
            log.error("添加缓存失败！", e);
        }
    }

    /**
     * 获取缓存
     */
    public <T> T get(String key, Class<T> clazz) {
        if (StringUtils.isBlank(key) || !hasKey(key)) {
            log.debug("获取redis缓存失败: key->{}", key);
            return null;
        }
        try {
            String content = this.stringRedisTemplate.opsForValue().get(key);
            log.debug("获取redis缓存: key->{};val->{}", key, content);
            return OBJECTMAPPER.readValue(content, clazz);
        } catch (IOException e) {
            log.error("获取缓存失败！", e);
        }
        return null;
    }

    /**
     * 判断key是否存在
     */
    private boolean hasKey(String key) {
        boolean hasKey = this.stringRedisTemplate.hasKey(key);
        log.debug("判断缓存是否存在: key->{};hasKey->{}", key, hasKey);
        return hasKey;
    }

    /**
     * 拼装key
     *
     * @param oid   组织id
     * @param month 当前月份
     * @return string
     */
    String keySHYK(Long oid, Integer month) {
        log.debug("三会一课拼装key,入参: oid->{};month->{}", oid, month);
        if (oid == null || month == null) {
            log.debug("三会一课拼装key失败！: oid->{};month->{};key->{};", oid, month, "");
            return "";
        } else {
            String key = RedisConstant.MEETING_TASK_UNDONE_ORG_STATS
                    .concat("_")
                    .concat(oid.toString())
                    .concat("_")
                    .concat(month.toString());
            log.debug("三会一课拼装key: oid->{};month->{};key->{};", oid, month, key);
            return key;
        }
    }

}