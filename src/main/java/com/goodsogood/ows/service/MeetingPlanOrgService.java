package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.MeetingOrgMapper;
import com.goodsogood.ows.model.db.MeetingOrgEntity;
import com.goodsogood.ows.model.vo.OrgForm;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-23 13:55
 **/
@Service
@Log4j2
public class MeetingPlanOrgService {
    private final MeetingOrgMapper meetingOrgMapper;

    @Autowired
    public MeetingPlanOrgService(MeetingOrgMapper meetingOrgMapper) {
        this.meetingOrgMapper = meetingOrgMapper;
    }

    /**
     * 更新执行组织 删除原有的组织 新增指定组织
     *
     * @param executeOrgs   List<OrgForm> 执行组织 为空表示清空组织
     * @param meetingPlanId 计划id
     */
    public void updateExcuteOrgs(List<OrgForm> executeOrgs, Long meetingPlanId) {
        if (meetingPlanId != null) {
            Example example = new Example(MeetingOrgEntity.class);
            example.createCriteria().andEqualTo("meetingPlanId", meetingPlanId);
            meetingOrgMapper.deleteByExample(example);
        } else { // 为null 什么也不做
            return;
        }

        if (executeOrgs != null && !executeOrgs.isEmpty()) {
            // 保存组织
            final ArrayList<MeetingOrgEntity> meetingOrgEntities = new ArrayList<>();
            executeOrgs.forEach(executeOrg -> {
                if (executeOrg.getOrgId() != null) {
                    MeetingOrgEntity meetingOrgEntity = new MeetingOrgEntity();
                    meetingOrgEntity.setMeetingPlanId(meetingPlanId);
                    meetingOrgEntity.setOrgId(executeOrg.getOrgId());
                    meetingOrgEntity.setOrgName(executeOrg.getOrgName());
                    meetingOrgEntities.add(meetingOrgEntity);
                }
            });
            meetingOrgMapper.insertList(meetingOrgEntities);
        }
    }

    /**
     * 更新执行组织名称
     *
     * @param orgId   组织id
     * @param orgName 组织名称
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateMeetingPlanOrgName(long orgId, String orgName) {
        if (StringUtils.isNotBlank(orgName)) {
            // 条件
            Example example = new Example(MeetingOrgEntity.class);
            example.createCriteria().andEqualTo("orgId", orgId);

            // 更新信息
            MeetingOrgEntity meetingOrgEntity = new MeetingOrgEntity();
            meetingOrgEntity.setOrgName(orgName);

            meetingOrgMapper.updateByExampleSelective(meetingOrgEntity, example);

        }
    }

}