package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.common.MeetingCanstant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组织生活会
 */

@Service
@Log4j2
public class OrgLifeService {

    private final OrgLifeMapper orglifeMapper;
    private final OrgLifeTagMapper orgLifeTagMapper;
    private final OrgLifeFileMapper orgLifeFileMapper;
    private final OrgLifeCheckMapper orgLifeCheckMapper;
    private final OrgLifeUploaderMapper orgLifeUploaderMapper;
    private final OrgLifeAdviceMapper orgLifeAdviceMapper;
    private final OrgLifeAsyncService asyncService;
    private final MeetingAgendaMapper meetingAgendaMapper;
    private final OrgLifeCommentMapper orgLifeCommentMapper;
    private final MeetingTalkService meetingTalkService;
    private final OrgLifeFileService orgLifeFileService;
    private final MeetingPeoplePartyLifeService meetingPeoplePartyLifeService;
    private final RestTemplateService restTemplateService;
    private final CommentFileService commentFileService;
    private final OrgLifeStudyMapper orgLifeStudyMapper;
    private final MeetingService service;
    private final MeetingMapper meetingMapper;
    private final MeetingRedisService meetingRedisService;
    private final ThirdService thirdService;
    private final OrgLifeNoticeMapper orgLifeNoticeMapper;
    private final OrgLifeMapper orgLifeMapper;



    private final Errors errors;

    public OrgLifeService(OrgLifeMapper orglifeMapper,
                          OrgLifeTagMapper orgLifeTagMapper,
                          OrgLifeFileMapper orgLifeFileMapper,
                          OrgLifeCheckMapper orgLifeCheckMapper,
                          OrgLifeUploaderMapper orgLifeUploaderMapper,
                          OrgLifeAdviceMapper orgLifeAdviceMapper,
                          OrgLifeAsyncService asyncService,
                          MeetingAgendaMapper meetingAgendaMapper,
                          OrgLifeCommentMapper orgLifeCommentMapper,
                          MeetingTalkService meetingTalkService,
                          OrgLifeFileService orgLifeFileService,
                          MeetingPeoplePartyLifeService meetingPeoplePartyLifeService,
                          RestTemplateService restTemplateService,
                          CommentFileService commentFileService,
                          OrgLifeStudyMapper orgLifeStudyMapper, MeetingService service, MeetingMapper meetingMapper, MeetingRedisService meetingRedisService, ThirdService thirdService, OrgLifeNoticeMapper orgLifeNoticeMapper, OrgLifeMapper orgLifeMapper, Errors errors) {
        this.orglifeMapper = orglifeMapper;
        this.orgLifeTagMapper = orgLifeTagMapper;
        this.orgLifeFileMapper = orgLifeFileMapper;
        this.orgLifeCheckMapper = orgLifeCheckMapper;
        this.orgLifeUploaderMapper = orgLifeUploaderMapper;
        this.orgLifeAdviceMapper = orgLifeAdviceMapper;
        this.asyncService = asyncService;
        this.meetingAgendaMapper = meetingAgendaMapper;
        this.orgLifeCommentMapper = orgLifeCommentMapper;
        this.meetingTalkService = meetingTalkService;
        this.orgLifeFileService = orgLifeFileService;
        this.meetingPeoplePartyLifeService = meetingPeoplePartyLifeService;
        this.restTemplateService = restTemplateService;
        this.commentFileService = commentFileService;
        this.orgLifeStudyMapper = orgLifeStudyMapper;
        this.service = service;
        this.meetingMapper = meetingMapper;
        this.meetingRedisService = meetingRedisService;
        this.thirdService = thirdService;
        this.orgLifeNoticeMapper = orgLifeNoticeMapper;
        this.orgLifeMapper = orgLifeMapper;
        this.errors = errors;
    }


    /**
     * 添加/编辑 组织生活会
     * @param form
     * @param headers
     * @return
     */
    public Long addOrEdit(OrgLifeAddEditForm form, HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        LocalDateTime time = LocalDateTime.now();
        OrgLifeEntity entity = new OrgLifeEntity();
        BeanUtils.copyProperties(form,entity);
        entity.setUpdateTime(time);
        entity.setLastChangeUser(header.getUserId());
        if (ObjectUtils.isEmpty(form.getLifeId())){ //添加
            log.debug("添加 组织生活，header -> [{}]",header);
            entity.setCreateUser(header.getUserId());
            entity.setRegionId(header.getRegionId());
            entity.setOrgId(header.getOid());
            entity.setOrgLevel(restTemplateService.getOrgLevel(header,header.getOid()));
            entity.setOrgName(header.getOrgName());
            entity.setCreateTime(time);
            entity.setIsDel(0);
            entity.setStatus(Constant.ORG_LIFE_NEW);
            int i = orglifeMapper.insertUseGeneratedKeys(entity);
            if (i > 0){
                log.debug("组织生活会 生成班子内部谈心谈话，lifeId -> [{}]，header -> [{}]",entity.getLifeId(),header);
                meetingTalkService.generaTalkData(header.getOid(),3,entity.getLifeId(),headers);
                log.debug("组织生活会 生成对照检察，lifeId -> [{}]，header -> [{}]",entity.getLifeId(),header);
                List<SessionUserForm> users = restTemplateService.getSessionUser(header);
                List<OrgLifeCheckEntity> checks = new ArrayList<>();
                if (!CollectionUtils.isEmpty(users)){
                    users.forEach(x -> {
                        OrgLifeCheckEntity check = new OrgLifeCheckEntity();
                        check.setLifeId(entity.getLifeId());
                        check.setUserId(x.getUserId());
                        check.setUsername(x.getUserName());
                        check.setOrgId(x.getOrgId());
                        check.setOrgName(x.getOrgName());
                        check.setStep(1);
                        check.setIsDel(0);
                        check.setCreateTime(time);
                        check.setUpdateTime(time);
                        check.setCreateUser(header.getUserId());
                        check.setLastChangeUser(header.getUserId());
                        checks.add(check);
                    });
                    orgLifeCheckMapper.insertList(checks);
                }
            }
        }else { //编辑
            log.debug("编辑 组织生活，lifeId -> [{}]，header -> [{}]",form.getLifeId(),header);
            orglifeMapper.updateByPrimaryKeySelective(entity);
        }
        return entity.getLifeId();
    }

    /**
     * 删除组织生活会
     * @param header
     * @param lifeId
     * @return
     */
    public Integer del(HeaderHelper.SysHeader header,Long lifeId){
        log.debug("删除 组织生活，lifeId -> [{}]，header -> [{}]",lifeId,header);
        Example example = new Example(OrgLifeEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId",lifeId)
                .andEqualTo("orgId",header.getOid())
                .andEqualTo("regionId",header.getRegionId());
        OrgLifeEntity entity = new OrgLifeEntity();
        entity.setIsDel(1);
        entity.setLastChangeUser(header.getUserId());
        entity.setUpdateTime(LocalDateTime.now());
        return orglifeMapper.updateByExampleSelective(entity,example);
    }


    /**
     * 删除组织生活会
     * @param headers
     * @param lifeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer delAll(HttpHeaders headers,Long lifeId){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("删除第三方任务开始");
        delTask(headers,lifeId);
        log.debug("删除第三方任务结束");
        //删除t_meeting_org_life_comment
        orgLifeCommentMapper.delByLifeId(lifeId);
        //删除t_meeting_org_life_tag
        orgLifeTagMapper.delByLifeId(lifeId);
        //删除t_meeting_org_life_notice
        orgLifeNoticeMapper.delByLifeId(lifeId);
        //删除t_meeting_org_advice
        orgLifeAdviceMapper.delByLifeId(lifeId);
        //删除t_meeting_org_life_study
        orgLifeStudyMapper.delByLifeId(lifeId);
        //删除t_meeting_org_check
        orgLifeCheckMapper.delByLifeId(lifeId);
        //删除t_meeting_org_uploader
        orgLifeUploaderMapper.delByLifeId(lifeId);
        //删除谈心谈话
        meetingTalkService.delBySource(2,lifeId,header);
        //删除t_meeting_org_life
        orglifeMapper.deleteByPrimaryKey(lifeId);
        //删除t_meeting_org_life_file
        orgLifeFileMapper.delByLifeId(lifeId);
        return 1;
    }


    public void delTask(HttpHeaders headers, Long lifeId){
        Example example = new Example(OrgLifeUploaderEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId",lifeId);
        example.selectProperties("type","dataId");
        List<OrgLifeUploaderEntity> list = orgLifeUploaderMapper.selectByExample(example);
        List<String> taskIds = new ArrayList<>();
        for(OrgLifeUploaderEntity uploader : list){
            String id = "" + lifeId + "_" + uploader.getType() + "_" + uploader.getDataId();
            String taskId = Constant.ORGTASKKEY + "_" + id;//组织生活会任务唯一标识
            log.debug("mt删除组织生活会任务："+taskId);
            taskIds.add(taskId);
        }
        if(CollectionUtils.isNotEmpty(taskIds)){
            thirdService.delPending(headers,taskIds);
        }
    }

    /**
     * 标签管理
     * @param header
     * @param form
     * @return
     */
    public Integer tagMange(HeaderHelper.SysHeader header, OrgLifeTagManageForm form){
        int data;
        LocalDateTime time = LocalDateTime.now();
        if (form.getFlag() == 1){ //增加标签
            log.debug("添加标签 ，lifeIds -> [{}]，tagIds -> [{}]，header -> [{}]",form.getLifeIds(),form.getTagIds(),header);
            List<OrgLifeTagEntity> list = new ArrayList<>();
            form.getLifeIds().forEach(x -> {
                form.getTagIds().forEach(y -> {
                    OrgLifeTagEntity entity = new OrgLifeTagEntity();
                    entity.setLifeId(x);
                    entity.setTagId(y);
                    entity.setCreateUser(header.getUserId());
                    entity.setCreateTime(time);
                    list.add(entity);
                });
            });
            data = orgLifeTagMapper.addTag(list);
        }else { //删除标签
            data = orgLifeTagMapper.delTag(form.getLifeIds(),form.getTagIds());
        }
        asyncService.multiUpdateId(form.getLifeIds(),header.getUserId(),time);
        return data;
    }

    /**
     * 组织生活会查询
     * @param findForm
     * @param page
     * @param pageSize
     * @return
     */
    public List<OrgLifeForm> list(OrgLifeFindForm findForm, Integer page, Integer pageSize){
        return PageHelper.startPage(page,pageSize).doSelectPage(() -> orglifeMapper.list(findForm));
    }

    /**
     * 组织生活会标题年度查询
     * @param lifeId
     * @return
     */
    public OrgLifeEntity lifeTitle(Long lifeId){
        Example example = new Example(OrgLifeEntity.class);
        example.selectProperties("lifeId","title","years").createCriteria().andEqualTo("lifeId",lifeId);
        OrgLifeEntity entity = orglifeMapper.selectOneByExample(example);
        return entity;
    }

    /**
     * 结束会议
     * @param headers
     * @param lifeId
     * @return
     */
    public Integer finish(Long lifeId,HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("结束 组织生活会 ，lifeId -> [{}]，header -> [{}]", lifeId, header);
        LocalDateTime time = LocalDateTime.now();
        Example example = new Example(OrgLifeEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId",lifeId)
                .andEqualTo("orgId",header.getOid())
                .andEqualTo("regionId",header.getRegionId())
                .andNotEqualTo("isDel",1);
        OrgLifeEntity entity = orglifeMapper.selectOneByExample(example);
        if (ObjectUtils.isEmpty(entity)){
            throw new ApiException("操作失败", new Result<>(errors, 3151, HttpStatus.INTERNAL_SERVER_ERROR.value(), "组织生活会不存在"));
        }
        if (Constant.ORG_LIFE_END <= entity.getStatus()){
            throw new ApiException("操作失败", new Result<>(errors, 3151, HttpStatus.INTERNAL_SERVER_ERROR.value(), "该组织生活会已结束"));
        }
        entity.setStatus(Constant.ORG_LIFE_END);
        entity.setLastChangeUser(header.getUserId());
        entity.setUpdateTime(time);
        int update = orglifeMapper.updateByPrimaryKeySelective(entity);
        if (update > 0){ //异步拷贝相关数据至会后
            asyncService.copyFile(lifeId,time);
            asyncService.copyOrgStudyFile(lifeId);
            asyncService.copyCheck(lifeId,time);
            asyncService.copyAdviceDirect(lifeId,time);
            asyncService.copyAdviceOther(lifeId,time);
            asyncService.copyTalk(headers,lifeId,header.getOid());
        }
        return update;
    }

    /**
     * 修改会议状态
     * @param lifeId
     * @param status
     * @param step
     * @param header
     */
    public void changeStatus(Long lifeId, Integer status, Integer step,HeaderHelper.SysHeader header){
        Integer status2;
        if (status.equals(Constant.ORG_LIFE_NEW) && step.equals(Constant.ORG_LIFE_STEP_BEFORE)){
            status2 = Constant.ORG_LIFE_READY;
        }else if (status.equals(Constant.ORG_LIFE_END) && step.equals(Constant.ORG_LIFE_STEP_AFTER)){
            status2 = Constant.ORG_LIFE_COMBING;
        } else {
            return;
        }
        log.debug("修改组织生活会状态，lifeId -> [{}]，status -> [{} -> {}]，header -> [{}]",lifeId,status,status2,header);
        OrgLifeEntity entity = new OrgLifeEntity();
        entity.setLifeId(lifeId);
        entity.setStatus(status2);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setLastChangeUser(header.getUserId());
        orglifeMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 只上传文件的模块回显查询
     * @param lifeId
     * @param step
     * @param modeType
     * @return
     */
    public OnlyOrgLifeFileForm OnlyLifeFileFind(Long lifeId, Integer step, List<Integer> modeType){
        OnlyOrgLifeFileForm form = new OnlyOrgLifeFileForm();
        form.setFiles(orgLifeFileMapper.onlyFile(lifeId,step,modeType));
        if (step != 2 || (modeType.size() == 1 && !modeType.contains(Constant.OrgModelType.PRE_OTHER.getModelId()))){
            form.setUploader(orgLifeUploaderMapper.uploader(lifeId,modeType));
        }
        form.setUploader("");
        return form;
    }

    /**
     * 查询对照检查材料
     * @param lifeId
     * @param step
     */
    public List<OrgLifeCheckForm> check(Long lifeId, Integer step){
        return orgLifeCheckMapper.check(lifeId,step);
    }

    /**
     * 征求意见
     * @param lifeId
     * @param step
     * @return
     */
    public OrgLifeAdviceForm advice(Long lifeId, Integer step){
        OrgLifeAdviceForm form = new OrgLifeAdviceForm();
        List<OrgLifeAdviceForm.Advice> advice = orgLifeAdviceMapper.adviceFind(lifeId,step);
        if (CollectionUtils.isNotEmpty(advice)){
            form.setAdvice(advice);
            for (OrgLifeAdviceForm.Advice a : advice){
                if (a.getAdviceType().equals(1)){
                    String mode = Constant.OrgModelType.ADVICE_DIRECT.getModelId().toString();
                    form.setFiles(orgLifeFileMapper.dataFile(a.getAdviceId(),step,mode));
                    break;
                }
            }
        }
        return form;
    }


    /**
     * 会中详情查询
     * @param lifeId
     * @return
     */
    public OrgLifeMeetingForm inTheMeeting(Long lifeId){
        //查询会议议程
        Example example = new Example(OrgLifeEntity.class);
        example.selectProperties("years","meetingId","lifeId").createCriteria().andEqualTo("lifeId",lifeId).andNotEqualTo("isDel",1);
        OrgLifeEntity life = orglifeMapper.selectOneByExample(example);
        if( ObjectUtils.isEmpty(life)) {
            log.error("未查询到组织生活会[{}]",lifeId);
            throw new ApiException("找不到",new Result<>(errors,9404,HttpStatus.INTERNAL_SERVER_ERROR.value(),"组织生活会"));
        }
        OrgLifeMeetingForm form = new OrgLifeMeetingForm();
        form.setLifeId(lifeId);
        form.setYears(life.getYears());
        if (ObjectUtils.isEmpty(life.getMeetingId())){
            form.setNoAgenda("（会前准备阶段，您还未发起组织生活会喔~）");
            form.setAgenda(new ArrayList<>(0));
        }else { //查询议程
            form.setNoAgenda("");
            form.setAgenda(meetingAgendaMapper.agendaTitle(life.getMeetingId()));
        }
        //查询会前附件
        form.setFiles(orgLifeFileService.queryFileInfo(lifeId,1));
        //查询民主评议
        List<Long> commentIds = orglifeMapper.orgLifeComment(lifeId);
        if (CollectionUtils.isNotEmpty(commentIds)){
            form.setComments(commentIds);
        }else {
            form.setComments(new ArrayList<>(0));
        }
        return form;
    }


    /**
     *
     * @param header
     * @param lifeId
     * @param step  当前页面 1:会前   2：会后
     * @param type  1：检视剖析  2 征求意见
     * @param dataIds
     * @param adviceType 1：直接上传，2：问卷调查，3：座谈会，4：个别访谈
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteSpecial( HeaderHelper.SysHeader header, HttpHeaders headers,Long lifeId, Integer step, Integer type, List<Long> dataIds, Integer adviceType) {
        Long userId = header.getUserId();
        if(type==1){
            orgLifeCheckMapper.multiDel(dataIds,userId);
            //附件也删掉
            orgLifeFileMapper.deleteByIds(lifeId,27,dataIds,userId);
            return;
        }
        if(type==2 && adviceType==1) {//直接上传
            log.debug("删除征求意见-直接上传");
            orgLifeFileService.deleteAttach(lifeId,dataIds, step,header);
            orgLifeFileMapper.deleteAdviceFile(lifeId,step);
            orgLifeAdviceMapper.oneDirectFile(lifeId,step);
            return;
        }
        if(CollectionUtils.isEmpty(dataIds)){
            log.debug("数据行id为空导致没有进行删除");
            return;
        }
        Long dataId = dataIds.get(0);//征求意见除直接上传外只能一个一个删除
        Long meetingId = queryAdvice(dataId);
       if(adviceType==4){//个别访谈
           //查询该行的talkid
            log.debug("删除征求意见-个别访谈");
            meetingTalkService.delMeetingTalk(meetingId,header);
           //删除关联关系
           orgLifeAdviceMapper.oneDel(dataId,adviceType);
           return;
        }
       if(adviceType==3 ||adviceType==2){
            if(adviceType==3){
                log.debug("调用王若宇方法删除meeting的座谈会开始_"+meetingId);
                try{
                    meetingPeoplePartyLifeService.deleteMeetingById(headers,header,meetingId);
                }catch (Exception e){
                    log.error("调用王若宇方法删除meeting的座谈会报错_"+meetingId);
                }
                log.debug("调用王若宇方法删除meeting的座谈会结束_"+meetingId);

            }
//            else{
//                log.debug("删除征求意见-问卷调查");
//                try {
//                    log.debug("调用活动中心进行问卷调查删除开始_"+meetingId);
//                   RemoteApiHelper.get(restTemplate, String.format("http://%s/activity/delete/%s/2", togServicesConf.getActivityPlat(), meetingId), headers,new TypeReference<Result<Object>>() {});
//                } catch (IOException e) {
//                    e.printStackTrace();
//                    log.error("调用活动中心进行问卷调查删除失败_"+meetingId+"_"+e);
//                }finally {
//                    log.debug("调用活动中心进行问卷调查删除结束_"+meetingId);
//                }
//            }
           //删除关联关系
           orgLifeAdviceMapper.oneDel(dataId,adviceType);
        }

    }

    private Long queryAdvice(Long dataId) {
        OrgLifeAdviceEntity advice = orgLifeAdviceMapper.selectByPrimaryKey(dataId);
        Long meetingId = advice.getDataId();
        return meetingId;
    }


    /**
     *
     * @param header
     * @param lifeId
     * @param step 1：会前，2：会后
     * @param leaders 班子成员
     */
    public void saveCheck(HeaderHelper.SysHeader header, Long lifeId, Integer step, List<LeaderForm> leaders) {
        List<OrgLifeCheckEntity> list = new ArrayList<>();
        for(LeaderForm leaderForm : leaders){
            OrgLifeCheckEntity lifeCheckEntity = new OrgLifeCheckEntity();
            lifeCheckEntity.setLifeId(lifeId);
            lifeCheckEntity.setUserId(leaderForm.getUserId());
            lifeCheckEntity.setUsername(leaderForm.getUsername());
            lifeCheckEntity.setOrgId(leaderForm.getOrgId());
            lifeCheckEntity.setOrgName(leaderForm.getOrgName());
            lifeCheckEntity.setStep(step);
            lifeCheckEntity.setIsDel(0);
            lifeCheckEntity.setCreateTime(LocalDateTime.now());
            lifeCheckEntity.setCreateUser(header.getUserId());
            lifeCheckEntity.setUpdateTime(LocalDateTime.now());
            lifeCheckEntity.setLastChangeUser(header.getUserId());
            list.add(lifeCheckEntity);
        }
        log.debug("插入支部班子与支部委员检视剖析数据");
        if(!CollectionUtils.isEmpty(list)){
            orgLifeCheckMapper.insertList(list);
        }
    }

    /**
     * 同步民主生活会相关组织信息
     * @param organizationBase
     * @return
     */
    public void updateOrgInfo(OrganizationBase organizationBase) {
         orglifeMapper.updateOrgInfo(organizationBase);
         orgLifeCheckMapper.updateOrgInfo(organizationBase);
    }

    /**
     * 关联民主评议
     * @param header
     * @param lifeId
     * @param commentId
     * @param flag
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String linkComment(HeaderHelper.SysHeader header,Long lifeId, Long commentId, Integer flag){
        if (ObjectUtils.isEmpty(commentId)) return "操作成功";
        LocalDateTime time = LocalDateTime.now();
        OrgLifeCommentEntity entity = new OrgLifeCommentEntity();
        entity.setCommentId(commentId);
        entity.setLifeId(lifeId);
        entity.setCreateTime(time);
        entity.setCreateUser(header.getUserId());
        Example example = new Example(OrgLifeCommentEntity.class);
        if (flag == 1){
            log.debug("关联民主评议：userId -> [{}]，lifeId -> [{}]，commentId -> [{}]",header.getUserId(),lifeId,commentId);
            //判断有无关联民主评议，删除已有关联
            example.createCriteria().andEqualTo("lifeId",lifeId);
            OrgLifeCommentEntity last  = orgLifeCommentMapper.selectOneByExample(example);
            if (!ObjectUtils.isEmpty(last)){
                orgLifeCommentMapper.deleteCommentLink(lifeId,last.getCommentId());
                orgLifeFileService.deleteCommentTemplate(lifeId,28,last.getCommentId(),header.getUserId());
            }
            orgLifeCommentMapper.insertUseGeneratedKeys(entity);
            log.debug("民主评议模板插入附件表");
           commentFileService.relateComment(commentId,lifeId,header);
        }else {
            log.debug("取消关联民主评议：userId -> [{}]，lifeId -> [{}]，commentId -> [{}]",header.getUserId(),lifeId,commentId);
            orgLifeCommentMapper.deleteCommentLink(lifeId,commentId);
            log.debug("民主评议模板从附件表删除");
            orgLifeFileService.deleteCommentTemplate(lifeId,28,commentId,header.getUserId());
        }
        return "操作成功";
    }

    /**
     * 关联活动
     * @param lifeId
     * @param meetingIds
     */
    public void relateMeeting(HttpHeaders headers, Long lifeId,  List<Long> meetingIds, Integer step) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        orgLifeStudyMapper.replaceOrInsert(lifeId,meetingIds,1,header.getUserId());
    }

    /**
     * 取消关联活动
     * @param lifeId
     * @param studyIds
     */
    public void quitRelateMeeting(Long lifeId, List<Long> studyIds) {
        Example example = new Example(OrgLifeStudyEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId", lifeId);
        criteria.andIn("studyId", studyIds);
        criteria.andEqualTo("hasDirectRelate",1);
        orgLifeStudyMapper.deleteByExample(example);
    }

    /**
     * 查询meeting活动列表-分页查询
     * @param lifeId
     * @param meetingListForm  relate_type 关联状态 null:所有  0:过滤掉被关联了的  1：活动发起的  2：直接关联的
     * @return
     */
    public Page<MeetingEntity> queryMeetingList(Long lifeId, MeetingListForm meetingListForm) {
        Page<MeetingEntity> page = null;
        List<Long> dbStudyIds = new ArrayList<>();//要被过滤掉的meetingid
        //根据lifeId 查询出已经被关联的meetingid
        List<OrgLifeStudyEntity> studies = getOrgLifeStudyEntities(lifeId);
        if(0 == meetingListForm.getRelateType()){//0:过滤掉被关联了的
            dbStudyIds = studies.stream().map(OrgLifeStudyEntity::getStudyId).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(dbStudyIds)){
                meetingListForm.setMeetingIds(dbStudyIds);
            }
            page = filterMeetingExsits(meetingListForm);
            return page;
        } else if(1 == meetingListForm.getRelateType()){//1：活动发起的
            meetingListForm.setRecordType(0);
            dbStudyIds = studies.stream().filter(study->study.getHasDirectRelate()==null || study.getHasDirectRelate()==0).map(OrgLifeStudyEntity::getStudyId).collect(Collectors.toList());
        }else if(2 == meetingListForm.getRelateType()){//直接关联的
            meetingListForm.setRecordType(1);
            dbStudyIds = studies.stream().filter(study->null!=study.getHasDirectRelate() && study.getHasDirectRelate()==1).map(OrgLifeStudyEntity::getStudyId).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(dbStudyIds)){
            return null;
        }
        meetingListForm.setMeetingIds(dbStudyIds);
        page = queryMeetingExsits(meetingListForm);
        return page;
    }


    //查询关联的 或者 直接录入的
    private Page<MeetingEntity> queryMeetingExsits(MeetingListForm meetingListForm) {
        Page<MeetingEntity> page = PageHelper.startPage(meetingListForm.getPageBean().getPageNo(), meetingListForm.getPageBean().getPageSize()).doSelectPage(
                ()->this.meetingMapper.findAllV4(meetingListForm));
        //查询活动状态更新时间
        updateMeetingPage(meetingListForm, page);
        return page;
    }


    //过滤掉已经关联了的
    private Page<MeetingEntity> filterMeetingExsits(MeetingListForm meetingListForm) {
        Page<MeetingEntity> page = PageHelper.startPage(meetingListForm.getPageBean().getPageNo(), meetingListForm.getPageBean().getPageSize()).doSelectPage(()->{
            this.meetingMapper.findAllV3(meetingListForm);
        });
        //查询活动状态更新时间
        updateMeetingPage(meetingListForm, page);
        return page;
    }

    //根据lifeId 查询出已经被关联的meetingid
    private List<OrgLifeStudyEntity> getOrgLifeStudyEntities(Long lifeId) {
        Example example = new Example(OrgLifeStudyEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId", lifeId);
        return orgLifeStudyMapper.selectByExample(example);
    }

    private void updateMeetingPage(MeetingListForm meetingListForm, Page<MeetingEntity> page) {
        meetingRedisService.setStatusUpdateTime(page);
        if (CollectionUtils.isNotEmpty(page)) {
            Date now = new Date();
            page.forEach(meetingEntity -> {
                        if (meetingListForm.getRecordType() == 0) {
                            if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_SOON_START.shortValue()) {
                                // start_time > 当前时间，状态为未开始
                                if (meetingEntity.getStartTime().after(now)) {
                                    meetingEntity.setStatus((short) -1);
                                }
                                //	start_time < 当前时间 && end_time > 当前时间，状态为进行中
                                if (meetingEntity.getStartTime().before(now) && (meetingEntity.getEndTime() == null || meetingEntity.getEndTime().after(now))) {
                                    meetingEntity.setStatus((short) -2);
                                }
                                //	end_time  < 当前时间，状态为已结束
                                if (meetingEntity.getEndTime() != null && meetingEntity.getEndTime().before(now)) {
                                    meetingEntity.setStatus((short) -3);
                                }
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_CANCEL.shortValue()) {
                                // status = 9，状态为已取消
                                meetingEntity.setStatus((short) -4);
                            } else {
                                // status = 其他值，状态为已结束
                                meetingEntity.setStatus((short) -3);
                            }
                        } else {
                            if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_SOON_START.shortValue()) {
                                meetingEntity.setStatus((short) -1);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_CANCEL.shortValue()) {
                                meetingEntity.setStatus((short) -2);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_SUBMIT_CHECK.shortValue()) {
                                meetingEntity.setStatus((short) -3);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_PASS_FIRST.shortValue()
                                    || meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_PASS_MORE.shortValue()) {
                                meetingEntity.setStatus((short) -4);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_BACK.shortValue()) {
                                meetingEntity.setStatus((short) -5);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue()) {
                                meetingEntity.setStatus((short) -6);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_REVOKE.shortValue()) {
                                meetingEntity.setStatus((short) -7);
                            }
                        }
                    }
            );
        }
    }

    //直接录入创建关联关系
    public void createRelation(OrgLifeEntity lifeEntity){
        orgLifeMapper.updateByPrimaryKeySelective(lifeEntity);
    }
}
