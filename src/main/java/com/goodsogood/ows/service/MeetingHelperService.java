package com.goodsogood.ows.service;

import com.goodsogood.ows.common.Config;
import com.goodsogood.ows.configuration.RabbitmqQueueConfig;
import com.goodsogood.ows.mapper.MeetingMapper;
import com.goodsogood.ows.mapper.MeetingUserMapper;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingUserEntity;
import com.goodsogood.ows.model.vo.MeetingScoreMQVo;
import com.goodsogood.ows.service.rabbitMQ.Producer;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName : MeetingHelperService
 * <AUTHOR> tc
 * @Date: 2022/3/3 18:46
 * @Description : 组织生活服务类助手类，用于需要事务处理或异步处理的方法调用
 */
@Service
@Log4j2
public class MeetingHelperService {

    private final MeetingUserMapper meetingUserMapper;
    private final MeetingMapper meetingMapper;
    private final Producer producer;
    private final RabbitmqQueueConfig rabbitmqQueueConfig;

    @Autowired
    public MeetingHelperService(MeetingUserMapper meetingUserMapper, MeetingMapper meetingMapper, Producer producer, RabbitmqQueueConfig rabbitmqQueueConfig) {
        this.meetingUserMapper = meetingUserMapper;
        this.meetingMapper = meetingMapper;
        this.producer = producer;
        this.rabbitmqQueueConfig = rabbitmqQueueConfig;
    }

    /**
     * 通过钉钉日程同步到的已签到状态，修改meeting和meeting_user表里对应的字段值
     * @param regionId
     * @param meetingId
     * @param userIdList
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMeetingSignStatusByDingEventSync(Long regionId, Long meetingId, List<Long> userIdList,Integer dingEventSync){
        //修改活动下人员的考勤状态，只修改状态为未签到和请假的人
        String [] st = "2,3,4".split(",");
        Example example = new Example(MeetingUserEntity.class);
        example.createCriteria()
                .andEqualTo("meetingId",meetingId)
                .andIn("signStatus", Arrays.asList(st))
                .andIn("userId",userIdList);
        MeetingUserEntity entity = new MeetingUserEntity();
        entity.setSignStatus((short) 1);
        //修改签到状态
        meetingUserMapper.updateByExampleSelective(entity,example);
        //dingEventSync为空表示前端页面通过按钮发起的同步日程签到状态请求，这种情况就不去更新自动更新状态
        if(dingEventSync!=null){
            //修改活动表，修改是否已获取钉钉日程的签到状态
            Example example2 = new Example(MeetingEntity.class);
            example2.createCriteria().andEqualTo("meetingId",meetingId);
            MeetingEntity entity2 = new MeetingEntity();
            entity2.setDingEventSync(1);
            //修改同步状态
            meetingMapper.updateByExampleSelective(entity2,example2);
        }
        //调用增加积分接口
        //获取完整的活动信息
//        MeetingEntity meetingInfo = meetingMapper.findByIdAndOrg(meetingId,null);
//        MeetingScoreMQVo msmq = new MeetingScoreMQVo(regionId,meetingInfo,false,null, Config.MeetingScoreConf.MQ_ADD_MEETING_SCORE);
//        producer.send(Config.RabbitmqConf.DIRECT_EXCHANGE_NAME,rabbitmqQueueConfig.getMeetingScoreRouting(), JsonUtils.toJson(msmq));
    }
}
