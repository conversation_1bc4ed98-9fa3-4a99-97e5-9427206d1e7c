package com.goodsogood.ows.service;

import com.alibaba.excel.util.FileUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.db.MeetingAgendaEntity;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingResultFileEntity;
import com.goodsogood.ows.model.db.MeetingUserEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.PackageUtil;
import com.goodsogood.ows.utils.word.ExportData;
import com.goodsogood.ows.utils.word.SoMap;
import com.goodsogood.ows.utils.word.WordUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 组织生活-模板导出
 */
@Service
@Log4j2
public class MeetingEventService {

    @Value("${file-path}")
    private String filePath;

    private final static SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    private final static SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日");

    private final MeetingService meetingService;
    private final RestTemplateService restTemplateService;
    private final StringRedisTemplate redisTemplate;
    private final Errors errors;
    private final TogServicesConfig togServicesConfig;
    private final RestTemplate restTemplate;


    public MeetingEventService(MeetingService meetingService, RestTemplateService restTemplateService, StringRedisTemplate redisTemplate, Errors errors, TogServicesConfig togServicesConfig, RestTemplate restTemplate) {
        this.meetingService = meetingService;
        this.restTemplateService = restTemplateService;
        this.redisTemplate = redisTemplate;
        this.errors = errors;
        this.togServicesConfig = togServicesConfig;
        this.restTemplate = restTemplate;
    }

    /**
     * 将文件打包返回zip流
     *
     * @param headers 头
     * @param ids     meeting_id
     * @param isEdit  是否是编辑时查询详情	0：不是（默认）；1：是。
     * @param flag    1:基本信息 2:签到表 3:活动记录表 4:活动附件 5:活动照片
     * @return
     */
    @Async("wordDownAsync")
    public void getStreamData(HttpHeaders headers, List<Long> ids, short isEdit, List<Integer> flag, String uuid) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        // 一级 获取本地目录 活动资料
        File stairDir = Paths.get(filePath, uuid).toFile();
        if (!stairDir.exists()) stairDir.mkdirs();
        String levelName = "活动材料";
        File dir = Paths.get(stairDir.getAbsolutePath(), levelName).toFile();
        if (!dir.exists()) dir.mkdirs();
        if (null != ids) {
            for (Long id : ids) {
                MeetingEntity entity;
                try {
                    entity = meetingService.detail(sysHeader, id, isEdit);
                } catch (Exception e) {
                    redisTemplate.opsForValue().set(uuid, "error", 1, TimeUnit.HOURS);
                    return;
                }
                // 二级 将文件储存在本地目录 活动名称 (日期)
                File path = Paths.get(dir.getAbsolutePath(),
                        entity.getName() + formatter.format(entity.getStartTime())).toFile();
                if (!path.exists()) path.mkdirs();

                // 三级 生成docx
                if (!CollectionUtils.isEmpty(flag)) {
                    for (Integer integer : flag) {
                        try {
                            save(headers, entity, integer, path.getAbsolutePath() + File.separator);
                        } catch (IOException e) {
                            e.fillInStackTrace();
                            log.error(e.getLocalizedMessage(), e);
                        }
                    }
                }
            }
        }
        Result<List<FileForm>> result = null;
        try {
            String zip = PackageUtil.generateFile(dir.getAbsolutePath(), "zip");
            //保存到redis
            File file = new File(zip);
            if (file.exists()) {
                result = RemoteApiHelper.uploadAndResult(
                        restTemplate,
                        togServicesConfig.getFileCenter(),
                        file,
                        file.getName(),
                        headers,
                        new TypeReference<Result<List<FileForm>>>() {
                        });
            } else {
                redisTemplate.opsForValue().set(uuid, "error", 1, TimeUnit.HOURS);
            }
            log.debug("result=>{}", result);
            if (!ObjectUtils.isEmpty(result)) {
                redisTemplate.opsForValue().set(uuid, JsonUtils.toJson(result.getData()), 1, TimeUnit.HOURS);
            } else {
                redisTemplate.opsForValue().set(uuid, "error", 1, TimeUnit.HOURS);
            }
        } catch (ApiException e) {
            redisTemplate.opsForValue().set(uuid, "error", 1, TimeUnit.HOURS);
            Result<?> errorResulet = e.getResult();
            if (null != errorResulet) {
                log.debug("活动附件上传失败=>{},{},{}",
                        errorResulet.getCode(),
                        errorResulet.getMessage(),
                        errorResulet.getException());
            } else {
                log.error("活动附件上传失败errorResulet为空", e);
            }
            throw e;
        } catch (Exception e) {
            redisTemplate.opsForValue().set(uuid, "error", 1, TimeUnit.HOURS);
            log.error("zip上传失败: " + e.getMessage() + "：" + result, e);
        }

        try {
            org.apache.tomcat.util.http.fileupload.FileUtils.deleteDirectory(new File(stairDir.getPath()));
        } catch (IOException e) {
            e.printStackTrace();
            log.error("删除文件目录失败=>: " + stairDir.getPath(), e);
        }
    }

    /**
     * 整合
     *
     * @param sysHeader
     * @param flag
     * @param filePath  D:/xxx/src/main/resources/file/${path}/
     * @throws IOException
     */
    public void save(HttpHeaders sysHeader, MeetingEntity entity, Integer flag, String filePath) throws IOException {
        switch (flag) {
            case 1:// 基本信息
                getBasicInformation1(entity, filePath);
                break;
            case 2:// 签到表
                getSignInForm(entity, filePath);
                break;
            case 3:// 活动议程
                getActivationRecord(entity, filePath);
                break;
            case 4:// 活动附件
                log.debug("开始导出活动附件=>{}", filePath);
                getResultFile(sysHeader, entity, filePath);
                break;
            case 5:// 活动照片
                getResultImg(sysHeader, entity, filePath);
                break;
        }
    }

    /**
     * 生成基本信息导出模板(党课类型)
     *
     * @param entity 组织生活详情
     */
    public void getBasicInformation1(MeetingEntity entity, String dirPath) {
        MeetingEventForm.BasicInformation form = getBasicInformation(entity);
        //模板地址
        String[] titles = entity.getTypes().split("，");
        if (titles.length > 1) {
            for (String s : titles) {
                form.setTypes(s);
                saveFile(form, s, dirPath);
            }
        } else {
            saveFile(form, entity.getTypes(), dirPath);
        }
    }

    /**
     * 生成基本信息
     *
     * @param entity 操作实体
     * @return
     */
    public MeetingEventForm.BasicInformation getBasicInformation(MeetingEntity entity) {
        Map<Integer, Integer> numbers = getSignNumber(entity);//活动签到人数统计
        String hostUserName = entity.getHostUser().stream().map(MeetingUserEntity::getUserName).collect(Collectors.joining("、"));
        String recordUserName = entity.getRecordUser().stream().map(MeetingUserEntity::getUserName).collect(Collectors.joining("、"));
        String lecturersName = entity.getLecturers().stream().map(MeetingUserEntity::getUserName).collect(Collectors.joining("、"));
        String participantUserNames = entity.getParticipantUsers().stream().map(MeetingUserEntity::getUserName).collect(Collectors.joining("、"));
        String attendUserNames = entity.getAttendUsers().stream().map(MeetingUserEntity::getUserName).collect(Collectors.joining("、"));
        MeetingEventForm.BasicInformation form = new MeetingEventForm.BasicInformation();
        // 活动议程
        MeetingEventForm.ActivationRecord record = new MeetingEventForm.ActivationRecord();
        StringBuilder builder = new StringBuilder();
        if (!CollectionUtils.isEmpty(entity.getAgenda())) {
            List<MeetingAgendaEntity> entities = entity.getAgenda();
            for (int i = 0, j = 1; i < entities.size(); i++, j++) {
                record.setAgendaTitle(entities.get(i).getAgendaTitle());
                builder.append(num2Chinese(j)).append("、").append(record.getAgendaTitle()).append("\n");//生成中文序号（ 一、活动标题）
            }
        }
        // 基本信息
        double totalHours = 0;
        if (entity.getEndTime() != null && entity.getStartTime() != null) {
            DecimalFormat df = new java.text.DecimalFormat("#.0");
            totalHours = (double) (entity.getEndTime().getTime() - entity.getStartTime().getTime()) / (1000 * 60 * 60);//活动总时长
            totalHours = Double.parseDouble(df.format(totalHours));
            entity.setTotalHours(totalHours);
        }
        if (!ObjectUtils.isEmpty(entity)) {
            form.setTypes(entity.getTypes());
            form.setName(entity.getName());
            form.setAgendaTitles(builder.toString());
            form.setStartTime(formatter1.format(entity.getStartTime()));
            form.setEndTime(entity.getEndTime() == null ? "" : formatter1.format(entity.getEndTime()));
            form.setTotalHours(entity.getTotalHours() == null ? "" : totalHours + "小时");
            form.setTheoryLearn(entity.getTheoryLearn() == null ? "" : entity.getTheoryLearn().toString() + "小时");
            form.setAddress(entity.getAddress());
            form.setHostUserName(hostUserName);
            form.setRecordUserName(recordUserName);
            form.setLecturersName(lecturersName);
            form.setLectureTitle(entity.getLectureTitle() == null ? "" : entity.getLectureTitle());
            form.setNumber1(numbers.get(1));
            form.setNumber2(numbers.get(2));
            form.setNumber3(numbers.get(3));
            form.setNumber4(numbers.get(4));
            form.setNumber5(numbers.get(5));
            form.setParticipantUserNames(participantUserNames);
            form.setAttendUserNames(attendUserNames);//基本信息:活动类型
        }
        return form;
    }

    /**
     * 保存基本信息文件
     *
     * @param form    模板参数
     * @param s       表格标题(活动类型)
     * @param dirPath 储存地址父级目录/${file}
     */
    public void saveFile(MeetingEventForm.BasicInformation form, String s, String dirPath) {
        ExportData evaluation;
        String fileName;
        if (s.contains("党课")) {
            evaluation = WordUtil.createExportData("file/model_one.doc");//模板
        } else {
            evaluation = WordUtil.createExportData("file/model_two.doc");//模板
        }
        fileName = Paths.get(dirPath, "01-基本信息：" + s + ".doc").toString();
        evaluation.setData("model", new SoMap(form));
        // 获取新生成的文件流
        byte[] data = evaluation.getByteArr();
        // 可以直接写入本地的文件
        try (FileOutputStream fos = new FileOutputStream(fileName)) {
            fos.write(data, 0, data.length);
        } catch (IOException ex) {
            log.error(ex.getMessage());
        }
    }

    /**
     * 获取活动签到人数
     *
     * @param entity
     * @return
     */
    public Map<Integer, Integer> getSignNumber(MeetingEntity entity) {
        List<MeetingUserEntity> userEntities = new ArrayList<>(entity.getParticipantUsers());
        if (!CollectionUtils.isEmpty(entity.getAttendUsers())) {
            userEntities.addAll(entity.getAttendUsers());
        }
        Map<Integer, Integer> map = new HashMap<>(4);
        if (!CollectionUtils.isEmpty(userEntities)) {
            // 应到人数
            Integer num1 = userEntities.size();
            // 实到人数
            Integer num2 = (int) userEntities.stream().filter(MeetingUserEntity -> MeetingUserEntity.getSignStatus() == 1).count();
            // 未签到人数
            Integer num4 = (int) userEntities.stream().filter(MeetingUserEntity -> MeetingUserEntity.getSignStatus() == 2).count();
            //补学人数
            Integer num5 = (int) userEntities.stream().filter(MeetingUserEntity -> MeetingUserEntity.getSignStatus() == 6).count();
            // 请假人数
            Integer num3 = num1 - num2 - num4 - num5;
            map.put(1, num1);
            map.put(2, num2);
            map.put(3, num3);
            map.put(4, num4);
            map.put(5, num5);
        } else {
            map.put(1, 0);
            map.put(2, 0);
            map.put(3, 0);
            map.put(4, 0);
            map.put(5, 0);
        }
        return map;
    }


    /**
     * 签到表模板
     *
     * @param entity
     * @param filePath src/main/resources/file/${目录}/
     * @return
     */
    public void getSignInForm(MeetingEntity entity, String filePath) {
        List<SoMap> list = getSignIn(entity);
        if (!CollectionUtils.isEmpty(list)) {
            MeetingEventForm.BasicInformation form = new MeetingEventForm.BasicInformation();
            form.setName(entity.getName());
            form.setStartTime(formatter1.format(entity.getStartTime()));
            ExportData evaluation = WordUtil.createExportData("file/model_four.doc");//模板
            evaluation.setData("model", form);
            evaluation.setTable("list", list);
            // 获取新生成的文件流
            byte[] data = evaluation.getByteArr();
            // 可以直接写入本地的文件
            String fileName = Paths.get(filePath, "02-签到表.doc").toString();
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                fos.write(data, 0, data.length);
            } catch (IOException ex) {
                log.error(ex.getMessage());
            }
        }
    }

    /**
     * 获取签到表信息
     *
     * @param entity
     * @return
     */
    public List<SoMap> getSignIn(MeetingEntity entity) {
        List<SoMap> list = new ArrayList<>();
        List<MeetingUserEntity> meetingUserEntities = new ArrayList<>(entity.getParticipantUsers());
        if (!CollectionUtils.isEmpty(entity.getAttendUsers())) {
            meetingUserEntities.addAll(entity.getAttendUsers());
        }
        if (!CollectionUtils.isEmpty(meetingUserEntities)) {
            for (int i = 0, j = 1; i < meetingUserEntities.size(); i++, j++) {
                MeetingEventForm.SignInForm form = new MeetingEventForm.SignInForm();
                form.setNumber(j);
                form.setUserName(meetingUserEntities.get(i).getUserName());
                form.setSignStatus(Constant.SignCase.getSignCase(meetingUserEntities.get(i).getSignStatus()));
                list.add(new SoMap(form));
            }
        }
        return list;
    }

    /**
     * 活动记录模板 (议程记录)
     *
     * @param entity
     * @param filePath src/main/resources/file/${目录}/
     */
    public void getActivationRecord(MeetingEntity entity, String filePath) {
        List<SoMap> list = new ArrayList<>();
        MeetingEventForm.ActivationRecord record;
        // 活动记录list
        if (!ObjectUtils.isEmpty(entity) && !CollectionUtils.isEmpty(entity.getAgenda())) {
            List<MeetingAgendaEntity> entities = entity.getAgenda();
            for (int i = 0, j = 1; i < entities.size(); i++, j++) {
                record = new MeetingEventForm.ActivationRecord();
                BeanUtils.copyProperties(entities.get(i), record);
                record.setAgendaTitle(num2Chinese(j) + "、" + record.getAgendaTitle());//生成中文序号（ 一、活动标题）
                record.setAgendaContent(entities.get(i).getAgendaContent() == null ? "\n" : entities.get(i).getAgendaContent() + "\n");
                list.add(new SoMap(record));
            }
            // 活动基本信息
            MeetingEventForm.BasicInformation form = getBasicInformation(entity);
            if (!StringUtils.isEmpty(form.getTypes())){
                if (form.getTypes().contains("，")){
                    List<String> strList = Arrays.asList(form.getTypes().split("，"));
                    form.setTypes(String.join("、", strList));
                }
            }
            // 活动签到表
            List<SoMap> list1 = getSignIn(entity);
            // 整合
            if (!CollectionUtils.isEmpty(list)) {
                //调用模板
                record = new MeetingEventForm.ActivationRecord();
                record.setName(entity.getName());
                ExportData evaluation = getExportData(entity.getTypes());
                evaluation.setData("model", form);//活动基本信息
                evaluation.setTable("list", list);//活动记录
                evaluation.setTable("list1", list1);//活动签到表
                // 获取新生成的文件流
                byte[] data = evaluation.getByteArr();
                // 可以直接写入本地的文件
                String fileName = Paths.get(filePath, "03-活动纪要.doc").toString();
                try (FileOutputStream fos = new FileOutputStream(fileName)) {
                    fos.write(data, 0, data.length);
                } catch (IOException ex) {
                    throw new RuntimeException(ex.getMessage());
                }
            }
        }
    }

    /**
     * 获取活动类型
     * @param types 活动类型字符串
     * @return
     */
    public ExportData getExportData(String types){
        String[] titles = types.split("，");
        List<String> typeList = Arrays.asList(titles);
        ExportData evaluation;
        if (typeList.contains("党课")){
            evaluation = WordUtil.createExportData("file/model_five.doc");
        }else {
            evaluation = WordUtil.createExportData("file/model_three.doc");
        }
        return evaluation;
    }

    /**
     * 中文序号
     *
     * @param section
     * @return
     */
    private static String num2Chinese(int section) {
        if (section >= 10 && section < 20)
            return "十" + num2Chinese(section % 10);
        String[] chnNumChar = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String[] chnUnitChar = {"", "十", "百", "千"};
        StringBuilder chnStr = new StringBuilder();
        StringBuilder strIns = new StringBuilder();
        int unitPos = 0;
        boolean zero = true;
        while (section > 0) {
            int v = section % 10;
            if (v == 0) {
                if (!zero) {
                    zero = true;
                    chnStr.append(chnNumChar[v]).append(chnStr);
                }
            } else {
                zero = false;
                strIns.delete(0, strIns.length());
                strIns.append(chnNumChar[v]);
                strIns.append(chnUnitChar[unitPos]);
                chnStr.insert(0, strIns);
            }
            unitPos++;
            section = (int) Math.floor(section / 10f);
        }
        return chnStr.toString();
    }

    /**
     * 获取该活动里面的附件
     *
     * @param headers
     * @param filePath src/main/resources/file/${目录}/
     */
    public void getResultFile(HttpHeaders headers, MeetingEntity entity, String filePath) {
        List<MeetingResultFileEntity> resultFiles = entity.getResultFiles();
        if (!CollectionUtils.isEmpty(resultFiles)) {
            resultFiles = resultFiles.stream()
                    .filter(MeetingResultFileEntity -> MeetingResultFileEntity.getType() == 1)
                    .collect(Collectors.toList());
            //调用文件中心 将附件存在本地目录
            for (MeetingResultFileEntity files : resultFiles) {
                String fileName = "04-" + files.getFileName();
                log.debug("导入活动附件地址=>{}", filePath);
                restTemplateService.getFileInputStream(headers, files.getId(), filePath, fileName);
            }
        }
    }

    /**
     * 组织生活照片模板
     *
     * @param entity
     * @param filePath src/main/resources/file/${目录}/
     */
    public void getResultImg(HttpHeaders headers, MeetingEntity entity, String filePath) {
        List<MeetingResultFileEntity> resultFiles = entity.getResultFiles();
        if (!CollectionUtils.isEmpty(resultFiles)) {
            resultFiles = resultFiles.stream()
                    .filter(MeetingResultFileEntity -> MeetingResultFileEntity.getType() == 0)
                    .collect(Collectors.toList());
            //调用文件中心 将附件存在本地目录
            for (MeetingResultFileEntity files : resultFiles) {
                log.debug("开始导出活动回顾附件1=>{}", filePath);
                String fileName = "05-" + files.getFileName();
                restTemplateService.getFileInputStream(headers, files.getId(), filePath, fileName);
            }
        }
    }

    /**
     * 生成签到表
     *
     * @return
     */
    public List<FileForm> getSignInInputStream(HttpHeaders headers, Long meetingId, short isEdit) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        MeetingEntity entity = meetingService.detail(sysHeader, meetingId, isEdit);
        List<SoMap> list = new ArrayList<>();
        List<MeetingUserEntity> meetingUserEntities = new ArrayList<>(entity.getParticipantUsers());
        if (!CollectionUtils.isEmpty(entity.getAttendUsers())) {
            meetingUserEntities.addAll(entity.getAttendUsers());
        }
        if (!CollectionUtils.isEmpty(meetingUserEntities)) {
            for (int i = 0, j = 1; i < meetingUserEntities.size(); i++, j++) {
                MeetingEventForm.SignInForm form = new MeetingEventForm.SignInForm();
                form.setNumber(j);
                form.setUserName(meetingUserEntities.get(i).getUserName());
                form.setSignStatus(Constant.SignCase.getSignCase(meetingUserEntities.get(i).getSignStatus()));
                list.add(new SoMap(form));
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            MeetingEventForm.BasicInformation form = new MeetingEventForm.BasicInformation();
            form.setName(entity.getName());
            form.setStartTime(formatter1.format(entity.getStartTime()));
            ExportData evaluation = WordUtil.createExportData("file/model_four.doc");//模板
            evaluation.setData("model", form);
            evaluation.setTable("list", list);
            // 获取新生成的文件流
            byte[] data = evaluation.getByteArr();
            // 可以直接写入本地的文件
            String fileNamePath = Paths.get(filePath, entity.getName() + "签到表.doc").toString();
            String fileName = entity.getName() + "签到表.doc";
            try (FileOutputStream fos = new FileOutputStream(fileNamePath)) {
                fos.write(data, 0, data.length);
                File file = new File(fileNamePath);
                fos.close();
                Result<List<FileForm>> result = RemoteApiHelper.uploadAndResult(
                        restTemplate,
                        togServicesConfig.getFileCenter(),
                        file,
                        fileName,
                        headers,
                        new TypeReference<Result<List<FileForm>>>() {
                        });
                FileUtils.delete(new File(fileNamePath));
                return result.getData();
            } catch (IOException ex) {
                FileUtils.delete(new File(fileNamePath));
                ex.fillInStackTrace();
                log.error(ex.getMessage());
                // 删除生成的文件
            }
        }
        throw new ApiException("未查询到活动详情", new Result<>(errors, 2005, HttpStatus.BAD_REQUEST.value()));
    }
}
