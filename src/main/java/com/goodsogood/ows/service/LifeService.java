package com.goodsogood.ows.service;

import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 民主生活会
 */

@Service
@Log4j2
public class LifeService {

    private final LifeMapper lifeMapper;
    private final LifeTagMapper lifeTagMapper;
    private final LifeCheckMapper lifeCheckMapper;
    private final LifeUploaderMapper lifeUploaderMapper;
    private final LifeFileMapper lifeFileMapper;
    private final LifeAdviceMapper lifeAdviceMapper;
    private final MeetingAgendaMapper meetingAgendaMapper;
    private final LifeFileService lifeFileService;
    private final MeetingTalkService meetingTalkService;
    private final LifeAsyncService asyncService;
    private final MeetingPeoplePartyLifeService meetingPeoplePartyLifeService;
    private final RestTemplateService restTemplateService;
    private final RestTemplate restTemplate;
    private final TogServicesConfig togServicesConf;
    private final Errors errors;
    private final ThirdService thirdService;
    private final LifeNoticeMapper lifeNoticeMapper;
    private final LifeStudyMapper lifeStudyMapper;

    public LifeService(LifeMapper lifeMapper,
                       LifeTagMapper lifeTagMapper,
                       LifeCheckMapper lifeCheckMapper,
                       LifeUploaderMapper lifeUploaderMapper,
                       LifeFileMapper lifeFileMapper,
                       LifeAdviceMapper lifeAdviceMapper,
                       MeetingAgendaMapper meetingAgendaMapper,
                       LifeFileService lifeFileService,
                       MeetingTalkService meetingTalkService,
                       LifeAsyncService asyncService,
                       MeetingPeoplePartyLifeService meetingPeoplePartyLifeService,
                       RestTemplateService restTemplateService, TogServicesConfig togServicesConf,
                       RestTemplate restTemplate,
                       Errors errors, ThirdService thirdService, LifeNoticeMapper lifeNoticeMapper, LifeStudyMapper lifeStudyMapper) {
        this.lifeMapper = lifeMapper;
        this.lifeTagMapper = lifeTagMapper;
        this.lifeCheckMapper = lifeCheckMapper;
        this.lifeUploaderMapper = lifeUploaderMapper;
        this.lifeFileMapper = lifeFileMapper;
        this.lifeAdviceMapper = lifeAdviceMapper;
        this.meetingAgendaMapper = meetingAgendaMapper;
        this.lifeFileService = lifeFileService;
        this.meetingTalkService = meetingTalkService;
        this.asyncService = asyncService;
        this.meetingPeoplePartyLifeService = meetingPeoplePartyLifeService;
        this.restTemplateService = restTemplateService;
        this.togServicesConf = togServicesConf;
        this.restTemplate = restTemplate;
        this.errors = errors;
        this.thirdService = thirdService;
        this.lifeNoticeMapper = lifeNoticeMapper;
        this.lifeStudyMapper = lifeStudyMapper;
    }

    /**
     * 添加/编辑 民主生活会
     * @param form
     * @param headers
     * @return
     */
    public Long addOrEdit(LifeAddEditForm form, HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        LocalDateTime time = LocalDateTime.now();
        LifeEntity entity = new LifeEntity();
        BeanUtils.copyProperties(form,entity);
        entity.setUpdateTime(time);
        entity.setLastChangeUser(header.getUserId());
        if (ObjectUtils.isEmpty(form.getLifeId())){ //添加
            log.debug("添加 民主生活，header -> [{}]",header);
            entity.setCreateUser(header.getUserId());
            entity.setRegionId(header.getRegionId());
            entity.setOrgId(header.getOid());
            entity.setOrgLevel(restTemplateService.getOrgLevel(header,header.getOid()));
            entity.setOrgName(header.getOrgName());
            entity.setCreateTime(time);
            entity.setIsDel(0);
            entity.setStatus(Constant.LIFE_NEW);
            int i = lifeMapper.insertUseGeneratedKeys(entity);
            if (i > 0){
                log.debug("生成班子内部谈心谈话，lifeId -> [{}]，header -> [{}]",entity.getLifeId(),header);
                meetingTalkService.generaTalkData(header.getOid(),1,entity.getLifeId(),headers);
            }
        }else { //编辑
            log.debug("编辑 民主生活，lifeId -> [{}]，header -> [{}]",form.getLifeId(),header);
            lifeMapper.updateByPrimaryKeySelective(entity);
        }
        return entity.getLifeId();
    }

    /**
     * 删除民主生活会
     * @param header
     * @param lifeId
     * @return
     */
    public Integer del(HeaderHelper.SysHeader header,Long lifeId){
        log.debug("删除 民主生活，lifeId -> [{}]，header -> [{}]",lifeId,header);
        Example example = new Example(LifeEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId",lifeId)
                .andEqualTo("orgId",header.getOid())
                .andEqualTo("regionId",header.getRegionId());
        LifeEntity entity = new LifeEntity();
        entity.setIsDel(1);
        entity.setLastChangeUser(header.getUserId());
        entity.setUpdateTime(LocalDateTime.now());
        return lifeMapper.updateByExampleSelective(entity,example);
    }

    /**
     * 删除组织生活会
     * @param headers
     * @param lifeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer delAll(HttpHeaders headers,Long lifeId){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("删除第三方任务开始");
        delTask(headers,lifeId);
        log.debug("删除第三方任务结束");
        //删除t_meeting_life_comment--无
        //删除t_meeting_life_tag
        lifeTagMapper.delByLifeId(lifeId);
        //删除t_meeting_life_notice
        lifeNoticeMapper.delByLifeId(lifeId);
        //删除t_meeting_advice
        lifeAdviceMapper.delByLifeId(lifeId);
        //删除t_meeting_life_study
        lifeStudyMapper.delByLifeId(lifeId);
        //删除t_meeting_check
        lifeCheckMapper.delByLifeId(lifeId);
        //删除t_meeting_uploader
        lifeUploaderMapper.delByLifeId(lifeId);
        //删除谈心谈话
        meetingTalkService.delBySource(1,lifeId,header);
        //删除t_meeting_life
        lifeMapper.deleteByPrimaryKey(lifeId);
        //删除t_meeting_life_file
        lifeFileMapper.delByLifeId(lifeId);
        return 1;
    }

    public void delTask(HttpHeaders headers, Long lifeId){
        Example example = new Example(LifeUploaderEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId",lifeId);
        example.selectProperties("type","dataId");
        List<LifeUploaderEntity> list = lifeUploaderMapper.selectByExample(example);
        List<String> taskIds = new ArrayList<>();
        for(LifeUploaderEntity uploader : list){
            String id = "" + lifeId + "_" + uploader.getType() + "_" + uploader.getDataId();
            String taskId = Constant.TASKKEY + "_" + id;//民主生活会任务唯一标识
            log.debug("mt删除民主生活会任务："+taskId);
            taskIds.add(taskId);
        }
        if(CollectionUtils.isNotEmpty(taskIds)){
            thirdService.delPending(headers,taskIds);
        }
    }


    /**
     * 标签管理
     * @param header
     * @param form
     * @return
     */
    public Integer tagMange(HeaderHelper.SysHeader header, LifeTagManageForm form){
        int data;
        LocalDateTime time = LocalDateTime.now();
        if (form.getFlag() == 1){ //增加标签
            log.debug("添加标签 ，lifeIds -> [{}]，tagIds -> [{}]，header -> [{}]",form.getLifeIds(),form.getTagIds(),header);
            List<LifeTagEntity> list = new ArrayList<>();
            form.getLifeIds().forEach(x -> {
                form.getTagIds().forEach(y -> {
                    LifeTagEntity entity = new LifeTagEntity();
                    entity.setLifeId(x);
                    entity.setTagId(y);
                    entity.setCreateUser(header.getUserId());
                    entity.setCreateTime(time);
                    list.add(entity);
                });
            });
            data = lifeTagMapper.addTag(list);
        }else { //删除标签
            data = lifeTagMapper.delTag(form.getLifeIds(),form.getTagIds());
        }
        asyncService.multiUpdateId(form.getLifeIds(),header.getUserId(),time);
        return data;
    }

    /**
     * 民主生活会查询
     * @param findForm
     * @param page
     * @param pageSize
     * @return
     */
    public List<LifeForm> list(LifeFindForm findForm, Integer page, Integer pageSize){
        return PageHelper.startPage(page,pageSize).doSelectPage(() -> lifeMapper.list(findForm));
    }

    /**
     * 民主生活会标题年度查询
     * @param lifeId
     * @return
     */
    public LifeEntity lifeTitle(Long lifeId){
        Example example = new Example(LifeEntity.class);
        example.selectProperties("lifeId","title","years").createCriteria().andEqualTo("lifeId",lifeId);
        LifeEntity entity = lifeMapper.selectOneByExample(example);
        return entity;
    }

    /**
     * 结束会议
     * @param headers
     * @param lifeId
     * @return
     */
    public Integer finish(Long lifeId,HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("结束 民主生活会 ，lifeId -> [{}]，header -> [{}]", lifeId, header);
        LocalDateTime time = LocalDateTime.now();
        Example example = new Example(LifeEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId",lifeId)
                .andEqualTo("orgId",header.getOid())
                .andEqualTo("regionId",header.getRegionId())
                .andNotEqualTo("isDel",1);
        LifeEntity entity = lifeMapper.selectOneByExample(example);
        if (ObjectUtils.isEmpty(entity)){
            throw new ApiException("操作失败", new Result<>(errors, 3151, HttpStatus.INTERNAL_SERVER_ERROR.value(), "民主生活会不存在"));
        }
        if (Constant.LIFE_END <= entity.getStatus()){
            throw new ApiException("操作失败", new Result<>(errors, 3151, HttpStatus.INTERNAL_SERVER_ERROR.value(), "该民主生活会已结束"));
        }
        entity.setStatus(Constant.LIFE_END);
        entity.setLastChangeUser(header.getUserId());
        entity.setUpdateTime(time);
        int update = lifeMapper.updateByPrimaryKeySelective(entity);
        if (update > 0){ //异步拷贝相关数据至会后
            asyncService.copyFile(lifeId,time);
            asyncService.copyOrgStudyFile(lifeId);
            asyncService.copyCheck(lifeId,time);
            asyncService.copyAdviceDirect(lifeId,time);
            asyncService.copyAdviceOther(lifeId,time);
            asyncService.copyTalk(headers,lifeId,header.getOid());
        }
        return update;
    }

    /**
     * 修改会议状态
     * @param lifeId
     * @param status
     * @param step
     * @param header
     */
    public void changeStatus(Long lifeId, Integer status, Integer step,HeaderHelper.SysHeader header){
        Integer status2;
        if (status.equals(Constant.LIFE_NEW) && step.equals(Constant.LIFE_STEP_BEFORE)){
            status2 = Constant.LIFE_READY;
        }else if (status.equals(Constant.LIFE_END) && step.equals(Constant.LIFE_STEP_AFTER)){
            status2 = Constant.LIFE_COMBING;
        } else {
            return;
        }
        log.debug("修改会议状态，lifeId -> [{}]，status -> [{} -> {}]，header -> [{}]",lifeId,status,status2,header);
        LifeEntity entity = new LifeEntity();
        entity.setLifeId(lifeId);
        entity.setStatus(status2);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setLastChangeUser(header.getUserId());
        lifeMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 只上传文件的模块回显查询
     * @param lifeId
     * @param step
     * @param modeType
     * @return
     */
    public OnlyLifeFileForm OnlyLifeFileFind(Long lifeId, Integer step, List<Integer> modeType){
        OnlyLifeFileForm form = new OnlyLifeFileForm();
        form.setFiles(lifeFileMapper.onlyFile(lifeId,step,modeType));
        if (step != 2 || (modeType.size() == 1 && !modeType.contains(Constant.ModelType.PRE_OTHER.getModelId()))){
            form.setUploader(lifeUploaderMapper.uploader(lifeId,modeType));
        }
        form.setUploader("");
        return form;
    }

    /**
     * 会前学习查询
     */
    public void orgStudy(){

    }


    /**
     * 查询班子成员个人检视剖析
     * @param lifeId
     * @param step
     */
    public List<LifeCheckForm> checkSelf(Long lifeId, Integer step){
        return lifeCheckMapper.checkSelf(lifeId,step);
    }

    /**
     * 征求意见
     * @param lifeId
     * @param step
     * @return
     */
    public LifeAdviceForm advice(Long lifeId, Integer step){
        LifeAdviceForm form = new LifeAdviceForm();
        List<LifeAdviceForm.Advice> advice = lifeAdviceMapper.adviceFind(lifeId,step);
        if (CollectionUtils.isNotEmpty(advice)){
            form.setAdvice(advice);
            for (LifeAdviceForm.Advice a : advice){
                if (a.getAdviceType().equals(1)){
                    String mode = Constant.ModelType.ADVICE_DIRECT.getModelId().toString();
                    form.setFiles(lifeFileMapper.dataFile(a.getAdviceId(),step,mode));
                    break;
                }
            }
        }
        return form;
    }

//    /**
//     * 谈心谈话
//     * @param lifeId
//     * @param talkType
//     * @param step
//     * @return
//     */
//    public List<LifeTalkForm> talk(Long lifeId, Integer talkType, Integer step){
//        return lifeTalkMapper.lifeTalk(lifeId,talkType,step);
//    }

    /**
     * 发起的民主生活会
     * @param lifeId
     * @param step
     * @return
     */
//    public List lifeMeeting(Long lifeId, Integer step);

    /**
     * 会中详情查询
     * @param lifeId
     * @return
     */
    public LifeMeetingForm inTheMeeting(Long lifeId){
        LifeMeetingForm form = new LifeMeetingForm();
        form.setLifeId(lifeId);
        //查询会议议程
        Example example = new Example(LifeEntity.class);
        example.selectProperties("meetingId","lifeId").createCriteria().andEqualTo("lifeId",lifeId).andNotEqualTo("isDel",1);
        LifeEntity life = lifeMapper.selectOneByExample(example);
        if( ObjectUtils.isNotEmpty(life)){ //会议已发起，查询会议议程
            if (ObjectUtils.isEmpty(life.getMeetingId())){
                form.setNoAgenda("（会前准备阶段，您还未发起民主生活会喔~）");
                form.setAgenda(new ArrayList<>(0));
            }else {
                form.setNoAgenda("");
                form.setAgenda(meetingAgendaMapper.agendaTitle(life.getMeetingId()));
            }
            //查询会前附件
            form.setFiles(lifeFileService.queryFileInfo(lifeId,1));
            return form;
        }else {
            log.error("未查询到民主生活会[{}]",lifeId);
            throw new ApiException("找不到",new Result<>(errors,9404,HttpStatus.INTERNAL_SERVER_ERROR.value(),"民主生活会"));
        }
    }

//    /**
//     * 会后修改会前材料
//     * @param form
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public Long editData(LifeEditDataForm form, HeaderHelper.SysHeader header){
//        log.debug("会后修改会议材料 lifeId -> [{}]，submit -> [{}]，header -> [{}]",form.getLifeId(),form.getIsSubmit(),header);
//        boolean hasAdd = CollectionUtils.isNotEmpty(form.getAdd());
//        boolean hasDel = CollectionUtils.isNotEmpty(form.getDel());
//        if (!hasAdd && !hasDel){
//            log.debug("lifeId -> [{}]，无修改记录",form.getLifeId());
//            return form.getLifeId();
//        }
//        if (form.getIsSubmit().equals(0)){ //取消
//            log.debug("取消修改，物理删除新增数据，lifeId -> [{}]",form.getLifeId());
//            if (hasAdd){
//                delExecute(form.getAdd(),form.getLifeId(),form.getIsSubmit(),header.getUserId());
//            }
//        }else { //确认修改
//            log.debug("确认修改，逻辑删除数据，lifeId -> [{}]",form.getLifeId());
//            if (hasDel){
//                delExecute(form.getDel(),form.getLifeId(),form.getIsSubmit(),header.getUserId());
//            }
//        }
//        return form.getLifeId();
//    }
//
//    /**
//     * 执行删除
//     * @param list
//     * @param lifeId
//     * @param isSubmit
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void delExecute(List<LifeEditDataForm.EditData> list,Long lifeId,Integer isSubmit,Long uid){
//        //获取文件数据
//        List<Long> lifeFileIds = list.stream().filter(x -> x.getIsFile() == 1).map(LifeEditDataForm.EditData::getDataId).collect(Collectors.toList());
//        //获取其他数据
//        Map<Integer,List<Long>> map = list.stream().filter(x -> x.getIsFile() == 0)
//                .collect(Collectors.groupingBy(LifeEditDataForm.EditData::getModelType,
//                        Collectors.mapping(LifeEditDataForm.EditData::getDataId,
//                                Collectors.toList())));
//        List<Long> toDelFile;
//        if (isSubmit == 0){ //取消修改，物理删除新增数据
//            //删除个人检视剖析
//            int type = Constant.ModelType.CHECK_LEADER.getType();
//            List<Long> ids = map.get(type);
//            log.debug("删除新增 个人检视剖析，lifeId -> [{}]，checkId -> [{}]",lifeId,ids);
//            if (CollectionUtils.isNotEmpty(ids)){
//                lifeCheckMapper.multiDel(ids);
//                //要删除的关联附件
//                toDelFile = lifeFileMapper.toDel(lifeId,Constant.ModelType.getModeIdByType(type),type);
//                log.debug("个人检视剖析关联文件 lifeFileId -> [{}]",toDelFile);
//                lifeFileIds.addAll(toDelFile);77
//            }
//            //删除征求意见
//            type = Constant.ModelType.ADVICE_DIRECT.getType();
//            ids = map.get(type);
//            log.debug("删除新增 征求意见，lifeId -> [{}]，adviceId -> [{}]",lifeId,ids);
//            if (CollectionUtils.isNotEmpty(ids)){
//                lifeAdviceMapper.multiDel(ids);
//                //要删除的关联附件
//                toDelFile = lifeFileMapper.toDel(lifeId,Constant.ModelType.getModeIdByType(type),type);
//                log.debug("征求意见关联文件 lifeFileId -> [{}]",toDelFile);
//                lifeFileIds.addAll(toDelFile);
//            }
//            //删除谈心谈话
//            type = Constant.ModelType.TALK_MEMBERS.getType();
//            ids = map.get(type);
//            log.debug("删除新增 谈心谈话，lifeId -> [{}]，talkId -> [{}]",lifeId,ids);
//            if (CollectionUtils.isNotEmpty(ids)){
//                meetingTalkMapper.multiDel(ids);
//                toDelFile = lifeFileMapper.toDel(lifeId,Constant.ModelType.getModeIdByType(type),type);
//                log.debug("谈心谈话关联文件 lifeFileId -> [{}]",toDelFile);
//                lifeFileIds.addAll(toDelFile);
//            }
//            //删除文件
//            log.debug("删除新增 文件，lifeId -> [{}]，lifeFileId -> [{}]",lifeId,lifeFileIds);
//            if (CollectionUtils.isNotEmpty(lifeFileIds)){
//                lifeFileMapper.multiDel(lifeFileIds);
//            }
//        }else { //确认修改，逻辑删除数据
//            //删除个人检视剖析
//            int type = Constant.ModelType.CHECK_LEADER.getType();
//            List<Long> ids = map.get(type);
//            log.debug("删除 个人检视剖析，lifeId -> [{}]，checkId -> [{}]",lifeId,ids);
//            if (CollectionUtils.isNotEmpty(ids)){
//                lifeCheckMapper.multiDel(ids,uid);
//                //要删除的关联附件
//                toDelFile = lifeFileMapper.toLogicDel(lifeId,Constant.ModelType.getModeIdByType(type),type);
//                log.debug("个人检视剖析关联文件 lifeFileId -> [{}]",toDelFile);
//                lifeFileIds.addAll(toDelFile);
//            }
//            //删除征求意见
//            type = Constant.ModelType.ADVICE_DIRECT.getType();
//            ids = map.get(type);
//            log.debug("删除新增 征求意见，lifeId -> [{}]，adviceId -> [{}]",lifeId,ids);
//            if (CollectionUtils.isNotEmpty(ids)){
//                lifeAdviceMapper.multiDel(ids);
//                //要删除的关联附件
//                toDelFile = lifeFileMapper.toLogicDel(lifeId,Constant.ModelType.getModeIdByType(type),type);
//                log.debug("征求意见关联文件 lifeFileId -> [{}]",toDelFile);
//                lifeFileIds.addAll(toDelFile);
//            }
//            //删除谈心谈话
//            type = Constant.ModelType.TALK_MEMBERS.getType();
//            ids = map.get(type);
//            log.debug("删除新增 谈心谈话，lifeId -> [{}]，talkId -> [{}]",lifeId,ids);
//            if (CollectionUtils.isNotEmpty(ids)){
//                meetingTalkMapper.multiDel(ids);
//                toDelFile = lifeFileMapper.toLogicDel(lifeId,Constant.ModelType.getModeIdByType(type),type);
//                log.debug("谈心谈话关联文件 lifeFileId -> [{}]",toDelFile);
//                lifeFileIds.addAll(toDelFile);
//            }
//            //删除文件
//            log.debug("删除新增 文件，lifeId -> [{}]，lifeFileId -> [{}]",lifeId,lifeFileIds);
//            if (CollectionUtils.isNotEmpty(lifeFileIds)){
//                lifeFileMapper.multiDel(lifeFileIds);
//            }
//        }
//
//    }

    /**
     *
     * @param header
     * @param lifeId
     * @param step  当前页面 1:会前   2：会后
     * @param type  1：检视剖析  2 征求意见
     * @param dataIds
     * @param adviceType 1：直接上传，2：问卷调查，3：座谈会，4：个别访谈
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteSpecial( HeaderHelper.SysHeader header, HttpHeaders headers,Long lifeId, Integer step, Integer type, List<Long> dataIds, Integer adviceType) {
        Long userId = header.getUserId();
        if(type==1){
            lifeCheckMapper.multiDel(dataIds,userId);
            //附件也删掉
            lifeFileMapper.deleteByIds(lifeId, Arrays.asList(5,6),dataIds,userId);
            return;
        }
        if(type==2 && adviceType==1) {//直接上传
            log.debug("删除征求意见-直接上传");
//            lifeFileService.deleteAttach(lifeId,dataIds, step,header);
            lifeFileMapper.deleteAdviceFile(lifeId,step);
            lifeAdviceMapper.oneDirectFile(lifeId,step);
            return;
        }
        if(CollectionUtils.isEmpty(dataIds)){
            log.debug("数据行id为空导致没有进行删除");
            return;
        }
        Long dataId = dataIds.get(0);//征求意见除直接上传外只能一个一个删除
        Long meetingId = queryAdvice(dataId);
       if(adviceType==4){//个别访谈
           //查询该行的talkid
            log.debug("删除征求意见-个别访谈");
            meetingTalkService.delMeetingTalk(meetingId,header);
           //删除关联关系
           lifeAdviceMapper.oneDel(dataId,adviceType);
           return;
        }
       if(adviceType==3 ||adviceType==2){
            if(adviceType==3){
                log.debug("调用王若宇方法删除meeting的座谈会开始_"+meetingId);
                try{
                    meetingPeoplePartyLifeService.deleteMeetingById(headers,header,meetingId);
                }catch (Exception e){
                    log.error("调用王若宇方法删除meeting的座谈会报错_"+meetingId);
                }
                log.debug("调用王若宇方法删除meeting的座谈会结束_"+meetingId);

            }
//            else{
//                log.debug("删除征求意见-问卷调查");
//                try {
//                    log.debug("调用活动中心进行问卷调查删除开始_"+meetingId);
//                   RemoteApiHelper.get(restTemplate, String.format("http://%s/activity/delete/%s/2", togServicesConf.getActivityPlat(), meetingId), headers,new TypeReference<Result<Object>>() {});
//                } catch (IOException e) {
//                    e.printStackTrace();
//                    log.error("调用活动中心进行问卷调查删除失败_"+meetingId+"_"+e);
//                }finally {
//                    log.debug("调用活动中心进行问卷调查删除结束_"+meetingId);
//                }
//            }
           //删除关联关系
           lifeAdviceMapper.oneDel(dataId,adviceType);
        }

    }

    private Long queryAdvice(Long dataId) {
        LifeAdviceEntity advice = lifeAdviceMapper.selectByPrimaryKey(dataId);
        Long meetingId = advice.getDataId();
        return meetingId;
    }


    /**
     *
     * @param header
     * @param lifeId
     * @param step 1：会前，2：会后
     * @param leaders 班子成员
     */
    public void saveCheck(HeaderHelper.SysHeader header, Long lifeId, Integer step, List<LeaderForm> leaders) {
        List<LifeCheckEntity> list = new ArrayList<>();
        for(LeaderForm leaderForm : leaders){
            LifeCheckEntity lifeCheckEntity = new LifeCheckEntity();
            lifeCheckEntity.setLifeId(lifeId);
            lifeCheckEntity.setUserId(leaderForm.getUserId());
            lifeCheckEntity.setUsername(leaderForm.getUsername());
            lifeCheckEntity.setOrgId(leaderForm.getOrgId());
            lifeCheckEntity.setOrgName(leaderForm.getOrgName());
            lifeCheckEntity.setStep(step);
            lifeCheckEntity.setIsDel(0);
            lifeCheckEntity.setCreateTime(LocalDateTime.now());
            lifeCheckEntity.setCreateUser(header.getUserId());
            lifeCheckEntity.setUpdateTime(LocalDateTime.now());
            lifeCheckEntity.setLastChangeUser(header.getUserId());
            list.add(lifeCheckEntity);
        }
        log.debug("插入班子成员检视剖析数据");
        if(!CollectionUtils.isEmpty(list)){
            lifeCheckMapper.insertList(list);
        }


    }

    /**
     * 同步民主生活会相关组织信息
     * @param organizationBase
     * @return
     */
    public void updateOrgInfo(OrganizationBase organizationBase) {
         lifeMapper.updateOrgInfo(organizationBase);
         lifeCheckMapper.updateOrgInfo(organizationBase);
    }


    //直接录入创建关联关系
    public void createRelation(LifeEntity lifeEntity){
        lifeMapper.updateByPrimaryKeySelective(lifeEntity);
    }
}
