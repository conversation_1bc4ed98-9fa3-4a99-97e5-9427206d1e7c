package com.goodsogood.ows.service;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.pojo.MeetingPushParam;
import com.goodsogood.ows.configuration.MeetingNoticeConfiguration;
import com.goodsogood.ows.configuration.NoticeTypes;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 纪实通知服务
 *
 * <AUTHOR>
 * @date 2019-05-10 11:20
 * @since 1.0.3
 **/
@Service
@Log4j2
public class MeetingNoticeService {

    @Value("${tbc-city-id}")
    private Long tbcCityId = null;

    @Value("${gray}")
    private Integer gray = 1;

    private final MeetingNoticeSupportService meetingNoticeSupportService;
    private final MeetingNoticeConfiguration meetingNoticeConfiguration;
    private final UserCenterService userCenterService;
    private final OrgTypeConfig orgTypeConfig;


    @Autowired
    public MeetingNoticeService(MeetingNoticeSupportService meetingNoticeSupportService, MeetingNoticeConfiguration meetingNoticeConfiguration, UserCenterService userCenterService, OrgTypeConfig orgTypeConfig) {
        this.meetingNoticeSupportService = meetingNoticeSupportService;
        this.meetingNoticeConfiguration = meetingNoticeConfiguration;
        this.userCenterService = userCenterService;
        this.orgTypeConfig = orgTypeConfig;
    }

    /**
     * 发送通知
     * <p>
     * 增加主题党日提醒  tc 2021-11-19
     */
    public void sendNotice(final LogAspectHelper.SSLog ssLog, MeetingPushParam pushParam) {
        log.debug("推送通知：meetingNoticeConfiguration = {}", meetingNoticeConfiguration);
        if (meetingNoticeConfiguration == null || CollectionUtils.isEmpty(meetingNoticeConfiguration.getTypes())) {
            log.debug("发送通知：需要发送的类型为空，推出发送！");
            return;
        }
        meetingNoticeConfiguration.getTypes().forEach((k, v) -> {
            Long regionId;
            List<NoticeTypes> noticeTypesList;
            if ((regionId = k) != null && !CollectionUtils.isEmpty((noticeTypesList = v))) {
                /**
                 * 取消考核组织/管理组织的限制
                 * 2021-11-23  tc
                 */
                    /*//获取考核单位
                    List<Long> checkOrgIdList = this.openService.getCheckOrgIds(regionId);
                    if (ListUtils.isEmpty(checkOrgIdList)) {
                         log.debug("发送通知：考核单位查询为空，推出发送！ regionId:{}", regionId);
                         return;
                    }
                    log.debug("考核单位：checkOrgIdList 长度：{}", checkOrgIdList.size());
                    log.debug("考核单位：checkOrgIdList = {}", checkOrgIdList);

                    //获取有支委会或者党小组的组织，当发送支委会或者党小组的时候必须是有的组织才发送
                    Map<String, List<Long>> dataPeriodGroup = this.openService.getOrgPeriodIdsAndOrgGroup(checkOrgIdList);*/
                //目前支持推送消息的业务类型
                //1、支部党员大会          每月25号 10点
                //2、支部委员会            每月25号 10点
                //3、党小组会              每月25号 10点
                //4、党课                 每季度月末25号 10点
                //5、主题党日              每季度月末25号 10点
                HttpHeaders headers = new HttpHeaders();
                String token = UUID.randomUUID().toString().replaceAll("-", "");
                headers.set("_tk", token);
                HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
                header.setRegionId(regionId);
                header.setOid(tbcCityId);
                header.setUserId(6965L);
                header.setOrgType(102803);
                header.setGray(gray);
                header.setType(2);
                List<Long> groupIds = userCenterService.findByGroupIds(header, tbcCityId);
                log.debug("查询主题党日提醒定时任务断点一:{},{}", groupIds, JsonUtils.toJson(header));

                // 如果是市局下的支部就把跳转地址改成市值同步的url地址
                List<OrganizationBase> allChildOrg = userCenterService.findAllChildOrg(header, header.getOid());
                // 获取列表中所有的党支部
                List<Long> orgIds = allChildOrg.stream()
                        .filter(x -> orgTypeConfig.getBranchChild().contains(x.getOrgTypeChild()))
                        .map(OrganizationBase::getOrgId).collect(Collectors.toList());

                noticeTypesList.forEach(type -> {
                    //多线程线程安全
                    NoticeTypes newType = new NoticeTypes();
                    BeanUtils.copyProperties(type, newType);
                    this.meetingNoticeSupportService.sendNotice(
                            header,
                            ssLog,
                            newType,
                            pushParam,
                            regionId,
                            groupIds,
                            orgIds);
                });
            }
        });
    }

    /**
     * 由于多线程，导致在合并数据的时候回产生checkOrgIdList非线程安全问题
     *
     * @param checkOrgIdList
     * @return
     */
    private List<Long> getList(List<Long> checkOrgIdList) {
        List<Long> list = new ArrayList<>();
        list.addAll(checkOrgIdList);
        return list;
    }

}
