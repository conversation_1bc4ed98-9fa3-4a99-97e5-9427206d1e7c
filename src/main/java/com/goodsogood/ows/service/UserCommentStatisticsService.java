package com.goodsogood.ows.service;

import cn.hutool.core.convert.Convert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.UserCommentMapper;
import com.goodsogood.ows.mapper.UserCommentStatisticsMapper;
import com.goodsogood.ows.model.db.UserCommentStatisticsEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.utils.*;
import com.google.common.base.Preconditions;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 民主评议统计服务层
 * @date 2020/1/6
 */
@Service
@Log4j2
public class UserCommentStatisticsService {

    public static final int[] COMMENT_LEVEL = new int[]{1, 2, 3, 4, 5, 41, 42};

    private final String[] title = new String[]{"组织名称", "党员数", "参加评议党员数", "\"优秀\"等级党员数", "\"合格\"等级党员数",
            "\"基本合格\"等级党员数", "\"不合格\"等级党员数", "\"不合格\"等级中限期改正党员数", "\"不合格\"等级中除名处理党员数","\"不确定等次\""};
    private final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

    @Value("${temp-path}")
    @NotBlank
    private String tempPath;

    private final Errors errors;
    private final OpenService openService;
    private final UserCommentMapper userCommentMapper;
    private final OrgTypeConfig orgTypeConfig;
    private final UserCommentStatisticsMapper userCommentStatisticsMapper;
    private final RegionService regionService;
    private final StringRedisTemplate redisTemplate;
    private final RestTemplate restTemplate;
    private final TogServicesConfig togServicesConfig;

    @Autowired
    public UserCommentStatisticsService(Errors errors, OpenService openService, UserCommentMapper userCommentMapper,
                                        OrgTypeConfig orgTypeConfig, UserCommentStatisticsMapper userCommentStatisticsMapper,
                                        RegionService regionService, StringRedisTemplate redisTemplate, RestTemplate restTemplate, TogServicesConfig togServicesConfig) {
        this.errors = errors;
        this.openService = openService;
        this.userCommentMapper = userCommentMapper;
        this.orgTypeConfig = orgTypeConfig;
        this.userCommentStatisticsMapper = userCommentStatisticsMapper;
        this.regionService = regionService;
        this.redisTemplate = redisTemplate;
        this.restTemplate = restTemplate;
        this.togServicesConfig = togServicesConfig;
    }

    public void generateUserCommentStatistics(HeaderHelper.SysHeader sysHeader, Integer year) {
        List<Integer> years = new ArrayList<>();
        if (year == null) {
            years = this.userCommentMapper.getYearByUpdate(format.format(new Date()));
        } else {
            years.add(year);
        }
        for (Integer y : years) {
            // 获取组织列表
            List<OrgInfoForm> orgInfoList = this.openService.getOrgInfoList(regionService.bindingOrgId(sysHeader.getRegionId()), Constant.YES);
//            List<OrgInfoForm> orgInfoList=new ArrayList<>(1);
//            OrgInfoForm orgInfoForm=new OrgInfoForm();
//            orgInfoForm.setOrgId(4685L);
//            orgInfoForm.setParentId(3L);
//            orgInfoForm.setOrgName("固守-党委");
//            orgInfoForm.setOrgTypeChild(10280301);
//            orgInfoForm.setOrgLevel("-0-1-3-");
//            orgInfoList.add(orgInfoForm);
            orgInfoList.forEach(orgInfo -> {
                Long partyNumber = this.openService.getPartyNumberByOrgId(orgInfo.getOrgId());
                // 查询统计数据是否已存在
                Example example = new Example(UserCommentStatisticsEntity.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("reviewYear", y).andEqualTo("orgId", orgInfo.getOrgId());
                List<UserCommentStatisticsEntity> list = this.userCommentStatisticsMapper.selectByExample(example);

                // 查询当前组织评议统计结果
                List<UserComment> commentCount =
                        this.userCommentMapper.selectCommentCount(orgInfo.getOrgId(), y);
                long sum = commentCount.stream().mapToInt(UserComment::getNumber).summaryStatistics().getSum();
                if (CollectionUtils.isEmpty(list)) {
                    UserCommentStatisticsEntity entity = new UserCommentStatisticsEntity();
                    entity.setReviewYear(y);
                    entity.setOrgId(orgInfo.getOrgId());
                    entity.setOrgParentId(orgInfo.getParentId());
                    entity.setOrgName(orgInfo.getOrgName());
                    entity.setOrgTypeChild(orgInfo.getOrgTypeChild());
                    entity.setOrgLevel(orgInfo.getOrgLevel());
                    entity.setRegionId(sysHeader.getRegionId());
                    // 获取党员数据量
                    entity.setPartyNumber(partyNumber.intValue());
                    // 参加评议的数量
                    entity.setJoinCommentNumber((int) sum);
                    entity.setCreateUser(Constant.USER);
                    entity.setCreateTime(new Date());
                    // 评议统计为空
                    for (int i : COMMENT_LEVEL) {
                        entity.setUserCommentStatisticsId(null);
                        entity.setRating(i);
                        entity.setRatingNumber(0);
                        int count = 0;
                        for (UserComment comment : commentCount) {
                            String id = comment.getRating() + String.valueOf(null == comment.getDealOpinion() ? "" : comment.getDealOpinion());
                            if (i == comment.getRating().intValue() || i == Integer.valueOf(id).intValue()) {
                                count += comment.getNumber();
                            }
                        }
                        entity.setRatingNumber(count);
                        this.userCommentStatisticsMapper.insert(entity);
                    }
                } else {
                    // 已存在评议统计结果
                    list.forEach(userComment -> {
                        userComment.setOrgParentId(orgInfo.getParentId());
                        userComment.setOrgName(orgInfo.getOrgName());
                        userComment.setOrgTypeChild(orgInfo.getOrgTypeChild());
                        userComment.setOrgLevel(orgInfo.getOrgLevel());
                        // 获取党员数据量
                        userComment.setPartyNumber(partyNumber.intValue());
                        // 参加评议的数量
                        userComment.setJoinCommentNumber((int) sum);
                        userComment.setLastChangeUser(Constant.USER);
                        userComment.setUpdateTime(new Date());
                        int count = 0;
                        for (UserComment comment : commentCount) {
                            String id = comment.getRating() + String.valueOf(null == comment.getDealOpinion() ? "" : comment.getDealOpinion());
                            if (userComment.getRating().equals(comment.getRating()) || userComment.getRating().equals(Integer.valueOf(id))) {
                                count += comment.getNumber();
                            }
                        }
                        userComment.setRatingNumber(count);
                        this.userCommentStatisticsMapper.updateByPrimaryKeySelective(userComment);
                    });

                }
            });
        }
    }

    /**
     * 判断是否是工委登录
     *
     * @param queryForm
     * @return com.github.pagehelper.Page<com.goodsogood.ows.model.vo.UserCommentStatisticsVO>
     * <AUTHOR>
     * @date 2020/1/7
     */
    public Page<UserCommentStatisticsVO> queryUserCommentStatistics(HeaderHelper.SysHeader sysHeader, UserCommentStatisticsQueryForm queryForm) {

        if (queryForm.getOrgId().equals(regionService.bindingOrgId(sysHeader.getRegionId()))) {
            return this.queryCommentStatistics(queryForm, Constant.YES);
        } else {
            return this.queryCommentStatistics(queryForm, Constant.NO);
        }
    }

    /**
     * 导出民主评议统计列表
     *
     * @param queryForm
     * @return java.util.List<com.goodsogood.ows.model.vo.UserCommentStatisticsVO>
     * <AUTHOR>
     * @date 2020/1/7
     */
    @Async("asyncGenerateExcelExecutor")
    public void exportUserCommentStatistics(HttpHeaders headers, UserCommentStatisticsQueryForm queryForm, String uuid, String redisRepeatKey ) {
        List<UserCommentStatisticsVO> voList = new ArrayList<>();
        if (queryForm.getOrgId().equals(3L)) {
            voList = this.exportCommentStatistics(queryForm, Constant.YES);
        } else {
            voList = this.exportCommentStatistics(queryForm, Constant.NO);
        }

        String tableName = "民主评议党员统计";

        List<List<String>> itemList = new ArrayList();
        List<String> tableHeader = new ArrayList<>(Arrays.asList(title));
        itemList.add(tableHeader);
        RateUtils.build(Convert.toInt(voList.size()), uuid);
        voList.forEach(vo -> {
            List<UserCommentStatisticsVO.UserCommentInfo> infoList = vo.getCommentInfoList();
            List<String> row = new ArrayList<>();
            row.add(vo.getOrgName());
            row.add(Convert.toStr(vo.getPartyNumber()));
            row.add(Convert.toStr(vo.getJoinCommentNumber()));
            row.add(Convert.toStr(this.getUserCommentInfoByList(infoList, Constant.FINE).getCommentLevelNumber()));
            row.add(Convert.toStr(this.getUserCommentInfoByList(infoList, Constant.QUALIFIED).getCommentLevelNumber()));
            row.add(Convert.toStr(this.getUserCommentInfoByList(infoList, Constant.BASIC_QUALIFIED).getCommentLevelNumber()));
            row.add(Convert.toStr(this.getUserCommentInfoByList(infoList, Constant.UNQUALIFIED).getCommentLevelNumber()));
            row.add(Convert.toStr(this.getUserCommentInfoByList(infoList, Constant.UNQUALIFIED_RECTIFICATION).getCommentLevelNumber()));
            row.add(Convert.toStr(this.getUserCommentInfoByList(infoList, Constant.UNQUALIFIED_EXPEL).getCommentLevelNumber()));
            row.add(Convert.toStr(this.getUserCommentInfoByList(infoList, Constant.UNKNOWN).getCommentLevelNumber()));
            itemList.add(row);
            RateUtils.auto(uuid);
        });

        AsyncFileDownUtils.asyncGenerateExcel(this.redisTemplate, this.restTemplate, this.togServicesConfig, this.errors, headers, itemList, tableName, tempPath, uuid, redisRepeatKey);

    }

    public Object downExcel(HttpServletRequest request, HttpServletResponse response, String uuid, String redisRepeatKey) {
        Object rate = "0";
        try {
            rate = AsyncFileDownUtils.asyncDownFile(this.errors, this.redisTemplate, uuid, redisRepeatKey);
        } catch (Exception e) {
            log.error("下载民主评议统计excel失败! errorMasage {}", e.getMessage(), e);
            throw new ApiException("下载民主评议统计出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "下载民主评议统计出错"));
        }
        return rate;
    }

    private UserCommentStatisticsVO.UserCommentInfo getUserCommentInfoByList(List<UserCommentStatisticsVO.UserCommentInfo> infoList, int rating) {
        return infoList.stream().filter(userCommentInfo -> userCommentInfo.getCommentLevel() == rating)
                .collect(Collectors.maxBy(Comparator.comparing(UserCommentStatisticsVO.UserCommentInfo::getCommentLevel))).get();
    }

    /**
     * 查询民主评议统计列表，带分页
     *
     * @param queryForm
     * @param isExistChild
     * @return com.github.pagehelper.Page<com.goodsogood.ows.model.vo.UserCommentStatisticsVO>
     * <AUTHOR>
     * @date 2020/1/7
     */
    private Page<UserCommentStatisticsVO> queryCommentStatistics(UserCommentStatisticsQueryForm queryForm, int isExistChild) {
        int p = Preconditions.checkNotNull(queryForm.getPage());
        int r = Preconditions.checkNotNull(queryForm.getPageSize());
        Page<UserCommentStatisticsVO> userCommentStaList = PageHelper.startPage(p, r).doSelectPage(() ->
                this.userCommentStatisticsMapper.selectUserCommentStatisticsGroupOrg(queryForm.getOrgId(), queryForm.getOrgName(), queryForm.getYear(), isExistChild, this.orgTypeConfig.getBranchChild()));
        userCommentStaList.forEach(userInfoCommentSta -> {
            this.supplyCommentStatistics(userInfoCommentSta, queryForm.getYear(), isExistChild);
        });
        return userCommentStaList;
    }

    /**
     * 民主评议统计列表，不分页
     *
     * @return
     * <AUTHOR>
     * @date 2020/1/7
     */
    private List<UserCommentStatisticsVO> exportCommentStatistics(UserCommentStatisticsQueryForm queryForm, int isExistChild) {
        List<UserCommentStatisticsVO> userCommentStaList =
                this.userCommentStatisticsMapper.selectUserCommentStatisticsGroupOrg(queryForm.getOrgId(), queryForm.getOrgName(), queryForm.getYear(), isExistChild, this.orgTypeConfig.getBranchChild());
        userCommentStaList.forEach(userInfoCommentSta -> {
            this.supplyCommentStatistics(userInfoCommentSta, queryForm.getYear(), isExistChild);
        });
        return userCommentStaList;
    }

    /**
     * 补充民主评议等级数据
     *
     * @param userCommentStatisticsVO
     * @param year
     * @param isExistChild
     * @return void
     * <AUTHOR>
     * @date 2020/1/7
     */
    private void supplyCommentStatistics(UserCommentStatisticsVO userCommentStatisticsVO, int year, int isExistChild) {
        List<UserCommentStatisticsVO.UserCommentInfo> userCommentInfoList = new ArrayList<>(6);
        List<UserCommentStatisticsEntity> entityList = this.selectCommentStatisticsList(userCommentStatisticsVO.getOrgId(), year);
        entityList.forEach(entity -> {
            UserCommentStatisticsVO.UserCommentInfo userCommentInfo = new UserCommentStatisticsVO().new UserCommentInfo();
            userCommentInfo.setCommentLevel(entity.getRating());
            userCommentInfo.setCommentLevelNumber(entity.getRatingNumber());
            userCommentInfoList.add(userCommentInfo);
        });
        userCommentStatisticsVO.setIsExistChild(isExistChild);
        userCommentStatisticsVO.setCommentInfoList(userCommentInfoList);
    }


    /**
     * 根据组织查询民主评议统计情况
     *
     * @param orgId
     * @param year
     * @return java.util.List<com.goodsogood.ows.model.db.UserCommentStatisticsEntity>
     * <AUTHOR>
     * @date 2020/1/6
     */
    private List<UserCommentStatisticsEntity> selectCommentStatisticsList(Long orgId, Integer year) {
        Example example = new Example(UserCommentStatisticsEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", orgId);
        criteria.andEqualTo("reviewYear", year);
        return this.userCommentStatisticsMapper.selectByExample(example);
    }

    /**
     * 同步修改组织信息
     *
     * @param organizationBase
     * @return int
     * <AUTHOR>
     * @date 2020/1/8
     */
    public int updateOrgInfo(OrganizationBase organizationBase) {
        Example example = new Example(UserCommentStatisticsEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", organizationBase.getOrganizationId());
        final List<UserCommentStatisticsEntity> entities = this.userCommentStatisticsMapper.selectByExample(example);
        entities.forEach(entity -> {
            entity.setOrgParentId(organizationBase.getParentId());
            entity.setOrgName(organizationBase.getName());
            entity.setOrgLevel(organizationBase.getOrgLevel());
            entity.setOrgTypeChild(organizationBase.getOrgTypeChild());
            entity.setLastChangeUser(-111L);
            entity.setUpdateTime(new Date());
            this.userCommentStatisticsMapper.updateByPrimaryKey(entity);
        });
        return entities.size();
    }
}
