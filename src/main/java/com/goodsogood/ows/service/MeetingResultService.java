package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.goodsogood.ows.common.*;
import com.goodsogood.ows.common.pojo.Headers;
import com.goodsogood.ows.common.pojo.PageBean;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.configuration.RabbitmqQueueConfig;
import com.goodsogood.ows.controller.ControllerHelper;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.mongo.TopPriorityMeeting;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.workflow.ApprovalBase;
import com.goodsogood.ows.service.rabbitMQ.Consumer;
import com.goodsogood.ows.service.rabbitMQ.Producer;
import com.goodsogood.ows.utils.*;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-25 15:40
 **/
@Service
@Log4j2
public class MeetingResultService {
    private final Consumer consumer;

    private final Errors errors;

    private final MeetingMapper meetingMapper;
    private final MeetingResultMapper meetingResultMapper;
    private final MeetingHistoryMapper meetingHistoryMapper;
    private final MeetingResolutionFileMapper meetingResolutionFileMapper;
    private final MeetingTopicMapper meetingTopicMapper;

    private final HeaderService headerService;
    private final MeetingService meetingService;
    private final WorkflowService workflowService;
    private final MeetingLeaveService meetingLeaveService;
    private final MeetingAnswerService meetingAnswerService;
    private final MeetingHistoryService meetingHistoryService;
    private final MeetingResultHsService meetingResultHsService;
    private final UserCenterService userService;
    private final MeetingTaskService meetingTaskService;
    private final StringRedisTemplate stringRedisTemplate;
    private final TopicService topicService;
    private final PushService pushService;
    private final IndexService indexService;
    private final MeetingResultFileMapper meetingResultFileMapper;
    private final MeetingDraftService meetingDraftService;
    private final ThirdService thirdService;
    private final MeetingWaitSignService meetingWaitSignService;
    private final MeetingPeoplePartyLifeService meetingPeoplePartyLifeService;
    private final MeetingScoreService meetingScoreService;
    private final RabbitmqQueueConfig rabbitmqQueueConfig;
    private final Producer producer;
    private final OrgLifeService orgLifeService;
    private final LifeService lifeService;
    private final TransferService transferService;
    private final TopPriorityService topPriorityService;

    @Autowired
    public MeetingResultService(
            Consumer consumer, Errors errors,
            MeetingMapper meetingMapper,
            MeetingResultMapper meetingResultMapper,
            MeetingResolutionFileMapper meetingResolutionFileMapper,
            HeaderService headerService,
            WorkflowService workflowService,
            MeetingService meetingService,
            MeetingHistoryMapper meetingHistoryMapper,
            MeetingTopicMapper meetingTopicMapper,
            MeetingLeaveService meetingLeaveService,
            MeetingAnswerService meetingAnswerService,
            MeetingHistoryService meetingHistoryService,
            MeetingResultHsService meetingResultHsService,
            UserCenterService userService,
            MeetingTaskService meetingTaskService,
            StringRedisTemplate stringRedisTemplate,
            TopicService topicService,
            PushService pushService,
            IndexService indexService,
            MeetingResultFileMapper meetingResultFileMapper,
            MeetingDraftService meetingDraftService, ThirdService thirdService, MeetingWaitSignService meetingWaitSignService,
            MeetingPeoplePartyLifeService meetingPeoplePartyLifeService, MeetingScoreService meetingScoreService,
            RabbitmqQueueConfig rabbitmqQueueConfig, Producer producer, OrgLifeService orgLifeService, LifeService lifeService, TransferService transferService, TopPriorityService topPriorityService) {
        this.consumer = consumer;
        this.errors = errors;
        this.meetingMapper = meetingMapper;
        this.meetingResultMapper = meetingResultMapper;
        this.meetingResolutionFileMapper = meetingResolutionFileMapper;
        this.headerService = headerService;
        this.workflowService = workflowService;
        this.meetingService = meetingService;
        this.meetingHistoryMapper = meetingHistoryMapper;
        this.meetingTopicMapper = meetingTopicMapper;
        this.meetingLeaveService = meetingLeaveService;
        this.meetingAnswerService = meetingAnswerService;
        this.meetingHistoryService = meetingHistoryService;
        this.meetingResultHsService = meetingResultHsService;
        this.meetingTaskService = meetingTaskService;
        this.userService = userService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.topicService = topicService;
        this.pushService = pushService;
        this.indexService = indexService;
        this.meetingResultFileMapper = meetingResultFileMapper;
        this.meetingDraftService = meetingDraftService;
        this.thirdService = thirdService;
        this.meetingWaitSignService = meetingWaitSignService;
        this.meetingPeoplePartyLifeService = meetingPeoplePartyLifeService;
        this.meetingScoreService = meetingScoreService;
        this.rabbitmqQueueConfig = rabbitmqQueueConfig;
        this.producer = producer;
        this.orgLifeService = orgLifeService;
        this.lifeService = lifeService;
        this.transferService = transferService;
        this.topPriorityService = topPriorityService;
    }

    /**
     * 提交纪实考核表 - 有活动流程 - 新增纪实报告
     *
     * @param headers
     * @param result
     * @return
     */
    @Transactional
    public void submit(HttpHeaders headers, MeetingResultForm result) {
        log.debug("mtresult1:" + result);
        Headers header = this.headerService.bulidHeader(headers);
        this.submit(headers,
                header,
                result,
                ResultConstant.HAS_MEETING,
                true, true);
        log.debug("mtresult2:" + result);
        log.debug("mtresult3:" + result.getMeeting());

        //调用增加积分接口
//        meetingScoreService.addMeetingScore(header.getRegionId(),result.getMeeting());  丢入队列处理  tc 2022-01-25
        MeetingScoreMQVo msmq = new MeetingScoreMQVo(header.getRegionId(), result.getMeeting(), false, null, Config.MeetingScoreConf.MQ_ADD_MEETING_SCORE);
        producer.send(Config.RabbitmqConf.DIRECT_EXCHANGE_NAME, rabbitmqQueueConfig.getMeetingScoreRouting(), JsonUtils.toJson(msmq));
        //调用补学相关的业务
        meetingWaitSignService.addSignInfo(result.getMeeting().getMeetingId()
                , ControllerHelper.getSysHeader(headers, errors));
        // 提交报告，更新首页缓存
        indexService.collectRedis();
    }

    /**
     * 提交纪实考核表 - 有活动流程 - 更新纪实报告 - 下级
     *
     * @param headers
     * @param result
     * @return
     */
    @Transactional
    public void update(HttpHeaders headers, MeetingResultForm result) {
        Headers header = this.headerService.bulidHeader(headers);
        this.submit(headers,
                header,
                result,
                ResultConstant.HAS_MEETING,
                true, false);
        // 调用增加积分接口
//        meetingScoreService.addMeetingScore(header.getRegionId(),result.getMeeting());  丢入队列处理  tc 2022-01-25
        MeetingScoreMQVo msmq = new MeetingScoreMQVo(header.getRegionId(), result.getMeeting(), false, null, Config.MeetingScoreConf.MQ_ADD_MEETING_SCORE);
        producer.send(Config.RabbitmqConf.DIRECT_EXCHANGE_NAME, rabbitmqQueueConfig.getMeetingScoreRouting(), JsonUtils.toJson(msmq));
        // 调用补学相关的业务
        meetingWaitSignService.addSignInfo(result.getMeeting().getMeetingId()
                , ControllerHelper.getSysHeader(headers, errors));

        // 更新报告，更新首页缓存
        indexService.collectRedis();
    }

    /**
     * 提交纪实考核表 - 有活动流程 - 更新纪实报告 - 上级
     *
     * @param headers
     * @param result
     * @return
     */
    @Transactional
    public void updateLeader(HttpHeaders headers, MeetingResultForm result) {
        this.submit(headers,
                this.headerService.bulidHeader(headers),
                result,
                ResultConstant.HAS_MEETING,
                false, false);
    }

    /**
     * 提交纪实考核表
     *
     * @param headers  httpheader
     * @param header   校验过的header
     * @param result   纪实结果接收表单
     * @param type     0：直接填写， 1：有活动流程
     * @param isWorker true:下级 false:上级
     * @param isAdd    true:新增 false:修改
     */
    private void submit(HttpHeaders headers, Headers header, MeetingResultForm result, int type, boolean isWorker, boolean isAdd) {
        header = this.headerService.bulidHeader(headers);
        int code = this.submitInner(headers, header, result.getResult(), result, type, isWorker, isAdd);
        if (code != 0) {
            if (code == -Global.Errors.NOT_FOUND.getCode()) {
                throw new ApiException("提交的活动未找到", new Result<>(errors, Math.abs(code), HttpStatus.OK.value(), StringCanstant.ACTIVITY));
            } else {
                throw new ApiException("更新纪实考核表-下级，出错：", new Result<>(errors, Math.abs(code), HttpStatus.OK.value()));
            }
        }
    }

    /**
     * 实现纪实结果的提交
     * <p>
     * 新增和修改走的是通一个接口，如果纪实结果已经存在，则走修改的方案
     *
     * @param headers
     * @param header
     * @param result
     * @param type    0：直接填写， 1：有活动流程
     * @param isAdd   true: 新增 false: 修改
     * @return
     */
    private int submitInner(HttpHeaders headers, Headers header, MeetingResultEntity result, MeetingResultForm resultForm, int type, boolean isWorker, boolean isAdd) {
        try {
            HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
            headers.set(HeaderHelper.OPERATOR_OID, sysHeader.getOid() + "");// 兼容移动端 将oid设置为uoid

            // 市值的党小组会标志 市值传过来的是否是党小组会的标志 就不走烟草逻辑
            boolean flag = transferService.fromSystemSzf(headers) && resultForm.getMeeting().getMeetingTypes().stream().map(MeetingTypeEntity::getTypeId).collect(Collectors.toList()).contains(3L);
            log.debug("查看是否是市值过来的党小组=>{},{},{}", headers, JsonUtils.toJson(sysHeader), flag);

            log.debug("填写纪实报告，是否新增：isAdd = {}, 是否下级 isWorker = {}", isAdd, isWorker);
            // 参数校验
            ReportCheck rs = this.checkReportParam(headers, header, result, resultForm, type, isWorker, isAdd);
            int code = rs.getCode();
            if (code != 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return code;
            }
            // 任务答案 2019-02-20 08:54:32 chenanshun
            if (resultForm != null && resultForm.getTopic() != null) {
                this.meetingAnswerService.answer(resultForm.getTopic(), header, type);
            }
            MeetingEntity meeting = rs.getMeeting();
            MeetingResultEntity resultEntity = rs.getResult();
            if (resultEntity == null) {
                resultEntity = new MeetingResultEntity();
            }

            //是否是新增纪实考核
            Short status = meeting.getStatus();
            //领导修改
            if (!isAdd && !isWorker) {
                code = this.leaderUpdateSubmit();
                if (code != 0) {
                    this.rollback();
                    return code;
                }
                //领导修改直接通过
                if (meetingHistoryService.isFirstCheckReport(meeting.getMeetingId())) {
                    status = MeetingCanstant.MEETING_STATUS_PASS_FIRST.shortValue();
                } else {
                    status = MeetingCanstant.MEETING_STATUS_PASS_MORE.shortValue();
                }
            }
            //新增
            if (isAdd) {
                code = this.addSubmit(headers, header, sysHeader, result, resultForm, resultEntity, meeting, type, isWorker);

                //更新活动t_meeting_task举办活动的次数
//                this.updateMeetingTaskNum(meeting.getMeetingId());

                if (code != 0) {
                    this.rollback();
                    return code;
                }
            }
            //下级修改
            if (!isAdd && isWorker) {
                code = this.updateSubmit(headers, header, sysHeader, result, resultForm, resultEntity, meeting, type, isWorker);
                if (code != 0) {
                    this.rollback();
                    return code;
                }
            }
            //更新活动的状态
            if (isWorker) {
                status = this.updateMeetingStatus(result.getMeetingId(), header.getUserId(), meeting.getStatus(), result.getMustApprove(), flag);
            } else {
                status = this.updateMeetingStatusByLeader(meeting.getMeetingId(), header.getUserId(), status);
            }

            //写活动流转日志
            MeetingHistoryEntity historyEntity = new MeetingHistoryEntity(
                    result.getMeetingId(), header.getUserId(), status.intValue(), null);
            this.meetingHistoryMapper.insertSelective(historyEntity);
            /**
             * 新需求发起的活动可以在开始前就去录入，如果是这样的情况，就需要删除原有流转日志表中预先插入的"活动举办"记录
             */
            if (meeting.getStartTime().after(new Date())) {
                Example example = new Example(MeetingHistoryEntity.class);
                example.createCriteria().andEqualTo("meetingId", meeting.getMeetingId())
                        .andEqualTo("status", MeetingCanstant.MEETING_STATUS_APPROVAL);
                meetingHistoryMapper.deleteByExample(example);
            }

            //切记置空worktask_id，以防止是被退回后的审批流程
            result.setWorkflowTaskId(null);
            result.setApproveStatus(ResultConstant.APPROVE_STATUS_PASS);

            result.setCreateTime(DateTime.now().toDate());
            result.setCreateUser(header.getUserId());
            result.setOrgId(header.getOid());

            //判断是否需要审批
            if (ResultConstant.MUST_APPROVE == result.getMustApprove() && isWorker) {
                result.setApproveStatus(ResultConstant.APPROVE_STATUS_ING);
                ApprovalBase approval = this.workflowService.buildApprovalBase(
                        result.getWorkflowId(), null, meeting.getName(), result.getMeetingId(), 7, null, header);
                long workfowTaskId = this.workflowService.addApproval(approval, headers);
                if (workfowTaskId < 0) {
                    this.rollback();
                    return -1924;
                }
                result.setWorkflowTaskId(workfowTaskId);

            } else {
                result.setSubmitTime(DateTime.now().toDate());
                result.setMustApprove(ResultConstant.NOT_MUST_APPROVE);
                result.setApproveStatus(ResultConstant.APPROVE_STATUS_PASS);
                result.setWorkflowId(null);
                result.setWorkflowName("");
                resultEntity.setMustApprove(ResultConstant.NOT_MUST_APPROVE);
                resultEntity.setApproveStatus(ResultConstant.APPROVE_STATUS_PASS);
                resultEntity.setWorkflowId(null);
                resultEntity.setWorkflowTaskId(null);
                resultEntity.setWorkflowName("  ");
                this.meetingResultMapper.updateByPrimaryKey(resultEntity);
            }

            //判断纪实考核是新增还是修改
            int oper;
            if (isAdd) {
                oper = 0;
                this.meetingResultMapper.insertSelective(result);
                //如果是有预先发起活动的，就需要判断历史result 附件
                if (type == 2) {
                    oper = 2;
                    result.setMeetingId(meeting.getMeetingId());
                }
            } else {
                oper = 1;
                result.setMeetingReslutId(resultEntity.getMeetingReslutId());
                result.setUpdateTime(DateTime.now().toDate());
                result.setLastChangeUser(header.getUserId());
                this.meetingResultMapper.updateByPrimaryKeySelective(result);
            }

            // v3.0.0 result 附件
            if (resultForm.getMeeting() != null && resultForm.getMeeting().getResultFiles() != null && resultForm.getMeeting().getResultFiles().size() > 0) {
                result.setResultFiles(resultForm.getMeeting().getResultFiles());
            }
            this.saveResultFiles(header, result, oper);

            //填写决议，并填写附件
            this.writeResolution(header, result, oper);

            //重置请假人的状态
            this.meetingLeaveService.meetingAfterRejectLeave(meeting.getMeetingId());
            if (isAdd) {
                String url = "";
                if (result.getResultFiles() != null && result.getResultFiles().size() > 0) {
                    List<MeetingResultFileEntity> l = result.getResultFiles().stream().filter(mrfe -> mrfe.getType() == 0).collect(Collectors.toList());
                    if (l != null && l.size() > 0) {
                        url = l.stream().map(MeetingResultFileEntity::getPath).collect(Collectors.joining(","));
                    }
                }
                /**
                 * 新增添加支部风采
                 */
                Map<String, Object> param = new HashMap<>();
                param.put("type", 1);
                param.put("desc", meeting.getName());
                param.put("node_time", DateUtils.dateFormat(new Date(), "yyyy-MM-dd"));
                param.put("url", url);
                param.put("org_id", sysHeader.getOid());
                thirdService.highlightAdd(headers, param);
            }
            return 0;
        } catch (ApiException e) {
            log.error("提交纪实报告出错：" + e.getResult().getCode(), e);
            throw e;
//            this.rollback();
//            return this.numberConvert(e.getResult().getCode());
        } catch (Exception e) {
            log.error("提交纪实报告出错：" + e.getMessage(), e);
            this.rollback();

        }
        return -9901;
    }

    /**
     * 回滚
     */
    private void rollback() {
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
    }

    /**
     * 填写纪实报告 参数校验
     *
     * @param headers
     * @param header
     * @param result
     * @param resultForm
     * @param type       0：直接填写， 1：有活动流程
     * @param isWorker   true: 下级 false: 上级
     * @return ReportCheck   返回一个活动的实体，因为在纪实系统里面操作的大部分是活动内容
     */
    private ReportCheck checkReportParam(HttpHeaders headers, Headers header, MeetingResultEntity result, MeetingResultForm resultForm, int type, boolean isWorker, boolean isAdd) {
        ReportCheck rs = new ReportCheck();
        MeetingEntity meeting;
        MeetingResultEntity resultEntity;

        //判断meetingId
        if (result == null || result.getMeetingId() == null) {
            log.debug("填写纪实报告：活动id必填");
            return this.rs(-9404, null, null);
        }

        //获取原始活动数据
        meeting = this.meetingMapper.selectByPrimaryKey(result.getMeetingId());
        if (meeting == null) {
            return this.rs(-9404, null, null);
        }
        // 查询活动关联的任务 2019-02-25 11:38:40 chenanshun
        meeting.setTopics(meetingTopicMapper.findByMeetingId(meeting.getMeetingId()));

        // 补录时校验任务答案是否都有填写 。后续填报与更新流程次改后都需要校验
//        if (resultForm.getTopic() == null || resultForm.getTopic().getTopics() == null || resultForm.getTopic().getTopics().size() != meeting.getTopics().size()) {
//            throw new ApiException("请检查活动内容是否填写完整", new Result<>(errors, 1933, HttpStatus.OK.value()));
//        }

        //判断当前时间是否大于活动开始时间
//        if (type == ResultConstant.HAS_MEETING && meeting.getStartTime().getTime() > DateTime.now().toDate().getTime()) {
//            return this.rs(-1934, meeting, null);
//        }

        //只有上级组织和下级组织才能操作

        if (header.getOid().longValue() != meeting.getPOrgId().longValue()
                && header.getOid().longValue() != meeting.getOrgId().longValue()) {
            return this.rs(-1930, meeting, null);
        }
        //判断活动当前状态是否有提交纪实系统的权限
        int auth = this.checkAuth(meeting.getStatus(), isWorker);
        if (auth != 0) {
            return this.rs(auth, meeting, null);
        }
        //判断是否提交过
        resultEntity = this.getResultByMeetingId(result.getMeetingId());

        //是否已经添加过纪实
        if (isAdd && resultEntity != null) {
            return this.rs(-1935, meeting, resultEntity);
        }

        //判断是否需要填决议
        if (meeting.getIsWResolution() == ResultConstant.IS_W_RESOLUTION
                && StringUtils.isEmpty(result.getResolution())) {
            return this.rs(-1926, meeting, resultEntity);
        }

        rs.setCode(0);
        rs.setMeeting(meeting);
        rs.setResult(resultEntity);

        return rs;
    }

    /**
     * 返回校验结果
     *
     * @param code
     * @param meetingEntity
     * @param resultEntity
     * @return
     */
    private ReportCheck rs(int code, MeetingEntity meetingEntity, MeetingResultEntity resultEntity) {
        ReportCheck reportDate = new ReportCheck();
        reportDate.setResult(resultEntity);
        reportDate.setMeeting(meetingEntity);
        reportDate.setCode(code);
        return reportDate;
    }

    @Data
    private static class ReportCheck {

        /**
         * 活动实体
         */
        private MeetingEntity meeting;

        /**
         * 纪实结果实体
         */
        private MeetingResultEntity result;

        /**
         * 错误码
         */
        private int code;

    }

    /**
     * 第一次提交
     *
     * @return
     */
    private int addSubmit(HttpHeaders headers, Headers header, HeaderHelper.SysHeader sysHeader,
                          MeetingResultEntity result,
                          MeetingResultForm resultForm,
                          MeetingResultEntity resultEntity,
                          MeetingEntity meeting,
                          int type,
                          boolean isWorker) {
        //更新活动
        int code = this.updateMeeting(headers, header, sysHeader,
                result, resultForm, resultEntity, meeting, type, isWorker);
        if (code != 0) {
            return code;
        }
        MeetingEntity mt = resultForm.getMeeting();
        if (mt == null || (CollectionUtils.isEmpty(mt.getParticipantUsers()) && CollectionUtils.isEmpty(mt.getAttendUsers()))) {
            mt = meetingMapper.findByIdAndOrg(meeting.getMeetingId(), meeting.getOrgId());
        }
        pushService.sendSmsToLeader(sysHeader, mt, HttpLogAspect.getSSLog());
        return 0;
    }

    /**
     * 更新活动
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateSubmit(HttpHeaders headers, Headers header, HeaderHelper.SysHeader sysHeader,
                            MeetingResultEntity result,
                            MeetingResultForm resultForm,
                            MeetingResultEntity resultEntity,
                            MeetingEntity meeting,
                            int type,
                            boolean isWorker) {
        //判断是否需要审批，取消之前的审批
        if (resultEntity.getMustApprove() == ResultConstant.MUST_APPROVE
                && ResultConstant.canUndoApprove(meeting.getStatus())) {
            if (!this.workflowService.undoApprove(resultEntity.getWorkflowTaskId(), headers)) {
                return -1929;
            }
        }

        //更新活动
        int code = this.updateMeeting(headers, header, sysHeader,
                result, resultForm, resultEntity, meeting, type, isWorker);
        return code;
    }

    /**
     * 更新活动
     *
     * @param headers
     * @param header
     * @param sysHeader
     * @param result
     * @param resultForm
     * @param resultEntity
     * @param meeting
     * @param type
     * @param isWorker
     * @return
     */
    private int updateMeeting(HttpHeaders headers, Headers header, HeaderHelper.SysHeader sysHeader,
                              MeetingResultEntity result,
                              MeetingResultForm resultForm,
                              MeetingResultEntity resultEntity,
                              MeetingEntity meeting,
                              int type,
                              boolean isWorker) {
        //有活动流程，则需要更新活动的信息
        if (type == ResultConstant.HAS_MEETING) {
            try {
                int code = this.meetingService.updateMeeting(headers, sysHeader, resultForm.getMeeting(), (short) 2);
                if (code < 0) {
                    return -1928;
                }
            } catch (ApiException e) {
                log.error("填写纪实报告，修改活动内容已知出错：" + e.getMessage(), e);
                return this.numberConvert(e.getResult().getCode());
            } catch (Exception e) {
                log.error("填写纪实报告，修改活动内容出错：" + e.getMessage(), e);
                return -1928;
            }

            //写变更日志
            this.meetingResultHsService.insertReportHs(
                    JsonUtils.toJson(meeting), JsonUtils.toJson(resultForm), result.getMeetingId(),
                    header.getUserId(), "有活动流程的纪实考核，提交纪实报告，" + (isWorker ? "下级" : "领导")
            );
        }
        return 0;
    }

    /**
     * 领导更新
     *
     * @return
     */
    private int leaderUpdateSubmit() {
        return 0;
    }


    /**
     * 校验活动的当前状态是否能被当前人操作
     *
     * @param meetingStatus 活动的状态
     * @param isWorker      是否是下级
     * @return
     */
    private int checkAuth(short meetingStatus, boolean isWorker) {
        if (isWorker) {
            //3：活动待举办（待填报结果 5：填报审查中（第一次） 6：填报未通过（第一次） 8：退回 10：填报审查中（第一次之后） 11：填报未通过（第一次之后） 15:已撤回
            //if()
            if (meetingStatus == MeetingCanstant.MEETING_STATUS_SOON_START.shortValue()
                    || meetingStatus == MeetingCanstant.MEETING_STATUS_SUBMIT_FIRST.shortValue()
                    || meetingStatus == MeetingCanstant.MEETING_STATUS_SUBMIT_FIRST_NOT_PASS.shortValue()
                    || meetingStatus == MeetingCanstant.MEETING_STATUS_BACK.shortValue()
                    || meetingStatus == MeetingCanstant.MEETING_STATUS_SUBMIT_MORE.shortValue()
                    || meetingStatus == MeetingCanstant.MEETING_STATUS_SUBMIT_MORE_NOT_PSSS.shortValue()
                    || meetingStatus == MeetingCanstant.MEETING_STATUS_REVOKE.shortValue()
            ) {
                return 0;
            }
        } else {
            //7：已提交 12：待复核
            if (meetingStatus == MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue()
                    || meetingStatus == MeetingCanstant.MEETING_STATUS_SUBMIT_CHECK.shortValue()
            ) {
                return 0;
            }
        }
        return -1931;
    }

    /**
     * 领导直接更新状态
     *
     * @param meetingId
     * @param uid
     * @param status
     * @return
     */
    private Short updateMeetingStatusByLeader(long meetingId, long uid, short status) {
        this.meetingService.updateMeetingStatus(status, meetingId, uid);
        return status;
    }

    /**
     * 更新活动的状态
     *
     * @param meetingId
     * @param uid
     * @param status      活动状态
     * @param mustApprove 是需要审批 0：不需要 1：需要
     */
    private Short updateMeetingStatus(long meetingId, long uid, short status, long mustApprove, boolean szfFlag) {
        //判断是否是第一次提交
        //默认第一次提交，并且审批通过
        boolean isFirst = this.meetingHistoryService.isFirstCommitReport(meetingId);
        Short stat;
        if (mustApprove == ResultConstant.MUST_APPROVE) {
            //需要审批
            if (isFirst) {
                stat = MeetingCanstant.MEETING_STATUS_SUBMIT_FIRST.shortValue();
            } else {
                stat = MeetingCanstant.MEETING_STATUS_SUBMIT_MORE.shortValue();
            }
        } else {
            //不需要审批
            if (isFirst) {
                stat = MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue();
            } else {
                stat = MeetingCanstant.MEETING_STATUS_SUBMIT_CHECK.shortValue();
            }
        }
        // 更新任务完成状态
        // 2018-12-21 14:09:24 chenanshun
        this.meetingTaskService.upTaskStatus(meetingId, stat, szfFlag);
        this.meetingService.updateMeetingStatus(stat, meetingId, uid);
        return stat;
    }

    /**
     * 保存纪实情况附件
     *
     * @param header
     * @param result
     * @param oper   0：新增 1：修改  2: 发起了活动的新增
     */
    private void saveResultFiles(Headers header, MeetingResultEntity result, int oper) {
        List<MeetingResultFileEntity> resultFiles = new ArrayList<>();
        List<MeetingResultFileEntity> oldResultFiles = new ArrayList<>();
        List<MeetingResultFileEntity> newResultFiles = new ArrayList<>();
        //修改
        if (oper == 1 || oper == 2) {
            // 查询历史数据
            Example example = new Example(MeetingResultFileEntity.class);
            example.createCriteria().andEqualTo("meetingId", result.getMeetingId());
            oldResultFiles = this.meetingResultFileMapper.selectByExample(example);
        }
        if (result != null && CollectionUtils.isNotEmpty(result.getResultFiles())) {
            newResultFiles = result.getResultFiles();
        }
        resultFiles = CheckDataOperateUtils.check(newResultFiles, oldResultFiles, MeetingResultFileEntity.class);
        List<MeetingResultFileEntity> addResultFiles = new ArrayList<>();
        List<Long> delResultFiles = new ArrayList<>();
        Date date = DateTime.now().toDate();
        resultFiles.forEach(rf -> {
            if (rf.getOperateTag() == 1) {
                rf.setMeetingId(result.getMeetingId());
                rf.setIsDel(0);
                rf.setCreateTime(date);
                rf.setCreateUser(header.getUserId());
                addResultFiles.add(rf);
            } else if (rf.getOperateTag() == 2 && rf.getMeetingResultFileId() != null) {
                delResultFiles.add(rf.getMeetingResultFileId());
            }
        });
        if (CollectionUtils.isNotEmpty(addResultFiles)) {
            meetingResultFileMapper.insertList(addResultFiles);
        }
        if (CollectionUtils.isNotEmpty(delResultFiles)) {
            Example example = new Example(MeetingResultFileEntity.class);
            example.createCriteria().andIn("meetingResultFileId", delResultFiles)
                    .andEqualTo("meetingId", result.getMeetingId());
            MeetingResultFileEntity up = new MeetingResultFileEntity();
            up.setIsDel(1);
            up.setUpdateTime(DateTime.now().toDate());
            up.setLastChangeUser(header.getUserId());
            meetingResultFileMapper.updateByExampleSelective(up, example);
        }
    }

    /**
     * 填写决议
     *
     * @param header
     * @param result
     * @param oper   0：新增 1：修改
     */
    private void writeResolution(Headers header, MeetingResultEntity result, int oper) {
        Long meetingId = result.getMeetingId();
        MeetingEntity meeting = new MeetingEntity();
        Date date = DateTime.now().toDate();
        meeting.setMeetingId(meetingId);
        meeting.setUpdateTime(date);
        meeting.setLastChangeUser(header.getUserId());
        meeting.setResolution(result.getResolution());
        List<MeetingResolutionFileEntity> addList = new ArrayList<>();
        //修改先删除
        if (oper == 1) {
            Example example = new Example(MeetingResolutionFileEntity.class);
            example.createCriteria().andEqualTo("meetingId", meetingId);
            this.meetingResolutionFileMapper.deleteByExample(example);
        }
        if (result.getFiles() != null) {
            result.getFiles().forEach(file -> {
                file.setIsDel(0);
                file.setCreateTime(date);
                file.setMeetingId(meetingId);
                file.setCreateUser(header.getUserId());

                addList.add(file);
            });
            //新增附件
            if (!ListUtils.isEmpty(addList)) {
                this.meetingResolutionFileMapper.insertList(addList);
            }
        }

        //修改活动
        this.meetingMapper.updateByPrimaryKeySelective(meeting);


    }


    /**
     * 获取活动的纪实情况
     *
     * @param meetingId
     * @return
     */
    public MeetingResultEntity getResultByMeetingId(Long meetingId) {
        if (meetingId == null || meetingId == 0) {
            return null;
        }
        Example example = new Example(MeetingResultEntity.class);
        example.createCriteria().andEqualTo("meetingId", meetingId);
        return this.meetingResultMapper.selectOneByExample(example);
    }


    /**
     * 更新工作流状态
     *
     * @param meetingId
     * @param workflowTaskId
     * @param userId
     * @param status
     */
    @Transactional
    public void updateStatusByWorkflow(long meetingId, long workflowTaskId, long userId,
                                       int status, String reason, Long reUid) {
        log.debug("纪实考核审批回调入参：meetingId={}, status={}, userId={}, workflowTaskId={}, reUid={}, reason={} ",
                meetingId, status, userId, workflowTaskId, reUid, reason);
        //step1、更新活动状态
        //step2、更新活动纪实表审批流程tast_id
        //step3、更新纪实表提交时间
        Integer meetingStat = this.updateMeetingStatusInCallback(meetingId, status, userId, workflowTaskId, reUid);

        //step4、插入活动历史记录
        MeetingHistoryEntity historyEntity = new MeetingHistoryEntity();
        historyEntity.setStatus(meetingStat);
        historyEntity.setMeetingId(meetingId);
        if (status == 1) {
            historyEntity.setCreateUser(userId);
        } else {
            historyEntity.setReason(reason);
            historyEntity.setCreateUser(reUid);
        }
        historyEntity.setCreateTime(DateTime.now().toDate());

        //纪录下更改日志
        StringBuilder sb = new StringBuilder();
        sb.append("meetingId=");
        sb.append(meetingId);
        sb.append("&status=");
        sb.append(status);
        sb.append("$userId=");
        sb.append(userId);
        sb.append("&workflowTaskId=");
        sb.append(workflowTaskId);
        sb.append("&reUid=");
        sb.append(reUid);
        sb.append("&reason=");
        sb.append(reason);
        this.meetingResultHsService.insertReportHs("", sb.toString(), userId, meetingId, "纪实考核审批回调");

        this.meetingHistoryMapper.insertSelective(historyEntity);
    }

    /**
     * 更新活动的状态
     *
     * @param meetingId
     * @param status
     */
    private Integer updateMeetingStatusInCallback(long meetingId, int status, long userId, long workflowTaskId, Long reUid) {
        Integer meetingStat = null;
        MeetingEntity meeting = new MeetingEntity();
        MeetingResultEntity resultEntity = new MeetingResultEntity();
        //判断是否是第一次提交审批
        boolean flag = this.meetingHistoryService.isFirstCommitReportInCallback(meetingId);
        //审批通过
        if (status == 1) {
            if (flag) {
                meetingStat = MeetingCanstant.MEETING_STATUS_SUBMIT;
            } else {
                meetingStat = MeetingCanstant.MEETING_STATUS_SUBMIT_CHECK;
            }
            //更新纪实的提交时间
            resultEntity.setSubmitTime(DateTime.now().toDate());
            resultEntity.setApproveStatus(ResultConstant.APPROVE_STATUS_PASS);
            resultEntity.setLastChangeUser(userId);
        } else if (status == 3 || status == 2) {
            //审批不通过
            if (flag) {
                meetingStat = MeetingCanstant.MEETING_STATUS_SUBMIT_FIRST_NOT_PASS;
            } else {
                meetingStat = MeetingCanstant.MEETING_STATUS_SUBMIT_MORE_NOT_PSSS;
            }
            resultEntity.setApproveStatus(ResultConstant.APPROVE_STATUS_NO_PASS);
            resultEntity.setLastChangeUser(reUid);
        }
        meeting.setMeetingId(meetingId);
        meeting.setStatus(meetingStat.shortValue());
        meeting.setUpdateTime(DateTime.now().toDate());
        meeting.setLastChangeUser(userId);
        this.meetingMapper.updateByPrimaryKeySelective(meeting);
        // 更新任务完成状态
        // 2018-12-21 14:09:24 chenanshun
        this.meetingTaskService.upTaskStatus(meetingId, meetingStat.shortValue(), false);
        resultEntity.setMeetingId(meetingId);
        resultEntity.setWorkflowTaskId(workflowTaskId);
        resultEntity.setUpdateTime(DateTime.now().toDate());

        this.updateByMeetingId(resultEntity);

        return meetingStat;
    }

    /**
     * 根据活动id更新活动的纪实情况
     *
     * @param meetingResultEntity
     */
    private void updateByMeetingId(MeetingResultEntity meetingResultEntity) {
        Example example = new Example(MeetingResultEntity.class);
        example.createCriteria().andEqualTo("meetingId", meetingResultEntity.getMeetingId());
        this.meetingResultMapper.updateByExampleSelective(meetingResultEntity, example);
    }

    /**
     * 直接提交纪实考核表 -- 下级
     *
     * @param headers
     * @param header
     * @param resultForm
     * @return
     * @Param szf:是否是红岩魂传入
     */
    // 2019-02-19 09:40:20 chenanshun
    @Transactional(rollbackFor = Exception.class)
    public Long add(HttpHeaders headers, Headers header, MeetingAndResultForm resultForm, Boolean szf) {
        //前端已无此逻辑，后端这段代码不能屏蔽，屏蔽可能影响到其他逻辑
        addTopic(header, resultForm);
        //添加一个活动
        resultForm.getMeeting().setMustApprove((short) 0);
        HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
        var meeting = this.meetingService.addMeeting(headers, sysHeader, resultForm.getMeeting(), (short) 2, szf);

        //工作任务绑定关系
        List<MeetingTopicEntity> meetingTopiList = this.meetingTopicMapper.findByMeetingId(meeting.getMeetingId());
        List<MeetingReportTopicContentForm> topics = resultForm.getTopic().getTopics();
        topics.forEach(topic -> {
            for (MeetingTopicEntity meetingTopicEntity : meetingTopiList) {
                if (topic.getTopicId().longValue() == meetingTopicEntity.getTopicId().longValue()) {
                    topic.setMeetingTopicId(meetingTopicEntity.getMeetingTopicId());
                }
            }
        });

        //新增任务的答案
        MeetingResultForm meetingResultForm = new MeetingResultForm();
        MeetingReportForm reportForm = new MeetingReportForm();
        meetingResultForm.setTopic(reportForm);
        meetingResultForm.setMeeting(resultForm.getMeeting());
        reportForm.setTopics(topics);
        reportForm.setMeetingId(meeting.getMeetingId());

//        this.meetingAnswerService.answer(reportForm, header, ResultConstant.HAS_NO_MEETING);
        //提交纪实结果
        MeetingResultEntity result = resultForm.getResult();
        result.setMeetingId(meeting.getMeetingId());
        int code = this.submitInner(headers, header, result, meetingResultForm, ResultConstant.HAS_NO_MEETING, true, true);
        if (code != 0) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            return code;
            return meeting.getMeetingId();
        }
        //写变更日志
        this.meetingResultHsService.insertReportHs(
                "", JsonUtils.toJson(resultForm), result.getMeetingId(),
                header.getUserId(), "直接填写纪实报告"
        );

        //调用补学相关的业务
        meetingWaitSignService.addSignInfo(meeting.getMeetingId(), sysHeader);
        /* 验证在life_id不存在的情况下 是否存在民主生活会类型 */
        meetingPeoplePartyLifeService.checkExistLifeType(resultForm.getMeeting());

        // 提交报告，更新首页缓存
        indexService.collectRedis();
        // 删除草稿
//        meetingDraftService.deleteDraft(sysHeader.getRegionId(),resultForm.getMeeting().getOrgId() ,sysHeader.getUserId());

        //调用增加积分接口
        MeetingEntity mt = resultForm.getMeeting();
        mt.setMeetingId(meeting.getMeetingId());
//        meetingScoreService.addMeetingScore(header.getRegionId(),mt);   丢入队列处理  tc 2022-01-25
        MeetingScoreMQVo msmq = new MeetingScoreMQVo(header.getRegionId(), mt, false, null, Config.MeetingScoreConf.MQ_ADD_MEETING_SCORE);
        producer.send(Config.RabbitmqConf.DIRECT_EXCHANGE_NAME, rabbitmqQueueConfig.getMeetingScoreRouting(), JsonUtils.toJson(msmq));


        //如果是组织生活会或者民主生活会，则创建关联关系
        MeetingEntity meetingEntity = resultForm.getMeeting();
        if (null == meetingEntity) {
//            return 0;
            return meeting.getMeetingId();
        }
        if (meetingEntity.getSourceType() != null && 1 == meetingEntity.getSourceType()) {
            LifeEntity lifeEntity = new LifeEntity();
            lifeEntity.setLifeId(meetingEntity.getLifeId());
            lifeEntity.setMeetingId(meeting.getMeetingId());
            lifeEntity.setUpdateTime(LocalDateTime.now());
            lifeEntity.setLastChangeUser(header.getUserId());
            lifeService.createRelation(lifeEntity);
        } else if (meetingEntity.getSourceType() != null && 2 == meetingEntity.getSourceType()) {
            OrgLifeEntity lifeEntity = new OrgLifeEntity();
            lifeEntity.setLifeId(meetingEntity.getLifeId());
            lifeEntity.setMeetingId(meeting.getMeetingId());
            lifeEntity.setUpdateTime(LocalDateTime.now());
            lifeEntity.setLastChangeUser(header.getUserId());
            orgLifeService.createRelation(lifeEntity);
        }
//        return 0;
        return meeting.getMeetingId();

    }

    /**
     * 补录时添加任务
     *
     * @param header     Headers
     * @param resultForm MeetingAndResultForm
     */
    private void addTopic(Headers header, MeetingAndResultForm resultForm) {
        // 初始化resultForm
        if (resultForm.getTopic() == null) {
            resultForm.setTopic(new MeetingReportForm());
        }
        if (resultForm.getTopic().getTopics() == null) {
            resultForm.getTopic().setTopics(new ArrayList<>());
        }
        // 1.添加任务
        // 2.将新添加的任务放到resultForm.topic中
        if (resultForm.getAddTopics() != null && !resultForm.getAddTopics().isEmpty()) {
            for (TopicEntity topic : resultForm.getAddTopics()) {
                topicService.addTopicByMeeting(header, resultForm.getMeeting(), topic);
                // 封装meetingReportTopicContentForm 新添加的任务对应的答案
                MeetingReportTopicContentForm meetingReportTopicContentForm = new MeetingReportTopicContentForm();
                resultForm.getTopic().getTopics().add(meetingReportTopicContentForm);
                meetingReportTopicContentForm.setTopicId(topic.getTopicId());// 设置topicId
                // 答案
                if (topic.getContents() != null && !topic.getContents().isEmpty()) {
                    List<MeetingReportAnswerForm> contents = new ArrayList<>();
                    meetingReportTopicContentForm.setContents(contents);// set List<MeetingReportAnswerForm>

                    // 封装 MeetingReportAnswerForm
                    for (TopicContentEntity ce : topic.getContents()) {
                        ce.setContentId(ce.getContentId());
                        MeetingReportAnswerForm maf = new MeetingReportAnswerForm();
                        contents.add(maf);
                        maf.setContentId(ce.getContentId());
                        maf.setContent(ce.getAnsCnt());
                        maf.setType(ce.getType());
                        maf.setFiles(ce.getAnsFiles());
                        if (ce.getType() == 2 || ce.getType() == 3) {// 单选或多项
                            List<Integer> answer = new ArrayList<>();
                            // 选项(seq)改为选项的id
                            for (Long seq : ce.getAnswer()) {
                                ce.getOpts().stream().filter(opt -> opt.getSeq().longValue() == seq).findFirst().ifPresent(t -> answer.add(t.getOptsId().intValue()));
                            }
                            maf.setOpts(answer);
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取纪实结果详情
     *
     * @param headers
     * @param meetingId chenanshun 2018年12月11日 11:41:45 未填写纪实结果，审批流程默认返回不需要审批
     */
    public MeetingEntity detail(HttpHeaders headers, Long meetingId, short isEdit) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        MeetingEntity meetingEntity = this.meetingService.detail(sysHeader, meetingId, isEdit);

        //重置活动的审批流程
        MeetingResultEntity resultEntity = this.getResultByMeetingId(meetingId);
        if (resultEntity != null) {
            meetingEntity.setMustApprove(resultEntity.getMustApprove().shortValue());
            meetingEntity.setWorkflowId(resultEntity.getWorkflowId());
            meetingEntity.setWorkflowName(resultEntity.getWorkflowName());
            meetingEntity.setWorkflowTaskId(resultEntity.getWorkflowTaskId());
        } else {
            meetingEntity.setMustApprove((short) 0);
            meetingEntity.setWorkflowId(null);
            meetingEntity.setWorkflowName(null);
            meetingEntity.setWorkflowTaskId(null);
        }
        return meetingEntity;
    }

    /**
     * 转换成负数
     *
     * @param value
     * @return
     */
    private int numberConvert(int value) {
        return Integer.valueOf("-" + value);
    }

    /**
     * @param header
     * @param oper             查询类型，1：纪实结果检查 2：纪实结果追踪
     * @param orgName          组织名称
     * @param meetingClass     所属类别
     * @param meetingTypes     活动类型
     * @param meetingStartTime 活动时间（开始）
     * @param meetingEndTime   活动时间（结束）
     * @param submitStartTime  提交时间（开始）
     * @param submitEndTime    提交时间（结束）
     * @param pageBean
     * @return
     */
    public Page<MeetingResultListForm> page(Headers header,
                                            int oper,
                                            String orgName,
                                            Integer meetingClass,
                                            String meetingTypes,
                                            String meetingStartTime,
                                            String meetingEndTime,
                                            String submitStartTime,
                                            String submitEndTime,
                                            PageBean pageBean) {
        return PageHelper.startPage(pageBean.getPageNo(), pageBean.getPageSize())
                .doSelectPage(() -> meetingResultMapper.page(header.getOid(), oper, orgName, meetingClass, meetingTypes, meetingStartTime, meetingEndTime, submitStartTime, submitEndTime));
    }

    /**
     * 2019-02-14 17:52:06 陈安顺 添加分布式锁
     * 纪实结果检查（通过、退回）
     *
     * @param headers                请求头
     * @param meetingResultCheckForm 请求参数
     * @return Boolean
     */
    @Transactional
    public Boolean check(HttpHeaders headers, MeetingResultCheckForm meetingResultCheckForm) {
        //判断是否是红岩魂传入的,如果是红岩魂传入的，需要设置user_id org_id,还需要将记录人等手机号转为烟草的user_id
        if (transferService.fromSystemSzf(headers)) {
            transferService.transferHeaders(headers);
        }
        // 1.获取锁 10s
        String uuid = UUID.randomUUID().toString();
        String key = "LOCK_" + RedisConstant.MEETING_RESULT_CHECK + meetingResultCheckForm.getMeetingId();
        //获取redis分布式锁
        boolean lock = RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, key, uuid, 10 * 1000);
        if (!lock) {
            throw new ApiException("有用户正在执行check操作", new Result<>(errors, 1827, HttpStatus.OK.value()));
        }
        try {
            Headers header = this.headerService.bulidHeader(headers);
            // 2019-02-12 15:13:29 chenanshun
            // 当“检查通过”和“退回”操作时，需要校验该活动是否由当前组织发起，若是则提示“不能对本组织的活动进行检查或退回，请通知上级组织操作”
            long meetingId = meetingResultCheckForm.getMeetingId();
            MeetingEntity meetingEntity = this.meetingMapper.selectByPrimaryKey(meetingId);
            if (meetingEntity == null) {
                throw new ApiException("活动未找到", new Result<>(errors, 9404, HttpStatus.OK.value(), StringCanstant.ACTIVITY));
            }
            if (meetingEntity.getOrgId().equals(header.getOid())) {
                throw new ApiException("不能对本组织的活动进行检查或退回", new Result<>(errors, 1936, HttpStatus.OK.value()));
            }
            short oper = meetingResultCheckForm.getOper();
            String reason = meetingResultCheckForm.getReason();

            long userId = header.getUserId();
            Date now = DateTime.now().toDate();
            Short newStatus = MeetingCanstant.MEETING_STATUS_PASS_FIRST.shortValue();// 默认通过
            MeetingHistoryEntity meetingHistoryEntity = new MeetingHistoryEntity();
            meetingHistoryEntity.setReason(reason);
            if (oper == 2) {
//                if (StringUtils.isEmpty(reason)) {
//                    throw new ApiException("退回理由不能为空", new Result<>(errors, 1804, HttpStatus.OK.value(), "退回理由不能为空"));
//                }
                newStatus = MeetingCanstant.MEETING_STATUS_BACK.shortValue();
            }

            Short oldStatus = meetingEntity.getStatus();
            // 1.查询当前组织的下级组织
            // 2.判断下级组织是否包含活动的发起组织
            // 3.不包含则无权限修改
            /*
             *  1.组织id不能为null
             *  2.用户id不能为null
             */
            HeaderHelper.SysHeader sysHeader = ControllerHelper.getSysHeader(headers, errors);
            List<OrganizationBase> allChildOrg = userService.findAllChildOrg(sysHeader, sysHeader.getOid());
            if (allChildOrg == null || allChildOrg.isEmpty() || allChildOrg.stream().noneMatch(org -> org.getOrgId().equals(meetingEntity.getOrgId()))) {
                throw new ApiException("无权限审批此纪实结果", new Result<>(errors, 1826, HttpStatus.OK.value()));
            }
            if (oper == 1) {
                // 通过。状态为提交或者待复核
                if (oldStatus != MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue()
                        && oldStatus != MeetingCanstant.MEETING_STATUS_SUBMIT_CHECK.shortValue()) {
                    throw new ApiException("活动状态不匹配，无法执行审批操作", new Result<>(errors, 1827, HttpStatus.OK.value()));
                }
            } else {
                // 退回。状态为提交或者待复核或检查通过
                if (oldStatus != MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue()
                        && oldStatus != MeetingCanstant.MEETING_STATUS_SUBMIT_CHECK.shortValue()
                        && oldStatus != MeetingCanstant.MEETING_STATUS_PASS_FIRST.shortValue()
                        && oldStatus != MeetingCanstant.MEETING_STATUS_PASS_MORE.shortValue()) {
                    throw new ApiException("活动状态不匹配，无法执行审批操作", new Result<>(errors, 1827, HttpStatus.OK.value()));
                }
            }

            if (oper == 1) {
                // 第一次审核就通过
                if (oldStatus == MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue()) {
                    newStatus = MeetingCanstant.MEETING_STATUS_PASS_FIRST.shortValue();
                }
                // 多次审核后通过
                else {
                    newStatus = MeetingCanstant.MEETING_STATUS_PASS_MORE.shortValue();
                }
            }
            MeetingEntity newMeetingEntity = new MeetingEntity();
            newMeetingEntity.setStatus(newStatus);
            newMeetingEntity.setUpdateTime(now);
            newMeetingEntity.setLastChangeUser(userId);
            newMeetingEntity.setMeetingId(meetingId);
            meetingMapper.updateByPrimaryKeySelective(newMeetingEntity);

            meetingHistoryEntity.setMeetingId(meetingId);
            meetingHistoryEntity.setStatus(newStatus.intValue());
            meetingHistoryEntity.setCreateUser(userId);
            meetingHistoryEntity.setCreateTime(now);
            meetingHistoryMapper.insert(meetingHistoryEntity);
            //退回调用补学的方法
            if (oper == 2) {
                //调用退回后扣减人员积分   tc 2021-12-09
                MeetingEntity mt = meetingMapper.findByIdAndOrg(meetingId, sysHeader.getOid());
                log.debug("调用退回后扣减人员积分 regionId={} meeting={} isCancel={}", sysHeader.getRegionId(), mt, false);
//                meetingScoreService.reduceMeetingScore(sysHeader.getRegionId(),mt,false);
                //调用退回后扣减人员积分  丢入队列
                MeetingScoreMQVo msmq = new MeetingScoreMQVo(sysHeader.getRegionId(), mt, false, null, Config.MeetingScoreConf.MQ_REDUCE_MEETING_SCORE);
                producer.send(Config.RabbitmqConf.DIRECT_EXCHANGE_NAME, rabbitmqQueueConfig.getMeetingScoreRouting(), JsonUtils.toJson(msmq));

                //回退补学的状态
                meetingWaitSignService.backSignInfo(newMeetingEntity.getMeetingId(), sysHeader);
            }
            // 上级审核后，更新首页缓存
            indexService.collectRedis();
            if (oper == 2) {
                // 2023-09-25 退回后处理第一议题相关数据
                // 移除本组织的第一议题数据
                var topPriorityMeeting = new TopPriorityMeeting();
                topPriorityMeeting.setMeetingId(meetingEntity.getMeetingId());
                topPriorityMeeting.setAssociatedOrg(meetingEntity.getOrgId());
                topPriorityService.removeAssociatedAndMeetings(topPriorityMeeting);
            }
            return true;
        } finally {
            // 释放锁
            RedisLockUtil.releaseDistributedLock(stringRedisTemplate, key, uuid);
        }

    }


    /**
     * 检查活动的任务是否都已经回答
     *
     * @param meetingId
     */
    private void checkTopicIsAnswer(Long meetingId) {
        List<MeetingTopicEntity> list = this.meetingTopicMapper.findByMeetingId(meetingId);
        if (!ListUtils.isEmpty(list)) {
            list.forEach(entity -> {
                if (entity.getStatus() == ResultConstant.MEETING_TOPIC_STATUS_NO) {
                    throw new ApiException("请检查活动内容是否填写完整", new Result<>(errors, 1933, HttpStatus.OK.value()));
                }
            });
        }
    }
}