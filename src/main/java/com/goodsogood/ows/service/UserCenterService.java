package com.goodsogood.ows.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.SaasUtils;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:21
 */
@Service
@Log4j2
public class UserCenterService {
    @Value("${tog-services.user-center}")
    private String userCenter;

    private final RestTemplate restTemplate;
    private final HeaderService headerService;
    private final Errors errors;

    public UserCenterService(RestTemplate restTemplate, HeaderService headerService, Errors errors) {
        this.restTemplate = restTemplate;
        this.headerService = headerService;
        this.errors = errors;
    }

    /**
     * 移动端请求时，指定用户所属的oid
     */
    public List<Long> getUserOidByH5(HttpHeaders headers, HeaderHelper.SysHeader sysHeader) {
        List<Long> oids = new ArrayList<>();
        oids.add(headerService.bulidHeader(headers).getOid());
        return oids;
    }

    /**
     * 拉取下级组织信息
     *
     * @param orgId 组织id
     */
    public List<OrganizationBase> findAllChildOrg(HeaderHelper.SysHeader sysHeader, Long orgId) {
        String url = String.format("http://%s/org/find-all-child-org?org_id=%s", userCenter, orgId);
        List<OrganizationBase> list = null;
        try {
            list = RestTemplateHelper.get(sysHeader, url, new TypeReference<Result<List<OrganizationBase>>>() {
            });
        } catch (Exception e) {
            log.debug("拉取下级组织信息错误日志->" + url + "|" + e);
        }
        log.debug("拉取下级组织信息错误日志一->{},{}" , url,list);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 拉取下级组织信息(含删除)
     *
     * @param orgId      组织id
     * @param includeDel 是否包含删除组织 	1-是 0-否 默认0
     */
    public List<OrganizationBase> findAllChildOrg(HeaderHelper.SysHeader sysHeader, Long orgId, Integer includeDel) {
        String url = String.format("http://%s/org/find-all-child-org?org_id=%s&include_del=%s", userCenter, orgId, includeDel);
        List<OrganizationBase> list = RestTemplateHelper.get(sysHeader, url, new TypeReference<Result<List<OrganizationBase>>>() {
        });
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 拉取下级组织信息包含自己
     *
     * @param orgId 组织id
     */
    public List<OrganizationBase> findAllChildOrgInclude(HeaderHelper.SysHeader sysHeader, Long orgId) {
        String url = String.format("http://%s/org/find-all-child-org?org_id=%s&is_include=1", userCenter, orgId);
        List<OrganizationBase> list = RestTemplateHelper.get(sysHeader, url, new TypeReference<Result<List<OrganizationBase>>>() {
        });
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 获取所有组织
     */
    public List<OrganizationBase> allOrg(HeaderHelper.SysHeader sysHeader) {
        log.debug("allOrg 获取所有组织信息.");
        String url = String.format("http://%s/org/find-org-by-all", userCenter);
        return RestTemplateHelper.get(sysHeader, url, new TypeReference<Result<List<OrganizationBase>>>() {
        });
    }

    /**
     * 根据组织类型查询所有相关组织
     */
    public <T extends OrgForm> List<T> findOrgBy(HeaderHelper.SysHeader sysHeader, final MeetingAutoDistributionReqForm reqForm) {
        log.debug("allOrg 获取所有组织信息.");
        String url = String.format("http://%s/leader/find-auto-orgs", userCenter);
        List<OrgTypeForm> res = RestTemplateHelper.post(sysHeader, url, reqForm, new TypeReference<Result<List<OrgTypeForm>>>() {
        });
        return res == null ? new ArrayList<>() : (List<T>) res;
    }

    /**
     * 调获取组织与领导班子(考核二期)
     * 可能存在一个人是多个组织的领导班子
     */
    public List<UserLeaderCallBackForm> findLeaders(HeaderHelper.SysHeader sysHeader, Date endTime, List<Long> orgIds, String name) {

        String url = String.format("http://%s/leader/find-org-creates?end_time=%s&name=%s",
                userCenter, DateUtils.dateFormat(endTime, "yyyy-MM-dd HH:mm:ss"), StringUtils.isNotBlank(name) ? name : "");
        return RestTemplateHelper.post(sysHeader, url, orgIds, new TypeReference<Result<List<UserLeaderCallBackForm>>>() {
        });
    }


    /**
     * 根据查询ID查询组织基本信息
     */
    public List<OrganizationBase> findOrgByList(List<Long> orgIds, HeaderHelper.SysHeader header) {
        StopWatch stopWatch = new StopWatch("根据查询ID查询组织基本信息");
        String url = String.format("http://%s/org/find-org-by-list", userCenter);
        HttpHeaders headers = new HttpHeaders();
        headers.add("_tk", "-1");
        headers.add("_region_id", header.getRegionId().toString());

        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        stopWatch.start("远程调用用户中心");
        Map<String, Object> body = new HashMap<>();
        body.put("id_list", orgIds);
        log.debug("根据查询ID查询组织基本信息 url->{}, body->{}", url, orgIds);
        List<OrganizationBase> res = null;
        try {
            res = RemoteApiHelper.post(
                    restTemplate,
                    url,
                    body,
                    headers,
                    new TypeReference<>() {
                    });
        } catch (IOException e) {
            log.error("根据查询ID查询组织基本信息失败 url->{}, error->{}", url, e.getMessage());
        }
        stopWatch.stop();
        log.debug("根据查询ID查询组织基本信息 url->{}, res->{}", url, res);
        log.debug("根据查询ID查询组织基本信息\n{}", stopWatch.prettyPrint());
        return res;
    }

    /**
     * 根据查询ID查询组织基本信息
     */
    public List<OrgSecretaryForm> selectOrgSecretary(List<Long> orgIds, String year) {
        StopWatch stopWatch = new StopWatch("根据查询ID查询组织书记");
        String url = String.format("http://%s/select-org-secretary", userCenter);
        HttpHeaders headers = new HttpHeaders();
        headers.add("_tk", "-1");
        headers.add("_region_id", SaasUtils.getRegionId() + "");
        SelectOrgSecretaryQueryForm queryForm = SelectOrgSecretaryQueryForm.builder().orgIds(orgIds).year(year).build();
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        stopWatch.start("远程调用用户中心");
        log.debug("根据查询ID查询组织书记 url->{}, orgIds->{}, year->{}", url, orgIds, year);
        List<OrgSecretaryForm> res = null;
        try {
            res = RemoteApiHelper.post(restTemplate,
                    url,
                    queryForm,
                    headers,
                    new TypeReference<Result<List<OrgSecretaryForm>>>() {
                    });
        } catch (IOException e) {
            log.error("根据查询ID查询组织书记 url->{}, error->{}", url, e.getMessage());
        }
        stopWatch.stop();
        log.debug("根据查询ID查询组织书记 url->{}, res->{}", url, res);
        log.debug("根据查询ID查询组织书记 {}", stopWatch.prettyPrint());
        return res;
    }


    /**
     * 查询指定组织的所有人员信息。
     * 包含下级,离退休组织，所有人，不包含删除状态的组织和人员
     *
     * @return List<UserForm>
     */
    public List<UserForm> findAllUsersByOid(HeaderHelper.SysHeader sysHeader, Long orgId, int page, int pageSize) {
        return findUsers(sysHeader, orgId, 1, 1, 1, 2, page, pageSize);
    }

    /**
     * 批量获取用户信息 组织关系在查询组织或查询组织的下级中
     *
     * @param includeRetire 是否包含离退休组织 1:包含 2：不包含 3：仅包含 默认1
     * @param includeLevel  是否包含下级 1:包含 2：不包含 3：仅包含 默认1
     * @param includeAll    是否包含所有人 1-是 2-否，默认2
     * @param filterStatus  是否包含删除状态信息 1-是 2-否，默认1
     */
    private List<UserForm> findUsers(HeaderHelper.SysHeader sysHeader, Long orgId,
                                     Integer includeRetire,
                                     Integer includeLevel,
                                     Integer includeAll,
                                     Integer filterStatus, int page, int pageSize) {
        String url = String.format("http://%s/org/user/find-party-user-by-where" +
                        "?org_id=%s" +
                        "&include_retire=%d" +
                        "&include_level=%d" +
                        "&include_all=%d" +
                        "&filter_status=%d" +
                        "&page=%d" +
                        "&page_size=%d",
                userCenter, orgId, includeRetire, includeLevel, includeAll, filterStatus, page, pageSize);
        return RestTemplateHelper.get(sysHeader, url, new TypeReference<Result<List<UserForm>>>() {
        });
    }

    /**
     * 查询字典
     */
    public Map<Integer, String> dictionaryMap(Integer code) {

        HttpHeaders headers = new HttpHeaders();
        headers.add("_tk", "-1");

        try {

            List<Dictionary> dictionaryList = RemoteApiHelper.get(
                    restTemplate, String.format("http://%s/uc/op/list?code=%d", userCenter, code),
                    headers,
                    new TypeReference<Result<List<Dictionary>>>() {
                    }
            );

            Map<Integer, String> dictionaryMap = new HashMap<>(dictionaryList.size());

            for (Dictionary dictionary : dictionaryList) {
                dictionaryMap.put(dictionary.getOpKey(), dictionary.getOpValue());
            }

            return dictionaryMap;

        } catch (IOException e) {
            log.error("根据code查询字典失败 code->{} error={}", code, e.getMessage());
        }

        return new HashMap<>();
    }

    /**
     * 根据支部编号获取下属党小组编号
     */
    public List<Long> findOrgIdByBranchIds(HeaderHelper.SysHeader sysHeader, List<Long> branchIds) {
        String orgIds = StringUtils.join(branchIds, ',');
        String url = String.format("http://%s/org-group/find/orgId/by/branchIds?branch_ids=%s", userCenter, orgIds);
        log.debug("findOrgIdByBranchIds 根据支部编号批量获取下属党小组编号=>{},{},{}", url, orgIds,branchIds);
        return RestTemplateHelper.get(sysHeader, url, new TypeReference<Result<List<Long>>>() {
        });
    }

    /**
     * 根据顶级组织id查询下级组织的所有党小组id
     */
    public List<Long> findByGroupIds(HeaderHelper.SysHeader sysHeader, Long orgId) {
        //调用用户中心根据组织id查询下级组织id
        List<OrganizationBase> organizationBases = findAllChildOrg(sysHeader, orgId);
        if (CollectionUtils.isEmpty(organizationBases)) {
            return new ArrayList<>();
        }
        //在根据下级组织ids查询所有党小组id
        List<Long> orgIds = organizationBases.stream().map(OrganizationBase::getOrgId).collect(Collectors.toList());
        return findOrgIdByBranchIds(sysHeader, orgIds);
    }


    @Data
    private static class Dictionary {

        private String code;
        @JsonProperty("op_key")
        private Integer opKey;
        @JsonProperty("op_value")
        private String opValue;
        @JsonProperty("has_child")
        private int hasChild;
    }

}
