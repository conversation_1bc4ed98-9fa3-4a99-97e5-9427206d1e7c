package com.goodsogood.ows.service

import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import com.goodsogood.ows.common.Constant
import com.goodsogood.ows.common.pojo.MeetingPushParam
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.configuration.CommentConfig
import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.NumEncryptUtils
import com.goodsogood.ows.mapper.MeetingCommentMemberAppraisalMapper
import com.goodsogood.ows.mapper.MeetingCommentMemberComplexMapper
import com.goodsogood.ows.mapper.MeetingCommentMemberMapper
import com.goodsogood.ows.model.db.MeetingCommentMemberAppraisalEntity
import com.goodsogood.ows.model.db.MeetingCommentMemberComplexEntity
import com.goodsogood.ows.model.db.MeetingCommentMemberEntity
import com.goodsogood.ows.model.vo.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import tk.mybatis.mapper.entity.Example
import java.net.URLEncoder
import java.time.LocalDateTime

@Service
class CommentMemberService(@Autowired val errors: Errors,
                           @Autowired val thirdService: ThirdService,
                           @Autowired val orgTypeConfig: OrgTypeConfig,
                           @Autowired val commentService: CommentService,
                           @Autowired val commentConfig: CommentConfig,
                           @Autowired val openService: OpenService,
                           @Autowired val commentMemberMapper: MeetingCommentMemberMapper,
                           @Autowired val commentMemberAppraisalMapper: MeetingCommentMemberAppraisalMapper,
                           @Autowired val commentMemberComplexMapper: MeetingCommentMemberComplexMapper
) {

    private val log = LoggerFactory.getLogger(CommentMemberService::class.java)

    /**
     * 查询民主评议党员列表
     * @param commentMemberVO
     * @param headers
     */
    fun getCommentMemberList(commentMemberVO: CommentMemberVO, headers: HttpHeaders): Page<CommentMemberResultForm> {
        log.debug("查询民主评议党员列表 -> vo:[${commentMemberVO}]")
        val header = HeaderHelper.buildMyHeader(headers)
        // 判断当前登录的是不是党小组
        val orgId = header.uoid ?: header.oid
        val orgInfo = thirdService.findOrgInfoByOrgId(orgId)
        var flag = 1
        if (orgInfo != null) {
            if (orgTypeConfig.communistGroup.contains(orgInfo.orgTypeChild)) {
                flag = 0
            }
        } else {
            throw ApiException("调取用户中心，获取组织信息失败",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "调取用户中心，获取组织信息失败")
            )
        }
        // 查询党小组内的成员
        var userIdList = mutableListOf<Long>()
        if (flag == 0) {
            val userList = thirdService.getUserListByOrgId(orgId, 0, headers)
            userIdList = userList.filter { it.politicalType == 1 }.map { it.userId }.toMutableList()
        }
        // 判断是否要根据手机号查询，若需要则加密
        val phone = commentMemberVO.phone?.let {
            if(it.isNotBlank()) NumEncryptUtils.encrypt(it, NumEncryptUtils.PHONE)
            else null
        }

        val pages = PageHelper.startPage<CommentMemberResultForm>(commentMemberVO.page, commentMemberVO.pageSize)
            .doSelectPage<CommentMemberResultForm> {
                commentMemberMapper.getCommentMemberList(
                    commentMemberVO.commentId, commentMemberVO.name, phone,
                    commentMemberVO.selfRating, commentMemberVO.complexRating, userIdList
                )
            }
        pages.forEach {
            it.appraisalData = it.commentMemberId?.let { it1 -> this.statisticalAppraisal(it1) }
        }
        return pages
    }

    /**
     * 党员互评统计
     */
    fun statisticalAppraisal(commentMemberId: Long): CommentStatisticalDataForm {
        val commentMember = commentMemberMapper.selectByPrimaryKey(commentMemberId)
        if (commentMember != null) {
            val size = commentMember.commentId?.let { this.getMemberListByCommentId(it).size }
            if (size != null) {
                val statistical = commentMemberAppraisalMapper.appraisalStatistical(commentMemberId)
                statistical.other = size - 1 - statistical.excellent - statistical.qualified - statistical.basicQualified - statistical.unqualified
                return statistical
            }
        }
        return CommentStatisticalDataForm()
    }

    /**
     * 获取民主评议党员表自评详情
     * @param commentMemberId
     */
    fun getCommentMember(commentMemberId: Long? = null, userId: Long? = null, headers: HttpHeaders) :CommentMemberForm?  {
        val header = HeaderHelper.buildMyHeader(headers)
        var memberForm : CommentMemberForm? = null
        var memberId = commentMemberId
        if (memberId == null) {
            val commentForm = commentService.getCommentByHeader(headers = headers)
            if (commentForm != null) {
                val commentId = commentForm.commentId
                val uId: Long = userId ?: header.userId
                memberId = commentMemberMapper.getCommentMemberByUserId(commentId = commentId, userId = uId)
            }
        }
        if (memberId != null) {
            val entity = commentMemberMapper.selectByPrimaryKey(memberId)
            // 检查民主评议是否开启
            val commentEntity = commentService.getMeetingComment(entity.commentId)
            if (commentEntity != null && commentEntity.status > 0) {
                memberForm = CommentMemberForm()
                memberForm.commentMemberId = entity.commentMemberId
                memberForm.year = entity.year
                memberForm.userId = entity.userId
                memberForm.userName = entity.userName
                memberForm.phone = entity.phoneSecret
                memberForm.orgId = entity.orgId
                memberForm.orgName = entity.orgName
                memberForm.selfRating = entity.selfRating
                memberForm.selfContent = entity.selfContent
                memberForm.isDraft = entity.isDraft
            }
        } else {
            throw ApiException("预备党员不参加民主评议",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "预备党员不参加民主评议")
            )
        }
        return memberForm
    }

    /**
     * 新增民主评议党员自评
     * @param form
     * @param headers
     */
    fun insertCommentMember(form: CommentMemberForm, headers: HttpHeaders) : String {
        log.debug("新增民主评议党员自评 -> vo:[{}]", form)
        val header = HeaderHelper.buildMyHeader(headers)
        val orgId = header.uoid ?: header.oid
        var commentMemberId = form.commentMemberId
        if (commentMemberId == null ) {
            val commentForm = commentService.getCommentByHeader(headers = headers)
            if (commentForm != null) {
                commentMemberId = commentMemberMapper.getCommentMemberByUserId(commentId = commentForm.commentId, userId = header.userId)
            }
        }
        if(commentMemberId == null) {
            throw ApiException("预备党员不参加民主评议",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "预备党员不参加民主评议")
            )
        }
        val entity = commentMemberMapper.selectByPrimaryKey(commentMemberId)
        if (header.userId != entity.userId) {
            throw ApiException("自评非本人操作",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "请本人进行操作")
            )
        }
        if (orgId != entity.orgId) {
            throw ApiException("请登录${entity.orgName}组织操作",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "请登录【${entity.orgName}】组织操作")
            )
        }
        if(form.operate == 1 && entity.isDraft == 1) {
            throw ApiException("已提交，不能再保存草稿",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "已提交，不能再保存草稿")
            )
        }
        // 判断能不能编辑
        commentService.isEdit(entity.commentId)
        entity.selfRating = form.selfRating
        entity.selfContent = form.selfContent
        entity.updateTime = LocalDateTime.now()
        entity.lastChangeUser = header.userId
        if (form.operate == 1) entity.isDraft = 0
        else if (form.operate == 2) entity.isDraft = 1
        commentMemberMapper.updateByPrimaryKey(entity)
        // 生成登记表
        commentService.createGradeForm(commentMemberId, entity.commentId, headers)
        // 创建自评互评表
        commentService.createAppraisal(entity.commentId!!, headers)
        return Constant.SUCCESS
    }

    fun getCommentMemberStatus(headers: HttpHeaders) : MobileCommentMemberVO {
        val header = HeaderHelper.buildMyHeader(headers)
        val vo = MobileCommentMemberVO()
        val userId = header.userId
        val orgId = header.uoid ?: header.oid
        val commentEntity = commentService.selectCommentByOrgId(orgId)
        if (commentEntity?.commentId != null) {
            if (mutableListOf(1,3,5).contains(commentEntity.status)) vo.isEdit = 1
            val memberId = commentMemberMapper.getCommentMemberByUserId(commentId = commentEntity.commentId, userId = userId)
            if (memberId != null) {
                vo.isStart = 1
                vo.commentId = commentEntity.commentId
                val memberEntity = commentMemberMapper.selectByPrimaryKey(memberId)
                if (memberEntity.isDraft == 1 && memberEntity.selfRating != null) {
                    vo.selfFinish = 1
                }
                val forms = commentMemberAppraisalMapper.getMemberAppraisalListBySelf(commentId = commentEntity.commentId!!, type = 1, userId = userId)
                if (forms.isNotEmpty()) {
                    val filter = forms.filter { it.rating == null }
                    if (filter.isEmpty()) {
                        vo.appraisalFinish = 1
                    }
                }
            } else {
                throw ApiException("预备党员不参加民主评议",
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), "预备党员不参加民主评议")
                )
            }
        }
        return vo
    }

    /**
     * 民主评议添加党员
     */
    fun addMember(vo: DealMemberVO, headers: HttpHeaders): MutableList<String> {
        val header = HeaderHelper.buildMyHeader(headers)
        val commentId = vo.commentId
        log.debug("民主评议$commentId 添加人员${vo.userIds}, 操作人：${header.userId}")
        commentService.isEdit(commentId, mutableListOf(0,2,4))
        val comment = commentService.getMeetingComment(commentId)
        if (comment != null) {
            val memberList = getMemberByUser(vo.userIds, comment.year, commentId)
            val existUserList = mutableListOf<String>()
            val prePartyList = mutableListOf<String>()
            val existOrgList = mutableListOf<String>()
            for (userId in vo.userIds) {
                var flag = true
                memberList.forEach {
                    if (it.userId == userId) {
                        it.userName?.let { it1 -> existUserList.add(it1) }
                        flag = false
                    }
                }
                if (flag) {
                    val userList = thirdService.findUserInfoByKey(userId, header.regionId)
                    if (userList != null && userList.size == 1) {
                        val userInfoBase = userList[0]
                        if (userInfoBase.orgId != comment.orgId) {
                            existOrgList.add("[${userInfoBase.userName}]")
                            continue
                        }
                        if (userInfoBase.politicalType != 1) {
                            prePartyList.add("[${userInfoBase.userName}]")
                            continue
                        }
                        // 新增人员
                        commentService.addCommentMemberList(userList, commentId, comment.year, header.userId, headers)
                    }
                }
            }
            // 统计
            commentService.createCommentStatics(commentId, headers)
            // 测评表
            commentService.createCommentEvaluation(commentId, headers)
            // 创建自评互评表
            commentService.createAppraisal(commentId, headers)
            // 解决报错
            // 先判断已存在民主评议的人员
            if(existUserList.size > 0) {
                return existUserList
            }
            // 在判断 预备党员和非本组织人员
            val errorList = mutableListOf<String>()
            if(prePartyList.size > 0) {
                errorList.add("${prePartyList.joinToString(separator = "、")} 是预备党员，不能参加民主评议。")
            }
            if(existOrgList.size > 0) {
                errorList.add("${existOrgList.joinToString(separator = "、")} 不是本组织人员，不能参加民主评议。")
            }
            if (errorList.isNotEmpty()) {
                val errorMsg = errorList.joinToString(separator = ",")
                log.debug("报错信息：$errorMsg")
                throw ApiException(
                    errorMsg,
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), errorMsg)
                )
            }
            return mutableListOf()
        } else {
            throw ApiException(
                "民主评议异常",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "民主评议异常")
            )
        }
    }

    /**
     * 民主评议删除党员
     */
    @Transactional
    fun delMember(vo: DealMemberVO, headers: HttpHeaders): String {
        val header = HeaderHelper.buildMyHeader(headers)
        val commentId = vo.commentId
        log.debug("民主评议$commentId 删除人员MemberId${vo.userIds}, 操作人：${header.userId}")
        commentService.isEdit(commentId, mutableListOf(0,2,4))
        val comment = commentService.getMeetingComment(commentId)
        if (comment != null) {
            vo.userIds.forEach { memberId ->
                delMember(memberId)
                // 生成登记表
                commentService.createGradeForm(memberId, commentId, headers)
            }
            // 统计
            commentService.createCommentStatics(commentId, headers)
            // 测评表
            commentService.createCommentEvaluation(commentId, headers)
            // 创建自评互评表
            commentService.createAppraisal(commentId, headers)
        } else {
            throw ApiException(
                "民主评议异常",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "民主评议异常")
            )
        }
        return "操作成功"
    }

    private fun getMemberByUser(userIds: MutableList<Long>? = null, year: Int? = null, commentId: Long? = null): MutableList<MeetingCommentMemberEntity> {
        val example = Example(MeetingCommentMemberEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andEqualTo("year", year)
        criteria.andIn("userId", userIds)
        criteria.andNotEqualTo("commentId", commentId)
        return commentMemberMapper.selectByExample(example)
    }

    private fun delMember(memberId: Long) {
        val memberEntity = commentMemberMapper.selectByPrimaryKey(memberId)
        // 删除民主评议党员表
        commentMemberMapper.deleteByPrimaryKey(memberId)
        // 删除互评表
        val ids =
            commentMemberAppraisalMapper.selectAppraisalData(memberId, memberEntity.userId, memberEntity.commentId)
        if(ids.isNotEmpty()) {
            val appraisalEx = Example(MeetingCommentMemberAppraisalEntity::class.java)
            val appraisalCriteria = appraisalEx.createCriteria()
            appraisalCriteria.andIn("commentMemberAppraisalId", ids)
            commentMemberAppraisalMapper.deleteByExample(appraisalEx)
            // 删除综合评价表
            val complexEx = Example(MeetingCommentMemberComplexEntity::class.java)
            val complexCriteria = complexEx.createCriteria()
            complexCriteria.andEqualTo("commentMemberId", memberId)
            commentMemberComplexMapper.deleteByExample(complexEx)
        }
    }

    /**
     * 统计自评数据 - 各个等级多少
     */
    fun statisticalSelf(commentId: Long): CommentStatisticalDataForm {
        return commentMemberMapper.selfStatistical(commentId) ?: CommentStatisticalDataForm()
    }

    /**
     * 查询 民主评议 政治面貌 - 党员的组织
     */
    fun getMemberListByCommentId(commentId: Long) : MutableList<MeetingCommentMemberEntity> {
        val example = Example(MeetingCommentMemberEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andEqualTo("commentId", commentId)
        criteria.andEqualTo("politicalType", Constant.YES)
        return commentMemberMapper.selectByExample(example)
    }

    fun getNoSelfNum(commentId: Long) : Int {
        val list = getMemberListByCommentId(commentId)
        return list.filter { it.selfRating == null || it.selfRating == 0 }.size
    }

    fun sendMsg(commentId: Long, headers: HttpHeaders) : String {
        val header = HeaderHelper.buildMyHeader(headers)
        val comment = commentService.getMeetingComment(commentId)
        if (comment != null) {
            val list = getMemberListByCommentId(commentId)
            val filter = list.filter { it.selfRating == null || it.selfRating == 0 }
            val msgList = mutableListOf<SendCommentSelfMsg>()
            val url = URLEncoder.encode(commentConfig.msg.noSelf.url)
            filter.forEach {
                val msg = SendCommentSelfMsg()
                msg.userId = it.userId
                msg.userName = it.userName
                msg.year = comment.year
                msg.targetUrl = String.format(commentConfig.msg.url, url, comment.orgId)
                msgList.add(msg)
            }
            openService.sendNotice(msgList, commentConfig.msg.noSelf.templateId, commentConfig.msg.channel, MeetingPushParam(1), header.regionId)
        }
        return Constant.SUCCESS
    }
}