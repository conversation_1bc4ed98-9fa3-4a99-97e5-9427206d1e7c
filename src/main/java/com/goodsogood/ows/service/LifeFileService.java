package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.MeetingUrlConfig;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 民主生活会-附件处理
 */
@Service
@Log4j2
public class LifeFileService {
    private final LifeFileMapper lifeFileMapper;
    private final ThirdService thirdService;
    private final LifeUploaderMapper lifeUploaderMapper;
    private final LifeFileModelMapper lifeFileModelMapper;
    private final LifeMapper lifeMapper;
    private final LifeStudyMapper lifeStudyMapper;
    private final LifeAdviceMapper lifeAdviceMapper;
    private final LifeNoticeMapper lifeNoticeMapper;
    private final StringRedisTemplate stringRedisTemplate;
    private final MeetingPeoplePartyLifeService meetingPeoplePartyLifeService;
    private final Errors errors;
    private final List listTemplate = Arrays.asList(22, 23, 24, 25, 26);//谈心谈话模板type
    private final List listTalk = Arrays.asList(9, 11, 12, 13, 14);//谈心谈话附件type
//    private final MeetingUrlConfig meetingUrlConfig;
    private final TogServicesConfig togServicesConfig;
    private final RestTemplate restTemplate;
    @Value("${lifemeeting.file.num.usual}")
    private Integer usualNum;

    @Value("${lifemeeting.file.num.special}")
    private Integer specialNum;


    @Autowired
    public LifeFileService(LifeFileMapper lifeFileMapper, ThirdService thirdService,
                           LifeUploaderMapper lifeUploaderMapper,
                           LifeFileModelMapper lifeFileModelMapper,
                           LifeMapper lifeMapper,
                           LifeStudyMapper lifeStudyMapper,
                           LifeAdviceMapper lifeAdviceMapper,
                           LifeNoticeMapper lifeNoticeMapper,
                           StringRedisTemplate stringRedisTemplate,
                           MeetingPeoplePartyLifeService meetingPeoplePartyLifeService,
                           Errors errors, TogServicesConfig togServicesConfig, RestTemplate restTemplate) {
        this.lifeFileMapper = lifeFileMapper;
        this.thirdService = thirdService;
        this.lifeUploaderMapper = lifeUploaderMapper;
        this.lifeFileModelMapper = lifeFileModelMapper;
        this.lifeMapper = lifeMapper;
        this.lifeStudyMapper = lifeStudyMapper;
        this.lifeAdviceMapper = lifeAdviceMapper;
        this.lifeNoticeMapper = lifeNoticeMapper;
        this.stringRedisTemplate = stringRedisTemplate;
        this.meetingPeoplePartyLifeService = meetingPeoplePartyLifeService;
        this.errors = errors;
//        this.meetingUrlConfig = meetingUrlConfig;//url跳转配置
        this.togServicesConfig = togServicesConfig;
        this.restTemplate = restTemplate;
    }


    /**
     * 附件查询
     *
     * @param lifeId 民主生活会的id
     * @param type   模块
     * @param step   当前页面，1：会前 2：会后，3：上报
     * @return List<LifeFileEntity>  当前会议模块下所有附件列表
     */
    public List<LifeFileEntity> queryAttach(Long lifeId, List<Integer> type, Integer step) {
        Example example = new Example(LifeFileEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId", lifeId).andIn("type", type).andEqualTo("step", step).andEqualTo("isDel", 0);
//        example.setOrderByClause("type ASC,createTime DESC");
        example.selectProperties("lifeFileId", "fileId", "fileName", "userId", "username", "isDirect", "url");
        List<LifeFileEntity> lifeFiles = lifeFileMapper.selectByExample(example);
        return lifeFiles;
    }

    /**
     * 附件详情汇总
     *
     * @param lifeId 民主生活会的id
     * @param step   模块: 1：会前材料，2：会后材料 3：上报材料
     * @return Map<Integer, List<LifeFileVO>>   模块 , 模块里的附件
     */
    public Map<Integer, List<LifeFileVO>> queryFileInfo(Long lifeId, Integer step) {
        Map<Integer, List<LifeFileVO>> map1;
        if (step == 3) {//查询已上报的附件
            List<LifeFileVO> lifeFileEntities = lifeFileMapper.queryFileSubmit(lifeId);
            map1 = lifeFileEntities.stream().collect(Collectors.groupingBy(LifeFileVO::getType));
            return map1;
        }
        //查询会后或会前资料或即将要上报的材料
        List<LifeFileVO> lifeFileEntities = lifeFileMapper.queryFileInfo(lifeId, step);
        map1 = lifeFileEntities.stream().collect(Collectors.groupingBy(LifeFileVO::getType));
        if (!checkInsertFile(lifeId, step)) {//如果meeting附件已插入附件表，则直接查询附件表（会后、上报为隔离线）
            return map1;
        }
        //关联查询组织学习的附件
        log.debug("关联查询组织学习的附件");
        Map<Integer, List<LifeAndMeetingVO>> study = new HashMap<>();
        queryMeetingStudyFile(lifeId,study,3,step);
        if (!CollectionUtils.isEmpty(study)) {
            meetingPeoplePartyLifeService.getFileByMeetings(study);
            List<LifeFileVO> studyListTemp = getLifeFileVoList(study);
            if(CollectionUtils.isEmpty(map1.get(2))){
                map1.put(2,studyListTemp);
            }else{
                map1.get(2).addAll(studyListTemp);
            }
        }
        if(step==1){
            log.debug("关联查询发起民主会议附件会前");
            Map<Integer, List<LifeAndMeetingVO>> meeting = new HashMap<>();
            queryMeetingFile(lifeId,meeting,17);
            if (!CollectionUtils.isEmpty(meeting)) {
                meetingPeoplePartyLifeService.getFileByMeetings(meeting);//调用王若宇的方法返回meeting附件
                Map<Integer, List<LifeFileVO>> mapMeeting = getLifeFileVo(meeting,8 );//将map里附件信息进行完善返给前端
                if(CollectionUtils.isEmpty(map1.get(8))){
                    map1.putAll(mapMeeting);
                }else{
                    map1.get(8).addAll(mapMeeting.get(8));
                }
            }
        }else{
            log.debug("关联查询发起民主会议附件会后");
            Map<Integer, List<LifeAndMeetingVO>> meeting = new HashMap<>();
            queryMeetingFile(lifeId,meeting,20);
            if (!CollectionUtils.isEmpty(meeting)) {
                meetingPeoplePartyLifeService.getFileByMeetings(meeting);//调用王若宇的方法返回meeting附件
                Map<Integer, List<LifeFileVO>> mapMeeting = getLifeFileVo(meeting,11 );//将map里附件信息进行完善返给前端
                if(CollectionUtils.isEmpty(map1.get(11))){
                    map1.putAll(mapMeeting);
                }else{
                    map1.get(11).addAll(mapMeeting.get(11));
                }
            }
        }
//
//        log.debug("关联查询会议通报的附件");
//        Map<Integer, List<LifeAndMeetingVO>> map = queryNoticeFile(lifeId,step);
//        if(!CollectionUtils.isEmpty(map)){
//            meetingPeoplePartyLifeService.getFileByMeetings(map);//调用王若宇的方法返回meeting附件
//            Map<Integer, List<LifeFileVO>> mapNotice = getLifeFileVo(map, 11);
//            map1.putAll(mapNotice);
//        }
//        log.debug("关联查询征求意见-问卷调查的附件");
//        Map<Integer, List<LifeAndMeetingVO>> mapExam = queryAdviceFile(lifeId, 2, 8, step);
//        meetingPeoplePartyLifeService.getFileByMeetings(mapExam);//调用王若宇的方法返回meeting附件
//        if (!CollectionUtils.isEmpty(mapExam)) {
//            map1.get(4).addAll(getLifeFileVoList(mapExam));
//        }
        log.debug("关联查询征求意见-座谈会的附件");
        Map<Integer, List<LifeAndMeetingVO>> mapTalk = queryAdviceFile(lifeId, 3, 10, step);
        log.debug("调用meeting方法开始");
        if (!CollectionUtils.isEmpty(mapTalk)) {
            try{
                meetingPeoplePartyLifeService.getFileByMeetings(mapTalk);
            }catch(Exception e){
                log.error("调用meeting查询附件报错"+e);
            }
            log.debug("调用meeting方法结束");
            List<LifeFileVO> talkListTemp =  getLifeFileVoList(mapTalk);
            if(CollectionUtils.isEmpty(map1.get(4))){
                map1.put(4,talkListTemp);
            }else{
                map1.get(4).addAll(talkListTemp);
            }
        }

        return map1;
    }

    /**
     * 判断是否将该会议关联的meeting附件插入民主生活附件表
     * @param lifeId
     * @param step  当前页面：1-会前/会中 2-会后
     * @return  boolean：true——未插入、false:已插入
     */
    private boolean checkInsertFile(Long lifeId, Integer step) {
        log.debug("查询是否将meeting相关附件插入组织生活会附件表"+lifeId+step);
        if(stringRedisTemplate.opsForValue().get(Constant.LIFEFILE + lifeId + "_" + step) == null){//再加一层数据库判断，防止缓存被删除
           LifeEntity lifeEntity =  lifeMapper.selectByPrimaryKey(lifeId);
           Integer status = lifeEntity.getStatus();
           return step==1&&status<3 || step==2&&status<5;
        }
        return false;
    }


    /**
     * 根据约定的map-LifeFileVO处理成信息完善的LifeAndMeetingVO返给前端
     * @param meetingFileMap  附件信息：只有name url，待填充其他信息的map
     * @param type  最终返给前端的大模块id
     * @return Map<Integer, List<LifeFileVO>>  返给前端的map：模块  模块下的附件信息
     */
    //根据会议id查询对应模块下的附件，返给前端map，type为最终的大模块，map的key为具体模块id
    private Map<Integer, List<LifeFileVO>> getLifeFileVo(Map<Integer, List<LifeAndMeetingVO>> meetingFileMap, Integer type) {
        log.debug("根据会议id查询对应模块下的附件");
        if(meetingFileMap!=null && meetingFileMap.size()>0){
            Map<Integer, List<LifeFileVO>> map = new HashMap();
            List<LifeFileVO> list = getLifeFileVoList(meetingFileMap);
            map.put(type, list);
            return map;
        }
        return Collections.emptyMap();
    }

    /**
     *将List<LifeAndMeetingVO>处理成List<LifeFileVO>
     * @param meetingFileMap
     * @return List<LifeFileVO>
     */
    //根据前端传入的map参数，处理成list(用于获取征求意见-座谈会、问卷调查的附件list)
    private List<LifeFileVO> getLifeFileVoList(Map<Integer, List<LifeAndMeetingVO>> meetingFileMap) {
        log.debug("getLifeFileVoList");
        List<LifeFileVO> list = new ArrayList<>();
        Set<Map.Entry<Integer, List<LifeAndMeetingVO>>> entrySet = meetingFileMap.entrySet();
        for (Map.Entry<Integer, List<LifeAndMeetingVO>> entry : entrySet) {
            Integer key = entry.getKey();
            List<LifeAndMeetingVO> lifeFileMeeting = entry.getValue();
            for (LifeAndMeetingVO vo : lifeFileMeeting) {
                Long dataId = vo.getDataId();
                List<LifeAndMeetingVO.LifeAndMeetingFile> files = vo.getMeetingFiles();
                if (files != null && files.size() > 0) {
                    for (LifeAndMeetingVO.LifeAndMeetingFile file : files) {
                        LifeFileVO lifeFile = new LifeFileVO();
                        lifeFile.setFileName(file.getFileName());
                        lifeFile.setFileNameDown(file.getFileName());
                        lifeFile.setFileId(file.getFileId());
                        lifeFile.setFileUrl(file.getPath());
                        lifeFile.setType(key);
                        lifeFile.setIsSubmit(0);
                        lifeFile.setDataId(dataId);
                        list.add(lifeFile);
                    }
                }

            }
        }
        return list;

    }


    /**
     * 指定上传人查询:将上传人信息list处理到map里
     *
     * @param lifeUploaderEntities 实体类
     * @return map :id,name
     */
    public Map<Long, String> queryUploader(List<LifeUploaderEntity> lifeUploaderEntities) {
        log.debug("根据上传人信息list将其以map形式返给前端");
        Map<Long, String> uploader = new HashMap();
        lifeUploaderEntities.stream().forEach(i -> {
            uploader.put(i.getUserId(), i.getUsername());
        });
        return uploader;
    }


    /**
     * 指定上传人查询:根据会议id，模块，数据行id查询指定上传人并处理到map返给前端
     * @param lifeId
     * @param type  模块
     * @param dataId
     * @return
     */
    public List<LifeFileUserVO> queryUploader(Long lifeId, Integer type, Long dataId) {
        log.debug("查询指定上传人并处理到map返给前端"+lifeId+type+dataId);
        List<LifeUploaderEntity> lifeUploaderEntities = getLifeUploader(lifeId, type, dataId);
        List<LifeFileUserVO> list = new ArrayList<>();
        lifeUploaderEntities.stream().forEach(i->{
            list.add(new LifeFileUserVO(i.getUploaderId(),i.getUserId(),i.getUsername()));
        });
        return list;
    }

    /**
     * 保存附件
     * @Param saveAttachForm :要插入的附件相关信息
     * @Param  header
     * @return List  ：插入后的附件信息
     */
    @Transactional(rollbackFor = Exception.class)
    public List<LifeFileEntity> saveAttach(SaveAttachForm saveAttachForm, HeaderHelper.SysHeader header) {
        Integer type = saveAttachForm.getType();
        if (listTemplate.contains(type)) {//谈心谈话模板：存在则更新，不存在则新增；
            log.debug("谈心谈话模板处理：存在则更新，不存在则新增");
            InsrOrUpdtFile(saveAttachForm);
            return Collections.EMPTY_LIST;
        }
        //0.判断参数data_id
        if (!getDataId(saveAttachForm)) {
            throw new ApiException("该模块下data_id不能为为空", new Result(errors, 3160, HttpStatus.OK.value()));
        }
        //1.如果是他人上传，判断是否为指定上传人、是否超过截止时间、是否携带task_id
        if (saveAttachForm.getIsDirect() == 0) {
            List<LifeUploaderEntity> uploaderEntities = getLifeUploader(saveAttachForm.getLifeId(), saveAttachForm.getType(), saveAttachForm.getDataId());
            Boolean check_other = checkOther(saveAttachForm.getUserId(), uploaderEntities);
            if (!check_other) {
                throw new ApiException("只能由指定人进行上传", new Result(errors, 3161, HttpStatus.OK.value()));
            }
            //如果是他人上传，判断是否超过上传时间
            LifeUploaderEntity uploaderNow = uploaderEntities.stream().filter(i -> i.getUserId().equals(saveAttachForm.getUserId())).findFirst().get();
            if (LocalDateTime.now().isAfter(uploaderNow.getEndTime())) {
                throw new ApiException("超过任务截止时间不能上传!", new Result(errors, 3162, HttpStatus.OK.value()));
            }
            //如果他人上传，需要携带参数task_id
            if (saveAttachForm.getTaskId() == null) {
                throw new ApiException("他人上传的附件请传入任务id!", new Result(errors, 3163, HttpStatus.OK.value()));
            }
        }
        //2.判断附件数量()
        List<LifeFileForm> lifeFiles = saveAttachForm.getLifeFile();
        Integer numDb = selNumDb(saveAttachForm);//数据库现有附件数量
        Integer numTotal = lifeFiles.size() + numDb;//本次上传后文件数量
        if (listTalk.contains(saveAttachForm.getType()) && numTotal > specialNum) {//如果是谈心谈话相关的，附件数量不得大于5
            throw new ApiException("上传附件数量不得大于", new Result(errors, 3164, HttpStatus.OK.value(), "" + specialNum));
        } else if (!listTalk.contains(saveAttachForm.getType()) && numTotal > usualNum) {//如果不是谈心谈话相关的，附件数量不得大于20
            throw new ApiException("上传附件数量不得大于", new Result(errors, 3164, HttpStatus.OK.value(), "" + usualNum));
        }
        //3.附件新增保存至数据库
        List<LifeFileEntity> file = saveAttchDb(saveAttachForm);
        //4.如果是他人上传，更新任务状态
        if (saveAttachForm.getIsDirect() == 0) {
            //民主生活会任务唯一标识
//            String id = ""+ saveAttachForm.getLifeId()+"_"+ saveAttachForm.getType()+"_"+ saveAttachForm.getDataId();
//            String taskId = Constant.TASKKEY + "_" + id;//民主生活会任务唯一标识
            String taskId = saveAttachForm.getTaskId();
            log.debug("他人上传附件调用任务底座");
            thirdService.finishPending(header.getRegionId(), saveAttachForm.getUserId(), taskId);
        }
        return file;
    }

    /**
     * 查询附件上传人-完整信息
     * @param lifeId
     * @param type
     * @param dataId
     * @return  list
     */
    private List<LifeUploaderEntity> getLifeUploader(Long lifeId, Integer type, Long dataId) {
        log.debug("查询指定上传人完整信息");
        Example example = new Example(LifeUploaderEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId", lifeId).andEqualTo("type", type);
        if (dataId != null) {
            criteria.andEqualTo("dataId", dataId);
        }
        List<LifeUploaderEntity> lifeUploaderEntities = lifeUploaderMapper.selectByExample(example);
        return lifeUploaderEntities;
    }


    /**
     * 谈心谈话模板处理
     * @param saveAttachForm
     */
    //检查是否存在该行的附件模板-主要用于谈心谈话
    @Transactional(rollbackFor = Exception.class)
    private void InsrOrUpdtFile(SaveAttachForm saveAttachForm) {
        log.debug("插入谈心谈话模板数据准备");
        Long lifeId = saveAttachForm.getLifeId();
        Integer type = saveAttachForm.getType();
        Integer step = saveAttachForm.getStep();
        Long dataId = saveAttachForm.getDataId();
        List<LifeFileForm> lifeFiles = saveAttachForm.getLifeFile();
        if(CollectionUtils.isEmpty(lifeFiles)){
            return;
        }
        //判断模板数量
        if (saveAttachForm.getLifeFile().size() > 1) {
            throw new ApiException("模板重复请检查", new Result(errors, 3165, HttpStatus.OK.value()));
        }
        //如果是谈心谈话相关的模板,判断该行是否已存在模板
        Example example = new Example(LifeFileEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId", lifeId).andEqualTo("type", type).andEqualTo("step", step).andEqualTo("dataId", dataId).andEqualTo("isDel",0);
        LifeFileEntity lifeFile = lifeFileMapper.selectOneByExample(example);
        log.debug("谈心谈话附件处理");
        if (lifeFile == null) {//如不存在则插入
            //附件新增保存至数据库
            List<LifeFileEntity> file = saveAttchDb(saveAttachForm);
            return;
        }
        //如果已存在，则判断是否已上报
        if (lifeFile.getIsSubmit() == 1) {
            throw new ApiException("该文件已提交，不允许编辑", new Result(errors, 3166, HttpStatus.OK.value()));
        }
        //没有上报则附件信息更新至数据库
        updateAttach(saveAttachForm, lifeFile.getLifeFileId());
    }

    /**
     * 谈心谈话模板更新
     * @param saveAttachForm
     * @param lifeFileId
     */
    //更新附件-谈心谈话模板，每次编辑更新
    private void updateAttach(SaveAttachForm saveAttachForm, Long lifeFileId) {
        log.debug("更新谈心谈话模板");
        LifeFileEntity lifeFileEntity = new LifeFileEntity();
        BeanUtils.copyProperties(saveAttachForm, lifeFileEntity, "lifeFile");
        List<LifeFileForm> lifeFiles = saveAttachForm.getLifeFile();
        for (LifeFileForm file : lifeFiles) {//只会有一个模板
            lifeFileEntity.setLifeFileId(lifeFileId);
//            lifeFileEntity.setFileId(file.getFileId());
            lifeFileEntity.setFileName(file.getFileName());
            lifeFileEntity.setUrl(file.getUrl());
            lifeFileEntity.setUpdateTime(LocalDateTime.now());
            lifeFileEntity.setLastChangeUser(saveAttachForm.getUserId());
            lifeFileMapper.updateByPrimaryKeySelective(lifeFileEntity);
        }

    }


    /**
     * 上报异步处理
     * @param lifeId
     * @param lifeFileMap  查询详情返给前端的map，前端传入
     * @param header
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean report(Long lifeId, Map<Integer, List<LifeFileVO>> lifeFileMap, HeaderHelper.SysHeader header)  {
        log.debug("发起民主生活会--陈虹交"+lifeId);
        Map<Integer, List<LifeAndMeetingVO>> mapNew = new HashMap<>();
        queryMeetingFile(lifeId, mapNew, 20);
        Map<Integer, List<LifeAndMeetingVO>> map = new HashMap<>();
//        log.debug("查询会议通报表"+lifeId);
//        Map<Integer, List<LifeAndMeetingVO>> map = queryNoticeFile(lifeId,2);
//        log.debug("查询征求意见-问卷调查"+lifeId);
//        Map<Integer, List<LifeAndMeetingVO>> mapExam = queryAdviceFile(lifeId, 2, 8, 2);
        log.debug("查询征求意见-座谈会"+lifeId);
        Map<Integer, List<LifeAndMeetingVO>> mapTalk = queryAdviceFile(lifeId, 3, 10, 2);
//        map.putAll(mapExam);
        map.putAll(mapTalk);
        map.putAll(mapNew);
        log.debug("征求意见-座谈会/问卷调查、会议通报的附件插入附件"+lifeId);
        List<LifeFileEntity> allSpecial = insMeetingFile(lifeId, map, 2);
        log.debug("特殊文件个数"+allSpecial.size());
        log.debug("特殊文件"+allSpecial);
        //附件表已插入标志更新，用于查询详情时不再关联meeting
        stringRedisTemplate.opsForValue().set(Constant.LIFEFILE + lifeId + "_" + 2, "ok");
        //获取当前所有附件
        List<Long> ids = getFileIds(lifeFileMap, allSpecial);
        log.debug("将上报的附件打上标志"+ids);
        if(CollectionUtils.isEmpty(ids)){
            return true;
        }
        lifeFileMapper.updateSubmit(ids, header.getUserId());
        log.debug("更改会议状态"+lifeId);
        changeStatus(lifeId, Constant.LIFE_REPORT, header);
        return true;
    }

    /**
     * 查询该会议id下的会前准备meeting并放入约定的map，用于调用王若宇方法返回meeting附件信息-发起民主生活会
     * @param lifeId
     * @param map  约定的map
     * @param type  具体模块
     */
    private void queryMeetingFile(Long lifeId, Map<Integer, List<LifeAndMeetingVO>> map, Integer type) {
        List<LifeAndMeetingVO> meetingIds = new ArrayList<>();
        Example example2 = new Example(LifeEntity.class);
        Example.Criteria criteria2 = example2.createCriteria();
        criteria2.andEqualTo("lifeId", lifeId).andEqualTo("isDel", 0);
        List<LifeEntity> lifeEntities = lifeMapper.selectByExample(example2);
        if (lifeEntities != null && lifeEntities.size() > 0) {
            for (LifeEntity life : lifeEntities) {
                LifeAndMeetingVO vo = new LifeAndMeetingVO();
                vo.setMeetingId(life.getMeetingId());
                vo.setDataId(null);
                meetingIds.add(vo);
            }
            map.put(type, meetingIds);
        }
    }

    /**
     * 查询该会议id下有哪些组织学习数据行并放入约定的map，用于调用王若宇方法返回meeting附件信息-组织学习
     * @param lifeId
     * @param map  约定的map
     * @param type  具体模块
     * @param step  1-会前 2-会后
     */
    private void queryMeetingStudyFile(Long lifeId, Map<Integer, List<LifeAndMeetingVO>> map, Integer type,Integer step) {
        log.debug("组织学习附件查询");
        List<LifeAndMeetingVO> studyIds = new ArrayList<>();
        Example example = new Example(LifeStudyEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId", lifeId).andEqualTo("step", step);
        List<LifeStudyEntity> lifeStudyEntities = lifeStudyMapper.selectByExample(example);
        if (lifeStudyEntities != null && lifeStudyEntities.size() > 0) {
            for (LifeStudyEntity study : lifeStudyEntities) {
                LifeAndMeetingVO vo = new LifeAndMeetingVO();
                vo.setMeetingId(study.getStudyId());
                vo.setDataId(study.getLifeStudyId());
                studyIds.add(vo);
            }
            map.put(type, studyIds);
        }
    }


    /**
     * 根据会议id查询出相关的会议通报表，整合到map里，后续根据该map调用王若宇的方法返回map附件信息-会议通报
     * @param lifeId
     * @param step
     * @return  map
     */
    //查询该会议下有哪些meeting_id，并封装到map里，作为查询meeting附件的参数。--会议通报
    private Map<Integer, List<LifeAndMeetingVO>> queryNoticeFile(Long lifeId,Integer step) {
        log.debug("查询会议通报表"+lifeId+"_"+"_"+step);
        Example example = new Example(LifeNoticeEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId", lifeId).andEqualTo("step", step);
        //查询该民主生活会下有哪些meetingid
        List<LifeNoticeEntity> lifeNoticeEntities = lifeNoticeMapper.selectByExample(example);
        Map<Integer, List<LifeAndMeetingVO>> map = new HashMap<>();
        List<LifeAndMeetingVO> noticeIds = new ArrayList<>();//会议通报
        if (lifeNoticeEntities != null && lifeNoticeEntities.size() > 0) {
            for (LifeNoticeEntity notice : lifeNoticeEntities) {
                LifeAndMeetingVO vo = new LifeAndMeetingVO();
                vo.setMeetingId(notice.getNoticeId());
                vo.setDataId(notice.getLifeNoticeId());
                noticeIds.add(vo);
            }
            map.put(20, noticeIds);
        }
        return map;
    }

    /**
     * 根据会议id查询出相关的征求意见表，整合到map里，后续根据该map调用王若宇的方法返回map附件信息
     * @param lifeId
     * @param adviceType  2:问卷调查 3：座谈会
     * @param type
     * @param step
     * @return  map：约定的map
     */
    //查询该会议下有哪些会议通报，将其关联的meeting放入map作为查询meeting附件的参数-征求意见
    private Map<Integer, List<LifeAndMeetingVO>> queryAdviceFile(Long lifeId, Integer adviceType, Integer type, Integer step) {
        log.debug("查询征求意见表"+lifeId+"_"+adviceType+"_"+step);
        List<LifeAdviceEntity> lifeExamEntities = getLifeAdviceEntities(lifeId, adviceType, step);
        Map<Integer, List<LifeAndMeetingVO>> map = new HashMap<>();
        List<LifeAndMeetingVO> adviceExamIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(lifeExamEntities)) {
            for (LifeAdviceEntity advice : lifeExamEntities) {
                LifeAndMeetingVO vo = new LifeAndMeetingVO();
                vo.setMeetingId(advice.getDataId());
                vo.setDataId(advice.getAdviceId());
                adviceExamIds.add(vo);
            }
            map.put(type, adviceExamIds);
            return map;
        }
       return Collections.emptyMap();

    }

    /**
     * 查询该会议下有哪些征求意见：adviceType：2-问卷调查  3-座谈会
     * @param lifeId
     * @param adviceType 2-问卷调查  3-座谈会
     * @param step  1-会前 2-会后
     * @return
     */

    private List<LifeAdviceEntity> getLifeAdviceEntities(Long lifeId, Integer adviceType, Integer step) {
        Example example = new Example(LifeAdviceEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId", lifeId).andEqualTo("adviceType", adviceType).andEqualTo("step", step).andEqualTo("isDel", 0);
        //查询该民主生活会下有哪些meetingid
        List<LifeAdviceEntity> lifeExamEntities = lifeAdviceMapper.selectByExample(example);
        return lifeExamEntities;
    }

    /**
     * 根据前端传入的map获取所有的附件id
     * @param lifeFileMap 选中的附件：map(模块，附件信息)，里面life_file_id==null的需要借助specialList来获取life_file_id
     * @param specialList:会议通报、征求意见-问卷调查 征求意见-座谈会相关附件信息:这些为关联查询，没有life_file_id，需要单独处理
     * @return list:上报的附件ids
     */
    //根据前端传入的map获取所有的附件id(specialList会议通报、征求意见-问卷调查 征求意见-座谈会相关附件插入数据库信息，lifeFileMap前端传入参数)
    private List<Long> getFileIds(Map<Integer, List<LifeFileVO>> lifeFileMap, List<LifeFileEntity> specialList) {
        log.debug("根据前端传入的map获取所有的上报附件id");
        List<Long> ids = new ArrayList<>();
        Set<Map.Entry<Integer, List<LifeFileVO>>> entrySet = lifeFileMap.entrySet();
        for (Map.Entry<Integer, List<LifeFileVO>> entry : entrySet) {
            List<LifeFileVO> list = entry.getValue();
            List<Long> lifeFileIds = list.stream().map(LifeFileVO::getLifeFileId).filter(Objects::nonNull).collect(Collectors.toList());
            log.debug("根据前端传入lifeFileIds"+lifeFileIds);
            ids.addAll(lifeFileIds);
            List<LifeFileVO> specialFiles = list.stream().filter(i -> i.getLifeFileId() == null).collect(Collectors.toList());//关联的meeting附件
            for (LifeFileVO vo : specialFiles) {//遍历传入id为空的数据，即关联的meeting附件，i为数据库信息，vo为前端传入信息
                List<Long> specialIds = specialList.stream().filter(i -> checkId(i, vo)).map(LifeFileEntity::getLifeFileId).collect(Collectors.toList());//如果data_id,name url与前端传入都匹配，则为选中上附件
                log.debug("specialIds"+specialIds);
                ids.addAll(specialIds);
            }
        }
        return ids;
    }


    /**
     * 判断选中的附件与数据库的附件是否是同一个
     * @param entity  :数据库
     * @param vo ：前端传入
     * @return true：如果data_id,name url与前端传入都匹配
     */
    //判断选中的附件与数据库的附件是否是同一个
    private Boolean checkId(LifeFileEntity entity, LifeFileVO vo) {
        log.debug("查询数据库附件:"+entity);
        log.debug("查询传入附件"+ vo);
//        Boolean checkId = (entity.getDataId() ==null? entity.getFileName().equals(vo.getFileName()) && entity.getUrl().equals(vo.getFileUrl()): entity.getDataId()==vo.getDataId() && entity.getFileName().equals(vo.getFileName()) && entity.getUrl().equals(vo.getFileUrl()));
        Boolean checkId = (entity.getDataId() ==null? entity.getFileId().equals(vo.getFileId()) : Objects.equals(entity.getDataId(), vo.getDataId()) && entity.getFileId().equals(vo.getFileId()));
        log.debug("比较"+ checkId);
        return checkId;
    }

    /**
     * 删除上传人
     *
     * @return Boolean
     * @Param uploaderId  t_meeting_uploader主键
     */
    //不涉及任务的回退，暂无此接口，在上传人表中删除后，打开任务会校验不通过，因此不会造成删除了上传人还能上传附件。
    public Boolean deleteUploader(Long userId, Long lifeId, Integer type, Long dataId) {
        lifeUploaderMapper.deleteUploader(userId, lifeId, type, dataId);
        return true;
    }

    /**
     * 删除附件
     *
     * @return Boolean
     * @Param lifeFileId  民主生活会附件id
     * @Param step 当前页面：1：会前、会中 2：会后
     */
    public Boolean deleteAttach(Long lifeId, List<Long> lifeFileIds, Integer step, HeaderHelper.SysHeader headers) {
        List<Long> lifeFileId = lifeFileIds.stream().filter(x -> x != null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(lifeFileId)){
            log.debug("该情况为征求意见-直接上传，传递的dataid不是附件id");
            return false;//该情况为征求意见-直接上传，传递的dataid不是附件id
        }
        //判断当前会议状态，会前/会中页面：只有当前会议状态<3可以删除;会后页面：只有当前会议状态!=5可以删除;
        Integer status = getStatus(lifeId);
        if ((step == 1 && status >= 3) || (step == 2 && status == 5)) {
            throw new ApiException("当前会议状态下该页面的附件不允许删除", new Result(errors, 3167, HttpStatus.OK.value()));
        }
        lifeFileMapper.deleteAll(lifeFileId, headers.getUserId());
        return true;
    }

    private Integer getStatus(Long lifeId){
        LifeEntity lifeEntity = lifeMapper.selectByPrimaryKey(lifeId);
        return lifeEntity.getStatus();
    }


    /**
     * 删除谈心谈话模板、附件
     * @param saveAttachForm
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTalkFile(SaveAttachForm saveAttachForm){
        //判断当前会议状态，会前/会中页面：只有当前会议状态<3可以删除;会后页面：只有当前会议状态!=5可以删除;
        Integer status = getStatus(saveAttachForm.getLifeId());
        Integer step = saveAttachForm.getType();
        if ( (step== 1 && status >= 3) || (step == 2 && status == 5)) {
            throw new ApiException("当前会议状态下该页面的附件不允许删除", new Result(errors, 3167, HttpStatus.OK.value()));
        }
        log.debug("删除谈心谈话附件");
        Long lifeId = saveAttachForm.getLifeId();
        Integer type = saveAttachForm.getType();
        Long dataId = saveAttachForm.getDataId();
        Long userId = saveAttachForm.getUserId();
        if(listTemplate.contains(type)){////如果是删除模板，连带附件一起删除
            this.deleteTalkTemplate(lifeId,type,dataId,userId);
        }else{//否则只是删除附件
            List fileName = new ArrayList();
            List url =  new ArrayList();
            for(LifeFileForm file : saveAttachForm.getLifeFile()){
                fileName.add(file.getFileName());
                url.add(file.getUrl());
            }
            lifeFileMapper.deleteTalkFile(lifeId,type,dataId,userId,fileName,url);
        }
    }

    /**
     * 删除谈心谈话模板-连带附件删除,如果是个人访谈则将征求意见-个人访谈数据删除了
     * @param lifeId
     * @param typeTalk
     * @param dataId
     * @param userId
     */
    private void deleteTalkTemplate(Long lifeId,Integer typeTalk,Long dataId,Long userId) {
        switch (typeTalk){
            case 22:
                lifeFileMapper.deleteTalkTemplate(lifeId,Arrays.asList(22,9),dataId,userId);
                //删除征求意见-个人访谈
                //删除关联关系
                lifeAdviceMapper.oneDelTalk(lifeId,dataId);
                break;
            case 23:
                lifeFileMapper.deleteTalkTemplate(lifeId,Arrays.asList(23,11),dataId,userId);
                break;
            case 24:
                lifeFileMapper.deleteTalkTemplate(lifeId,Arrays.asList(24,12),dataId,userId);
                break;
            case 25:
                lifeFileMapper.deleteTalkTemplate(lifeId,Arrays.asList(25,13),dataId,userId);
                break;
            case 26:
                lifeFileMapper.deleteTalkTemplate(lifeId,Arrays.asList(26,14),dataId,userId);
                break;
        }
    }

    /**
     * 上传人打开任务时校验
     *
     * @return Boolean
     * @Param lifeId  民主生活会id
     * @Param type 模块
     */
    public Boolean openTask(String taskInfo, HeaderHelper.SysHeader header) {
        List<String> taskInfos = Arrays.asList(taskInfo.split("_"));
        Long lifeId = Long.valueOf(taskInfos.get(1));
        Integer type = Integer.valueOf(taskInfos.get(2));
        List<LifeUploaderEntity> uploaderEntities = new ArrayList<>();
        if (taskInfos.size() < 4 || (taskInfos.size() == 4 && taskInfos.get(3).equals("null"))) {
            uploaderEntities = getLifeUploader(lifeId, type, null);
        } else {
            uploaderEntities = getLifeUploader(lifeId, type, Long.parseLong(taskInfos.get(3)));
        }
        Long userId = header.getUserId();
        //判断当前用户是否为指定上传人
        Boolean check_other = checkOther(userId, uploaderEntities);
        if (!check_other) {//如果该指定上传人被删除也会报此错
            throw new ApiException("只能由指定人进行上传!", new Result(errors, 3168, HttpStatus.OK.value()));
        }
        //判断是否超过截止日期
        //如果是他人上传，判断是否超过上传时间
        LifeUploaderEntity uploaderNow = uploaderEntities.stream().filter(i -> i.getUserId().equals(userId)).findFirst().get();
        if (LocalDateTime.now().isAfter(uploaderNow.getEndTime())) {
            throw new ApiException("超过任务截止时间不能上传!", new Result(errors, 3169, HttpStatus.OK.value()));
        }
        return true;
    }


    /**
     * 指定上传人并发起上传任务
     *
     * @return Boolean
     * @Param lifeFileTaskForm
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean fileTask(LifeFileTaskForm lifeFileTaskForm, HeaderHelper.SysHeader header, HttpHeaders headers) {
        //数据插入t_meeting_uploader
        List<UploaderVO> userList = insertUploader(lifeFileTaskForm, header);
        //2.发起任务
        createTask(lifeFileTaskForm, header, headers, userList);
//        //3.设置跳转地址
//        String url = meetingUrlConfig.getLifeModelUrl(lifeFileTaskForm.getType());
        //4.发送钉钉消息
        sendDingding(header.getRegionId(),userList,lifeFileTaskForm.getTitle());
        return true;
    }

    public void sendDingding(Long regionId, List<UploaderVO> userList,String taskName){
        for(UploaderVO user : userList){
            PushRequest pushRequest = new PushRequest();
            List<SendMsgFormMeeting> dataList = new ArrayList<>(1);
            SendMsgFormMeeting form = new SendMsgFormMeeting();
            // 设置模板所需 参数
            form.setUserName(user.getName());
            form.setTaskName(taskName);
//            form.setTargetUrl(url);//url跳转地址
            form.setSourceName("民主生活会");
            form.setUserId(user.getId());
            dataList.add(form);
            pushRequest.setTemplateId(205L);
            pushRequest.setChannelType((byte) 4);
            pushRequest.setSource("ows-meeting");
            pushRequest.setData(dataList);
            log.debug("mt民主生活会发送消息开始:"+pushRequest);
            sendPushRequest(pushRequest, regionId);
        }
    }

    /**
     * 消息发送
     *
     * @param pushRequest 推送中心封装类
     * @param regionId    区县id
     */
    public void sendPushRequest(PushRequest pushRequest, Long regionId) {
        String url = String.format("http://%s/global/push/diff", togServicesConfig.getPushCenter());
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("_region_id", regionId.toString());
        try {
            log.debug("预警推送消息开始->pushRequest:【{}】，url->【{}】", pushRequest, url);
            RemoteApiHelper.post(this.restTemplate, url, pushRequest, httpHeaders, new TypeReference<Result<Long>>() {
            });
            log.debug("预警推送消息成功->pushRequest:【{}】，url->【{}】", pushRequest, url);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("预警消息发送失败->pushRequest:【{}】，message->【{}】", pushRequest, e.getMessage(), e);
        }
    }


    //将localDateTime转为Date
    public Date getDate(LocalDateTime time) {
        if (time == null) {
            return new Date(9999, 1, 1, 23, 59, 10);
        } else {
            return Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
        }
    }

    /**
     * 数据插入t_meeting_uploader
     * @param lifeFileTaskForm
     * @param header
     * @return
     */
    //数据插入t_meeting_uploader
    public List<UploaderVO> insertUploader(LifeFileTaskForm lifeFileTaskForm, HeaderHelper.SysHeader header) {
        log.debug("插入t_meeting_uploader");
        Long createUser = header.getUserId();
        Long regionId = header.getRegionId();
        LocalDateTime now = LocalDateTime.now();
        List<UploaderVO> userListTask = new ArrayList<>();
        List<UploaderVO> userList = lifeFileTaskForm.getUsers();
        List<LifeUploaderEntity> loaders = new ArrayList<>();
        //查询数据库中uploader表已有哪些上传人，如果已有，则不操作。
        List<LifeUploaderEntity> lifeUploader = getLifeUploader(lifeFileTaskForm.getLifeId(),lifeFileTaskForm.getType(),lifeFileTaskForm.getDataId());
        List<Long> ids = new ArrayList<>();
        if(!CollectionUtils.isEmpty(lifeUploader)){
            ids = lifeUploader.stream().map(i->i.getUserId()).collect(Collectors.toList());
        }
        for (UploaderVO vo : userList) {
            //先判断该用户是否已在列表中
            if(!ids.contains(vo.getId())){//如已存在不会重复添加
                LifeUploaderEntity uploaderEntity = new LifeUploaderEntity();
                uploaderEntity.setUserId(vo.getId());
                uploaderEntity.setUsername(vo.getName());
                BeanUtils.copyProperties(lifeFileTaskForm, uploaderEntity, "users");
                uploaderEntity.setCreateUser(createUser);
                uploaderEntity.setCreateTime(now);
                loaders.add(uploaderEntity);
                userListTask.add(new UploaderVO(vo.getId(),vo.getName()));
            }
        }
        if(!CollectionUtils.isEmpty(loaders)){
            lifeUploaderMapper.insertList(loaders);
            return userListTask;
        }
        return Collections.EMPTY_LIST;

    }

    /**
     * 发起任务
     * @param lifeFileTaskForm
     * @param header
     * @param headers
     * @param userList:上传人list
     */
    private void createTask(LifeFileTaskForm lifeFileTaskForm, HeaderHelper.SysHeader header, HttpHeaders headers, List<UploaderVO> userList) {
        if(CollectionUtils.isEmpty(userList)){
            return;
        }
        log.debug("民主生活会发起指定上传任务");
        LifeEntity lifeEntity = lifeMapper.selectByPrimaryKey(lifeFileTaskForm.getLifeId());
        String year = "" + lifeEntity.getYears() + "年";
        String sourceMark = "民主生活会~~" + year;//第三方标签(用 ~~ 分隔)
        Integer type = lifeFileTaskForm.getType();
        LifeFileModelEntity lifeFileModelEntity = lifeFileModelMapper.selectByPrimaryKey(type);
        String modelName = lifeFileModelEntity.getName();
        String title = (lifeFileTaskForm.getType()==1 || lifeFileTaskForm.getType()== 15? modelName: "上传" + modelName);
        lifeFileTaskForm.setTitle(title);
        String id = "" + lifeFileTaskForm.getLifeId() + "_" + lifeFileTaskForm.getType() + "_" + lifeFileTaskForm.getDataId();
        String taskId = Constant.TASKKEY + "_" + id;//民主生活会任务唯一标识
        lifeFileTaskForm.setTaskId(taskId);
        Map<String, Object> uriMap = new HashMap<>();
        uriMap.put("type", Constant.TASKKEY);
        uriMap.put("id", id);
        try {
            //创建任务系统任务表单
            ThirdAddForm thirdAddForm = ThirdAddForm.builder().beginTime(new Date()).endTime(getDate(lifeFileTaskForm.getEndTime()))
                    .taskTitle(title).sourceId(taskId).sourceMark(sourceMark).sourceUri(JsonUtils.toJson(uriMap))
                    .createOrg(header.getOid()).callback(2).users(userList.stream().map(i -> i.getId()).collect(Collectors.toList())).build();
            //创建任务
            CreateTodoForm createTodoForm = CreateTodoForm.builder().thirdAddForm(thirdAddForm).build();
            thirdService.insertPending(headers, header.getRegionId(), header.getUserId(), 2, 2, createTodoForm);
        } catch (Exception e) {
            log.error("创建任务中心个人任务失败", e);
        }
    }

    /**
     *附件保存到数据库
     * @param saveAttachForm
     * @return list  :存入数据库附件信息
     *
     */
    public List<LifeFileEntity> saveAttchDb(SaveAttachForm saveAttachForm) {
        log.debug("判断是否为征求意见，如是，保存征求意见到advice表");
        Long dataId = saveAdvice(saveAttachForm);
        //如果是谈心谈话的附件，先判断是否已存在，如果已存在，则先删除，再新增
        if(listTalk.contains(saveAttachForm.getType())){
            handleExistTalkFle(saveAttachForm);
        }
        log.debug("保存附件到数据库");
        List<LifeFileForm> lifeFiles = saveAttachForm.getLifeFile();
        //3.附件信息保存至数据库
        List<LifeFileEntity> files = new ArrayList<>();
        for (LifeFileForm file : lifeFiles) {
            LifeFileEntity lifeFileEntity = new LifeFileEntity();
            //将SaveAttachForm属性复制到lifeFileEntity
            BeanUtils.copyProperties(saveAttachForm, lifeFileEntity, "lifeFile", "taskId");
            lifeFileEntity.setFileId(file.getFileId());
            lifeFileEntity.setFileName(file.getFileName());
            lifeFileEntity.setFileNameDown(file.getFileNameDown());
            lifeFileEntity.setUrl(file.getUrl());
            lifeFileEntity.setIsSubmit(0);
            lifeFileEntity.setIsDel(0);
            lifeFileEntity.setCreateTime(LocalDateTime.now());
            lifeFileEntity.setCreateUser(saveAttachForm.getUserId());
            lifeFileEntity.setLastChangeUser(saveAttachForm.getUserId());
            lifeFileEntity.setUpdateTime(LocalDateTime.now());
            if((saveAttachForm.getType()==7 && saveAttachForm.getDataId()==null)){//征求意见直接上传的datd_id为上面插入的id，注意附件表里谈心谈话的附件data_id为沈健那边的id
                lifeFileEntity.setDataId(dataId);
            }//如果是已有数的附件上传，则data_id为前端传入的data_id
            files.add(lifeFileEntity);
        }
        if (!files.isEmpty()) {
            lifeFileMapper.insertList(files);
        }
        return files;
    }

    private void handleExistTalkFle(SaveAttachForm saveAttachForm) {//用于谈心谈话新增附件，先将原附件全部删除
        //判断当前会议状态，会前/会中页面：只有当前会议状态<3可以删除;会后页面：只有当前会议状态!=5可以删除;
        Integer status = getStatus(saveAttachForm.getLifeId());
        Integer step = saveAttachForm.getType();
        if ( (step== 1 && status >= 3) || (step == 2 && status == 5)) {
            throw new ApiException("当前会议状态下该页面的附件不允许再新增!", new Result(errors, 3171, HttpStatus.OK.value()));
        }
        lifeFileMapper.deleteTalkFileAll(saveAttachForm.getLifeId(),saveAttachForm.getType(),saveAttachForm.getDataId(),saveAttachForm.getUserId());
    }

    private Long saveAdvice(SaveAttachForm saveAttachForm) {
        //判断是不是征求意见-个人访谈，如果是新增数据，则先将征求意见-个人访谈保存至advice表（谈心谈话都是新增行）
        Long dataId = 0L;
        if(saveAttachForm.getType()==22 || (saveAttachForm.getType()==7 && saveAttachForm.getDataId()==null)){//代表新增数据,需求变更，直接上传只有一条数据对应多个附件
//        if(saveAttachForm.getType()==22){
            LifeAdviceEntity lifeAdviceEntity = new LifeAdviceEntity();
            lifeAdviceEntity.setLifeId(saveAttachForm.getLifeId());
            lifeAdviceEntity.setStep(saveAttachForm.getStep());
            lifeAdviceEntity.setIsDel(0);
            lifeAdviceEntity.setCreateTime(LocalDateTime.now());
            lifeAdviceEntity.setCreateUser(saveAttachForm.getUserId());
            lifeAdviceEntity.setLastChangeUser(saveAttachForm.getUserId());
            lifeAdviceEntity.setUpdateTime(LocalDateTime.now());
            Integer adviceType = (saveAttachForm.getType()==22?4:1);//直接上传是1，个人访谈是4
            lifeAdviceEntity.setAdviceType(adviceType);
            lifeAdviceEntity.setDataId(saveAttachForm.getDataId());
            lifeAdviceMapper.insert(lifeAdviceEntity);
            dataId = lifeAdviceEntity.getAdviceId();//如果没有传入dataid，主要针对首次直接上传/个别访谈，将数据行id返回

        }
        return dataId;
    }

    /**
     * 判断该有数据行id但没有传入数据行id
     * @param saveAttachForm
     * @return true:该有并且有  false：该有但没有
     */
    private Boolean getDataId(SaveAttachForm saveAttachForm) {
        //0.判断参数data_id
        LifeFileModelEntity model = lifeFileModelMapper.selectByPrimaryKey(saveAttachForm.getType());
        //需要明细数据行，但没有数据行id
        return model.getCheck() != 1 || saveAttachForm.getDataId() != null;
    }

    /**
     * 判断是否为指定上传人
     * @param userId
     * @param lifeUploaderEntities
     * @return true:是指定人 false：不是指定上传人
     *
     */
    private Boolean checkOther(Long userId, List<LifeUploaderEntity> lifeUploaderEntities) {
        Map<Long, String> assertUploader = queryUploader(lifeUploaderEntities);
        return assertUploader.containsKey(userId);
    }

    /**
     * 根据会议id type data_id查询数据库现有附件数量，用来判断是否超量上传
     * @param saveAttachForm
     * @return
     */
    private Integer selNumDb(SaveAttachForm saveAttachForm) {//totalNum允许上传的最大数量
        Integer numDb = lifeFileMapper.selectNum(saveAttachForm);
        return numDb;
    }

    /**
     * 将meeting相关的附件插入到民主生活会附件中
     * @param lifeId
     */
    //将meeting相关的附件插入到民主生活会附件中
    public void cloneMeetingToLife(Long lifeId) {
        Map<Integer, List<LifeAndMeetingVO>> map = new HashMap<>();
        //组织学习
        queryMeetingStudyFile(lifeId, map, 3,1);
        List<LifeFileEntity> list = insMeetingFile(lifeId, map, 1);
        log.debug("复制附件表到会后-不含征求意见");
        cloneToAfter(list);
        //发起民主生活会
        Map<Integer, List<LifeAndMeetingVO>> mapNew = new HashMap<>();
        queryMeetingFile(lifeId, mapNew, 17);
        log.debug("组织学习、发起民主生活会插入附件表");
        List<LifeFileEntity> listNew = insMeetingFile(lifeId, mapNew, 1);
//        log.debug("查询征求意见-问卷调查"+lifeId);
//        Map<Integer, List<LifeAndMeetingVO>> mapExam = queryAdviceFile(lifeId, 2, 8, 1);
        log.debug("查询征求意见-座谈会"+lifeId);
        Map<Integer, List<LifeAndMeetingVO>> mapTalk = queryAdviceFile(lifeId, 3, 10, 1);
//        mapExam.putAll(mapTalk);//合并map
        log.debug("会议结束征求意见插入附件表"+lifeId+"会前");
        insMeetingFile(lifeId, mapTalk, 1);
        //插入后打上将附件已插入标志
        stringRedisTemplate.opsForValue().set(Constant.LIFEFILE + lifeId + "_" + 1, "ok");
    }

    /**
     * 复制附件表到会后-排除征求意见（征求意见-座谈会、问卷调查是关联查询）
     * @Param list :要复制的附件
     */
    private void cloneToAfter(List<LifeFileEntity> list) {
        if (list == null || list.size() <= 0) {
            return;
        }
        list.stream().forEach(i -> i.setStep(2));
        lifeFileMapper.insertList(list);
    }

    /**
     * 将meeting关联的附件插入到民主生活会附件表中
     * @param lifeId
     * @param map
     * @param step
     * @return
     */
    private List<LifeFileEntity> insMeetingFile(Long lifeId, Map<Integer, List<LifeAndMeetingVO>> map, Integer step) {
        log.debug(" 将meeting关联的附件插入到民主生活会附件表中"+lifeId+"_"+step);
        if (map != null && map.size() > 0) {
            log.debug("上报前调用王若宇方法返回对应模块的所有附件开始");
            meetingPeoplePartyLifeService.getFileByMeetings(map);
            log.debug("上报前调用王若宇方法返回对应模块的所有附件结束");
            List<LifeFileEntity> list = getLifeFileEntities(lifeId, map, step);
            log.debug("数据处理后插入民主生活会附件表"+list);
            if (!CollectionUtils.isEmpty(list)) {
                lifeFileMapper.insertList(list);
                log.debug("数据处理后插入民主生活会附件表11"+list);
                return list;
            }
        }

        return Collections.EMPTY_LIST;
    }

    /**
     * 对map里的附件信息进行字段补充，map为meeting返回的map
     * @param lifeId
     * @param meetingFileMap
     * @param step
     * @return
     */
    private List<LifeFileEntity> getLifeFileEntities(Long lifeId, Map<Integer, List<LifeAndMeetingVO>> meetingFileMap, Integer step) {
        log.debug("getLifeFileEntities"+"_"+lifeId+"_"+step);
        List<LifeFileEntity> list = new ArrayList<>();
        Set<Map.Entry<Integer, List<LifeAndMeetingVO>>> entrySet = meetingFileMap.entrySet();
        for (Map.Entry<Integer, List<LifeAndMeetingVO>> entry : entrySet) {
            Integer type = entry.getKey();
            List<LifeAndMeetingVO> lifeFileMeetingAll = entry.getValue();
            List<LifeAndMeetingVO> lifeFileMeeting = lifeFileMeetingAll.stream().filter(meeting -> !CollectionUtils.isEmpty(meeting.getMeetingFiles())).collect(Collectors.toList());
            for (LifeAndMeetingVO vo : lifeFileMeeting) {
                Long dataId = vo.getDataId();
                List<LifeAndMeetingVO.LifeAndMeetingFile> files = vo.getMeetingFiles();
                if (files != null && files.size() > 0) {
                    for (LifeAndMeetingVO.LifeAndMeetingFile file : files) {
                        LifeFileEntity lifeFile = new LifeFileEntity();
                        lifeFile.setFileName(file.getFileName());
                        lifeFile.setFileNameDown(file.getFileName());
                        lifeFile.setFileId(file.getFileId());
                        lifeFile.setUrl(file.getPath());
                        lifeFile.setStep(step);
                        lifeFile.setType(type);
                        lifeFile.setIsSubmit(0);
                        lifeFile.setLifeId(lifeId);
                        lifeFile.setIsDel(0);
                        lifeFile.setIsDirect(1);
                        lifeFile.setCreateTime(LocalDateTime.now());
                        lifeFile.setUpdateTime(LocalDateTime.now());
                        lifeFile.setCreateUser(null);
                        lifeFile.setDataId(dataId);
                        list.add(lifeFile);
                    }
                }

            }
        }
        return list;
    }

    /**
     * 更改会议状态
     * @param lifeId
     * @param status
     * @param header
     */
    public void changeStatus(Long lifeId, Integer status, HeaderHelper.SysHeader header) {
        log.debug("修改会议状态，lifeId -> [{}]，status -> [{}]，header -> [{}]", lifeId, status, header);
        LifeEntity entity = new LifeEntity();
        entity.setLifeId(lifeId);
        entity.setStatus(status);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setLastChangeUser(header.getUserId());
        lifeMapper.updateByPrimaryKeySelective(entity);
    }
}
