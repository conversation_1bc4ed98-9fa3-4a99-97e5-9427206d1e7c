package com.goodsogood.ows.service

import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import com.goodsogood.ows.common.Constant
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.Escape
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.LocalDateTimeUtils
import com.goodsogood.ows.mapper.MeetingCommentApproveMapper
import com.goodsogood.ows.mapper.MeetingCommentMapper
import com.goodsogood.ows.mapper.MeetingCommentMemberComplexMapper
import com.goodsogood.ows.mapper.MeetingCommentMemberMapper
import com.goodsogood.ows.model.db.*
import com.goodsogood.ows.model.vo.*
import com.goodsogood.ows.model.vo.activity.UserInfoBase
import org.apache.ibatis.session.RowBounds
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import tk.mybatis.mapper.entity.Example
import java.time.LocalDateTime
import java.time.Year

@Service
class CommentService(@Autowired val errors: Errors,
                     @Autowired val openService: OpenService,
                     @Autowired val thirdService: ThirdService,
                     @Autowired val orgTypeConfig: OrgTypeConfig,
                     @Autowired val commentFileService: CommentFileService,
                     @Autowired val commentStaticService: CommentStaticService,
                     @Autowired val meetingCommentMapper: MeetingCommentMapper,
                     @Autowired val meetingCommentMemberMapper: MeetingCommentMemberMapper,
                     @Autowired val commentApproveMapper: MeetingCommentApproveMapper,
                     @Autowired val simpleApplicationConfigHelper: SimpleApplicationConfigHelper) {

    private val log = LoggerFactory.getLogger(CommentService::class.java)


    /**
     * 获取评议列表
     * @param year          年度
     * @param status        状态
     * @param includeChild  包含下级
     * @param type          类型      1-包含未开启，2-不包含未开启
     * @param headers       请求头
     */
    fun getCommentList(year: Int?, orgId: Long?, status: Int?, includeChild: Int, type: Int, headers: HttpHeaders): MutableList<MeetingCommentForm> {
        log.debug("获取评议列表 -> year:[${year}],orgId:[${orgId}] status:[${status}]")
        val header = HeaderHelper.buildMyHeader(headers)
        // 判断当前登录组织，如果是党小组，则反查党支部信息，并且查询结果不包含未开启的民主评议数据
        var flag = if(type != 1) 0 else 1
        var oid = orgId ?: (header.uoid ?: header.oid)
        val currentYear = Year.now().value
        // 查询当前登录组织详情
        var orgInfo = thirdService.findOrgInfoByOrgId(oid)
        if (orgInfo != null) {
            if (orgTypeConfig.communistGroup.contains(orgInfo.orgTypeChild)) {
                oid = thirdService.findBranchByGroup(header.regionId, oid)
                orgInfo = thirdService.findOrgInfoByOrgId(oid)
                flag = 0
            } else if (!orgTypeConfig.branchChild.contains(orgInfo.orgTypeChild)) {
                throw ApiException("当前组织类型不是党支部",
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), "当前组织类型不是党支部"))
            }
        } else {
            throw ApiException("调取用户中心，获取组织信息失败",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "调取用户中心，获取组织信息失败"))
        }
        val commentList = selectCommentList(year, status, oid, includeChild, flag)
        // 封装返回数据
        var resultList  = mutableListOf<MeetingCommentForm>()
        if (commentList.isNotEmpty()) {
            resultList = commentList.map {
                MeetingCommentForm(
                    it.commentId,
                    it.orgId,
                    it.orgName,
                    it.year,
                    it.status,
                    CommentStatusEnum.getCommentStatusEnum(it.status)?.value
                )
            }.toMutableList()
        }
        // 判断查询结果里面是否包含当前年份的数据，若没有插入一条当年的数据
        // 2023-04-04 按照要求移除这段属于王飞的逻辑 需求编号：4129
        /*if (type == 1 && (year == null || year == currentYear) && (status == null || status == 0)) {
            val entity = selectCommentByOrgAndYear(oid, currentYear)
            if (entity == null) {
                val filter = commentList.filter { it.year == currentYear }
                if (filter.isEmpty()) {
                    resultList.add(
                        MeetingCommentForm(
                            null,
                            oid,
                            orgInfo?.name,
                            currentYear,
                            CommentStatusEnum.UNOPENED.key,
                            CommentStatusEnum.UNOPENED.value
                        )
                    )
                }
            }
        }*/
        resultList.sortBy { it.year }
        return resultList
    }



    /**
     * 批量查询Ids
     */
    fun getCommentListById(commentIds: List<Long>?, headers: HttpHeaders): MutableList<MeetingCommentForm> {
        var resultList = mutableListOf<MeetingCommentForm>()
        if (!commentIds.isNullOrEmpty()) {
            val example = Example(MeetingCommentEntity::class.java)
            example.createCriteria().andIn("commentId", commentIds)
            val entities = meetingCommentMapper.selectByExample(example)
            if (!entities.isNullOrEmpty()) {
                resultList = entities.map {
                    MeetingCommentForm(
                        it.commentId,
                        it.orgId,
                        it.orgName,
                        it.year,
                        it.status,
                        CommentStatusEnum.getCommentStatusEnum(it.status)?.value
                    )
                }.toMutableList()
            }
        }
        return resultList
    }

    /**
     * 开启民主评议
     * @param   commentId
     * @param   orgId
     * @param   year
     * @param   headers
     */
    @Transactional
    fun startComment(commentId: Long?, orgId: Long, year: Int, excellentNum: Int = 0, headers: HttpHeaders): String {
        log.debug("开启民主评议 -> commentId:[${commentId}], orgId:[${orgId}], year:[${year}], excellentNum:[${excellentNum}]")
        val header = HeaderHelper.buildMyHeader(headers)
        // 查询当前时间是否有其他开启的民主评议
        var comment : MeetingCommentEntity? = selectCommentByOrgAndYear(orgId, year)
        if (comment != null && comment.status != 0) {
            throw ApiException(
                "当前民主评议已开启",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "当前民主评议已开启")
            )
        }
        // 判断组织是否是党支部
        val orgInfo = thirdService.findOrgInfoByOrgId(orgId)
        if (!orgTypeConfig.branchChild.contains(orgInfo?.orgTypeChild)) {
            throw ApiException("当前组织类型不是党支部",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "当前组织类型不是党支部"))
        }
        val i = selectCommentOpenStatus(orgId, commentId ?: comment?.commentId)
        if (i != 0) {
            throw ApiException(
                "当前存在已开启的民主评议，不能同时开启两条民主评议",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "当前存在已开启的民主评议，不能同时开启两条民主评议")
            )
        }
        var id = commentId
        if (commentId != null || comment != null) {
            if (commentId != null) {
                comment = meetingCommentMapper.selectByPrimaryKey(commentId)
            }
            if (comment != null) {
                id = comment.commentId
                comment.excellentNum = excellentNum
                comment.status = CommentStatusEnum.ACTIVATED.key
                comment.updateTime = LocalDateTime.now()
                comment.lastChangeUser = header.userId
                meetingCommentMapper.updateByPrimaryKey(comment)
            }
        } else {
            val orgInfo = thirdService.findOrgInfoByOrgId(orgId)
            if (orgInfo != null) {
                comment = MeetingCommentEntity()
                comment.orgId = orgId
                comment.orgName = orgInfo.name
                comment.orgLevel = orgInfo.orgLevel
                comment.parentId = orgInfo.parentId
                comment.year = year
                comment.excellentNum = excellentNum
                comment.status = CommentStatusEnum.ACTIVATED.key
                comment.createTime = LocalDateTime.now()
                comment.createUser = header.userId
                comment.regionId = header.regionId
                meetingCommentMapper.insert(comment)
                id = comment.commentId
            } else {
                throw ApiException(
                    "调取用户中心，获取组织信息失败",
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), "调取用户中心，获取组织信息失败")
                )
            }
        }

        // 开启民主评议，拉取用户中心本支部的人员
        if (id != null) {
            // 生成党员
            val userList = thirdService.getUserListByOrgId(orgId, 1, headers)
            addCommentMemberList(userList, id, year, header.userId, headers)
            // 统计
            createCommentStatics(id, headers)
            // 测评表
            createCommentEvaluation(id, headers)
            // 创建自评互评表
            createAppraisal(id, headers)
        } else {
            throw ApiException(
                "操作民主评议失败，请联系管理员",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "操作民主评议失败，请联系管理员")
            )
        }

        return Constant.SUCCESS
    }

    /**
     * 提交民主评议审核
     * @param commentId
     * @param headers
     */
    fun submit(commentId: Long, headers: HttpHeaders) : String{
        log.debug("提交民主评议审核 -> commentId:[${commentId}], headers:[${headers}]")
        val header = HeaderHelper.buildMyHeader(headers)
        // 查询出民主评议数据
        val example = Example(MeetingCommentEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andEqualTo("commentId", commentId)
            .andIn("status", mutableListOf(CommentStatusEnum.ACTIVATED.key, CommentStatusEnum.REVIEW_FAILED.key, CommentStatusEnum.PEND_FAILED.key))
        val entity = meetingCommentMapper.selectOneByExample(example)
        if (entity != null) {
            // 判断优秀党员数量
            val memberList = meetingCommentMemberMapper.getCommentMemberList(
                commentId = commentId,
                complexRating = CommentRatingEnum.EXCELLENT.key
            )
            if (memberList.size > entity.excellentNum) {
                throw ApiException(
                    "提交民主评议失败，实际优秀党员数量[${memberList.size}]大于设置的优秀党员数量[${entity.excellentNum}]",
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), "提交民主评议失败，实际优秀党员数量[${memberList.size}]大于设置的优秀党员数量[${entity.excellentNum}]")
                )
            }
            // 提交上级组织审核时，判定组织层级关系
            // 若上级组织为顶级组织, 状态为审定
            // 若上级组织为一级组织, 状态为审定
            // 若上级组织为二级组织, 状态为审查
            val orgData = simpleApplicationConfigHelper.getOrgByRegionId(header.regionId)
            val parentOrg = thirdService.findOrgInfoByOrgId(entity.parentId)
                ?: throw ApiException(
                    "提交民主评议失败，远程服务调用用户中心失败",
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), "提交民主评议失败，请联系管理员")
                )
            if (entity.parentId == orgData.orgId || parentOrg.parentId == orgData.orgId) {
                // 上级组织为顶级组织，则直接提交审定
                entity.status = CommentStatusEnum.PENGDING.key
            }  else {
                entity.status = CommentStatusEnum.REVIEWING.key
            }
            entity.updateTime = LocalDateTime.now()
            entity.lastChangeUser = header.userId
            meetingCommentMapper.updateByPrimaryKey(entity)
        } else {
            throw ApiException("该民主评议状态不能提交",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "该民主评议状态不能提交"))
        }
        return Constant.SUCCESS
    }

    /**
     * 查询民主评议是否可以编辑
     * @param commentId
     * @param statusList
     */
    fun isEdit(commentId: Long? = null, statusList: MutableList<Int> = mutableListOf(0,2,4,6)) {
        val edit = meetingCommentMapper.isEdit(commentId, statusList)
        if (edit == 0) {
            throw ApiException("本次民主评议不能编辑",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "本次民主评议不能编辑")
            )
        } else if(edit == -1) {
            throw ApiException("本次民主评议暂未开启",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "本次民主评议暂未开启")
            )
        }
    }

    /**
     * 查询民主评议是否可以提交
     * @param commentId
     * @return 1 - 不可以，0 - 可以
     */
    fun isSubmit(commentId: Long): Int {
        var result = 0
        val meetingComment = getMeetingComment(commentId)
        if (meetingComment != null) {
            val status = meetingComment.status
            if (status == 2 || status == 4 || status == 6) {
                result = 1
            }
        }
        return result
    }

    fun getCommentByHeader(commentId: Long? = null, headers: HttpHeaders) : MeetingCommentForm? {
        val header = HeaderHelper.buildMyHeader(headers)
        val orgId = header.uoid ?: header.oid
        val commentEntity: MeetingCommentEntity? = if (commentId == null) {
            selectCommentByOrgId(orgId)
        } else {
            meetingCommentMapper.selectByPrimaryKey(commentId)
        }
        return commentEntity?.let { MeetingCommentForm(commentEntity.commentId, commentEntity.orgId, commentEntity.orgName, commentEntity.year, commentEntity.status, CommentStatusEnum.getCommentStatusEnum(commentEntity.status)?.value) }
    }

    /**
     * 查询民主评议
     * @param commentId
     */
    fun getMeetingComment(commentId: Long?) : MeetingCommentEntity? {
        return meetingCommentMapper.selectByPrimaryKey(commentId)
    }

    /**
     * 查询民主评议列表 - 支部管理员自评互评
     * @param year          年度
     * @param status        状态
     * @param orgId         组织ID
     * @param includeChild  是否包含下级 0-不包含，1-包含
     * @param flag          1-查询状态包含未开启的，0-查询状态不包含未开启的，2-查询进行审查的数据，3-查询进入审定的数据
     */
    fun selectCommentList(year: Int?, status: Int?, orgId: Long, includeChild: Int, flag: Int): MutableList<MeetingCommentEntity> {
        return meetingCommentMapper.selectByExample(createExample(year, status, orgId, includeChild, flag))
    }

    /**
     * 查询民主评议列表 - 支部管理员自评互评
     * @param year          年度
     * @param status        状态
     * @param orgId         组织ID
     * @param includeChild  是否包含下级 0-不包含，1-包含
     * @param flag          1-查询状态包含未开启的，0-查询状态不包含未开启的，2-查询进行审查的数据，3-查询进入审定的数据
     */
    fun selectCommentPage(year: Int?, status: Int?, orgId: Long, includeChild: Int, flag: Int, page: Int = 0, pageSize: Int = 10): Page<MeetingCommentEntity> {
        return PageHelper.startPage<CommentMemberAppraisalResultForm>(page, pageSize)
            .doSelectPage {
                meetingCommentMapper.selectByExample(createExample(year, status, orgId, includeChild, flag))
            }
    }

    /**
     * 自动创建民主评议
     */
    @Transactional
    fun autoGeneraComment(regionId: Long, year: Int) {
        log.debug("自动创建民主评议开始 -> regionId:[${regionId}], year[${year}]")
        val orgData = simpleApplicationConfigHelper.getOrgByRegionId(regionId)
        val orgInfoList = openService.getOrgInfoList(orgData.orgId, Constant.YES)
        orgInfoList.forEach { orgInfo ->
            log.debug("自动生成民主评议[orgId:${orgInfo}, year:${year}] -- start")
            val headers = HttpHeaders()
            headers.set(HeaderHelper.OPERATOR_REGION, regionId.toString())
            headers.set(HeaderHelper.OPERATOR_ID, "-999")
            headers.set(HeaderHelper.OPERATOR_NAME, Escape.escape("自动生成"))
            if (orgTypeConfig.branchChild.contains(orgInfo.orgTypeChild )) {
                // 如果是党支部创建民主评议
                val comment = selectCommentByOrgAndYear(orgInfo.orgId, year) ?: MeetingCommentEntity()
                if (comment.commentId == null) {
                    comment.year = year
                    comment.orgId = orgInfo.orgId
                    comment.parentId = orgInfo.parentId
                    comment.orgName = orgInfo.orgName
                    comment.orgLevel = orgInfo.orgLevel
                    comment.regionId = regionId
                    comment.status = CommentStatusEnum.UNOPENED.key
                    comment.createTime = LocalDateTime.now()
                    comment.createUser = -999
                    meetingCommentMapper.insert(comment)
                    comment.commentId?.let { commentId ->
                        // 新建民主评议党员表
                        val userList = thirdService.getUserListByOrgId(orgInfo.orgId, 1, headers)
                        addCommentMemberList(userList, commentId, year, -999, headers)
                        // 统计
                        createCommentStatics(commentId, headers)
                        // 测评表
                        createCommentEvaluation(commentId, headers)
                    }
                }
            }
            log.debug("自动生成民主评议[orgId:${orgInfo}, year:${year}] -- end")
        }
    }


    private fun createExample(year: Int?, status: Int?, orgId: Long, includeChild: Int, flag: Int): Example {
        val ex =  Example(MeetingCommentEntity::class.java)
        val criteria = ex.createCriteria()
        year?.let { criteria.andEqualTo("year", it) }
        status?.let { criteria.andEqualTo("status", it) }
        when (flag) {
            0 -> {
                criteria.andNotEqualTo("status", CommentStatusEnum.UNOPENED.key)
            }
            2 -> {
                criteria.andGreaterThan("status", CommentStatusEnum.ACTIVATED.key)
            }
            3 -> {
                criteria.andGreaterThan("status", CommentStatusEnum.REVIEW_FAILED.key)
            }
        }
        val criteriaAnd = ex.createCriteria()
        criteriaAnd.andEqualTo("orgId", orgId)
        if (includeChild == 1) {
            criteriaAnd.orLike("orgLevel", "%-${orgId}-%")
        }
        ex.and(criteriaAnd)
        return ex
    }

    fun insertOrUpdateComment(commentEntity: MeetingCommentEntity) {
        if (commentEntity.commentId == null)
            meetingCommentMapper.insert(commentEntity)
        else
            meetingCommentMapper.updateByPrimaryKey(commentEntity)
    }

    /**
     * 创建登记表
     */
    fun createGradeForm(commentMemberId: Long?, commentId: Long? = null, headers: HttpHeaders) {
        commentMemberId?.let {
            log.debug("生成登记表调用 [commentMemberId:${commentMemberId}] -- start")
            commentFileService.selfTemplateHandle(it, commentId, headers)
            log.debug("生成登记表调用 [commentMemberId:${commentMemberId}] -- end")
        }
    }

    fun createGradeFormByComment(commentId: Long?, headers: HttpHeaders) {
        commentId?.let {
            val memberEntities = getCommentMemberList(commentId)
            memberEntities.forEach {member ->
                createGradeForm(member.commentMemberId, commentId, headers)
            }
        }
    }

    /**
     * 统计民主评议
     */
    fun createCommentStatics(commentId: Long, headers: HttpHeaders) {
        log.debug("统计民主评议调用 [commentId:${commentId}] -- start")
        commentStaticService.saveToStatistics(mutableListOf(commentId), headers)
        log.debug("统计民主评议调用 [commentId:${commentId}] -- end")
    }

    /**
     * 生成测评表
     */
    fun createCommentEvaluation(commentId: Long, headers: HttpHeaders) {
        log.debug("生成测评表调用 [commentId:${commentId}] -- start")
        commentFileService.orgTemplateHandle(commentId, headers)
        log.debug("生成测评表调用 [commentId:${commentId}] -- end")
    }

    fun selectCommentByOrgId(orgId: Long) : MeetingCommentEntity? {
        val example = Example(MeetingCommentEntity::class.java)
        example.createCriteria().andEqualTo("orgId", orgId)
            .andGreaterThan("status", 0)
            .andLessThan("status", 6)
        val commentList = meetingCommentMapper.selectByExample(example)
        return if (commentList.size > 1) {
            throw ApiException("数据错误，orgId [${orgId}] 存在多个开启的民主评议",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "数据错误，当前组织存在多个开启的民主评议"))
        } else if (commentList.size == 1) commentList[0]
        else null
    }

    /**
     * 查询优秀党员数量
     */
    fun getExcellentData(commentId: Long?, orgId: Long?, headers: HttpHeaders) : CommentExcellentForm {
        var excellentNum = 0
        var userNum = 0
        if (commentId == null) {
            if (orgId == null) {
                throw ApiException("查询设置优秀党员页面数据报错，commentId:[null]，orgId:[null] 都为空",
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), "调用接口失败，参数出现问题"))
            }
            val userList = thirdService.getUserListByOrgId(orgId, 1, headers)
            userNum = userList.filter { it.politicalType == 1 }.size
        } else {
            val entity = meetingCommentMapper.selectByPrimaryKey(commentId)
            if (entity != null) {
                val userList = entity.orgId?.let { thirdService.getUserListByOrgId(it, 1, headers) }
                excellentNum = entity.excellentNum
                if (userList != null) {
                    userNum = userList.filter { it.politicalType == 1 }.size
                }
            }
        }
        return CommentExcellentForm(partyMemberNum = userNum, excellentNum =  excellentNum)
    }

    fun setExcellentData(commentId: Long, excellentNum: Int, headers: HttpHeaders) {
        val header = HeaderHelper.buildMyHeader(headers)
        val entity = meetingCommentMapper.selectByPrimaryKey(commentId)
        if (entity != null) {
            entity.excellentNum = excellentNum
            entity.updateTime = LocalDateTime.now()
            entity.lastChangeUser = header.userId
            meetingCommentMapper.updateByPrimaryKey(entity)
        } else {
            throw ApiException("设置优秀党员数据报错，commentId:[${commentId}]， 查询民主评议为空",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "查询民主评议为空"))
        }
    }

    /**
     * 根据组织和年份查询民主评议数据
     * @param orgId
     * @param year
     */
    private fun selectCommentByOrgAndYear(orgId: Long, year: Int): MeetingCommentEntity? {
        val example = Example(MeetingCommentEntity::class.java)
        example.createCriteria().andEqualTo("orgId", orgId)
            .andEqualTo("year", year)
        val commentList = meetingCommentMapper.selectByExample(example)
        return if (commentList.size > 1) {
            throw ApiException("数据错误，year[${year}] orgId [${orgId}] 重复数据",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "数据错误，当前组织在${year}存在多条"))
        } else if (commentList.size == 1) commentList[0]
        else null
    }

    /**
     * 查询当前时间是否有其他开启的民主评议
     * @param orgId
     * @param commentId
     */
    private fun selectCommentOpenStatus(orgId: Long, commentId: Long?) : Int {
        val example = Example(MeetingCommentEntity::class.java)
        val criteria = example.createCriteria().andEqualTo("orgId", orgId)
            .andGreaterThan("status", 0)
            .andLessThan("status", 6)
        if (commentId != null) {
            criteria.andNotEqualTo("commentId", commentId)
        }
        return meetingCommentMapper.selectCountByExample(example)
    }

    /**
     * 新增民主评议党员表
     * @param userList
     * @param commentId
     * @param year
     * @param createUser
     */
    fun addCommentMemberList(userList: List<UserInfoBase>, commentId: Long, year: Int? = null, createUser: Long, headers: HttpHeaders) {
        if (userList.isNotEmpty()) {
            val commentMemberList = getCommentMemberList(commentId)
            val memberUserIdList = commentMemberList.map { it.userId }
            userList.filter { it.politicalType == 1 }.forEach { user ->
                if (!memberUserIdList.contains(user.userId)) {
                    val commentMember = MeetingCommentMemberEntity()
                    commentMember.commentId = commentId
                    commentMember.year = year
                    commentMember.userId = user.userId
                    commentMember.userName = user.userName
                    commentMember.phone = user.phone
                    commentMember.phoneSecret = user.phoneSecret
                    commentMember.politicalType = user.politicalType
                    commentMember.orgId = user.orgId
                    commentMember.orgName = user.orgName
                    commentMember.isDraft = 0
                    commentMember.createTime = LocalDateTime.now()
                    commentMember.createUser = createUser
                    meetingCommentMemberMapper.insert(commentMember)
                    // 生成登记表
                    createGradeForm(commentMember.commentMemberId, commentId, headers)
                }
            }
        }
    }

    fun getCommentMemberList(commentId: Long) : MutableList<MeetingCommentMemberEntity> {
        val ex = Example(MeetingCommentMemberEntity::class.java)
        val criteria = ex.createCriteria()
        criteria.andEqualTo("commentId", commentId)
        criteria.andEqualTo("politicalType", 1)
        return meetingCommentMemberMapper.selectByExample(ex)
    }

    fun updateUser(userId: Long, politicalType: Int, time: String, headers: HttpHeaders) {
        log.debug("民主评议更新用政治面貌: $userId 的政治面貌: $politicalType")
        val commentMemberId = meetingCommentMapper.editableCommentMember(userId)
        if (commentMemberId != null) {
            val commentMemberEntity = meetingCommentMemberMapper.selectByPrimaryKey(commentMemberId)
            commentMemberEntity.politicalType = politicalType
            commentMemberEntity.updateTime = LocalDateTime.now()
            commentMemberEntity.lastChangeUser = -999
            meetingCommentMemberMapper.updateByPrimaryKey(commentMemberEntity)
            createGradeForm(commentMemberId, commentMemberEntity.commentId, headers)
            commentMemberEntity.commentId?.let {
                createGradeForm(commentMemberId, it, headers)
                // 统计
                createCommentStatics(it, headers)
                // 测评表
                createCommentEvaluation(it, headers)
                // 创建自评互评表
                createAppraisal(it, headers)
            }
        }
    }

    /**
     * @title 创建自评互评表
     * @description
     * <AUTHOR>
     * @param commentId     民主评议ID
     * @updateTime 2022/3/28 4:42 PM
     * @throws
     */
    fun createAppraisal(commentId: Long, headers: HttpHeaders) {
        val commentEntity = meetingCommentMapper.selectByPrimaryKey(commentId)
        if (commentEntity  != null) {
            val countMember = meetingCommentMapper.selectCountMember(commentId)
            val dataForm = CommentUserAppraisalDataForm()
            dataForm.commentId = commentId
            dataForm.orgName = commentEntity.orgName
            dataForm.year = commentEntity.year.toString()
            if (commentEntity.status == CommentStatusEnum.APPROVED.key) {
                val ex = Example(MeetingCommentApproveEntity::class.java)
                ex.createCriteria().andEqualTo("commentId", commentId)
                ex.orderBy("createTime").desc()
                val rowBounds = RowBounds(0, 1)
                val approveList = commentApproveMapper.selectByExampleAndRowBounds(ex, rowBounds)
                if (approveList.size == 1) {
                    dataForm.time = LocalDateTimeUtils.toString(approveList[0].createTime, "yyyy年MM月dd日")
                }
            }
            //自评互评汇总表:把自评的结果加到互评结果上了的。比如互评里面，优秀是15个，如果自评是优秀，那么导出的表优秀就是16个
            countMember.forEachIndexed { index, userDataForm ->
                when (userDataForm.selfRating) {
                    CommentRatingEnum.EXCELLENT.key -> {
                        userDataForm.excellent = userDataForm.excellent + 1
                    }
                    CommentRatingEnum.QUALIFIED.key -> {
                        userDataForm.qualified = userDataForm.qualified + 1
                    }
                    CommentRatingEnum.BASIC_QUALIFIED.key -> {
                        userDataForm.basic = userDataForm.basic + 1
                    }
                    CommentRatingEnum.UNQUALIFIED.key -> {
                        userDataForm.unqualified = userDataForm.unqualified + 1
                    }
                }
                userDataForm.index = index + 1
                dataForm.userList.add(userDataForm)
            }
            commentFileService.addAppraisalTable(dataForm, headers)
        }
    }
}