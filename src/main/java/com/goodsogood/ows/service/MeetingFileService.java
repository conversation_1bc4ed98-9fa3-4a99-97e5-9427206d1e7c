package com.goodsogood.ows.service;

import com.goodsogood.ows.common.FileSourceEnum;
import com.goodsogood.ows.mapper.MeetingFileMapper;
import com.goodsogood.ows.model.db.MeetingFileEntity;
import com.goodsogood.ows.model.vo.MeetingFileForm;
import com.goodsogood.ows.model.vo.MeetingFileVO;
import com.goodsogood.ows.utils.BeanUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @describe 文件接口
 * @date 2019-12-30
 */
@Service
@Log4j2
public class MeetingFileService {

    private final MeetingFileMapper meetingFileMapper;

    public MeetingFileService(MeetingFileMapper meetingFileMapper) {
        this.meetingFileMapper = meetingFileMapper;
    }

    @Transactional
    public void addFile(Long linkedId, List<MeetingFileForm> files, FileSourceEnum fileSourceEnum) {
        if (!CollectionUtils.isEmpty(files)) {
            List<MeetingFileEntity> list = BeanUtil.copyList(files, MeetingFileEntity.class, (BeanUtil.CopyCallBack<MeetingFileEntity>) (source, o) -> {
                if (fileSourceEnum != null) {
                    o.setSource(fileSourceEnum.getSource());
                }
                o.setLinkId(linkedId);
            });

            meetingFileMapper.insertList(list);
        }

    }

    @Transactional
    public void delete(Long linkedId, FileSourceEnum fileSourceEnum) {
        MeetingFileEntity fileEntity = new MeetingFileEntity();
        fileEntity.setLinkId(linkedId);
        fileEntity.setSource(fileSourceEnum.getSource());
        meetingFileMapper.delete(fileEntity);
    }


    public List<MeetingFileVO> selectByLinkedId(Long linkedId, FileSourceEnum fileSourceEnum) {
        MeetingFileEntity fileEntity = new MeetingFileEntity();
        fileEntity.setLinkId(linkedId);
        fileEntity.setSource(fileSourceEnum.getSource());
        List<MeetingFileEntity> files = this.meetingFileMapper.select(fileEntity);

        if (!CollectionUtils.isEmpty(files)) {
            return BeanUtil.copyList(files, MeetingFileVO.class);
        } else {
            return Collections.EMPTY_LIST;
        }
    }

    public List<MeetingFileVO> selectByLinkedIds(List<Long> linkedId,FileSourceEnum fileSourceEnum) {
        Example example = new Example(MeetingFileEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("source",fileSourceEnum.getSource());
        criteria.andIn("linkId",linkedId);
        List<MeetingFileEntity> files = this.meetingFileMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(files)) {
            return BeanUtil.copyList(files, MeetingFileVO.class);
        } else {
            return Collections.EMPTY_LIST;
        }
    }
}
