package com.goodsogood.ows.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.CommendPenalizeStatusEnum;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.common.FileSourceEnum;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.*;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.UserCommendPenalizeMapper;
import com.goodsogood.ows.model.db.UserCommendPenalizeEntity;
import com.goodsogood.ows.model.db.UserCommentEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.user.UserChangeForm;
import com.goodsogood.ows.model.vo.workflow.ApprovalBase;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ExportExcel;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.SaasUtils;
import com.google.common.base.Preconditions;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 党员奖惩服务层
 * @date 2019/12/31
 */
@Service
@Log4j2
public class UserCommendPenalizeService {

    private static final String REDIS_KEY_FILE = "REDIS_KEY_USER_FILE_";

    private final Errors errors;
    private final TogServicesConfig togServicesConfig;
    private final UserCommendPenalizeMapper userCommendPenalizeMapper;
    private final UserCommentService userCommentService;
    private final MeetingFileService meetingFileService;
    private final CommendPenalizeConfiguration commendPenalizeConfiguration;
    private final OpenService openService;
    private final RestTemplate restTemplate;
    private final WorkflowService workflowService;
    private final HeaderService headerService;
    private final OrgTypeConfig orgTypeConfig;
    private final StringRedisTemplate redisTemplate;
    private final ThirdService thirdService;

    @Autowired
    public UserCommendPenalizeService(Errors errors, TogServicesConfig togServicesConfig,
                                      UserCommendPenalizeMapper userCommendPenalizeMapper,
                                      UserCommentService userCommentService, MeetingFileService meetingFileMapper,
                                      CommendPenalizeConfiguration commendPenalizeConfiguration, OpenService openService,
                                      RestTemplate restTemplate, WorkflowService workflowService, HeaderService headerService,
                                      OrgTypeConfig orgTypeConfig, StringRedisTemplate redisTemplate, ThirdService thirdService) {
        this.errors = errors;
        this.togServicesConfig = togServicesConfig;
        this.userCommendPenalizeMapper = userCommendPenalizeMapper;
        this.userCommentService = userCommentService;
        this.meetingFileService = meetingFileMapper;
        this.commendPenalizeConfiguration = commendPenalizeConfiguration;
        this.openService = openService;
        this.restTemplate = restTemplate;
        this.workflowService = workflowService;
        this.headerService = headerService;
        this.orgTypeConfig = orgTypeConfig;
        this.redisTemplate = redisTemplate;
        this.thirdService = thirdService;
    }

    /**
     * 新增党员奖惩信息
     * <AUTHOR>
     * @date 2019/12/31
     * @param addVO
     * @param userId
     * @return void
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertUserCommendPenalize(UserCommendPenalizeAddVO addVO, Long userId, HttpHeaders headers){
        UserCommendPenalizeEntity entity = new UserCommendPenalizeEntity();
        Long regionId = SaasUtils.getRegionId();
        // 判断奖惩级别
        final Integer type = addVO.getType();
        final Integer level = addVO.getLevel();
        final Integer name = addVO.getName();
        if (type.equals(Constant.REWARD)) {
            // 级别
            if (level == null) {
                throw new ApiException("奖励级别不能为空 ", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "奖励级别不能为空"));
            }
            entity.setLevel(level);
            //不走审批流审核，直接给通过
//            entity.setApprovalStatus(CommendPenalizeStatusEnum.UNDER_REVIEW.getKey());
            entity.setApprovalStatus(CommendPenalizeStatusEnum.PASS.getKey());
        } else {
            entity.setApprovalStatus(CommendPenalizeStatusEnum.PASS.getKey());
        }
        // 党奖名称为其他时，内容为必填
        if (Objects.equals(name, Constant.REWARD_NAME_USER_OTHER)) {
            String content = addVO.getContent();
            if (StringUtils.isBlank(content)) {
                throw new ApiException("奖励名称不能为空 ", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "奖励名称不能为空"));
            }
        }
        // 调用用户中心获取组织和人员信息
        UserInfoBase user = this.findUserById(addVO.getUserId(), headers);
        OrganizationBase org = this.userCommentService.findOrgById(user.getOrgId());
        // 封装数据
        entity.setUserId(user.getUserId());
        entity.setUserName(user.getUserName());
        entity.setOrgId(org.getOrganizationId());
        entity.setOrgName(org.getName());
        entity.setOrgLevel(org.getOrgLevel());
        entity.setCertNumber(user.getCertNumberSecret());
        entity.setRegionId(regionId);
        entity.setType(type);
        entity.setCategory(addVO.getCategory());
        entity.setName(name);
        entity.setContent(addVO.getContent());
        entity.setEffectiveTime(addVO.getEffectiveTime());
        entity.setAwardUnit(addVO.getAwardUnit());
        entity.setRelatedFile(addVO.getRelatedFile());
        if (StringUtils.isNotBlank(addVO.getBasisDescription())) {
            entity.setBasisDescription(addVO.getBasisDescription());
        }
        entity.setStatus(Constant.YES);
        entity.setCreateUser(userId);
        entity.setCreateTime(new Date());
        int insert = this.userCommendPenalizeMapper.insert(entity);
        if (Objects.nonNull(addVO.getFiles())) {
            this.meetingFileService.addFile(entity.getMeetingUserCommendPenalizeId(), addVO.getFiles(), FileSourceEnum.MEMBER_COMMEND_PENALIZE);
        }
        if (Objects.nonNull(addVO.getHonorPic())) {
            this.meetingFileService.addFile(entity.getMeetingUserCommendPenalizeId(), addVO.getHonorPic(), FileSourceEnum.MEMBER_COMMEND_PENALIZE_PIC);
        }
        if (type.equals(Constant.REWARD)) {
//            // 新建审核流程
//            final Long workflowTaskId = this.createWorkflow(entity, headers);
//            // 保存审批流ID
//            entity.setWorkflowTaskId(workflowTaskId);
//            entity.setUpdateTime(new Date());
//            this.userCommendPenalizeMapper.updateByPrimaryKey(entity);
            //暂时不使用工作流,回调直接执行回调增加相关奖励逻辑
            this.workflowSkipCallBack(entity, headers);
        } else {
            // 惩罚 调用扣分
            this.operateUserCredit(entity, headers);
            // 组织惩罚 调用扣分
            this.operateOrgCredit(entity, headers);
        }
        return insert;
    }

    /**
     * 更新党员奖惩信息
     * <AUTHOR>
     * @date 2019/12/31
     * @param updateVO
     * @param userId
     * @return int
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateUserCommendPenalize(UserCommendPenalizeUpdateVO updateVO, Long userId, HttpHeaders headers) {
        UserCommendPenalizeEntity entity = this.userCommendPenalizeMapper.selectByPrimaryKey(updateVO.getMeetingUserCommendPenalizeId());
        // 判断奖惩类型
        final Integer type = entity.getType();
        if (entity.getApprovalStatus().equals(CommendPenalizeStatusEnum.PASS.getKey())) {
            throw new ApiException("审核已通过，不支持编辑", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "审核已通过，不支持编辑"));
        }
        // 级别
        final Integer level = updateVO.getLevel();
        if (type.equals(Constant.REWARD)) {
            if (level == null) {
                throw new ApiException("奖励级别不能为空 ", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "奖励级别不能为空"));
            }
            entity.setLevel(level);
        }
        final Integer name = updateVO.getName();
        // 党奖名称为其他时，内容为必填
        if (Objects.equals(name, Constant.REWARD_NAME_USER_OTHER)) {
            String content = updateVO.getContent();
            if (StringUtils.isBlank(content)) {
                throw new ApiException("奖励名称不能为空 ", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "奖励名称不能为空"));
            }
        }
        entity.setEffectiveTime(updateVO.getEffectiveTime());
        entity.setCategory(updateVO.getCategory());
        entity.setName(name);
        entity.setContent(updateVO.getContent());
        entity.setAwardUnit(updateVO.getAwardUnit());
        entity.setRelatedFile(updateVO.getRelatedFile());
        if (StringUtils.isNotBlank(updateVO.getBasisDescription())) {
            entity.setBasisDescription(updateVO.getBasisDescription());
        }
        entity.setApprovalStatus(CommendPenalizeStatusEnum.UNDER_REVIEW.getKey());
        entity.setLastChangeUser(userId);
        entity.setUpdateTime(new Date());
        int update = this.userCommendPenalizeMapper.updateByPrimaryKeySelective(entity);

        this.meetingFileService.delete(updateVO.getMeetingUserCommendPenalizeId(), FileSourceEnum.MEMBER_COMMEND_PENALIZE);
        this.meetingFileService.addFile(updateVO.getMeetingUserCommendPenalizeId(), updateVO.getFiles(), FileSourceEnum.MEMBER_COMMEND_PENALIZE);

        this.meetingFileService.delete(updateVO.getMeetingUserCommendPenalizeId(), FileSourceEnum.MEMBER_COMMEND_PENALIZE_PIC);
        this.meetingFileService.addFile(updateVO.getMeetingUserCommendPenalizeId(), updateVO.getHonorPic(), FileSourceEnum.MEMBER_COMMEND_PENALIZE_PIC);

        if (type.equals(Constant.REWARD)) {
            // 当审核状态为待审核时，需要先撤销审批流程
            if (entity.getApprovalStatus().equals(CommendPenalizeStatusEnum.UNDER_REVIEW.getKey())) {
                this.workflowService.undoApprove(entity.getWorkflowTaskId(), headers);
            }
            // 新建审核流程
            final Long workflowTaskId = this.createWorkflow(entity, headers);
            // 保存审批流ID
            entity.setWorkflowTaskId(workflowTaskId);
            entity.setUpdateTime(new Date());
            this.userCommendPenalizeMapper.updateByPrimaryKey(entity);
        }

        return update;
    }

    /**
     * 删除党员奖惩信息
     * <AUTHOR>
     * @date 2019/12/31
     * @param meetingUserCommendPenalizeId
     * @param userId
     * @return int
     */
    @Transactional(rollbackFor = Exception.class)
    public int delUserCommendPenalize(Long meetingUserCommendPenalizeId, Long userId, HttpHeaders headers) {
        UserCommendPenalizeEntity entity = this.userCommendPenalizeMapper.selectByPrimaryKey(meetingUserCommendPenalizeId);
        if (entity.getApprovalStatus() != null && entity.getApprovalStatus().equals(CommendPenalizeStatusEnum.PASS.getKey())) {
            throw new ApiException("审核已通过，不支持删除", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "审核已通过，不支持删除"));
        }
        entity.setMeetingUserCommendPenalizeId(meetingUserCommendPenalizeId);
        entity.setStatus(Constant.DEL);
        entity.setLastChangeUser(userId);
        entity.setUpdateTime(new Date());
        this.userCommendPenalizeMapper.updateByPrimaryKey(entity);
        if (entity.getType().equals(Constant.REWARD)) {
            // 当审核状态为待审核时，需要先撤销审批流程
            if (entity.getApprovalStatus().equals(CommendPenalizeStatusEnum.UNDER_REVIEW.getKey())) {
                this.workflowService.undoApprove(entity.getWorkflowTaskId(), headers);
            }
        }
        return 1;
    }

    /**
     * 根据ID获取奖惩详细情况
     * <AUTHOR>
     * @date 2019/12/31
     * @param meetingUserCommendPenalizeId
     * @return com.goodsogood.ows.model.vo.UserCommendPenalizeVO
     */
    public UserCommendPenalizeVO getUserCommendPenalizeInfo(Long meetingUserCommendPenalizeId){
        UserCommendPenalizeVO vo = new UserCommendPenalizeVO();
        Example example = new Example(UserCommendPenalizeEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("status", Constant.YES);
        criteria.andEqualTo("meetingUserCommendPenalizeId", meetingUserCommendPenalizeId);
        UserCommendPenalizeEntity entity = this.userCommendPenalizeMapper.selectOneByExample(example);
        if (null != entity) {
            BeanUtils.copyProperties(entity, vo);
        }
        vo.setFiles(meetingFileService.selectByLinkedId(meetingUserCommendPenalizeId, FileSourceEnum.MEMBER_COMMEND_PENALIZE));
        vo.setHonorPic(meetingFileService.selectByLinkedId(meetingUserCommendPenalizeId, FileSourceEnum.MEMBER_COMMEND_PENALIZE_PIC));

        return vo;
    }

    /**
     * 查询党员奖惩列表
     * <AUTHOR>
     * @date 2019/12/31
     * @param queryForm
     * @return com.github.pagehelper.Page<com.goodsogood.ows.model.vo.UserCommendPenalizeVO>
     */
    public Page<UserCommendPenalizeVO> queryUserCommendPenalizeList(UserCommendPenalizeQueryForm queryForm, Long regionId) {
        //设置分页属性
        int p = Preconditions.checkNotNull(queryForm.getPage());
        int r = Preconditions.checkNotNull(queryForm.getPageSize());
        Page<UserCommendPenalizeVO> page = PageHelper.startPage(p, r).doSelectPage(() ->
                this.userCommendPenalizeMapper.queryUserCommendPenalizeList(queryForm.getOrgId(), queryForm.getUserName(),
                        queryForm.getCategory(), queryForm.getLevel(),queryForm.getName(), queryForm.getStartTime(), queryForm.getEndTime(), regionId, queryForm.getType(), queryForm.getApprovalStatus()));
        return page;
    }

    /**
     * 查询党员奖惩列表
     * 2022-02-24 列表里需要插入
     * <AUTHOR>
     * @date 2021-09-06
     * @param userId
     * @return com.github.pagehelper.Page<com.goodsogood.ows.model.vo.UserCommendPenalizeVO>
     */
    public Page<UserCommendPenalizeVO> queryUserCommendPenalizeListByUserId(Long userId, Integer page, Integer pageSize) {
        //设置分页属性
        int p = Preconditions.checkNotNull(page);
        int r = Preconditions.checkNotNull(pageSize);
        Page<UserCommendPenalizeVO> pageData = PageHelper.startPage(p, r).doSelectPage(() ->
                this.userCommendPenalizeMapper.selectUserCommendPenalizeListByUserId(userId));
        // 奖励类别
        List<OptionForm> rewardCategoryCodeList = this.openService.getOptionListByCode(Constant.REWARD_CATEGORY.toString());
        // 奖励级别
        List<OptionForm> rewardLevelCodeList = this.openService.getOptionListByCode(Constant.REWARD_LEVEL.toString());
        // 奖励名称
        List<OptionForm> rewardNameCodeList = this.openService.getOptionListByCode(Constant.REWARD_NAME_USER_NEW.toString());
        pageData.forEach(data -> {
            final OptionForm categoryOption = this.openService.getOptionByList(rewardCategoryCodeList, data.getCategory());
            final OptionForm levelOption = this.openService.getOptionByList(rewardLevelCodeList, data.getLevel());
            final OptionForm nameOption = this.openService.getOptionByList(rewardNameCodeList, data.getName());
            if (Objects.nonNull(categoryOption)) {
                data.setCategoryValue(categoryOption.getOpValue());
            }
            if (Objects.nonNull(levelOption)) {
                data.setLevelValue(levelOption.getOpValue());
            }
            if (Objects.nonNull(nameOption)) {
                data.setNameValue(nameOption.getOpValue());
            }
            data.setFiles(meetingFileService.selectByLinkedId(data.getMeetingUserCommendPenalizeId(), FileSourceEnum.MEMBER_COMMEND_PENALIZE));
            data.setHonorPic(meetingFileService.selectByLinkedId(data.getMeetingUserCommendPenalizeId(), FileSourceEnum.MEMBER_COMMEND_PENALIZE_PIC));
        });
        return pageData;
    }

    /**
     * 获取奖惩字段
     * @param type 1-人员，2-组织
     * @return
     */
    public CommendPenalizeFields getFields(Integer type) {
        final CommendPenalizeFields fields = new CommendPenalizeFields();
        if (type == 1) {
            final List<CommendPenalizeDownloadFieldsEnum> awardEnums =
                    CommendPenalizeDownloadFieldsEnum.Companion.getFieldsByType(1);
            fields.setAwardFields(awardEnums.stream().map(CommendPenalizeDownloadFieldsEnum::getValue).collect(Collectors.toList()));
            final List<CommendPenalizeDownloadFieldsEnum> penalizeEnums =
                    CommendPenalizeDownloadFieldsEnum.Companion.getFieldsByType(2);
            fields.setPenalizeFields(penalizeEnums.stream().map(CommendPenalizeDownloadFieldsEnum::getValue).collect(Collectors.toList()));
        } else if (type == 2) {
            final List<CommendPenalizeDownloadFieldsEnum> awardEnums =
                    CommendPenalizeDownloadFieldsEnum.Companion.getFieldsByType(3);
            fields.setAwardFields(awardEnums.stream().map(CommendPenalizeDownloadFieldsEnum::getValue).collect(Collectors.toList()));
            final List<CommendPenalizeDownloadFieldsEnum> penalizeEnums =
                    CommendPenalizeDownloadFieldsEnum.Companion.getFieldsByType(4);
            fields.setPenalizeFields(penalizeEnums.stream().map(CommendPenalizeDownloadFieldsEnum::getValue).collect(Collectors.toList()));
        }
        return fields;
    }

    /**
     * 导出党员奖惩列表
     * <AUTHOR>
     * @date 2019/12/31
     * @param form
     * @return com.github.pagehelper.Page<com.goodsogood.ows.model.vo.UserCommendPenalizeVO>
     */
    @Async("commendPenalizeAsync")
    public void exportUserCommendPenalizeList(String uuid, UserCommendPenalizeExportQueryForm form, Long regionId, HttpHeaders headers) {
        List<UserCommendPenalizeVO> voList = this.userCommendPenalizeMapper.queryUserCommendPenalizeList(form.getOrgId(), form.getUserName(),
                null, null, form.getName(), form.getStartTime(), form.getEndTime(), regionId, form.getType(), form.getApprovalStatus());
        // 奖励类别
        List<OptionForm> rewardCategoryCodeList = this.openService.getOptionListByCode(Constant.REWARD_CATEGORY.toString());
        // 惩罚ALL
        List<OptionForm> penalizeCodeList = this.openService.getOptionListByCode(Constant.PENALIZE_ALL.toString());
        // 奖励级别
        List<OptionForm> rewardLevelCodeList = this.openService.getOptionListByCode(Constant.REWARD_LEVEL.toString());
        // 奖励名称
        List<OptionForm> rewardNameCodeList = this.openService.getOptionListByCode(Constant.REWARD_NAME_USER_NEW.toString());
        // 准备数据
        final List<CommendPenalizeExportForm> forms = convertObject(voList, rewardCategoryCodeList, rewardLevelCodeList, rewardNameCodeList, penalizeCodeList);
        final List<CommendPenalizeExportForm> rewardList = forms.stream().filter(vo -> Objects.equals(vo.getType(), Constant.REWARD)).collect(Collectors.toList());
        final List<CommendPenalizeExportForm> penalizeList = forms.stream().filter(vo -> Objects.equals(vo.getType(), Constant.PENALIZE)).collect(Collectors.toList());
        List<UploadFileResultForm> fileList = new ArrayList<>();
        // 奖励
        if (form.getType() == null || form.getType() == Constant.REWARD) {
            final UploadFileResultForm rewardForm = createExcel(rewardList, form.getAwardFields(), Constant.REWARD, headers);
            fileList.add(rewardForm);
        }
        // 惩罚
        if (form.getType() == null || form.getType() == Constant.PENALIZE) {
            final UploadFileResultForm penalizeForm = createExcel(penalizeList, form.getPenalizeFields(), Constant.PENALIZE, headers);
            fileList.add(penalizeForm);
        }
        redisTemplate.opsForValue().set(REDIS_KEY_FILE + uuid, JsonUtils.toJson(fileList), 5, TimeUnit.MINUTES);
    }

    public Object getCommendPenalizeFile(String uuid) {
        if(Boolean.TRUE.equals(redisTemplate.hasKey(REDIS_KEY_FILE + uuid))) {
            final String json = redisTemplate.opsForValue().get(REDIS_KEY_FILE + uuid);
            return JsonUtils.toObjList(json, UploadFileResultForm.class);
        } else {
            return -1;
        }
    }

    private List<CommendPenalizeExportForm> convertObject(List<UserCommendPenalizeVO> voList, List<OptionForm> rewardCategoryCodeList,
                                                          List<OptionForm> rewardLevelCodeList, List<OptionForm> rewardNameCodeList, List<OptionForm> penalizeCodeList) {
        List<CommendPenalizeExportForm> resultList = new ArrayList<>(voList.size());
        voList.forEach(vo -> {
            OptionForm categoryOption;
            OptionForm levelOption;
            OptionForm nameOption;
            if (vo.getType().equals(Constant.REWARD)) {
                categoryOption = this.openService.getOptionByList(rewardCategoryCodeList, vo.getCategory());
                levelOption = this.openService.getOptionByList(rewardLevelCodeList, vo.getLevel());
                nameOption = this.openService.getOptionByList(rewardNameCodeList, vo.getName());
            } else {
                categoryOption = this.openService.getOptionByList(penalizeCodeList, vo.getCategory());
                levelOption = this.openService.getOptionByList(penalizeCodeList, vo.getLevel());
                nameOption = this.openService.getOptionByList(penalizeCodeList, vo.getName());
            }
            CommendPenalizeExportForm form = new CommendPenalizeExportForm();
            form.setObjectName(vo.getUserName());
            form.setDate(vo.getEffectiveTime());
            form.setCategory(categoryOption.getOpValue());
            form.setLevel(levelOption.getOpValue());
            form.setName(nameOption.getOpValue());
            form.setRelatedFile(vo.getRelatedFile());
            form.setAwardUnit(vo.getAwardUnit());
            form.setBasisDescription(vo.getBasisDescription());
            form.setType(vo.getType());
            resultList.add(form);
        });
        return resultList;
    }

    /**
     * @title 创建表格
     * <AUTHOR>
     * @param list
     * @param titles
     * @param type
     * @param headers
     * @updateTime 2022/4/8 5:49 PM
     * @return UploadFileResultForm
     */
    private UploadFileResultForm createExcel(List<CommendPenalizeExportForm> list, List<String> titles,
                                             int type, HttpHeaders headers) {
        if (CollectionUtils.isNotEmpty(list)) {
            String name = (type == 1 ? "党员奖励信息" : "党员惩罚信息") + ".xls";
            try {
                return ((ExportExcel<CommendPenalizeExportForm>) (form, row) -> {
                    for (int i = 0; i < titles.size(); i++) {
                        final String fieldName = titles.get(i);
                        final CommendPenalizeDownloadFieldsEnum anEnum =
                                CommendPenalizeDownloadFieldsEnum.Companion.getFieldByTypeAndValue((type == 1 ? 1 : 2), fieldName);
                        if (anEnum != null) {
                            // 反射取值
                            try {
                                final Class<? extends CommendPenalizeExportForm> commentClass = form.getClass();
                                final Field field = commentClass.getDeclaredField(anEnum.getKey());
                                field.setAccessible(true);
                                final String value = Convert.toStr(field.get(form), "");
                                row.createCell(i, CellType.STRING).setCellValue(value);
                            } catch (Exception e) {
                                log.error("反射报错 -> ", e);
                            }
                        }
                    }
                }).createExcelUploadFileCenter(list, titles, name, errors, headers, restTemplate, togServicesConfig);
            } catch (NoSuchFieldException e) {
                log.error("反射异常报错 -> ", e);
            } catch (IOException e) {
                log.error("IO异常 -> ", e);
            }
        }
        return null;
    }

    /**
     * 查询单个人员信息
     * <AUTHOR>
     * @date 2019/12/31
     * @param userId
     */
    public UserInfoBase findUserById(Long userId, HttpHeaders headers) {
        List<UserInfoBase> userInfoBase;
        // 远程调用地址
        String url = String.format("http://%s/find-user-by-key?user_id=%s", this.togServicesConfig.getUserCenter(), userId);
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 调用远程方法
        try {
            userInfoBase = RemoteApiHelper.get(this.restTemplate, url, headers, new TypeReference<Result<List<UserInfoBase>>>(){});
            log.debug("远程调用USER-CENTER查询人员信息: [{}]", userInfoBase);
        } catch (Exception e) {
            log.error("远程调用USER-CENTER查询人员信息, 错误内容:", e);
            throw new ApiException("用户中心服务异常 ", new Result<>(errors, 9907, HttpStatus.BAD_REQUEST.value(), "用户中心服务异常"));
        }

        return ((null != userInfoBase && userInfoBase.size() > 0) ? userInfoBase.get(0) : null);
    }

    /**
     * 同步修改组织信息
     * <AUTHOR>
     * @date 2020/1/8
     * @param organizationBase
     * @return int
     */
    public int updateOrgInfo(OrganizationBase organizationBase) {
        // 封装查询条件
        Example example = new Example(UserCommendPenalizeEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", organizationBase.getOrganizationId());
        final List<UserCommendPenalizeEntity> penalizeEntities = this.userCommendPenalizeMapper.selectByExample(example);
        penalizeEntities.forEach(e -> {
            e.setOrgName(organizationBase.getName());
            e.setOrgLevel(organizationBase.getOrgLevel());
            e.setUpdateTime(new Date());
            e.setLastChangeUser(-111L);
            this.userCommendPenalizeMapper.updateByPrimaryKey(e);
        });
        return penalizeEntities.size();
    }

    /**
     * 同步修改用户信息
     * <AUTHOR>
     * @date 2020/1/8
     * @param userChangeForm
     * @return int
     */
    public int updateUserInfo(UserChangeForm userChangeForm) {
        // 封装查询条件
        Example example = new Example(UserCommentEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userChangeForm.getUserId());
        final List<UserCommendPenalizeEntity> entities = this.userCommendPenalizeMapper.selectByExample(example);
        OrganizationBase org = this.userCommentService.findOrgById(userChangeForm.getNewOrgId());
        entities.forEach(entity -> {
            entity.setOrgId(userChangeForm.getNewOrgId());
            entity.setOrgName(org.getName());
            entity.setOrgLevel(org.getOrgLevel());
            entity.setUserName(userChangeForm.getUserName());
            entity.setCertNumber(userChangeForm.getCertNumberSecret());
            entity.setLastChangeUser(-222L);
            entity.setUpdateTime(new Date());
            this.userCommendPenalizeMapper.updateByPrimaryKey(entity);
        });
        return entities.size();
    }

    private Long createWorkflow(UserCommendPenalizeEntity entity, HttpHeaders headers) {
        // 建立审核流程
        final Long workflowId = Objects.equals(entity.getLevel(), Constant.REWARD_LEVEL_OTHER) ? this.commendPenalizeConfiguration.getWorkflowIdOther() : this.commendPenalizeConfiguration.getWorkflowIdNormal();
        final Integer workflowType = Objects.equals(entity.getLevel(), Constant.REWARD_LEVEL_OTHER) ? this.commendPenalizeConfiguration.getWorkflowTypeOther() : this.commendPenalizeConfiguration.getWorkflowTypeNormal();
        // 奖励类型
        final List<OptionForm> listByCategory = this.openService.getOptionListByCode(Constant.REWARD_CATEGORY.toString());
        final OptionForm optionByCategory = this.openService.getOptionByList(listByCategory, entity.getCategory());
        // 奖励级别
        final List<OptionForm> listByLevel = this.openService.getOptionListByCode(Constant.REWARD_LEVEL.toString());
        final OptionForm optionByLevel = this.openService.getOptionByList(listByLevel, entity.getLevel());
        // 奖励名称
        final String name = this.getRewardName(entity);
        final String title = entity.getOrgName() + ": " + entity.getUserName() + "添加[" + name + "]奖励";
        // 审核body
        final CommendPenalizeApprovalForm approvalForm = new CommendPenalizeApprovalForm(CommendPenalizeApprovalForm.USER, entity.getOrgName() + "：" + entity.getUserName(), optionByCategory.getOpValue(), optionByLevel.getOpValue(), name);
        final ApprovalBase approvalBase = this.workflowService.buildApprovalBase(workflowId, entity.getOrgId(), title, entity.getMeetingUserCommendPenalizeId(), workflowType, JsonUtils.toJson(approvalForm), this.headerService.bulidHeader(headers));
        final long workflowTaskId = this.workflowService.addApproval(approvalBase, headers);
        if (workflowTaskId == -1) {
            throw new ApiException("新建审批流程失败 ", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "新建审批流程失败"));
        }
        return workflowTaskId;
    }

    /**
     * 工作流回调
     * @param meetingUserCommendPenalizeId
     * @param workflowTaskId
     * @param userId
     * @param status
     * @param reason
     * @param reUid
     * @param headers
     */
    public void workflowCallBack(long meetingUserCommendPenalizeId, long workflowTaskId, long userId,
                                 int status, String reason, Long reUid, HttpHeaders headers) {
        log.debug("党员奖惩审批回调入参：meetingId={}, status={}, userId={}, workflowTaskId={}, reUid={}, reason={} ",
                meetingUserCommendPenalizeId, status, userId, workflowTaskId, reUid, reason);
        UserCommendPenalizeEntity entity = this.userCommendPenalizeMapper.selectByPrimaryKey(meetingUserCommendPenalizeId);
        if (!entity.getWorkflowTaskId().equals(workflowTaskId)) {
            log.error("审批流程回调taskId[{}], 与数据taskId[{}]， 不一致", workflowTaskId, entity.getWorkflowTaskId());
            return;
        }
        entity.setLastChangeUser(userId);
        entity.setUpdateTime(new Date());
        if (status == 1) {
            // 审核通过
            entity.setApprovalStatus(CommendPenalizeStatusEnum.PASS.getKey());
            if (entity.getType().equals(1)) {
                final List<MeetingFileVO> fileVOS = this.meetingFileService.selectByLinkedId(entity.getMeetingUserCommendPenalizeId(), FileSourceEnum.MEMBER_COMMEND_PENALIZE_PIC);
                // 调用个人成长轨迹
                this.openService.addUserHighlight(entity, fileVOS, headers);
                // 调用积分
                this.operateUserCredit(entity, headers);
                // 调用我的荣耀
                this.addGlory(entity, headers);
                // 组织积分
                this.operateOrgCredit(entity, headers);
            }
        } else {
            // 审核不通过
            entity.setApprovalStatus(CommendPenalizeStatusEnum.NO_PASS.getKey());
        }
        this.userCommendPenalizeMapper.updateByPrimaryKey(entity);
    }

    private void workflowSkipCallBack(UserCommendPenalizeEntity entity, HttpHeaders headers) {
        final List<MeetingFileVO> fileVOS = this.meetingFileService.selectByLinkedId(entity.getMeetingUserCommendPenalizeId(), FileSourceEnum.MEMBER_COMMEND_PENALIZE_PIC);
        // 调用个人成长轨迹
        this.openService.addUserHighlight(entity, fileVOS, headers);
        // 调用积分
        this.operateUserCredit(entity, headers);
        // 调用我的荣耀
        this.addGlory(entity, headers);
        // 组织积分
        this.operateOrgCredit(entity, headers);
    }

    /**
     * 操作个人积分
     * @param entity
     * @param headers
     */
    private void operateUserCredit(UserCommendPenalizeEntity entity, HttpHeaders headers) {
        List<IndexUserScoreForm> dataList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
        Integer dataMonth = Integer.valueOf(dateFormat.format(new Date()));
        log.debug("操作个人积分 entity -> {}", entity);
            // 备注
            String explainTxt;
            if (entity.getType().equals(Constant.REWARD)) {
                final List<CommendPenalizeConfiguration.Score> score = this.commendPenalizeConfiguration.getUserScoreList();
                final Optional<CommendPenalizeConfiguration.Score> scoreOptional =
                        score.stream().filter(s -> s.getKey().equals(entity.getLevel())).findFirst();
                if (scoreOptional.isPresent()) {
                    final Integer score1 = scoreOptional.get().getScore();
                    // 奖励级别
                    final List<OptionForm> optionListByLevel = this.openService.getOptionListByCode(Constant.REWARD_LEVEL.toString());
                    final OptionForm optionByReward = this.openService.getOptionByList(optionListByLevel, entity.getLevel());
                    // 奖励名称
                    explainTxt = optionByReward.getOpValue() + this.getRewardName(entity);
//                    this.openService.operateUserCredit(entity.getUserId(), entity.getUserName(), 0, ScoreTypeNum.BEST_EXCELLENT.getType(), score1, explainTxt, headers);
                    IndexUserScoreForm userScoreForm = new IndexUserScoreForm(DorisScoreConstant.MEETING_COMMEND_GOOD_USER,dataMonth,entity.getUserId(),score1.longValue());
                    dataList.add(userScoreForm);
                }
            } else {
                final List<CommendPenalizeConfiguration.Score> score = this.commendPenalizeConfiguration.getUserScoreList();
                final Optional<CommendPenalizeConfiguration.Score> scoreOptional =
                        score.stream().filter(s -> s.getKey().equals(entity.getName())).findFirst();
                if (scoreOptional.isPresent()) {
                    final Integer score1 = scoreOptional.get().getScore();
                    // 惩罚类别
                    final List<OptionForm> optionListByCategory = this.openService.getOptionListByCode(Constant.PENALIZE_CATEGORY.toString());
                    final OptionForm optionByCategory = this.openService.getOptionByList(optionListByCategory, entity.getCategory());
                    log.debug("惩罚类别 -> [{}]", optionByCategory);
                    // 惩罚名称
                    final List<OptionForm> optionListByName = this.openService.getOptionListByCode(entity.getCategory().toString());
                    final OptionForm optionByName = this.openService.getOptionByList(optionListByName, entity.getName());
                    log.debug("惩罚名称 -> [{}]", optionByName);
                    explainTxt = optionByCategory.getOpValue() + optionByName.getOpValue();
//                    this.openService.operateUserCredit(entity.getUserId(), entity.getUserName(), 1, ScoreTypeNum.PARTY_SPIRIT.getType(), score1, explainTxt, headers);
                    Long dorisScore = Long.valueOf(score1>0? score1*(-1): score1);
                    IndexUserScoreForm userScoreForm = new IndexUserScoreForm(DorisScoreConstant.MEETING_COMMEND_BAD_USER,dataMonth,entity.getUserId(),dorisScore);
                    dataList.add(userScoreForm);
                }
            }
            if(CollectionUtils.isNotEmpty(dataList)){
                thirdService.addDorisUserScore(null,dataList);

            }

    }

    /**
     * 党员收到奖惩后对应组织积分调整
     * @param entity
     * @param headers
     */
    private void operateOrgCredit(UserCommendPenalizeEntity entity, HttpHeaders headers) {
        List<IndexOrgScoreForm> dataList = new ArrayList();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
        Integer dataMonth = Integer.valueOf(dateFormat.format(new Date()));
       StringBuilder sb = new StringBuilder(entity.getOrgName()).append("-").append(entity.getUserName());
        // 查询操作组织组织
        List<OrgNameResultForm> partyOrgList = this.openService.getPartyOrgListByOrgId(entity.getOrgId(), headers);
        partyOrgList = partyOrgList.stream().filter(org -> orgTypeConfig.getBasicChild().contains(org.getOrgTypeChild())).collect(Collectors.toList());
        //添加人员所属组织
        OrgNameResultForm orgForm = new OrgNameResultForm();
        orgForm.setOrgId(entity.getOrgId());
        partyOrgList.add(orgForm);
        // 调用逻辑
        if (entity.getType().equals(Constant.REWARD)) {
            final List<CommendPenalizeConfiguration.Score> score = this.commendPenalizeConfiguration.getChangeOrgScore();
            final Optional<CommendPenalizeConfiguration.Score> scoreOptional =
                    score.stream().filter(s -> s.getKey().equals(entity.getLevel())).findFirst();
            if (scoreOptional.isPresent()) {
                final Integer score1 = scoreOptional.get().getScore();
                // 奖励级别
                final List<OptionForm> optionListByLevel = this.openService.getOptionListByCode(Constant.REWARD_LEVEL.toString());
                final OptionForm optionByReward = this.openService.getOptionByList(optionListByLevel, entity.getLevel());
                // 奖励名称
                final String name = this.getRewardName(entity);
                sb.append("获得").append(optionByReward.getOpValue()).append(name);
//                partyOrgList.forEach(org -> this.openService.operateOrgCredit(org.getOrgId(), 0, ScoreTypeNum.PIONEER_BEST.getType(), score1, sb.toString(), headers));
                partyOrgList.forEach(org ->{
                    dataList.add(new IndexOrgScoreForm(DorisScoreConstant.MEETING_COMMEND_GOOD_USER_ORG,dataMonth,org.getOrgId(),1,score1.longValue()));
                });
            }
        } else {
            // 惩罚类别
//            final List<OptionForm> optionListByCategory = this.openService.getOptionListByCode(Constant.PENALIZE_CATEGORY.toString());
//            final OptionForm optionByCategory = this.openService.getOptionByList(optionListByCategory, entity.getCategory());
//            log.debug("惩罚类别 -> [{}]", optionByCategory);
//            // 惩罚名称
//            final List<OptionForm> optionListByName = this.openService.getOptionListByCode(entity.getCategory().toString());
//            final OptionForm optionByName = this.openService.getOptionByList(optionListByName, entity.getName());
//            log.debug("惩罚名称 -> [{}]", optionByName);
//            sb.append("受到").append(optionByCategory.getOpValue()).append(optionByName.getOpValue());
////            partyOrgList.forEach(org -> this.openService.operateOrgCredit(org.getOrgId(), 1, ScoreTypeNum.PARTY_EDUCATION.getType(), 5, sb.toString(), headers));
//            partyOrgList.forEach(org ->{
//                dataList.add(new IndexOrgScoreForm(DorisScoreConstant.MEETING_COMMEND_BAD_ORG,dataMonth,org.getOrgId(),1, (long) -5));
//            });
       }
        if(CollectionUtils.isNotEmpty(dataList)){
            thirdService.addDorisOrgScore(null,dataList);
        }

    }

    public String getRewardName(UserCommendPenalizeEntity entity) {
        String name = "";
        if (Constant.REWARD_NAME_USER_OTHER.equals(entity.getName())) {
            name = StringUtils.isNotBlank(entity.getContent()) ? entity.getContent() : "";
        } else {
            final List<OptionForm> nameOption = this.openService.getOptionListByCode(Constant.REWARD_NAME_USER_NEW.toString());
            final OptionForm optionByName = this.openService.getOptionByList(nameOption, entity.getName());
            if (Objects.nonNull(optionByName)) {
                name = optionByName.getOpValue();
            }
        }
        return name;
    }

    /**
     * 新增我的荣耀
     * @param entity
     * @param headers
     */
    private void addGlory(UserCommendPenalizeEntity entity, HttpHeaders headers) {
        if (entity.getType().equals(Constant.REWARD)) {
            final String effectiveTime = entity.getEffectiveTime();
            final String rewardName = getRewardName(entity);
            final List<MeetingFileVO> fileVOS = this.meetingFileService.selectByLinkedId(entity.getMeetingUserCommendPenalizeId(), FileSourceEnum.MEMBER_COMMEND_PENALIZE_PIC);
            String path = null;
            if (!fileVOS.isEmpty()) {
                path = fileVOS.get(0).getPath();
            }
            final Date date = DateUtils.stringToDate(effectiveTime, DateUtils.DATE_FORMAT);
            final LocalDateTime time = LocalDateTimeUtil.of(date);
            openService.addMyGlory(entity.getUserId(), GloryType.MEMBER_REWARD.getKey(), time, rewardName, (entity.getAwardUnit() == null? "" : entity.getAwardUnit()) + "授予\"" + rewardName + "\"荣誉称号", path, headers);
        }
    }

    public CommendStatisticsForm getMyCommendStatistics(Long userId) {
        CommendStatisticsForm form = new CommendStatisticsForm();
        final Example example = new Example(UserCommendPenalizeEntity.class);
        example.orderBy("level").asc().orderBy("createTime").desc();
        final Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("type", Constant.REWARD);
        criteria.andEqualTo("status", Constant.YES);
        criteria.andEqualTo("approvalStatus", CommendPenalizeStatusEnum.PASS.getKey());
        final List<UserCommendPenalizeEntity> list = userCommendPenalizeMapper.selectByExample(example);
        form.setCommendNum(list.size());
        if (!list.isEmpty()) {
            StringBuilder sb = new StringBuilder("在这一天获得了");
            final UserCommendPenalizeEntity entity = list.get(0);
            // 奖励级别
            final List<OptionForm> optionListByLevel = this.openService.getOptionListByCode(Constant.REWARD_LEVEL.toString());
            final OptionForm optionByReward = this.openService.getOptionByList(optionListByLevel, entity.getLevel());
            // 奖励名称
            final String name = this.getRewardName(entity);
            sb.append(optionByReward.getOpValue()).append("的“").append(name).append("”称号呢~");
            form.setContent(sb.toString());
            if (StringUtils.isNotBlank(entity.getEffectiveTime())) {
                try {
                    final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    final Date date = simpleDateFormat.parse(entity.getEffectiveTime());
                    final SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
                    form.setDate(sdf.format(date));
                } catch (ParseException e) {
                    log.error("时间格式化出错误");
                }
            }
        }
        return form;
    }
}
