package com.goodsogood.ows.service;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.MeetingTagMapper;
import com.goodsogood.ows.model.db.MeetingTagEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.TagEditForm;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : MeetingTagService
 * <AUTHOR> tc
 * @Date: 2021/11/9 14:19
 * @Description : 活动标签业务类
 */
@Service
@Log4j2
public class MeetingTagService {
    private final MeetingTagMapper meetingTagMapper;
    private final Errors errors;

    @Autowired
    public MeetingTagService(MeetingTagMapper meetingTagMapper, Errors errors) {
        this.meetingTagMapper = meetingTagMapper;
        this.errors = errors;
    }

    /**
     * 删除活动标签引用
     *
     * @param tagId
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2021/11/9 14:27
     */
    public Boolean tagDel(Long tagId) {
        Boolean re = true;
        try {
            Example example = new Example(MeetingTagEntity.class);
            example.createCriteria().andEqualTo("tagId", tagId);
            meetingTagMapper.deleteByExample(example);
        } catch (Exception e) {
            log.error("删除活动标签引用失败！ tagId={}", tagId, e);
            re = false;
        }
        return re;
    }

    /**
     * 修改活动标签引用的标签名
     *
     * @param tagId
     * @param tagName
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2021/11/9 14:27
     */
    public Boolean tagUpd(Long tagId, String tagName) {
        Boolean re = true;
        try {
            Example example = new Example(MeetingTagEntity.class);
            example.createCriteria().andEqualTo("tagId", tagId);
            MeetingTagEntity mt = MeetingTagEntity.builder().tagName(tagName).build();
            meetingTagMapper.updateByExampleSelective(mt, example);
        } catch (Exception e) {
            log.error("修改活动标签引用的标签名失败！ tagId={}", tagId, e);
            re = false;
        }
        return re;
    }

    /**
     * 批量编辑活动标签
     *
     * @param tagEditForm
     * @param userId      头消息里的操作人编号
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2021/11/9 14:27
     */
    public void tagEdit(TagEditForm tagEditForm, Long userId) {
        //标签编号集合
        List<Long> tagIdList = tagEditForm.getMeetingTag().stream().map(MeetingTagEntity::getTagId).collect(Collectors.toList());
        Map<Long, String> tagMap = new HashMap<>();
        tagEditForm.getMeetingTag().stream().forEach(tag -> {
            tagMap.put(tag.getTagId(), tag.getTagName());
        });
        try {
            Example example = new Example(MeetingTagEntity.class);
            //判断是批量新增还是删除
            if (tagEditForm.getEditType() == 1) {
                //批量新增
                //查询已存在的标签
                List<MeetingTagEntity> mteList = meetingTagMapper.findByMeetingIdsAndTagIds(tagEditForm.getMeetingIds(), tagIdList);
                List<String> existMidList = mteList.stream().map(m -> {
                    return m.getMeetingId() + "," + m.getTagId();
                }).collect(Collectors.toList());
                //筛选出需要添加的活动编号
                List<String> formList = new ArrayList<>();
                tagEditForm.getMeetingIds().stream().forEach(mid -> {
                    tagIdList.stream().forEach(tid -> {
                        formList.add(mid + "," + tid);
                    });
                });
                List<String> addMidList = formList.stream().filter(mi -> existMidList.stream().noneMatch(em -> em.equals(mi))).collect(Collectors.toList());
                //组装活动标签实体
                List<MeetingTagEntity> insertData = new ArrayList<>(addMidList.size());
                addMidList.stream().forEach(minfo -> {
                    Long mid = Long.valueOf(minfo.split(",")[0]);
                    Long tid = Long.valueOf(minfo.split(",")[1]);
                    MeetingTagEntity mte = MeetingTagEntity.builder().meetingId(mid)
                            .tagId(tid).tagName(tagMap.get(tid))
                            .lastChangeUser(userId).lastUpdateTime(new Date()).build();
                    insertData.add(mte);
                });
                if (insertData != null && insertData.size() > 0) {
                    meetingTagMapper.insertList(insertData);
                }
            } else if (tagEditForm.getEditType() == 2) {
                //批量删除
                example.createCriteria().andIn("tagId", tagIdList).andIn("meetingId", tagEditForm.getMeetingIds());
                meetingTagMapper.deleteByExample(example);
            }
        } catch (Exception e) {
            log.error("批量编辑活动标签失败！ tagEditForm={}", tagEditForm, e);
            throw new ApiException("批量编辑活动标签失败!", new Result<>(errors, 2009, HttpStatus.OK.value(), "批量编辑活动标签失败!"));
        }
    }

    /**
     * 根据会议编号和对应会议标签查询出对应的实际的会议
     *
     * @param meetingIds 会议ids
     * @param tagIds     标签ids
     * @return 返回 会议ids中对应的标签ids数据
     */
    List<MeetingTagEntity> findByMeetingIdsAndTagIds(List<Long> meetingIds, List<Long> tagIds) {
        return meetingTagMapper.findByMeetingIdsAndTagIds(meetingIds, tagIds);
    }
}
