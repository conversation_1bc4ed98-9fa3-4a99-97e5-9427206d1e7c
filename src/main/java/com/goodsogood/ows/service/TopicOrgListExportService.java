package com.goodsogood.ows.service;

import com.goodsogood.ows.model.db.TopicOrgEntity;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;

/**
 * 统计结果导出接口
 *
 * <AUTHOR>
 * @since v3.0.0 2020年8月6日 17:56:00
 **/
@Log4j2
public class TopicOrgListExportService extends ExportExcelAbstract<TopicOrgEntity> {
    /**
     * excel 表头
     */
    private static final String[] TITLE = new String[]{"组织名称", "完成情况描述", "附件", "状态"};

    /**
     * header
     */
    private static final String HEADER = "任务完成情况";

    /**
     * sheetName
     */
    private static final String SHEET_NAME = "任务完成情况";


    /**
     * 文件名称
     */
    private static final String FILE_NAME = "任务完成情况";


    public TopicOrgListExportService() {
        super(null, TITLE, SHEET_NAME, FILE_NAME);
    }

    @Override
    public void createRow(HSSFSheet sheet, TopicOrgEntity t) {
        if (t != null) {
            // 新建行
            HSSFRow row = sheet.createRow(nextRow());

            int cellNum = 0;
            // 组织名称
            HSSFCell c0 = row.createCell(cellNum++);
            c0.setCellStyle(this.getColStyle());
            c0.setCellValue(t.getOrgName());
            // 完成情况描述
            HSSFCell c1 = row.createCell(cellNum++);
            c1.setCellStyle(this.getColStyle());
            c1.setCellValue(t.getAnsCnts());

            // 附件
            HSSFCell c2 = row.createCell(cellNum++);
            c2.setCellStyle(this.getColStyle());
            c2.setCellValue(t.getFileNames());

            // 状态
            HSSFCell c3 = row.createCell(cellNum++);
            c3.setCellStyle(this.getColStyle());
            c3.setCellValue(t.getStatus().equals(2) ? "已完成" : "未完成");
        }
    }

    @Override
    public void otherSet() {
        // 设计列宽
        setColumn(this.getSheet(), new double[]{60, 60, 24, 24});
    }
}
