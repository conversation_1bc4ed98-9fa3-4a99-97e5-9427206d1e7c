package com.goodsogood.ows.service

import cn.hutool.core.util.EscapeUtil
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.configuration.MongoCollectionNameConfig
import com.goodsogood.ows.configuration.RemoteSpiderConfiguration
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.HeaderHelper.SysHeader
import com.goodsogood.ows.helper.LocalDateTimeUtils
import com.goodsogood.ows.model.mongo.ConfigRepositoryCustom
import com.goodsogood.ows.model.mongo.TopPriorityEntity
import com.goodsogood.ows.model.mongo.TopPriorityMeeting
import com.goodsogood.ows.model.vo.*
import com.goodsogood.ows.repository.TopPriorityRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation.*
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * <AUTHOR>
 * @date 2023/9/22
 * @description class 第一议题管理Service
 */
@Service
class TopPriorityService(
    val errors: Errors,
    val mongoTemplate: MongoTemplate,
    val userCenterService: UserCenterService,
    val topPriorityRepository: TopPriorityRepository,
    val mongoCollectionNameConfig: MongoCollectionNameConfig,
    val meetingTagService: MeetingTagService,
    val applicationConfigHelper: SimpleApplicationConfigHelper,
    val remoteSpiderConfiguration: RemoteSpiderConfiguration,
    val redisTemplate: StringRedisTemplate,
    val openService: OpenService,
) {
    private val log = LoggerFactory.getLogger(TopPriorityService::class.java)

    private val collectionName = ConfigRepositoryCustom.topPriority + "-" + mongoCollectionNameConfig.region

    @Value("\${saas.label}")
    var label: String? = "v4.0.0"

    @Value("\${top-priority.special-reading}")
    var specialReadingTag: Long? = 46 // 灰度是69

    // private val collectionName = ConfigRepositoryCustom.topPriority
    fun list(): List<TopPriorityEntity> {
        return mongoTemplate.findAll(
            TopPriorityEntity::class.java,
            collectionName
        )
    }

    /**
     * 通过topPriorityForm 获取第一议题列表
     */
    @JvmOverloads
    fun listByTopPriorityForm(
        topPriorityForm: TopPriorityForm,
        sysHeader: SysHeader,
        page: Int? = 1,
        pageSize: Int? = 20,
    ): Page<TopPriorityEntity> {
        val oid = (sysHeader.uoid ?: sysHeader.oid) ?: -1
        val query = Query()
        val criteriaList = mutableListOf<Criteria>()
        // 获取顶级组织获取
        val orgData = applicationConfigHelper.getOrgByRegionId(label, sysHeader.regionId)
        if (oid != orgData.orgId) { //   3L
            // 如果不是顶级组织，查询当前单位和顶级组织发布的
            criteriaList.add(
                Criteria().andOperator(
                    Criteria().orOperator(
                        Criteria.where("orgId").`is`(orgData.orgId),
                        Criteria.where("unitOrgId").`is`(topPriorityForm.unitOrgId),
                    )
                )
            )
        }
        // 这里只查询市局发布的
//        criteriaList.add(Criteria.where("orgId").`is`(3))
        // 通过关键字查询
        topPriorityForm.keyword?.let {
            if (it != "") {
                criteriaList.add(
//                    Criteria().andOperator(
//                        Criteria().orOperator(
                    Criteria.where("title").regex(it),
//                            Criteria.where("summary").regex(it),
//                            Criteria.where("content").regex(it),
//                            Criteria.where("outerLink").regex(it),
//                        )
//                    )
                )
            }
        }
        // 通过发布类型查询
        topPriorityForm.type?.let {
            if (it != 0) {
                when (it) {
                    1 -> {
                        // 如果是1 查询市局发布的
                        criteriaList.add(Criteria.where("orgId").`is`(orgData.orgId))//  3
                    }

                    2 -> {
                        // 如果是2 查询本单位发布的
                        criteriaList.add(Criteria.where("unitOrgId").`is`(topPriorityForm.unitOrgId))
                    }
                }
            }
        }
        // 通过本组织是否已经关联查询
        topPriorityForm.associated?.let {
            if (it == 1) {
                // 添加条件 判断 meetings.associatedOrg 不包含oid的情况
//                query.addCriteria(Criteria.where("meetings.associatedOrg").`in`(oid))
                criteriaList.add(
                    Criteria().andOperator(
                        Criteria().orOperator(
                            Criteria.where("meetings").exists(false),
                            Criteria.where("meetings.associatedOrg").nin(oid)
                        )
                    )
                )
            }
        }
        // 通过媒体发布时间查询
        topPriorityForm.sourceTime?.let {
            val start = if (it[0] == null || it[0] == "") {
                LocalDate.of(1990, 1, 1)
            } else {
                LocalDateTimeUtils.toLocalDate(it[0])
            }
            val end = if (it[1] == null || it[1] == "") {
                LocalDate.of(9999, 12, 31)
            } else {
                LocalDateTimeUtils.toLocalDate(it[1])
            }
            criteriaList.add(Criteria.where("sourceTime").gte(start).lte(end))
        }

        if (criteriaList.size > 0) {
            query.addCriteria(Criteria().andOperator(*criteriaList.toTypedArray()))
        }

        val count = mongoTemplate.count(
            query,
            TopPriorityEntity::class.java,
            collectionName
        )
        val pageable = PageRequest.of(page!! - 1, pageSize!!, Sort.by(Sort.Direction.DESC, "createTime"))

        // 返回分页对象
        return PageImpl(
            mongoTemplate.find(
                query.with(pageable), TopPriorityEntity::class.java,
                collectionName
            ), pageable, count
        )
    }

    /**
     * 添加或者修改
     */
    fun addOrUpdate(
        topPriorityEntity: TopPriorityEntity,
        sysHeader: SysHeader,
        headers: HttpHeaders,
    ): TopPriorityEntity {
        // 如果createTime is null，为其设置为当前时间
        topPriorityEntity.createTime = topPriorityEntity.createTime ?: LocalDateTime.now()
        // 设置 updateTime
        topPriorityEntity.updateTime = LocalDateTime.now()
        // 通过sysHeader补全其他用户组织信息
        with(topPriorityEntity) {
            this.userId = this.userId ?: sysHeader.userId
            this.orgId = this.orgId ?: sysHeader.uoid ?: sysHeader.oid
            this.userName = this.userName ?: EscapeUtil.unescape(sysHeader.userName ?: "")
            this.orgName = this.orgName ?: EscapeUtil.unescape(sysHeader.orgName ?: "")
        }
        // 补全数据
        if (topPriorityEntity.topPriorityId == null && topPriorityEntity.unitShortName == null) {
            val org = openService.findOrgById(topPriorityEntity.orgId, headers)
            org?.ownerId?.let {
                topPriorityEntity.unitOrgId = topPriorityEntity.unitOrgId ?: org.ownerId
                topPriorityEntity.unitOrgName = topPriorityEntity.unitOrgName ?: org.ownerName
                topPriorityEntity.unitShortName = topPriorityEntity.unitShortName ?: org.ownerShortName
            }
        }
        if (topPriorityEntity.topPriorityId != null) { // 更新
            val temp = topPriorityRepository.findById(topPriorityEntity.topPriorityId!!).orElseThrow {
                ApiException(
                    "第一议题id不能为空", Result<Any>(
                        errors,
                        9404,
                        HttpStatus.OK.value(),
                        "对应的第一议题"
                    )
                )
            }
            // 补全meetings
            topPriorityEntity.meetings = temp.meetings
            // 补全组织
            with(topPriorityEntity) {
                this.orgId = temp.orgId
                this.userName = EscapeUtil.unescape(sysHeader.userName ?: "")
                this.orgName = temp.orgName
                this.unitOrgId = temp.unitOrgId
                this.unitOrgName = temp.unitOrgName
                this.unitShortName = temp.unitShortName
            }
        }
        return topPriorityRepository.save(topPriorityEntity)
    }

    /**
     * 删除，如果当前第一议题被其他会议（组织）关联，不允许删除
     */
    fun delete(id: String): String {
        val topPriorityEntity = topPriorityRepository.findById(id).get()
        if (topPriorityEntity.meetings != null && topPriorityEntity.meetings!!.isNotEmpty()) {
//            throw ApiException(
//                "当前第一议题被其他会议（组织）关联，不允许删除",
//                Result<Any>(
//                    errors,
//                    3201,
//                    HttpStatus.OK.value(),
//                    topPriorityEntity.associatedOrgIds.size.toString(),
//                    topPriorityEntity.meetings.size.toString()
//                )
//            )
            return "[${topPriorityEntity.title}]被其他${topPriorityEntity.meetings!!.size}个会议引用，不允许删除；"
        }
        topPriorityRepository.deleteById(id)
        return "success"
    }

    /**
     * 为 TopPriority 添加关联组织和关联会议
     * @param topPriorityMeeting 关联组织和关联会议
     */
    fun addAssociatedAndMeetingsById(topPriorityMeeting: TopPriorityMeeting, id: String): Long {
        return mongoTemplate.updateFirst(
            Query.query(Criteria.where("topPriorityId").`is`(id)),
            Update().addToSet("meetings", topPriorityMeeting)
                .set("associated", 1),
            TopPriorityEntity::class.java,
            collectionName
        ).modifiedCount
    }

    /**
     * 从 TopPriority 中移除关联组织和关联会议
     * @param topPriorityMeeting 关联组织和关联会议
     */
    fun removeAssociatedAndMeetings(topPriorityMeeting: TopPriorityMeeting): Long {
        val query = Query.query(
            Criteria.where("meetings.meetingId").`is`(topPriorityMeeting.meetingId)
                .and("meetings.associatedOrg").`is`(topPriorityMeeting.associatedOrg)
        )
        val data = mongoTemplate.find(query, TopPriorityEntity::class.java, collectionName)
        data.forEach {
            // 这里极端情况下可能出现脏数据，几率很小，因此不做处理
            it.meetings?.removeIf { meeting ->
                meeting.meetingId == topPriorityMeeting.meetingId && meeting.associatedOrg == topPriorityMeeting.associatedOrg
            }
            mongoTemplate.save(it, collectionName)
        }

        return data.size.toLong()
    }

    fun getTopPriorityById(id: String): TopPriorityEntity {
        return topPriorityRepository.findById(id).orElseThrow {
            ApiException(
                "第一议题不存在",
                Result<Any>(
                    errors,
                    9404,
                    HttpStatus.OK.value(),
                    "对应的第一议题"
                )
            )
        }
    }

    /**
     * 通过关联的会议信息查询第一议题列表
     * @param unitOrgId 当前单位id
     * @param orgName 单位简称(查询用)
     * @param meetingTime 会议时间（开始时间：结束时间）
     * @param title 第一议题标题
     * @param source 发布类型 ,1:市局(org_id = 3）,2:区县(org_id != 3)，不穿查询全部
     * @param sysHeader 请求头
     * @param page 页码
     * @param pageSize 每页条数
     */
    fun listAssociated(
        unitOrgId: Long?,
        orgName: String?,
        meetingTime: Pair<String?, String?>?,
        title: String?,
        source: Int?,
        sysHeader: SysHeader,
        page: Int,
        pageSize: Int,
    ): PageImpl<TopPriorityLearningVO> {
        val oid = sysHeader.uoid ?: sysHeader.oid
        // 如果不是顶级组织，需要获取下级组织的条件
        val orgIds: List<Long> = if (oid == 3L) {
            listOf()
        } else {
            openService.getOrgInfoList(oid, 1).map { it.orgId }
        }

        val criteriaList = mutableListOf<Criteria>()

        // meetings 不为空并且 meetings 长度要大于0
        criteriaList.add(Criteria.where("meetings").exists(true))

        // 会议组织权限控制
        if (orgIds.isNotEmpty()) {
            // 判断 meetings.associatedOrg是否包含orgIds
            criteriaList.add(Criteria.where("meetings.associatedOrg").`in`(orgIds))
        }

        // 日期查询
        meetingTime?.let {
            val start = LocalDateTimeUtils.toLocalDate(it.first ?: "1999-01-01")
            val end = LocalDateTimeUtils.toLocalDate(it.second ?: "9999-12-31")
            criteriaList.add(Criteria.where("meetings.meetingTime").gte(start).lte(end))
        }

        // 通过关键字查询
        title?.let {
            criteriaList.add(Criteria.where("title").regex(it))
        }

        // 学习组织
        orgName?.let {
            if (it != "") {
                criteriaList.add(
                    Criteria().andOperator(
                        Criteria().orOperator(
                            Criteria.where("meetings.associatedUnitOrgName").regex(it),
                            Criteria.where("meetings.associatedUnitShortName").regex(it),
                            Criteria.where("meetings.associatedName").regex(it),
                        )
                    )
                )
            }
        }

        // 通过发布来源查询
        source?.let {
            if (it != 0) {
                when (it) {
                    1 -> {
                        // 获取顶级组织获取
                        val orgData = applicationConfigHelper.getOrgByRegionId(label, sysHeader.regionId)
                        // 如果是1 查询市局发布的
                        criteriaList.add(Criteria.where("orgId").`is`(orgData.orgId))
                    }

                    2 -> {
                        // 如果是2 查询本单位发布的
                        criteriaList.add(Criteria.where("unitOrgId").`is`(unitOrgId))
                    }
                }
            }
        }

        // Combine all criteria with a single $and operator
        val query = Criteria().andOperator(*criteriaList.toTypedArray())
        // 通过 Aggregation 展开 meetings
        val aggregate = newAggregation(
            TopPriorityEntity::class.java,
            // 展开 meetings
            unwind("meetings"),
            // 匹配查询条件
            match(query),
            // project
            project(
                "topPriorityId",
                "title",
                "unitOrgId",
                "unitOrgName",
                "unitShortName",
                "orgName",
                "orgId",
                "sourceTime",
            )
                .and("meetings.associatedOrg").`as`("associatedOrgId")
                .and("meetings.associatedName").`as`("associatedOrgName")
                .and("meetings.associatedUnitShortName").`as`("associatedUnitShortName")
                .and("meetings.meetingId").`as`("meetingId")
                .and("meetings.meetingTitle").`as`("meetingTitle")
                .and("meetings.meetingTime").`as`("learningTime")
                .and("meetings.meetingTypes").`as`("meetingTypeId")
                .and("meetings.meetingTypeNames").`as`("meetingTypeName")
                .and("meetings.meetingTime").`as`("meetingTime"),
            sort(Sort.Direction.DESC, "meetingTime"), // 按照 meetingTime 字段降序排序
            // 分页
            skip((page - 1) * pageSize.toLong()),
            limit(pageSize.toLong())
        )

        log.debug("aggregate：{}", aggregate)

        val count = mongoTemplate.aggregate(
            newAggregation(
                TopPriorityEntity::class.java,
                // 展开 meetings
                unwind("\$meetings"),
                // 匹配查询条件
                match(query),
                //
                group().count().`as`("count"),
                // project
                project(
                    "count"
                )
            ), collectionName,
            Map::class.java
        ).uniqueMappedResult?.get("count") as Int?
        log.debug("总条数->{}", count)
        val results = mongoTemplate.aggregate(
            aggregate,
            collectionName,
            TopPriorityLearningVO::class.java
        )
        val pageable = PageRequest.of(page - 1, pageSize)
        // 返回分页对象
        return PageImpl(
            results.mappedResults, pageable, (count ?: 0).toLong()
        )
    }

    /**
     * 通过第一议题对已学单位、逾期学习单位和未学单位进行汇总查询
     * @param unitOrgId 当前单位id
     * @param title 第一议题标题
     * @param headers 请求头
     * @param sysHeader 请求头
     * @param page 页码
     * @param pageSize 每页条数
     */
    fun listSummaryByPriority(
        unitOrgId: Long?,
        title: String?,
        intervalTime: List<Int?>?,
        headers: HttpHeaders,
        sysHeader: SysHeader,
        page: Int,
        pageSize: Int,
    ): Result<List<TopPriorityLearningVO>> {
        // 先获得所有的单位id
        val corps = openService.getCorps(headers).toMutableList()
//        val corps = mutableListOf<OpenService.OrgBaseInfo>()
        // 创建查询
        val criteriaList = mutableListOf<Criteria>()
        // 初始化空条件
        criteriaList.add(Criteria())
        // 这里只查询市局发布的
        criteriaList.add(Criteria.where("orgId").`is`(3))
        // 先通过当前单位id（对应meetings.associatedUnitOrgId）进行过滤
        unitOrgId?.let {
            if (sysHeader.uoid != 3L && sysHeader.oid != 3L) {
                criteriaList.add(Criteria.where("meetings.associatedUnitOrgId").`is`(it))
            }
        }

        // 通过title模糊查询
        title?.let {
            if (title.isNotBlank()) {
                criteriaList.add(Criteria.where("title").regex(it))
            }
        }
        // Combine all criteria with a single $and operator
        val query = Criteria().andOperator(*criteriaList.toTypedArray())
        // 通过 Aggregation 展开 meetings 进行汇总
        val aggregate = newAggregation(
            TopPriorityEntity::class.java,
            // 展开 meetings
            unwind("meetings"),
            // 匹配查询条件
            match(query),
            sort(Sort.Direction.DESC, "createTime"),
            group(
                "_id",
                "title",
                "sourceTime",
            ).push(
                mapOf(
                    "associatedOrg" to "\$meetings.associatedOrg",
                    "associatedName" to "\$meetings.associatedName",
                    "associatedUnitOrgId" to "\$meetings.associatedUnitOrgId",
                    "meetingId" to "\$meetings.meetingId",
                    "associatedName" to "\$meetings.associatedName",
                    "meetingTime" to "\$meetings.meetingTime",
                    "meetingTitle" to "\$meetings.meetingTitle",
                    "meetingTypes" to "\$meetings.meetingTypes",
                    "meetingTypeNames" to "\$meetings.meetingTypeNames",
                    // 计算 meetingTime 和 sourceTime 时间差
                    "diffDays" to mapOf(
                        "\$divide" to listOf(
                            mapOf(
                                "\$subtract" to listOf("\$meetings.meetingTime", "\$sourceTime")
                            ),
                            86400000 // 将毫秒转换为天
                        )
                    )
                )
            ).`as`("meetings"),
            project()
                .and("_id.id").`as`("_id")
                .and("_id.title").`as`("title")
                .and("_id.sourceTime").`as`("sourceTime")
                .and("meetings").`as`("meetings"),
            match(
                Criteria.where("meetings.diffDays").lt(
                    intervalTime?.get(1) ?: 9999
                ).gte(
                    intervalTime?.get(0) ?: 0
                )
            ),
            sort(Sort.Direction.DESC, "sourceTime"),
            // 分页
            skip((page - 1) * pageSize.toLong()),
            limit(pageSize.toLong())
        )
        log.debug("aggregate：{}", aggregate)
        // 计算count
        val count = mongoTemplate.aggregate(
            newAggregation(
                TopPriorityEntity::class.java,
                // 展开 meetings
                unwind("\$meetings"),
                // 匹配查询条件
                match(query),
                //
                group(
                    "_id",
                    "title",
                    "sourceTime",
                ).push(
                    mapOf(// 计算 meetingTime 和 sourceTime 时间差
                        "diffDays" to mapOf(
                            "\$divide" to listOf(
                                mapOf(
                                    "\$subtract" to listOf("\$meetings.meetingTime", "\$sourceTime")
                                ),
                                86400000 // 将毫秒转换为天
                            )
                        )
                    )
                ).`as`("meetings"),
                project()
                    .and("meetings").`as`("meetings"),
                match(
                    Criteria.where("meetings.diffDays").lt(
                        intervalTime?.get(1) ?: 9999
                    ).gte(
                        intervalTime?.get(0) ?: 0
                    )
                ),
                group().count().`as`("count"),
                // project
                project(
                    "count"
                )
            ), collectionName,
            Map::class.java
        ).uniqueMappedResult?.get("count") as Int?
        // 获取数据
        val pageable = PageRequest.of(page - 1, pageSize)
        log.debug("总条数->{}", count)
        val results = mongoTemplate.aggregate(
            aggregate,
            collectionName,
            TopPriorityEntity::class.java
        )
        // 返回分页对象
        val data = PageImpl(results.mappedResults, pageable, (count ?: 0).toLong())
        return Result(data.content.map {
            // map结果为TopPriorityLearningVO，同时计算已学单位数 已学单位 逾期学习单位数 逾期学习单位 未学单位数 未学单位
            val tpl =
                TopPriorityLearningVO(topPriorityId = it.topPriorityId, title = it.title, sourceTime = it.sourceTime)
            // 获取已学单位(diffDays <= 30)
            tpl.completedUnit = it.meetings?.filter { meeting ->
                meeting.diffDays?.let { diffDays ->
                    diffDays <= 30
                } ?: false
            }?.distinctBy { m -> m.meetingId }?.map { meeting ->
                TopPriorityUnitVO(
                    associatedOrg = meeting.associatedOrg,
                    associatedUnitOrgId = meeting.associatedUnitOrgId,
                    associatedUnitOrgName = meeting.associatedUnitOrgName,
                    associatedUnitShortName = meeting.associatedUnitShortName,
                    associatedName = meeting.associatedName,
                )
            } ?: listOf()
            // 获取已学单位数量
            tpl.completedUnitNum = tpl.completedUnit?.size ?: 0
            // 获取逾期学习单位
            tpl.lateUnit = it.meetings?.filter { meeting ->
                meeting.diffDays?.let { diffDays ->
                    diffDays > 30
                } ?: false
            }?.distinctBy { m -> m.meetingId }?.map { meeting ->
                TopPriorityUnitVO(
                    associatedOrg = meeting.associatedOrg,
                    associatedUnitOrgId = meeting.associatedUnitOrgId,
                    associatedUnitOrgName = meeting.associatedUnitOrgName,
                    associatedUnitShortName = meeting.associatedUnitShortName,
                    associatedName = meeting.associatedName,
                )
            } ?: listOf()
            // 获取逾期学习单位数量
            tpl.lateUnitNum = tpl.lateUnit?.size ?: 0
            // 获取未学单位 (corps中不包含已学单位和逾期学习单位的单位)
            tpl.unlearnedUnit = corps.filter { corp ->
                tpl.completedUnit?.find { unit ->
                    unit.associatedUnitOrgId == corp.orgId
                } == null && tpl.lateUnit?.find { unit ->
                    unit.associatedUnitOrgId == corp.orgId
                } == null
            }.distinctBy { c -> c.orgId }.map { corp ->
                TopPriorityUnitVO(
                    associatedUnitOrgId = corp.orgId,
                    associatedUnitOrgName = corp.name,
                    associatedUnitShortName = corp.shortName,
                )
            }
            // 获取未学习单位数量
            tpl.unlearnedUnitNum = tpl.unlearnedUnit?.size ?: 0
            tpl
        }, errors).also {
            it.pageSize = pageSize
            it.pageNum = data.number.plus(1)
            it.total = data.totalElements
        }
    }

    /**
     * 通过单位查询汇总查询，这里只统计市局，默认条件org_id =3
     * 因为不到50个单位，因此不进行分页
     * @param unitOrgId 当前单位id
     * @param unitOrgName 单位名称(查询用)
     * @param intervalTime 按议题统计-间隔时间（天）
     * @param headers 请求头
     * @param sysHeader 请求头
     * @return List<TopPriorityUnitStatisticsVO> 第一议题列表
     */
    fun listSummaryByUnit(
        unitOrgId: Long?,
        unitOrgName: String?,
        intervalTime: List<Int?>?,
        headers: HttpHeaders,
        sysHeader: SysHeader,
    ): List<TopPriorityUnitStatisticsVO> {
        // 先获得所有的单位id
        val corps = openService.getCorps(headers).toMutableList()
        // 创建查询
        val criteriaList = mutableListOf<Criteria>()
        // 这里只查询市局发布的
        criteriaList.add(Criteria.where("orgId").`is`(3))
        // 先通过当前单位id（对应meetings.associatedUnitOrgId）进行过滤
        unitOrgId?.let {
            if (sysHeader.uoid != 3L && sysHeader.oid != 3L) {
                criteriaList.add(Criteria.where("meetings.associatedUnitOrgId").`is`(it))
            }
        }
        // 通过单位名称筛选 corps
        unitOrgName?.let {
            if (it.isNotBlank()) {
                corps.removeIf { corp ->
                    // 排除名称和简称不包含unitOrgName的单位
                    corp.name?.contains(it)?.not() == true && corp.shortName?.contains(it)?.not() == true
                }
            }
            // 通过corps.orgId 对应 meetings.associatedUnitOrgId 进行过滤
            criteriaList.add(Criteria.where("meetings.associatedUnitOrgId").`in`(corps.map { corp ->
                corp.orgId
            }))
        }
        // 创建aggregate
        val aggregate = newAggregation(
            TopPriorityEntity::class.java,
            // 展开 meetings
            unwind("meetings"),
            // 匹配查询条件
            match(Criteria().andOperator(*criteriaList.toTypedArray())),
            //         $group: {
            //            _id: "$meetings.associatedUnitOrgId", // 以associatedUnitOrgId为分组键
            //            meetings: {
            //                $push: {
            //                    doc_id: "$_id",
            //                    title: "$title",
            //                    source: "$source",
            //                    sourceTime: "$sourceTime",
            //                    associatedOrg: "$meetings.associatedOrg",
            //                    associatedName: "$meetings.associatedName",
            //                    associatedUnitOrgId: "$meetings.associatedUnitOrgId",
            //                    meetingId: "$meetings.meetingId",
            //                    meetingTitle: "$meetings.meetingTitle",
            //                    meetingTime: "$meetings.meetingTime",
            //                    meetingTypes: "$meetings.meetingTypes",
            //                    meetingTypeNames: "$meetings.meetingTypeNames"
            //                }
            //            }
            //        }
            group(
                "meetings.associatedUnitOrgId"
            ).push(
                mapOf(
                    "topPriorityId" to "\$_id",
                    "title" to "\$title",
                    "sourceTime" to "\$sourceTime",
                    "associatedOrg" to "\$meetings.associatedOrg",
                    "associatedName" to "\$meetings.associatedName",
                    "associatedUnitOrgId" to "\$meetings.associatedUnitOrgId",
                    "meetingId" to "\$meetings.meetingId",
                    "meetingTitle" to "\$meetings.meetingTitle",
                    "meetingTime" to "\$meetings.meetingTime",
                    "meetingTypes" to "\$meetings.meetingTypes",
                    "meetingTypeNames" to "\$meetings.meetingTypeNames",
                    // 计算 meetingTime 和 sourceTime 时间差
                    "diffDays" to mapOf(
                        "\$divide" to listOf(
                            mapOf(
                                "\$subtract" to listOf("\$meetings.meetingTime", "\$sourceTime")
                            ),
                            86400000 // 将毫秒转换为天
                        )
                    )
                )
            ).`as`("meetings"),
            //         $project: {
            //            _id: 1,
            //            meetings: 1
            //        }
            project()
                .and("_id").`as`("unitOrgId")
                .and("meetings").`as`("meetings"),
            match(
                Criteria.where("meetings.diffDays").lt(
                    intervalTime?.get(1) ?: 9999
                ).gte(
                    intervalTime?.get(0) ?: 0
                )
            ),
        )
        val results = mongoTemplate.aggregate(
            aggregate,
            collectionName,
            TopPriorityUnitStatisticsVO::class.java
        )
        results.forEach {
            // 处理results 生成汇总数据
            // priority_num	number	N/A	Y	所有已学议题数
            //party_group_num	number	N/A	Y	党组会学习数量
            //leading_group_num	number	N/A	Y	中心组学习数量
            //completed_priority_num	number	N/A	Y	正常已学议题数
            //late_priority_num	number	N/A	Y	逾期学习议题数

            // 补全单位名称
            val corp = corps.find { corp ->
                corp.orgId == it.unitOrgId
            }
            it.unitOrgName = corp?.name ?: ""
            it.unitShortName = corp?.shortName ?: it.unitOrgName
            it.seq = corp?.seq ?: 0
            // 所有已学议题数
            it.priorityNum = it.meetings?.distinctBy { m -> m.topPriorityId }?.size ?: 0
            val meetingIds = mutableSetOf<Long>()
            // meetings通过 meeting_id去重然后计算统计结果
            it.meetings?.distinctBy { m -> m.topPriorityId }?.forEach { meeting ->
                // 正常已学议题数(diffDays <= 30)
                it.completedPriorityNum += (if (meeting.diffDays?.let { diffDays ->
                        diffDays <= 30
                    } == true) 1 else 0)
                // 逾期学习议题数(diffDays > 30)
                it.latePriorityNum += (if (meeting.diffDays?.let { diffDays ->
                        diffDays > 30
                    } == true) 1 else 0)
                // 党组会学习数量 //  这里需要确认 会议类型的名称 是否时 党组会议
                it.partyGroupNum += (if (meeting.meetingTypeNames?.split(",")?.contains("党组会议") == true) 1 else 0)
                // 中心组学习数量 //  这里需要确认 会议类型的名称 是否时 党组理论学习中心组学习会议
                it.leadingGroupNum += (
                        if (meeting.meetingTypeNames?.split(",")
                                ?.contains("党组理论学习中心组学习会议") == true
                        ) 1 else 0
                        )
                meetingIds.add(meeting.meetingId ?: -1)
            }
            // 专题读书班是会议标签，需要t_meeting_tag中关联查询
            it.specialReadingNum = meetingTagService.findByMeetingIdsAndTagIds(
                meetingIds.toList(),
                listOf(specialReadingTag ?: 46)
            ).size
        }
        return results.filter { it.unitOrgName?.isNotBlank() == true }.sortedBy { it.seq }.toList()
    }


    /**
     * 通过爬虫获取第一议题内容，并添加到第一议题
     * 添加的时候需要通过title和发布时间去重
     */
    fun addBySpider(startDay: String?): Boolean {
        val today = LocalDateTimeUtils.toDay(LocalDate.now().plusDays(1))
        remoteSpiderConfiguration.region.keys.forEach { key ->
            val startTime = startDay
                ?: if (redisTemplate.hasKey(remoteSpiderConfiguration.timeCacheKey + key)) {
                    LocalDateTimeUtils.toDay(
                        LocalDateTimeUtils.toLocalDate(
                            redisTemplate.opsForValue().get(remoteSpiderConfiguration.timeCacheKey + key)
                        ).minusDays(7)
                    )
                } else {
                    LocalDateTimeUtils.toDay(LocalDate.now().minusDays(7))
                }
            // 根据配置的regionId获取顶级组织
            val orgData = applicationConfigHelper.getOrgByRegionId(label, key)
            val topOrg =
                openService.findOrgById(orgData.orgId, HttpHeaders().also { it.add(HeaderHelper.OPERATOR_REGION, key.toString()) })
            remoteSpiderConfiguration.region[key]?.forEach { (t, u) ->
                try {
                    // 获取对应的第一议题
                    val topPriorityEntities = openService.getSpiderNews(t, startTime, today, 1, 1000)?.map {
                        val topPriorityEntity = TopPriorityEntity(
                            title = it.title,
                            source = u.source ?: "中烟官网",
                            sourceTime = it.releaseTime,
                            summary = it.summary,
                            content = clearHtml(it.content),
                            exLink = it.source, // 外链
                            orgId = topOrg.organizationId,
                            orgName = topOrg.orgName,
                            unitOrgId = topOrg.ownerId,
                            unitOrgName = topOrg.ownerName,
                            unitShortName = topOrg.ownerShortName,
                            userId = -999,
                            userName = "爬虫",
                            createTime = LocalDateTime.now(),
                            updateTime = LocalDateTime.now(),
                        )
                        topPriorityEntity
                    }?.filter {
                        // 过滤掉已经存在的第一议题
                        topPriorityRepository.findByOrgIdAndTitleAndSourceTime(
                            topOrg.organizationId,
                            it.title ?: "XX",
                            it.sourceTime ?: LocalDate.now()
                        ).isEmpty()
                    }
                    topPriorityEntities?.forEach { topPriorityEntity ->
                        val headers = HttpHeaders().also { h -> h.add(HeaderHelper.OPERATOR_REGION, key.toString()) }
                        this.addOrUpdate(
                            topPriorityEntity,
                            HeaderHelper.buildMyHeader(headers),
                            headers
                        )
                    }
                } catch (e: Exception) {
                    log.error("通过爬虫获取第一议题失败:region:[$key],type:[$t]", e)
                }
            }
            // 回写startTime
            redisTemplate.opsForValue().set(remoteSpiderConfiguration.timeCacheKey + key, today)
        }
        return true
    }

    private fun clearHtml(htmlString: String) =
        htmlString.replace("\\n", "").replace(Regex("<p>|<br>"), "\n").replace(Regex("<[^>]+>|<!--[^>]*-->"), "")
            .replace(Regex("&nbsp;"), " ")
}