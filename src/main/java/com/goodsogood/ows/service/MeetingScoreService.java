package com.goodsogood.ows.service;

import com.goodsogood.ows.configuration.DorisScoreConstant;
import com.goodsogood.ows.configuration.MeetingScore;
import com.goodsogood.ows.configuration.MeetingScoreConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.CemUniqueListMapper;
import com.goodsogood.ows.mapper.MeetingOrgScoreDetailMapper;
import com.goodsogood.ows.mapper.MeetingTaskMapper;
import com.goodsogood.ows.mapper.MeetingUserScoreDetailMapper;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.IndexOrgScoreForm;
import com.goodsogood.ows.model.vo.IndexUserScoreForm;
import com.goodsogood.ows.model.vo.ScoreConsumeForm;
import com.goodsogood.ows.utils.AppConfigHelperUtils;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.ListUtils;
import lombok.Builder;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : MeetingScoreService
 * <AUTHOR> tc
 * @Date: 2021/11/30 15:29
 * @Description : 组织生活积分获取服务类
 */
@Service
@Log4j2
public class MeetingScoreService {

    private final MeetingScoreConfig meetingScoreConfig;
    private final MeetingOrgScoreDetailMapper meetingOrgScoreDetailMapper;
    private final MeetingUserScoreDetailMapper meetingUserScoreDetailMapper;
    private final ThirdService thirdService;
    private final AppConfigHelperUtils appConfigHelperUtils;
    private final MeetingTaskMapper meetingTaskMapper;
    private final CemUniqueListMapper cemUniqueListMapper;

    @Autowired
    public MeetingScoreService(MeetingScoreConfig meetingScoreConfig, MeetingOrgScoreDetailMapper meetingOrgScoreDetailMapper, MeetingUserScoreDetailMapper meetingUserScoreDetailMapper, ThirdService thirdService, AppConfigHelperUtils appConfigHelperUtils, MeetingTaskMapper meetingTaskMapper, CemUniqueListMapper cemUniqueListMapper) {
        this.meetingScoreConfig = meetingScoreConfig;
        this.meetingOrgScoreDetailMapper = meetingOrgScoreDetailMapper;
        this.meetingUserScoreDetailMapper = meetingUserScoreDetailMapper;
        this.thirdService = thirdService;
        this.appConfigHelperUtils = appConfigHelperUtils;
        this.meetingTaskMapper = meetingTaskMapper;
        this.cemUniqueListMapper = cemUniqueListMapper;
    }

    /**
     * 基础分发放
     */
    public void basicsScoreAdd(Long regionId, Integer scoreType, Date queryDate) {
        //根据积分类型选择加分方法
        switch (scoreType) {
            case 4:
                this.branchCommitteeScore(regionId, queryDate);
                break;
            case 5: // 支党小组组长每年基础分增加
                this.partyGroupScore(regionId, queryDate);
                break;
            default:
        }
    }

    /**
     * 组织生活个人积分,增加
     */
    public void userScoreAdd(Long regionId, Long meetingId, Integer typeId, Integer scoreType, List<Long> userIdList, Date queryDate, Integer isRollBack) {
        //读取配置
        MeetingScoreConfig.ScoreConf sc = meetingScoreConfig.getScoreConf().get(regionId);
        MeetingScore msConf = sc.getUserScore().stream().filter(ms -> Objects.equals(ms.getTypeId(), typeId) && Objects.equals(ms.getScoreType(), scoreType)).findFirst().get();
        this.operationUserScoreByCycle(regionId, meetingId, queryDate, userIdList, 0, msConf, isRollBack);
    }

    /**
     * 组织生活个人积分,减少
     */
    public void userScoreReduce(Long regionId, Long meetingId, Integer typeId, Integer scoreType, List<Long> userIdList, Date queryDate, Integer isRollBack) {
        //读取配置
        MeetingScoreConfig.ScoreConf sc = meetingScoreConfig.getScoreConf().get(regionId);
        MeetingScore msConf = sc.getUserScore().stream().filter(ms -> Objects.equals(ms.getTypeId(), typeId) && Objects.equals(ms.getScoreType(), scoreType)).findFirst().get();
        this.operationUserScoreByCycle(regionId, meetingId, queryDate, userIdList, 1, msConf, isRollBack);
    }

    /**
     * 组织生活组织积分,增加
     */
    public void orgScoreAdd(Long regionId, Long meetingId, Integer typeId, Integer scoreType, List<Long> orgIdList, Date queryDate, Integer isRollBack) {
        //读取配置
        MeetingScoreConfig.ScoreConf sc = meetingScoreConfig.getScoreConf().get(regionId);
        MeetingScore msConf = sc.getOrgScore().stream().filter(ms -> Objects.equals(ms.getTypeId(), typeId) && Objects.equals(ms.getScoreType(), scoreType)).findFirst().get();
        this.operationOrgScoreByCycle(regionId, meetingId, queryDate, orgIdList, 0, msConf, isRollBack);
    }

    /**
     * 组织生活组织积分,减少
     */
    public void orgScoreReduce(Long regionId, Long meetingId, Integer typeId, Integer scoreType, List<Long> orgIdList, Date queryDate, Integer isRollBack) {
        //读取配置
        MeetingScoreConfig.ScoreConf sc = meetingScoreConfig.getScoreConf().get(regionId);
        /*
        { meeting_type: 4,type-id: 4,score_type: 1, cycle: 2, score: 2, min: 0, max: 2, log_txt: '党课开展积分',type: 2,ruleId: 44}
         */
        MeetingScore msConf = sc.getOrgScore().stream().filter(ms -> Objects.equals(ms.getTypeId(), typeId) && Objects.equals(ms.getScoreType(), scoreType)).findFirst().get();
        this.operationOrgScoreByCycle(regionId, meetingId, queryDate, orgIdList, 1, msConf, isRollBack);
    }

    /**
     * 组织生活增加积分
     */
//    @Async("meetingScoreAsync")
    public void addMeetingScore(Long regionId, MeetingEntity meeting) {
        try {
            // 查询出活动绑定的活动类型
            List<MeetingTypeEntity> typeList = meeting.getMeetingTypes();
            if (!ListUtils.isEmpty(typeList)) {
                typeList.forEach(
                        type -> {
                            // 查找对应MeetingTask的开始时间
                            MeetingTaskEntity task = meetingTaskMapper.selectByPrimaryKey(type.getMeetingTaskId());
                            Date queryDate = task.getStartTime();
                            Integer typeId = type.getTypeId().intValue();
                            //已签到人员
                            List<Long> signed;
                            //未已签到人员
                            List<Long> unSigned;
                            switch (typeId) {
                                case 1:
                                    /*
                                      党支部党员大会
                                     */
                                    //党员积分
                                    signed = this.findUserIdBySignIn("signed", meeting.getParticipantUsers());
                                    if (!signed.isEmpty()) {
                                        //已签到党员加分
                                        this.userScoreAdd(regionId, meeting.getMeetingId(), typeId, 1, signed, queryDate, 0);
                                    }

                                    //组织积分
                                    this.orgScoreAdd(regionId, meeting.getMeetingId(), typeId, 1, new ArrayList<>(Collections.singletonList(meeting.getOrgId())), queryDate, 0);
                                    break;
                                case 2:
                                    /*
                                      党支部委员会会议
                                     */
                                    //党员积分
                                    unSigned = this.findUserIdBySignIn("unsigned", meeting.getParticipantUsers());
                                    if (!unSigned.isEmpty()) {
                                        //未签到党员扣分
                                        this.userScoreReduce(regionId, meeting.getMeetingId(), typeId, 1, unSigned, queryDate, 0);
                                    }

                                    //组织积分
                                    this.orgScoreAdd(regionId, meeting.getMeetingId(), typeId, 1, new ArrayList<>(Collections.singletonList(meeting.getOrgId())), queryDate, 0);
                                    break;
                                case 3:
                                    /*
                                      党小组会
                                     */
                                    //党员积分
                                    signed = this.findUserIdBySignIn("signed", meeting.getParticipantUsers());
                                    if (!signed.isEmpty()) {
                                        //已签到党员加分
                                        this.userScoreAdd(regionId, meeting.getMeetingId(), typeId, 1, signed, queryDate, 0);
                                    }
                                    //给党小组组长加分
                                    this.groupLeaderScore(regionId, meeting, queryDate, 0);


                                    //组织积分
                                    //查询小组所属的支部是否都已经开展，如果开展了给支部加分
                                    Long branchId = thirdService.findBranchByGroup(regionId, meeting.getOrgId());
                                    if (branchId != null) {
                                        List<Long> groupIds = thirdService.findGroupByBranch(regionId, branchId);
                                        //判断是否所有支部已经完成开展任务
                                        List<Long> unfinisheds = meetingTaskMapper.findUnfinishedByOrgId(groupIds, 3, DateUtils.dateFormat(queryDate, "yyyy-MM-dd"));
                                        if (unfinisheds != null && unfinisheds.size() == 0) {
                                            //给党小组所属支部加分
                                            this.orgScoreAdd(regionId, meeting.getMeetingId(), typeId, 1, new ArrayList<>(Collections.singletonList(branchId)), queryDate, 0);
                                        }
                                    }
                                    break;
                                case 4:
                                    /*
                                      党课
                                     */
                                    //党员积分
                                    signed = this.findUserIdBySignIn("signed", meeting.getParticipantUsers());
                                    if (!signed.isEmpty()) {
                                        //已签到党员加分
                                        this.userScoreAdd(regionId, meeting.getMeetingId(), typeId, 1, signed, queryDate, 0);
                                    }
                                    //讲课人加分
                                    List<Long> lecturers = meeting.getLecturers().stream().map(MeetingUserEntity::getUserId).collect(Collectors.toList());
                                    if (!lecturers.isEmpty()) {
                                        this.userScoreAdd(regionId, meeting.getMeetingId(), typeId, 2, lecturers, queryDate, 0);
                                    }
                                    //组织积分
                                    this.orgScoreAdd(regionId, meeting.getMeetingId(), typeId, 1, new ArrayList<>(Collections.singletonList(meeting.getOrgId())), queryDate, 0);
                                    break;
                                case 5:
                                    /*
                                      主题党日
                                     */
                                    //党员积分
                                    signed = this.findUserIdBySignIn("signed", meeting.getParticipantUsers());
                                    if (!signed.isEmpty()) {
                                        //已签到党员加分
                                        this.userScoreAdd(regionId, meeting.getMeetingId(), typeId, 1, signed, queryDate, 0);
                                    }
                                    //组织增加积分
                                    this.orgScoreAdd(regionId, meeting.getMeetingId(), typeId, 1, new ArrayList<>(Collections.singletonList(meeting.getOrgId())), queryDate, 0);
                                    break;
                                default:
                                    log.error("<组织生活增加积分> 出错！ 无效活动类型typeId={}", type.getTypeId());
                                    break;
                            }
                        });
            }
        } catch (Exception e) {
            log.error("<组织生活增加积分> 出错！ regionId=" + regionId + " meeting=" + meeting, e);
        }
    }


    /**
     * 撤回/退回和取消组织生活时，积分操作
     *
     * @param regionId 区县编号
     * @param meeting  活动信息
     * @param isCancel 是否为取消活动，取消活动需要回退组织积分
     */
//    @Async("meetingScoreAsync")
    public void reduceMeetingScore(Long regionId, MeetingEntity meeting, Boolean isCancel) {
        try {
            // 查询出活动绑定的活动类型
            List<MeetingTypeEntity> typeList = meeting.getMeetingTypes();
            if (!ListUtils.isEmpty(typeList)) {
                typeList.forEach(
                        type -> {
                            // 查找对应MeetingTask的开始时间
                            MeetingTaskEntity task = meetingTaskMapper.selectByPrimaryKey(type.getMeetingTaskId());
                            Date queryDate = task.getStartTime();
                            Integer typeId = type.getTypeId().intValue();
                            //已签到人员
                            List<Long> signed;
                            //未已签到人员
                            List<Long> unSigned;
                            switch (typeId) {
                                case 1:
                                    /*
                                     * 党支部党员大会
                                     */
                                    if (isCancel) {
                                        //组织积分
                                        this.orgScoreReduce(regionId, meeting.getMeetingId(), typeId, 1, new ArrayList<>(Collections.singletonList(meeting.getOrgId())), queryDate, 1);
                                    } else {
                                        //党员积分
                                        signed = this.findUserIdBySignIn("signed", meeting.getParticipantUsers());
                                        if (signed.size() > 0) {
                                            //已签到党员扣分
                                            this.userScoreReduce(regionId, meeting.getMeetingId(), typeId, 1, signed, queryDate, 1);
                                        }
                                    }
                                    break;
                                case 2:
                                    /*
                                     * 党支部委员会会议
                                     */
                                    if (isCancel) {
                                        //组织积分
                                        this.orgScoreReduce(regionId, meeting.getMeetingId(), typeId, 1, new ArrayList<>(Collections.singletonList(meeting.getOrgId())), queryDate, 1);
                                    } else {
                                        //党员积分
                                        unSigned = this.findUserIdBySignIn("unsigned", meeting.getParticipantUsers());
                                        if (unSigned.size() > 0) {
                                            //未签到党员加分
                                            this.userScoreAdd(regionId, meeting.getMeetingId(), typeId, 1, unSigned, queryDate, 1);
                                        }
                                    }
                                    break;
                                case 3:
                                    /*
                                     * 党小组会
                                     */
                                    if (isCancel) {
                                        //查询小组所属的支部是否都已经开展，如果开展了给支部加分
                                        Long branchId = thirdService.findBranchByGroup(regionId, meeting.getOrgId());
                                        if (branchId != null) {
                                            List<Long> groupIds = thirdService.findGroupByBranch(regionId, branchId);
                                            //判断是否所有支部已经完成开展任务
                                            List<Long> unfinisheds = meetingTaskMapper.findUnfinishedByOrgId(groupIds, 3, DateUtils.dateFormat(queryDate, "yyyy-MM-dd"));
                                            if (CollectionUtils.isNotEmpty(unfinisheds)) {
                                                //给党小组所属支部扣分
                                                this.orgScoreReduce(regionId, meeting.getMeetingId(), typeId, 1, new ArrayList<>(Collections.singletonList(branchId)), queryDate, 0);
                                            }
                                        }
                                    } else {
                                        //党员积分
                                        signed = this.findUserIdBySignIn("signed", meeting.getParticipantUsers());
                                        if (signed.size() > 0) {
                                            //已签到党员扣分
                                            this.userScoreReduce(regionId, meeting.getMeetingId(), typeId, 1, signed, queryDate, 1);
                                        }

                                        //给党小组组长扣分
                                        this.groupLeaderScore(regionId, meeting, queryDate, 1);
                                    }
                                    break;
                                case 4:
                                    /*
                                     * 党课
                                     */
                                    if (isCancel) {
                                        //组织积分
                                        this.orgScoreReduce(regionId, meeting.getMeetingId(), typeId, 1, new ArrayList<>(Collections.singletonList(meeting.getOrgId())), queryDate, 1);
                                    } else {
                                        //党员积分
                                        signed = this.findUserIdBySignIn("signed", meeting.getParticipantUsers());
                                        if (signed.size() > 0) {
                                            //已签到党员扣分
                                            this.userScoreReduce(regionId, meeting.getMeetingId(), typeId, 1, signed, queryDate, 1);
                                        }
                                        //讲课人扣分
                                        List<Long> lecturers = meeting.getLecturers().stream().map(MeetingUserEntity::getUserId).collect(Collectors.toList());
                                        if (lecturers.size() > 0) {
                                            this.userScoreReduce(regionId, meeting.getMeetingId(), typeId, 2, lecturers, queryDate, 1);
                                        }
                                    }
                                    break;
                                case 5:
                                    /*
                                     * 主题党日
                                     */
                                    if (isCancel) {
                                        //组织增加积分
                                        this.orgScoreReduce(regionId, meeting.getMeetingId(), typeId, 1, new ArrayList<>(Collections.singletonList(meeting.getOrgId())), queryDate, 1);
                                    } else {
                                        //党员积分
                                        signed = this.findUserIdBySignIn("signed", meeting.getParticipantUsers());
                                        if (signed.size() > 0) {
                                            //已签到党员扣分
                                            this.userScoreReduce(regionId, meeting.getMeetingId(), typeId, 1, signed, queryDate, 1);
                                        }
                                    }
                                    break;
                                default:
                                    log.error("<组织生活扣减积分> 出错！ 无效活动类型typeId={}", type.getTypeId());
                                    break;
                            }
                        });
            }
        } catch (Exception e) {
            log.error("<组织生活扣减积分> 出错！ regionId={} meeting={}", regionId, meeting);
        }
    }

    /**
     * 未开展党小组会的组长扣分
     *
     * @return void
     * <AUTHOR>
     * &#064;date  2021/12/9 19:10
     */
    public void unfinishedGroupLeader(Long regionId, Date queryDate) {
        try {
            //读取配置党小组组长开展积分
            MeetingScoreConfig.ScoreConf sc = meetingScoreConfig.getScoreConf().get(regionId);
            MeetingScore msConf = sc.getUserScore().stream().filter(ms -> {
                return ms.getMeetingType() == 3 && ms.getScoreType() == 3;
            }).findFirst().get();
            //查询未完成党小组会党小组
            List<Long> unfinishedOrgId = meetingTaskMapper.findUnfinishedByTypeId(msConf.getTypeId(), DateUtils.dateFormat(queryDate, "yyyy-MM-dd"));
            //就查询用户中心，获取当前党小组组长
            String orgIds = unfinishedOrgId.stream().map(Object::toString).collect(Collectors.joining(","));
            Map<Long, Long> userIdMap = thirdService.findGroupLeader(regionId, orgIds);
            List<Long> groupLeaderId = null;
            if (userIdMap != null) {
                groupLeaderId = new ArrayList<>(userIdMap.values());
            }
            this.userScoreReduce(regionId, null, msConf.getTypeId(), msConf.getScoreType(), groupLeaderId, queryDate, 0);
        } catch (Exception e) {
            log.error("未开展党小组会的组长扣分 出错！ regionId={} queryDate={}", regionId, queryDate, e);
        }
    }

    /**
     * @param flag 获取模式  signed:获取已签到人员   unsigned:获取未签到人员
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2021/12/9 9:47
     */
    private List<Long> findUserIdBySignIn(String flag, List<MeetingUserEntity> meetingUserList) {
        List<Long> userIdList = new ArrayList<>();
        if (flag.equals("signed")) {
            userIdList = meetingUserList.stream().map(user -> {
                log.debug("测试输出: user.id ={}  user.signStatus={}", user.getUserId(), user.getSignStatus());
                if (user.getSignStatus() == 1 || user.getSignStatus() == 6) {
                    return user.getUserId();
                } else {
                    return null;
                }
            }).collect(Collectors.toList());
        } else if (flag.equals("unsigned")) {
            userIdList = meetingUserList.stream().map(user -> {
                if (user.getSignStatus() == 2) {
                    return user.getUserId();
                } else {
                    return null;
                }
            }).collect(Collectors.toList());
        }
        //除去空元素
        userIdList = userIdList.stream().filter(p -> p != null).collect(Collectors.toList());
        return userIdList;
    }

    /**
     * 单独给党小组会，党组组长操作积分逻辑
     *
     * @param regionId      区县编号
     * @param meeting       活动信息
     * @param queryDate     查询时间
     * @param operationType 操作类型  0 加积分  1 扣积分
     * @return void
     * <AUTHOR>
     * @date 2021/12/9 13:48
     */
    private void groupLeaderScore(Long regionId, MeetingEntity meeting, Date queryDate, Integer operationType) {
        try {
            //读取配置
            MeetingScoreConfig.ScoreConf sc = meetingScoreConfig.getScoreConf().get(regionId);
            MeetingScore msConf = sc.getUserScore().stream().filter(ms -> ms.getMeetingType() == 3 && ms.getScoreType() == 3).findFirst().get();

            Integer cycle = msConf.getCycle();
            Integer max = msConf.getMax();
            Integer min = msConf.getMin();
            Long score = msConf.getScore();
            Integer scoreType = msConf.getScoreType();
            String logTxt = msConf.getLogTxt();
            String remark = msConf.getRemark();
            Integer meetingType = msConf.getMeetingType();
            Long s = operationType == 0 ? score : (-score);
            //根据region获取顶级组织编号
            Long topOrgId = appConfigHelperUtils.findTopOrgIdByRegionId(regionId);

            //为了避免已经更换组长，所以需先要去数据库获取记录获取当时的组长编号
            Map<String, Integer> reCycle = findCycle(cycle, queryDate);
            List<MeetingUserScoreDetailEntity> msdList = meetingUserScoreDetailMapper.findGroupLeaderScoreInfo(meeting.getMeetingId(), reCycle.get("start"), reCycle.get("end"));
            List<Long> groupLeaderId = new ArrayList<>();
            int flg;//处理分支标记
            if (msdList == null || msdList.size() == 0) {
                flg = 0;
                //如果没有记录，就查询用户中心，获取当前党小组组长
                Map<Long, Long> userIdMap = thirdService.findGroupLeader(regionId, meeting.getOrgId().toString());
                if (userIdMap != null) {
                    groupLeaderId = new ArrayList<>(userIdMap.values());
                }
            } else {
                flg = 1;
                groupLeaderId = msdList.stream().map(MeetingUserScoreDetailEntity::getUserId).collect(Collectors.toList());
                //验证能否加分或者扣分
                for (MeetingUserScoreDetailEntity msd : msdList) {
                    if ((msd.getScore() + s) >= min && (msd.getScore() + s) <= max) {
                        groupLeaderId.add(msd.getUserId());
                    }
                }
            }

            //上报到积分中心
            groupLeaderId.forEach(userId -> {
                String token = UUID.randomUUID().toString().replaceAll("-", "");
                Long ret = null;
                if (flg == 0 && operationType == 0) {
                    //只做个0分做记录在meeting数据库，不上报记录到积分中心
                    ret = 1L;
                } else {
                    ScoreConsumeForm scoreConsumeForm = new ScoreConsumeForm(regionId, userId, token, score, 12, operationType, topOrgId, logTxt, remark);
                    ret = thirdService.addScoreByUserId(regionId, scoreConsumeForm);
                }
                //记录结果
                MeetingUserScoreDetailEntity msde = MeetingUserScoreDetailEntity.builder().meetingId(meeting.getMeetingId()).meetingType(meetingType).operationType(operationType).score(operationType == 0 ? score : (0 - score)).scoreType(scoreType)
                        .scoreSign(this.findScoreSign(cycle, queryDate)).scoreToken(token).userId(userId).status(ret != null ? 1 : 2)
                        .createTime(new Date()).remark(remark).build();
                meetingUserScoreDetailMapper.insert(msde);
            });
            // TODO 检查这里需要 加入doris库吗!!!!!!!!
            log.debug("党组组长操作积分 完成！ regionId={} meeting={} queryDate={} operationType={}", regionId, meeting, queryDate, operationType);
        } catch (Exception e) {
            log.error("党组组长操作积分 报错！ regionId={} meeting={} queryDate={} operationType={}", regionId, meeting, queryDate, operationType);
        }
    }

    /**
     * 支部委员会成员每年基础分增加
     */
    private void branchCommitteeScore(Long regionId, Date queryDate) {
        try {
            String dateStr = DateUtils.dateFormat(queryDate, "yyyy-MM-dd");
            //读取配置
            MeetingScoreConfig.ScoreConf sc = meetingScoreConfig.getScoreConf().get(regionId);
            MeetingScore msConf = sc.getBasicsScore().stream().filter(ms -> ms.getScoreType() == 4).findFirst().get();
            //查询支委会成员
            List<Long> userIdList = thirdService.findBranchCommittee(regionId, dateStr);
            //上报到积分中心
            log.debug("<支部委员会成员每年基础分增加> 开始！ regionId={} queryDate={} msConf={}", regionId, queryDate, msConf);
            log.debug("<支部委员会成员每年基础分增加> 支委会成员数量！ regionId={} queryDate={} size={}", regionId, queryDate, userIdList.size());
            //验证并增加积分
            operationUserScoreByCycle(regionId, null, queryDate, userIdList, 0, msConf, 0);
            //处理到doris
//            if(msConf.getType()==2 || msConf.getType()==3){
//                List<IndexUserScoreForm> dataList = new ArrayList<>();
//                if (userIdList != null) {
//                    Long dorisScore = msConf.getScore();
//                    Integer ruleId = msConf.getRuleId();
//                    userIdList.forEach(userId->{
//                        Integer dataMonth = Integer.parseInt(dateStr.substring(0,4)+dateStr.substring(5,7));
//                        IndexUserScoreForm userScoreForm = new IndexUserScoreForm(ruleId,dataMonth,userId,dorisScore);
//                        dataList.add(userScoreForm);
//                        thirdService.addDorisUserScore(regionId,dataList);
//                    });
//                }
//            }

        } catch (Exception e) {
            log.error("<支部委员会成员每年基础分增加> 报错！ regionId={} queryDate={}", regionId, queryDate, e);
        }
    }

    /**
     * 支党小组组长每年基础分增加
     */
    private void partyGroupScore(Long regionId, Date queryDate) {
        try {
            String dateStr = DateUtils.dateFormat(queryDate, "yyyy-MM-dd");
            //读取配置
            MeetingScoreConfig.ScoreConf sc = meetingScoreConfig.getScoreConf().get(regionId);
            /*
            msConf = {
            score_type: 5,
            cycle: 3,
            score: 20,
            min: 0,
            max: 20,
            log_txt: '党小组组长基础分',
            type: 2,
            ruleId: 11
            }
             */
            MeetingScore msConf = sc.getBasicsScore().stream().filter(ms -> ms.getScoreType() == 5).findFirst().get();
            log.debug("<支党小组组长每年基础分增加> 开始！ regionId={} queryDate={} msConf={}", regionId, queryDate, msConf);
            //查询党组组长
            Map<Long, Long> userIdMap = thirdService.findGroupLeader(regionId, "");
            List<Long> userIdList = null;
            if (userIdMap != null) {
                userIdList = new ArrayList<>(userIdMap.values());
            }

            if (userIdList != null) {
                log.debug("<支党小组组长每年基础分增加> 党小组组长数量！ regionId={} queryDate={} size={}", regionId, queryDate, userIdList.size());
            }

            operationUserScoreByCycle(regionId, null, queryDate, userIdList, 0, msConf, 0);

        } catch (Exception e) {
            log.error("<支党小组组长每年基础分增加> 报错！ regionId={} queryDate={}", regionId, queryDate, e);
        }
    }

//    /**
//     * 去重 人员id
//     */
//    private List<Long> distinctDorisUserScore(Integer ruleId,String timeStr,List<Long> source){
//        if (null == source || source.isEmpty()){
//            return null;
//        }
//        Map<String,Long> map = new HashMap<>();
//        List<CemUniqueListEntity> entities;
//        source.forEach(u->{
//            String key = timeStr + "-" + u;
//            map.put(key,u);
//        });
//        Set<String> strings = map.keySet();
//        Example e = new Example(CemUniqueListEntity.class);
//        e.createCriteria()
//                .andEqualTo("ruleId",ruleId)
//                .andIn("uniqueCode",strings);
//        entities = cemUniqueListMapper.selectByExample(e);
//        entities.forEach(x-> map.remove(x.getUniqueCode()));
//        return (List<Long>) map.values();
//    }

    /**
     * 操作用户积分
     * isRollBack 是否为回滚操作  0 否 1 是。
     * 如果是回滚操作则需要判断引起回滚操作的会议的当前积分情况(获得和扣减的情况)，根据本次会议的积分情况判断是否进行真实的加分或扣分
     * 例如考勤已签到加分后，撤回/回退/取消操作时扣分就是回滚操作。
     * 例如考勤未签到扣分后，撤回/回退/取消操作时加分就是回滚操作。
     */
    private void operationUserScoreByCycle(Long regionId, Long meetingId, Date queryDate, List<Long> userIdList, Integer operationType, MeetingScore msConf, Integer isRollBack) {
        try {
            if (msConf == null) {
                //未获取到配置直接抛出异常
                log.error("组织生活，积分奖励配置信息失败!");
                throw new ApiException("组织生活，积分奖励配置信息失败");
            }
            if (isRollBack == 1 && meetingId == null) {
                log.error("isRollBack 为1(回滚操作)时 meetingId 不能为空!");
                throw new ApiException("isRollBack 为1(回滚操作)时 meetingId 不能为空!");
            }
            Integer cycle = msConf.getCycle();
            Integer max = msConf.getMax();
            Integer min = msConf.getMin();
            Long score = msConf.getScore();
            Integer scoreType = msConf.getScoreType();
            String logTxt = msConf.getLogTxt();
            String remark = msConf.getRemark();
            Integer meetingType = msConf.getMeetingType();
            Integer ruleId = msConf.getRuleId();
            //根据region获取顶级组织编号
            Long topOrgId = appConfigHelperUtils.findTopOrgIdByRegionId(regionId);

            //验证积分周期
            Map<String, Integer> reCycle = findCycle(cycle, queryDate);
            //查看是否属于当前周期,如果是以前的数据，则将consumeTime设置为以前的日期
            Boolean isNotNow = isNow(queryDate, cycle);
            //此处不能设置为当前时间，积分中心那边有判断，用以判断是否以前周期的积分！！
//            String consumeTime = isNotNow ? DateUtils.dateFormat(queryDate, "yyyy-MM-dd") + " 00:01:01" : DateUtils.dateFormat(new Date(), "yyyy-MM-dd") + " 00:01:01";
            String consumeTime = isNotNow ? DateUtils.dateFormat(queryDate, "yyyy-MM-dd") + " 00:01:01" : null;
            List<MeetingUserScoreDetailEntity> excludeUser = meetingUserScoreDetailMapper.findUserScoreCycle(userIdList, meetingType, scoreType, reCycle.get("start"), reCycle.get("end"), isRollBack, meetingId);
            log.debug("<" + logTxt + "> 积分周期内积分查询结果！ regionId={} meetingId={} meetingType={} queryDate={} topOrgId={} operationType={} isRollBack={} excludeUser={} ", regionId, meetingId, meetingType, queryDate, topOrgId, operationType, isRollBack, excludeUser);
            //根据配置筛选未满足加分条件的人员
            Long s = operationType == 0 ? score : (-score);
            List<Long> excludeUserId = new ArrayList<>();
            for (Long uid : userIdList) {
                int flg = -1;
                for (MeetingUserScoreDetailEntity edu : excludeUser) {
                    if (uid.equals(edu.getUserId())) {
                        flg = 1;
                        log.debug("测试输出:edu.score={}   s={}  min={} max={} (edu.getScore()+s)={} ", edu.getScore(), s, min, max, (edu.getScore() + s));
                        if ((edu.getScore() + s) >= min && (edu.getScore() + s) <= max) {
                        } else {
                            excludeUserId.add(edu.getUserId());
                        }
                    }
                }
                log.debug("测试输出:flg={} ", flg);
                if (flg == -1) {
                    //未查到加分记录，按照积分为0处理
                    if ((0 + s) >= min && (0 + s) <= max) {
                    } else {
                        excludeUserId.add(uid);
                    }
                }
            }
            log.debug("<" + logTxt + "> 积分周期内不满足积分条件的人员数量！ regionId={} meetingId={} meetingType={} queryDate={} topOrgId={} operationType={} size={}", regionId, meetingId, meetingType, queryDate, topOrgId, operationType, excludeUserId.size());
            //去除不满足加分条件的人员
            userIdList.removeAll(excludeUserId);
            log.debug("<" + logTxt + "> 人员筛选结果！ regionId={} meetingId={} meetingType={} queryDate={} topOrgId={} operationType={} size={}", regionId, meetingId, meetingType, queryDate, topOrgId, operationType, userIdList.size());

            //上报到积分中心
            Map<Long, ScoreVO> initializeMap = new HashMap<>();
            if (msConf.getType() == 1 || msConf.getType() == 3) {
                userIdList.forEach(userId -> {
                    String token = UUID.randomUUID().toString().replaceAll("-", "");
                    ScoreConsumeForm scoreConsumeForm = new ScoreConsumeForm(regionId, userId, token, score, 12, operationType, topOrgId, logTxt, remark, consumeTime);
                    Long ret = thirdService.addScoreByUserId(regionId, scoreConsumeForm);
                    initializeMap.put(userId, new ScoreVO(token, ret != null));
                    log.debug("组织生活三指标用户积分日志一=>{}", JsonUtils.toJson(scoreConsumeForm));
//                    //记录结果
//                    MeetingUserScoreDetailEntity msde = MeetingUserScoreDetailEntity.builder().meetingId(meetingId).meetingType(meetingType).operationType(operationType).score(operationType==0?score:(-score)).scoreType(scoreType)
//                            .scoreSign(this.findScoreSign(cycle,queryDate)).scoreToken(token).userId(userId).status(ret!=null?1:2)
//                            .createTime(new Date()).remark(remark).build();
//                    meetingUserScoreDetailMapper.insert(msde);
                });
            }

            // 如果initializeMap为空(为空代表他没有走上面那个判断的逻辑) 初始化doris需要加积分的那一批人员容器
            if (MapUtils.isEmpty(initializeMap)) {
                userIdList.forEach(x -> {
                    String token = UUID.randomUUID().toString().replaceAll("-", "");
                    initializeMap.put(x, new ScoreVO(token, true));
                });
            }

            //处理到doris
            if (msConf.getType() == 2 || msConf.getType() == 3) {
                String finalConsumeTime = consumeTime==null? DateUtils.dateFormat(new Date(), "yyyy-MM-dd") + " 00:01:01": consumeTime;
                log.debug("组织生活三指标用户积分日志三=>[{}]", consumeTime);
                userIdList.forEach(userId -> {
                    ScoreVO vo = initializeMap.get(userId);
                    if (vo.getOK()) {
                        List<IndexUserScoreForm> dataList = new ArrayList<>();
                        Long dorisScore = operationType == 0 ? score : -score;
                        Integer dataMonth = Integer.parseInt(finalConsumeTime.substring(0, 4) + finalConsumeTime.substring(5, 7));
                        IndexUserScoreForm userScoreForm = new IndexUserScoreForm(ruleId, dataMonth, userId, dorisScore);
                        dataList.add(userScoreForm);
                        thirdService.addDorisUserScore(regionId, dataList);
                    }
                });
            }
            log.debug("operationUserScoreByCycle断点一:{}", initializeMap);
            if (MapUtils.isNotEmpty(initializeMap)) {
                initializeMap.forEach((userId, vo) -> {
                    //记录结果
                    MeetingUserScoreDetailEntity msde = MeetingUserScoreDetailEntity.builder().meetingId(meetingId).meetingType(meetingType).operationType(operationType).score(operationType == 0 ? score : (-score)).scoreType(scoreType)
                            .scoreSign(this.findScoreSign(cycle, queryDate)).scoreToken(vo.getToken()).userId(userId).status(vo.getOK() ? 1 : 2)
                            .createTime(new Date()).remark(remark == null ? logTxt : remark).build();
                    meetingUserScoreDetailMapper.insert(msde);
                });
            }

        } catch (Exception e) {
            log.error("<" + msConf.getLogTxt() + "> 报错！ regionId={} queryDate={}", regionId, queryDate, e);
        }
    }

    /**
     * 操作组织积分
     * regionId 区县编号
     * queryDate 查询
     * <p>
     * isRollBack 是否为回滚操作  0 否 1 是。
     * 如果是回滚操作则需要判断引起回滚操作的会议的当前积分情况(获得和扣减的情况)，根据本次会议的积分情况判断是否进行真实的加分或扣分
     * 例如考勤已签到加分后，撤回/回退/取消操作时扣分就是回滚操作。
     * 例如考勤未签到扣分后，撤回/回退/取消操作时加分就是回滚操作。
     */
    private void operationOrgScoreByCycle(Long regionId, Long meetingId, Date queryDate, List<Long> orgIdList, Integer operationType, MeetingScore msConf, Integer isRollBack) {
        try {
            if (msConf == null) {
                //未获取到配置直接抛出异常
                log.error("组织生活，积分奖励配置信息失败!");
                throw new ApiException("组织生活，积分奖励配置信息失败");
            }
            if (isRollBack == 1 && meetingId == null) {
                log.error("isRollBack 为1(回滚操作)时 meetingId 不能为空!");
                throw new ApiException("isRollBack 为1(回滚操作)时 meetingId 不能为空!");
            }
            Integer cycle = msConf.getCycle();
            Integer max = msConf.getMax();
            Integer min = msConf.getMin();
            Long score = msConf.getScore();
            Integer scoreType = msConf.getScoreType();
            String logTxt = msConf.getLogTxt();
            Integer meetingType = msConf.getMeetingType();
            Integer ruleid = msConf.getRuleId();
            //新增积分任务标记  tc 2022-06-06
            String remark = null;
            Integer ruleId = null;
            if (meetingType == 1) {
                remark = "积分任务_按时组织召开党员大会";
//                ruleId = DorisScoreConstant.MEETING_BIGPARTY_ORG;
            } else if (meetingType == 2) {
                remark = "积分任务_按时组织召开支委会";
//                ruleId = DorisScoreConstant.MEETING_BRANCH_ORG;
            } else if (meetingType == 4) {
                remark = "积分任务_按时组织开展党课";
//                ruleId = DorisScoreConstant.MEETING_LECTURE_ORG;
            } else if (meetingType == 5) {
                remark = "积分任务_按时组织开展主题党日活动";
//                ruleId = DorisScoreConstant.MEETING_LECTURE_ORG;

            }

            //根据region获取顶级组织编号
            Long topOrgId = appConfigHelperUtils.findTopOrgIdByRegionId(regionId);

            //验证积分周期
            Map<String, Integer> reCycle = findCycle(cycle, queryDate);
            //查看是否属于当前周期,如果是以前的数据，则将consumeTime设置为以前的日期
            Boolean isNotNow = isNow(queryDate, cycle);
            String consumeTime = isNotNow ? DateUtils.dateFormat(queryDate, "yyyy-MM-dd") + " 00:01:01" : DateUtils.dateFormat(new Date(), "yyyy-MM-dd") + " 00:01:01";
            List<MeetingOrgScoreDetailEntity> excludeOrg = meetingOrgScoreDetailMapper.findOrgScoreCycle(orgIdList, meetingType, scoreType, reCycle.get("start"), reCycle.get("end"), isRollBack, meetingId);
            log.debug("<" + logTxt + "> 积分周期内积分查询结果！ regionId={} meetingId={} meetingType={} queryDate={} topOrgId={} operationType={} isRollBack={} excludeOrg={} ", regionId, meetingId, meetingType, queryDate, topOrgId, operationType, isRollBack, excludeOrg);
            //根据配置筛选未满足加分条件的组织
            Long s = operationType == 0 ? score : (0 - score);
            log.debug("operationOrgScoreByCycle断点一=>{},{}", s, JsonUtils.toJson(excludeOrg));
            List<Long> excludeOrgId = new ArrayList<>();
            for (Long oid : orgIdList) {
                int flg = -1;
                for (MeetingOrgScoreDetailEntity edo : excludeOrg) {
                    if (oid.equals(edo.getOrgId())) {
                        flg = 1;
//                        log.debug("测试输出:edu.score={}   s={}  min={} max={} (edu.getScore()+s)={} ",edo.getScore(),s,min,max,(edo.getScore()+s));
                        if ((edo.getScore() + s) >= min && (edo.getScore() + s) <= max) {
                        } else {
                            excludeOrgId.add(edo.getOrgId());
                        }
                    }
                }
//                log.debug("测试输出:flg={} ",flg);
                if (flg == -1) {
                    //未查到加分记录，按照积分为0处理
                    if ((0 + s) >= min && (0 + s) <= max) {
                    } else {
                        excludeOrgId.add(oid);
                    }
                }
            }

            log.debug("<" + logTxt + "> 积分周期内不满足积分条件的组织数量！ regionId={} meetingId={} meetingType={} queryDate={} topOrgId={} size={}", regionId, meetingId, meetingType, queryDate, topOrgId, excludeOrgId.size());
            //去除不满足加分条件的组织
            orgIdList.removeAll(excludeOrgId);
            //上报到积分中心
            String finalRemark = remark;

            // 装可以加分的那一批组织的容器
            Map<Long, ScoreVO> initializeMap = new HashMap<>(orgIdList.size());
            if (msConf.getType() == 1 || msConf.getType() == 3) {
                orgIdList.forEach(orgId -> {
                    String token = UUID.randomUUID().toString().replaceAll("-", "");
                    ScoreConsumeForm scoreConsumeForm = new ScoreConsumeForm(regionId, token, orgId, score, 12, operationType, topOrgId, logTxt, finalRemark, consumeTime);
                    log.debug("组织生活三指标组织积分日志一=>{}", JsonUtils.toJson(scoreConsumeForm));
                    Long ret = thirdService.addScoreByOrgId(regionId, scoreConsumeForm);
                    initializeMap.put(orgId, new ScoreVO(token, ret != null));
                    //记录结果
//                    MeetingOrgScoreDetailEntity mode = MeetingOrgScoreDetailEntity.builder().meetingId(meetingId).meetingType(meetingType).operationType(operationType).score(operationType == 0 ? score : (-score)).scoreType(scoreType)
//                            .scoreSign(this.findScoreSign(cycle, queryDate)).scoreToken(token).orgId(orgId).status(ret != null ? 1 : 2)
//                            .createTime(new Date()).remark(logTxt).build();
//                    meetingOrgScoreDetailMapper.insert(mode);
                });
            }

            // 如果initializeMap为空(为空代表他没有走上面那个判断的逻辑) 初始化doris需要加积分的那一批组织容器
            if (MapUtils.isEmpty(initializeMap)) {
                orgIdList.forEach(x -> {
                    String token = UUID.randomUUID().toString().replaceAll("-", "");
                    initializeMap.put(x, new ScoreVO(token, true));
                });
            }
            log.debug("initializeMap是否为空=>{},{}", initializeMap, msConf.getType());
            //处理到doris
            if (msConf.getType() == 2 || msConf.getType() == 3) {
                orgIdList.forEach(orgId -> {
                    ScoreVO vo = initializeMap.get(orgId);
                    if (vo.OK) {
                        List<IndexOrgScoreForm> dataList = new ArrayList<>();
                        Long dorisScore = operationType == 0 ? score : (-score);
                        Integer dataMonth = Integer.parseInt(consumeTime.substring(0, 4) + consumeTime.substring(5, 7));
                        IndexOrgScoreForm orgScoreForm = new IndexOrgScoreForm(ruleid, dataMonth, orgId, 1, dorisScore);
                        dataList.add(orgScoreForm);
                        thirdService.addDorisOrgScore(regionId, dataList);
                    }
                });
            }
            if (MapUtils.isNotEmpty(initializeMap)) {
                initializeMap.forEach((orgId, vo) -> {
                    MeetingOrgScoreDetailEntity mode = MeetingOrgScoreDetailEntity.builder().meetingId(meetingId).meetingType(meetingType).operationType(operationType).score(operationType == 0 ? score : (-score)).scoreType(scoreType)
                            .scoreSign(this.findScoreSign(cycle, queryDate)).scoreToken(vo.getToken()).orgId(orgId).status(vo.OK ? 1 : 2)
                            .createTime(new Date()).remark(logTxt).build();
                    meetingOrgScoreDetailMapper.insert(mode);
                });
            }
        } catch (Exception e) {
            log.error("<" + msConf.getLogTxt() + "_" + msConf.getRemark() + "> 报错！ regionId={} queryDate={}", regionId, queryDate, e);
        }
    }


    /**
     * 三指标加分类
     */
    @Data
    @Builder
    private static class ScoreVO {
        // 日志表记录的token
        String token;
        // 是否加分成功
        Boolean OK;

        public ScoreVO() {
        }

        public ScoreVO(String token, Boolean OK) {
            this.token = token;
            this.OK = OK;
        }
    }

    private Boolean isNow(Date queryDate, Integer cycle) {
        switch (cycle) {
            case 1://月度
                return queryDate.before(DateUtils.firstDayOfMonth());
            case 2://季度
                return queryDate.before(DateUtils.firstDateOfQuarter(LocalDate.now().getYear(), DateUtils.getQuarter()));
            case 3://年度
                return Integer.parseInt(DateUtils.dateFormat(queryDate, "yyyy")) < LocalDate.now().getYear();
        }
        return false;
    }


    /**
     * @param cycle     周期  1 月度，2季度，3 年度
     * @param queryDate 查询日期
     * @return
     * @throws Exception
     */
    private Map<String, Integer> findCycle(Integer cycle, Date queryDate) throws Exception {
        Map<String, Integer> re = new HashMap<>();
        if (cycle == 1) {
            //月度周期
            String dt = DateUtils.dateFormat(queryDate, "yyyyMM");
            re.put("start", Integer.valueOf(dt));
            re.put("end", Integer.valueOf(dt));
        } else if (cycle == 2) {
            //季度周期
            Calendar cale = Calendar.getInstance();
            int month = cale.get(Calendar.MONTH) + 1;
            String dt = DateUtils.dateFormat(queryDate, "yyyyMM");
            if (month % 3 == 0) {
                re.put("start", Integer.valueOf(DateUtils.getDateByMonths(-2, "yyyyMM", dt)));
                re.put("end", Integer.valueOf(dt));
            } else if (month % 3 == 1) {
                re.put("start", Integer.valueOf(dt));
                re.put("end", Integer.valueOf(DateUtils.getDateByMonths(2, "yyyyMM", dt)));
            } else if (month % 3 == 2) {
                re.put("start", Integer.valueOf(DateUtils.getDateByMonths(-1, "yyyyMM", dt)));
                re.put("end", Integer.valueOf(DateUtils.getDateByMonths(1, "yyyyMM", dt)));
            }
        } else if (cycle == 3) {
            //年度周期
            String dt = DateUtils.dateFormat(queryDate, "yyyy");
            re.put("start", Integer.valueOf(dt));
            re.put("end", Integer.valueOf(dt));
        }
        return re;
    }

    private Integer findScoreSign(Integer cycle, Date queryDate) {
        String dt = "";
        if (cycle == 3) {
            //年度周期
            dt = DateUtils.dateFormat(queryDate, "yyyy");
        } else {
            //月度和季度周期
            dt = DateUtils.dateFormat(queryDate, "yyyyMM");
        }
        return Integer.valueOf(dt);
    }
}
