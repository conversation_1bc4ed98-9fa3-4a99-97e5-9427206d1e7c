package com.goodsogood.ows.service

import cn.hutool.core.date.LocalDateTimeUtil
import com.goodsogood.ows.common.FileSourceEnum
import com.goodsogood.ows.mapper.MeetingCommentMapper
import com.goodsogood.ows.mapper.UserCommendPenalizeMapper
import com.goodsogood.ows.model.db.CommentRatingEnum
import com.goodsogood.ows.model.db.MeetingCommentMemberEntity
import com.goodsogood.ows.model.db.UserCommendPenalizeEntity
import com.goodsogood.ows.model.vo.GloryType
import com.goodsogood.ows.model.vo.MeetingFileVO
import com.goodsogood.ows.utils.DateUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example

@Service
class GloryService(@Autowired val userCommendPenalizeMapper: UserCommendPenalizeMapper,
                   @Autowired val userCommendPenalizeService: UserCommendPenalizeService,
                   @Autowired val commentMemberComplexService: CommentMemberComplexService,
                   @Autowired val commentMapper: MeetingCommentMapper,
                   @Autowired val meetingFileService: MeetingFileService,
                   @Autowired val openService: OpenService
) {

    private val log = LoggerFactory.getLogger(GloryService::class.java)

    /**
     * @param type 1-党员奖惩，2-民主评议
     */
    fun batchAddGlory (userId: Long? = null, type: Int? = null, headers: HttpHeaders) {
        log.debug("处理我的荣誉数据 userId: $userId, type: $type")
        if (type == null || type == 1) {
            val userCommendPenalizeList = selectUserCommendPenalizeList(userId)
            userCommendPenalizeList.forEach {
                val date = DateUtils.stringToDate(it.effectiveTime, DateUtils.DATE_FORMAT)
                val time = LocalDateTimeUtil.of(date)
                val rewardName = userCommendPenalizeService.getRewardName(it)
                val fileVOS: List<MeetingFileVO> = this.meetingFileService.selectByLinkedId(
                    it.meetingUserCommendPenalizeId,
                    FileSourceEnum.MEMBER_COMMEND_PENALIZE_PIC
                )
                var path: String? = null
                if (fileVOS.isNotEmpty()) {
                    path = fileVOS[0].path
                }
                openService.addMyGlory(
                    it.userId,
                    GloryType.MEMBER_REWARD.key,
                    time,
                    rewardName,
                    (if (it.awardUnit == null) "" else it.awardUnit) + "授予\"$rewardName\"荣誉称号",
                    path,
                    headers
                )
            }
        }

        if (type == null || type == 2) {
            val commentList = selectCommentMemberList(userId)
            commentList.forEach {
                it.commentMemberId?.let { id ->
                    val memberComplex = commentMemberComplexService.selectMemberComplex(id)
                    if (memberComplex.complexRating == CommentRatingEnum.EXCELLENT.key) {
                        openService.addMyGlory(
                            it.userId,
                            GloryType.COMMENT.key,
                            memberComplex.createTime,
                            "民主评议荣获优秀等次",
                            "在${it.year}年党员民主评议中荣获优秀等次",
                            null,
                            headers
                        )
                    }
                }
            }
        }
    }

    private fun selectUserCommendPenalizeList(userId: Long? = null): MutableList<UserCommendPenalizeEntity> {
        val example = Example(UserCommendPenalizeEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andEqualTo("type" , 1);
        criteria.andEqualTo("status", 1);
        criteria.andEqualTo("approvalStatus", 2);
        if (userId != null) {
            criteria.andEqualTo("userId", userId);
        }
        return userCommendPenalizeMapper.selectByExample(example)
    }

    private fun selectCommentMemberList(userId: Long? = null): MutableList<MeetingCommentMemberEntity> {
        return commentMapper.selectCommentMember(userId, 1);
    }
}