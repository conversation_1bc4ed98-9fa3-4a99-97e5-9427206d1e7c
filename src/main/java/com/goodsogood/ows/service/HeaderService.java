package com.goodsogood.ows.service;

import com.goodsogood.ows.common.pojo.Headers;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.utils.HttpInnerUtils;
import com.google.common.base.Preconditions;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <p>公共的header services</p>
 * <li>主要用于一些公共的参数校验和获取</li>
 *
 * <AUTHOR>
 * @create 2018-10-23 14:06
 **/
@Service
@Log4j2
public class HeaderService {
     private final static String OPERATOR_UOID = "_uoid";
     private final Errors errors;
     public final RestTemplate restTemplate;

     @Value("${tog-services.user-center}")
     private String userCenter;

     @Autowired
     public HeaderService(Errors errors, RestTemplate restTemplate) {
          this.errors = errors;
          this.restTemplate = restTemplate;
     }

     /**
      * 构造头信息，包含组织名称
      * @param headers
      * @return
      */
     public Headers bulidOrgHeader(HttpHeaders headers) {
          Headers header = this.bulidHeaderInner(headers);
          String orgName;
          //非空验证
          try {
               orgName = HttpInnerUtils.getOrgInfo(this.restTemplate, headers, this.userCenter, header.getOid(), "name").toString();
               header.setOrgName(orgName);
          } catch (NullPointerException e) {
               throw new ApiException("请求头信息参数有错", new Result<>(errors, Global.Errors.ERROR_HEADER, HttpStatus.OK.value(), "请求头信息参数有错"));
          } catch (Exception e) {
               log.error("解析请求头，远程获取用户中心组织信息出错: " + e.getMessage(), e);
               throw new ApiException("解析请求头，远程获取用户中心组织信息出错", new Result<>(errors, 1923, HttpStatus.INTERNAL_SERVER_ERROR.value(), "解析请求头，远程获取用户中心组织信息出错"));
          }
          return header;
     }

     /**
      * 获取头信息
      * @param headers
      * @return
      */
     public Headers bulidHeader(HttpHeaders headers){
          return bulidHeaderInner(headers);
     }

     /**
      * 获取头信息
      * @param headers
      * @return
      */
     private Headers bulidHeaderInner(HttpHeaders headers){
          Headers header = new Headers();
          HeaderHelper.SysHeader sysHeader;
          Long oid,uid,regionId;
          Integer type;
          String orgName, token, userName;
          //非空验证
          String message = "";
          try {
               sysHeader = HeaderHelper.buildMyHeader(headers);
               Preconditions.checkNotNull(sysHeader);
               message = "oid";
               oid = Preconditions.checkNotNull(sysHeader.getOid());
               List<String> uoid = headers.get(OPERATOR_UOID);
               if (uoid != null && !uoid.isEmpty() && StringUtils.isNotBlank(headers.get(OPERATOR_UOID).get(0))) {
                    oid = Long.parseLong(headers.get(OPERATOR_UOID).get(0));
               } else {
                    log.debug("请求头信息参数{}为空", OPERATOR_UOID);
               }
               message = "uid";
               uid = Preconditions.checkNotNull(sysHeader.getUserId());
               message = "type";
               type = Preconditions.checkNotNull(sysHeader.getType());
               message = "token";
               token = Preconditions.checkNotNull(sysHeader.getToken());
               message = "userName";
               userName = Preconditions.checkNotNull(sysHeader.getUserName());
               message = "orgName";
               orgName =  Preconditions.checkNotNull(sysHeader.getOrgName());
               message = "regionId";
               regionId =  Preconditions.checkNotNull(sysHeader.getRegionId());
               header.setType(type);
               header.setOid(oid);
               header.setUserId(uid);
               header.setToken(token);
               header.setUserName(userName);
               header.setOrgName(orgName);
               header.setRegionId(regionId);
          } catch (NullPointerException e) {
               throw new ApiException("请求头信息参数有错, " + message + " 为空" , new Result<>(errors, Global.Errors.ERROR_HEADER, HttpStatus.OK.value(), "请求头信息参数有错" + message + " 为空"));
          } catch (Exception e) {
               log.error("解析请求头出错: " + e.getMessage(), e);
               throw new ApiException("解析请求头出错", new Result<>(errors, 1923, HttpStatus.INTERNAL_SERVER_ERROR.value(), "解析请求头出错"));
          }
          return header;
     }



}
