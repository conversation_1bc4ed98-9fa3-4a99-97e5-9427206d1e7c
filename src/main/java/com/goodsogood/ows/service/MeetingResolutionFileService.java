package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.MeetingResolutionFileMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2018-10-25 18:01
 */
@Service
@Log4j2
public class MeetingResolutionFileService {

  private final MeetingResolutionFileMapper meetingResolutionFileMapper;

  @Autowired
  public MeetingResolutionFileService(MeetingResolutionFileMapper meetingResolutionFileMapper) {
    this.meetingResolutionFileMapper = meetingResolutionFileMapper;
  }
}
