package com.goodsogood.ows.service;

import com.aliyun.dingtalkconference_1_0.models.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.UserDingInfoMapper;
import com.goodsogood.ows.mapper.VideoConferenceMapper;
import com.goodsogood.ows.mapper.VideoConferenceUserMapper;
import com.goodsogood.ows.model.db.MeetingOrgEntity;
import com.goodsogood.ows.model.db.UserDingInfoEntity;
import com.goodsogood.ows.model.db.VideoConferenceEntity;
import com.goodsogood.ows.model.db.VideoConferenceUserEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.VideoConferenceForm;
import com.goodsogood.ows.model.vo.VideoConferenceVo;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 视频会议
 */
@Service
@Log4j2
public class VideoConferenceService {

    private final VideoConferenceMapper videoConferenceMapper;

    private final TzdVieoConferencesService tzdVieoConferencesService;

    private final UserDingInfoMapper userDingInfoMapper;

    private final VideoConferenceUserMapper videoConferenceUserMapper;

    @Autowired
    public VideoConferenceService(VideoConferenceMapper videoConferenceMapper, TzdVieoConferencesService tzdVieoConferencesService, UserDingInfoMapper userDingInfoMapper, VideoConferenceUserMapper videoConferenceUserMapper) {

        this.videoConferenceMapper = videoConferenceMapper;
        this.tzdVieoConferencesService = tzdVieoConferencesService;
        this.userDingInfoMapper = userDingInfoMapper;
        this.videoConferenceUserMapper = videoConferenceUserMapper;
    }

    /**
     * 查询视频会议列表
     *
     * @param regionId
     * @param videoConferenceForm
     * @return
     */
    public Page<VideoConferenceEntity> list(Long regionId, VideoConferenceForm videoConferenceForm) {

        Page<VideoConferenceEntity> page = PageHelper.startPage(videoConferenceForm.getPageNo(), videoConferenceForm.getPageSize())
                .doSelectPage(() -> this.videoConferenceMapper.selectList(videoConferenceForm.getConferenceType(),
                        videoConferenceForm.getConferenceName(),
                        videoConferenceForm.getConferenceTitle(),
                        videoConferenceForm.getRoomCode(),
                        videoConferenceForm.getMinCreateTime(),
                        videoConferenceForm.getMaxCreateTime(),
                        regionId,
                                videoConferenceForm.getIsHistory())
                        );
        return page;
    }

    /**
     * 查询视频会议详情
     *
     * @param conferenceId
     * @return
     */
    public VideoConferenceVo info(Long conferenceId) {
        VideoConferenceEntity videoConferenceEntity = videoConferenceMapper.selectByPrimaryKey(conferenceId);

        VideoConferenceVo videoConferenceVo = new VideoConferenceVo();
        videoConferenceVo.setConferenceId(videoConferenceEntity.getConferenceId());
        videoConferenceVo.setConferenceTitle(videoConferenceEntity.getConferenceTitle());
        videoConferenceVo.setConferenceName(videoConferenceEntity.getConferenceName());
        videoConferenceVo.setConferenceType(videoConferenceEntity.getConferenceType());
        videoConferenceVo.setEndTime(videoConferenceEntity.getEndTime());
        videoConferenceVo.setStartTime(videoConferenceEntity.getStartTime());

        Example exampleUser = new Example(VideoConferenceUserEntity.class);
        exampleUser.createCriteria().andEqualTo("conferenceId", videoConferenceEntity.getConferenceId());
        List<VideoConferenceUserEntity> videoConferenceUser = videoConferenceUserMapper.selectByExample(exampleUser);

        videoConferenceVo.setUsers(videoConferenceUser);
        return videoConferenceVo;
    }

    /**
     * 加入视频会议
     *
     * @param regionId
     * @param roomCode          房间号
     * @return
     */
    public VideoConferenceEntity join(Long regionId, String roomCode){
        Example example = new Example(VideoConferenceEntity.class);
        example.createCriteria().andEqualTo("regionId", regionId).andEqualTo("roomCode", roomCode);
        VideoConferenceEntity videoConferenceEntity = videoConferenceMapper.selectOneByExample(example);
        return videoConferenceEntity;
    }

    /**
     * 创建视频会议
     *
     * @param sysHeader
     * @param videoConferenceVo
     * @return
     */
    @Transactional
    public Result<Integer> addVideoConference(HeaderHelper.SysHeader sysHeader, VideoConferenceVo videoConferenceVo, Errors errors) {
        //获取当前创建人员的unionId
        Example example = new Example(UserDingInfoEntity.class);
        example.createCriteria().andEqualTo("userId", sysHeader.getUserId());
        List<UserDingInfoEntity> userDingInfoEntitys = userDingInfoMapper.selectByExample(example);
        if (userDingInfoEntitys == null || userDingInfoEntitys.size() == 0 || StringUtils.isEmpty(userDingInfoEntitys.get(0).getUnionId())) {
            throw new ApiException("发起人unionId为空", new Result<>(500, "发起人unionId为空", 500, null));
        }

        List<Long> userIds = new ArrayList<>();
        if(videoConferenceVo.getUsers() != null && videoConferenceVo.getUsers().size() > 0){
            userIds = videoConferenceVo.getUsers().stream().map(VideoConferenceUserEntity::getUserId).toList();
        }
        //参会人员统一查询unionId
        Example exampleUserIds = new Example(UserDingInfoEntity.class);
        exampleUserIds.createCriteria().andIn("userId", userIds);
        List<UserDingInfoEntity> userDingInfoEntityIds = userDingInfoMapper.selectByExample(exampleUserIds);
        List<String> users = userDingInfoEntityIds.stream().map(UserDingInfoEntity::getUnionId).collect(Collectors.toList());
        CreateVideoConferenceResponse response = tzdVieoConferencesService.createVideoConference(userDingInfoEntitys.get(0).getUnionId(),
                videoConferenceVo.getConferenceTitle(),
                users);

        if (response == null && response.getBody() != null) {
            throw new ApiException("调用天子钉创建视频会议接口失败", new Result<>(500, "调用天子钉创建视频会议接口失败", 500, null));
        }
        //保存数据库
        CreateVideoConferenceResponseBody body = response.getBody();
        VideoConferenceEntity videoConferenceEntity = new VideoConferenceEntity();
        BeanUtils.copyProperties(videoConferenceVo, videoConferenceEntity);
        videoConferenceEntity.setTzdConferenceId(body.getConferenceId());
        videoConferenceEntity.setConferencePassword(body.getConferencePassword());
        videoConferenceEntity.setHostPassword(body.getHostPassword());
        videoConferenceEntity.setExternalLinkUrl(body.getExternalLinkUrl());
        videoConferenceEntity.setPhoneNumbers(JsonUtils.toJson(body.getPhoneNumbers()));
        videoConferenceEntity.setRoomCode(body.getRoomCode());
        videoConferenceEntity.setCreateUnionId(userDingInfoEntitys.get(0).getUnionId());
        videoConferenceEntity.setCreateId(sysHeader.getUserId());
        videoConferenceEntity.setCreateTime(new Date());
        videoConferenceEntity.setStatus(0);
        videoConferenceEntity.setRegionId(sysHeader.getRegionId());
        videoConferenceMapper.insert(videoConferenceEntity);
        //保存参会人员
        for (VideoConferenceUserEntity videoConferenceUserEntity : videoConferenceVo.getUsers()) {
            videoConferenceUserEntity.setConferenceId(videoConferenceEntity.getConferenceId());
            videoConferenceUserMapper.insert(videoConferenceUserEntity);
        }
        return new Result<>(1, errors);
    }

    /**
     * 创建预约会议
     *
     * @param sysHeader
     * @param videoConferenceVo
     * @return
     */
    @Transactional
    public Result<Integer> addScheduleConferences(HeaderHelper.SysHeader sysHeader, VideoConferenceVo videoConferenceVo, Errors errors) {
        //判断开始时间不能小于当前时间
        if (videoConferenceVo.getStartTime().compareTo(new Date()) <= 0) {
            throw new ApiException("预约会议开始时间不能小于当前时间", new Result<>(400, "预约会议开始时间不能小于当前时间", 400, null));
        }
        //获取当前创建人员的unionId
        Example example = new Example(UserDingInfoEntity.class);
        example.createCriteria().andEqualTo("userId", sysHeader.getUserId());
        List<UserDingInfoEntity> userDingInfoEntitys = userDingInfoMapper.selectByExample(example);
        if (userDingInfoEntitys == null || userDingInfoEntitys.size() == 0 || StringUtils.isEmpty(userDingInfoEntitys.get(0).getUnionId())) {
            throw new ApiException("发起人unionId为空", new Result<>(500, "发起人unionId为空", 500, null));
        }
        List<Long> userIds = new ArrayList<>();
        if(videoConferenceVo.getUsers() != null && videoConferenceVo.getUsers().size() > 0){
            userIds = videoConferenceVo.getUsers().stream().map(VideoConferenceUserEntity::getUserId).toList();
        }
        //参会人员统一查询unionId
//        Example exampleUserIds = new Example(UserDingInfoEntity.class);
//        exampleUserIds.createCriteria().andIn("userId", userIds);
//        List<UserDingInfoEntity> userDingInfoEntityIds = userDingInfoMapper.selectByExample(exampleUserIds);
        CreateScheduleConferenceResponse response = tzdVieoConferencesService.createScheduleConference(userDingInfoEntitys.get(0).getUnionId(),
                videoConferenceVo.getConferenceTitle(),
                videoConferenceVo.getStartTime(),
                videoConferenceVo.getEndTime());

        if (response == null && response.getBody() != null) {
            throw new ApiException("调用天子钉创建预约会议接口失败", new Result<>(500, "调用天子钉创建预约会议接口失败", 500, null));
        }
        //保存数据库
        CreateScheduleConferenceResponseBody body = response.getBody();
        VideoConferenceEntity videoConferenceEntity = new VideoConferenceEntity();
        BeanUtils.copyProperties(videoConferenceVo, videoConferenceEntity);
        videoConferenceEntity.setTzdConferenceId(body.getScheduleConferenceId());
        videoConferenceEntity.setConferencePassword(body.getScheduleConferenceId());
        videoConferenceEntity.setExternalLinkUrl(body.getUrl());
        videoConferenceEntity.setPhoneNumbers(JsonUtils.toJson(body.getPhones()));
        videoConferenceEntity.setRoomCode(body.getRoomCode());
        videoConferenceEntity.setCreateUnionId(userDingInfoEntitys.get(0).getUnionId());
        videoConferenceEntity.setCreateId(sysHeader.getUserId());
        videoConferenceEntity.setCreateTime(new Date());
        videoConferenceEntity.setRegionId(sysHeader.getRegionId());
        videoConferenceEntity.setStatus(0);
        videoConferenceMapper.insert(videoConferenceEntity);
        //保存参会人员
        if(videoConferenceVo.getUsers() != null && videoConferenceVo.getUsers().size() > 0) {
            for (VideoConferenceUserEntity videoConferenceUserEntity : videoConferenceVo.getUsers()) {
                videoConferenceUserEntity.setConferenceId(videoConferenceEntity.getConferenceId());
                videoConferenceUserMapper.insert(videoConferenceUserEntity);
            }
        }
        return new Result<>(1, errors);
    }

    /**
     * 修改预约会议
     *
     * @param sysHeader
     * @param videoConferenceVo
     * @return
     */
    @Transactional
    public Result<Integer> editScheduleConferences(HeaderHelper.SysHeader sysHeader, VideoConferenceVo videoConferenceVo, Errors errors) {
        //获取旧的预约会议信息
        VideoConferenceEntity videoConferenceEntity = videoConferenceMapper.selectByPrimaryKey(videoConferenceVo.getConferenceId());
        //判断当前会议类型
        if (videoConferenceEntity.getConferenceType() == 0) {
            throw new ApiException("视频会议不能修改", new Result<>(400, "视频会议不能修改", 400, null));
        }
        //判断当前修改人是否是创建人
        if (!sysHeader.getUserId().equals(videoConferenceEntity.getCreateId())) {
            throw new ApiException("当前会议只有创建人可以修改", new Result<>(400, "当前会议只有创建人可以修改", 400, null));
        }
        //判断是否已开始或已结束的会议不能修改
        if (videoConferenceEntity.getStatus() != 0) {
            throw new ApiException("当前预约会议不是未开始状态，无法修改", new Result<>(400, "当前预约会议不是未开始状态，无法修改", 400, null));
        }
        //判断新会议开始时间不能小于当前时间
        if (videoConferenceVo.getStartTime().compareTo(new Date()) <= 0) {
            throw new ApiException("预约会议开始时间不能小于当前时间", new Result<>(400, "预约会议开始时间不能小于当前时间", 400, null));
        }
        //获取当前创建人员的unionId
        Example example = new Example(UserDingInfoEntity.class);
        example.createCriteria().andEqualTo("userId", sysHeader.getUserId());
        List<UserDingInfoEntity> userDingInfoEntitys = userDingInfoMapper.selectByExample(example);
        if (userDingInfoEntitys == null || userDingInfoEntitys.size() == 0 || StringUtils.isEmpty(userDingInfoEntitys.get(0).getUnionId())) {
            throw new ApiException("发起人unionId为空", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "发起人unionId为空"));
        }
        List<Long> userIds = new ArrayList<>();
        if(videoConferenceVo.getUsers() != null && videoConferenceVo.getUsers().size() > 0){
            userIds = videoConferenceVo.getUsers().stream().map(VideoConferenceUserEntity::getUserId).toList();
        }
        //参会人员统一查询unionId
//        Example exampleUserIds = new Example(UserDingInfoEntity.class);
//        exampleUserIds.createCriteria().andIn("userId", userIds);
//        List<UserDingInfoEntity> userDingInfoEntityIds = userDingInfoMapper.selectByExample(exampleUserIds);
        UpdateScheduleConferenceResponse response = tzdVieoConferencesService.updateScheduleConference(
                videoConferenceEntity.getTzdConferenceId(),
                videoConferenceEntity.getCreateUnionId(),
                videoConferenceVo.getConferenceTitle(),
                videoConferenceVo.getStartTime(),
                videoConferenceVo.getEndTime());

        if (response == null && response.getBody() != null) {
            throw new ApiException("调用天子钉更新会议接口失败", new Result<>(500, "调用天子钉更新会议接口失败", 500, null));
        }
        //保存数据库
        UpdateScheduleConferenceResponseBody body = response.getBody();
        if (body.getSuccess()) {
            videoConferenceEntity.setConferenceTitle(videoConferenceVo.getConferenceTitle());
            videoConferenceEntity.setStartTime(videoConferenceVo.getStartTime());
            videoConferenceEntity.setEndTime(videoConferenceVo.getEndTime());
            videoConferenceMapper.updateByPrimaryKey(videoConferenceEntity);
            //保存参会人员(先删除，再添加)
            Example exampleUser = new Example(VideoConferenceUserEntity.class);
            exampleUser.createCriteria().andEqualTo("conferenceId", videoConferenceEntity.getConferenceId());
            videoConferenceUserMapper.deleteByExample(exampleUser);
            //添加
            if(videoConferenceVo.getUsers() != null && videoConferenceVo.getUsers().size() > 0){
                for (VideoConferenceUserEntity videoConferenceUserEntity : videoConferenceVo.getUsers()) {
                    videoConferenceUserEntity.setConferenceId(videoConferenceEntity.getConferenceId());
                    videoConferenceUserMapper.insert(videoConferenceUserEntity);
                }
            }
            return new Result<>(1, errors);
        }

        return new Result<>(0, errors);
    }

    /**
     * 取消预约会议
     *
     * @param sysHeader
     * @param videoConferenceVo
     * @return
     */
    @Transactional
    public Result<Integer> cancelScheduleConferences(HeaderHelper.SysHeader sysHeader, VideoConferenceVo videoConferenceVo, Errors errors) {
        //获取旧的预约会议信息
        VideoConferenceEntity videoConferenceEntity = videoConferenceMapper.selectByPrimaryKey(videoConferenceVo.getConferenceId());
        //判断当前会议类型
        if (videoConferenceEntity.getConferenceType() == 0) {
            throw new ApiException("视频会议不能取消", new Result<>(400, "视频会议不能取消", 400, null));
        }
        //判断当前修改人是否是创建人
        if (!sysHeader.getUserId().equals(videoConferenceEntity.getCreateId())) {
            throw new ApiException("当前会议只有创建人可以取消", new Result<>(400, "当前会议只有创建人可以取消", 400, null));
        }
        //判断是否已开始或已结束的会议不能取消
        if (videoConferenceEntity.getStatus() != 0) {
            throw new ApiException("当前预约会议不是未开始状态，无法取消", new Result<>(400, "当前预约会议不是未开始状态，无法取消", 400, null));
        }

        //获取当前创建人员的unionId
        Example example = new Example(UserDingInfoEntity.class);
        example.createCriteria().andEqualTo("userId", sysHeader.getUserId());
        List<UserDingInfoEntity> userDingInfoEntitys = userDingInfoMapper.selectByExample(example);
        if (userDingInfoEntitys == null || userDingInfoEntitys.size() == 0 || StringUtils.isEmpty(userDingInfoEntitys.get(0).getUnionId())) {
            throw new ApiException("发起人unionId为空", new Result<>(500, "发起人unionId为空", 500, null));
        }
        CancelScheduleConferenceResponse response = tzdVieoConferencesService.cancelScheduleConference(
                videoConferenceEntity.getTzdConferenceId(),
                videoConferenceEntity.getCreateUnionId());

        if (response == null && response.getBody() != null) {
            throw new ApiException("调用天子钉取消会议接口失败", new Result<>(500, "调用天子钉取消会议接口失败", 500, null));
        }
        //保存数据库
        CancelScheduleConferenceResponseBody body = response.getBody();
        if (body.getSuccess()) {
            videoConferenceEntity.setStatus(3);
            videoConferenceMapper.updateByPrimaryKey(videoConferenceEntity);
            return new Result<>(1, errors);
        }
        return new Result<>(0, errors);
    }

    /**
     * 批量查询会议信息
     *
     * @return
     */
    @Transactional
    public Integer queryConferenceInfoBatchList(Long regionId) {
        //查询当前所有会议（除开已结束和已取消会议）
        Example example = new Example(VideoConferenceEntity.class);
        example.createCriteria().andNotIn("status", new ArrayList(Arrays.asList(2, 3))).andEqualTo("regionId", regionId);
        List<VideoConferenceEntity> videoConferenceEntities = videoConferenceMapper.selectByExample(example);
        if (videoConferenceEntities == null || videoConferenceEntities.size() == 0) {
            return 1;
        }

        for (VideoConferenceEntity videoConferenceEntity : videoConferenceEntities) {
            Integer status = null;
            //分别获取当前会议信息
            if(videoConferenceEntity.getConferenceType() == 0){
                //快速会议
                QueryConferenceInfoResponse response = tzdVieoConferencesService.queryConferenceInfoWithOptions(videoConferenceEntity.getTzdConferenceId());
                if(response!=null && response.getBody() != null && response.getBody().getConfInfo() !=null){
                    status = response.getBody().getConfInfo().getStatus();
                    if (status == 1) {
                        //会议结束
                        videoConferenceEntity.setStatus(2);
                        videoConferenceMapper.updateByPrimaryKey(videoConferenceEntity);
                    }else if(status == 2){
                        //进行中
                        videoConferenceEntity.setStatus(1);
                        videoConferenceMapper.updateByPrimaryKey(videoConferenceEntity);
                    }
                }
            }else if(videoConferenceEntity.getConferenceType() == 1){
                //预约会议
                QueryScheduleConferenceInfoResponse response = tzdVieoConferencesService.queryScheduleConferenceInfoWithOptions(videoConferenceEntity.getTzdConferenceId());
                if(response!=null && response.getBody() != null && response.getBody().getConferenceList() !=null && response.getBody().getConferenceList().size() > 0){
                    status = response.getBody().getConferenceList().get(0).getStatus();
                    if (status == 1) {
                        //进行中
                        videoConferenceEntity.setStatus(1);
                        videoConferenceMapper.updateByPrimaryKey(videoConferenceEntity);
                    }else if(status == 2){
                        //会议结束
                        videoConferenceEntity.setStatus(2);
                        videoConferenceMapper.updateByPrimaryKey(videoConferenceEntity);
                    }
                }
            }

        }

        return 1;
    }
}
