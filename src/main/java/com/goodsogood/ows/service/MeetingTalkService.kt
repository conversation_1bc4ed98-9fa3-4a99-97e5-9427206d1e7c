package com.goodsogood.ows.service

import com.fasterxml.jackson.core.type.TypeReference
import com.github.pagehelper.PageHelper
import com.goodsogood.ows.common.Constant
import com.goodsogood.ows.common.FileSourceEnum
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.configuration.TogServicesConfig
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.RemoteApiHelper
import com.goodsogood.ows.mapper.CountMeetingTalkMapper
import com.goodsogood.ows.mapper.MeetingTalkContentMapper
import com.goodsogood.ows.mapper.MeetingTalkLinkMapper
import com.goodsogood.ows.mapper.MeetingTalkMapper
import com.goodsogood.ows.model.db.*
import com.goodsogood.ows.model.vo.*
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.utils.DateUtils
import com.goodsogood.ows.utils.FileUtil
import com.goodsogood.ows.utils.word.SoMap
import com.goodsogood.ows.utils.word.WordUtil
import org.bouncycastle.asn1.x500.style.RFC4519Style.o
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.client.RestTemplate
import tk.mybatis.mapper.entity.Example
import java.io.FileOutputStream
import java.io.IOException
import java.nio.file.Paths
import java.time.LocalDateTime
import java.util.*

@Service
class MeetingTalkService(
    @Autowired val errors: Errors,
    @Autowired val restTemplate: RestTemplate,
    @Autowired val thirdService: ThirdService,
    @Autowired val meetingTalkMapper: MeetingTalkMapper,
    @Autowired val meetingFileService: MeetingFileService,
    @Autowired val meetingTalkLinkMapper: MeetingTalkLinkMapper,
    @Autowired val meetingTalkContentMapper: MeetingTalkContentMapper,
    @Autowired val togServicesConfig: TogServicesConfig,
    @Autowired val lifeFileService: LifeFileService,
    @Autowired val restTemplateService: RestTemplateService,
    @Autowired val orgLifeFileService: OrgLifeFileService,
    @Autowired val countMeetingTalkMapper: CountMeetingTalkMapper,
    @Autowired val openService: OpenService,
) {

    private val log = LoggerFactory.getLogger(MeetingTalkService::class.java)

    @Value("\${temp-path}")
    val tmpPath: String = "/home/<USER>/appdata/tmp/ows-meeting"

    private val format: String = "yyyy-MM-dd HH:mm"

    /**
     * 新增和更新谈心谈话
     */
    @Transactional
    fun editMeetingTalk(form: MeetingTalkForm, headers: HttpHeaders): String {
        log.debug("talk edit -> {}", form)
        val header = HeaderHelper.buildMyHeader(headers)
        // 判断时间
        if (form.beginTime?.isAfter(form.endTime) == true) {
            throw ApiException(
                "谈话时间开始时间大于结束时间",
                Result<Any>(errors, 1920, HttpStatus.OK.value())
            )
        }
        // 如果source != 0，sourceId 必须不能为空
        if ((form.source != 0) && (form.sourceId == null)) {
            throw ApiException(
                "第三方来源ID不能为空",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "第三方来源ID不能为空")
            )
        }
        // 查询组织信息
        val orgInfo = this.thirdService.findOrgInfoByOrgId(form.orgId)
        if (orgInfo == null || orgInfo.name == null) {
            throw ApiException(
                "查询组织失败",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "查询组织失败")
            )
        }
        // 判断谈心谈话是否已创建
        var talkId = form.talkId
        var meetingTalkEntity = MeetingTalkEntity()
        if (talkId == null) {
            // 新增谈心谈话主表
            meetingTalkEntity.orgId = form.orgId
            meetingTalkEntity.orgName = orgInfo.name
            meetingTalkEntity.orgLevel = orgInfo.orgLevel
            meetingTalkEntity.talkType = form.talkType
            meetingTalkEntity.location = form.location
            meetingTalkEntity.beginTime = form.beginTime
            meetingTalkEntity.endTime = form.endTime
            meetingTalkEntity.source = form.source
            meetingTalkEntity.sourceId = form.sourceId
            meetingTalkEntity.status = Constant.YES
            meetingTalkEntity.isSubmit = form.isSubmit
            meetingTalkEntity.regionId = header.regionId
            meetingTalkEntity.createTime = Date()
            meetingTalkEntity.updateTime = Date()
            meetingTalkEntity.createUser = header.userId
            val insert = this.meetingTalkMapper.insert(meetingTalkEntity)
            if (insert > 0) {
                talkId = meetingTalkEntity.talkId
            } else {
                throw ApiException(
                    "保存谈心谈话失败",
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), "保存谈心谈话失败")
                )
            }
        } else {
            val talkEntity = this.meetingTalkMapper.selectByPrimaryKey(talkId)
            if (talkEntity != null) {
                meetingTalkEntity = talkEntity
                if (form.source !in listOf(1, 2, 3, 4)) {
                    meetingTalkEntity.talkType = form.talkType
                }
                meetingTalkEntity.location = form.location
                meetingTalkEntity.beginTime = form.beginTime
                meetingTalkEntity.endTime = form.endTime
                meetingTalkEntity.isSubmit = 1
                meetingTalkEntity.updateTime = Date()
                meetingTalkEntity.lastChangeUser = header.userId
                this.meetingTalkMapper.updateByPrimaryKey(meetingTalkEntity)
            } else {
                throw ApiException(
                    "更新失败，ID出现错误",
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), "更新失败，ID出现错误")
                )
            }
        }

        // 新增谈心谈话用户表
        val linkList = mutableListOf<MeetingTalkLinkEntity>()
        // 谈话人
        form.talkUser.forEach {
            val linkEntity = MeetingTalkLinkEntity(
                talkId, it.userId, it.username, Constant.TALK_USER,
                Constant.YES, Date(), header.userId
            )
            linkList.add(linkEntity)
        }
        // 被谈话人
        form.toTalkUser.forEach {
            val linkEntity = MeetingTalkLinkEntity(
                talkId, it.userId, it.username, Constant.TO_TALK_USER,
                Constant.YES, Date(), header.userId
            )
            linkList.add(linkEntity)
        }
        // 入库
        if (linkList.isNotEmpty()) {
            val ex = Example(MeetingTalkLinkEntity::class.java)
            ex.createCriteria().andEqualTo("talkId", talkId)
            this.meetingTalkLinkMapper.deleteByExample(ex)
            this.meetingTalkLinkMapper.insertList(linkList)
        }

        // 新增谈心谈话内容
        val contentList = mutableListOf<MeetingTalkContentEntity>()
        form.talkContent.forEach {
            val contentEntity = MeetingTalkContentEntity(
                talkId, it.title, it.content, Constant.YES,
                Date(), header.userId
            )
            contentList.add(contentEntity)
        }
        // 入库
        if (contentList.isNotEmpty()) {
            val ex = Example(MeetingTalkContentEntity::class.java)
            ex.createCriteria().andEqualTo("talkId", talkId)
            this.meetingTalkContentMapper.deleteByExample(ex)
            this.meetingTalkContentMapper.insertList(contentList)
        }
        // 新增上传附件
        val fileList = this.meetingFileService.selectByLinkedId(talkId, FileSourceEnum.MEETING_FILE_TALK)
        this.meetingFileService.delete(talkId, FileSourceEnum.MEETING_FILE_TALK)
        this.meetingFileService.addFile(talkId, form.files, FileSourceEnum.MEETING_FILE_TALK)

        // 生成模板
        // 生成文件上传
        if (meetingTalkEntity.isSubmit == 1) {
            val resultForm = this.createDocData(this.createTalkFile(form), headers)
            val fileForm = MeetingFileForm(
                resultForm.id,
                FileSourceEnum.MEETING_TALK.source,
                resultForm.name,
                resultForm.path,
                resultForm.fileName,
                resultForm.size
            )
            this.meetingFileService.delete(talkId, FileSourceEnum.MEETING_TALK)
            this.meetingFileService.addFile(talkId, mutableListOf(fileForm), FileSourceEnum.MEETING_TALK)
            this.talkCreateAfter(form, talkId, resultForm, fileList, meetingTalkEntity, header)
        }
        return "success"
    }

    /**
     * 逻辑删除谈心谈话
     */
    @Transactional
    fun delMeetingTalk(talkId: Long, header: HeaderHelper.SysHeader): String {
        val talk = this.meetingTalkMapper.selectByPrimaryKey(talkId)
        if (talk != null)
            this.deleteTalk(talk, header.userId)
        return "success"
    }

    /**
     * 批量逻辑删除谈心谈话
     */
    @Transactional
    fun batchDelMeetingTalk(talkIds: MutableList<Long>, header: HeaderHelper.SysHeader): String {
        val example = Example(MeetingTalkEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andIn("talkId", talkIds)
        val talkList = this.meetingTalkMapper.selectByExample(example)
        talkList.forEach { talk ->
            if (talk != null)
                this.deleteTalk(talk, header.userId)
        }
        return "success"
    }

    @Transactional
    fun deleteTalk(talk: MeetingTalkEntity, changeUserId: Long) {
        log.debug("tail del -> talk: {}", talk)
        talk.status = Constant.NO
        talk.updateTime = Date()
        talk.lastChangeUser = changeUserId
        this.meetingTalkMapper.updateByPrimaryKey(talk)
        // 删除附件
        this.meetingFileService.delete(talk.talkId, FileSourceEnum.MEETING_TALK)
        // talk_link
        val talkLinkList = this.meetingTalkLinkMapper.getMeetingTalkLink(talk.talkId)
        talkLinkList.forEach {
            it.status = Constant.NO
            it.updateTime = Date()
            it.lastChangeUser = changeUserId
            this.meetingTalkLinkMapper.updateByPrimaryKeySelective(it)
        }
        // talk_content
        val talkContentList = this.meetingTalkContentMapper.getMeetingTalkContent(talk.talkId)
        talkContentList.forEach {
            it.status = Constant.NO
            it.updateTime = Date()
            it.lastChangeUser = changeUserId
            this.meetingTalkContentMapper.updateByPrimaryKeySelective(it)
        }
        this.delMeetingLife(talk = talk, lastChangeUserId = changeUserId)
    }

    /**
     * @title 根据第三方数据删除谈心谈话
     * @description 组织生活会和民主生活会删除时，需要调用该方法删除相应的谈心谈话
     * <AUTHOR>
     * @param source    类型  1 - 民主生活会  2 - 组织生活会
     * @param sourceId  第三方ID
     * @param header    build过后的请求头
     * @updateTime 2022/3/30 5:03 PM
     * @return
     */
    @Transactional
    fun delBySource(source: Int? = null, sourceId: Long? = null, header: HeaderHelper.SysHeader) {
        log.debug("谈心谈话批量删除 source：$source sourceId：$sourceId, 操作人：${header.userId}")
        if (source != null && sourceId != null) {
            var sourceList = mutableListOf<Int>()
            if (source == 1) {
                sourceList = mutableListOf(1, 2)
            } else if (source == 2) {
                sourceList = mutableListOf(3, 4)
            }
            if (sourceList.isNotEmpty()) {
                val example = Example(MeetingTalkEntity::class.java)
                val criteria = example.createCriteria()
                criteria.andIn("source", sourceList)
                criteria.andEqualTo("sourceId", sourceId)
                val talkList = meetingTalkMapper.selectByExample(example)
                talkList.forEach { talk ->
                    if (talk != null)
                        this.deleteTalk(talk, header.userId)
                }
            }
        }
    }

    /**
     * 根据主键查询谈心谈话
     */
    fun queryMeetingTalk(talkId: Long): MeetingTalkForm {
        val talkForm = MeetingTalkForm()
        val talkEntity = this.meetingTalkMapper.selectByPrimaryKey(talkId)
        if (talkEntity != null) {
            // talk_base_info
            talkForm.talkId = talkEntity.talkId
            talkForm.talkType = talkEntity.talkType
            talkForm.beginTime = talkEntity.beginTime
            talkForm.endTime = talkEntity.endTime
            talkForm.location = talkEntity.location
            talkForm.source = talkEntity.source
            talkForm.isSubmit = talkEntity.isSubmit!!
            talkForm.sourceId = talkEntity.sourceId
            // talk_link
            val talkLink = this.meetingTalkLinkMapper.getMeetingTalkLink(talkId)
            val talkUserList = mutableListOf<TalkUser>()
            val toTalkUserList = mutableListOf<TalkUser>()
            talkLink.forEach { link ->
                if (link.type == 1) {
                    // 谈话人
                    talkUserList.add(TalkUser(link.userId, link.username))
                } else {
                    toTalkUserList.add(TalkUser(link.userId, link.username))
                }
            }
            talkForm.talkUser = talkUserList
            talkForm.toTalkUser = toTalkUserList
            // talk_content
            val talkContentList = this.meetingTalkContentMapper.getMeetingTalkContent(talkId)
            val contentList = mutableListOf<TalkContent>()
            talkContentList.forEach { content ->
                contentList.add(TalkContent(content.title, content.content))
            }
            talkForm.talkContent = contentList
            // talk_file
            val fileFormList = mutableListOf<MeetingFileForm>()
            val fileList = this.meetingFileService.selectByLinkedId(talkId, FileSourceEnum.MEETING_FILE_TALK)
            if (!fileList.isNullOrEmpty()) {
                fileList.forEach { file ->
                    val form = MeetingFileForm()
                    form.fileId = file.fileId
                    form.name = file.name
                    form.path = file.path
                    form.fileName = file.fileName
                    form.size = file.size
                    fileFormList.add(form)
                }
            }
            talkForm.files = fileFormList
        }
        log.debug("结果 -> {}", talkForm)
        return talkForm
    }

    /**
     * 根据谈心谈话类型获取
     */
    fun queryList(
        orgId: Long, talkType: Int, source: Int, sourceId: Long?,
        talkUser: String?, toTalkUser: String?, startTime: LocalDateTime?, endTime: LocalDateTime?,
        showNullTime: Int,
    ): MutableList<MeetingTalkVO> {
        val example = Example(MeetingTalkEntity::class.java)
        val criteria = example.createCriteria()
        if (talkType != -1) {
            criteria.andEqualTo("talkType", talkType)
        }
        if (orgId != 3L) {
            criteria.andEqualTo("orgId", orgId)
        }
        criteria.andEqualTo("status", Constant.YES)
        if (source != -1) {
            criteria.andEqualTo("source", source)
        }
        if (sourceId != -1L) {
            criteria.andEqualTo("sourceId", sourceId)
        }
        if (startTime != null && showNullTime == 0) {
            criteria.andGreaterThanOrEqualTo("beginTime", startTime)
        }
        if (endTime != null && showNullTime == 0) {
            criteria.andLessThanOrEqualTo("endTime", endTime)
        }
        // 按照beginTime逆排序
        example.orderBy("updateTime").desc()
        val list = this.meetingTalkMapper.selectByExample(example)
        val meetingTalkList = mutableListOf<MeetingTalkVO>()
        list.forEach { talk ->
            if (talk != null) {
                val meetingTalkVO = MeetingTalkVO()
                meetingTalkVO.talkId = talk.talkId
                meetingTalkVO.talkType = talk.talkType
                val linkExample = Example(MeetingTalkLinkEntity::class.java)
                linkExample.createCriteria().andEqualTo("talkId", talk.talkId)
                    .andEqualTo("status", Constant.YES)
                val linkUsers = this.meetingTalkLinkMapper.selectByExample(linkExample)
                // 谈话人列表
                val talkUserList = linkUsers.filter { user ->
                    user?.type?.equals(1) == true
                }.joinToString(separator = ",") { it?.username!! }
                // 被谈话人列表
                val toTalkUserList = linkUsers.filter { user ->
                    user?.type?.equals(2) == true
                }.joinToString(separator = ",") { it?.username!! }
                meetingTalkVO.talkUser = talkUserList
                meetingTalkVO.toTalkUser = toTalkUserList
                val beginTimeStr = DateUtils.localDateTimeToString(talk.beginTime, "yyyy-MM-dd HH:mm")
                val endTimeStr = DateUtils.localDateTimeToString(talk.endTime, "yyyy-MM-dd HH:mm")
                if (talk.beginTime != null && talk.endTime != null) {
                    meetingTalkVO.talkTime = beginTimeStr + "至" + endTimeStr
                }
                meetingTalkVO.isSubmit = talk.isSubmit
                meetingTalkVO.sourceId = talk.sourceId
                meetingTalkVO.source = talk.source
                var flagTalker: Boolean = true
                var flagToTalker: Boolean = true
                if (talkUser != null && talkUser != "" && !(meetingTalkVO.talkUser!!.contains(talkUser))) {
                    flagTalker = false
                }
                if (toTalkUser != null && toTalkUser != "" && !(meetingTalkVO.toTalkUser!!.contains(toTalkUser))) {
                    flagToTalker = false
                }
                if (flagToTalker && flagTalker) {
                    meetingTalkList.add(meetingTalkVO)
                }
            }
        }
        return meetingTalkList
    }

    /**
     * 创建模板文件
     */
    private fun createTalkFile(form: MeetingTalkForm): MeetingTalkDocFileForm {
        val meetingTalkDocFileForm = MeetingTalkDocFileForm()
        meetingTalkDocFileForm.title = form.talkUser[0].username + "和" + form.toTalkUser[0].username
        meetingTalkDocFileForm.type = form.talkType.let { TalkTypeEnum.getTalkTypeEnum(it)?.value }
        meetingTalkDocFileForm.username = form.talkUser.joinToString(separator = "、") { it.username!! }
        meetingTalkDocFileForm.toUsername = form.toTalkUser.joinToString(separator = "、") { it.username!! }
        meetingTalkDocFileForm.beginTime = DateUtils.localDateTimeToString(form.beginTime, format)
        meetingTalkDocFileForm.endTime = DateUtils.localDateTimeToString(form.endTime, format)
        meetingTalkDocFileForm.location = form.location
        val list = mutableListOf<TalkDocContent>()
        form.talkContent.forEach {
            list.add(TalkDocContent(it.title ?: "", it.content ?: ""))
        }
        if (list.isEmpty()) {
            list.add(TalkDocContent())
        }
        meetingTalkDocFileForm.list = list
        log.debug("生成模板文件 -> [${meetingTalkDocFileForm}]")
        return meetingTalkDocFileForm
    }

    /**
     * 根据模板文件建立临时文件，并上传文件中心，删除临时文件
     */
    private fun createDocData(form: MeetingTalkDocFileForm, headers: HttpHeaders): UploadFileResultForm {
        log.debug("根据模板文件生成文件开始！")
        val evaluation = WordUtil.createExportData("file/talk_template.docx") //模板
        val list = mutableListOf<SoMap>()
        if (form.list.isNotEmpty()) {
            form.list.forEach {
                list.add(SoMap(it))
            }
            evaluation.setData("talk", form)
            evaluation.setTable("users", list)
            val data = evaluation.byteArr

            val name =
                form.type + "-" + form.username?.split("，")?.get(0) + "-" + form.toUsername?.split("，")?.get(0) + ".doc"

            // 可以直接写入本地的文件
            val fileName = Paths.get(tmpPath, name).toString()
            try {
                FileOutputStream(fileName).use { fos -> fos.write(data, 0, data.size) }
            } catch (ex: IOException) {
                log.error("生成文件报错", ex)
            }
            log.debug("根据模板文件生成文件成功 -> [${fileName}]")
            // 上传文件
            val resultForm =
                FileUtil.sendFileCenter(fileName, name, "file", errors, headers, restTemplate, togServicesConfig)
            if (resultForm != null) {
                log.debug("文件上传成功")
            } else {
                throw ApiException(
                    "文件上传失败",
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), "文件上传文件服务器失败")
                )
            }
            // 上传成功后删除本地文件
            FileUtil.deleteFile(fileName)
            return resultForm
        } else {
            return UploadFileResultForm()
        }
    }

    /**
     * 自动创建谈心谈话
     * @param   orgId       组织ID
     * @param   source      1 - 民主生活会会前，2 - 民主生活会会后
     * @param   sourceId    民主生活会ID
     * @param   headers     请求头
     */
    @Transactional
    fun generaTalkData(orgId: Long, source: Int, sourceId: Long, headers: HttpHeaders) {
        log.debug("自动创建谈心谈话 -> orgId:[${orgId}] source:[${source}] sourceId:[${sourceId}]")
        val header = HeaderHelper.buildMyHeader(headers)
        if (source == 1) {
            // 民主生活会会前
            val userList = this.getPartyGroupUser(orgId, headers)
            userList.forEach { user1 ->
                userList.forEach { user2 ->
                    if (user1.userId != user2.userId) {
                        val form = MeetingTalkForm()
                        form.orgId = orgId
                        form.source = source
                        form.sourceId = sourceId
                        form.talkUser = mutableListOf(user1)
                        form.toTalkUser = mutableListOf(user2)
                        form.talkType = TalkTypeEnum.LEADER.key
                        form.isSubmit = 0
                        this.editMeetingTalk(form, headers)
                    }
                }
            }
        } else if (source == 3) {
            val userForms = this.restTemplateService.getSessionUser(header)
            if (userForms != null && userForms.isNotEmpty()) {
                val userList = userForms.map { TalkUser(it.userId, it.userName) }
                // 书记和班子成员之间
                val secretary =
                    userForms.filter { it.positionName.contains("书记") && !it.positionName.contains("副书记") }
                if (secretary.isNotEmpty()) {
                    val secretaryList = secretary.map { TalkUser(it.userId, it.userName) }
                    secretaryList.forEach { user1 ->
                        userList.forEach { user2 ->
                            if (user1.userId != user2.userId) {
                                val form = MeetingTalkForm()
                                form.orgId = orgId
                                form.source = source
                                form.sourceId = sourceId
                                form.talkUser = mutableListOf(user1)
                                form.toTalkUser = mutableListOf(user2)
                                form.talkType = TalkTypeEnum.LEADER_TEAM.key
                                form.isSubmit = 0
                                this.editMeetingTalk(form, headers)
                            }
                        }
                    }
                }
                // 班子成员之间
                userList.forEach { user1 ->
                    userList.forEach { user2 ->
                        if (user1.userId != user2.userId) {
                            val form = MeetingTalkForm()
                            form.orgId = orgId
                            form.source = source
                            form.sourceId = sourceId
                            form.talkUser = mutableListOf(user1)
                            form.toTalkUser = mutableListOf(user2)
                            form.talkType = TalkTypeEnum.TEAM.key
                            form.isSubmit = 0
                            this.editMeetingTalk(form, headers)
                        }
                    }
                }
            }
        } else if (mutableListOf(2, 4).contains(source)) {
            // 民主生活会会后 需要复制会前的谈心谈话记录
            val exTalk = Example(MeetingTalkEntity::class.java)
            val criteriaTalk = exTalk.createCriteria()
            criteriaTalk.andEqualTo("source", (source - 1))
                .andEqualTo("sourceId", sourceId)
                .andEqualTo("status", Constant.YES)
                .andEqualTo("isSubmit", Constant.YES)
            val talkList = this.meetingTalkMapper.selectByExample(exTalk)
            // query talk list
            talkList.forEach { talk ->
                if (talk != null) {
                    // copy meeting_talk
                    val talkId = talk.talkId
                    log.debug("复制谈心谈话 -> 开始 talkId -> [${talkId}]")
                    talk.talkId = null
                    talk.copyId = talkId
                    talk.source = source
                    talk.createTime = Date()
                    talk.createUser = header.userId
                    this.meetingTalkMapper.insert(talk)
                    log.debug("复制谈心谈话主表完成 -> new talkId [${talk.talkId}]")
                    // copy meeting_talk_link
                    val linkList = this.meetingTalkLinkMapper.getMeetingTalkLink(talkId)
                    linkList.forEach { link ->
                        link.talkLinkId = null
                        link.talkId = talk.talkId
                        link.createTime = Date()
                        link.createUser = header.userId
                        this.meetingTalkLinkMapper.insert(link)
                    }
                    log.debug("复制谈心谈话内容表完成 -> 新增条数 [${linkList.size}]")
                    // copy meeting_talk_content
                    val contentList = this.meetingTalkContentMapper.getMeetingTalkContent(talkId)
                    contentList.forEach { content ->
                        content.talkContentId = null
                        content.talkId = talk.talkId
                        content.createTime = Date()
                        content.createUser = header.userId
                        this.meetingTalkContentMapper.insert(content)
                    }
                    log.debug("复制谈心谈话人员表完成 -> 新增条数 [${contentList.size}]")
                    // copy 谈心谈话模板和附件
                    this.copyTalkFile(talk, talkId, headers)
                }
            }
        }
    }

    /**
     * 民主生活会 党组成员与人谈话
     * @param   form        请求类
     * @param   headers     请求头
     */
    @Transactional
    fun generaLeaderTalk(form: GeneraLeaderAndUserTalkForm, headers: HttpHeaders): String {
        log.debug("民主生活会 党组成员与人谈话 -> form:[${form}]")
        // 查询党组成员
        form.orgId?.let { getPartyGroupUser(it, headers) }?.forEach { groupUser ->
            form.userList.forEach { user ->
                if (groupUser.userId != user.userId) {
                    val talkForm = MeetingTalkForm()
                    talkForm.orgId = form.orgId
                    talkForm.source = form.source
                    talkForm.sourceId = form.sourceId
                    talkForm.talkUser = mutableListOf(user)
                    talkForm.toTalkUser = mutableListOf(groupUser)
                    talkForm.talkType = form.talkType
                    talkForm.isSubmit = 0
                    this.editMeetingTalk(talkForm, headers)
                }
            }
        }
        return "success"
    }

    /**
     * copy 谈心谈话模板和附件
     */
    private fun copyTalkFile(meetingTalk: MeetingTalkEntity, oldTalkId: Long?, headers: HttpHeaders) {
        val header = HeaderHelper.buildMyHeader(headers)
        // copy 谈心谈话模板和附件
        val copyList = mutableListOf<MeetingFileForm>()
        val attach =
            this.meetingFileService.selectByLinkedId(oldTalkId, FileSourceEnum.MEETING_FILE_TALK)
        var attachList = mutableListOf<MeetingFileForm>()
        if (!attach.isNullOrEmpty()) {
            attachList = attach.map {
                MeetingFileForm(
                    it.fileId,
                    FileSourceEnum.MEETING_FILE_TALK.source,
                    it.name,
                    it.path,
                    it.fileName,
                    it.size
                )
            }.toMutableList()
            copyList.addAll(attachList)
        }
        val template =
            this.meetingFileService.selectByLinkedId(oldTalkId, FileSourceEnum.MEETING_TALK)
        var templateList = mutableListOf<MeetingFileForm>()
        if (!template.isNullOrEmpty()) {
            templateList = template.map {
                MeetingFileForm(
                    it.fileId,
                    FileSourceEnum.MEETING_TALK.source,
                    it.name,
                    it.path,
                    it.fileName,
                    it.size
                )
            }.toMutableList()
            copyList.addAll(templateList)
        }
        this.meetingFileService.addFile(meetingTalk.talkId, copyList, null)
        log.debug("复制谈心谈话附件表完成 -> 新增条数 [${copyList.size}]")
        // copy 民主生活会 谈心谈话 附件 和模板
        val form = MeetingTalkForm(
            talkId = meetingTalk.talkId, source = meetingTalk.source, sourceId = meetingTalk.sourceId,
            talkType = meetingTalk.talkType, files = attachList
        )
        var fileResultForm = UploadFileResultForm()
        if (templateList.isNotEmpty()) {
            fileResultForm = UploadFileResultForm(
                id = templateList[0].fileId,
                name = templateList[0].name,
                fileName = templateList[0].fileName,
                path = templateList[0].path
            )
            this.sendMeetingLife(form, meetingTalk.talkId, 2, fileResultForm, header)
        }
        this.sendMeetingLife(form, meetingTalk.talkId, 1, fileResultForm, header)
    }

    /**
     * 调用用户中心获取党组成员列表
     * @param orgId     发起民主生活会组织ID
     * @param headers   请求头
     */
    private fun getPartyGroupUser(orgId: Long, headers: HttpHeaders): List<TalkUser> {
        val userList: MutableList<PartyGroupUserVO>
        val url = "http://${togServicesConfig.userCenter}/party-group/find-user-by-base-org?org_id=$orgId"
        try {
            userList =
                RemoteApiHelper.get(
                    restTemplate,
                    url,
                    headers,
                    object : TypeReference<Result<MutableList<PartyGroupUserVO>?>?>() {})!!
            log.debug("调用用户中心获取党组成员列表 -> [$userList]")
        } catch (e: Exception) {
            log.error("调用用户中心获取党组成员列表失败", e)
            throw ApiException(
                "调用用户中心获取党组成员列表失败",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "请联系管理员, 获取党组成员列表出错")
            )
        }
        return userList.map { TalkUser(it.userId, it.userName) }.toMutableList().distinctBy { it.userId }
    }

    /**
     * 同步修改组织信息
     * <AUTHOR>
     * @date 2020/1/8
     * @param organizationBase
     * @return int
     */
    fun updateOrgInfo(organizationBase: OrganizationBase): Int {
        // 封装组织数据更新信息
        val example = Example(MeetingTalkEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andEqualTo("orgId", organizationBase.organizationId)
        val list = meetingTalkMapper.selectByExample(example)
        list.forEach {
            if (it != null) {
                it.orgName = organizationBase.name
                it.orgLevel = organizationBase.orgLevel
                it.updateTime = Date()
                it.lastChangeUser = -111L
                this.meetingTalkMapper.updateByPrimaryKey(it)
            }
        }
        return list.size
    }

    /**
     *处理完谈心谈话，后续操作
     */
    fun talkCreateAfter(
        form: MeetingTalkForm, talkId: Long?, resultForm: UploadFileResultForm, files: MutableList<MeetingFileVO>?,
        meetingTalkEntity: MeetingTalkEntity, header: HeaderHelper.SysHeader,
    ) {
        var fileList = files
        // source 为1，2时，分别为民主生活会会前和会后，需要回调民主生活会方法，进行处理
        // source 为3，4时，分别为组织生活会会前和会后，需要回调组织生活会方法，进行处理
        if (mutableListOf(1, 2, 3, 4).contains(form.source)) {
            // 发送到民主生活会
            if (talkId != null) {
                this.sendMeetingLife(form, talkId, 1, resultForm, header)
            }
            // 发送附件到民主生活会
            // 对于旧文件和新文件，新文件中不存在旧文件，则删除旧文件
            val lifeFileList = mutableListOf<LifeFileForm>()
            if (fileList == null) fileList = mutableListOf()
            fileList.forEach { oldFile ->
                val filter = form.files.filter { newFile ->
                    newFile.fileId == oldFile.fileId
                }
                if (filter.isEmpty()) {
                    lifeFileList.add(LifeFileForm(null, oldFile.fileName, oldFile.path))
                }
            }
            if (lifeFileList.isNotEmpty()) {
                this.delMeetingLife(
                    talk = meetingTalkEntity,
                    fileList = lifeFileList,
                    lastChangeUserId = header.userId,
                    flag = 2
                )
            }
            // 对于旧文件和新文件，旧文件中不存在新文件，则新增文件
            var flag = false
            form.files.forEach { newFile ->
                val filter = fileList.filter { oldFile ->
                    newFile.fileId == oldFile.fileId
                }
                if (filter.isEmpty()) {
                    flag = true
                }
            }
            if (flag) {
                this.sendMeetingLife(form = form, talkId = talkId, flag = 2, header = header)
            }
        }
    }

    /**
     * 发送文件到民主生活会
     * @param form 谈心谈话表单
     * @param talkId 谈心谈话ID
     * @param flag 1 - 模板生成文件，2 - 附件
     * @param fileForm 模板文件详情
     * @param header 请求头
     */
    @Transactional
    fun sendMeetingLife(
        form: MeetingTalkForm? = null,
        talkId: Long? = null,
        flag: Int,
        fileForm: UploadFileResultForm = UploadFileResultForm(),
        header: HeaderHelper.SysHeader? = null,
    ) {
        if (form != null && mutableListOf(1, 2, 3, 4).contains(form.source)) {
            val attachForm = SaveAttachForm()
            attachForm.lifeId = form.sourceId
            attachForm.dataId = talkId
            // 组织生活会的类型转换3 -> 1, 4 -> 2
            attachForm.step = if (form.source == 3) 1 else if (form.source == 4) 2 else form.source
            attachForm.isDirect = 1
            form.talkType.let { type ->
                attachForm.type =
                    if (flag == 1)
                        type.let { TalkTypeEnum.getTalkTypeEnum(it)?.template }
                    else
                        type.let { TalkTypeEnum.getTalkTypeEnum(it)?.attach }
            }
            attachForm.userId = header?.userId
            attachForm.username = header?.userName
            val files = mutableListOf<LifeFileForm>()
            if (flag == 1) {
                if (fileForm.fileName != null) {
                    val lifeFileForm = LifeFileForm(fileForm.id, fileForm.fileName, fileForm.path)
                    files.add(lifeFileForm)
                }
            } else {
                form.files.forEach { file ->
                    val lifeFileForm = LifeFileForm(file.fileId, file.fileName, file.path)
                    files.add(lifeFileForm)
                }
            }
            attachForm.lifeFile = files
            log.debug("调用第三方保存文件，参数 -> [$attachForm]")
            if (mutableListOf(1, 2).contains(form.source)) {
                val attach = this.lifeFileService.saveAttach(attachForm, header)
                log.debug("调用民主生活会保存文件，返回结果 -> [$attach]")
            } else if (mutableListOf(3, 4).contains(form.source)) {
                val attach = this.orgLifeFileService.saveAttach(attachForm, header)
                log.debug("调用组织生活会保存文件，返回结果 -> [$attach]")
            }
        }
    }

    /**
     * 调用民主生活会删除文件
     * @param talk      民主生活会实体类
     * @param lastChangeUserId  修改人
     * @param flag      1 - 模板生成文件，2 - 附件
     */
    @Transactional
    fun delMeetingLife(
        talk: MeetingTalkEntity? = null,
        fileList: MutableList<LifeFileForm> = mutableListOf(),
        lastChangeUserId: Long = 0,
        flag: Int? = 1,
    ) {
        if (talk != null && mutableListOf(1, 2, 3, 4).contains(talk.source)) {
            val attachForm = SaveAttachForm()
            attachForm.lifeId = talk.sourceId
            attachForm.dataId = talk.talkId
            attachForm.step = talk.source
            attachForm.isDirect = 1
            attachForm.userId = lastChangeUserId
            attachForm.lifeFile = fileList
            talk.talkType.let { type ->
                when (flag) {
                    1 -> {
                        attachForm.type = TalkTypeEnum.getTalkTypeEnum(type)?.template
                    }

                    2 -> {
                        attachForm.type = TalkTypeEnum.getTalkTypeEnum(type)?.attach
                    }
                }
            }
            if (mutableListOf(1, 2).contains(talk.source)) {
                this.lifeFileService.deleteTalkFile(attachForm)
            } else if (mutableListOf(3, 4).contains(talk.source)) {
                this.orgLifeFileService.deleteTalkFile(attachForm)
            }
        }
    }

    /***
     * 通过单位名称进行谈心谈话模糊查询统计
     */
    fun countMeetingTalkByCondition(
        unitName: String?, headers: HttpHeaders, unitId: Long,
    ): Collection<CountMeetingTalkVo> {
        val sysHeader = HeaderHelper.buildMyHeader(headers)
        //获取组织id
        val orgId = sysHeader.uoid ?: sysHeader.oid
        if (orgId == 3L) {
            //根据数据库查询本组织及下级组织的信息,并用List封装CountMeetingTalkEntity返回
            val listEntity = countMeetingTalkMapper.countMeetingTalkByCondition(orgId)
            listEntity.forEach {
                for (i in 0..18) {
                    it.typeAndNumber!!["type$i"] = it.orgId?.let { it1 -> countMeetingTalkMapper.countType(it1, i) }!!
                }
            }
            val map = mutableMapOf<String, CountMeetingTalkVo>()
            //将所有本组织及下级组织的组织id集成到orgIdList中
            val orgIdList =
                mutableListOf<Long>().also { it.addAll(listEntity.map { list -> list.orgId ?: -1 }.toList()) }
            //调用远程服务进行批量查找orgIdList
            val orgIdAndOBaseMap = openService.findOrgByIds(orgIdList, headers).associateBy { it.organizationId }
            //初始化Map用于分类相同单位的实体,并将相同单位的数据合并
            for (item in listEntity) {
                val organizationBase: OrganizationBase? = orgIdAndOBaseMap[item.orgId]
                val ownerName: String = organizationBase?.ownerName ?: "无"
                // 通过compute方法来合并相同单位的数据
                map.compute(ownerName) { _, existingItem ->
                    // 合并数据
                    existingItem?.apply {
                        for (i in 0..18) {
                            existingItem.typeAndNumber!!["type$i"] =
                                existingItem.typeAndNumber!!["type$i"]!! + item.typeAndNumber!!["type$i"]!!
                        }
                        peopleNum = peopleNum.plus(item.peopleNum)
                    } ?: item
                }
                map[ownerName]?.unitName = ownerName
                map[ownerName]?.unitId = organizationBase?.ownerId ?: -1L
            }
            return if (unitName == null || unitName == "") {
                map.values.also { v ->
                    v.removeIf { o -> o.unitName == "固守远望（重庆）信息技术有限公司" }
                }
            } else {
                map.filter { it.key.contains(unitName) }.values
            }
        } else {
            val result = CountMeetingTalkVo()
            //拉取远程服务:根据unitId查询unitName
            result.unitName = openService.findOrgById(unitId, headers).name ?: return listOf()
            //拉取远程服务:根据unitId查询相关单位信息
            val orgFrom = openService.getOrgByCorp(unitId, headers)
            //获取所有单位相关的ids
            val ids = orgFrom.branchOrg.plus(orgFrom.allOrg) ?: return listOf()
            val list = countMeetingTalkMapper.countMeetingTalkByIds(ids)
            //将所有单位相关的ids分别查询,然后统计各个id的各个类型的数量,并设置到各条数据的map中去,合并所有数据到result中的数据
            for (item in list) {
                for (i in 0..18) {
                    //合并result的类型数据
                    result.typeAndNumber?.let {
                        if (result.typeAndNumber!!.contains("type$i")) {
                            result.typeAndNumber!!["type$i"] =
                                result.typeAndNumber!!["type$i"]!! + item.orgId?.let { it1 ->
                                    countMeetingTalkMapper.countType(it1, i)
                                }!!
                        } else {
                            result.typeAndNumber!!["type$i"] =
                                item.orgId?.let { it1 -> countMeetingTalkMapper.countType(it1, i) }!!
                        }
                    }
                }
                result.peopleNum += item.peopleNum
            }
            return listOf(result)
        }
    }

}
