package com.goodsogood.ows.service;

import cn.hutool.core.convert.Convert;
import com.github.pagehelper.Page;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.UserCommentEntity;
import com.goodsogood.ows.model.vo.FindUserListByOrgForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.UserCommentQueryForm;
import com.goodsogood.ows.model.vo.UserInfoCommentVO;
import com.goodsogood.ows.utils.AsyncFileDownUtils;
import com.goodsogood.ows.utils.RateUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 导出民主评议服务层
 * @date 2020/1/7
 */
@Service
@Log4j2
public class UserCommentExportService {

    //创建临时文件存放的路径
    @Value("${temp-path}")
    @NotBlank
    private String tempPath;

    private static final int page_size = 5000;
    private static final String[] HEADERS = new String[]{"党员姓名", "证件号", "所在组织", "性别", "民族", "出生年月", "入党时间", "学历", "行政职务", "党内职务", "评议等级"};
    private Result<Page<UserInfoCommentVO>> result;
    private Page<UserInfoCommentVO> pageData = new Page<>();
    private List<Long> userIds = new ArrayList<>();
    private List<UserCommentEntity> userCommentList = new ArrayList<>();

    private final Errors errors;
    private final UserCommentService userCommentService;
    private final StringRedisTemplate redisTemplate;
    private final TogServicesConfig togServicesConfig;
    private final RestTemplate restTemplate;

    @Autowired
    public UserCommentExportService(Errors errors, UserCommentService userCommentService,
                                    StringRedisTemplate redisTemplate, TogServicesConfig togServicesConfig,
                                    RestTemplate restTemplate) {
        this.errors = errors;
        this.userCommentService = userCommentService;
        this.redisTemplate = redisTemplate;
        this.togServicesConfig = togServicesConfig;
        this.restTemplate = restTemplate;
    }

    @Async("asyncGenerateExcelExecutor")
    public void exportUserComment(UserCommentQueryForm queryForm, String uuid, String redisRepeatKey, HttpHeaders headers) {
        // 判断是否选择评议等级，
        // 如果选择了等级，则从评议列表中取出人员列表进行查询
        Integer commentLevel = queryForm.getCommentLevel();
        FindUserListByOrgForm form = new FindUserListByOrgForm();
        form.setOrgId(queryForm.getOrgId());
        form.setPageSize(page_size);
        List<List<String>> itemList = new ArrayList<>();
        // 表头
        itemList.add(Arrays.asList(HEADERS));
        if (null == commentLevel || commentLevel == 0) {
            form.setUserName(queryForm.getUserName());
            this.exportBatchUserInfoComment(form, itemList, uuid, queryForm.getYear(), 1, 1, headers);
        } else {
            this.exportBatchUserInfoComment2(form, itemList, uuid, queryForm.getOrgId(), queryForm.getYear(), commentLevel, queryForm.getUserName(), 1, 1, headers);
        }
        String tableName = "民主评议党员情况";
        AsyncFileDownUtils.asyncGenerateExcel(this.redisTemplate, this.restTemplate, this.togServicesConfig, errors, headers, itemList, tableName, tempPath, uuid, redisRepeatKey);
    }

    /**
     * 未选择评议等级, 导出
     * <AUTHOR>
     * @date 2020/1/7
     * @param form
     * @param year
     * @param page
     * @param index
     * @return void
     */
    private void exportBatchUserInfoComment(FindUserListByOrgForm form, List<List<String>> itemList, String uuid,
                                            int year, int page, int index, HttpHeaders header) {
        pageData.clear();
        // 设置页码
        form.setPage(page);
        result = this.userCommentService.getUserCommentInfoListByUserCenter(form, header);
        // 总页数
        Integer pages = result.getPages();
        // 当前页码
        Integer pageNum = result.getPageNum();
        if (pageNum.equals(1)) {
            RateUtils.build(Convert.toInt(result.getTotal()), uuid);
        }
        pageData = result.getData();
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(header);
        this.supplyUserCommentFormMeeting(year, sysHeader.getRegionId());
        if (CollectionUtils.isNotEmpty(pageData)) {
            this.createRow(pageData, itemList, uuid, index);
        }
        log.debug("当前在第[{}]页，总共有[{}]页", pageNum, pages);
        if(!pageNum.equals(pages + 1)) {
            page++;
            this.exportBatchUserInfoComment(form, itemList, uuid, year, page, index, header);
        }
    }

    private void exportBatchUserInfoComment2(FindUserListByOrgForm form, List<List<String>> itemList, String uuid,
                                             Long orgId, int year, int rating, String userName, int page, int index,
                                             HttpHeaders headers) {
        pageData.clear();
        pageData = this.userCommentService.selectUserListByLevel(orgId, year, rating, userName, page, page_size);
        // 总页数
        int pages = pageData.getPages();
        // 当前页码
        int pageNum = pageData.getPageNum();
        if (pageNum == 1) {
            RateUtils.build(Convert.toInt(pageData.getTotal()), uuid);
        }
        this.supplyUserCommentFormUser(form, headers);
        if (CollectionUtils.isNotEmpty(pageData)) {
            this.createRow(pageData, itemList, uuid, index);
        }
        if(pageNum != pages + 1) {
            page++;
            this.exportBatchUserInfoComment2(form, itemList, uuid, orgId, year, rating, userName, page, index, headers);
        }
    }

    /**
     * 补充数据
     * <AUTHOR>
     * @date 2020/1/7
     * @param year
     * @return void
     */
    private void supplyUserCommentFormMeeting(int year, Long regionId){
        userIds.clear();
        userCommentList.clear();
        userIds = pageData.stream().map(UserInfoCommentVO::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userIds)) {
            userCommentList = this.userCommentService.getUserListByUserId(year, userIds, regionId);
        }
        // 封装评级等级
        pageData.forEach(userInfo -> {
            userCommentList.forEach(userInfoComment -> {
                if (userInfo.getUserId().equals(userInfoComment.getUserId())) {
                    userInfo.setUserCommentId(userInfoComment.getUserCommentId());
                    userInfo.setCommentLevel(userInfoComment.getRating());
                }
            });
        });
    }

    private void supplyUserCommentFormUser(FindUserListByOrgForm form, HttpHeaders headers) {
        userIds.clear();
        // 封装用户信息
        userIds = pageData.stream().map(UserInfoCommentVO::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userIds)) {
            form.setUserIds(userIds);
            Result<Page<UserInfoCommentVO>> resultData = this.userCommentService.getUserCommentInfoListByUserCenter(form, headers);
            pageData.forEach(userCommentInfo -> {
                resultData.getData().forEach(userInfo -> {
                    if (userCommentInfo.getUserId().equals(userInfo.getUserId())) {
                        userCommentInfo.setCertNumber(userInfo.getCertNumber());
                        userCommentInfo.setOrgId(userInfo.getOrgId());
                        userCommentInfo.setOrgName(userInfo.getOrgName());
                        userCommentInfo.setSex(userInfo.getSex());
                        userCommentInfo.setEthnic(userInfo.getEthnic());
                        userCommentInfo.setBirthday(userInfo.getBirthday());
                        userCommentInfo.setJoiningTime(userInfo.getJoiningTime());
                        userCommentInfo.setEducation(userInfo.getEducation());
                        userCommentInfo.setGradeName(userInfo.getGradeName());
                        userCommentInfo.setPositionName(userInfo.getPositionName());
                    }
                });
            });
        }
    }

    /**
     *
     * <AUTHOR>
     * @date 2020/1/7
     * @param list 数据列表
     * @param rowIndex
     * @return List<String>
     */
    private void createRow(List<UserInfoCommentVO> list, List<List<String>> itemList, String uuid, int rowIndex){
        for (int i = 0; i < list.size(); i++) {
            List<String> row = new ArrayList<>();
            UserInfoCommentVO comment = list.get(i);
            rowIndex++;
            //创建单元格，并填充数据
            row.add(comment.getUserName());
            row.add(comment.getCertNumber());
            row.add(comment.getOrgName());
            row.add(comment.getSex());
            row.add(comment.getEthnic());
            row.add(comment.getBirthday());
            row.add(comment.getJoiningTime());
            row.add(comment.getEducation());
            row.add(comment.getGradeName());
            row.add(comment.getPositionName());
            row.add(this.getRatingName(comment.getCommentLevel()));
            itemList.add(row);
            RateUtils.auto(uuid);
        }
    }

    private String getRatingName(Integer rating){
        String ratingName = "";
        if (rating != null) {
            switch (rating) {
                case Constant.FINE:
                    ratingName = "优秀";
                    break;
                case Constant.QUALIFIED:
                    ratingName = "合格";
                    break;
                case Constant.BASIC_QUALIFIED:
                    ratingName = "基本合格";
                    break;
                case Constant.UNQUALIFIED:
                    ratingName = "不合格";
                    break;
                case Constant.UNKNOWN:
                    ratingName = "不确定等次";
                    break;
                default:
                    ratingName = "--";
                    break;
            }
        }
        return ratingName;
    }

    public Object exportComment(String uuid, String redisRepeatKey, HttpServletRequest request, HttpServletResponse response){
        Object rate;
        try {
            rate = AsyncFileDownUtils.asyncDownFile(this.errors, this.redisTemplate, uuid, redisRepeatKey);
        } catch (Exception e) {
            log.error("下载民主评议党员情况excel失败! errorMasage {}", e.getMessage(), e);
            throw new ApiException("下载民主评议党员情况出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "下载民主评议统计出错"));
        }
        return rate;
    }
}
