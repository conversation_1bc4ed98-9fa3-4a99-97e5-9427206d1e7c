package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.MeetingResultHsMapper;
import com.goodsogood.ows.model.db.MeetingResultHsEntity;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @create 2018-10-25 09:12
 **/
@Service
@Log4j2
public class MeetingResultHsService {

    private final MeetingResultHsMapper meetingResultHsMapper;

    @Autowired
    public MeetingResultHsService(MeetingResultHsMapper meetingResultHsMapper) {
        this.meetingResultHsMapper = meetingResultHsMapper;
    }

    /**
     * 新增纪实历史流水
     * @param beforeJson
     * @param updateJson
     * @param uid
     */
    public void insertReportHs(String beforeJson, String updateJson,Long meetingId, Long uid, String remark){
        if (meetingId == null) {
            log.info("meetingId为空。不保存记录");
            return;
        }

        MeetingResultHsEntity hs = new MeetingResultHsEntity();
        if (!StringUtils.isEmpty(beforeJson)) {
            hs.setUpdateBefore(beforeJson);
        }
        if (!StringUtils.isEmpty(updateJson)) {
            hs.setUpdateContent(updateJson);
        }
        if (!StringUtils.isEmpty(remark)) {
            hs.setRemark(remark);
        }
        hs.setCreateTime(DateTime.now().toDate());
        hs.setCreateUser(uid);
        hs.setMeetingId(meetingId);
        this.meetingResultHsMapper.insertSelective(hs);
    }


}