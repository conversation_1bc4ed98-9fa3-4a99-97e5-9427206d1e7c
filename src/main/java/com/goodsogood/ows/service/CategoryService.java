package com.goodsogood.ows.service;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.CategoryMapper;
import com.goodsogood.ows.mapper.MeetingTaskMapper;
import com.goodsogood.ows.mapper.TypeMapper;
import com.goodsogood.ows.model.db.CategoryEntity;
import com.goodsogood.ows.model.db.TypeEntity;
import com.goodsogood.ows.model.vo.CategoryListForm;
import com.goodsogood.ows.utils.SaasUtils;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:25
 **/
@Service
@Log4j2
public class CategoryService {

    private final CategoryMapper mapper;
    private final TypeMapper typeMapper;
    private final MeetingTaskMapper meetingTaskMapper;

    @Autowired
    public CategoryService(CategoryMapper mapper, TypeMapper typeMapper, MeetingTaskMapper meetingTaskMapper) {
        this.mapper = mapper;
        this.typeMapper = typeMapper;
        this.meetingTaskMapper = meetingTaskMapper;
    }


    /**
     * 添加类别
     *
     * @param sysHeader      用户信息
     * @param categoryEntity 类别信息 添加前，id设置为null
     * @return 新增的类别id
     */
    public Long addCategory(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, CategoryEntity categoryEntity) {
        Long regionId = SaasUtils.getRegionId();
        categoryEntity.setCategoryId(null);//添加前，id设置为null
        categoryEntity.setOrgId(sysHeader.getOid());
        categoryEntity.setRegionId(regionId);
        categoryEntity.setCreateUser(sysHeader.getUserId());
        categoryEntity.setCreateTime(DateTime.now().toDate());
        categoryEntity.setOrgName(sysHeader.getOrgName());
        mapper.insert(categoryEntity);
        return categoryEntity.getCategoryId();
    }

    /**
     * 修改类别
     */
    public int updateCategory(HeaderHelper.SysHeader sysHeader, CategoryEntity categoryEntity) {
        categoryEntity.setLastChangeUser(sysHeader.getUserId());
        categoryEntity.setUpdateTime(DateTime.now().toDate());
        return mapper.updateByPrimaryKeySelective(categoryEntity);
    }


    /**
     * 查询活动类别
     */
    public int delCategory(long id) {
        return mapper.deleteByPrimaryKey(id);
    }

    /**
     * 查询活动类别
     */
    public List<CategoryEntity> listAllByName(String category) {
        Long regionId = SaasUtils.getRegionId();
        CategoryListForm categoryListFrom = new CategoryListForm();
        categoryListFrom.setCategory(category);
        categoryListFrom.setRegionId(regionId);
        return mapper.listAllCategory(categoryListFrom);
    }

    /**
     * 查询活动类别
     */
    public CategoryEntity listOneByName(String category) {
        Long regionId = SaasUtils.getRegionId();
        return this.mapper.findByName(category, regionId);
    }

    /**
     * 查询活动类别
     */
    public CategoryEntity findById(long id) {
        return mapper.selectByPrimaryKey(id);
    }

    /**
     * 查询活动类别
     */
    public List<CategoryEntity> listAllCategory(CategoryListForm categoryListFrom) {
        Long regionId = SaasUtils.getRegionId();
        if (categoryListFrom == null) {
            categoryListFrom = new CategoryListForm();
            categoryListFrom.setRegionId(regionId);
        }

        categoryListFrom.setRegionId(regionId);
        return mapper.listAllCategory(categoryListFrom);
    }

    /**
     * 查询活动类别
     * @param tag   1:发起活动下拉框；2:任务完成情况下拉框
     */
    public List<CategoryEntity> listAllCategory(long oid,short tag) {
        return meetingTaskMapper.findCategoryIdByOrgId(oid,tag);
    }

    /**
     * 校验当前类别是否有类型
     */
    public boolean isUse(long id) {
        Example example = new Example(TypeEntity.class);
        example.createCriteria().andEqualTo("categoryId", id);
        return typeMapper.selectByExample(example).size() > 0;
    }

    /**
     * 校验当前类别是否存在
     */
    public boolean isExist(String category) {
        Long regionId = SaasUtils.getRegionId();
        return this.mapper.findByName(category, regionId) != null;
    }
}