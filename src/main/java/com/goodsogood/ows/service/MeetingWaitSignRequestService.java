package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.vo.PushRequest;
import com.goodsogood.ows.model.vo.Result;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/11/11
 */
@Service
@Log4j2
public class MeetingWaitSignRequestService {

    private final TogServicesConfig togServicesConfig;
    private final RestTemplate restTemplate;
    private final Errors errors;

    public MeetingWaitSignRequestService(TogServicesConfig togServicesConfig,
                                         RestTemplate restTemplate, Errors errors) {
        this.togServicesConfig = togServicesConfig;
        this.restTemplate = restTemplate;
        this.errors = errors;
    }


    public Long sendPushRequest(PushRequest pushRequest, Long regionId) {
        String url = "http://" + togServicesConfig.getPushCenter() + "/global/push/diff";
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("_region_id", regionId.toString());
        // 调用远程方法
        try {
            return RemoteApiHelper.post(this.restTemplate, url, pushRequest, httpHeaders, new TypeReference<Result<Long>>() {
            });
        } catch (Exception e) {
            log.error("初始化补学错误->[{}]", e.getMessage(), e);
            e.printStackTrace();
            throw new ApiException("补学初始化失败,请联系系统管理员:[消息发送失败]!",
                    new Result<>(errors, 3051, HttpStatus.BAD_REQUEST.value(), "消息发送失败"));
        }
    }
}
