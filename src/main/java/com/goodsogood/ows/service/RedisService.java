package com.goodsogood.ows.service;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2018/10/29 16:08
 */
@Service
@Log4j2
public class RedisService {

    private final StringRedisTemplate stringRedisTemplate;


    @Autowired
    public RedisService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 根据前缀异步删除指定map中的key
     *
     * @param map    指定的map
     * @param prefix 需要删除的key前缀
     */
    @Async
    public void delMapByKeyPre(String map, String prefix, LogAspectHelper.SSLog ssLog) {
        // 在进入异步方法时，重新设置下上下文
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        helper.reSetContext(ssLog);
        // 获取指定map的key值
        Set<Object> keys = stringRedisTemplate.opsForHash().keys(map);
        // 需要删除的key值
        Set<String> delKeys = new HashSet<>();
        for (Object o : keys) {
            String key = (String) o;
            if (key.startsWith(prefix)) {
                delKeys.add(key);
            }
        }
        // 删除key
        if (delKeys.size() > 0) {
            stringRedisTemplate.boundHashOps(map).delete(delKeys.toArray());
        }
    }

    /**
     * 根据删除指定map中的key
     *
     * @param map 指定的map
     * @param key key
     */
    public void delMapByKey(String map, String key) {
        stringRedisTemplate.boundHashOps(map).delete(key);
    }
}
