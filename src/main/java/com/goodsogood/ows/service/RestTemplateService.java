package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SessionUserForm;
import com.goodsogood.ows.model.vo.UserInfoForm;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import lombok.extern.log4j.Log4j2;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.NotBlank;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Log4j2
@Service
public class RestTemplateService {
    @Value("${tog-services.file-center}")
    @NotBlank
    private String serverFileCenter;

    @Value("${tog-services.user-center}")
    private String userCenter;

    private final RestTemplate restTemplate;
    private final Errors errors;

    public RestTemplateService(RestTemplate restTemplate, Errors errors) {
        this.restTemplate = restTemplate;
        this.errors = errors;
    }

    /**
     * 下载文件
     *
     * @param header
     * @param id
     * @return
     */
    public void getFileInputStream(HttpHeaders header, Long id, String dir, String fileName) {
        String url = String.format("http://%s/file/download/%s", serverFileCenter, id);
        try {
            log.debug("远程调用开始file_id:【{}】,url【{}】,fileName【{}】", id, url,fileName);
            RemoteApiHelper.download(restTemplate, url, dir, fileName, header);
            log.debug("结束导出活动回顾附件=>{}",url);
        } catch (Exception e) {
            e.fillInStackTrace();
            log.error("远程调用文件中心，获取文件失败", e);
        }
    }


    /**
     * 获取文件流
     *
     *
     * @param imgUrl 手写签名的地址
     * @return
     */
    public byte[] obtainFileInputStream(String imgUrl, HttpHeaders headers) throws IOException {
        byte[] source = null;
        try {
            log.debug("远程调用文件中心开始imgUrl: {}", imgUrl);
            String url = String.format("http://%s/file/imgdownload?img_url=%s", serverFileCenter,imgUrl);
            log.debug("访问地址："+url);
            //body(new InputStreamResource(inputStream)==用byte[]接收，不能直接用地址访问，会connection refuse
            source = restTemplate.exchange(url, HttpMethod.GET, null,byte[].class).getBody();
            log.debug("结束远程调用文件中心");
        } catch (Exception e) {
            e.fillInStackTrace();
            log.error("远程调用文件中心，获取文件失败", e);
        }
        return source;
    }
    /**
     * 获取组织等级
     * @param header
     * @param orgId
     * @return
     */
    public String getOrgLevel(HeaderHelper.SysHeader header, Long orgId){
        String url = String.format("http://%s/org/find-org-by-list", userCenter);
        Map<String,List<Long>> body = new HashMap<String, List<Long>>(){{put("id_list", Collections.singletonList(orgId));}};
        try {
            log.debug("远程调用，获取组织[{}]等级",orgId);
            List<OrganizationBase> result = RemoteApiHelper.post(
                    restTemplate,
                    url,
                    body,
                    HeaderHelper.setMyHttpHeader(LogAspectHelper.putLogHeader(), header),
                    new TypeReference<Result<List<OrganizationBase>>>() {
                    }
            );
            if (!CollectionUtils.isEmpty(result)){
                return result.get(0).getOrgLevel();
            }
            return " ";
        }catch (Exception e){
            log.error("远程调用获取组织[{}]等级失败",orgId,e);
            return " ";
        }
    }

    /**
     * 组织届次用户信息
     * @param header
     * @return
     */
    public List<SessionUserForm> getSessionUser(HeaderHelper.SysHeader header){
        String url = String.format("http://%s/period/find-new-users?org_id=%s", userCenter,header.getOid());
        try {
            log.debug("远程调用，获取组织[{}]届次用户信息",header.getOid());
            return RemoteApiHelper.get(
                    restTemplate,
                    url,
                    HeaderHelper.setMyHttpHeader(LogAspectHelper.putLogHeader(), header),
                    new TypeReference<Result<List<SessionUserForm>>>() {
                    }
            );
        }catch (Exception e){
            log.error("远程调用获取组织[{}]届次用户信息失败",header.getOid(),e);
            return null;
        }
    }

}
