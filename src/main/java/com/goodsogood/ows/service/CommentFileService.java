package com.goodsogood.ows.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.common.FileSourceEnum;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.utils.FileUtil;
import com.goodsogood.ows.utils.word.ExportData;
import com.goodsogood.ows.utils.word.ExportWordUtil;
import com.goodsogood.ows.utils.word.SoMap;
import com.goodsogood.ows.utils.word.WordUtil;
import com.thoughtworks.xstream.core.util.Base64Encoder;
import freemarker.template.TemplateException;
import lombok.extern.log4j.Log4j2;
import lombok.val;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 民主评议附件处理
 *
 * <AUTHOR>
 * @create 2021-12-21
 */
@Service
@Log4j2
public class CommentFileService {
    private final ExportWordUtil exportWordUtil;
    @Value("${temp-path}")
//    String path = "/home/<USER>/appdata/tmp/ows-meeting";
        String path = "C:\\Users\\<USER>\\Desktop\\work";
    private final Errors errors;
    private final RestTemplate restTemplate;
    TogServicesConfig togServicesConfig;
    MeetingFileService meetingFileService;
    OrgLifeFileService orgLifeFileService;
    MeetingCommentMemberMapper meetingCommentMemberMapper;
    OrgLifeCommentMapper orgLifeCommentMapper;
    ThirdService thirdService;
    MeetingCommentMapper meetingCommentMapper;
    OpenService openService;
    MeetingCommentMemberComplexMapper meetingCommentMemberComplexMapper;
    MeetingCommentApproveMapper meetingCommentApproveMapper;
    private final ObjectMapper objectMapper;
    private final RestTemplateService restTemplateService;

    @Autowired
    public CommentFileService(ExportWordUtil exportWordUtil,
                              RestTemplate restTemplate,
                              TogServicesConfig togServicesConfig,
                              MeetingFileService meetingFileService,
                              OrgLifeFileService orgLifeFileService,
                              MeetingCommentMemberMapper meetingCommentMemberMapper,
                              OrgLifeCommentMapper orgLifeCommentMapper,
                              ThirdService thirdService,
                              OpenService openService,
                              MeetingCommentMemberComplexMapper meetingCommentMemberComplexMapper,
                              MeetingCommentApproveMapper meetingCommentApproveMapper,
                              MeetingCommentMapper meetingCommentMapper,
                              ObjectMapper objectMapper,
                              Errors errors, RestTemplateService restTemplateService) {
        this.exportWordUtil = exportWordUtil;
        this.restTemplate = restTemplate;
        this.togServicesConfig = togServicesConfig;
        this.meetingFileService = meetingFileService;
        this.orgLifeFileService = orgLifeFileService;
        this.meetingCommentMemberMapper = meetingCommentMemberMapper;
        this.orgLifeCommentMapper = orgLifeCommentMapper;
        this.thirdService = thirdService;
        this.openService = openService;
        this.meetingCommentMemberComplexMapper = meetingCommentMemberComplexMapper;
        this.meetingCommentApproveMapper = meetingCommentApproveMapper;
        this.meetingCommentMapper = meetingCommentMapper;
        this.objectMapper = objectMapper;
        this.errors = errors;
        this.restTemplateService = restTemplateService;
    }

    /**
     * 自评模板处理
     *
     * @Param commentMemId:t_meeting_comment_member主键
     * @Param userForm:UserGradeForm,将导出信息封装到UserGradeForm，用于导出模板
     * @Param headers
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    public void selfTemplateHandle(Long commentMemId, Long commentId, HttpHeaders headers) throws IOException {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("开始处理");
        log.debug("判断该模板是否存在");
        if (checkExist(commentMemId)) {//如果存在则调用删除处理
            delHandle(commentMemId, commentId, FileSourceEnum.MEETING_COMMENT, headers);
        }
        //将自评信息处理到UserGradeForm
        UserGradeForm userForm = createUserGradeForm(commentMemId, headers);
        if (Objects.isNull(userForm)) {
            log.debug("没有人员信息不需要生成模板");
            return;
        }
        UploadFileResultForm fileInfo;
        log.debug("不存在则新增处理");
        fileInfo = addSelfHandle(commentMemId, userForm, headers);
        List<Long> lifeIds = orgCommentRelation(commentRelation(commentMemId));
        log.debug("判断该民主评议是否关联了组织生活会");
        if (!CollectionUtils.isEmpty(lifeIds)) {
            for (Long lifeId : lifeIds) {
                log.debug("将附件信息保存到组织生活会附件(自行判断新增或编辑)");
                saveCommentToLife(commentMemId, 28, header, Arrays.asList(fileInfo), lifeId);
            }
        }
    }

    /**
     * 综合评议模板处理
     *
     * @Param commentId:comment_id主键
     * @Param lifeId ：组织生活会id
     * @Param orgForm:OrgGradeForm,将导出信息封装到OrgGradeForm，用于导出模板
     * @Param headers
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    public void orgTemplateHandle(Long commentId, HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("将综合评定信息处理到OrgGradeForm" + commentId);
        OrgGradeForm orgForm = createOrgGradeForm(commentId);
        log.debug("将综合评定信息处理到OrgGradeForm结束" + orgForm);
        if (Objects.isNull(orgForm)) {
            return;
        }
        UploadFileResultForm fileInfo = null;
        log.debug("判断该测评模板是否存在" + commentId);
        if (checkExist(commentId)) {//如果存在则调用删除处理
            delHandle(commentId, null, FileSourceEnum.MEETING_ORG_COMMENT, headers);
        }
        fileInfo = addOrgHandle(commentId, orgForm, headers);
        List<Long> lifeIds = orgCommentRelation(commentId);
        if (!CollectionUtils.isEmpty(lifeIds)) {//判断该民主评议是否关联了组织生活会
            log.debug("附件插入组织生活会");
            for (Long lifeId : lifeIds) {
                log.debug("将附件信息保存到组织生活会附件(自行判断新增或编辑)");
                saveCommentToLife(commentId, 29, header, Arrays.asList(fileInfo), lifeId);
            }
        }
    }


    /**
     * 导出报告-word:按勾选导出
     *
     * @param commentIds
     * @param type       0: 全部导出  1: 导出登记表  2: 导出测评表  3: 导出自评互评汇总表
     * @return
     */
    public MeetingFileListVo queryCommentFile(List<Long> commentIds, List<Integer> type) {
        MeetingFileListVo vo = new MeetingFileListVo();
        vo.setFileName("民主评议党员材料");
        List<MeetingFileListVo> orgVos = new ArrayList<>();
        for (Long commentId : commentIds) {
            MeetingFileListVo voOrg = new MeetingFileListVo();
            //根据commentid查询org和year
            MeetingCommentEntity meetingComment = queryComment(commentId);
            if (ObjectUtils.isEmpty(meetingComment)) {
                continue;
            }
            voOrg.setFileName(meetingComment.getOrgName() + "_" + meetingComment.getYear());

            List<MeetingFileListVo> list = new ArrayList<>();
            if (type.contains(0)) {
                list.add(createUserListVo(commentId));
                list.add(createOrgListVo(commentId));
                list.add(createAppraisalVo(commentId));
            }

            if (type.contains(1)) {//登记表
                list.add(createUserListVo(commentId));
            }

            if (type.contains(2)) {
                list.add(createOrgListVo(commentId));
            }

            if (type.contains(3)) {
                list.add(createAppraisalVo(commentId));
            }
            voOrg.setListFile(list);
            orgVos.add(voOrg);
        }
        vo.setListFile(orgVos);
        return vo;
    }

    /**
     * 导出自评报告-word：按查询条件导出
     *
     * @param commentMemberVO
     * @return
     */
    public MeetingFileListVo querySelfCommentFile(CommentMemberVO commentMemberVO, List<Integer> type, HttpHeaders headers) {
        log.debug("调用沈健查询方法开始" + commentMemberVO);
        List<CommentMemberResultForm> commentMemForm = queryCommentMemberList(commentMemberVO, headers);
        log.debug("调用沈健查询方法结束");
        if (!CollectionUtils.isEmpty(commentMemForm)) {
            log.debug("根据memid查询commentids");
            List<Long> commentMemIds = commentMemForm.stream().map(CommentMemberResultForm::getCommentMemberId).collect(Collectors.toList());
            List<Long> commentIds = commentRelations(commentMemIds);
            return queryCommentFile(commentIds, type);
        }
        return new MeetingFileListVo();
    }

    /**
     * 查询民主评议党员列表-用于导出报告
     *
     * @param commentMemberVO
     * @param headers
     */
    private List<CommentMemberResultForm> queryCommentMemberList(CommentMemberVO commentMemberVO, HttpHeaders headers) {
        log.debug("查询民主评议党员列表 -> vo:[${commentMemberVO}]");
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String phone = null;
        // 判断是否要根据手机号查询，若需要则加密
        if (commentMemberVO.getPhone() != null) {
            phone = NumEncryptUtils.encrypt(commentMemberVO.getPhone(), NumEncryptUtils.PHONE);
        }
        log.debug("调用沈健查询" + commentMemberVO.getCommentId() + "_" + commentMemberVO.getName() + "_" + phone + "_" + commentMemberVO.getSelfRating() + "_" + commentMemberVO.getComplexRating());
        List<CommentMemberResultForm> list = meetingCommentMemberMapper.getCommentMemberList(commentMemberVO.getCommentId(), commentMemberVO.getName(), phone,
                commentMemberVO.getSelfRating(), commentMemberVO.getComplexRating(), null);
        log.debug("调用沈健查询完成" + list);
        return list;

    }

    private MeetingFileListVo createOrgListVo(Long commentId) {
        List<MeetingFileVO> orgfiles = meetingFileService.selectByLinkedId(commentId, FileSourceEnum.MEETING_ORG_COMMENT);
        MeetingFileListVo innerVo = new MeetingFileListVo();
        innerVo.setFileName("02党员民主测评表");
        List<MeetingFileListVo> list = new ArrayList<>();
        orgfiles.forEach(i -> {
            list.add(new MeetingFileListVo(i.getFileName(), i.getPath()));
        });
        innerVo.setListFile(list);
        return innerVo;
    }

    private MeetingFileListVo createUserListVo(Long commentId) {
        List<Long> commentMemIds = memCommentRealte(commentId);
        return createSelfUserListVo(commentMemIds);
    }

    private MeetingFileListVo createSelfUserListVo(List<Long> commentMemIds) {
        List<MeetingFileVO> userfiles = meetingFileService.selectByLinkedIds(commentMemIds, FileSourceEnum.MEETING_COMMENT);
        MeetingFileListVo innerVo = new MeetingFileListVo();
        innerVo.setFileName("01民主评议登记表");
        List<MeetingFileListVo> list = new ArrayList<>();
        userfiles.forEach(i -> {
            list.add(new MeetingFileListVo(i.getFileName(), i.getPath()));
        });
        innerVo.setListFile(list);
        return innerVo;
    }

    private MeetingFileListVo createAppraisalVo(Long commentId) {
        List<MeetingFileVO> orgfiles = meetingFileService.selectByLinkedId(commentId, FileSourceEnum.MEETING_COMMENT_APPRAISAL);
        MeetingFileListVo innerVo = new MeetingFileListVo();
        innerVo.setFileName("03民主评议党员自评互评汇总表");
        List<MeetingFileListVo> list = new ArrayList<>();
        orgfiles.forEach(i -> {
            list.add(new MeetingFileListVo(i.getFileName(), i.getPath()));
        });
        innerVo.setListFile(list);
        return innerVo;
    }


    /**
     * @param commentMemId
     * @return
     */
    //将自评信息处理到UserGradeForm
    public UserGradeForm createUserGradeForm(Long commentMemId, HttpHeaders headers) throws IOException {
        log.debug("将自评信息处理到UserGradeForm:" + commentMemId);
        MeetingCommentMemberEntity memberEntity = meetingCommentMemberMapper.selectByPrimaryKey(commentMemId);
        if (memberEntity == null) {
            return null;
        }
        log.debug("memberEntity" + memberEntity);
        Long regionId = queryComment(memberEntity.getCommentId()).getRegionId();
        Long userId = memberEntity.getUserId();
        Long orgId = memberEntity.getOrgId();
        log.debug("查询用户基本信息");
        if (userId == null || orgId == null || regionId == null) {
            return null;
        }
//        UserInfoForm userInfo = new UserInfoForm();
//        userInfo.setName("王五");
//        userInfo.setOrgName("ssdjigou");
        final List<UserInfoBase> baseList = thirdService.findUserInfoByKey(userId, regionId);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(baseList)) {
            return null;
        }
        final UserInfoBase userInfo = baseList.get(0);
        UserGradeForm userForm = new UserGradeForm();
        //调用文件服务中心获取手动签名图片流(暂时不用)
//        String encodeImg = getImgEncode(userId, headers);
//        userForm.setImgStr(encodeImg);
        userForm.setUsername(userInfo.getName());
        //性别查询
        if (!ObjectUtils.isEmpty(userInfo.getGender())) {
            OptionForm sex = openService.getOptionByList(openService.getOptionListByCode(Constant.GENDER_CODE), userInfo.getGender());
            userForm.setSex(sex == null ? "" : sex.getOpValue());
        } else {
            userForm.setSex("");
        }
        userForm.setDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
        log.debug("userinfo的orgname" + userInfo.getOrgName() + "_" + memberEntity.getOrgName());
//        userForm.setOrgName(userInfo.getOrgName());
        userForm.setOrgName(memberEntity.getOrgName());
        userForm.setBirthDay(userInfo.getBirthday() == null ? "" : userInfo.getBirthday());
        //学历查询
        if (!ObjectUtils.isEmpty(userInfo.getEducation())) {
            log.debug("学历查询" + userInfo.getEducation());
            OptionForm education = openService.getOptionByList(openService.getOptionListByCode(Constant.EDUCATION_CODE_NEW), userInfo.getEducation());
            log.debug("学历查询1" + education);
            userForm.setEducation(education == null ? "" : education.getOpValue() == null ? "" : education.getOpValue());
            log.debug("学历查询2" + education.getOpValue());
        } else {
            userForm.setEducation("");
        }
        userForm.setJoinTime(userInfo.getJoinTime() == null ? "" : userInfo.getJoinTime());
        userForm.setProfession("");//职务留空
        //自评等级
        if (memberEntity.getSelfRating() != null) {
            switch (memberEntity.getSelfRating()) {
                case 1:
                    userForm.setLevel1("√");
                    break;
                case 2:
                    userForm.setLevel2("√");
                    break;
                case 3:
                    userForm.setLevel3("√");
                    break;
                case 4:
                    userForm.setLevel4("√");
                    break;
            }

        }
        userForm.setIdentifytion(ObjectUtils.isEmpty(memberEntity.getSelfContent()) ? "" : memberEntity.getSelfContent()); //自我鉴定
        LocalDateTime selfDate = ObjectUtils.isEmpty(memberEntity.getUpdateTime()) ? memberEntity.getCreateTime() : memberEntity.getUpdateTime();
        DateTimeFormatter format2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        assert selfDate != null;
        String selfDateStr = format2.format(selfDate);
        userForm.setYear1(selfDateStr.substring(0, 4));
        userForm.setMonth1(selfDateStr.substring(5, 7));
        userForm.setDay1(selfDateStr.substring(8, 10));
        //综合评议一条一行
        MeetingCommentMemberComplexEntity complexEntity = queryCommentComplex(commentMemId);
        if (!ObjectUtils.isEmpty(complexEntity)) {
            String suggestion = complexEntity.getComplexSuggestion();//待出数据后再更改，一条一行
            List<String> suggestions = objectMapper.readValue(suggestion, List.class);
            String suggestionStr = StringUtils.join(suggestions.toArray(), "<w:br/>");
            userForm.setOrgIdentifytion(suggestionStr);
            LocalDateTime orgDate = ObjectUtils.isEmpty(complexEntity.getUpdateTime()) ? complexEntity.getCreateTime() : complexEntity.getUpdateTime();
            assert orgDate != null;
            String orgDateStr = format2.format(orgDate);
            userForm.setYear2(orgDateStr.substring(0, 4));
            userForm.setMonth2(orgDateStr.substring(5, 7));
            userForm.setDay2(orgDateStr.substring(8, 10));
        }
        List<MeetingCommentApproveEntity> approveEntity = queryApprove(memberEntity.getCommentId());
        MeetingCommentApproveEntity approveBranchEntity = approveEntity.stream().filter(i -> i.getApproveStatus() == 1 || i.getApproveStatus() == 2).sorted(Comparator.comparing(MeetingCommentApproveEntity::getCreateTime).reversed()).findFirst().orElse(null);
        MeetingCommentApproveEntity approveCommitEntity = approveEntity.stream().filter(i -> i.getApproveStatus() == 3 || i.getApproveStatus() == 4).sorted(Comparator.comparing(MeetingCommentApproveEntity::getCreateTime).reversed()).findFirst().orElse(null);
        if (approveBranchEntity != null) {
            //党总支审批意见
            userForm.setPartyIdentifytion(approveBranchEntity.getContent() != null ? approveBranchEntity.getContent() : "");
            String branchDateStr = format2.format(Objects.requireNonNull(approveBranchEntity.getCreateTime()));
            userForm.setYear3(branchDateStr.substring(0, 4));
            userForm.setMonth3(branchDateStr.substring(5, 7));
            userForm.setDay3(branchDateStr.substring(8, 10));
        }
        if (approveCommitEntity != null) {
            //党委审查意见
            userForm.setCommitIdentifytion(approveCommitEntity.getContent() != null ? approveCommitEntity.getContent() : "");
            String commitDateStr = format2.format(Objects.requireNonNull(approveCommitEntity.getCreateTime()));
            userForm.setYear4(commitDateStr.substring(0, 4));
            userForm.setMonth4(commitDateStr.substring(5, 7));
            userForm.setDay4(commitDateStr.substring(8, 10));
        }
        return userForm;
    }


    private String getImgEncode(Long userId, HttpHeaders headers) throws IOException {
        log.debug("调用用户中心获取手写签名图片的url开始");
        String imgUrl = thirdService.findUserHandWrite(userId);
        if (StringUtils.isBlank(imgUrl)) {
            return "";
        }
        byte[] data = restTemplateService.obtainFileInputStream(imgUrl, headers);
//        String httpUrl = "https://dangjian.cq.tobacco.gov.cn/zuul/owsz/file/file/imgdownload?img_url="+ imgUrl;
//        byte[] data = response.getBody();a
        Base64Encoder encoder = new Base64Encoder();
        return encoder.encode(data);
    }


    //将综合评定信息处理到OrgGradeForm
    public OrgGradeForm createOrgGradeForm(Long commentId) {
        //查询所有t_meeting_comment_member_complex
        List<CommentOrgGreadVO> vos = meetingCommentMemberComplexMapper.getComplexFormInfo(commentId);
        log.debug("查询所有t_meeting_comment_member_complex完成" + vos);
        OrgGradeForm orgGradeForm = new OrgGradeForm();
        if (CollectionUtils.isEmpty(vos)) {
            return null;
        }
        orgGradeForm.setOrgName(vos.get(0).getOrgName());
        orgGradeForm.setDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
        List<OrgGradeForm.OrgForm> orgForms = new ArrayList<>();
        AtomicInteger num = new AtomicInteger(1);
        vos.forEach(vo -> {
            OrgGradeForm.OrgForm orgForm = new OrgGradeForm.OrgForm();
            orgForm.setNum(String.valueOf(num.get()));
            num.getAndIncrement();
            orgForm.setUsername(vo.getUserName() == null ? "" : vo.getUserName());
            if (!ObjectUtils.isEmpty(vo.getComplexRating())) {
                switch (vo.getComplexRating()) {
                    case 1:
                        orgForm.setLevel1("√");
                        break;
                    case 2:
                        orgForm.setLevel2("√");
                        break;
                    case 3:
                        orgForm.setLevel3("√");
                        break;
                    case 4:
                        orgForm.setLevel4("√");
                        break;
                }
            }
            orgForms.add(orgForm);

        });
        orgGradeForm.setOrgForm(orgForms);
        return orgGradeForm;
    }


    /**
     * 删除模板
     *
     * @param id         如是自评，则为自评id,如是综合评，则为commentid
     * @param sourceEnum
     * @param headers
     */
    @Transactional(rollbackFor = Exception.class)
    public void delHandle(Long id, Long commentId, FileSourceEnum sourceEnum, HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("删除文件服务器上该模板");//????
        log.debug("删除民主评议该模板");
        meetingFileService.delete(id, sourceEnum);
        List<Long> lifeIds = null;
        if (sourceEnum.getSource().equals(FileSourceEnum.MEETING_COMMENT.getSource())) {//自评：根据commentMemid查commentId查lifeid
            lifeIds = orgCommentRelation(commentId != null ? commentId : commentRelation(id));
            if (!CollectionUtils.isEmpty(lifeIds)) {//如果关联了民主生活
                log.debug("删除组织生活会该模板附件-登记表");
                orgLifeFileService.deleteTalkTemplateComment(lifeIds, 28, id, header.getUserId());
            }
        } else if (sourceEnum.getSource().equals(FileSourceEnum.MEETING_ORG_COMMENT.getSource())) {
            lifeIds = orgCommentRelation(id);
            if (!CollectionUtils.isEmpty(lifeIds)) {//如果关联了民主生活
                log.debug("删除组织生活会该模板附件-测评表");
                orgLifeFileService.deleteTalkTemplateComment(lifeIds, 29, id, header.getUserId());
            }
        } else if (sourceEnum.getSource().equals(FileSourceEnum.MEETING_COMMENT_APPRAISAL.getSource())) {
            lifeIds = orgCommentRelation(id);
            if (!CollectionUtils.isEmpty(lifeIds)) {//如果关联了民主生活
                log.debug("删除组织生活会该模板附件-自评互评表");
                orgLifeFileService.deleteTalkTemplateComment(lifeIds, 30, id, header.getUserId());
            }
        }
    }


    //保存民主评议附件到组织生活会
    private void saveCommentToLife(Long id, int type, HeaderHelper.SysHeader header, List<UploadFileResultForm> fileInfos, Long lifeId) {
        SaveAttachForm saveAttachForm = new SaveAttachForm();
        saveAttachForm.setDataId(id);
        saveAttachForm.setIsDirect(1);
        saveAttachForm.setStep(2);
        saveAttachForm.setType(type);//综合
        saveAttachForm.setLifeId(lifeId);
        saveAttachForm.setUserId(header.getUserId());
        saveAttachForm.setUsername(header.getUserName());
        List<LifeFileForm> list = new ArrayList<>();
        for (UploadFileResultForm fileInfo : fileInfos) {
            LifeFileForm file = new LifeFileForm(fileInfo.getId(), fileInfo.getFileName(), fileInfo.getFileName(), fileInfo.getPath());
            list.add(file);
        }
        saveAttachForm.setLifeFile(list);
        orgLifeFileService.saveAttach(saveAttachForm, header);
    }


    //判断是否已经存在该模板
    private Boolean checkExist(Long id) {
        return meetingFileService.selectByLinkedId(id, FileSourceEnum.MEETING_COMMENT) != null;
    }

    //新增处理-自评模板
    public UploadFileResultForm addSelfHandle(Long commentMemId, UserGradeForm userForm, HttpHeaders headers) {
        //生成模板
        String fileName = createSelfTemplate(userForm);
        //上传文件服务器
        UploadFileResultForm fileInfo = uploadServer(fileName, headers);
//        UploadFileResultForm fileInfo = new UploadFileResultForm();
//        fileInfo.setFileName("cesh.doc");
//        fileInfo.setPath("/ddddd");
//        fileInfo.setName("ssss");
        //保存到附件表
        saveMeetingFile(commentMemId, fileInfo, FileSourceEnum.MEETING_COMMENT);
        return fileInfo;
    }

    //新增处理-综合评议模板
    public UploadFileResultForm addOrgHandle(Long commentId, OrgGradeForm orgForm, HttpHeaders headers) {
        log.debug("生成测评模板" + orgForm);
        String fileName = createOrgTemplate(orgForm);
        //上传文件服务器
        UploadFileResultForm fileInfo = uploadServer(fileName, headers);
        log.debug("测评表信息" + fileInfo);
//        UploadFileResultForm fileInfo = new UploadFileResultForm();
//        fileInfo.setFileName("cesh.doc");
//        fileInfo.setPath("/ddddd");
//        fileInfo.setName("ssss");
        log.debug("保存到附件表");
        saveMeetingFile(commentId, fileInfo, FileSourceEnum.MEETING_ORG_COMMENT);
        return fileInfo;
    }


    //保存到t_meeting_file
    public void saveMeetingFile(Long id, UploadFileResultForm fileInfo, FileSourceEnum sourceEnum) {
        MeetingFileForm meetingFile = new MeetingFileForm(fileInfo.getId(), null, fileInfo.getName(), fileInfo.getPath(), fileInfo.getFileName(), fileInfo.getSize());
        this.meetingFileService.addFile(id, Collections.singletonList(meetingFile), sourceEnum);
    }

    //生成自评模板
    public String createSelfTemplate(UserGradeForm userForm) {
        log.debug("生成自评模板->{}", userForm);
        String fileName = userForm.getUsername() + "_" + "民主评议党员登记表.docx";
        try {
            if (userForm.getImgStr().length() < 5 || StringUtils.isBlank(userForm.getImgStr())) {
                exportWordUtil.exportWordAlone("民主评议党员登记表e.ftl", path, fileName, userForm);
            } else {
                exportWordUtil.exportWordAlone("民主评议党员登记表-有图.ftl", path, fileName, userForm);
            }
        } catch (IOException | TemplateException e) {
            log.error("生成自评模板失败，请检查参数!fileName->" + fileName, e);
            throw new ApiException("模板生成失败!", new Result<>(errors, 3172, HttpStatus.BAD_REQUEST.value()));
        }
        return fileName;
    }


    //生成综合评议模板(mapkey为orgName date)
    public String createOrgTemplate(OrgGradeForm orgGradeForm) {
        String orgName = orgGradeForm.getOrgName();
        String date = orgGradeForm.getDate();
        Map<String, String> map = new HashMap<>();
        map.put("orgName", orgName);
        map.put("date", date);
        log.debug("生成综合评议模板");
        String dirName = orgGradeForm.getOrgName() + "_" + "党员民主评议测评表.docx";
        log.debug("导出文件名：" + dirName);
        log.debug("mt导出文件名：" + map);
        try {
            exportWordUtil.exportWordAll("党员民主评议测评表_1.ftl", path, dirName, map, orgGradeForm.getOrgForm());
        } catch (IOException | TemplateException e) {
            log.error("生成综合评议模板，请检查参数!" + e);
            throw new ApiException("模板生成失败!", new Result<>(errors, 3172, HttpStatus.BAD_REQUEST.value()));
        }
        return dirName;
    }


    //上传文件服务器
    public UploadFileResultForm uploadServer(String fileName, HttpHeaders headers) {
        log.debug("上传文件服务器");
        log.debug("mengt排错" + path + File.pathSeparator + fileName);
        String pathNew = Paths.get(path, fileName).toString();
        log.debug("mengt排错2" + pathNew);
        UploadFileResultForm file = FileUtil.sendFileCenter(pathNew, fileName, "file", errors, headers, restTemplate, togServicesConfig);
        if (file != null) {
            log.debug("上传文件服务器成功");
        } else {
            throw new ApiException("文件上传失败", new Result<>(errors, 2009, HttpStatus.OK.value(), "文件上传文件服务器失败"));
        }
        // 上传成功后删除本地文件
        FileUtil.deleteFile(pathNew);
        return file;
    }


    //自评关联的commentid
    private Long commentRelation(Long commentMemId) {
        MeetingCommentMemberEntity memberEntity = meetingCommentMemberMapper.selectByPrimaryKey(commentMemId);
        return memberEntity.getCommentId();
    }

    //自评关联的commentids
    private List<Long> commentRelations(List<Long> commentMemIds) {
        Example example = new Example(MeetingCommentMemberEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("commentMemberId", commentMemIds);
        example.selectProperties("commentId");
        List<MeetingCommentMemberEntity> memberEntity = meetingCommentMemberMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(memberEntity)) {
            return Collections.EMPTY_LIST;
        }
        return memberEntity.stream().map(MeetingCommentMemberEntity::getCommentId).collect(Collectors.toList());
    }

    //自评关联的t_meeting_comment
    private MeetingCommentEntity queryComment(Long commentId) {
        return meetingCommentMapper.selectByPrimaryKey(commentId);
    }

    //根据list commentId查询出
    private List<MeetingCommentEntity> queryComments(List<Long> commentIds) {
        Example example = new Example(MeetingCommentEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("commentId", commentIds);
        example.selectProperties("orgId", "orgName", "year");
//        example.setOrderByClause();
        return meetingCommentMapper.selectByExample(example);
    }

    //自评关联的t_meeting_comment_member_complex
    private MeetingCommentMemberComplexEntity queryCommentComplex(Long commentMemId) {
        Example example = new Example(MeetingCommentMemberComplexEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("commentMemberId", commentMemId);
        return meetingCommentMemberComplexMapper.selectOneByExample(example);
    }

    //根据commentId查询审批意见
    public List<MeetingCommentApproveEntity> queryApprove(Long commentId) {
        Example example = new Example(MeetingCommentApproveEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("commentId", commentId);
        return meetingCommentApproveMapper.selectByExample(example);
    }

    //commentId关联的自评id
    private List<Long> memCommentRealte(Long commentId) {
        Example example = new Example(MeetingCommentMemberEntity.class);
        Example.Criteria criteria = example.createCriteria();
        example.selectProperties("commentMemberId");
        criteria.andEqualTo("commentId", commentId);
        List<MeetingCommentMemberEntity> list = meetingCommentMemberMapper.selectByExample(example);
        return list.stream().map(MeetingCommentMemberEntity::getCommentMemberId).collect(Collectors.toList());
    }

    //commentid关联的组织生活会id
    private List<Long> orgCommentRelation(Long commentId) {
        Example example = new Example(OrgLifeCommentEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("commentId", commentId);
        example.selectProperties("lifeId");
        List<OrgLifeCommentEntity> list = orgLifeCommentMapper.selectByExample(example);//如果没有关联
        return list.stream().map(OrgLifeCommentEntity::getLifeId).collect(Collectors.toList());
    }

    /**
     * 将民主评议附件插入组织生活会附件表
     */
    @Transactional(rollbackFor = Exception.class)
    public void relateComment(Long commentId, Long lifeId, HeaderHelper.SysHeader header) {
        //将关联的民主评议附件放到组织生活会附件表中
        log.debug("查询民主评议自评id");
        List<Long> ids = memCommentRealte(commentId);
        for (Long id : ids) {
            List<MeetingFileVO> meetingSelfFiles = meetingFileService.selectByLinkedId(id, FileSourceEnum.MEETING_COMMENT);
            if (!CollectionUtils.isEmpty(meetingSelfFiles)) {
                List<UploadFileResultForm> files = transMeetingToUpForm(meetingSelfFiles);
                saveCommentToLife(id, 28, header, files, lifeId);
            }
        }
        log.debug("查询民主评议综合模板");
        List<MeetingFileVO> meetingOrgFiles = meetingFileService.selectByLinkedId(commentId, FileSourceEnum.MEETING_ORG_COMMENT);
        if (!CollectionUtils.isEmpty(meetingOrgFiles)) {
            List<UploadFileResultForm> files = transMeetingToUpForm(meetingOrgFiles);
            saveCommentToLife(commentId, 29, header, files, lifeId);
        }
    }


    //类型转换，将MeetingFileVO转为UploadFileResultForm
    private List<UploadFileResultForm> transMeetingToUpForm(List<MeetingFileVO> meetingSelfFiles) {
        List<UploadFileResultForm> files = new ArrayList<>();
        for (MeetingFileVO vo : meetingSelfFiles) {
            UploadFileResultForm fileInfo = new UploadFileResultForm();
            fileInfo.setFileName(vo.getFileName());
            fileInfo.setId(vo.getFileId());
            fileInfo.setName(vo.getName());
            fileInfo.setPath(vo.getPath());
            fileInfo.setSize(vo.getSize());
            files.add(fileInfo);
        }
        return files;
    }

    public void addAppraisalTable(CommentUserAppraisalDataForm form, HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("生成自评互评表: " + form);
        if (Objects.isNull(form)) {
            return;
        }
        UploadFileResultForm fileInfo = null;
        log.debug("判断该自评互评表是否存在" + form.getCommentId());
        if (checkExist(form.getCommentId())) {//如果存在则调用删除处理
            delHandle(form.getCommentId(), null, FileSourceEnum.MEETING_COMMENT_APPRAISAL, headers);
        }
        fileInfo = createAppraisalTable(form, headers);
        List<Long> lifeIds = orgCommentRelation(form.getCommentId());
        if (!CollectionUtils.isEmpty(lifeIds)) {//判断该民主评议是否关联了组织生活会
            log.debug("附件插入组织生活会");
            for (Long lifeId : lifeIds) {
                log.debug("将附件信息保存到组织生活会附件(自行判断新增或编辑)");
                saveCommentToLife(form.getCommentId(), 30, header, Arrays.asList(fileInfo), lifeId);
            }
        }
    }

    public UploadFileResultForm createAppraisalTable(CommentUserAppraisalDataForm form, HttpHeaders headers) {
        final ExportData evaluation = WordUtil.createExportData("file/民主评议党员自评互评汇总表.docx");//模板
        final ArrayList<SoMap> soMaps = new ArrayList<>();
        if (!form.getUserList().isEmpty()) {
            form.getUserList().forEach(user -> soMaps.add(new SoMap(user)));
            evaluation.setData("comment", form);
            evaluation.setTable("user", soMaps);
            final byte[] data = evaluation.getByteArr();
            String name = "民主评议党员自评互评汇总表.docx";
            // 可以直接写入本地的文件
            val fileName = Paths.get(path, name).toString();
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                fos.write(data, 0, data.length);
            } catch (IOException ex) {
                log.error("生成民主评议党员自评互评汇总表报错", ex);
            }
            log.debug("生成民主评议党员自评互评汇总表成功!");
            final UploadFileResultForm resultForm = FileUtil.sendFileCenter(fileName, name, "file", errors, headers, restTemplate, togServicesConfig);
            if (resultForm != null) {
                log.debug("文件上传成功");
            } else {
                throw new ApiException("文件上传失败", new Result<>(errors, 2009, HttpStatus.OK.value(), "文件上传文件服务器失败"));
            }
            // 上传成功后删除本地文件
            FileUtil.deleteFile(fileName);
            log.debug("保存到附件表");
            saveMeetingFile(form.getCommentId(), resultForm, FileSourceEnum.MEETING_COMMENT_APPRAISAL);
            return resultForm;
        } else {
            return new UploadFileResultForm();
        }
    }
}
