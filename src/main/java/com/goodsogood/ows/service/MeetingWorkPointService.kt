package com.goodsogood.ows.service

import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.LocalDateTimeUtils
import com.goodsogood.ows.model.db.MeetingWorkPointEntity
import com.goodsogood.ows.model.vo.MeetingWorkPointForm
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.repository.MeetingWorkPointRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.domain.Specification
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import java.time.LocalDateTime
import javax.persistence.criteria.Predicate

/**
 * <AUTHOR>
 * @date 2023/10/9
 * @description class MeetingWorkPointService
 */
@Service
class MeetingWorkPointService(
    val errors: Errors,
    val restTemplate: RestTemplate,
    val thirdService: ThirdService,
    val openService: OpenService,
    val applicationConfigHelper: SimpleApplicationConfigHelper,
    val meetingWorkPointRepository: MeetingWorkPointRepository,
) {
    @Value("\${saas.label}")
    var label: String? = "v4.0.0"

    private val log = LoggerFactory.getLogger(MeetingWorkPointService::class.java)

    /**
     * 根据单位id(顶级组织的第一层id，加上3的单位)，创建本年的工作要点，如果存在就不创建了
     * 一年一条
     * @param
     */
    @JvmOverloads
    fun createByOwnerId(regionId: Long, headers: HttpHeaders? = null, year: Int? = null) {
        val header = headers ?: HttpHeaders().apply {
            add(HeaderHelper.OPERATOR_REGION, regionId.toString())
        }
        // 如果不传入年份，就使用当年的
        val y = year ?: LocalDateTime.now().year
//        val orgData = applicationConfigHelper.getOrgByRegionId(label, regionId)
        val levelOneOrgs = openService.getCorps(header).toMutableList()
//        // 添加顶级组织的单位id
//        levelOneOrgs.add(openService.findOrgById(orgData.orgId!!, header)?.also {
//            // 补全信息
//            it.orgId = orgData.orgId
//        })
        levelOneOrgs.forEach {
            // 为每个单位创建两条MeetingWorkPointEntity数据（上下半年）
            // 先查询当前年有这个单位的数据没
            if (it.orgId == null) {
                log.warn("单位${it.name}的orgId为空，跳过")
                return@forEach
            }
            val list = meetingWorkPointRepository.findByOwnerIdAndYear(it.orgId, y)
            // 获取上半年和下半年的数据，如果存在就不管了，如果不存在，需要创建新的
//            val halfYear = listOf(1, 2)
//            val halfYear = listOf(1)
//            halfYear.forEach { h ->
            if (list.none { l -> l.ownerId == it.orgId }) {
//                    log.debug("------->{}", it)
                val entity = MeetingWorkPointEntity()
                entity.ownerId = it.orgId
                entity.ownerName = it.name ?: "未知单位"
                entity.orgId = -999 // it.orgId 初始化的时候为0
                entity.year = y
//                    entity.halfYear = h
                entity.createUser = -999
                entity.createTime = LocalDateTime.now()
                meetingWorkPointRepository.save(entity)
            }
//            }
        }
    }

    /**
     * 根据条件获取工作要点列表,根据组织id判断查看的单位，为3看全部，其他的只能看对应的owner_id
     * @param form MeetingWorkPointForm
     * @param page Int 页码
     * @param pageSize Int 每页条数
     * @param httpHeaders HttpHeaders
     * @param header HeaderHelper.SysHeader
     * @return Page<MeetingWorkPointEntity>
     */
    fun listByQuery(
        form: MeetingWorkPointForm,
        page: Int,
        pageSize: Int,
        httpHeaders: HttpHeaders,
        sysHeader: HeaderHelper.SysHeader
    ): Page<MeetingWorkPointEntity> {
        // 通过from表单的条件，生成查询条件，获取对应的MeetingWorkPointEntity
        // 通过Specification构建查询条件
        val spec = Specification<MeetingWorkPointEntity> { root, _, criteriaBuilder ->
            val predicates: MutableList<Predicate> = mutableListOf()
            // 可以添加更多的条件，例如 criteriaBuilder.and() 或 criteriaBuilder.or()
            // 添加默认条件 where 1 = 1
            predicates.add(criteriaBuilder.equal(criteriaBuilder.literal(1), 1))
            // 如果不是3查询的 //  这里应该要去获取顶级组织id
            if (sysHeader.uoid != 3L && sysHeader.oid != 3L) {
                predicates.add(criteriaBuilder.equal(root.get<Long>("ownerId"), form.ownerId))
            }
            // 单位名称
            if (form.ownerName != null && form.ownerName?.isNotEmpty() == true) {
                predicates.add(criteriaBuilder.like(root.get("ownerName"), "%${form.ownerName}%"))
            }
            // 文件名称
            if (form.fileName != null && form.fileName?.isNotEmpty() == true) {
                predicates.add(criteriaBuilder.like(root.get("fileName"), "%${form.fileName}%"))
            }
            // 年度
            if (form.year != null) {
                predicates.add(criteriaBuilder.equal(root.get<Int>("year"), form.year))
            }
            // 上下半年
            if (form.halfYear != null) {
                predicates.add(criteriaBuilder.equal(root.get<Int>("halfYear"), form.halfYear))
            }
            // 日期
            if (form.publishTime != null && form.publishTime?.size == 2) {
                predicates.add(
                    criteriaBuilder.between(
                        root.get("publishTime"),
                        LocalDateTimeUtils.toLocalDate(form.publishTime?.get(0) ?: "2000-01-01"),
                        LocalDateTimeUtils.toLocalDate(form.publishTime?.get(1) ?: "2900-12-31"),
                    )
                )
            }
            // 是否上传,通过附件是否存在
            if (form.status != null) {
                if (form.status == 1) {
                    predicates.add(criteriaBuilder.isNotNull(root.get<String>("attachment")))
                } else {
                    predicates.add(criteriaBuilder.isNull(root.get<String>("attachment")))
                }
            }

            criteriaBuilder.and(*predicates.toTypedArray())
        }
        return meetingWorkPointRepository.findAll(
            spec,
            PageRequest.of(page - 1, pageSize, Sort.by("updateTime").descending())
        )
    }

    /**
     * 通过id更新工作要点详情
     * @param id Long
     * @param entity MeetingWorkPointEntity
     */
    fun updateById(
        id: Long,
        entity: MeetingWorkPointEntity,
        sysHeader: HeaderHelper.SysHeader
    ): MeetingWorkPointEntity {
        val temp = meetingWorkPointRepository.findById(id).orElseThrow {
            ApiException(
                "工作要点不存在",
                Result<Any>(
                    errors,
                    9404,
                    HttpStatus.OK.value(),
                    "对应的工作要点"
                )
            )
        }
//        val orgData = applicationConfigHelper.getOrgByRegionId(label, sysHeader.regionId)
        // uoid = 3 或者 oid = 3 以外，需要判断 temp.ownerId != entity.ownerId

        if (sysHeader.uoid != 3L && sysHeader.oid != 3L && temp.ownerId != entity.ownerId) {
            throw ApiException(
                "更新的时候，单位id不对应" + temp.ownerId + "!=" + entity.ownerId,
                Result<Any>(
                    errors,
                    3202,
                    HttpStatus.OK.value()
                )
            )
        }
        entity.id = temp.id
        entity.orgId = sysHeader.uoid ?: sysHeader.oid
        entity.createTime = temp.createTime
        entity.createUser = temp.createUser
        entity.updateTime = LocalDateTime.now()
        entity.updateUser = sysHeader.userId
        if (sysHeader.uoid == 3L || sysHeader.oid == 3L) {
            entity.ownerId = temp.ownerId
            entity.ownerName = temp.ownerName
        }
        return meetingWorkPointRepository.save(entity)
    }

    fun findById(id: Long): MeetingWorkPointEntity {
        return meetingWorkPointRepository.findById(id).orElseThrow {
            ApiException(
                "工作要点不存在",
                Result<Any>(
                    errors,
                    9404,
                    HttpStatus.OK.value(),
                    "对应的工作要点"
                )
            )
        }
    }

}