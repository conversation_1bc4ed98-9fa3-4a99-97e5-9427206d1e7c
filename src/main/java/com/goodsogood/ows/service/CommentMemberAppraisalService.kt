package com.goodsogood.ows.service

import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import com.goodsogood.ows.common.Config
import com.goodsogood.ows.common.Constant
import com.goodsogood.ows.common.pojo.MeetingPushParam
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.configuration.CommentConfig
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.mapper.MeetingCommentMemberAppraisalMapper
import com.goodsogood.ows.mapper.MeetingCommentMemberMapper
import com.goodsogood.ows.model.db.CommentRatingEnum
import com.goodsogood.ows.model.db.MeetingCommentMemberAppraisalEntity
import com.goodsogood.ows.model.db.MeetingCommentMemberEntity
import com.goodsogood.ows.model.vo.*
import com.goodsogood.ows.utils.redisLock.RedisLockUtil
import org.apache.commons.lang.StringUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import tk.mybatis.mapper.entity.Example
import java.net.URLEncoder
import java.time.LocalDateTime
import java.util.*

@Service
class CommentMemberAppraisalService(
    @Autowired val errors: Errors,
    @Autowired val redisTemplate: StringRedisTemplate,
    @Autowired val commentConfig: CommentConfig,
    @Autowired val thirdService: ThirdService,
    @Autowired val commentService: CommentService,
    @Autowired val openService: OpenService,
    @Autowired val commentMemberService: CommentMemberService,
    @Autowired val meetingCommentMemberMapper: MeetingCommentMemberMapper,
    @Autowired val meetingCommentMemberAppraisalMapper: MeetingCommentMemberAppraisalMapper
) {

    private val log = LoggerFactory.getLogger(CommentMemberAppraisalService::class.java)

    /**
     * 查询民主评议互评列表
     * @param queryVO
     * @param headers
     */
    fun getMemberList(queryVO: MemberAppraisalQueryVO, headers: HttpHeaders): Page<CommentMemberAppraisalResultForm>? {
        val header = HeaderHelper.buildMyHeader(headers)
        val userId = header.userId
        log.debug("查询民主评议互评列表 -> queryVO:[${queryVO}] header:[${header}]")
        // type = 1时, 代表普通党员查询列表，可以查看未提交的数据， type = 2时，代表管理员查询列表，只能查看普通党员已提交的数据
        // 判断民主评议是否开启
        val commentEntity = commentService.getMeetingComment(queryVO.commentId)
        if (commentEntity != null && commentEntity.status > 0) {
            if (queryVO.commentMemberId != null) {
                val pages = PageHelper.startPage<CommentMemberAppraisalResultForm>(queryVO.page, queryVO.pageSize)
                    .doSelectPage<CommentMemberAppraisalResultForm> {
                        meetingCommentMemberMapper.getCommentMember(queryVO.commentId, queryVO.commentMemberId)
                    }
                val appraisalList = this.meetingCommentMemberAppraisalMapper.getMemberAppraisalList(
                    queryVO.commentId,
                    queryVO.commentMemberId,
                    queryVO.type
                )
                pages.forEach { page ->
                    appraisalList.forEach { appraisal ->
                        if (page.userId == appraisal.userId) {
                            page.rating = appraisal.rating
                            page.commentMemberAppraisalId = appraisal.commentMemberAppraisalId
                        }
                    }
                    page.userId = null
                    page.phone = null
                    page.userName = null
                }
                return pages
            } else {
                return PageHelper.startPage<CommentMemberAppraisalResultForm>(queryVO.page, queryVO.pageSize)
                    .doSelectPage {
                        this.meetingCommentMemberAppraisalMapper.getMemberAppraisalListBySelf(
                            queryVO.commentId,
                            queryVO.type,
                            userId
                        )
                    }
            }
        }
        return null
    }

    /**
     * 查询民主评议互评详情
     * @param memberAppraisalId
     */
    fun getMemberAppraisal(memberId: Long?, memberAppraisalId: Long?, headers: HttpHeaders): MemberAppraisalInfoForm {
        val infoForm = MemberAppraisalInfoForm()
        val header = HeaderHelper.buildMyHeader(headers)
        if (memberAppraisalId == null) {
            val memberEntity = meetingCommentMemberMapper.selectByPrimaryKey(memberId)
            if (memberEntity != null) {
                infoForm.commentUserId = memberEntity.userId
                infoForm.commentUserName = memberEntity.userName
                val appraisalInfo = selectMemberAppraisalInfo(memberId, header.userId)
                if (appraisalInfo.commentMemberAppraisalId != null) {
                    infoForm.commentMemberAppraisalId = appraisalInfo.commentMemberAppraisalId
                    infoForm.rating = appraisalInfo.appraisalRating
                    infoForm.content = appraisalInfo.appraisalContent
                }
            }
        } else {
            val appraisalEntity = meetingCommentMemberAppraisalMapper.selectByPrimaryKey(memberAppraisalId)
            if (appraisalEntity != null) {
                val memberEntity = meetingCommentMemberMapper.selectByPrimaryKey(appraisalEntity.commentMemberId)
                infoForm.commentMemberAppraisalId = appraisalEntity.commentMemberAppraisalId
                infoForm.commentUserId = memberEntity.userId
                infoForm.commentUserName = memberEntity.userName
                infoForm.appraisalUserId = appraisalEntity.userId
                infoForm.appraisalUserName = appraisalEntity.userName
                infoForm.rating = appraisalEntity.appraisalRating
                infoForm.content = appraisalEntity.appraisalContent
            }
        }
        return infoForm
    }

    /**
     * 新增民主评议互评详情
     * @param form
     * @param headers
     */
    @Transactional
    fun editMemberAppraisal(form: MemberAppraisalForm, headers: HttpHeaders): String {
        val header = HeaderHelper.buildMyHeader(headers)
        // 接入Redis锁机制，防止重复新增标签
        val requestId = UUID.randomUUID().toString()
        val lockKey: String = Config.COMMENT_MEMBER_APPRAISAL_LOCK_PREFIX + header.userId
        try {
            var lock: Boolean =
                RedisLockUtil.tryGetDistributedLock(redisTemplate, lockKey, requestId, Config.SCHEDULER_LOCK_EXPIRE)
            log.debug("是否拿到锁：lock -> [{}]", lock)
            // 判断是否获取锁
            if (!lock) {
                lock = RedisLockUtil.tryGetLock(redisTemplate, lockKey, requestId, Config.SCHEDULER_LOCK_EXPIRE)
            }
            log.debug("获取锁： requestId -> [{}]", requestId)
            // 执行具体逻辑
            if (lock) {
                log.debug("新增民主评议互评详情 -> form:[${form}] header:[${header}]")
                val memberEntity = meetingCommentMemberMapper.selectByPrimaryKey(form.commentMemberId)
                if (header.userId == memberEntity.userId) {
                    throw ApiException(
                        "不能对自己进行评价",
                        Result<Any>(errors, 2009, HttpStatus.OK.value(), "不能对自己进行评价")
                    )
                }
                if (memberEntity.politicalType != 1) {
                    throw ApiException(
                        "预备党员不参加民主评议",
                        Result<Any>(errors, 2009, HttpStatus.OK.value(), "预备党员不参加民主评议")
                    )
                }
                // 判断当前登录人是否可以互评
                meetingCommentMemberMapper.getCommentMemberByUserId(
                    commentId = memberEntity.commentId,
                    userId = header.userId
                ) ?: throw ApiException(
                    "你不是当前组织所属成员，不能评价",
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), "你不是当前组织所属成员，不能评价")
                )
                commentService.isEdit(memberEntity.commentId)
                val entity = selectMemberAppraisalInfo(form.commentMemberId, header.userId)
                val baseList = thirdService.findUserInfoByKey(header.userId, header.regionId)
                if (!baseList.isNullOrEmpty()) {
                    val userInfoBase = baseList[0]
                    entity.userId = header.userId
                    entity.userName = userInfoBase.userName
                    entity.phone = userInfoBase.phone
                    entity.phoneSecret = userInfoBase.phoneSecret
                    entity.appraisalRating = form.rating
                    entity.appraisalContent = if (form.content.isNullOrBlank()) {
                        CommentRatingEnum.getCommentRatingEnum(form.rating)?.value
                    } else {
                        form.content
                    }
                    entity.isDraft = 1
                    if (entity.commentMemberAppraisalId == null) {
                        entity.commentMemberId = form.commentMemberId
                        entity.createTime = LocalDateTime.now()
                        entity.createUser = header.userId
                        meetingCommentMemberAppraisalMapper.insert(entity)
                    } else {
                        // 前端传入的id与数据库id是否一致
                        if (form.commentMemberAppraisalId != null &&
                            entity.commentMemberAppraisalId != form.commentMemberAppraisalId
                        ) {
                            throw ApiException(
                                "前端传入的id与数据库id不一致",
                                Result<Any>(errors, 2009, HttpStatus.OK.value(), "保存失败，数据异常")
                            )
                        }
                        entity.updateTime = LocalDateTime.now()
                        entity.lastChangeUser = header.userId
                        meetingCommentMemberAppraisalMapper.updateByPrimaryKey(entity)
                    }
                } else {
                    throw ApiException(
                        "调取用户中心，获取人员信息失败",
                        Result<Any>(errors, 2009, HttpStatus.OK.value(), "保存失败，查询人员信息错误")
                    )
                }
                // 创建自评互评表
                commentService.createAppraisal(memberEntity.commentId!!, headers)
            } else {
                log.error("长时间没拿到锁 requestId -> [{}]", requestId)
            }
        } catch (e: Exception) {
            log.error("【新增民主评议互评详情】报错, 错误信息", e)
        } finally {
            // 释放锁
            log.debug(
                "执行完毕, 消耗时间 : lockKey -> [{}], requestId -> [{}]",
                lockKey,
                requestId
            )
            if (!StringUtils.isEmpty(lockKey) && !StringUtils.isEmpty(requestId)) {
                log.debug(
                    "执行完毕，释放锁: lockKey -> [{}], requestId -> [{}]",
                    lockKey,
                    requestId
                )
                RedisLockUtil.releaseDistributedLock(redisTemplate, lockKey, requestId)
            }
        }
        return Constant.SUCCESS
    }

    fun batchInputAppraisal(batchForm: MemberAppraisalBatchForm, headers: HttpHeaders): String {
        log.debug("批量新增民主评议互评 -> vo:[{}]", batchForm)
        batchForm.memberIds.forEach { memberId ->
            val form = MemberAppraisalForm()
            form.commentMemberId = memberId
            form.rating = batchForm.rating
            form.content = batchForm.content
            editMemberAppraisal(form, headers)
        }
        return Constant.SUCCESS
    }

    /**
     * 提交互评
     * @param commentId
     * @param headers
     */
    fun submit(commentId: Long, headers: HttpHeaders): String {
        val header = HeaderHelper.buildMyHeader(headers)
        log.debug("提交互评 -> commentId:[${commentId}] header:[${header}]")
        // 查询民主评议党员表数据
        val ex = Example(MeetingCommentMemberEntity::class.java)
        val criteria = ex.createCriteria()
        criteria.andEqualTo("commentId", commentId)
        criteria.andEqualTo("politicalType", 1)
        val memberList = meetingCommentMemberMapper.selectByExample(ex)
        val memberIdList = memberList.map { it.commentMemberId }
        // 查询民主评议互评表数据
        val example = Example(MeetingCommentMemberAppraisalEntity::class.java)
        val criteria1 = example.createCriteria()
        criteria1.andIn("commentMemberId", memberIdList)
            .andEqualTo("userId", header.userId)
        val list = meetingCommentMemberAppraisalMapper.selectByExample(example)
        // 修改数据为提交状态
        list.map {
            it.isDraft = 1
            it.updateTime = LocalDateTime.now()
            it.lastChangeUser = header.userId
        }
        meetingCommentMemberAppraisalMapper.insertList(list)
        // 创建自评互评表
        commentService.createAppraisal(commentId, headers)
        return Constant.SUCCESS
    }

    /**
     * 查询互评数据
     * @param commentMemberId
     * @param userId
     */
    private fun selectMemberAppraisalInfo(commentMemberId: Long?, userId: Long): MeetingCommentMemberAppraisalEntity {
        val ex = Example(MeetingCommentMemberAppraisalEntity::class.java)
        val criteria = ex.createCriteria()
        criteria.andEqualTo("commentMemberId", commentMemberId)
            .andEqualTo("userId", userId)
        val entities = meetingCommentMemberAppraisalMapper.selectByExample(ex)
        return if (entities.size == 0) {
            MeetingCommentMemberAppraisalEntity()
        } else if (entities.size == 1) {
            entities[0]
        } else {
            throw ApiException(
                "数据库存在重复互评数据commentMemberId:[${commentMemberId}],userId:[${userId}]",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "存在重复互评数据, 请联系管理员")
            )
        }
    }

    /**
     * 党员互评统计
     */
    fun statisticalAppraisal(commentMemberId: Long): CommentStatisticalDataForm {
        val commentMember = meetingCommentMemberMapper.selectByPrimaryKey(commentMemberId)
        if (commentMember != null) {
            val size = commentMember.commentId?.let { commentMemberService.getMemberListByCommentId(it).size }
            if (size != null) {
                val statistical = meetingCommentMemberAppraisalMapper.appraisalStatistical(commentMemberId)
                statistical.other =
                    size - 1 - statistical.excellent - statistical.qualified - statistical.basicQualified - statistical.unqualified
                return statistical
            }
        }
        return CommentStatisticalDataForm()
    }

    fun getUserNoAppraisal(userId: Long?, commentId: Long): Int {
        val memberList = commentMemberService.getMemberListByCommentId(commentId)
        val memberIds = memberList.map { it.commentMemberId }.toMutableList()
        val size = memberList.size
        return size - memberStatistical(userId, memberIds)
    }

    /**
     * 查询未完成互评的人员
     */
    fun memberNoAppraisal(commentId: Long): MutableList<MeetingCommentMemberEntity> {
        val memberList = commentMemberService.getMemberListByCommentId(commentId)
        val memberIds = memberList.map { it.commentMemberId }.toMutableList()
        val size = memberList.size - 1
        val resultList = mutableListOf<MeetingCommentMemberEntity>()
        memberList.forEach {
            if (memberStatistical(it.userId, memberIds) < size) {
                resultList.add(it)
            }
        }
        return resultList
    }

    fun memberStatistical(userId: Long?, memberIds: MutableList<Long?>): Int {
        val ex = Example(MeetingCommentMemberAppraisalEntity::class.java)
        val criteria = ex.createCriteria()
        criteria.andEqualTo("userId", userId)
        criteria.andIn("commentMemberId", memberIds)
        return meetingCommentMemberAppraisalMapper.selectCountByExample(ex)
    }

    fun sendMsg(commentId: Long, headers: HttpHeaders): String {
        val header = HeaderHelper.buildMyHeader(headers)
        val comment = commentService.getMeetingComment(commentId)
        if (comment != null) {
            val userIds = memberNoAppraisal(commentId)
            val msgList = mutableListOf<SendCommentAppraisalMsg>()
            val url = URLEncoder.encode(commentConfig.msg.noAppraisal.url)
            userIds.forEach {
                val msg = SendCommentAppraisalMsg()
                msg.userId = it.userId
                msg.userName = it.userName
                msg.num = getUserNoAppraisal(it.userId, commentId) - 1
                msg.targetUrl = String.format(commentConfig.msg.url, url, comment.orgId)
                msgList.add(msg)
            }
            log.debug("发送消息 -> $msgList")
            openService.sendNotice(
                msgList,
                commentConfig.msg.noAppraisal.templateId,
                commentConfig.msg.channel,
                MeetingPushParam(1),
                header.regionId
            )
        }
        return Constant.SUCCESS
    }
}