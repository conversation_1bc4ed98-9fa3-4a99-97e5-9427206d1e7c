package com.goodsogood.ows.service;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.github.pagehelper.Page;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.common.pojo.MeetingPushParam;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.db.MeetingOrgCommendPenalizeEntity;
import com.goodsogood.ows.model.db.UserCommendPenalizeEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.push.PushConstant;
import com.goodsogood.ows.push.normal.NormalMessagePushRequest;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.ListUtils;
import com.goodsogood.ows.utils.TimeUtils;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 对外接口
 *
 * <AUTHOR>
 * @date 2019-05-14 9:32
 * @since 1.0.3
 **/
@Service
@Log4j2
public class OpenService {

    @Value("${tog-services.sas}")
    @NotNull
    private String sas;

    @Value("${tog-services.user-center}")
    @NotNull
    private String user;

    @Value("${tog-services.push-center}")
    @NotNull
    private String pushCenter;

    @Value("${tog-services.credit-center}")
    @NotNull
    private String creditCenter;

    @Value("${remote-spider.url-host}")
    private String spiderServerName;

    @Value("${remote-spider.protocol}")
    private String spiderProtocol;

    private final RestTemplate generalRestTemplate;
    private final RestTemplate restTemplate;
    private final SimpleApplicationConfigHelper applicationConfigHelper;

    //党小组
    public static final String ORG_GROUP = "org_group";
    //届次（支委会）
    public static final String ORG_PERIOD = "org_period";

    //推送模式
    private final ThreadLocal<Integer> pushMpdel = new ThreadLocal<>();

    @Autowired
    public OpenService(RestTemplate generalRestTemplate, RestTemplate restTemplate, SimpleApplicationConfigHelper applicationConfigHelper) {
        this.generalRestTemplate = generalRestTemplate;
        this.restTemplate = restTemplate;
        this.applicationConfigHelper = applicationConfigHelper;
    }


    /**
     * 获取所有的考核单位
     *
     * @return
     */
    public List<Long> getCheckOrgIds(Long regionId) {
        List<Long> list = new ArrayList<>();
        //不分页，一次性查出所有的考核单位数据，考核单位数据量不大
        //is_page 1-是 2-否，默认否
        int i = 1;
        do {
            String url = String.format("http://%s/org/find-all-eval-org?is_page=%d&page_num=%d&page_size=%d",
                    user, 1, i, 500);
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.add("_region_id", regionId + "");

                Page<OrganizationBase> userPage = RemoteApiHelper.get(restTemplate, url, headers,
                        new TypeReference<Result<Page<OrganizationBase>>>() {
                        });
                if (userPage == null || userPage.size() == 0) {
                    log.debug("调用远程获取所有的考核单位为空，退出查询！");
                    break;
                }
                //填充返回的数据
                userPage.forEach(user -> {
                    list.add(user.getOrgId());
                });
            } catch (Exception e) {
                log.error("调用远程获取所有的考核单位服务失败, 错误内容:", e);
            }
            i++;
        } while (true);

        return list;
    }

    /**
     * 获取有届次的所有组织id
     * 获取有党小组的所有组织id
     * <p>
     * 满足当前时间有效
     *
     * @param checkOrgIdList 考核组织
     * @return
     */
    public Map<String, List<Long>> getOrgPeriodIdsAndOrgGroup(List<Long> checkOrgIdList) {
        Map<String, List<Long>> map = new HashMap<>();
        //申明党小组
        List<Long> orgGroup = new ArrayList<>();
        //申明届次（支委会）
        List<Long> orgPeriod = new ArrayList<>();
        //不分页，一次性查出所有的考核单位数据，考核单位数据量不大
        String url = String.format("http://%s/period/find-orgs", user);
        List<PeriodFindOrgsForm> param = new LinkedList<>();
        int i = 1;
        for (Long orgId : checkOrgIdList) {
            HttpHeaders headers = new HttpHeaders();
            String date = TimeUtils.getDate();
            try {
                PeriodFindOrgsForm form = new PeriodFindOrgsForm();
                form.setOrgId(orgId);
                form.setPeriodQueryTime(date);
                param.add(form);
                //为了保证读取接口不超时，分段进行查询
                if ((i % 50 == 0) || i == checkOrgIdList.size()) {
                    log.debug("调用远程获取所有的支委会和党小组参数 param = {}", param);
                    List<PeriodFindOrgsResultForm> rs = RemoteApiHelper.post(restTemplate, url, param, headers, new
                            TypeReference<Result<List<PeriodFindOrgsResultForm>>>() {
                            });
                    if (rs == null || rs.size() == 0) {
                        log.debug("调用远程获取所有的支委会和党小组，退出查询！");
                        break;
                    }
                    //填充返回的数据
                    rs.forEach(resultForm -> {
                        if (resultForm != null) {
                            log.debug("查询党小组和支委会返回遍历结果：orgId = {}, resultForm = {}",
                                    resultForm.getOrgId(), resultForm);
                            //判断是否有支委会
                            if (resultForm.getPeriodSize() != null && resultForm.getPeriodSize() != 0) {
                                orgPeriod.add(resultForm.getOrgId());
                            }
                            //判断是否有党小组
                            if (resultForm.getCreateOrgGroups() != null && resultForm.getCreateOrgGroups() != 0) {
                                orgGroup.add(resultForm.getOrgId());
                            }
                        } else {
                            log.debug("查询党小组和支委会返回为空！！！");
                        }
                    });
                    param.clear();
                }
                i++;
            } catch (Exception e) {
                i++;
                log.error("远程获取有 党小组 支委会 参数 i = {}, param = {}", i, param);
                log.error("远程获取有 党小组 支委会 数量, 错误内容:", e);
            }
        }

        log.debug("远程获取有 党小组 组织数量，size = {}， data = {}", orgGroup.size(), orgGroup);
        log.debug("远程获取有 支委会 的组织数量，size = {}， data = {}", orgPeriod.size(), orgPeriod);
        //封装返回结果
        map.put(ORG_GROUP, orgGroup);
        map.put(ORG_PERIOD, orgPeriod);
        return map;
    }

    /**
     * 调用消息中心，发送模板消息
     *
     * @param userList
     * @param templateId
     * @param channelType
     */
    public <T extends SendMsgForm> void sendNotice(List<T> userList, int templateId, int channelType, MeetingPushParam pushParam, Long regionId) {
        if (pushParam == null) {
            pushParam = new MeetingPushParam();
        }
        if (ListUtils.isEmpty(userList)) {
            log.debug("通知用户为空，退出发送");
            return;
        }
        List<SendMsgForm> dataList = new ArrayList<>();
        Long pushId = null;
        //由于消息中心每次只能消费100条数据，所以需要对推送的数据做一次切割
        int i = 1;
        for (SendMsgForm vo : userList) {
            //封装消息中心需要的数据
            dataList.add(vo);
            //按消息中心最大条数切割，不足的直接全部
            if ((i % 100 == 0) || i == userList.size()) {
                MsgTemplateForm msg = MsgTemplateForm.builder()
                        .data(dataList)
                        .templateId(templateId)
                        .channelType(channelType)
                        .source("meeting")
                        .pushModel(pushParam.getPushModel())
                        .build();
                if (pushId != null) {
                    msg.setPushId(pushId);
                }
                log.debug("推送报文：人员 = {}", msg.getData());
                String url = String.format("http://%s/global/push/diff", pushCenter);
                HttpHeaders headers = new HttpHeaders();
                headers.set(HeaderHelper.OPERATOR_REGION, String.valueOf(regionId));
                restTemplate.setErrorHandler(new ClientExceptionHandler());
                // 调用远程方法
                try {
                    Long rs = RemoteApiHelper.post(restTemplate, url, msg, headers, new TypeReference<Result<Long>>() {
                    });
                    pushId = rs;
                    log.debug(">>>> rs = {}", rs);
                } catch (Exception e) {
                    log.error("调用远程发送通知服务失败, 错误内容:", e);
                }
                dataList.clear();
            }
            //标志位递增
            i++;
        }

    }

    /**
     * 调用用户中心，获取组织的所有管理员
     *
     * @param regionId  区县编号
     * @param orgIdList
     * @param targetUrl 跳转地址
     * @return
     */
    public List<SendMsgForm> getAdmin(Long regionId, List<Long> orgIdList, String targetUrl) {
        List<SendMsgForm> list = new ArrayList<>();
        //不分页，一次性查出所有的考核单位数据，考核单位数据量不大
        String url = String.format("http://%s/org/user/find-org-mgr-by-list?is_page=2", user);
        Map<String, Object> ids = new HashMap<>();
        HttpHeaders headers = new HttpHeaders();
        headers.set(HeaderHelper.OPERATOR_REGION, String.valueOf(regionId));
        ids.put("id_list", orgIdList);
        try {
            Page<UserInfoBase> userPage = RemoteApiHelper.post(restTemplate, url, ids, headers,
                    new TypeReference<Result<Page<UserInfoBase>>>() {
                    });
            if (userPage == null || userPage.size() == 0) {
                log.debug("调用远程获取管理员为空，退出查询！");
                return list;
            }
            //填充返回的数据
            for (UserInfoBase user : userPage) {
                SendMsgForm vo = new SendMsgForm();
                vo.setUserId(user.getUserId());
                vo.setOrgName(user.getOrgName());
                if (StringUtils.isNotBlank(targetUrl)) {
                    vo.setTargetUrl(targetUrl + "&sel_org_id=" + user.getOrgId());
                }
                list.add(vo);
                log.debug("接收通知的管理员 user = {}", vo);
            }
        } catch (Exception e) {
            log.error("调用远程获取所有的管理员服务失败, 错误内容:", e);
        }
        return list;
    }

    /**
     * 调用用户中心，获取组织的书记
     *
     * @param regionId  区县编号
     * @param orgIdList
     * @param targetUrl 跳转地址
     * @return
     */
    public List<SendMsgForm> getLeader(Long regionId, List<Long> orgIdList, String targetUrl) {
        List<SendMsgForm> list = new ArrayList<>();
        String url = String.format("http://%s/period/find-leader-by-org-id", user);
        Map<String, Object> ids = new HashMap<>();
        HttpHeaders headers = new HttpHeaders();
        headers.set(HeaderHelper.OPERATOR_REGION, String.valueOf(regionId));
        ids.put("id_list", orgIdList);
        try {
            Page<UserInfoBase> userPage = RemoteApiHelper.post(restTemplate, url, ids, headers,
                    new TypeReference<Result<Page<UserInfoBase>>>() {
                    });
            if (userPage == null || userPage.size() == 0) {
                log.debug("调用远程获取组织书记为空，退出查询！");
                return list;
            }
            //填充返回的数据
            for (UserInfoBase user : userPage) {
                SendMsgForm vo = new SendMsgForm();
                vo.setUserId(user.getUserId());
                vo.setOrgName(user.getOrgName());
                if (StringUtils.isNotBlank(targetUrl)) {
                    vo.setTargetUrl(targetUrl + "&sel_org_id=" + user.getOrgId());
                }
                list.add(vo);
                log.debug("接收通知的组织书记 user = {}", vo);
            }
        } catch (Exception e) {
            log.error("调用远程获取所有的组织书记服务失败, 错误内容:", e);
        }
        return list;
    }

    /**
     * 远程获取sas的统计结果
     *
     * @param form sas统计结果的查询封装条件
     * @return
     */
    public SasOrgLifeReponse getSasCount(long regionId, SasOrgLifeConditionForm form) {
        String url = String.format("http://%s/sas/org-life/list", sas);
        // 调用远程方法
        try {
            HeaderHelper.SysHeader sysHeader = RestTemplateHelper.getRegionIdLogHeader(regionId);
            SasOrgLifeReponse rs = RestTemplateHelper.post(sysHeader, url, form, new TypeReference<Result<SasOrgLifeReponse>>() {
            });
            if (rs != null) {
                log.debug("定时发送任务：查询数据条数：" + rs.getResultPage().getData().size());
            }
            return rs;
        } catch (Exception e) {
            log.error("调用远程获取sas的统计结果服务失败, 错误内容:", e);
        }
        return null;
    }

    /**
     * 获取组织下级列表
     *
     * @param orgId
     * @param isInclude
     * @return java.util.List<com.goodsogood.ows.model.vo.OrgInfoForm>
     * <AUTHOR>
     * @date 2020/1/3
     */
    public List<OrgInfoForm> getOrgInfoList(Long orgId, Integer isInclude) {
        List<OrgInfoForm> orgInfoFormList = new ArrayList<>();
        // 远程地址
        String url = String.format("http://%s/org/find-all-child-org?org_id=%s&is_include=%s", user, orgId, isInclude);
        HttpHeaders headers = new HttpHeaders();
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            orgInfoFormList = RemoteApiHelper.get(this.restTemplate, url, headers, new TypeReference<Result<List<OrgInfoForm>>>() {
            });
        } catch (Exception e) {
            log.error("调用远程获取user的服务失败, 错误内容:", e);
        }

        return orgInfoFormList;
    }

    /**
     * 获取组织党员数量列表
     *
     * @param orgId
     * @return java.util.List<com.goodsogood.ows.model.vo.OrgUserCountForm>
     * <AUTHOR>
     * @date 2020/1/3
     */
    public Long getPartyNumberByOrgId(Long orgId) {
        Long partyNumber = 0L;
        // 远程地址
        String url = String.format("http://%s/find-party-number-by-org?org_id=%s", user, orgId);
        HttpHeaders headers = new HttpHeaders();
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            partyNumber = RemoteApiHelper.get(this.restTemplate, url, headers, new TypeReference<Result<Long>>() {
            });
        } catch (Exception e) {
            log.error("调用远程获取user的服务失败, 错误内容:", e);
        }
        return partyNumber;
    }

    /**
     * 调用用户中心查询字典表
     *
     * @param code
     * @return java.util.List<com.goodsogood.ows.model.vo.OptionForm>
     * <AUTHOR>
     * @date 2020/1/7
     */
    public List<OptionForm> getOptionListByCode(String code) {
        List<OptionForm> optionList = new ArrayList<>();
        // 远程地址
        String url = String.format("http://%s/uc/op/list-child?code=%s", user, code);
        HttpHeaders headers = new HttpHeaders();
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            optionList = RemoteApiHelper.get(this.restTemplate, url, headers, new TypeReference<Result<List<OptionForm>>>() {
            });
            log.debug("调用用户中心查询字典表 结果: [{}]", JsonUtils.toJson(optionList));
        } catch (Exception e) {
            log.error("调用远程获取user的服务失败, 错误内容:", e);
        }
        return optionList;
    }

    /**
     * 获取组织所有上级
     *
     * @param orgId
     * @return java.util.List<com.goodsogood.ows.model.vo.OrgUserCountForm>
     * <AUTHOR>
     * @date 2020/1/3
     */
    public List<OrgNameResultForm> getPartyOrgListByOrgId(Long orgId, HttpHeaders headers) {
        List<OrgNameResultForm> resultList = new ArrayList<>();
        // 远程地址
        String url = String.format("http://%s/org/find-all-parent-org?org_id=%s", user, orgId);
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            resultList = RemoteApiHelper.get(this.restTemplate, url, headers, new TypeReference<Result<List<OrgNameResultForm>>>() {
            });
        } catch (Exception e) {
            log.error("调用远程获取user的服务失败, 错误内容:", e);
        }
        return resultList;
    }

    /**
     * 调用新增个人成长轨迹
     *
     * @param entity
     * @param files
     * @param headers
     */
    public void addUserHighlight(UserCommendPenalizeEntity entity, List<MeetingFileVO> files, HttpHeaders headers) {
        UserHighlightForm form = new UserHighlightForm();
        form.setUserId(form.getUserId());
        form.setType(1);
        // 奖励级别
        String level = "";
        if (!Constant.REWARD_LEVEL_OTHER.equals(entity.getLevel())) {
            final List<OptionForm> levelOption = this.getOptionListByCode(Constant.REWARD_LEVEL.toString());
            final OptionForm optionByLevel = this.getOptionByList(levelOption, entity.getLevel());
            if (Objects.nonNull(optionByLevel)) {
                level = optionByLevel.getOpValue();
            }
        }
        // 奖励名称
        String name = "";
        if (Constant.REWARD_NAME_USER_OTHER.equals(entity.getName())) {
            name = StringUtils.isNotBlank(entity.getContent()) ? entity.getContent() : "";
        } else {
            final List<OptionForm> nameOption = this.getOptionListByCode(Constant.REWARD_NAME_USER_NEW.toString());
            final OptionForm optionByName = this.getOptionByList(nameOption, entity.getName());
            if (Objects.nonNull(optionByName)) {
                name = optionByName.getOpValue();
            }
        }
        form.setDesc("获得" + level + name);
        form.setNodeTime(entity.getEffectiveTime());
        if (files != null && files.size() > 0) {
            String url = files.stream().map(MeetingFileVO::getPath).collect(Collectors.joining(","));
            form.setUrl(url);
        }
        log.debug("请求体 -> [{}]", form);
        // 远程地址
        String url = String.format("http://%s/person-center/user/highlight/add", user);
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            RemoteApiHelper.post(this.restTemplate, url, form, headers, new TypeReference<Result<Object>>() {
            });
        } catch (Exception e) {
            log.error("调用远程获取user的服务失败, 错误内容:", e);
        }
    }

    /**
     * 调用新增组织风采或轨迹
     *
     * @param entity
     * @param files
     * @param type    1.支部风采 2.支部发展史
     * @param headers
     */
    public void addOrgHighlight(MeetingOrgCommendPenalizeEntity entity, List<MeetingFileVO> files, int type, HttpHeaders headers) {
        BranchHighlightForm form = new BranchHighlightForm();
        form.setOrgId(entity.getOrgId());
        form.setType(type);
        // 奖励级别
        String level = "";
        if (!Constant.REWARD_LEVEL_OTHER.equals(entity.getLevel())) {
            final List<OptionForm> levelOption = this.getOptionListByCode(Constant.REWARD_LEVEL.toString());
            final OptionForm optionByLevel = this.getOptionByList(levelOption, entity.getLevel());
            if (Objects.nonNull(optionByLevel)) {
                level = optionByLevel.getOpValue();
            }
        }
        // 奖励名称
        String name = "";
        if (Constant.REWARD_NAME_ORG_OTHER.equals(entity.getName())) {
            name = StringUtils.isNotBlank(entity.getContent()) ? entity.getContent() : "";
        } else {
            final List<OptionForm> nameOption = this.getOptionListByCode(Constant.REWARD_NAME_ORG_NEW.toString());
            final OptionForm optionByName = this.getOptionByList(nameOption, entity.getName());
            if (Objects.nonNull(optionByName)) {
                name = optionByName.getOpValue();
            }
        }
        form.setDesc("获得" + level + name);
        form.setNodeTime(DateUtils.dateFormat(entity.getRatifyTime(), "yyyy-MM-dd"));
        if (files != null && files.size() > 0) {
            String url = files.stream().map(MeetingFileVO::getPath).collect(Collectors.joining(","));
            form.setUrl(url);
        }
        log.debug("请求体 -> [{}]", form);
        // 远程地址
        String url = String.format("http://%s/person-center/org/highlight/add", user);
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            RemoteApiHelper.post(this.restTemplate, url, form, headers, new TypeReference<Result<Object>>() {
            });
        } catch (Exception e) {
            log.error("调用远程获取user的服务失败, 错误内容:", e);
        }
    }

    /**
     * 组织积分发放
     *
     * @param orgId
     * @param operType
     * @param scoreType
     * @param score
     * @param explainTxt
     * @param headers
     */
    public void operateOrgCredit(Long orgId, Integer operType, Integer scoreType, Integer score, String explainTxt, HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        headers.set("access_key", "ows");

        final Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(sysHeader.getRegionId());
        final UUID token = UUID.randomUUID();
        OrgCreditRequestForm form = new OrgCreditRequestForm();
        form.setRegionId(sysHeader.getRegionId());
        form.setScoreOrgId(orgId);
        form.setOrgId(orgData.getOrgId());
        form.setScoreType(scoreType);
        form.setScore(score);
        form.setOperType(operType);
        form.setToken(token.toString().replaceAll("-", ""));
        form.setExplainTxt(explainTxt);
        form.setRemark("积分任务_获评“先进基层党组织”");
        log.debug("请求参数 -> [{}]", JsonUtils.toJson(form));
        // 远程地址
        String url = String.format("http://%s/score/consume/nocheck/org", creditCenter);
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            RemoteApiHelper.post(this.restTemplate, url, form, headers, new TypeReference<Result<Object>>() {
            });
        } catch (Exception e) {
            log.error("调用远程获取credit的服务失败, 错误内容:", e);
        }
    }

    /**
     * 个人积分发放
     *
     * @param userId
     * @param userName
     * @param operType
     * @param scoreType
     * @param score
     * @param explainTxt
     * @param headers
     */
    public void operateUserCredit(Long userId, String userName, Integer operType, Integer scoreType, Integer score, String explainTxt, HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        headers.set("access_key", "ows");
        final Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(sysHeader.getRegionId());
        final UUID token = UUID.randomUUID();
        UserCreditRequestForm form = new UserCreditRequestForm();
        form.setRegionId(sysHeader.getRegionId());
        form.setUserId(userId);
        form.setUserName(userName);
        form.setOrgId(orgData.getOrgId());
        form.setScoreType(scoreType);
        form.setScore(score);
        form.setOperType(operType);
        form.setToken(token.toString().replaceAll("-", ""));
        form.setExplainTxt(explainTxt);
        log.debug("请求参数 -> [{}]", JsonUtils.toJson(form));
        // 远程地址
        String url = String.format("http://%s/score/consume/nocheck/nopwd", creditCenter);
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            RemoteApiHelper.post(this.restTemplate, url, form, headers, new TypeReference<Result<Long>>() {
            });
        } catch (Exception e) {
            log.error("调用远程获取credit的服务失败, 错误内容:", e);
        }
    }

    public OptionForm getOptionByList(List<OptionForm> optionList, Integer key) {
        for (OptionForm option : optionList) {
            if (option.getOpKey().equals(key)) {
                return option;
            } else if (option.getChildOption() != null && option.getChildOption().size() > 0) {
                OptionForm form = this.getOptionByList(option.getChildOption(), key);
                if (Objects.nonNull(form.getOpKey())) {
                    return form;
                }
            }
        }
        return new OptionForm();
    }

    public List<Integer> getOptionKeyList(List<OptionForm> optionList, Integer key) {
        List<OptionForm> list = new ArrayList<>(optionList);
        List<Integer> keyList = new ArrayList<>();
        optionList.forEach(option -> this.getChildList(list, option));
        this.getKeyList(keyList, list, key);
        Collections.reverse(keyList);
        return keyList;
    }

    private void getChildList(List<OptionForm> optionList, OptionForm form) {
        List<OptionForm> childOption = form.getChildOption();
        if (CollectionUtils.isNotEmpty(childOption)) {
            optionList.addAll(childOption);
            for (OptionForm option : childOption) {
                this.getChildList(optionList, option);
            }
        }
    }

    private void getKeyList(List<Integer> keyList, List<OptionForm> list, Integer key) {
        for (OptionForm op : list) {
            if (op.getOpKey().equals(key)) {
                keyList.add(key);
                this.getKeyList(keyList, list, Integer.valueOf(op.getCode()));
            }
        }
    }

    /**
     * 新增你是我的荣耀
     *
     * @param userId  用户ID
     * @param type    1-奖惩登记，2-民主评议
     * @param time    时间
     * @param title   标题
     * @param remark  描述
     * @param path    图片地址
     * @param headers 请求头
     */
    @Async("gloryAsync")
    public void addMyGlory(Long userId, Integer type, LocalDateTime time, String title, String remark, String path, HttpHeaders headers) {
        final HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("新增你是我的荣耀 userId: {}, type: {}, time: {}, remark: {}, path: {}", userId, type, time, remark, path);
        final MyGloryForm gloryForm = new MyGloryForm(userId, type, time, title, remark, path);
        log.debug("请求参数 -> [{}]", JsonUtils.toJson(gloryForm));
        // 远程地址
        String url = String.format("http://%s/my-glory/add", user);
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        final HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set(HeaderHelper.OPERATOR_REGION, header.getRegionId().toString());
        try {
            RemoteApiHelper.post(this.restTemplate, url, gloryForm, httpHeaders, new TypeReference<Result<String>>() {
            });
        } catch (Exception e) {
            log.error("调用远程用户中心新增我的荣耀失败, 错误内容:", e);
        }
    }

    /**
     * 删除你是我的荣耀
     *
     * @param userId  用户ID
     * @param type    1-奖惩登记，2-民主评议
     * @param time    时间
     * @param title   标题
     * @param remark  描述
     * @param headers 请求头
     */
    @Async("gloryAsync")
    public void delMyGlory(Long userId, Integer type, LocalDateTime time, String title, String remark, HttpHeaders headers) {
        final HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("删除你是我的荣耀 userId: {}, type: {}, time: {}, title:{}, remark: {}", userId, type, time, title, remark);
        final DelGloryForm form = new DelGloryForm(userId, type, title, remark, time);
        log.debug("请求参数 -> [{}]", JsonUtils.toJson(form));
        // 远程地址
        String url = String.format("http://%s/my-glory/del-by-example", user);
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        final HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set(HeaderHelper.OPERATOR_REGION, header.getRegionId().toString());
        try {
            RemoteApiHelper.post(this.restTemplate, url, form, httpHeaders, new TypeReference<Result<String>>() {
            });
        } catch (Exception e) {
            log.error("调用远程用户中心新增我的荣耀失败, 错误内容:", e);
        }
    }


    /**
     * 获取所管理的机构id
     *
     * @return
     */
    public List<Long> getManageOrgIds(Long userId, Long regionId) {
        List<Long> list = new ArrayList<>();
        //不分页，一次性查出所有的考核单位数据，考核单位数据量不大
        //is_page 1-是 2-否，默认否

        String url = String.format("http://%s/org/find-manage-list?user_id=%d",
                user, userId);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("_region_id", regionId + "");
            List<ManageOrgForm> orgInfoList = RemoteApiHelper.get(restTemplate, url, headers,
                    new TypeReference<Result<List<ManageOrgForm>>>() {
                    });
            list = orgInfoList.stream().map(ManageOrgForm::getOrganizationId).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("调用远程获取所有的考核单位服务失败, 错误内容:", e);
        }
        return list;
    }


    /**
     * 根据手机号查询user_id
     *
     * @return
     */
    public List<UserInfoBase> getUserIdByPhone(List<String> phoneList, Long regionId) {
        //不分页，一次性查出所有的考核单位数据，考核单位数据量不大
        //is_page 1-是 2-否，默认否
        List<UserInfoBase> orgInfoList = null;
        String url = String.format("http://%s/org/fxx?user_id=%d",
                user, phoneList);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("_region_id", regionId + "");
            orgInfoList = RemoteApiHelper.get(restTemplate, url, headers,
                    new TypeReference<Result<List<UserInfoBase>>>() {
                    });
        } catch (Exception e) {
            log.error("调用远程获取所有的考核单位服务失败, 错误内容:", e);
        }
        return orgInfoList;
    }


    /**
     * 调用用户中心根据手机号和姓名匹配user_id,考虑分页
     *
     * @param headers
     * @param allUser
     * @return
     */
    public List<UserInfoBase> findUserIdByPhone(HttpHeaders headers, List<BatchForm> allUser) {
        log.debug("调用用户中心根据手机号和姓名匹配user_id" + JsonUtils.toJson(allUser));
        if (CollectionUtils.isNotEmpty(allUser)) {
            allUser.forEach(x -> {
                if (null == x.getPhone()) {
                    x.setPhone("");
                }
            });
        }
        List<UserInfoBase> userInfoList = null;
        String url = String.format("http://%s/org/user/find-user-by-batch",
                user);
        try {
            userInfoList = RemoteApiHelper.post(restTemplate, url, allUser, headers,
                    new TypeReference<Result<List<UserInfoBase>>>() {
                    });
        } catch (Exception e) {
            log.error("调用用户中心根据手机号和姓名匹配user_id, 错误内容:", e);
        }
        return userInfoList;
    }


    /**
     * 调用用户中心根据orgname获取orgId
     *
     * @param regionId
     * @param orgNames
     * @return
     */
    public List<OrganizationBase> findOrgIdByName(Long regionId, List<String> orgNames) {
        log.debug("调用用户中心根据orgname获取orgId");
        List<OrganizationBase> orgInfoList = null;
        Map<String, List<String>> map = new HashMap<>();
        map.put("org_name", orgNames);
        String url = String.format("http://%s/org/user/find-org-by-precise",
                user);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("_region_id", regionId + "");
            orgInfoList = RemoteApiHelper.post(restTemplate, url, map, headers,
                    new TypeReference<Result<List<OrganizationBase>>>() {
                    });
        } catch (Exception e) {
            log.error("调用用户中心根据orgname获取orgId, 错误内容:", e);
        }
        return orgInfoList;
    }

    /**
     * 获取组织信息
     *
     * @param orgId
     * @param headers
     * @return org
     */
    public OrganizationBase findOrgById(Long orgId, HttpHeaders headers) {
        OrganizationBase organizationBase = new OrganizationBase();
        // 远程调用地址
        String url = String.format("http://%s/find-org-by-id?org_id=%s&timestamp=%d", user, orgId, System.currentTimeMillis());
        log.debug("请求用户中心URL -> [{}]", url);
        // 调用远程方法
        try {
            organizationBase = RemoteApiHelper.getAndResult(restTemplate, url, headers, new TypeReference<Result<OrganizationBase>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, (Result) null);
        }
        return organizationBase;
    }

    /**
     * 批量获取组织信息
     *
     * @param orgIds
     * @param headers
     * @return map of org
     */
    public List<OrganizationBase> findOrgByIds(List<Long> orgIds, HttpHeaders headers) {
        List<OrganizationBase> organizationList = new ArrayList<>();
        // 远程调用地址
        String url = String.format("http://%s/find-org-by-ids", user, orgIds);
        // 调用远程方法
        try {
            organizationList = RemoteApiHelper.postAndResult(restTemplate, url, orgIds, headers, new TypeReference<Result<List<OrganizationBase>>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, (Result) null);
        }
        return organizationList;
    }

    /**
     * 获取当前组织id下的第一层组织id、名称、所属单位id和名称
     *
     * @param orgId   组织id
     * @param headers 请求头
     * @return orgs
     */
    public List<OrganizationBase> findLevelOneOrgs(Long orgId, HttpHeaders headers) {
        List<OrganizationBase> organizationBaseList = new ArrayList<>();

        // 远程调用地址
        String url = String.format("http://%s/org/find-child-org-by-id?org_id=%s&year=%d&month=%d", user, orgId, LocalDate.now().getYear(), LocalDate.now().getMonthValue());
        // 调用远程方法
        try {
            organizationBaseList = RemoteApiHelper.getAndResult(restTemplate, url, headers, new TypeReference<Result<List<OrganizationBase>>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, (Result) null);
        }
        return organizationBaseList;
    }

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrgBaseInfo {
        Long orgId = null;
        String name = null;
        String shortName = null;
        Integer seq = 0;
    }

    public List<OrgBaseInfo> getCorps(HttpHeaders headers) {
        List<OrgBaseInfo> orgBaseInfoList = new ArrayList<>();
        // 远程调用地址
        String url = String.format("http://%s/corp/query-list?page=%d&page_size=%d", user, 1, 9999);
        // 调用远程方法
        try {
            orgBaseInfoList = RemoteApiHelper.getAndResult(restTemplate, url, headers, new TypeReference<Result<List<OrgBaseInfo>>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, (Result) null);
        }
        return orgBaseInfoList;
    }

    @Data
    @ApiModel
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OrgByCorpForm {

        @JsonProperty("all_org")
        List<Long> allOrg = new ArrayList<>();

        @JsonProperty("branch_org")
        List<Long> branchOrg = new ArrayList<>();
    }

    /**
     * /org/find-org-by-corp
     *
     * @param corpId  单位id
     * @param headers 请求头
     * @return list of OrgByCorpForm
     */
    public OrgByCorpForm getOrgByCorp(Long corpId, HttpHeaders headers) {
        OrgByCorpForm orgs = new OrgByCorpForm();
        // 远程调用地址
        String url = String.format("http://%s/org/find-org-by-corp?corpId=%s", user, corpId);
        // 调用远程方法
        try {
            orgs = RemoteApiHelper.getAndResult(restTemplate, url, headers, new TypeReference<Result<OrgByCorpForm>>() {
            }).getData();
        } catch (IOException e) {
            throw new ApiException(e.getMessage(), e, (Result) null);
        }
        return orgs;
    }

    /**
     * 获取新闻爬取的数据
     *
     * @param type     这里默认使用 LpSpeech 重要讲话
     * @param startDay 为空，开始时间为 1970-01-01
     * @param endDay   为空，结束时间为 9999-12-31
     * @param page     默认为1
     * @param pageSize 默认为10
     */
    public List<NewsRemoteSpiderForm> getSpiderNews(String type, String startDay, String endDay, int page, int pageSize) {
        String query = StrUtil.format("{}{}/news/query?type={}&start_day={}&end_day={}&page={}&page_size={}",
                spiderProtocol,
                spiderServerName, type,
                ObjectUtils.isEmpty(startDay) ? "" : startDay,
                ObjectUtils.isEmpty(endDay) ? "" : endDay, page, pageSize);
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            return RemoteApiHelper.get(this.generalRestTemplate, query, httpHeaders, new TypeReference<>() {
            });
        } catch (IOException e) {
            log.error("拉取SpiderNews服务-> getSpiderNews()错误  msg:" + e.getMessage(), e);
//            throw new ApiException("远程调用失败:${0}", new Result<>(errors, 309, HttpStatus.INTERNAL_SERVER_ERROR.value(), "拉取SpiderNews服务-> getSpiderNews()错误:" + e.getMessage()));
            throw new ApiException(e.getMessage(), e, (Result) null);
        }
    }

    /**
     * erp消息定时推送方法
     *
     * @param sysHeader
     * @param userIds
     * @param orgIds
     * @param content
     */
    public void zyPushTimingMessage(HeaderHelper.SysHeader sysHeader, List<Long> userIds, List<Long> orgIds,String content,Date pushTime) {
        try {
            String url = String.format("http://%s/global/push/same", pushCenter);
            NormalMessagePushRequest normalMessagePushRequest = new NormalMessagePushRequest();
            //中烟erp消息推送
            normalMessagePushRequest.setChannelType(PushConstant.ChannelType.ZYERP);
            //模板id
            normalMessagePushRequest.setTemplateId(120L);
            //是否全量
            normalMessagePushRequest.setIsFull((byte) 0);
            //推送人员ids
            normalMessagePushRequest.setUserIds(userIds);
            //推送组织orgIds
            normalMessagePushRequest.setOrgIds(orgIds);
            //推送内容
            normalMessagePushRequest.setData(content);
            //推送模块
            normalMessagePushRequest.setSource("Meeting");
            // 是否外链 0-否
            normalMessagePushRequest.setIsLinkOut((byte) 0);
            if(pushTime == null){
                // 推送类型,0-即时推送,1-定时推送
                normalMessagePushRequest.setPushType((byte) 0);
            }else{
                // 推送类型,0-即时推送,1-定时推送
                normalMessagePushRequest.setPushType((byte) 1);
                //推送时间
                normalMessagePushRequest.setPushTime(pushTime);
            }
            RestTemplateHelper.post(
                    sysHeader, url, normalMessagePushRequest, new TypeReference<Result<Boolean>>() {
                    });
        }catch (Exception e){
            return;
        }
    }

}
