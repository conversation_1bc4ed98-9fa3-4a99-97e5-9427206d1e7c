package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.FileSourceEnum;
import com.goodsogood.ows.common.RatingEnum;
import com.goodsogood.ows.common.StatusEnum;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.MeetingOrgDebriefReviewScheduler;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingOrgDebriefReviewMapper;
import com.goodsogood.ows.model.db.MeetingOrgDebriefReviewEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.utils.BeanUtil;
import com.goodsogood.ows.utils.ExportExcel;
import com.goodsogood.ows.utils.PageUtil;
import com.goodsogood.ows.utils.SaasUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe 组织述职接口
 * @date 2019-12-30
 */
@Service
@Log4j2
public class MeetingOrgDebriefReviewService {

    private final MeetingOrgDebriefReviewMapper meetingOrgDebriefReviewMapper;
    private final StringRedisTemplate redisTemplate;
    private final UserCenterService userCenterService;
    private final ObjectMapper objectMapper;
    private final MeetingFileService meetingFileMapper;
    private final RegionService regionService;
    private final Errors errors;

    @Autowired
    public MeetingOrgDebriefReviewService(MeetingOrgDebriefReviewMapper meetingOrgDebriefReviewMapper, StringRedisTemplate redisTemplate, UserCenterService userCenterService, ObjectMapper objectMapper, MeetingFileService meetingFileMapper, RegionService regionService, Errors errors) {
        this.meetingOrgDebriefReviewMapper = meetingOrgDebriefReviewMapper;
        this.redisTemplate = redisTemplate;
        this.userCenterService = userCenterService;
        this.objectMapper = objectMapper;
        this.meetingFileMapper = meetingFileMapper;
        this.regionService = regionService;
        this.errors = errors;
    }

    /**
     * 添加组织述职评议
     */
    @Transactional
    public void add(HeaderHelper.SysHeader user, MeetingOrgDebriefReviewForm dto) {
        Long regionId = SaasUtils.getRegionId();
        OrganizationBase org = userCenterService.findOrgByList(Collections.singletonList(dto.getOrgId()), user).get(0);

        Date date = new Date();

        MeetingOrgDebriefReviewEntity exist = new MeetingOrgDebriefReviewEntity();
        exist.setStatus(StatusEnum.NORMAL.getStatus());
        exist.setOrgId(dto.getOrgId());
        exist.setReviewYear(dto.getReviewYear());

        exist = meetingOrgDebriefReviewMapper.selectOne(exist);

        if (Objects.nonNull(exist)) {
            throw new ApiException("该组织当年已经有评议记录 不能重复添加", new Result<>(errors, 2006, HttpStatus.OK.value()));
        }

        MeetingOrgDebriefReviewEntity add = BeanUtil.copy(dto, MeetingOrgDebriefReviewEntity.class);
        add.setStatus(StatusEnum.NORMAL.getStatus());
        add.setCreateUser(user.getUserId());
        add.setRegionId(regionId);
        add.setCreateTime(date);
        add.setOrgLevel(org.getOrgLevel());
        add.setCurrentOrgLevel(org.getOrgLevel());
        add.setOrgTypeChild(org.getOrgTypeChild());
        meetingOrgDebriefReviewMapper.insertUseGeneratedKeys(add);

        meetingFileMapper.addFile(add.getMeetingOrgDebriefReviewId(), dto.getFiles(), FileSourceEnum.ORG_DEBRIEF_REVIEW);
    }

    /**
     * 修改组织述职评议信息
     */
    @Transactional
    public void edit(HeaderHelper.SysHeader user, MeetingOrgDebriefReviewForm dto) {
        //OrganizationBase org = userCenterService.findOrgByList(Collections.singletonList(dto.getOrgId())).get(0);

        MeetingOrgDebriefReviewEntity exist = new MeetingOrgDebriefReviewEntity();
        exist.setStatus(StatusEnum.NORMAL.getStatus());
        exist.setOrgId(dto.getOrgId());
        exist.setReviewYear(dto.getReviewYear());

        exist = meetingOrgDebriefReviewMapper.selectOne(exist);

        if (exist != null && !exist.getMeetingOrgDebriefReviewId().equals(dto.getMeetingOrgDebriefReviewId())) {
            throw new ApiException("该组织当年已经有评议记录 不能重复添加", new Result<>(errors, 2006, HttpStatus.OK.value()));
        }


        MeetingOrgDebriefReviewEntity edit = BeanUtil.copy(dto, MeetingOrgDebriefReviewEntity.class);
        edit.setUpdateTime(new Date());
        edit.setLastChangeUser(user.getUserId());
        // edit.setOrgLevel(org.getOrgLevel());
        //edit.setOrgTypeChild(org.getOrgTypeChild());
        meetingOrgDebriefReviewMapper.updateByPrimaryKeySelective(edit);

        meetingFileMapper.delete(dto.getMeetingOrgDebriefReviewId(), FileSourceEnum.ORG_DEBRIEF_REVIEW);
        meetingFileMapper.addFile(dto.getMeetingOrgDebriefReviewId(), dto.getFiles(), FileSourceEnum.ORG_DEBRIEF_REVIEW);
    }

    /**
     * 查询组织述职评议详情
     */
    public MeetingOrgDebriefReviewDetailVO detail(MeetingOrgDebriefReviewForm dto) {
        MeetingOrgDebriefReviewEntity example = new MeetingOrgDebriefReviewEntity();
        example.setMeetingOrgDebriefReviewId(dto.getMeetingOrgDebriefReviewId());
        example.setStatus(StatusEnum.NORMAL.getStatus());
        MeetingOrgDebriefReviewEntity select = meetingOrgDebriefReviewMapper.selectOne(example);

        MeetingOrgDebriefReviewDetailVO vo = BeanUtil.copy(select, MeetingOrgDebriefReviewDetailVO.class);

        if (!Objects.isNull(vo)) {
            vo.setFiles(meetingFileMapper.selectByLinkedId(dto.getMeetingOrgDebriefReviewId(),
                    FileSourceEnum.ORG_DEBRIEF_REVIEW));
        }

        return vo;
    }

    /**
     * 删除组织述职评议
     */
    public void delete(HeaderHelper.SysHeader user, MeetingOrgDebriefReviewForm dto) {
        MeetingOrgDebriefReviewEntity delete = BeanUtil.copy(dto, MeetingOrgDebriefReviewEntity.class);
        delete.setStatus(StatusEnum.DELETE.getStatus());
        delete.setUpdateTime(new Date());
        delete.setLastChangeUser(user.getUserId());
        meetingOrgDebriefReviewMapper.updateByPrimaryKeySelective(delete);
    }

    /**
     * 查询组织述职评议信息列表
     */
    public Page<MeetingOrgDebriefReviewQueryVO> list(HeaderHelper.SysHeader sysHeader, MeetingOrgDebriefReviewQueryForm dto) {
        OrganizationBase org = userCenterService.findOrgByList(Collections.singletonList(dto.getOrgId()), sysHeader).get(0);
        dto.setStatus(StatusEnum.NORMAL.getStatus());
        dto.setOrgLevel(org.getOrgLevel());

        if (Objects.isNull(dto.getRating())) {
            List<OrganizationBase> orgList = userCenterService.findAllChildOrgInclude(sysHeader, dto.getOrgId());

            if (StringUtils.isNotBlank(dto.getOrgName())) {
                orgList = orgList.stream().filter(v -> v.getOrgName().contains(dto.getOrgName())).collect(Collectors.toList());

            }

            Page<MeetingOrgDebriefReviewQueryVO> page = PageUtil.pageResult(orgList, MeetingOrgDebriefReviewQueryVO.class, dto.getPageNum(), dto.getPageSize());

            if (CollectionUtils.isEmpty(page)) {
                return page;
            }

            List<Long> selectOrgIds = new ArrayList<>();
            for (MeetingOrgDebriefReviewQueryVO meetingOrgDebriefReviewQueryVO : page) {
                selectOrgIds.add(meetingOrgDebriefReviewQueryVO.getOrgId());
            }

            dto.setOrgIds(selectOrgIds);

            List<MeetingOrgDebriefReviewQueryVO> selectOrgs = meetingOrgDebriefReviewMapper.listMeetingOrgDebriefReviewQueryVO(dto);

            for (MeetingOrgDebriefReviewQueryVO meetingOrgDebriefReviewQueryVO : page) {

                for (MeetingOrgDebriefReviewQueryVO selectOrg : selectOrgs) {

                    if (selectOrg.getOrgId().equals(meetingOrgDebriefReviewQueryVO.getOrgId())) {
                        meetingOrgDebriefReviewQueryVO.setRating(selectOrg.getRating());
                        meetingOrgDebriefReviewQueryVO.setMeetingOrgDebriefReviewId(selectOrg.getMeetingOrgDebriefReviewId());
                        break;
                    }

                }

            }

            //查询组织书记
            List<OrgSecretaryForm> secretaryForms = this.userCenterService.selectOrgSecretary(selectOrgIds, dto.getReviewYear().toString());

            for (MeetingOrgDebriefReviewQueryVO meetingOrgDebriefReviewQueryVO : page) {
                String secretaries = secretaryForms.stream()
                        .filter(secretary -> secretary.getOrgId().equals(meetingOrgDebriefReviewQueryVO.getOrgId()))
                        .map(OrgSecretaryForm::getUserName)
                        .collect(Collectors.joining(","));
                meetingOrgDebriefReviewQueryVO.setPartyLeader(secretaries);

            }

            return page;
        } else {
            Page<MeetingOrgDebriefReviewQueryVO> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize())
                    .doSelectPage(() -> meetingOrgDebriefReviewMapper.listMeetingOrgDebriefReviewQueryVO(dto));

            if (CollectionUtils.isEmpty(page)) {
                return page;
            }

            List<Long> selectOrgIds = new ArrayList<>();
            for (MeetingOrgDebriefReviewQueryVO meetingOrgDebriefReviewQueryVO : page) {
                selectOrgIds.add(meetingOrgDebriefReviewQueryVO.getOrgId());
            }

            List<OrganizationBase> partyLeaderOrgs = userCenterService.findOrgByList(selectOrgIds, sysHeader);
            //查询组织书记
            List<OrgSecretaryForm> secretaryForms = this.userCenterService.selectOrgSecretary(selectOrgIds, dto.getReviewYear().toString());

            for (MeetingOrgDebriefReviewQueryVO meetingOrgDebriefReviewQueryVO : page) {

                Optional<OrganizationBase> baseOptional = partyLeaderOrgs.stream().filter(party -> party.getOrgId().equals(meetingOrgDebriefReviewQueryVO.getOrgId())).findFirst();
                baseOptional.ifPresent(organizationBase -> meetingOrgDebriefReviewQueryVO.setOrgTypeChild(organizationBase.getOrgTypeChild()));

                String secretaryName = secretaryForms.stream()
                        .filter(secretary -> secretary.getOrgId().equals(meetingOrgDebriefReviewQueryVO.getOrgId()))
                        .map(OrgSecretaryForm::getUserName)
                        .collect(Collectors.joining(","));

                meetingOrgDebriefReviewQueryVO.setPartyLeader(secretaryName);
            }
            return page;
        }
    }

    /**
     * 组织述职评议信息列表导出
     */
    public Boolean excelList(HeaderHelper.SysHeader sysHeader, MeetingOrgDebriefReviewQueryForm dto, HttpServletResponse response) {
        OrganizationBase org = userCenterService.findOrgByList(Collections.singletonList(dto.getOrgId()), sysHeader).get(0);
        dto.setStatus(StatusEnum.NORMAL.getStatus());
        dto.setOrgLevel(org.getOrgLevel());

        if (Objects.isNull(dto.getRating())) {
            List<OrganizationBase> orgList = userCenterService.findAllChildOrgInclude(sysHeader, dto.getOrgId());

            if (StringUtils.isNotBlank(dto.getOrgName())) {
                orgList = orgList.stream().filter(v -> v.getOrgName().contains(dto.getOrgName())).collect(Collectors.toList());

            }

            List<MeetingOrgDebriefReviewQueryVO> excelList = BeanUtil.copyList(orgList, MeetingOrgDebriefReviewQueryVO.class);

            List<Long> selectOrgIds = new ArrayList<>();
            for (MeetingOrgDebriefReviewQueryVO meetingOrgDebriefReviewQueryVO : excelList) {
                selectOrgIds.add(meetingOrgDebriefReviewQueryVO.getOrgId());
            }

            dto.setOrgIds(selectOrgIds);

            List<MeetingOrgDebriefReviewQueryVO> selectOrgs = meetingOrgDebriefReviewMapper.listMeetingOrgDebriefReviewQueryVO(dto);

            for (MeetingOrgDebriefReviewQueryVO meetingOrgDebriefReviewQueryVO : excelList) {

                for (MeetingOrgDebriefReviewQueryVO selectOrg : selectOrgs) {

                    if (selectOrg.getOrgId().equals(meetingOrgDebriefReviewQueryVO.getOrgId())) {
                        meetingOrgDebriefReviewQueryVO.setRating(selectOrg.getRating());
                        break;
                    }

                }

            }

            //查询组织书记
            List<OrgSecretaryForm> secretaryForms = this.userCenterService.selectOrgSecretary(selectOrgIds, dto.getReviewYear().toString());

            for (MeetingOrgDebriefReviewQueryVO meetingOrgDebriefReviewQueryVO : excelList) {
                String secretaries = secretaryForms.stream()
                        .filter(secretary -> secretary.getOrgId().equals(meetingOrgDebriefReviewQueryVO.getOrgId()))
                        .map(OrgSecretaryForm::getUserName)
                        .collect(Collectors.joining(","));
                meetingOrgDebriefReviewQueryVO.setPartyLeader(secretaries);

            }

            return excelList(excelList, response);

        } else {

            List<MeetingOrgDebriefReviewQueryVO> excelList = meetingOrgDebriefReviewMapper.listMeetingOrgDebriefReviewQueryVO(dto);

            List<Long> selectOrgIds = new ArrayList<>();
            for (MeetingOrgDebriefReviewQueryVO meetingOrgDebriefReviewQueryVO : excelList) {
                selectOrgIds.add(meetingOrgDebriefReviewQueryVO.getOrgId());
            }

            List<OrganizationBase> partyLeaderOrgs = userCenterService.findOrgByList(selectOrgIds, sysHeader);
            //查询组织书记
            List<OrgSecretaryForm> secretaryForms = this.userCenterService.selectOrgSecretary(selectOrgIds, dto.getReviewYear().toString());

            for (MeetingOrgDebriefReviewQueryVO meetingOrgDebriefReviewQueryVO : excelList) {

                Optional<OrganizationBase> baseOptional = partyLeaderOrgs.stream().filter(party -> party.getOrgId().equals(meetingOrgDebriefReviewQueryVO.getOrgId())).findFirst();
                baseOptional.ifPresent(organizationBase -> meetingOrgDebriefReviewQueryVO.setOrgTypeChild(organizationBase.getOrgTypeChild()));

                String secretaryName = secretaryForms.stream()
                        .filter(secretary -> secretary.getOrgId().equals(meetingOrgDebriefReviewQueryVO.getOrgId()))
                        .map(OrgSecretaryForm::getUserName)
                        .collect(Collectors.joining(","));

                meetingOrgDebriefReviewQueryVO.setPartyLeader(secretaryName);
            }
            return excelList(excelList, response);
        }

    }

    private boolean excelList(List<MeetingOrgDebriefReviewQueryVO> excelList, HttpServletResponse response) {
        Map<Integer, String> dictionaryMap = userCenterService.dictionaryMap(102803);


        try {
            ((ExportExcel<MeetingOrgDebriefReviewQueryVO>) (obj, row) -> {
                row.createCell(0, CellType.STRING).setCellValue(obj.getOrgName());
                row.createCell(1, CellType.STRING).setCellValue(obj.getPartyLeader());
                row.createCell(2, CellType.STRING).setCellValue(obj.getRating() == null ? "无" : RatingEnum.RATING_MAP.get(obj.getRating()).getDescription());
                row.createCell(3, CellType.STRING).setCellValue(dictionaryMap.getOrDefault(obj.getOrgTypeChild(), ""));
            }).exportExcel(response, excelList,
                    new String[]{"组织名称", "组织书记", "评议等级", "组织类型"}, "基层组织述职评议情况.xls");
        } catch (IOException | NoSuchFieldException e) {
            log.error("导出组织奖惩信息异常", e);
            return false;
        }

        return true;
    }

    /**
     * 基层述职评议结果统计查询
     */
    public Page<MeetingOrgDebriefReviewStatisticsQueryVO> statisticsList(HeaderHelper.SysHeader sysHeader, MeetingOrgDebriefReviewStatisticsQueryForm dto) {
        String val = String.valueOf(redisTemplate.opsForHash()
                .get(MeetingOrgDebriefReviewScheduler.MEETING_ORG_DEBRIEF_REVIEW_STATISTICS, String.valueOf(regionService.bindingOrgId(sysHeader.getRegionId()))));
        try {
            List<MeetingOrgDebriefReviewStatisticsQueryVO> orgList = objectMapper.readValue(val, new TypeReference<List<MeetingOrgDebriefReviewStatisticsQueryVO>>() {
            });

            if (StringUtils.isNotBlank(dto.getOrgName())) {
                orgList = orgList.stream().filter(v -> v.getOrgName().contains(dto.getOrgName())).collect(Collectors.toList());

            }

            if (CollectionUtils.isEmpty(orgList)) {
                return new Page<>();
            }

            Page<MeetingOrgDebriefReviewStatisticsQueryVO> page = PageUtil.pageResult(orgList, MeetingOrgDebriefReviewStatisticsQueryVO.class, dto.getPageNum(), dto.getPageSize());

            generateStatisticsCount(dto, page);

            return page;
        } catch (IOException e) {
            return new Page<>();
        }
    }

    public void updateCurrentOrgLevelByOrgId(Long orgId, String currentOrgLevel) {
        meetingOrgDebriefReviewMapper.updateCurrentOrgLevelByOrgId(orgId, currentOrgLevel);
    }

    /**
     * 基层述职评议结果统计导出
     */
    public boolean statisticsExcelList(HeaderHelper.SysHeader sysHeader, MeetingOrgDebriefReviewStatisticsQueryForm dto, HttpServletResponse response) {
        String val = String.valueOf(redisTemplate.opsForHash().get(MeetingOrgDebriefReviewScheduler.MEETING_ORG_DEBRIEF_REVIEW_STATISTICS, String.valueOf(regionService.bindingOrgId(sysHeader.getRegionId()))));
        try {
            List<MeetingOrgDebriefReviewStatisticsQueryVO> orgList = objectMapper.readValue(val, new TypeReference<List<MeetingOrgDebriefReviewStatisticsQueryVO>>() {
            });

            if (StringUtils.isNotBlank(dto.getOrgName())) {
                orgList = orgList.stream().filter(v -> v.getOrgName().contains(dto.getOrgName())).collect(Collectors.toList());

            }

            generateStatisticsCount(dto, orgList);

            ((ExportExcel<MeetingOrgDebriefReviewStatisticsQueryVO>) (obj, row) -> {
                row.createCell(0, CellType.STRING).setCellValue(obj.getOrgName());
                row.createCell(1, CellType.NUMERIC).setCellValue(obj.getBasisOrgNum());
                row.createCell(2, CellType.NUMERIC).setCellValue(obj.getParticipateOrgNum());
                row.createCell(3, CellType.NUMERIC).setCellValue(obj.getGoodOrgNum());
                row.createCell(4, CellType.NUMERIC).setCellValue(obj.getBetterOrgNum());
                row.createCell(5, CellType.NUMERIC).setCellValue(obj.getGeneralOrgNum());
                row.createCell(6, CellType.NUMERIC).setCellValue(obj.getDifferOrgNum());
            }).exportExcel(response, orgList,
                    new String[]{"组织名称", "基础党组织数", "参与组织数", "\"好\"等级组织数", "\"较好\"等级组织数", "\"一般\"等级组织数", "\"差\"等级组织数"}, "基层组织述职评议结果统计.xls");


        } catch (IOException | NoSuchFieldException e) {
            log.error("导出组织奖惩信息异常", e);
            return false;
        }


        return true;
    }

    private void generateStatisticsCount(MeetingOrgDebriefReviewStatisticsQueryForm dto, List<MeetingOrgDebriefReviewStatisticsQueryVO> list) {
        if (!CollectionUtils.isEmpty(list)) {
            List<Long> orgIds = list.stream().map(MeetingOrgDebriefReviewStatisticsQueryVO::getOrgId).collect(Collectors.toList());
            MeetingOrgDebriefReviewStatisticsQueryVO.OrgDebriefReviewStatisticsDTO example = new MeetingOrgDebriefReviewStatisticsQueryVO.OrgDebriefReviewStatisticsDTO();
            example.setOrgIds(orgIds);
            example.setReviewYear(dto.getReviewYear());
            example.setStatus(StatusEnum.NORMAL.getStatus());
            List<MeetingOrgDebriefReviewStatisticsQueryVO.OrgDebriefReviewStatistics> statisticsList = meetingOrgDebriefReviewMapper.listOrgDebriefReviewStatistics(example);

            for (MeetingOrgDebriefReviewStatisticsQueryVO.OrgDebriefReviewStatistics orgDebriefReviewStatistics : statisticsList) {

                for (MeetingOrgDebriefReviewStatisticsQueryVO meetingOrgDebriefReviewStatisticsQueryVO : list) {

                    if (orgDebriefReviewStatistics.getOrgLevel().contains("-" + meetingOrgDebriefReviewStatisticsQueryVO.getOrgId() + "-")) {

                        if (orgDebriefReviewStatistics.getRating().equals(RatingEnum.GOOD.getRating())) {
                            meetingOrgDebriefReviewStatisticsQueryVO.setGoodOrgNum(
                                    meetingOrgDebriefReviewStatisticsQueryVO.getGoodOrgNum() + orgDebriefReviewStatistics.getCount()
                            );

                            meetingOrgDebriefReviewStatisticsQueryVO.setParticipateOrgNum(meetingOrgDebriefReviewStatisticsQueryVO.getParticipateOrgNum() + orgDebriefReviewStatistics.getCount());
                        }

                        if (orgDebriefReviewStatistics.getRating().equals(RatingEnum.GENERAL.getRating())) {
                            meetingOrgDebriefReviewStatisticsQueryVO.setGeneralOrgNum(
                                    meetingOrgDebriefReviewStatisticsQueryVO.getGeneralOrgNum() + orgDebriefReviewStatistics.getCount()
                            );

                            meetingOrgDebriefReviewStatisticsQueryVO.setParticipateOrgNum(meetingOrgDebriefReviewStatisticsQueryVO.getParticipateOrgNum() + orgDebriefReviewStatistics.getCount());
                        }

                        if (orgDebriefReviewStatistics.getRating().equals(RatingEnum.BETTER.getRating())) {
                            meetingOrgDebriefReviewStatisticsQueryVO.setBetterOrgNum(
                                    meetingOrgDebriefReviewStatisticsQueryVO.getBetterOrgNum() + orgDebriefReviewStatistics.getCount()
                            );

                            meetingOrgDebriefReviewStatisticsQueryVO.setParticipateOrgNum(meetingOrgDebriefReviewStatisticsQueryVO.getParticipateOrgNum() + orgDebriefReviewStatistics.getCount());
                        }

                        if (orgDebriefReviewStatistics.getRating().equals(RatingEnum.DIFFER.getRating())) {
                            meetingOrgDebriefReviewStatisticsQueryVO.setDifferOrgNum(
                                    meetingOrgDebriefReviewStatisticsQueryVO.getDifferOrgNum() + orgDebriefReviewStatistics.getCount()
                            );

                            meetingOrgDebriefReviewStatisticsQueryVO.setParticipateOrgNum(meetingOrgDebriefReviewStatisticsQueryVO.getParticipateOrgNum() + orgDebriefReviewStatistics.getCount());
                        }

                        break;
                    }
                }

            }
        }

    }
}
