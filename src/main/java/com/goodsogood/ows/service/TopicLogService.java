package com.goodsogood.ows.service;

import com.goodsogood.ows.common.TopicConstant;
import com.goodsogood.ows.mapper.TopicLogMapper;
import com.goodsogood.ows.model.db.TopicContentEntity;
import com.goodsogood.ows.model.db.TopicLogEntity;
import com.goodsogood.ows.model.db.TopicLogFileEntity;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-24 16:32
 **/
@Service
@Log4j2
public class TopicLogService {

    private final TopicLogMapper topicLogMapper;
    private final TopicLogFileService topicLogFileService;

    @Autowired
    public TopicLogService(TopicLogMapper topicLogMapper, TopicLogFileService topicLogFileService) {
        this.topicLogMapper = topicLogMapper;
        this.topicLogFileService = topicLogFileService;
    }

    /**
     * 根据活动任务的管理id查询所有的答案
     *
     * @param meetingTopicId
     * @return
     */
    public List<TopicLogEntity> getToplicLog(Long meetingTopicId, Long topicOrgId) {
        Example example = new Example(TopicLogEntity.class);
        Example.Criteria criteria = example.createCriteria();
        if (meetingTopicId != null) {
            criteria.andEqualTo("meetingTopicId", meetingTopicId);
        }
        if (topicOrgId != null) {
            criteria.andEqualTo("topicOrgId", topicOrgId);
        }
        return this.topicLogMapper.selectByExample(example);
    }

    /**
     * topic.contents中添加答案
     *
     * @param contents 任务内容
     * @param logList  任务答案
     */
    public void topicAnswerSetToContent(List<TopicLogEntity> logList, List<TopicContentEntity> contents) {
        //遍历日志的时候，被绑定到contentId后，移除
        List<TopicLogEntity> removeIds = new ArrayList<>();
        //合并任务详情和答案
        contents.forEach(content -> {
            List<Long> opts = new ArrayList<>();
            for (TopicLogEntity log : logList) {
                if (content.getContentId().longValue() == log.getContentId().longValue()) {
                    removeIds.add(log);
                    //问答题
                    if (TopicConstant.isContent(content.getType())) {
                        content.setAnsCnt(log.getAnsCnt());
                        //查询是否有附件
                        List<TopicLogFileEntity> files = this.topicLogFileService.getTopicLogFile(log.getTopicLogId());
                        if (!ListUtils.isEmpty(files)) {
                            content.setAnsFiles(files);
                        }
                        return;
                    }
                    //有选项的题
                    if (TopicConstant.isOpts(content.getType())) {
                        opts.add(log.getOptsId());
                    }
                }
            }
            logList.removeAll(removeIds);
            content.setAnswer(opts);
        });
    }
}