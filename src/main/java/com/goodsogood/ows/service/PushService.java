package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.MeetingCanstant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.configuration.PartMeetingSmsConfiguration;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingTypeEntity;
import com.goodsogood.ows.model.db.MeetingUserEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.parsing.SimpleGenericTokenParser;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.SaasUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Description: 推送消息</p>
 *
 * <AUTHOR>
 * @date 2018/11/5 11:11
 */
@Service
@Log4j2
public class PushService {

    @Value("${tog-services.push-center}")
    private String pushCenter;


    private final RestTemplate restTemplate;
    private final Errors errors;
    private final MeetingService meetingService;
    private final OpenService openService;
    private final PartMeetingSmsConfiguration partMeetingSmsConfiguration;
    private final UserCenterService userCenterService;
    private final RegionService regionService;
    private final WechatService wechatService;
    private final MeetingConfig meetingConfig;

    public PushService(
            RestTemplate restTemplate,
            Errors errors,
            MeetingService meetingService,
            OpenService openService,
            PartMeetingSmsConfiguration partMeetingSmsConfiguration,
            UserCenterService userCenterService,
            RegionService regionService, WechatService wechatService, MeetingConfig meetingConfig) {
        this.restTemplate = restTemplate;
        this.errors = errors;
        this.meetingService = meetingService;
        this.openService = openService;
        this.partMeetingSmsConfiguration = partMeetingSmsConfiguration;
        this.userCenterService = userCenterService;
        this.regionService = regionService;
        this.wechatService = wechatService;
        this.meetingConfig = meetingConfig;
    }

    /**
     * 短信推送业务：领导干部参加支部活动。
     *
     * @param meetingEntity 会议信息
     */
    @Async
    public void sendSmsToLeader(HeaderHelper.SysHeader sysHeader, MeetingEntity meetingEntity, LogAspectHelper.SSLog ssLog) {
        // 进入异步方法，重新设置下上下文
        LogHelper.asyncLog(ssLog, () -> {
            // 不推送 直接返回
            if (!partMeetingSmsConfiguration.getSend()) {
                log.info(" 短信推送业务（领导干部参加支部活动通知）关闭");
                return;
            }
            // 查询活动详情
            //提交成功 给“参加人员”和“列席人员”中的领导班子成员 发送短信
            List<MeetingUserEntity> pushLeaders = new ArrayList<>();
            // 查询所有领导班子信息
            List<UserLeaderCallBackForm> leaderCallBackForms = userCenterService.findLeaders(sysHeader,
                    DateTime.now().toDate(), Collections.singletonList(regionService.bindingOrgId(sysHeader.getRegionId())), null);
            if (CollectionUtils.isNotEmpty(leaderCallBackForms)) {
                List<CreateLeaderForm> createLeaderFormList = new ArrayList<>();
                for (UserLeaderCallBackForm userLeaderCallBackForm : leaderCallBackForms) {
                    if (CollectionUtils.isNotEmpty(userLeaderCallBackForm.getValues())) {
                        createLeaderFormList.addAll(userLeaderCallBackForm.getValues());
                    }
                }
                // “参加人员”和“列席人员”中的领导班子
                List<MeetingUserEntity> userEntityList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(meetingEntity.getParticipantUsers())) {
                    userEntityList.addAll(meetingEntity.getParticipantUsers());
                }
                if (CollectionUtils.isNotEmpty(meetingEntity.getAttendUsers())) {
                    userEntityList.addAll(meetingEntity.getAttendUsers());
                }
                if (CollectionUtils.isNotEmpty(createLeaderFormList)) {
                    Set<Long> leaders = createLeaderFormList.stream().map(CreateLeaderForm::getUserId).collect(Collectors.toSet());
                    for (MeetingUserEntity meetingUserEntity : userEntityList) {
                        if (leaders.contains(meetingUserEntity.getUserId())) {
                            pushLeaders.add(meetingUserEntity);
                        }
                    }
                }
            }
            // 发送短信
            partMeetingSmsByGlobalPushDiff(meetingEntity, pushLeaders, sysHeader);
        });
    }

    /**
     * 短信推送业务：领导干部参加支部活动。
     */
    private Boolean partMeetingSmsByGlobalPushDiff(MeetingEntity meetingEntity, List<MeetingUserEntity> pushLeader, HeaderHelper.SysHeader sysHeader) {
        if (meetingEntity != null && CollectionUtils.isNotEmpty(pushLeader)) {
            String startTime = DateUtils.dateFormat(meetingEntity.getStartTime(), "yyyy-MM-dd HH:mm");
            // 查询组织名称
            if (StringUtils.isBlank(meetingEntity.getOrgName())) {
                List<OrganizationBase> organizationBaseList = userCenterService.findOrgByList(Collections.singletonList(meetingEntity.getOrgId()), sysHeader);
                if (CollectionUtils.isNotEmpty(organizationBaseList)) {
                    meetingEntity.setOrgName(StringUtils.isBlank(organizationBaseList.get(0).getOrgName()) ? organizationBaseList.get(0).getName() : organizationBaseList.get(0).getOrgName());
                }
            }
            if (StringUtils.isNotBlank(meetingEntity.getOrgName())) {
                List<SendMsgToLeaderForm> list = new ArrayList<>();
                Set<Long> pushLeadersId = new HashSet<>();
                for (MeetingUserEntity meetingUserEntity : pushLeader) {
                    if (pushLeadersId.add(meetingUserEntity.getUserId())) {
                        SendMsgToLeaderForm sendMsgToLeaderForm = new SendMsgToLeaderForm();
                        sendMsgToLeaderForm.setUserId(meetingUserEntity.getUserId());
                        sendMsgToLeaderForm.setName(meetingUserEntity.getUserName());
                        sendMsgToLeaderForm.setOraname(meetingEntity.getOrgName());
                        sendMsgToLeaderForm.setType(meetingEntity.getTypes());
                        sendMsgToLeaderForm.setDatetime(startTime);
                        list.add(sendMsgToLeaderForm);
                    }
                }
                // huang_kangjie 由于在推送的时候需要主题推送，所以暂时默认原来的推送
                this.openService.sendNotice(list, partMeetingSmsConfiguration.getMsgTemplateId(),
                        partMeetingSmsConfiguration.getMsgChannelType(), null, SaasUtils.getRegionId());
            }
        }
        return true;
    }

    /**
     * 推送活动通知
     */
    public Boolean meetingNoticeByGlobalPushSame(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, MeetingNoticeForm meetingNoticeForm) throws UnsupportedEncodingException {
        // 查询活动详情
        MeetingEntity meetingEntity = meetingService.findById(sysHeader, meetingNoticeForm.getMeetingId());
        if (meetingEntity.getStatus() != MeetingCanstant.MEETING_STATUS_SOON_START.shortValue()
                || DateUtils.ltToday(meetingEntity.getStartTime())) {//非活动待举办状态不能发送通知
            throw new ApiException("非活动待举办状态不能发送通知", new Result<>(errors, 1811, HttpStatus.BAD_REQUEST.value()));
        }
        // 推送的userId
        List<MeetingUserEntity> meetingUserEntities = new ArrayList<>();
        meetingUserEntities.addAll(meetingEntity.getRecordUser());
        meetingUserEntities.addAll(meetingEntity.getHostUser());
        meetingUserEntities.addAll(meetingEntity.getParticipantUsers());
        meetingUserEntities.addAll(meetingEntity.getAttendUsers());
        Set<Long> userIds = meetingUserEntities.stream()
                .filter(meetingUserEntity -> meetingUserEntity.getUserId() != null && meetingUserEntity.getUserId() >= 0)
                .map(MeetingUserEntity::getUserId).collect(Collectors.toSet());

        //跳转地址
        long regionId = sysHeader.getRegionId();
        // 推送的数据 data
        //默认值：【活动名称】
        String name;
        if (StringUtils.isNotBlank(meetingNoticeForm.getName())) {
            name = meetingNoticeForm.getName();
        } else {
            name = meetingEntity.getName();
        }
        // 开始时间
        String time = DateUtils.dateFormat(meetingEntity.getStartTime(), "yyyy年MM月dd日 HH:mm");
        //默认值：【活动地点】；
        String address;
        if (StringUtils.isNotBlank(meetingNoticeForm.getAddress())) {
            address = meetingNoticeForm.getAddress();
        } else {
            address = meetingEntity.getName();
        }
        SimpleGenericTokenParser parser = SimpleGenericTokenParser.builder()
                .addFieldVal("meetingTypeNames",
                        StringUtils.join(meetingEntity.getMeetingTypes().stream().map(MeetingTypeEntity::getType).collect(Collectors.toList())))
                .addFieldVal("name", name)
                .addFieldVal("address", address)
                .addFieldVal("time", time)
                .build();
        MeetingConfig.NoticeConfig.TemplateConfig templateConfig = meetingConfig.getNotice().getTemplate();
        PushWechatTemplateForm.DataForm dataForm = templateConfig.getMsg();
        // 组装参数
        PushWechatTemplateForm pushWechatTemplateForm = PushWechatTemplateForm.builder()
                .data(parser.parse(dataForm))
                .templateId(templateConfig.getId())
                .channelType(templateConfig.getChannelType())
                .source(templateConfig.getSource())
                .userIds(userIds)
                .pushType(0)
                .linkOut(1)
                .linkUrl(templateLinkUrl(regionId, meetingEntity))
                .full(0)
                .orgId(regionService.bindingOrgId(regionId))
                .build();
        return this.push(headers, sysHeader, pushWechatTemplateForm);
    }

    private String templateLinkUrl(
            long regionId, MeetingEntity meetingEntity)
            throws UnsupportedEncodingException {
        MeetingConfig.NoticeConfig.TemplateConfig templateConfig
                = meetingConfig.getNotice().getTemplate();
        Region.RegionData regionData = regionService.regionData(regionId);
        boolean isSaasVersion = regionData.wechatIsSaasVersion();
        SimpleGenericTokenParser parser = SimpleGenericTokenParser.builder()
                .addFieldVal(meetingEntity).build();
        if (isSaasVersion) {
            // saas 跳转到微信中心
            String redirectCheckPageUri =
                    parser.parse(templateConfig.getRedirectCheckPageUri());
            return wechatService.loginPreRouterUrl(regionId, redirectCheckPageUri);
        } else {
            // V1.0.6 跳转前端
            StringBuilder templateLinkUrl = new StringBuilder();
            templateLinkUrl.append(regionData.getWechatDomain().trim());
            String checkPageToDetailUri = templateConfig.getCheckPageToDetailUri();
            templateLinkUrl.append(parser.parse(checkPageToDetailUri));
            return templateLinkUrl.toString();
        }
    }

    /**
     * 推送
     */
    private Boolean push(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, PushWechatTemplateForm pushWechatTemplateForm) {
        if (noSendId(pushWechatTemplateForm)) {
            log.debug("推送目标为null！\n{}", JsonUtils.toJson(pushWechatTemplateForm));
            return true;
        }
        headers = HeaderHelper.setMyHttpHeader(headers, sysHeader);
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        try {
            //post请求
            log.info("push-center/push-活动通知请求参数：{}", JsonUtils.toJson(pushWechatTemplateForm));
            Boolean res = RemoteApiHelper.post(
                    restTemplate,
                    String.format("http://%s/%s", pushCenter, "/global/push/same"),
                    pushWechatTemplateForm, headers,
                    new TypeReference<Result<Boolean>>() {
                    });
            log.debug("res->{}", res);
            return res;
        } catch (ApiException e) {
            Result result = e.getResult();
            log.debug("error->{}", result);
            throw new ApiException("远程服务调用失败", result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ApiException("调用push失败", new Result<>(errors, 9907, HttpStatus.SERVICE_UNAVAILABLE.value()));
        }
    }

    /**
     * 判断是否有推送目标
     *
     * @param pushWechatTemplateForm 推送参数
     * @return true 没有推送目标 false 存在推送目标
     */
    private boolean noSendId(PushWechatTemplateForm pushWechatTemplateForm) {
        if (pushWechatTemplateForm.getFull() == null) {
            return true;
        } else if (pushWechatTemplateForm.getFull().equals(1)) {
            return false;
        } else {
            return !pushWechatTemplateForm.getFull().equals(0)
                    || (isEmpty(pushWechatTemplateForm.getUserIds()) && isEmpty(pushWechatTemplateForm.getOrgIds()));
        }
    }

    /**
     * 判断是否有Id
     *
     * @param ids set
     */
    private boolean isEmpty(Set<Long> ids) {
        return ids == null || ids.isEmpty();
    }
}
