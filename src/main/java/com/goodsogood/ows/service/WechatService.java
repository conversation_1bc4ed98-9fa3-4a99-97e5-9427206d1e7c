package com.goodsogood.ows.service;

import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.configuration.WechatConfiguration;
import com.goodsogood.ows.parsing.SimpleGenericTokenParser;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version 2020/6/15 15:49
 */
@Service
@Log4j2
public class WechatService {
    private final WechatConfiguration wechatConfiguration;
    private final RegionService regionService;

    public WechatService(WechatConfiguration wechatConfiguration, RegionService regionService) {
        this.wechatConfiguration = wechatConfiguration;
        this.regionService = regionService;
    }

    /**
     * @param regionId 区县id
     * @return <p>http://xxx.com/redirect/login_pre_router?project_name=owssaas
     * &appid=wx26dcc78a62c66223&scope=snsapi_base&version=4&org_id=3&routerType=-2&redirect={redirect}
     * <p>
     */
    public String loginPreRouterUrl(long regionId, String redirectUri)
            throws UnsupportedEncodingException {
        Region region = regionService.region();
        Region.RegionData regionData = regionService.regionData(regionId);
        long bindingOrgId = regionService.bindingOrgId(regionId);
        StringBuilder loginPreRouterUrl = new StringBuilder();
        loginPreRouterUrl.append(region.getWechatCenterUrl());
        SimpleGenericTokenParser parser =
                SimpleGenericTokenParser.builder()
                        .addFieldVal("bindingOrgId", String.valueOf(bindingOrgId))
                        .addFieldVal(wechatConfiguration)
                        .addFieldVal(regionData)
                        .addFieldVal(region)
                        .addFieldVal("redirect", URLEncoder.encode(redirectUri, "UTF-8"))
                        .build();
        loginPreRouterUrl.append(parser.parse(wechatConfiguration.getLoginPreRouterUri()));
        return loginPreRouterUrl.toString();
    }
}
