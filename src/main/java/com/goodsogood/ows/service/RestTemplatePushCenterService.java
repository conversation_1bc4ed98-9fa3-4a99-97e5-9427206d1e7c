package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.PushRequest;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.push.normal.NormalMessagePushRequest;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Description: 调用推送中心
 *
 * <AUTHOR>
 * @version 2020/5/7 15:55
 */
@Service
@Log4j2
public class RestTemplatePushCenterService {
    @Value("${tog-services.push-center}")
    private String pushCenter;

    /**
     * 普通消息推送
     */
    public <T extends NormalMessagePushRequest> void globalPushSameByUserIds(
            HeaderHelper.SysHeader sysHeader, T normalMessagePushRequest) {
        log.debug("通知人数:{}", normalMessagePushRequest.getUserIds().size());
        String url = String.format("http://%s/global/push/same", pushCenter);
        RestTemplateHelper.post(
                sysHeader, url, normalMessagePushRequest, new TypeReference<Result<Boolean>>() {
                });
    }

    /**
     * 推送中心每次推送人数有限制
     *
     * @return 批次
     */
    public <T extends PushRequest> Long globalPushDiffByUserIds(
            HeaderHelper.SysHeader sysHeader, T pushRequest) {
        log.debug("变量消息推送:{}", JsonUtils.toJson(pushRequest));
        String url = String.format("http://%s/global/push/diff", pushCenter);
        return RestTemplateHelper.post(
                sysHeader, url, pushRequest, new TypeReference<Result<Long>>() {
                });
    }

}
