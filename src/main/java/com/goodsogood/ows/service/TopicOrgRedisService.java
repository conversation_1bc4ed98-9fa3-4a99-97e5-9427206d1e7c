package com.goodsogood.ows.service;

import com.goodsogood.ows.common.RedisConstant;
import com.goodsogood.ows.mapper.TopicOrgMapper;
import com.goodsogood.ows.model.vo.MeetingTopicTaskListForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * <AUTHOR>
 * @create 2018-10-23 09:03
 **/
@Service
@Log4j2
public class TopicOrgRedisService {

    private final TopicOrgMapper topicOrgMapper;
    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public TopicOrgRedisService(TopicOrgMapper topicOrgMapper, StringRedisTemplate stringRedisTemplate) {
        this.topicOrgMapper = topicOrgMapper;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 未完成TOPIC数量统计（不包括逾期）
     *
     * @param oid 组织id
     */
    int undoneCountByRedis(Long oid) {
        // oid 为null 直接返回
        if (oid == null) {
            return 0;
        }
        String redisKey = undoneCountByRedisKey(oid);
        Object redisTopicCount = stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).get(redisKey);
        // 缓存中有值，返回缓存值
        if (redisTopicCount != null) {
            return Integer.valueOf((String) redisTopicCount);
        }
        return undoneCountAndRedisByDb(oid);
    }

    /**
     * 未完成任务数量统计（不包括逾期）
     *
     * @param oid 组织id
     */
    private int undoneCountAndRedisByDb(Long oid) {
        if (oid == null) {
            return 0;
        }
        Integer count = undoneTopicCount(oid);
        // 存入缓存，有效时间1小时
        String redisKey = undoneCountByRedisKey(oid);
        stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).put(redisKey, count.toString());
        return count;
    }


    private Integer undoneTopicCount(Long oid) {
        MeetingTopicTaskListForm meetingTopicTaskListForm = new MeetingTopicTaskListForm();
        meetingTopicTaskListForm.setStatus((short) 1);
        meetingTopicTaskListForm.setOrgIds(Collections.singletonList(oid));
        meetingTopicTaskListForm.setOrgId(oid);
        return taskCount(meetingTopicTaskListForm);
    }

    /**
     * redis key
     *
     * @param oid 组织id
     * @return key
     */
    String undoneCountByRedisKey(Long oid) {
        return RedisConstant.MEETING_TOPIC_UNDONE_COUNT + oid;
    }

    /**
     * 2018年11月9日 16:21:31 chenanshun
     * 未完成活动任务总数 不包含逾期任务
     */
    public int taskCount(MeetingTopicTaskListForm meetingTopicTaskListForm) {
        return topicOrgMapper.taskCount(meetingTopicTaskListForm);
    }

}