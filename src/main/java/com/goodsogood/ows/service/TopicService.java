package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.goodsogood.ows.common.StringCanstant;
import com.goodsogood.ows.common.TopicConstant;
import com.goodsogood.ows.common.pojo.Headers;
import com.goodsogood.ows.common.pojo.PageBean;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.utils.CheckDataOperateUtils;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:39
 **/
@Service
@Log4j2
public class TopicService {

    private final Errors errors;
    private final TopicMapper mapper;
    private final TopicFileMapper topicFileMapper;
    private final TopicContentMapper topicContentMapper;
    private final TopicOptsMapper topicOptsMapper;
    private final TopicOrgMapper topicOrgMapper;

    private final TopicFileService topicFileService;
    private final TopicOrgService topicOrgService;
    private final TopicContentService topicContentService;
    private final TopicOptsService topicOptsService;
    private final MeetingTopicService meetingTopicService;
    private final IndexService indexService;
    private final TopicServiceAsync topicServiceAsync;

    private Long uid;

    @Autowired
    public TopicService(
            Errors errors,
            TopicMapper mapper,
            TopicFileMapper topicFileMapper,
            TopicContentMapper topicContentMapper,
            TopicOptsMapper topicOptsMapper,
            TopicOrgMapper topicOrgMapper,
            TopicFileService topicFileService,
            TopicOrgService topicOrgService,
            TopicContentService topicContentService,
            TopicOptsService topicOptsService,
            MeetingTopicService meetingTopicService,
            IndexService indexService,
            TopicServiceAsync topicServiceAsync) {
        this.errors = errors;
        this.mapper = mapper;
        this.topicFileMapper = topicFileMapper;
        this.topicContentMapper = topicContentMapper;
        this.topicOptsMapper = topicOptsMapper;
        this.topicOrgMapper = topicOrgMapper;
        this.topicFileService = topicFileService;
        this.topicOrgService = topicOrgService;
        this.topicContentService = topicContentService;
        this.topicOptsService = topicOptsService;
        this.meetingTopicService = meetingTopicService;
        this.indexService = indexService;
        this.topicServiceAsync = topicServiceAsync;
    }

    /**
     * 新增任务
     *
     * @param topic
     */
    @Transactional(rollbackFor = Exception.class)
    public int add(HeaderHelper.SysHeader sysHeader, TopicEntity topic) {
        if (topic.getStatus() == TopicConstant.STATUS_LEADER && topic.getNoticeType() == null) {
            topic.setNoticeType(2);
        } else if (topic.getStatus() == TopicConstant.STATUS_OWNER) {
            topic.setNoticeType(null);
        }
        int code = this.addInner(topic, sysHeader.getOid(), sysHeader.getUserId(), sysHeader.getOrgName(), 0);
        if (code == 0) {
            //  新增任务成功，异步推送通知
            topicServiceAsync.addTopicNotice(sysHeader, topic, HttpLogAspect.getSSLog());
        }
        return code;
    }

    /**
     * 内部添加，防止事务回滚出错
     * @param topic 任务内容
     * @param oid 组织id
     * @param uid 用户id
     * @param orgName 组织名称
     * @param type 0: 新增 1： 修改
     * @return int 错误编码
     */
    public int addInner(TopicEntity topic, Long oid, Long uid, String orgName, int type) {
        checkParams(topic, oid, uid, orgName);
        int code;
        try {
            //新增、修改基础信息
            Map<String, Long> map = this.addOrUpdateTopicBase(topic, oid, uid, orgName, type);
            code = (map.get("code")).intValue();
            if(code != 0) {
                log.debug("新增任务：新增基础信息出错！");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return code;
            }
            Long topicId = map.get("id");
            //新增文件
            addFiles(topic.getFiles(), topicId);

            //新增选项
            addContent(topic.getContents(), topicId);

            //发送个各个需要派发的组织
            send(topic.getOrgs(), topicId);
            // topic添加后刷新移动端首页缓存
            indexService.collectRedis();
            return 0;
        } catch (ApiException e) {
            if(e.getResult().getCode() == 1804) {
                throw new ApiException(e.getMessage(), new Result<>(errors, e.getResult().getCode(), HttpStatus.OK.value(), e.getMessage()));
            }
            throw new ApiException(e.getMessage(), new Result<>(errors, e.getResult().getCode(), HttpStatus.OK.value()));
        } catch (Exception e) {
            log.error("新增任务出错", e);
            code = Global.Errors.SYSTEM_UNKNOWN.getCode();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return code;
    }

    private void checkParams(TopicEntity topic, Long oid, Long uid, String orgName) {
        this.uid = uid;

        //判断内容是否为空
        if (topic.getContents().size() == 0) {
            throw new ApiException("任务内容不能为空", new Result<>(errors, 1804, HttpStatus.OK.value(), StringCanstant.TASK + "内容不能为空"));
        }

        // 校验开始时间是否大于现在

        // 2019-02-01 08:35:15
        // 1）修改开始时间
        // 允许选择当前时间之前的日期
        // if (DateUtils.getToday().getTime() > topic.getStartTime().getTime()) {
        //            throw new ApiException("开始时间必须大于当前时间", new Result<>(errors, 1804,
        // HttpStatus.OK.value(), "开始时间在当前时间之前"));
        //        }
        // 结束时间 如果开始时间年份小于2019，在2019的基础上加100在开始;大于等于，开始时间年份+100
        if (topic.getEndTime() == null) {
            topic.setEndTime(this.getDate(DateUtils.yearAdd100(topic.getStartTime())));
        } else {
            topic.setEndTime(this.getDate(topic.getEndTime()));
        }

        if (topic.getEndTime().getTime() < topic.getStartTime().getTime()) {
            throw new ApiException("结束时间必须大于开始时间", new Result<>(errors, 1804, HttpStatus.OK.value(), "开始时间必须小于结束时间"));
        }

        //创建方式判断
        if (topic.getStatus() == TopicConstant.STATUS_LEADER && ListUtils.isEmpty(topic.getOrgs())) {
            throw new ApiException("任务为派发的方式时，派发组织必须填写", new Result<>(errors, 1804, HttpStatus.OK.value(), "必须选择派发的组织"));
        }
        if (topic.getStatus() == TopicConstant.STATUS_OWNER) {
            List<TopicOrgEntity> orgs = new ArrayList<>();
            TopicOrgEntity org = new TopicOrgEntity();
            org.setOrgId(oid);
            org.setOrgName(orgName);
            orgs.add(org);
            topic.setOrgs(orgs);
        }
    }

    /**
     * 新增任务的基础信息
     *
     * @param topic
     * @param oid
     * @param uid
     * @param orgName
     * @param type    0: 新增 1： 修改
     * @return
     */
    private Map<String, Long> addOrUpdateTopicBase(TopicEntity topic, Long oid, Long uid, String orgName, int type){
        Map<String, Long> map = new HashMap<>();
        map.put("code", 0L);
        if(topic.getEndTime().before(topic.getStartTime())) {
            map.put("code", 1920L);
            return map;
        }
        if(type == 1) {
            //修改
            topic.setUpdateTime(DateTime.now().toDate());
            topic.setLastChangeUser(uid);
            topic.setId(topic.getTopicId());
            this.mapper.updateByPrimaryKeySelective(topic);
            log.debug("修改任务基本属性完成！");
        } else {
            //新增
            //判断开始时间和结束时间的大小
            topic.setCreateTime(DateTime.now().toDate());
            topic.setCreateUser(uid);
            topic.setOrgId(oid);
            topic.setIsDel(0);
            topic.setOrgName(orgName);
            this.mapper.insertUseGeneratedKeys(topic);
            log.debug("新增任务：新增基本属性完成！");
        }

        map.put("id", topic.getTopicId());
        return map;
    }

    /**
     * 新增任务附件
     * @param files
     * @param topicId
     */
    private void addFiles(List<TopicFileEntity> files, Long topicId) {
        if(!ListUtils.isEmpty(files)) {
            files.forEach( file -> {
                file.setTopicFileId(null);
                file.setTopicId(topicId);
                file.setCreateTime(DateTime.now().toDate());
                file.setCreateUser(this.uid);
                this.topicFileMapper.insertSelective(file);
            });
            log.debug("新增任务：新增文件完成！");
        }
    }

    /**
     * 新增内容
     * @param contents
     * @param topicId
     */
    private void addContent(List<TopicContentEntity> contents, Long topicId) {
        if(!ListUtils.isEmpty(contents)) {
            contents.forEach( content -> {
                content.setCreateUser(this.uid);
                content.setCreateTime(DateTime.now().toDate());
                content.setTopicId(topicId);
                this.topicContentMapper.insertUseGeneratedKeys(content);
                //单选题或者多选选题
                if(content.getType() == TopicConstant.ContentType.RADIO.getType()
                        || content.getType() == TopicConstant.ContentType.CHECK_BOX.getType()) {
                    if(ListUtils.isEmpty(content.getOpts()) || content.getOpts().size() < 2) {
                        throw new ApiException("内容选项至少配置2个", new Result<>(errors, 1804, HttpStatus.OK.value(), "内容选项至少配置2个"));
                    }
                    addOpts(content.getOpts(), topicId, content.getId());
                }


            });
            log.debug("新增任务：新增内容完成！");
        }
    }

    /**
     * 新增子选项
     * @param opts
     * @param topicId
     * @param contentId
     */
    private void addOpts(List<TopicOptsEntity> opts, long topicId, long contentId){
        if (!ListUtils.isEmpty(opts)) {
            opts.forEach( opt -> {
                opt.setContentId(contentId);
                opt.setCreateTime(DateTime.now().toDate());
                opt.setCreateUser(this.uid);
                opt.setTopicId(topicId);
                this.topicOptsMapper.insert(opt);
            });
        }
    }

    /**
     * 将任务分发到所选阻止
     * @param orgs
     * @param topicId
     */
    private void send(List<TopicOrgEntity> orgs, Long topicId) {
        if(!ListUtils.isEmpty(orgs)) {
            Date createTime = DateTime.now().toDate();
            List<TopicOrgEntity> orgList = new ArrayList<>();
            orgs.forEach( org -> {
                org.setCreateTime(createTime);
                org.setCreateUser(this.uid);
                org.setTopicId(topicId);
                org.setStatus(1);

                orgList.add(org);
            });

            this.topicOrgMapper.insertList(orgList);
            log.debug("新增任务：下发组织完成！");
        }
    }


    /**
     * 获取任务的完整信息
     * @param topicId
     * @return
     */
    public TopicEntity detail(Long topicId) {
        //查询基本信息
        TopicEntity topic = this.getTopicById(topicId);
        if(topic == null) {
            throw new ApiException("not found", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.OK.value(), StringCanstant.TASK + "详情"));
        }
        //查询文件
        List<TopicFileEntity> files = this.topicFileService.getTopicFileList(topicId);
        topic.setFiles(files);

        //查询内容和选项
        List<TopicContentEntity> contents = this.topicContentService.getTopicContentList(topicId);
        if(!ListUtils.isEmpty(contents)) {
            contents = this.setOpts(contents, topicId);
            topic.setContents(contents);
        }

        //查询所有的组织
        List<TopicOrgEntity> orgs = this.topicOrgService.getTopicOrgList(topicId,null,null);
        topic.setOrgs(orgs);

        return topic;
    }

    /**
     * 设置非文本的内容的子选项
     * @param topicId
     * @return
     */
    public List<TopicContentEntity> setOpts(List<TopicContentEntity> contents, Long topicId) {
        List<TopicOptsEntity> optsList = this.topicOptsService.getTopicOptsList(topicId);
        List<TopicContentEntity> contentsNew = new ArrayList<>();
        contents.forEach( content -> {
            //如果有子选项则进行遍历获取他的子选项
            if(content.getType() == TopicConstant.ContentType.RADIO.getType() ||
                    content.getType() == TopicConstant.ContentType.CHECK_BOX.getType()) {
                List<TopicOptsEntity> opts = new ArrayList<>();
                TopicOptsEntity opt;
                List<TopicOptsEntity> remove = new ArrayList<>();
                for(int i = 0; i < optsList.size(); i++) {
                    opt = optsList.get(i);
                    if(content.getContentId().longValue() == opt.getContentId().longValue()) {
                        opt.setIsDel(0);
                        opt = this.setNull(opt);
                        opts.add(opt);
                        remove.add(opt);
                    }
                }
                optsList.removeAll(remove);
                content.setOpts(opts);
            }
            content.setIsDel(0);
            content = this.setNull(content);
            contentsNew.add(content);
        });
        return contentsNew;
    }

    /**
     * 置空不需要的属性
     * @param content
     * @return
     */
    public TopicContentEntity setNull(TopicContentEntity content) {
        content.setCreateUser(null);
        content.setCreateTime(null);
        content.setUpdateTime(null);
        content.setLastChangeUser(null);
        return content;
    }

    /**
     * 置空不需要的属性
     * @param opt
     * @return
     */
    public TopicOptsEntity setNull(TopicOptsEntity opt) {
        opt.setCreateUser(null);
        opt.setCreateTime(null);
        opt.setUpdateTime(null);
        opt.setLastChangeUser(null);
        return opt;
    }

    /**
     * 根据任务id查询任务详情
     * @param topicId
     * @return
     */
    public TopicEntity getTopicById(Long topicId) {
        if(topicId == null || topicId == 0) {
            return null;
        }
        Example example = new Example(TopicEntity.class);
        example.createCriteria().andEqualTo("topicId", topicId);
        return this.mapper.selectOneByExample(example);
    }

    /**
     * 删除任务
     * @param topicId   任务id
     * @param uid       用户id
     * @param type      0: 删除 1：修改
     */
    public void delete(Long topicId, Long uid, int type) {
        //step1、判断该任务是否被任何组织使用过，即发起活动
        // v3.0.2 2020年8月4日 17:29:39
        // 被使用的的任务也可以删除和修改 任务不支持子选项
//        List<MeetingTopicEntity> list = this.meetingTopicService.getMeetingTopicList(topicId);
//        if(!ListUtils.isEmpty(list)) {
//            throw new ApiException("该活动任务已被使用，不能删除！", new Result<>(errors, 1921, HttpStatus.OK.value(), "该" + StringCanstant.JOB_TASK + "已被使用，不能删除！"));
//        }
        if (type == 0) {
            //删除
            TopicEntity topic = new TopicEntity();
            topic.setTopicId(topicId);
            topic.setIsDel(1);
            topic.setUpdateTime(DateTime.now().toDate());
            topic.setLastChangeUser(uid);
            this.mapper.updateByPrimaryKeySelective(topic);
        } else if (type == 1) {
            //修改
            //step2、删除任务
            //this.mapper.deleteByPrimaryKey(topicId);
            //step3、删除附件
            Example example = new Example(TopicFileEntity.class);
            example.createCriteria().andEqualTo("topicId", topicId);
            this.topicFileMapper.deleteByExample(example);
            //step4、删除内容
            example = new Example(TopicContentEntity.class);
            example.createCriteria().andEqualTo("topicId", topicId);
            this.topicContentMapper.deleteByExample(example);
            //step5、删除子选项
            example = new Example(TopicOptsEntity.class);
            example.createCriteria().andEqualTo("topicId", topicId);
            this.topicOptsMapper.deleteByExample(example);
            //step6、删除已发送的组织
            example = new Example(TopicOrgEntity.class);
            example.createCriteria().andEqualTo("topicId", topicId);
            this.topicOrgMapper.deleteByExample(example);
        }
        // topic删除后刷新移动端首页缓存
        indexService.collectRedis();
    }

    /**
     * 修改任务
     *
     * @param topic
     * @param header v3.0.0 被使用的任务能修改 只有一个content 任务不支持子选项
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(TopicEntity topic, Headers header) {
        Long topicId = topic.getTopicId();
        if (topicId == null || topicId == 0) {
            throw new ApiException("资源不存在", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.OK.value(), StringCanstant.TASK));
        }

        TopicEntity topicDb = this.detail(topicId);
        if (topicDb.getOrgId().longValue() != header.getOid().longValue()) {
            throw new ApiException("修改人组织id与创建的任务组织id不相同", new Result<>(errors, 1927, HttpStatus.OK.value()));
        }
        // 修改基本信息
        Date now = DateTime.now().toDate();
        this.checkParams(topic, header.getOid(), header.getUserId(), header.getOrgName());
        // 通知方式不能修改。修改不会触发通知
        topic.setNoticeType(topicDb.getNoticeType());
        topic.setUpdateTime(now);
        topic.setLastChangeUser(header.getUserId());
        topic.setLastChangeUser(uid);
        topic.setId(topic.getTopicId());
        this.mapper.updateByPrimaryKeySelective(topic);

        // 修改附件
        List<TopicFileEntity> files = CheckDataOperateUtils.check(topic.getFiles(), topicDb.getFiles(), TopicFileEntity.class);
        List<Long> delFiles = files.stream().filter(te -> te.getOperateTag().equals(2))
                .map(TopicFileEntity::getTopicFileId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(delFiles)) {
            Example example = new Example(TopicFileEntity.class);
            example.createCriteria()
                    .andEqualTo("topicId", topic.getTopicId())
                    .andIn("topicFileId", delFiles);
            this.topicFileMapper.deleteByExample(example);
        }
        List<TopicFileEntity> addFiles = files.stream().filter(te -> te.getOperateTag().equals(1))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addFiles)) {
            addFiles.forEach(file -> {
                file.setTopicFileId(null);
                file.setTopicId(topicId);
                file.setCreateTime(DateTime.now().toDate());
                file.setCreateUser(this.uid);
                this.topicFileMapper.insertSelective(file);
            });
        }
        // 修改内容 只有一个内容，无子选项
        if (CollectionUtils.isNotEmpty(topic.getContents()) && CollectionUtils.isNotEmpty(topicDb.getContents())) {
            TopicContentEntity upContentEntity = new TopicContentEntity();
            upContentEntity.setContentId(topicDb.getContents().get(0).getContentId());
            upContentEntity.setUpdateTime(now);
            upContentEntity.setLastChangeUser(uid);
            upContentEntity.setName(topic.getContents().get(0).getName());
            upContentEntity.setDescription(topic.getContents().get(0).getDescription());
            this.topicContentMapper.updateByPrimaryKeySelective(upContentEntity);
        }

        // 修改执行组织
        List<TopicOrgEntity> orgList = CheckDataOperateUtils.check(topic.getOrgs(), topicDb.getOrgs(), TopicOrgEntity.class);
        List<Long> delOrgList = orgList.stream().filter(to -> to.getOperateTag().equals(2))
                .map(TopicOrgEntity::getTopicOrgId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(delOrgList)) {
            Example example = new Example(TopicOrgEntity.class);
            example.createCriteria()
                    .andEqualTo("topicId", topic.getTopicId())
                    .andIn("topicOrgId", delOrgList);
            this.topicOrgMapper.deleteByExample(example);
        }
        //新增组织 发送给各个需要派发的组织
        List<TopicOrgEntity> addOrgList = orgList.stream().filter(to -> to.getOperateTag().equals(1)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addOrgList)) {
            send(addOrgList, topicId);
        }
        // topic更新后刷新移动端首页缓存
        indexService.collectRedis();
    }


    /**
     * 修改文件
     * @param files
     * @param topicId
     */
    public void updateFiles(List<TopicFileEntity> files, Long topicId) {
        if(!ListUtils.isEmpty(files)) {
            //申明需要新增的
            List<TopicFileEntity> addList = new ArrayList<>();

            files.forEach( file -> {
                //新增
                if (file.getTopicFileId() == null && file.getIsDel() == TopicConstant.IS_DEL_0) {
                    addList.add(file);
                }
                //删除
                if (file.getTopicFileId() != null && file.getIsDel() == TopicConstant.IS_DEL_1) {
                    this.topicFileMapper.deleteByPrimaryKey(file.getTopicFileId());
                }
            });
            //执行新增
            if(!ListUtils.isEmpty(addList)) {
                this.addFiles(addList, topicId);
            }
        }
    }

    /**
     * 修改任务内容
     * @param contents
     * @param topicId
     */
    public void updateContents(List<TopicContentEntity> contents, Long topicId, Long uid) {
        this.uid = uid;
        if(!ListUtils.isEmpty(contents)) {
            //申明需要新增的
            List<TopicContentEntity> addList = new ArrayList<>();
            contents.forEach( content -> {
                //step1、维护内容
                //新增
                if(content.getContentId() == null) {
                    addList.add(content);
                }
                //修改
                if(content.getContentId() != null && content.getIsDel() == TopicConstant.IS_DEL_0) {
                    content.setUpdateTime(DateTime.now().toDate());
                    content.setLastChangeUser(uid);
                    //维护子项
                    List<TopicOptsEntity> opts = content.getOpts();
                    List<TopicOptsEntity> addOptsList = new ArrayList<>();
                    opts.forEach( opt -> {
                        //新增子选项
                        if( opt.getOptsId() == null ) {
                            opt = this.bulidOpt(opt, content.getContentId(), topicId, uid);
                            addOptsList.add(opt);
                        }
                        //修改子选项
                        if(opt.getOptsId() != null && opt.getIsDel() == TopicConstant.IS_DEL_0) {
                            opt.setUpdateTime(DateTime.now().toDate());
                            opt.setLastChangeUser(uid);
                            this.topicOptsMapper.updateByPrimaryKeySelective(opt);
                        }
                        //删除子选项
                        if(opt.getOptsId() != null && opt.getIsDel() == TopicConstant.IS_DEL_1) {
                            this.topicOptsMapper.deleteByPrimaryKey(opt.getOptsId());
                        }
                    });
                    this.topicContentMapper.updateByPrimaryKeySelective(content);

                    //批量新增内容的选项
                    this.addOpts(addOptsList, topicId, content.getContentId());
                }
                //删除
                if(content.getContentId() != null && content.getIsDel() == TopicConstant.IS_DEL_1) {
                    this.topicContentMapper.deleteByPrimaryKey(content.getContentId());
                    this.topicOptsService.deleteByContentId(content.getContentId());
                }

            });
            //批量新增内容
            this.addContent(addList, topicId);
        }
    }

    /**
     * 构造子选项对象
     * @param opt
     * @param contentId
     * @param topicId
     * @param uid
     * @return
     */
    public TopicOptsEntity bulidOpt(TopicOptsEntity opt, Long contentId, Long topicId, Long uid) {
        opt.setCreateTime(DateTime.now().toDate());
        opt.setCreateUser(uid);
        opt.setContentId(contentId);
        opt.setIsDel(0);
        opt.setTopicId(topicId);
        return opt;
    }

    /**
     * 修改组织派发的人员
     * @param orgs
     * @param delOrgs
     * @param topicId
     */
    public void updateOrg(List<TopicOrgEntity> orgs, List<TopicOrgEntity> delOrgs, Long topicId) {
        //新增
        this.send(orgs, topicId);
        //删除
        if(!ListUtils.isEmpty(delOrgs)) {
            delOrgs.forEach( org -> {
                this.topicOrgService.delTopicOrg(topicId, org.getOrgId());
            });
        }
    }


    /**
     * 获取一个组织收到或者自己创建的所有任务
     * @param name
     * @param oid
     * @return
     */
    public List<TopicEntity> getTopicList(String name, Long oid) {
        if(oid == null) {
            return new ArrayList<>();
        }
        //查询t_topic 属于自己创建的
        Example example = new Example(TopicEntity.class);
        example.selectProperties("topicId", "name", "startTime", "endTime");

        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", oid).andEqualTo("isDel",0);
        if(!StringUtils.isEmpty(name)) {
            criteria.andLike("name", "%" + name + "%");
        }
        List<TopicEntity> topicList = this.mapper.selectByExample(example);
        List<TopicEntity> orgTopicList = this.mapper.getTopicListFromOrg(name, oid);
        if(ListUtils.isEmpty(topicList)) {
            topicList = new ArrayList<>();
        }
        if(ListUtils.isEmpty(orgTopicList)) {
            orgTopicList = new ArrayList<>();
        }
        topicList.addAll(orgTopicList);
        return topicList;
    }

    /**
     * 根据id批量查询任务
     */
    public List<TopicEntity> findByOrgIdAndIds(long orgId,List<Long> ids) {
        return this.mapper.findByOrgIdAndIds(orgId,ids);
    }


    /**
     * 分页查询当前组织派发的任务
     *
     * @param name       任务名称
     * @param oid        创建组织
     * @param sStartTime 开始时间的起始时间
     * @param eStartTime 开始时间的结束时间
     * @param pageBean   分页信息
     * @return Page<TopicEntity>
     */
    public Page<TopicEntity> page(String name, Long oid, Date sStartTime, Date eStartTime, PageBean pageBean) {
        Page<TopicEntity> page = PageHelper.startPage(pageBean.getPageNo(), pageBean.getPageSize())
                .doSelectPage(() -> this.mapper.find(name, oid, sStartTime, eStartTime));
        return page;
    }

    /**
     * 结束时间转换
     * @param date
     * @return
     * @throws ParseException
     */
    public Date getDate(Date date) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat( "yyyy-MM-dd");
            String dates = formatter.format(date) + " 23:59:59";
            java.text.SimpleDateFormat formatters = new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss");
            return formatters.parse(dates);
        } catch (Exception e) {
            log.error("时间转换出错" + e.getMessage(), e);
        }
        return date;
    }

    /**
     * 发起活动、修改活动、直接填报时添加任务
     *
     * @param header  Headers
     * @param meeting MeetingEntity
     * @param topic   TopicEntity
     */
    public void addTopicByMeeting(Headers header, MeetingEntity meeting, TopicEntity topic) {
        topic.setStatus(TopicConstant.STATUS_OWNER);
        int code = addInner(topic, header.getOid(), header.getUserId(), header.getOrgName(), 0);
        topic.setTopicId(topic.getTopicId());
        if (code != 0) {
            log.debug("新增任务出错，返回状态码 code = {}", code);
            throw new ApiException("新增任务出错", new Result<>(errors, code, HttpStatus.OK.value()));
        }
        // 任务添加到MeetingAndResultForm.meeting.topics 中
        if (meeting.getTopics() == null) {
            meeting.setTopics(new ArrayList<>());
        }
        MeetingTopicEntity mte = new MeetingTopicEntity();
        meeting.getTopics().add(mte);
        mte.setIsNewAdd(true);
        mte.setTopicId(topic.getTopicId());
        mte.setTopicName(topic.getName());
    }

}
