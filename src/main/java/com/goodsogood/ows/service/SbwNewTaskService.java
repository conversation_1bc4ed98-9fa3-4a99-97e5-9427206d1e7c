package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.common.pojo.PageBean;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.SbwTaskEntity;
import com.goodsogood.ows.model.db.SbwTaskFlowEntity;
import com.goodsogood.ows.model.db.SbwTaskOrgEntity;
import com.goodsogood.ows.model.vo.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 南岸区信创新建任务
 *
 * <AUTHOR>
 * @version 2021/7/29
 */
@Log4j2
@Service
public class SbwNewTaskService {

    private final Errors errors;
    private final SbwTaskMapper sbwTaskMapper;
    private final SbwTaskOrgMapper sbwTaskOrgMapper;
    private final SbwHandleMapper sbwHandleMapper;
    private final SbwNewTaskMapper sbwNewTaskMapper;
    private final SbwAsyncService sbwAsyncService;
    private final SbwShiftTaskMapper sbwShiftTaskMapper;


    public SbwNewTaskService(Errors errors, SbwTaskMapper sbwTaskMapper, SbwTaskOrgMapper sbwTaskOrgMapper, SbwHandleMapper sbwHandleMapper, SbwNewTaskMapper sbwNewTaskMapper, SbwAsyncService sbwAsyncService, SbwShiftTaskMapper sbwShiftTaskMapper) {
        this.errors = errors;
        this.sbwTaskMapper = sbwTaskMapper;
        this.sbwTaskOrgMapper = sbwTaskOrgMapper;
        this.sbwHandleMapper = sbwHandleMapper;
        this.sbwNewTaskMapper = sbwNewTaskMapper;
        this.sbwAsyncService = sbwAsyncService;
        this.sbwShiftTaskMapper = sbwShiftTaskMapper;
    }

    /**
     * 新建任务-转办单
     *
     * @param headers 头
     * @param form    任务提交
     * @return 成功结果
     */
    public Long addTurnTask(HttpHeaders headers, SbwNewTaskForm form) {
        //已读
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Date date = new Date();
        Integer flags = form.getFlag();
        Long userId = header.getUserId();
        Long regionId = header.getRegionId();
        Long orgId = header.getOid();
        String orgName = header.getOrgName();
        form.setRegionId(regionId);//同步支部信息
        SbwTaskEntity entity = new SbwTaskEntity();
        entity.setCreateUser(userId);
        entity.setRegionId(regionId);
        entity.setOrgId(orgId);
        entity.setOrgName(orgName);
        entity.setTaskType(form.getTaskType());
        entity.setTitle(form.getTitle() != null ? form.getTitle().trim() : null);
        entity.setNumber(form.getNumber() != null ? form.getNumber().trim() : null);
        entity.setTimeType(form.getTimeType());
        entity.setBeginTime(form.getBeginTime());
        entity.setEndTime(getDate(form.getEndTime()));
        entity.setTypeId(form.getTypeId());
        entity.setSource(form.getSource() != null ? form.getSource().trim() : null);
        entity.setContent(form.getContent() != null ? form.getContent().trim() : null);
        entity.setVerifyOrgId(form.getVerifyOrgId());
        entity.setVerifyOrgName(form.getVerifyOrgName() != null ? form.getVerifyOrgName().trim() : null);
        entity.setFileId(form.getFileId() != null ? form.getFileId().trim() : null);
        entity.setFilename(form.getFilename() != null ? form.getFilename().trim() : null);
        entity.setRemark(form.getRemark() != null ? form.getRemark().trim() : null);
        entity.setIsHandle(form.getIsHandle());
        entity.setNoHandleContent(form.getNoHandleContent());
        entity.setIsDel(0);
        entity.setCreateTime(date);
        Example example;
        //提交
        if (flags == 1) {
            if (ObjectUtils.isEmpty(form)) {
                throw new ApiException("新建失败,请正确传递参数",
                        new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "未获取到数据"));
            }
            checkTask(entity);
            entity.setStatus(getFlag(form));
            int row;
            //查询任务是否是提交过后 并且没有开始
            int i;//1:不予处理  2:其他
            i = !ObjectUtils.isEmpty(form.getIsHandle()) && form.getIsHandle() == 0 ? 1 : 0;
            if (i == 1) {
                entity.setStatus(Constant.SHIFT_FLOW_NOT_HANDLE);
            }
            if (ObjectUtils.isEmpty(form.getTaskId())) {//直接提交
                row = sbwTaskMapper.insertUseGeneratedKeys(entity);
                if (row != 0) {
                    if (i != 1) {
                        addTaskOrg(entity, form);
                    }
                } else {
                    throw new ApiException("新建工作任务失败",
                            new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "新建组织失败"));
                }
                return entity.getTaskId();
            } else {
                example = new Example(SbwTaskEntity.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("taskId", form.getTaskId());
                SbwTaskEntity taskEntity = sbwTaskMapper.selectOneByExample(example);
                if (ObjectUtils.isEmpty(taskEntity)) {
                    throw new ApiException("新建任务失败",
                            new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "未查询到该任务id"));
                }
                if(!StringUtils.isEmpty(taskEntity.getFileId())){
                    if (null == entity.getFileId()){//如果上传的文件被删了就更新数据库
                        if (null != taskEntity.getFileId()){
                            sbwAsyncService.updateFile(taskEntity.getTaskId(),1);
                        }
                    }
                }
                // 状态为普通任务正常执行状态检查是否修改过组织信息
                if (sbwNewTaskMapper.findStartStatus(form.getTaskId(), "(2,3,4)") != 0) {
                    taskEntity.setStatus(getFlag(form));
                    checkOrg(taskEntity, form);
                    sbwAsyncService.TaskTitle(form.getTaskId(), regionId, form.getTitle(), form.getBeginTime(), getDate(form.getEndTime()), taskEntity.getStatus());
                    entity.setIsHandle(taskEntity.getIsHandle());
                    log.debug("组织修改:添加新的组织成功");
                    // 状态为草稿新增组织信息
                } else if (sbwNewTaskMapper.findStartStatus(form.getTaskId(), "(1)") != 0 || null == form.getIsHandle()) {
                    taskEntity.setStatus(getFlag(form));
                    addTaskOrg(taskEntity, form);
                    entity.setStatus(getFlag(form));
                    int rows = sbwTaskMapper.updateByExample(entity, example);
                    if (rows == 0) {
                        throw new ApiException("新建任务失败",
                                new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "草稿任务已有派发组织"));
                    }
                    //以下两条异步修改支部数据
                    sbwAsyncService.TaskTitle(form.getTaskId(), regionId, form.getTitle(), form.getBeginTime(), getDate(form.getEndTime()), entity.getStatus());
                    return -2L;//编辑任务成功
                } else {
                    form.setStatus(getFlag(form));
                    if (form.getIsHandle() != null) {
                        int handle = form.getIsHandle() == 0 ? Constant.SHIFT_FLOW_NOT_HANDLE : form.getIsHandle();
                        if (handle == Constant.SHIFT_FLOW_NOT_HANDLE) {//不予处理
                            entity.setStatus(handle);
                            sbwAsyncService.shiftFlow(header, form.getTaskId(), handle, form.getNoHandleContent(), date);
                            sbwAsyncService.shiftStatus(form.getTaskId(), handle);
                        } else if (handle == 1) {//处理
                            addTaskOrg(taskEntity, form);
                            sbwAsyncService.TaskTitle(form.getTaskId(), regionId, form.getTitle(), form.getBeginTime(), getDate(form.getEndTime()), form.getStatus());
                            sbwAsyncService.shiftStatus(form.getTaskId(), handle);
                            sbwAsyncService.shiftFlow(header, form.getTaskId(), form.getIsHandle(), form.getNoHandleContent(), date);
                        }
                    } else {
                        sbwAsyncService.TaskTitle(form.getTaskId(), regionId, form.getTitle(), form.getBeginTime(), getDate(form.getEndTime()), form.getStatus());
                    }
                }
                entity.setUpdateUser(userId);
                entity.setCreateUser(taskEntity.getCreateUser());
                entity.setUpdateTime(date);
                if (taskEntity.getStatus() > 0) {
                    sbwAsyncService.isRead(header.getOid(), form.getTaskId(), 2);
                } else {
                    sbwAsyncService.isRead(header.getOid(), form.getTaskId(), 1);
                }
                sbwTaskMapper.updateByExample(entity, example);
                return -2L;//编辑任务成功
            }
            //保存
        } else {
            if (ObjectUtils.isEmpty(form.getTaskId())) {
                entity.setStatus(Constant.TASK_STATUS_DRAFT);
                sbwTaskMapper.insertUseGeneratedKeys(entity);
                return entity.getTaskId();
            } else {
                example = new Example(SbwTaskEntity.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("taskId", form.getTaskId());
                SbwTaskEntity taskEntity = sbwTaskMapper.selectOneByExample(example);
                if (!ObjectUtils.isEmpty(taskEntity)) {
                    if (taskEntity.getStatus() > 0) {
                        entity.setStatus(Constant.TASK_STATUS_DRAFT);
                    } else {
                        entity.setStatus(taskEntity.getStatus());
                    }
                    sbwTaskMapper.updateByExample(entity, example);
                } else {
                    throw new ApiException("新建任务失败",
                            new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "未查询到该任务id"));
                }
            }
            return -1L;//保存任务成功
        }
    }

    /*根据时间判断获取flag*/
    public Integer getFlag(SbwNewTaskForm form) {
        //当前时间
        Date date = getTodayTime();
        Date date1 = new Date();
        log.debug("当前开始时间->:" + form.getBeginTime());
        //任务状态
        if (form.getBeginTime().after(date) && getDate(form.getEndTime()).after(date1)) {
            return Constant.TASK_STATUS_NP;
        } else if (getDate(form.getEndTime()).after(date1)) {
            return Constant.TASK_STATUS_ING;
        } else {
            return Constant.TASK_STATUS_END;
        }
    }

    /*过滤重复的组织id,然后将新增的几个组织信息添加进去*/
    public void checkOrg(SbwTaskEntity taskEntity, SbwNewTaskForm form) {
        if (!CollectionUtils.isEmpty(form.getOrg())) {
            List<Long> thisOrgIds = form.getOrg().stream().map(SbwNewTaskForm.Org::getOrgId).collect(Collectors.toList());
            Example example = new Example(SbwTaskEntity.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("taskId", form.getTaskId());
            SbwTaskEntity sbwTaskEntity = sbwTaskMapper.selectOneByExample(example);
            if (!ObjectUtils.isEmpty(sbwTaskEntity)) {
                List<Long> orgIds = sbwTaskMapper.findOrgIds(form.getTaskId(), taskEntity.getRegionId());
                //复制支部id到该变量
                List<Long> newOrgIds = new ArrayList<>(orgIds);
                log.debug("获取旧支部id->" + newOrgIds);
                if (!CollectionUtils.isEmpty(orgIds)) {
                    //如果删除过后如果为空则返回false 如果有相同的被删除了则返回true
                    if (orgIds.removeAll(thisOrgIds) || !CollectionUtils.isEmpty(orgIds)) {
                        orgIds.forEach(x -> {
                            //删除新数据不相同的支部
                            sbwTaskMapper.deleteOrg(x, form.getTaskId());
                            log.debug("删除");
                        });
                    }

                    if (thisOrgIds.removeAll(newOrgIds) || !CollectionUtils.isEmpty(thisOrgIds)) {
                        log.debug("过滤旧数据与新数据相同的元素,取出不同的元素->>>>>" + thisOrgIds);
                        if (ObjectUtils.isEmpty(thisOrgIds)) {
                            return;
                        }
                        List<SbwNewTaskForm.Org> orgList = new ArrayList<>();
                        thisOrgIds.forEach(x -> {
                            form.getOrg().forEach(o -> {
                                if (o.getOrgId().equals(x)) {
                                    SbwNewTaskForm.Org org = new SbwNewTaskForm.Org();
                                    org.setOrgId(o.getOrgId());
                                    org.setOrgName(o.getOrgName());
                                    orgList.add(org);
                                }
                            });
                        });
                        form.setOrg(orgList);
                        addTaskOrg(taskEntity, form);
                    }
                } else {
                    log.debug("没有旧数据新增2->>>>>" + thisOrgIds);
                    addTaskOrg(taskEntity, form);
                }
            }
        } else {
            throw new ApiException("新建任务失败",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "org组织信息不能为空"));
        }
    }

    /*检查转办单参数*/
    public void checkTask(SbwTaskEntity entity) {
        if (ObjectUtils.isEmpty(entity.getOrgId())) {
            //判断orgId是否为空
            throw new ApiException("新建任务失败,orgId不能为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "orgId不能为空"));
        } else if (null == entity.getTypeId()) {
            //判断orgName是否为空
            throw new ApiException("新建任务失败,typeId舆情分类id为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "typeId舆情分类id不能为空"));
        } else if (StringUtils.isEmpty(entity.getOrgName())) {
            //判断orgName是否为空
            throw new ApiException("新建任务失败,orgName支部名称为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "orgName支部名称不能为空"));
        } else if (ObjectUtils.isEmpty(entity.getTaskType())) {
            //判断taskType是否为空
            throw new ApiException("新建任务失败,taskType任务类型为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "taskType任务类型不能为空"));
        } else if (StringUtils.isEmpty(entity.getTitle())) {
            //判断title是否为空
            throw new ApiException("新建任务失败,title标题为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "title标题不能为空"));
        } else if (StringUtils.isEmpty(entity.getNumber())) {
            //判断number编号是否为空
            throw new ApiException("新建任务失败,number编号为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "number编号不能为空"));
        } else if (ObjectUtils.isEmpty(entity.getTimeType())) {
            //判断timeType时间类型是否为空
            throw new ApiException("新建任务失败,timeType时间类型为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "timeType时间类型不能为空"));
        } else if (ObjectUtils.isEmpty(entity.getBeginTime())) {
            //判断beginTime是否为空
            throw new ApiException("新建任务失败,beginTime开始时间为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "beginTime开始时间不能为空"));
        } else if (ObjectUtils.isEmpty(entity.getEndTime())) {
            //判断endTime是否为空
            throw new ApiException("新建任务失败,endTime结束时间为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "endTime结束时间不能为空"));
        } else if (StringUtils.isEmpty(entity.getSource())) {
            //判断source是否为空,字数是否超过2000
            throw new ApiException("新建任务失败,source舆情来源为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "source舆情来源不能为空"));
        } else if (StringUtils.isEmpty(entity.getContent())) {
            //判断content是否为空,字符是否超过2000
            throw new ApiException("新建任务失败,content舆情概要为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "content舆情概要不能为空"));
        } else if (ObjectUtils.isEmpty(entity.getVerifyOrgId())) {
            //判断verifyOrgId是否为空
            throw new ApiException("新建任务失败,verifyOrgId审核组织id为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "verifyOrgId审核组织id不能为空"));
        } else if (StringUtils.isEmpty(entity.getVerifyOrgName())) {
            //判断verifyOrgName是否为空
            throw new ApiException("新建任务失败,verifyOrgName审核组织名",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "verifyOrgName审核组织名不能为空"));
        } else if (ObjectUtils.isEmpty(entity.getCreateUser())) {
            //判断createUser是否为空
            throw new ApiException("新建任务失败,createUser创建用户id为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "createUser创建用户id不能为空"));
        } else if (!ObjectUtils.isEmpty(entity.getIsHandle())) {
            if (entity.getIsHandle().equals(Constant.SHIFT_FLOW_NOT_HANDLE)) {
                if (StringUtils.isEmpty(entity.getNoHandleContent().trim())) {
                    //判断createUser是否为空
                    throw new ApiException("新建任务失败,不予处理不能为空",
                            new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "请填写不予处理理由"));
                }
            }
        }
    }

    /*新增组织信息*/
    public void addTaskOrg(SbwTaskEntity taskEntity, SbwNewTaskForm form) {
        int wordRow;
        if (!CollectionUtils.isEmpty(form.getOrg())) {
            List<SbwTaskOrgEntity> list = new ArrayList<>();
            form.getOrg().forEach(x -> {
                SbwTaskOrgEntity entity = new SbwTaskOrgEntity();
                entity.setRegionId(taskEntity.getRegionId());
                entity.setTaskId(taskEntity.getTaskId());
                entity.setOrgId(x.getOrgId());
                entity.setOrgName(x.getOrgName());
                entity.setTitle(taskEntity.getTitle());
                entity.setType(0);
                entity.setStatus(taskEntity.getStatus());
                entity.setIsRead(0);
                entity.setBeginTime(taskEntity.getBeginTime());
                entity.setEndTime(getDate(taskEntity.getEndTime()));
                entity.setCreateTime(new Date());
                list.add(entity);
            });
            wordRow = sbwTaskOrgMapper.insertList(list);
        } else {
            throw new ApiException("新建任务失败,未选择组织信息",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "未选择组织信息"));
        }
        if (wordRow <= 0) {
            throw new ApiException("新建工作任务创建支部信息失败",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "创建支部信息失败"));
        }
    }

    /*获取当日时间*/
    public static Date getTodayTime() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /*结束时间转换*/
    public static Date getDate(Date date) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String dates = formatter.format(date) + " 23:59:59";
            java.text.SimpleDateFormat formatters = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return formatters.parse(dates);
        } catch (Exception e) {
            log.error("时间转换出错" + e.getMessage(), e);
        }
        return date;
    }

    /**
     * 我的任务-查询列表
     *
     * @param headers
     * @param title
     * @param beginTime
     * @param endTime
     * @param pageBean
     * @return
     */
    public Page<SbwTaskListForm> getTask(HttpHeaders headers, String title, Date beginTime, Date endTime, PageBean pageBean) {
        //如果时间为空则查询全部
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return PageHelper.startPage(pageBean.getPageNo(), pageBean.getPageSize())
                .doSelectPage(() -> this.sbwTaskMapper.find(header.getOid(), title, beginTime, endTime));
    }

    /**
     * 查看
     *
     * @param header
     * @param taskId
     * @return
     */
    public SbwTaskForm taskDetails(HeaderHelper.SysHeader header, Long taskId) {
        SbwTaskForm form = details(header.getOid(), taskId, 1);
        return form == null ? new SbwTaskForm() : form;
    }

    /**
     * 查询各个任务状态下的组织列表
     *
     * @param taskId
     * @param flag
     * @return
     */
    public List<SbwTaskOrgListForm> findOrg(Long taskId, Integer flag) {
        List<SbwTaskOrgListForm> forms;
        switch (flag) {
            case 1:
                forms = sbwNewTaskMapper.findAdoptOrg(taskId);
                break;

            case 2:
                forms = sbwNewTaskMapper.findRejectOrg(taskId);
                break;

            case 3:
                forms = sbwNewTaskMapper.findNotAdoptOrg(taskId);
                break;

            case 4:
                forms = sbwNewTaskMapper.findFeedbackOrg(taskId);
                break;

            case 5:
                forms = sbwNewTaskMapper.findCheckOrg(taskId);
                break;

            case 6:
                forms = sbwNewTaskMapper.findNotFeedbackOrg(taskId);
                break;
            default:
                forms = new ArrayList<>();
        }
        return forms;
    }

    /**
     * (发布任务)页面展示
     *
     * @param headers   头
     * @param title     标题
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param pageBean  分页类
     * @return 分页数据
     */
    public Page<SbwTaskListForm> releaseTaskList(HttpHeaders headers, String title, Date beginTime, Date endTime, PageBean pageBean) {
        //如果时间为空则查询全部
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return PageHelper.startPage(pageBean.getPageNo(), pageBean.getPageSize())
                .doSelectPage(() -> this.sbwNewTaskMapper.releaseTaskList(header.getRegionId(), header.getOid(), title, beginTime, endTime));
    }

    /**
     * 查看
     *
     * @return
     */
    public SbwTaskForm releaseTaskDetails(Long oid, Long taskId) {
        SbwTaskForm form = details(oid, taskId, 1);
        return form == null ? new SbwTaskForm() : form;
    }

    /**
     * 删除
     */
    public String delTask(HttpHeaders headers, Long taskId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long orgId = header.getOid();
        //判断是否是本人操作

        //修改删除状态
        Example example = new Example(SbwTaskEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", orgId)
                .andEqualTo("taskId", taskId)
                .andEqualTo("isDel", 0);
        SbwTaskEntity entity = sbwTaskMapper.selectOneByExample(example);
        if (!ObjectUtils.isEmpty(entity)) {
            if (entity.getOrgId().equals(orgId)) {
                sbwNewTaskMapper.updateIsDel(taskId);
            } else {
                //判断权限
                throw new ApiException("删除失败,您不是该支部成员,没有操作权限",
                        new Result<>(errors, 3007, HttpStatus.INTERNAL_SERVER_ERROR.value(), "您不是该支部成员,没有操作权限"));
            }
        } else {
            throw new ApiException("删除失败,未查询到该task_id",
                    new Result<>(errors, 3007, HttpStatus.INTERNAL_SERVER_ERROR.value(), "未查询到该task_id"));
        }
        return "删除成功";
    }

    /**
     * 展示我的任务提交转办单详情
     *
     * @param headers 头
     * @param taskId  任务id
     * @param oId     选择的支部id
     * @return 转办单列表
     */
    public SbwTaskForm showTask(HttpHeaders headers, Long taskId, Integer flag, Long oId) {
        if (ObjectUtils.isEmpty(taskId)) {
            throw new ApiException("查询失败,请传递task_id数据",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "请传递task_id数据"));
        }
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long regionId = header.getRegionId();
        //任务表查询开始
        Example example = new Example(SbwTaskEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("regionId", regionId)
                .andEqualTo("taskId", taskId)
                .andEqualTo("isDel", 0);
        SbwTaskEntity entity = sbwTaskMapper.selectOneByExample(example);
        SbwTaskForm form = new SbwTaskForm();
        if (!ObjectUtils.isEmpty(entity)) {
            BeanUtils.copyProperties(entity, form);
            if (entity.getStatus() == 1 || entity.getStatus() == -1){
                form.setIsHandle(entity.getIsHandle());
                form.setNoHandleContent(entity.getNoHandleContent());
            }else {
                form.setIsHandle(null);
                form.setNoHandleContent(null);
            }
            if (entity.getStatus() < 0) {
                sbwAsyncService.isRead(header.getOid(), taskId, 1);
            } else {
                sbwAsyncService.isRead(header.getOid(), taskId, 2);
            }
            log.debug("任务列表->>" + form);
        } else {
            throw new ApiException("查询列表失败",
                    new Result<>(errors, 3006, HttpStatus.INTERNAL_SERVER_ERROR.value(), "未获取到该任务信息"));
        }
        //任务表查询结束

        //查询执行记录开始
        Long orgId = header.getOid();
        if (flag == 2) {
            orgId = oId;
        }
        List<SbwTaskFlowEntity> sbwTaskFlowEntities = sbwTaskMapper.findRecord(taskId, regionId, orgId);
        log.debug("执行记录查询->" + sbwTaskFlowEntities);
        List<SbwTaskForm.ExecutiveLogging> record = new ArrayList<>();
        if (!CollectionUtils.isEmpty(sbwTaskFlowEntities)) {
            sbwTaskFlowEntities.forEach(x -> {
                SbwTaskForm.ExecutiveLogging logging = new SbwTaskForm.ExecutiveLogging();
                logging.setType(x.getType());
                logging.setTaskId(x.getTaskId());
                logging.setCheckContent(x.getContent());
                logging.setOrgName(x.getOperateOrgName());
                logging.setTime(x.getCreateTime());
                record.add(logging);
            });
            form.setRecord(record);
        } else {
            form.setRecord(null);
        }
        //查询执行记录结束

        //查询任务操作信息开始
        SbwHandleForm handle = null;
        SbwHandleForm checkHandle;
        SbwTaskForm.CheckMessage checkMessage = new SbwTaskForm.CheckMessage();
        //我的任务-提交转办单,审核任务-提交转办单
        if (flag == 1) {
            handle = sbwTaskMapper.findHandle(taskId, regionId, flag, null, header.getOid());
            if (!ObjectUtils.isEmpty(handle)) {
                checkHandle = sbwTaskMapper.findHandle(taskId, regionId, 2, 2, header.getOid());
                log.debug("任务审核-查询审核详情->" + checkHandle);
                receiptFiles(handle, form);
                if (!ObjectUtils.isEmpty(checkHandle)) {
                    checkMessage.setHandleId(checkHandle.getHandleId());
                    checkMessage.setHandleStatus(checkHandle.getHandleStatus());
                    checkMessage.setHandleComment(checkHandle.getHandleComment());
                    form.setCheckHandle(checkMessage);
                }
            }
        } else if (flag == 2) {
            handle = sbwTaskMapper.findHandle(taskId, regionId, 1, null, oId);
            if (!ObjectUtils.isEmpty(handle)) {
                checkHandle = sbwTaskMapper.findHandle(taskId, regionId, flag, null, oId);
                log.debug("任务审核-查询审核详情->" + checkHandle);
                receiptFiles(handle, form);
                if (!ObjectUtils.isEmpty(checkHandle)) {
                    checkMessage.setHandleId(checkHandle.getHandleId());
                    checkMessage.setHandleStatus(checkHandle.getHandleStatus());
                    checkMessage.setHandleComment(checkHandle.getHandleComment());
                    form.setCheckHandle(checkMessage);
                }
            }
            log.debug("任务审核-提交转办单->" + handle);
        }
        form.setHandle(handle);
        //查询任务操作信息结束

        //TODO 可能会出现异常
        files(entity, form);
        return form;
    }

    /*获取回执文件信息*/
    public void receiptFiles(SbwHandleForm handle, SbwTaskForm form) {
        if (!StringUtils.isEmpty(handle.getFileId())) {
            String[] s1 = handle.getFileId().split(",");
            String[] s2 = handle.getFilename().split(",");
            if (s2.length == 0) {
                throw new ApiException("文件名获取失败",
                        new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "回执文件名获取失败"));
            }
            List<SbwTaskForm.ReceiptFile> receiptFiles = new ArrayList<>(s1.length);
            for (int i = 0; i < s1.length; i++) {
                SbwTaskForm.ReceiptFile file = new SbwTaskForm.ReceiptFile();
                file.setReceiptFileId(Long.parseLong(s1[i]));
                file.setReceiptFilename(s2[i]);
                receiptFiles.add(file);
            }
            form.setReceiptFiles(receiptFiles);
        }
    }

    /*获取主任务文件信息*/
    public void files(SbwTaskEntity entity, SbwTaskForm form) {
        if (!StringUtils.isEmpty(entity.getFileId())) {
            String[] s1 = entity.getFileId().split(",");
            String[] s2 = entity.getFilename().split(",");
            if (s2.length == 0) {
                throw new ApiException("文件名获取失败",
                        new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "文件名获取失败"));
            }
            List<SbwTaskForm.File> files = new ArrayList<>(s1.length);
            for (int i = 0; i < s1.length; i++) {
                SbwTaskForm.File file = new SbwTaskForm.File();
                file.setFileId(Long.parseLong(s1[i]));
                file.setFilename(s2[i]);
                files.add(file);
            }
            form.setFiles(files);
        }
    }


    /**
     * 生成打印单
     */
    public SbwTaskForm createPrintWord(HeaderHelper.SysHeader header, Long taskId, Long orgId) {
        Long checkOrgId = header.getOid();
        if (ObjectUtils.isEmpty(orgId)) {
            return sbwNewTaskMapper.createPrintWord(taskId, checkOrgId);
        } else {
            return sbwNewTaskMapper.createPrintWord(taskId, orgId);
        }
    }

    /*根据task_id查询任务详情*/
    public SbwTaskForm details(Long oid, Long taskId, Integer flag) {
        Example example = new Example(SbwTaskEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", taskId);
        SbwTaskEntity entity = sbwTaskMapper.selectOneByExample(example);
        SbwTaskForm form = new SbwTaskForm();
        if (!ObjectUtils.isEmpty(entity)) {
            if (entity.getStatus() < 0) {
                sbwAsyncService.isRead(oid, taskId, 1);
            } else {
                sbwAsyncService.isRead(oid, taskId, 2);
            }
            BeanUtils.copyProperties(entity, form);
            int row = sbwShiftTaskMapper.checkShiftTask(taskId);
            if (row != 0) {
                form.setRecord(taskFlow(taskId));
            }else {
                form.setIsHandle(-1);
            }
            files(entity, form);
            return form;
        }
        return null;
    }

    /**
     * 代办任务流水
     *
     * @param taskId
     * @return
     */
    public List<SbwTaskForm.ExecutiveLogging> taskFlow(Long taskId) {
        List<SbwTaskForm.ExecutiveLogging> list = sbwShiftTaskMapper.taskFlow(taskId);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        StringBuilder builder = new StringBuilder();
        int type;
        int length;
        for (SbwTaskForm.ExecutiveLogging flow : list) {
            if (StringUtils.isBlank(flow.getTypeLink())) {
                continue;
            }
            length = flow.getTypeLink().length();
            if (flow.getTypeLink().indexOf(",") > 0) {
                type = Integer.parseInt(builder.append(flow.getTypeLink()).substring(builder.lastIndexOf(",") + 1));
            } else {
                type = Integer.parseInt(builder.append(flow.getTypeLink()).substring(0));
            }
            builder.delete(0, length);
            if (type > 0 && type != Constant.HANDLE_TYPE_RETURN) {
                flow.setType(Constant.HANDLE_TYPE_SA);
            } else {
                flow.setType(type);
            }
            flow.setTypeLink(null);
        }
        list = list.stream().filter(x -> !x.getType().equals(Constant.HANDLE_TYPE_RETURN)).collect(Collectors.toList());
        Collections.reverse(list);
        return list;
    }
}
