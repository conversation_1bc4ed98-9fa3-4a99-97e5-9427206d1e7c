package com.goodsogood.ows.service;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import com.aidangqun.ows.model.GetSignInRequest;
import com.aidangqun.ows.model.UserThirdInfoVO;
import com.aidangqun.ows.service.OrgUserDingTalkService;
import com.aidangqun.ows.service.ScheduleDingTalkService;
import com.aliyun.dingtalkcalendar_1_0.models.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.*;
import com.goodsogood.ows.common.pojo.Headers;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.configuration.RabbitmqQueueConfig;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.MeetingStatusHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.dto.MeetingReportDto;
import com.goodsogood.ows.model.mongo.TopPriorityMeeting;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.workflow.ApprovalBase;
import com.goodsogood.ows.service.rabbitMQ.Producer;
import com.goodsogood.ows.utils.*;
import com.google.zxing.WriterException;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-25 10:53
 **/
@Service
@Log4j2
public class MeetingService {

    @Value("${scope}")
    private double scope;

    @Value("${sign-qr-code-url}")
    private String signQrCodeUrl;

    @Value("${tog-services.user-center}")
    private String userCenter;

    private final OrgUserDingTalkService orgUserDingTalkService;
    private final ScheduleDingTalkService scheduleService;
    private final RestTemplate restTemplate;
    private final MeetingMapper meetingMapper;
    private final MeetingUserMapper meetingUserMapper;
    private final MeetingTypeMapper meetingTypeMapper;
    private final MeetingTopicMapper meetingTopicMapper;
    private final MeetingHistoryMapper meetingHistoryMapper;
    private final Errors errors;
    private final GroupService groupService;
    private final MeetingTaskService meetingTaskService;
    private final TopicService topicService;
    private final WorkflowService workflowService;
    private final HeaderService headerService;
    private final MeetingResultHsService meetingResultHsService;
    private final MeetingLeaveService meetingLeaveService;
    private final MeetingResultMapper meetingResultMapper;
    private final TopicOrgService topicOrgService;
    private final TopicLogService topicLogService;
    private final MeetingOrgGroupMapper meetingOrgGroupMapper;
    private final MeetingOrgPeriodMapper meetingOrgPeriodMapper;
    private final IndexService indexService;
    private final MeetingRedisService meetingRedisService;
    private final MeetingContactLeaderMapper meetingContactLeaderMapper;
    private final MeetingResultFileMapper meetingResultFileMapper;
    private final StringRedisTemplate stringRedisTemplate;
    private final MeetingAgendaMapper meetingAgendaMapper;
    private final MeetingTagMapper meetingTagMapper;
    private final TogServicesConfig togServicesConfig;
    private final ThirdService thirdService;
    private final MeetingWaitSignService meetingWaitSignService;
    private final MeetingPeoplePartyLifeService meetingPeoplePartyLifeService;
    private final Producer producer;
    private final RabbitmqQueueConfig rabbitmqQueueConfig;
    private final MeetingHelperService meetingHelperService;
    private final MeetingTaskMapper meetingTaskMapper;
    private final MeetingLeaveMapper meetingLeaveMapper;
    private final OpenService openService;
    private final TransferService transferService;

    private final TopPriorityService topPriorityService;

    /**
     * 编辑类型 活动管理
     */
    private static final short FROM_TYPE_MEETING = 1;

    /**
     * 编辑类型 纪实结果
     */
    private static final short FROM_TYPE_REPORT = 2;
    /**
     * 非编辑活动信息（编辑人员信息）时查询详情
     */
    public static final short IS_NOT_EDIT = 0;
    /**
     * 编辑活动基本信息（不包括编辑人员信息）时查询详情
     */
    private static final short IS_EDIT = 1;
    /**
     * 纪实填报 查询详情
     */
    public static final short IS_REPORT_EDIT = 2;

    /**
     * 签到动态二维码缓存key
     */
    public static final String DYNAMIC_KEY = "MEETING_DYNAMIC_QRCODE_";
    /**
     * 签到动态二维码，有效时间，单位秒
     */
    public static final Long DYNAMIC_EXPIRE = 12000L;

    @Autowired
    public MeetingService(OrgUserDingTalkService orgUserDingTalkService,
                          ScheduleDingTalkService scheduleService,
                          RestTemplate restTemplate,
                          MeetingMapper meetingMapper,
                          MeetingUserMapper meetingUserMapper,
                          MeetingTypeMapper meetingTypeMapper,
                          MeetingTopicMapper meetingTopicMapper,
                          MeetingHistoryMapper meetingHistoryMapper,
                          Errors errors,
                          GroupService groupService,
                          MeetingTaskService meetingTaskService,
                          TopicService topicService,
                          WorkflowService workflowService,
                          HeaderService headerService,
                          MeetingResultHsService meetingResultHsService,
                          MeetingLeaveService meetingLeaveService,
                          MeetingResultMapper meetingResultMapper,
                          TopicOrgService topicOrgService,
                          TopicLogService topicLogService,
                          MeetingOrgGroupMapper meetingOrgGroupMapper,
                          MeetingOrgPeriodMapper meetingOrgPeriodMapper,
                          IndexService indexService,
                          MeetingRedisService meetingRedisService,
                          MeetingContactLeaderMapper meetingContactLeaderMapper,
                          MeetingResultFileMapper meetingResultFileMapper, StringRedisTemplate stringRedisTemplate, MeetingAgendaMapper meetingAgendaMapper, MeetingTagMapper meetingTagMapper, TogServicesConfig togServicesConfig, ThirdService thirdService, MeetingWaitSignService meetingWaitSignService, MeetingPeoplePartyLifeService meetingPeoplePartyLifeService, Producer producer, RabbitmqQueueConfig rabbitmqQueueConfig, MeetingHelperService meetingHelperService, MeetingTaskMapper meetingTaskMapper, MeetingLeaveMapper meetingLeaveMapper, OpenService openService, TransferService transferService, TopPriorityService topPriorityService) {
        this.orgUserDingTalkService = orgUserDingTalkService;
        this.scheduleService = scheduleService;
        this.restTemplate = restTemplate;
        this.meetingMapper = meetingMapper;
        this.meetingUserMapper = meetingUserMapper;
        this.meetingTypeMapper = meetingTypeMapper;
        this.meetingTopicMapper = meetingTopicMapper;
        this.meetingHistoryMapper = meetingHistoryMapper;
        this.errors = errors;
        this.groupService = groupService;
        this.meetingTaskService = meetingTaskService;
        this.topicService = topicService;
        this.workflowService = workflowService;
        this.headerService = headerService;
        this.meetingResultHsService = meetingResultHsService;
        this.meetingLeaveService = meetingLeaveService;
        this.meetingResultMapper = meetingResultMapper;
        this.topicOrgService = topicOrgService;
        this.topicLogService = topicLogService;
        this.meetingOrgGroupMapper = meetingOrgGroupMapper;
        this.meetingOrgPeriodMapper = meetingOrgPeriodMapper;
        this.indexService = indexService;
        this.meetingRedisService = meetingRedisService;
        this.meetingContactLeaderMapper = meetingContactLeaderMapper;
        this.meetingResultFileMapper = meetingResultFileMapper;
        this.stringRedisTemplate = stringRedisTemplate;
        this.meetingAgendaMapper = meetingAgendaMapper;
        this.meetingTagMapper = meetingTagMapper;
        this.togServicesConfig = togServicesConfig;
        this.thirdService = thirdService;
        this.meetingWaitSignService = meetingWaitSignService;
        this.meetingPeoplePartyLifeService = meetingPeoplePartyLifeService;
        this.producer = producer;
        this.rabbitmqQueueConfig = rabbitmqQueueConfig;
        this.meetingHelperService = meetingHelperService;
        this.meetingTaskMapper = meetingTaskMapper;
        this.meetingLeaveMapper = meetingLeaveMapper;
        this.openService = openService;
        this.transferService = transferService;
        this.topPriorityService = topPriorityService;
    }

    /**
     * 添加活动 活动管理
     *
     * @param sysHeader     用户信息
     * @param meetingEntity 活动信息 添加前，id设置为null
     * @return 新增添加d的活动id
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addMeeting(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, MeetingEntity meetingEntity) {
        //判断是否是红岩魂传入的,如果是红岩魂传入的，需要设置user_id org_id,还需要将记录人等手机号转为烟草的user_id
        boolean szf = false;
        if (transferService.fromSystemSzf(headers)) {
//            transferService.transferAllHeaders(headers, sysHeader);
            //set 参会人 主持人 记录人 列席人 讲课人 user_id org_id
            transferService.transferPhoneToUserId(headers, meetingEntity);
            szf = true;
        }
        log.debug("数据转换后:" + meetingEntity);
        /* 验证在life_id不存在的情况下 是否存在民主生活会类型 */
        meetingPeoplePartyLifeService.checkExistLifeType(meetingEntity);
        addTopic(headers, meetingEntity);
        this.addMeeting(headers, sysHeader, meetingEntity, FROM_TYPE_MEETING, szf);
        meetingResultHsService.insertReportHs(JsonUtils.toJson(meetingEntity), null, meetingEntity.getMeetingId(), sysHeader.getUserId(), "发起" + StringCanstant.ACTIVITY);
        /* 民主生活会同步相关 */
        meetingPeoplePartyLifeService.checkColumnAndSaveDataInfo(meetingEntity, sysHeader);
        // 2023-09-25 这里不写入第一议题数据
//        if (meetingEntity.getAgenda() != null && !meetingEntity.getAgenda().isEmpty()) {
//            meetingEntity.getAgenda().forEach(agendaEntity -> {
//                if (agendaEntity.getTopPriority() != null) {
//                    Arrays.stream(agendaEntity.getTopPriority().split(",")).forEach(s -> {
//                        topPriorityService.addAssociatedAndMeetingsById(createTopPriorityMeeting(meetingEntity, headers), s);
//                    });
//                }
//            });
//        }
        return meetingEntity.getMeetingId();
    }

    public void setTopPriorityService(MeetingEntity meetingEntity, HttpHeaders headers) {
        log.debug("========addTopPriorityService:meeting:meeting=========>{}", meetingEntity);
        log.debug("========addTopPriorityService:meeting:status判断=========>{}", Arrays.asList(
                MeetingCanstant.MEETING_STATUS_SUBMIT,
                MeetingCanstant.MEETING_STATUS_SUBMIT_CHECK,
                MeetingCanstant.MEETING_STATUS_PASS_FIRST,
                MeetingCanstant.MEETING_STATUS_PASS_MORE
        ).contains(meetingEntity.getStatus().intValue()));
        // 先移除本组织的第一议题数据
        var topPriorityMeeting = new TopPriorityMeeting();
        topPriorityMeeting.setMeetingId(meetingEntity.getMeetingId());
        topPriorityMeeting.setAssociatedOrg(meetingEntity.getOrgId());
        topPriorityService.removeAssociatedAndMeetings(topPriorityMeeting);
        // 添加新的第一议题数据
        // 通过orgId获取单位信息
        var org = openService.findOrgById(meetingEntity.getOrgId(), headers);
        if (Arrays.asList(
                MeetingCanstant.MEETING_STATUS_SUBMIT,
                MeetingCanstant.MEETING_STATUS_SUBMIT_CHECK,
                MeetingCanstant.MEETING_STATUS_PASS_FIRST,
                MeetingCanstant.MEETING_STATUS_PASS_MORE
        ).contains(meetingEntity.getStatus().intValue()) && meetingEntity.getAgenda() != null && !meetingEntity.getAgenda().isEmpty()) {
            meetingEntity.getAgenda().forEach(agendaEntity -> {
                if (agendaEntity.getTopPriority() != null) {
                    Arrays.stream(agendaEntity.getTopPriority().split(",")).forEach(s -> {
                        topPriorityService.addAssociatedAndMeetingsById(createTopPriorityMeeting(org, meetingEntity, headers), s);
                    });
                }
            });
        }
    }

    public TopPriorityMeeting createTopPriorityMeeting(OrganizationBase org, MeetingEntity meetingEntity, HttpHeaders headers) {
        TopPriorityMeeting topPriorityMeeting = new TopPriorityMeeting();
        topPriorityMeeting.setMeetingId(meetingEntity.getMeetingId());
        topPriorityMeeting.setMeetingTitle(meetingEntity.getName());
        topPriorityMeeting.setMeetingTime(meetingEntity.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
        topPriorityMeeting.setAssociatedOrg(meetingEntity.getOrgId());
        topPriorityMeeting.setAssociatedName(meetingEntity.getOrgName());
        topPriorityMeeting.setAssociatedUnitOrgId(org.getOwnerId());
        topPriorityMeeting.setAssociatedUnitOrgName(org.getOwnerName());
        topPriorityMeeting.setAssociatedUnitShortName(org.getOwnerShortName());
        // 添加第一议题关联
        var meetingTypes = meetingTypeMapper.findByMeetingId(meetingEntity.getMeetingId());
        if (meetingTypes != null && !meetingTypes.isEmpty()) {
            // 循环meetingTypes，用逗号拼接typeId和type
            StringBuilder typeId = new StringBuilder();
            StringBuilder type = new StringBuilder();
            meetingTypes.forEach(meetingTypeEntity -> {
                typeId.append(meetingTypeEntity.getTypeId()).append(",");
                type.append(meetingTypeEntity.getType()).append(",");
            });
            topPriorityMeeting.setMeetingTypes(typeId.substring(0, typeId.length() - 1));
            topPriorityMeeting.setMeetingTypeNames(type.substring(0, type.length() - 1));
        }
        return topPriorityMeeting;
    }

    /**
     * 拉取钉钉日程记录
     *
     * @param
     * @return java.util.List<com.aliyun.dingtalkcalendar_1_0.models.ListEventsResponseBody.ListEventsResponseBodyEvents>
     * <AUTHOR>
     * @date 2021/9/23 15:29
     */
    public List<ListEventsResponseBody.ListEventsResponseBodyEvents> getScheduleList(HeaderHelper.SysHeader sysHeader, String startDate, String endDate) {
        List<ListEventsResponseBody.ListEventsResponseBodyEvents> events = new ArrayList<>();
        try {
            List<UserThirdInfoVO> userThirdInfo = orgUserDingTalkService.getUserThirdInfo(restTemplate, togServicesConfig.getUserCenter(), sysHeader.getRegionId(), sysHeader.getUserId());
            if (userThirdInfo != null && userThirdInfo.size() > 0) {
                String unionId = Objects.requireNonNull(userThirdInfo).get(0).getOpenId();
                ListEventsRequest ler = new ListEventsRequest();
                if (StringUtils.isNotEmpty(unionId)) {
                    /**
                     * 获取指定时间以内的日程
                     */
                    if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
                        //默认近7天
                        startDate = DateUtils.getDateByDays(-6, "yyyy-MM-dd", "");
                        endDate = DateUtils.dateFormat(new Date(), "yyyy-MM-dd");
                    } else if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
                        //根据开始时间向后偏移6天
                        endDate = DateUtils.getDateByDays(6, "yyyy-MM-dd", startDate);
                    } else if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
                        //根据结束时间向前偏移6天
                        startDate = DateUtils.getDateByDays(-6, "yyyy-MM-dd", endDate);
                    }
                    //转换成ISO-8601格式的时间
                    startDate = DateUtils.dateFormat(DateUtils.stringToDate(startDate + " 00:00:00", "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd'T'HH:mm:ssXXX");
                    endDate = DateUtils.dateFormat(DateUtils.stringToDate(endDate + " 23:59:59", "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd'T'HH:mm:ssXXX");
                    ler.timeMin = startDate;
                    ler.timeMax = endDate;
                    this.getListEvents(unionId, ler, null, events);
                } else {
                    log.error("<拉取钉钉日程记录>失败！ 用户中心返回的unionId为空 regionId={} userId={} userThirdInfo={}", sysHeader.getRegionId(), sysHeader.getUserId(), JsonUtils.toJson(userThirdInfo));
                }
                //反转顺序
                Collections.reverse(events);
            } else {
                log.error("<拉取钉钉日程记录>失败！ 用户中心返回的userThirdInfo为空 regionId={} userId={}", sysHeader.getRegionId(), sysHeader.getUserId());
            }
        } catch (Exception e) {
            log.error("拉取钉钉日程记录失败！regionId={} userId={}", sysHeader.getRegionId(), sysHeader.getUserId(), e);
        }
        return events;
    }

    /**
     * 递归获取钉钉日程信息
     *
     * @param unionId
     * @param ler
     * @param nextToken
     * @param events
     * @return java.util.List<com.aliyun.dingtalkcalendar_1_0.models.ListEventsResponseBody.ListEventsResponseBodyEvents>
     * <AUTHOR>
     * @date 2021/9/23 16:22
     */
    private void getListEvents(String unionId, ListEventsRequest ler, String nextToken,
                               List<ListEventsResponseBody.ListEventsResponseBodyEvents> events) throws Exception {
        ListEventsResponse result = new ListEventsResponse();
        if (StringUtils.isNotEmpty(nextToken)) {
            ler.setNextToken(nextToken);
        }
        result = scheduleService.getScheduleList(unionId, ler);
//        log.debug("调用钉钉底座，获取钉钉日程结果。result={}",JsonUtils.toJson(result));
        if (result != null) {
            if (result.getBody().getEvents() != null && result.getBody().getEvents().size() > 0) {
//                log.debug("日程获取 unionId={} events={}",unionId,JsonUtils.toJson(result.getBody().getEvents()));
                events.addAll(result.getBody().getEvents());
            }
            /**
             * 如果还有分页,查询后面分页
             */
            String reNextToken = result.getBody().getNextToken();
//            log.debug("日程获取 unionId={} 分页Token={}",unionId,reNextToken);
            if (StringUtils.isNotEmpty(reNextToken)) {
                //递归调用查询翻页结果
                this.getListEvents(unionId, ler, reNextToken, events);
            }
        }
    }

    /**
     * 添加任务
     *
     * @param headers       HttpHeaders
     * @param meetingEntity meetingEntity
     */
    private void addTopic(HttpHeaders headers, MeetingEntity meetingEntity) {
        // 初始化getTopics
        if (meetingEntity.getTopics() == null) {
            meetingEntity.setTopics(new ArrayList<>());
        }
        // 1.添加任务
        if (meetingEntity.getAddTopics() != null && !meetingEntity.getAddTopics().isEmpty()) {
            Headers header = this.headerService.bulidHeader(headers);
            for (TopicEntity topic : meetingEntity.getAddTopics()) {
                topicService.addTopicByMeeting(header, meetingEntity, topic);
            }
        }
    }

    /**
     * 修改活动 活动管理
     *
     * @param sysHeader     用户信息
     * @param meetingEntity 活动信息
     */
    @Transactional
    public int updateMeeting(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, MeetingEntity meetingEntity) {
        //判断是否是红岩魂传入的,如果是红岩魂传入的，需要设置user_id org_id,还需要将记录人等手机号转为烟草的user_id
        if (transferService.fromSystemSzf(headers)) {
            meetingEntity.setOrgId(sysHeader.getOid());
            transferService.transferAllHeaders(headers, sysHeader);
            transferService.transferPhoneToUserId(headers, meetingEntity);

        }
        log.debug("数据转换后:" + meetingEntity);
        /* 校验中间包含民主生活会的逻辑 */
        meetingPeoplePartyLifeService.checkMeetingUpdateByLife(meetingEntity);
        addTopic(headers, meetingEntity);
        log.debug("mt查询" + meetingEntity);
        return this.updateMeeting(headers, sysHeader, meetingEntity, FROM_TYPE_MEETING);
    }

    @Transactional
    public int addUser(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, MeetingAddUserForm meetingAddUserForm) {
        // 查询oldMeeting by id
        MeetingEntity oldMeetingEntity = this.findById(sysHeader, meetingAddUserForm.getMeetingId());
        MeetingEntity upMeetingEntity = new MeetingEntity();
        if (this.isMeetingAddUsers(oldMeetingEntity, FROM_TYPE_MEETING)) {
            upMeetingEntity.setMeetingId(oldMeetingEntity.getMeetingId());
            upMeetingEntity.setIsSignIn(oldMeetingEntity.getIsSignIn());
            upMeetingEntity.setSignInWay(oldMeetingEntity.getSignInWay());
            upMeetingEntity.setRecordUser(oldMeetingEntity.getRecordUser());
            upMeetingEntity.setHostUser(oldMeetingEntity.getHostUser());
            upMeetingEntity.setAttendUsers(meetingAddUserForm.getAttendUsers());
            upMeetingEntity.setParticipantUsers(meetingAddUserForm.getParticipantUsers());
        } else {
            // 不能添加
            throw new ApiException("add meeting-users error", new Result<>(errors, 1807, HttpStatus.FORBIDDEN.value(), "修改"));
        }
        int addNum = this.addMeetingUsers(headers, upMeetingEntity, oldMeetingEntity, FROM_TYPE_MEETING);
        // 添加人员 刷新缓存
        indexService.collectRedis();
        return addNum;
    }

    /**
     * 主动撤回活动。
     *
     * @param sysHeader 请求头
     * @param mid       纪实id
     * @return int 更新条数
     */
    @Transactional(rollbackFor = Exception.class)
    public int revokeMeeting(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, long mid) {
        //判断是否是红岩魂传入的,如果是红岩魂传入的，需要设置user_id org_id,还需要将记录人等手机号转为烟草的user_id
        if (transferService.fromSystemSzf(headers)) {
            transferService.transferAllHeaders(headers, sysHeader);
        }
        log.debug("数据转换后:" + headers);
        // 查询oldMeeting by id
        MeetingEntity meetingEntity = this.findById(sysHeader, mid);
        if (canRevoke(sysHeader, meetingEntity)) {
            Date now = DateTime.now().toDate();
            Short newStatus = MeetingCanstant.MEETING_STATUS_REVOKE.shortValue();
            MeetingEntity newMeetingEntity = new MeetingEntity();
            newMeetingEntity.setStatus(newStatus);
            newMeetingEntity.setUpdateTime(now);
            newMeetingEntity.setLastChangeUser(sysHeader.getUserId());
            newMeetingEntity.setMeetingId(mid);
            int upNum = meetingMapper.updateByPrimaryKeySelective(newMeetingEntity);

            MeetingHistoryEntity meetingHistoryEntity = new MeetingHistoryEntity();
            meetingHistoryEntity.setReason("撤回");
            meetingHistoryEntity.setMeetingId(mid);
            meetingHistoryEntity.setStatus(newStatus.intValue());
            meetingHistoryEntity.setCreateUser(sysHeader.getUserId());
            meetingHistoryEntity.setCreateTime(now);
            meetingHistoryMapper.insert(meetingHistoryEntity);
            // 撤回后，更新首页缓存
            indexService.collectRedis();
            // 调用补学相关回退逻辑
            meetingWaitSignService.backSignInfo(mid, sysHeader);
            //调用撤回后扣减人员积分   tc 2021-12-09
            log.debug("调用撤回后扣减人员积分 regionId={} meeting={} isCancel={}", sysHeader.getRegionId(), meetingEntity, false);
//            meetingScoreService.reduceMeetingScore(sysHeader.getRegionId(),meetingEntity,false);  丢入队列  tc 2022-01-25
            MeetingScoreMQVo msmq = new MeetingScoreMQVo(sysHeader.getRegionId(), meetingEntity, false, null, Config.MeetingScoreConf.MQ_REDUCE_MEETING_SCORE);
            producer.send(Config.RabbitmqConf.DIRECT_EXCHANGE_NAME, rabbitmqQueueConfig.getMeetingScoreRouting(), JsonUtils.toJson(msmq));
            // 2023-09-25  处理第一议题相关数据
            // 移除本组织的第一议题数据
            var topPriorityMeeting = new TopPriorityMeeting();
            topPriorityMeeting.setMeetingId(meetingEntity.getMeetingId());
            topPriorityMeeting.setAssociatedOrg(meetingEntity.getOrgId());
            topPriorityService.removeAssociatedAndMeetings(topPriorityMeeting);
            return upNum;
        } else {
            throw new ApiException("revoke meeting error", new Result<>(errors, 1807, HttpStatus.FORBIDDEN.value(), "撤回"));
        }
    }

    /**
     * 判断纪实是否能撤回
     * 状态为检察通过、已提交、待复核，且提交组织为当前组织的，且提交日期为当月（未跨月）的组织生活，允许撤回
     *
     * @param sysHeader     请求头
     * @param meetingEntity 纪实信息
     * @return true 可以撤回
     */
    private boolean canRevoke(HeaderHelper.SysHeader sysHeader, MeetingEntity meetingEntity) {
        Short status = meetingEntity.getStatus();
        // 当前组织
        if (sysHeader.getOid().equals(meetingEntity.getOrgId())) {
            // 状态 检察通过、已提交、待复核
            if (status == MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue()
                    || status == MeetingCanstant.MEETING_STATUS_PASS_FIRST.shortValue()
                    || status == MeetingCanstant.MEETING_STATUS_PASS_MORE.shortValue()
                    || status == MeetingCanstant.MEETING_STATUS_SUBMIT_CHECK.shortValue()) {
                // 查询提交信息
                Example example = new Example(MeetingResultEntity.class);
                example.createCriteria().andEqualTo("meetingId", meetingEntity.getMeetingId());
                MeetingResultEntity resultEntity = meetingResultMapper.selectOneByExample(example);
                // 时间未跨月
                if (resultEntity.getSubmitTime() != null && DateUtils.inCurrentMonth(resultEntity.getSubmitTime())) {
                    return true;
                } else {
                    log.info("活动不是本月提交的，不能撤回！活动提交时间：{}", DateUtils.dateFormat(resultEntity.getSubmitTime(), ""));
                }
            } else {
                log.info("活动状态不满足撤回！活动状态：{}", status);
            }
        } else {
            log.info("活动不是当前组织创建的，不能撤回！当前组织：{},活动创建组织：{}", sysHeader.getOid(), meetingEntity.getOrgId());
        }
        return false;
    }

    /*  2019-02-13 11:08:52 chenanshun 纪实管理“活动取消”功能的调整*/
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public int cancelMeeting(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, long mid) {
        //判断是否是红岩魂传入的,如果是红岩魂传入的，需要设置user_id org_id,还需要将记录人等手机号转为烟草的user_id
        if (transferService.fromSystemSzf(headers)) {
            transferService.transferAllHeaders(headers, sysHeader);
        }
        // 查询oldMeeting by id
        MeetingEntity meetingEntity = this.findById(sysHeader, mid);
        // 设置会议状态
        meetingEntity.setStatus(MeetingStatusHelper.convertToFormStatus(meetingEntity.getStatus(), meetingEntity.getStartTime()));

        Short status = meetingEntity.getStatus();
        if (status == MeetingCanstant.MEETING_STATUS_APPROVAL_APPLY.shortValue()
                || status == MeetingCanstant.MEETING_STATUS_APPROVAL_NOT_PASS.shortValue()
                || status == MeetingCanstant.MEETING_STATUS_APPROVAL.shortValue()
                || status == MeetingCanstant.MEETING_STATUS_SOON_START.shortValue()
                // 1.MEETING_STATUS_BACK 退回活动可以取消
                || status == MeetingCanstant.MEETING_STATUS_BACK.shortValue()
                || status == MeetingCanstant.MEETING_STATUS_REVOKE.shortValue()) {
            // 2.判断退回状态的活动的提交时间是否已跨季度，若已跨季度，则不允许操作
            if (status == MeetingCanstant.MEETING_STATUS_BACK.shortValue()
                    || status == MeetingCanstant.MEETING_STATUS_REVOKE.shortValue()) {
                // 查询提交信息
                Example example = new Example(MeetingResultEntity.class);
                example.createCriteria().andEqualTo("meetingId", mid);
                MeetingResultEntity resultEntity = meetingResultMapper.selectOneByExample(example);
                //判断是否提交过活动结果
                if (resultEntity.getSubmitTime() == null) {
                    throw new ApiException("取消活动出错,纪实考核报表信息未找到", new Result<>(errors, 1953, HttpStatus.FORBIDDEN.value()));
                }
                // 判断提交时间是否跨季度
                if (!DateUtils.inCurrentQuarter(resultEntity.getSubmitTime())) {
                    throw new ApiException("仅允许取消当前季度的活动", new Result<>(errors, 1952, HttpStatus.FORBIDDEN.value()));
                }
                // 3.将对应任务的完成次数减1。该任务的完成次数为0时，需将该任务的完成状态置为“未完成”
                // 4.将对应活动类型的完成次数减1。该活动类型的完成次数为小于要求次数时，需将该活动类型的完成状态置为“未完成”
                boolean flag = transferService.fromSystemSzf(headers) && meetingEntity.getMeetingTypes().stream().map(MeetingTypeEntity::getTypeId).collect(Collectors.toList()).contains(3L);
                log.debug("查看是否是市值过来的党小组=>{},{},{},{}", headers, JsonUtils.toJson(meetingEntity), JsonUtils.toJson(sysHeader), flag);
                meetingTaskService.upTaskStatus(mid, MeetingCanstant.MEETING_STATUS_CANCEL.shortValue(), flag);
            }
            MeetingEntity upm = new MeetingEntity();
            upm.setMeetingId(mid);
            upm.setStatus(MeetingCanstant.MEETING_STATUS_CANCEL.shortValue());
            upm.setUpdateTime(DateTime.now().toDate());
            upm.setLastChangeUser(sysHeader.getUserId());
            int num = meetingMapper.updateByPrimaryKeySelective(upm);
            if (status == MeetingCanstant.MEETING_STATUS_APPROVAL_APPLY.shortValue()) {// 待审批状态，撤销审批流
                this.undoApprove(headers, FROM_TYPE_MEETING, meetingEntity, "取消");
            }
            // 待举办状态 删除历史表中的待填写记录
            if (status == MeetingCanstant.MEETING_STATUS_SOON_START.shortValue()) {
                Example example = new Example(MeetingHistoryEntity.class);
                example.createCriteria().andEqualTo("meetingId", mid).andEqualTo("status", MeetingCanstant.MEETING_STATUS_APPROVAL.shortValue());
                meetingHistoryMapper.deleteByExample(example);
            }
            // 添加取消操作记录
            this.addCancelMeetingHistory(upm);
            // 修改已请假人状态
            meetingLeaveService.meetingAfterRejectLeave(mid);
            // 取消活动 刷新缓存
            indexService.collectRedis();
            //调用取消后扣减人员和组织积分   tc 2021-12-09
            log.debug("调用取消后扣减人员和组织积分 regionId={} meeting={} isCancel={}", sysHeader.getRegionId(), meetingEntity, true);

            /**
             * 取消后删除日程和三方任务
             * tc 2022-03-15
             */
            //删除钉钉日程
            if (StringUtils.isNotEmpty(meetingEntity.getDingEventId())) {
                this.delDingEvent(sysHeader.getRegionId(), meetingEntity.getDingEventCreateUser(), meetingEntity.getDingEventId(), meetingEntity.getMeetingId());
            }
            //删除三方任务
            String scid = MeetingCanstant.MEETING_PENDING_TASK_KEY + sysHeader.getRegionId() + "_" + meetingEntity.getMeetingId();
            this.delThirdTask(headers, meetingEntity.getMeetingId(), Collections.singletonList(scid));


//            meetingScoreService.reduceMeetingScore(sysHeader.getRegionId(),meetingEntity,true); 丢入队列  tc 2022-01-25
            MeetingScoreMQVo msmq = new MeetingScoreMQVo(sysHeader.getRegionId(), meetingEntity, true, null, Config.MeetingScoreConf.MQ_REDUCE_MEETING_SCORE);
            producer.send(Config.RabbitmqConf.DIRECT_EXCHANGE_NAME, rabbitmqQueueConfig.getMeetingScoreRouting(), JsonUtils.toJson(msmq));
            return num;
        } else {
            throw new ApiException("cancel meeting error", new Result<>(errors, 1807, HttpStatus.FORBIDDEN.value(), "取消"));
        }
    }


    /**
     * 删除已取消的活动
     */
    @Transactional
    public int delMeeting(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, long mid) {
        //判断是否是红岩魂传入的,如果是红岩魂传入的，需要设置user_id org_id,还需要将记录人等手机号转为烟草的user_id
        if (transferService.fromSystemSzf(headers)) {
            transferService.transferAllHeaders(headers, sysHeader);
        }
        // 查询oldMeeting by id
        MeetingEntity meetingEntity = this.findById(sysHeader, mid);
        Short status = meetingEntity.getStatus();
        if (status == MeetingCanstant.MEETING_STATUS_CANCEL.shortValue()) {
            meetingPeoplePartyLifeService.meetingDeleteLinkToDelete(mid);
            meetingPeoplePartyLifeService.orgLifeLinkToDelete(mid);
            MeetingEntity upm = new MeetingEntity();
            upm.setMeetingId(mid);
            upm.setIsDel((short) 1);
            upm.setUpdateTime(DateTime.now().toDate());
            upm.setLastChangeUser(sysHeader.getUserId());
            int delNum = meetingMapper.updateByPrimaryKeySelective(upm);
            // 删除活动 刷新缓存
            indexService.collectRedis();
            return delNum;
        } else {
            throw new ApiException("update meeting error", new Result<>(errors, 1807, HttpStatus.FORBIDDEN.value(), "删除"));
        }
    }


    /**
     * 查询活动列表 分页查询
     */
    public Page<MeetingEntity> listPage(MeetingListForm meetingListForm) {
        Page<MeetingEntity> page = PageHelper.startPage(meetingListForm.getPageBean().getPageNo(), meetingListForm.getPageBean().getPageSize())
                .doSelectPage(() -> this.meetingMapper.findAll(meetingListForm));
        //查询活动状态更新时间
        meetingRedisService.setStatusUpdateTime(page);
        return page;
    }

    /**
     * 查询活动列表 分页查询
     */
    public Page<MeetingEntity> listPageV2(MeetingListForm meetingListForm) {
        Page<MeetingEntity> page = PageHelper.startPage(meetingListForm.getPageBean().getPageNo(), meetingListForm.getPageBean().getPageSize())
                .doSelectPage(() -> this.meetingMapper.findAllV2(meetingListForm));
        //查询活动状态更新时间
        meetingRedisService.setStatusUpdateTime(page);
        if (CollectionUtils.isNotEmpty(page)) {
            Date now = new Date();
            page.forEach(meetingEntity -> {
                        if (meetingListForm.getRecordType() == 0) {
                            if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_SOON_START.shortValue()) {
                                // start_time > 当前时间，状态为未开始
                                if (meetingEntity.getStartTime().after(now)) {
                                    meetingEntity.setStatus((short) -1);
                                }
                                //	start_time < 当前时间 && end_time > 当前时间，状态为进行中
                                if (meetingEntity.getStartTime().before(now) && (meetingEntity.getEndTime() == null || meetingEntity.getEndTime().after(now))) {
                                    meetingEntity.setStatus((short) -2);
                                }
                                //	end_time  < 当前时间，状态为已结束
                                if (meetingEntity.getEndTime() != null && meetingEntity.getEndTime().before(now)) {
                                    meetingEntity.setStatus((short) -3);
                                }
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_CANCEL.shortValue()) {
                                // status = 9，状态为已取消
                                meetingEntity.setStatus((short) -4);
                            } else {
                                // status = 其他值，状态为已结束
                                meetingEntity.setStatus((short) -3);
                            }
                        } else {
                            if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_SOON_START.shortValue()) {
                                meetingEntity.setStatus((short) -1);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_CANCEL.shortValue()) {
                                meetingEntity.setStatus((short) -2);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_SUBMIT_CHECK.shortValue()) {
                                meetingEntity.setStatus((short) -3);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_PASS_FIRST.shortValue()
                                    || meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_PASS_MORE.shortValue()) {
                                meetingEntity.setStatus((short) -4);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_BACK.shortValue()) {
                                meetingEntity.setStatus((short) -5);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue()) {
                                meetingEntity.setStatus((short) -6);
                            } else if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_REVOKE.shortValue()) {
                                meetingEntity.setStatus((short) -7);
                            }
                        }
                    }
            );
        }
        return page;
    }


    /**
     * 查询组织生活详情 原始数据
     */
    public MeetingEntity findById(HeaderHelper.SysHeader sysHeader, long id) {
        MeetingEntity meetingEntity = meetingMapper.findByIdAndOrg(id, sysHeader.getOid());
        if (meetingEntity == null) {
            throw new ApiException("not found", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), StringCanstant.ACTIVITY));
        }
        // 任务答案
        if (meetingEntity.getTopics() != null) {
            for (MeetingTopicEntity topic : meetingEntity.getTopics()) {
                if (topic.getLogList() != null) {
                    topicLogService.topicAnswerSetToContent(topic.getLogList(), topic.getContents());
                    topic.setLogList(null);
                }
            }
        }
        return meetingEntity;
    }

    /**
     * 查询组织生活详情
     *
     * @param isEdit 是否是编辑时查询详情	0：不是（默认）；1：会议管理编辑页面查询 2：纪实管理页面编辑查询。
     */
    public MeetingEntity detail(HeaderHelper.SysHeader sysHeader, long id, short isEdit) {
        MeetingEntity meetingEntity = this.findById(sysHeader, id);
        //查询活动状态更新时间
        meetingRedisService.setStatusUpdateTime(meetingEntity);

        // 纪实编辑页面查询 不能更新类型。但是类型规则变化影响讲课标题信息
        if (isEdit == IS_REPORT_EDIT || isEdit == IS_EDIT) {
            // 判断是否要显示讲课标题和讲课人
            boolean hasLecturer = false;
            boolean hasLectureTitle = false;
            if (CollectionUtils.isNotEmpty(meetingEntity.getMeetingTypes())) {
                for (MeetingTypeEntity mt : meetingEntity.getMeetingTypes()) {
                    if (mt.getHasLecturer() != null && mt.getHasLecturer().equals(Constant.YES)) {
                        hasLecturer = true;
                    }
                    if (mt.getHasLectureTitle() != null && mt.getHasLectureTitle().equals(Constant.YES)) {
                        hasLectureTitle = true;
                    }
                    if (hasLecturer && hasLectureTitle) {
                        break;
                    }
                }
            }
            if (!hasLecturer) {
                meetingEntity.setLecturers(null);
                meetingEntity.setHasLecturer(Constant.NO);
            } else {
                meetingEntity.setHasLecturer(Constant.YES);
            }
            if (!hasLectureTitle) {
                meetingEntity.setLectureTitle(null);
                meetingEntity.setHasLectureTitle(Constant.NO);
            } else {
                meetingEntity.setHasLectureTitle(Constant.YES);
            }
        }
        // 会议编辑页面查询详情
        if (isEdit == IS_EDIT) {
            // 过滤过期的活动类型
            //1.查询有效期内的活动任务
            MeetingTypeTaskListForm meetingTypeListForm = new MeetingTypeTaskListForm();
            meetingTypeListForm.setTag(MeetingTypeTaskListForm.TAG_MEETING);
            meetingTypeListForm.setOrgId(sysHeader.getOid());
            List<MeetingTaskEntity> meetingTasks = meetingTaskService.typeList(meetingTypeListForm);
            List<MeetingTypeEntity> tempMeetingTasks = new ArrayList<>();
            for (MeetingTypeEntity meetingTypeEntity : meetingEntity.getMeetingTypes()) {
                if (meetingTasks.stream().anyMatch(task -> task.getMeetingTaskId().equals(meetingTypeEntity.getMeetingTaskId()))) {
                    tempMeetingTasks.add(meetingTypeEntity);
                }
            }
            meetingEntity.setMeetingTypes(tempMeetingTasks);
            if (meetingEntity.getMeetingTypes().size() > 1) {// 如果组合不存在，返回null
                List<Long> typeIds = meetingEntity.getMeetingTypes().stream().map(MeetingTypeEntity::getTypeId).distinct().sorted().collect(Collectors.toList());
                if (!existTypeGroup(sysHeader, typeIds)) {
                    meetingEntity.setMeetingTypes(new ArrayList<>());
                }
            }
            // 过滤过期的任务
            //1.查询有效期内的任务
            MeetingTopicTaskListForm meetingTopicTaskListForm = new MeetingTopicTaskListForm();
            meetingTopicTaskListForm.setTag((short) 1);
            meetingTopicTaskListForm.setOrgId(sysHeader.getOid());
            List<TopicOrgForm> topicTasks = topicOrgService.list(meetingTopicTaskListForm);
            List<MeetingTopicEntity> tempTopicTasks = new ArrayList<>();
            for (MeetingTopicEntity meetingTopicEntity : meetingEntity.getTopics()) {
                if (topicTasks.stream().anyMatch(task -> task.getTopicId().equals(meetingTopicEntity.getTopicId()))) {
                    tempTopicTasks.add(meetingTopicEntity);
                }
            }
            meetingEntity.setTopics(tempTopicTasks);
        }
        // 活动状态
        meetingEntity.setStatus(MeetingStatusHelper.convertToFormStatus(meetingEntity.getStatus(), meetingEntity.getStartTime()));

        //2018年11月9日 11:40:48 chenanshun
        // 将主持人、记录人的is_add 字段设置为null
        meetingEntity.getHostUser().forEach(meetingUserEntity -> meetingUserEntity.setIsAdd(null));
        meetingEntity.getRecordUser().forEach(meetingUserEntity -> meetingUserEntity.setIsAdd(null));

        // 设置手动录入人员的uid， 负值 递增
        if (meetingEntity.getAttendUsers() != null && !meetingEntity.getAttendUsers().isEmpty()) {
            long minId = -1;
            for (MeetingUserEntity user : meetingEntity.getAttendUsers()) {
                if (user.getUserId() == null || user.getUserId() < 0) {
                    user.setUserId(minId);
                    minId--;
                }
            }
        }
        if (meetingEntity.getParticipantUsers() != null && !meetingEntity.getParticipantUsers().isEmpty()) {
            long minId = -1;
            for (MeetingUserEntity user : meetingEntity.getParticipantUsers()) {
                if (user.getUserId() == null || user.getUserId() < 0) {
                    user.setUserId(minId);
                    minId--;
                }
            }
        }
        //判断是否提交过活动结果
        Example example = new Example(MeetingResultEntity.class);
        example.createCriteria().andEqualTo("meetingId", id);
        MeetingResultEntity resultEntity = meetingResultMapper.selectOneByExample(example);
        if (resultEntity != null) {
            meetingEntity.setResultStatus((short) 1);
        }


        if (meetingEntity.getIsSignIn() == 1) {
            //是否签到中
//            long now = DateTime.now().toDate().getTime();
//            if (now < meetingEntity.getSignStartTime().getTime()) {// 签到时间未到
//                meetingEntity.setSignInStatus(1);
//            } else if (now >= meetingEntity.getSignStartTime().getTime() && now < meetingEntity.getSignEndTime().getTime()) {//签到中
            meetingEntity.setSignInStatus(2);
//            } else {// 签到结束
//                meetingEntity.setSignInStatus(3);
//            }
            // 当前用户签到情况
            MeetingUserEntity meetingUserEntity = meetingEntity.getParticipantUsers().stream().filter(me -> me.getUserId() != null && me.getUserId().equals(sysHeader.getUserId())).findFirst().orElse(null);
            if (meetingUserEntity == null && meetingEntity.getAttendUsers() != null) {
                meetingUserEntity = meetingEntity.getAttendUsers().stream().filter(me -> me.getUserId() != null && me.getUserId().equals(sysHeader.getUserId())).findFirst().orElse(null);
            }
            if (meetingUserEntity == null) {
                meetingEntity.setSignStatus(1);
            } else {
                meetingEntity.setSignStatus(meetingUserEntity.getSignStatus().intValue());
            }
        }

        return meetingEntity;
    }


    /**
     * 添加活动
     *
     * @param sysHeader     用户信息
     * @param meetingEntity 活动信息 添加前，id设置为null
     * @param fromType      添加来源：1.活动管理页面添加（发起活动） 2.记实情况页面添加(直接录入活动)
     * @Param szf 是否是红岩魂过来的，红岩魂过来的党小组会要特殊处理
     */
    MeetingEntity addMeeting(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, MeetingEntity meetingEntity, Short fromType, Boolean szf) {
        List<MeetingTaskEntity> meetingTaskEntities = new ArrayList<>();
        boolean hasGroup = false;//是否是红岩魂过来的党小组
        //判断当前组织是否有所选任务
        if (szf) {
            List<Long> meetingGroupTaskIds =
                    meetingEntity.getMeetingTypes().stream().filter(i -> i.getTypeId() == 3)
                            .map(MeetingTypeEntity::getMeetingTaskId)
                            .distinct()
                            .collect(Collectors.toList());//党小组会
            log.debug("党小组会" + meetingGroupTaskIds);
            List<MeetingTaskEntity> meetingGorupEntities = null;
            if (CollectionUtils.isNotEmpty(meetingGroupTaskIds)) {
                hasGroup = true;
            }
        }
        List<Long> meetingTaskIds =
                meetingEntity.getMeetingTypes().stream()
                        .map(MeetingTypeEntity::getMeetingTaskId)
                        .distinct()
                        .collect(Collectors.toList());
        meetingTaskEntities = meetingTaskService.findByOrgIdAndIds(sysHeader.getOid(), meetingTaskIds, meetingEntity.getStartTime()); // 派发的活动任务
        log.debug("mt_sysheader" + sysHeader);
        log.debug("mt_meetingEntity" + meetingEntity);
        log.debug("mt_meetingTaskEntities" + meetingTaskEntities);
        this.checkAddParam(sysHeader, meetingEntity, meetingTaskEntities, fromType, hasGroup);
        //添加活动
        meetingEntity.setMeetingId(null);
        meetingEntity.setOrgId(sysHeader.getOid());
        meetingEntity.setRegionId(sysHeader.getRegionId());
        meetingEntity.setCreateUser(sysHeader.getUserId());
        meetingEntity.setOrgName(sysHeader.getOrgName());
        meetingEntity.setCreateTime(DateTime.now().toDate());
        meetingEntity.setUpdateTime(DateTime.now().toDate());
        meetingEntity.setIsDel((short) 0);
        meetingEntity.setAddType(fromType);
        meetingEntity.setCanSign(null != meetingEntity.getCanSign() ? meetingEntity.getCanSign() : (short) 0);
        this.initMeetingStatus(meetingEntity);
        //如果结束时间不为空，根据开始和结束时间计算时长
        if (meetingEntity.getEndTime() != null) {
            //计算间隔秒数
            long second = DateUtil.between(meetingEntity.getStartTime(), meetingEntity.getEndTime(), DateUnit.SECOND);
            Double hours = NumberUtil.div(second, 3600f, 1);
            meetingEntity.setTotalHours(hours);
        }
        log.debug("实际活动录入参数:[{}]", JsonUtils.toJson(meetingEntity));
        meetingMapper.insert(meetingEntity);
        //添加参会人员
        this.addMeetingUsers(headers, meetingEntity, null, fromType);

        if (meetingEntity.getSelContactLeaders().equals(1)) {
            this.addMeetingContactLeaders(
                    meetingEntity.getMeetingId(),
                    CheckDataOperateUtils.check(
                            meetingEntity.getContactLeaders(), null, MeetingContactLeaderEntity.class),
                    sysHeader);
        }
        //活动类型
        List<MeetingTypeEntity> meetingTypeEntities = meetingEntity.getMeetingTypes();
        this.addMeetingTypes(meetingEntity, meetingTypeEntities);
        //党小组
        this.addMeetingOrgGroups(meetingEntity);
        //支委会届次
        this.addMeetingOrgPeriod(meetingEntity);
        //工作任务
        List<MeetingTopicEntity> meetingEntityTopics = meetingEntity.getTopics();
        addMeetingTopics(meetingEntity, meetingEntityTopics);
        //活动议题 2021-09-13  tc
        addMeetingAgenda(meetingEntity, meetingEntity.getAgenda(), headers);
        //活动标签 2021-11-08  tc
        addMeetingTag(meetingEntity, meetingEntity.getMeetingTag());

        //决议附件 填写结果中处理
        if (meetingEntity.getMustApprove() == 1 && fromType == FROM_TYPE_MEETING) {//新建审批
            headers.set(HeaderHelper.OPERATOR_OID, sysHeader.getOid() + "");// 兼容移动端发起审批
            this.addApproval(headers, meetingEntity, false);
        } else if (fromType == FROM_TYPE_MEETING) {
            this.addMeetingStartHistory(meetingEntity);
        }

        //纪实附件
        List<MeetingResultFileEntity> resultFiles = meetingEntity.getResultFiles();
        if (CollectionUtils.isNotEmpty(resultFiles)) {
            Date date = new Date();
            resultFiles.forEach(file -> {
                file.setMeetingId(meetingEntity.getMeetingId());
                file.setCreateTime(date);
                file.setUpdateTime(date);
                file.setLastChangeUser(sysHeader.getUserId());
                file.setCreateUser(sysHeader.getUserId());
                file.setIsDel(0);
            });
            meetingResultFileMapper.insertList(resultFiles);
        }

        //判断是否为发起活动的添加
        if (fromType == FROM_TYPE_MEETING) {
            //获取要创建任务的人员
            //收集用户编号
            Set<Long> userIds = new HashSet<>();
            //主持人
            if (CollectionUtils.isNotEmpty(meetingEntity.getHostUser())) {
                meetingEntity.getHostUser().forEach(t -> {
                    if (t.getUserId() != null) {
                        userIds.add(t.getUserId());
                    }
                });
            }
            //参与人员
            if (CollectionUtils.isNotEmpty(meetingEntity.getParticipantUsers())) {
                meetingEntity.getParticipantUsers().forEach(t -> {
                    if (t.getUserId() != null) {
                        userIds.add(t.getUserId());
                    }
                });
            }
            //讲课人
            if (CollectionUtils.isNotEmpty(meetingEntity.getLecturers())) {
                meetingEntity.getLecturers().forEach(t -> {
                    if (t.getUserId() != null) {
                        userIds.add(t.getUserId());
                    }
                });
            }
            //联系领导干部
            if (CollectionUtils.isNotEmpty(meetingEntity.getContactLeaders())) {
                meetingEntity.getContactLeaders().forEach(t -> {
                    if (t.getUserId() != null) {
                        userIds.add(t.getUserId());
                    }
                });
            }

            // 纪实通知提醒实现
            if (!userIds.isEmpty()) {
                //如果勾选了钉钉提醒，就创建钉钉日程（改为ERP消息提醒）
                if (meetingEntity.getNotifyType() == 1) {
//                    this.createDingEvent(sysHeader.getRegionId(), sysHeader.getUserId(), meetingEntity, userIds.stream().collect(Collectors.toList()));
                    //创建定时消息，定时提醒
                    log.debug("当前会议参会人员：{}",JsonUtils.toJson(userIds));
                    String meetingContent = "您参与的【%s】会议将于【%s】在【%s】开始，请注意会议时间，及时参会！";
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    meetingContent = String.format(meetingContent,meetingEntity.getName(),format.format(meetingEntity.getStartTime()),meetingEntity.getAddress());
                    openService.zyPushTimingMessage(sysHeader,userIds.stream().collect(Collectors.toList()),null,meetingContent,pushMessageTime(meetingEntity.getStartTime(),Integer.valueOf(meetingEntity.getNotifyTime())));
                }
                createThirdTask(headers, sysHeader.getRegionId(), sysHeader.getUserId(), meetingEntity, userIds.stream().collect(Collectors.toList()));
            }
        }

        //topic添加后刷新移动端首页缓存
        indexService.collectRedis();

        return meetingEntity;
    }

    /**
     * 根据开始时间和推送设置计算返回推送时间
     *
     * @param startTime                 开始时间
     * @param notifyTime                推送设置
     * @return
     */
    private Date pushMessageTime(Date startTime,Integer notifyTime){
        Long pushMessageTime = null;
        switch (notifyTime){
            case 0:
                //开始时
                pushMessageTime = startTime.getTime();
                break;
            case 1:
                //5分钟前
                pushMessageTime = startTime.getTime() - (5*60*1000);
                break;
            case 2:
                //15分钟前
                pushMessageTime = startTime.getTime() - (15*60*1000);
                break;
            case 3:
                //30分钟前
                pushMessageTime = startTime.getTime() - (30*60*1000);
                break;
            case 4:
                //1小时前
                pushMessageTime = startTime.getTime() - (60*60*1000);
                break;
            case 5:
                //1天前
                pushMessageTime = startTime.getTime() - (24*60*60*1000);
                break;
        }
        return new Date(pushMessageTime);
    }

    /**
     * 更新联系领导
     *
     * @param upMeetingEntity  新的meetingEntity
     * @param oldMeetingEntity 原meetingEntity
     */
    private void updateMeetingContactLeaders(MeetingEntity upMeetingEntity, MeetingEntity oldMeetingEntity, HeaderHelper.SysHeader sysHeader) {
        checkSelContactLeaders(upMeetingEntity);
        List<MeetingContactLeaderEntity> upLeaderEntities = upMeetingEntity.getContactLeaders();
        List<MeetingContactLeaderEntity> oldLeaderEntities = new ArrayList<>();
        if (oldMeetingEntity != null) {
            oldLeaderEntities = oldMeetingEntity.getContactLeaders();
        }
        List<MeetingContactLeaderEntity> leaderEntities =
                CheckDataOperateUtils.check(
                        upLeaderEntities, oldLeaderEntities, MeetingContactLeaderEntity.class);
        log.debug("leaderEntities:" + leaderEntities);
        delMeetingContactLeaders(upMeetingEntity.getMeetingId(), leaderEntities, sysHeader);
        addMeetingContactLeaders(upMeetingEntity.getMeetingId(), leaderEntities, sysHeader);
    }

    /**
     * 更新讲课人
     *
     * @param upMeetingEntity 新的meetingEntity
     */
    private void updateMeetingLecturer(MeetingEntity upMeetingEntity, Short fromType, HeaderHelper.SysHeader sysHeader) {
        // 删除原有讲课人
        this.delLecturerByMid(upMeetingEntity.getMeetingId());// 删除讲课人
        // 添加新的讲课人
        List<MeetingUserEntity> lecturers = upMeetingEntity.getLecturers();
        if (CollectionUtils.isNotEmpty(lecturers)) {
            lecturers.forEach(meetingUserEntity -> meetingUserEntity.setTag(MeetingUserEntity.TAG_LECTURER));
            this.addMeetingUsers(upMeetingEntity, fromType, lecturers);
        }
    }

    /**
     * 添加联系领导
     *
     * @param meetingId      纪实id
     * @param leaderEntities 联系领导list
     * @param sysHeader      Header信息
     */
    private void addMeetingContactLeaders(
            long meetingId,
            List<MeetingContactLeaderEntity> leaderEntities,
            HeaderHelper.SysHeader sysHeader) {
        if (CollectionUtils.isNotEmpty(leaderEntities)) {
            List<MeetingContactLeaderEntity> addlist = new ArrayList<>();
            for (MeetingContactLeaderEntity contactLeaderEntity : leaderEntities) {
                if (contactLeaderEntity.getOperateTag().equals(1)) {
                    contactLeaderEntity.setMeetingContactLeaderId(null);
                    contactLeaderEntity.setMeetingId(meetingId);
                    contactLeaderEntity.setCreateUser(sysHeader.getUserId());
                    contactLeaderEntity.setCreateTime(DateTime.now().toDate());
                    // 未删除
                    contactLeaderEntity.setIsDel(0);
                    contactLeaderEntity.setUpdateTime(null);
                    contactLeaderEntity.setLastChangeUser(null);
                    addlist.add(contactLeaderEntity);
                }
            }
            if (CollectionUtils.isNotEmpty(addlist)) {
                meetingContactLeaderMapper.insertList(addlist);
            }
        }
    }

    private void delMeetingContactLeaders(long meetingId, List<MeetingContactLeaderEntity> list, HeaderHelper.SysHeader sysHeader) {
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> delIdList = new ArrayList<>();
            for (MeetingContactLeaderEntity contactLeaderEntity : list) {
                if (contactLeaderEntity.getOperateTag().equals(2) && contactLeaderEntity.getMeetingContactLeaderId() != null) {
                    delIdList.add(contactLeaderEntity.getMeetingContactLeaderId());
                }
                if (CollectionUtils.isNotEmpty(delIdList)) {
                    Example example = new Example(MeetingContactLeaderEntity.class);
                    example.createCriteria().andIn("meetingContactLeaderId", delIdList)
                            .andEqualTo("meetingId", meetingId);
                    MeetingContactLeaderEntity up = new MeetingContactLeaderEntity();
                    up.setIsDel(1);
                    up.setUpdateTime(DateTime.now().toDate());
                    up.setLastChangeUser(sysHeader.getUserId());
                    meetingContactLeaderMapper.updateByExampleSelective(up, example);
                }
            }
        }
    }

    /**
     * 保存会议关联的支委会届次
     *
     * @param meetingEntity 会议信息
     */
    private void addMeetingOrgPeriod(MeetingEntity meetingEntity) {
        if (CollectionUtils.isNotEmpty(meetingEntity.getOrgPeriods())) {
            for (MeetingOrgPeriodEntity meetingOrgPeriodEntity : meetingEntity.getOrgPeriods()) {
                meetingOrgPeriodEntity.setMeetingOrgPeriodId(null);
                meetingOrgPeriodEntity.setCreateTime(meetingEntity.getUpdateTime() == null ? meetingEntity.getCreateTime() : meetingEntity.getUpdateTime());
                meetingOrgPeriodEntity.setCreateUser(meetingEntity.getLastChangeUser() == null ? meetingEntity.getCreateUser() : meetingEntity.getLastChangeUser());
                meetingOrgPeriodEntity.setMeetingId(meetingEntity.getMeetingId());
                meetingOrgPeriodEntity.setStatus((short) 1);
            }
            meetingOrgPeriodMapper.insertList(meetingEntity.getOrgPeriods());
        }
    }

    /**
     * 保存会议关联的党小组
     *
     * @param meetingEntity 会议信息
     */
    private void addMeetingOrgGroups(MeetingEntity meetingEntity) {
        if (CollectionUtils.isNotEmpty(meetingEntity.getOrgGroups())) {
            for (MeetingOrgGroupEntity meetingOrgGroupEntity : meetingEntity.getOrgGroups()) {
                meetingOrgGroupEntity.setMeetingOrgGroupId(null);
                meetingOrgGroupEntity.setCreateTime(meetingEntity.getUpdateTime() == null ? meetingEntity.getCreateTime() : meetingEntity.getUpdateTime());
                meetingOrgGroupEntity.setCreateUser(meetingEntity.getLastChangeUser() == null ? meetingEntity.getCreateUser() : meetingEntity.getLastChangeUser());
                meetingOrgGroupEntity.setMeetingId(meetingEntity.getMeetingId());
                meetingOrgGroupEntity.setStatus((short) 1);
            }
            meetingOrgGroupMapper.insertList(meetingEntity.getOrgGroups());
        }
    }

    /**
     * 更新会议关联的党小组
     *
     * @param upMeetingEntity 新的会议信息
     */
    private void upMeetingOrgGroups(MeetingEntity upMeetingEntity) {
        Example example = new Example(MeetingOrgGroupEntity.class);
        example.createCriteria().andEqualTo("meetingId", upMeetingEntity.getMeetingId());
        meetingOrgGroupMapper.deleteByExample(example);
        this.addMeetingOrgGroups(upMeetingEntity);
    }

    /**
     * 更新会议关联的支委会
     *
     * @param upMeetingEntity 新的会议信息
     */
    private void upMeetingOrgPeriod(MeetingEntity upMeetingEntity) {
        Example example = new Example(MeetingOrgPeriodEntity.class);
        example.createCriteria().andEqualTo("meetingId", upMeetingEntity.getMeetingId());
        meetingOrgPeriodMapper.deleteByExample(example);
        this.addMeetingOrgPeriod(upMeetingEntity);
    }

    /**
     * 修改活动
     *
     * @param sysHeader       用户信息
     * @param upMeetingEntity 活动信息
     * @param fromType        修改来源：1.活动管理页面添加 2.记实情况页面添加
     */
    int updateMeeting(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, MeetingEntity upMeetingEntity, Short fromType) {
        int upnum;
        // 查询oldMeeting by id
        MeetingEntity oldMeetingEntity = this.findById(sysHeader, upMeetingEntity.getMeetingId());
        upMeetingEntity.setCreateTime(oldMeetingEntity.getCreateTime());
        upMeetingEntity.setCreateUser(oldMeetingEntity.getCreateUser());
        // 当活动状态为“发起审批中”、“发起未通过”时可用。可以修改活动发起时的第一、二、三个步骤的全部内容。
        // addType=2 修改全部内容
        // 判断当前组织是否有所选任务
        List<Long> meetingTaskIds =
                upMeetingEntity.getMeetingTypes().stream()
                        .map(MeetingTypeEntity::getMeetingTaskId)
                        .distinct()
                        .collect(Collectors.toList());
        // 查询活动类型信息
        List<MeetingTaskEntity> meetingTaskEntities = meetingTaskService.findByOrgIdAndIds(sysHeader.getOid(), meetingTaskIds, upMeetingEntity.getStartTime());
        boolean hasGroup = false;
        if (transferService.fromSystemSzf(headers)) {
            long hasGroupCount = upMeetingEntity.getMeetingTypes().stream().filter(i -> i.getTypeId() == 3).count();
            if (hasGroupCount > 0) {
                hasGroup = true;
            }
        }
        if (this.isEditAll(oldMeetingEntity, fromType)) {
            this.checkAddParam(sysHeader, upMeetingEntity, meetingTaskEntities, fromType, hasGroup);
            // 修改全部信息
            upMeetingEntity.setLastChangeUser(sysHeader.getUserId());
            upMeetingEntity.setUpdateTime(DateTime.now().toDate());
            this.reSetStatus(upMeetingEntity, fromType);
            // 更新活动
            upnum = meetingMapper.updateByPrimaryKeySelective(upMeetingEntity);
            // 更新类型
            this.upMeetingTypes(upMeetingEntity, oldMeetingEntity);
            // 更新党小组、支委会届次
            this.upMeetingOrgGroups(upMeetingEntity);
            this.upMeetingOrgPeriod(upMeetingEntity);
            // 更新任务
//            this.upMeetingTopics(upMeetingEntity, oldMeetingEntity);
            // 更新活动议程   2021-09-13 tc
            this.upMeetingAgenda(upMeetingEntity, oldMeetingEntity, headers);
            // 更新活动标签   2021-11-08 tc
            this.upMeetingTag(upMeetingEntity, oldMeetingEntity);
            upUsers(headers, upMeetingEntity, fromType, oldMeetingEntity);
            this.undoApprove(headers, fromType, oldMeetingEntity, "修改");
            // 新建审批
            if (upMeetingEntity.getMustApprove() == 1 && fromType == FROM_TYPE_MEETING) {
                this.addApproval(headers, upMeetingEntity, true);
                // 之前需要审批 更改为不需要审批，记录历史表
            } else if (fromType == FROM_TYPE_MEETING) {
                upMeetingEntity.setLastChangeUser(sysHeader.getUserId());
                upMeetingEntity.setUpdateTime(DateTime.now().toDate());
                this.addMeetingStartHistory(upMeetingEntity);
            }
            // 当活动状态为“待举办”时可用，可以进入活动发起时的第三个步骤，对参会人员、列席人员进行添加，此时不能减少人员，不能修改其他活动信息。
        } else if (this.isMeetingAddUsers(oldMeetingEntity, fromType)) {
            upnum = this.addMeetingUsers(headers, upMeetingEntity, oldMeetingEntity, fromType);
        } else if (this.isReportUpdateUsers(oldMeetingEntity, fromType)) {
            // 添加人员
            upnum = this.addMeetingUsers(headers, upMeetingEntity, oldMeetingEntity, fromType);// 添加参会人员
            // 删除isAdd的人员
            this.delMeetingUsersByIsAdd(upMeetingEntity, oldMeetingEntity);
            // 修改人员信息
            upnum += this.upMeetingUsers(upMeetingEntity, fromType, oldMeetingEntity);
        } else {
            throw new ApiException("update meeting error", new Result<>(errors, 1807, HttpStatus.FORBIDDEN.value(), "修改"));
        }
        // 更新联系领导
        log.debug("更新联系领导" + upMeetingEntity + " === " + oldMeetingEntity);
        updateMeetingContactLeaders(upMeetingEntity, oldMeetingEntity, sysHeader);

        // 判断讲课人是否必填 判断讲课标题是否必填
        // hasLecturer hasLectureTitle 默认值
        upMeetingEntity.setHasLecturer(0);
        upMeetingEntity.setHasLectureTitle(0);
        for (MeetingTaskEntity mt : meetingTaskEntities) {
            if (mt.getHasLectureTitle() != null && mt.getHasLectureTitle().equals(1)) {
                upMeetingEntity.setHasLectureTitle(1);
            }
            if (mt.getHasLecturer() != null && mt.getHasLecturer().equals(1)) {
                upMeetingEntity.setHasLecturer(1);
            }
        }
        // 判断讲课人是否必填
        if (upMeetingEntity.getHasLecturer() != null && upMeetingEntity.getHasLecturer() == 1) {
            if (CollectionUtils.isEmpty(upMeetingEntity.getLecturers())) {
                throw new ApiException("需要填写讲课人", new Result<>(errors, 2007, HttpStatus.BAD_REQUEST.value()));
            }
        }
        // 判断讲课标题是否必填
        if (upMeetingEntity.getHasLectureTitle() != null && upMeetingEntity.getHasLectureTitle() == 1) {
            if (StringUtils.isBlank(upMeetingEntity.getLectureTitle())) {
                throw new ApiException("需要填写讲课标题", new Result<>(errors, 2008, HttpStatus.BAD_REQUEST.value()));
            }
        }
        // 更新讲课人
        updateMeetingLecturer(upMeetingEntity, fromType, sysHeader);


        // 更新总是能更新的字段
        MeetingEntity upEntity = new MeetingEntity();
        upEntity.setMeetingId(upMeetingEntity.getMeetingId());

        // 是否选择联系领导字段更新
        upEntity.setSelContactLeaders(upMeetingEntity.getSelContactLeaders());
        upEntity.setHasLecturer(upMeetingEntity.getHasLecturer());
        upEntity.setHasLectureTitle(upMeetingEntity.getHasLectureTitle());

        // 讲课标题
        upEntity.setLectureTitle(upMeetingEntity.getLectureTitle());

        // 如果结束时间不为空，根据开始和结束时间计算时长
        if (upMeetingEntity.getEndTime() != null) {
            // 计算间隔秒数
            long second = DateUtil.between(upMeetingEntity.getStartTime(), upMeetingEntity.getEndTime(), DateUnit.SECOND);
            Double hours = NumberUtil.div(second, 3600f, 1);
            upEntity.setTotalHours(hours);
        }

        // 更新人
        upEntity.setLastChangeUser(sysHeader.getUserId());

        // 更新时间
        upEntity.setUpdateTime(new Date());
        meetingMapper.updateByPrimaryKeySelective(upEntity);

        /**
         * 更新附件  在外层方法里 有单独操作t_meeting_result_file表的方法 所以这里注释掉  tc 2021-09-15
         * 单独的修改操作 会直接调用这个类，没有外层操作t_meeting_result_file表的方法  所以还是需要这段代码，在活动录入的修改时，就让它重复执行一次吧  tc 2021-09-24
         */
        meetingResultFileMapper.deleteByMeetingId(upMeetingEntity.getMeetingId());

        List<MeetingResultFileEntity> resultFiles = upMeetingEntity.getResultFiles();
        if (CollectionUtils.isNotEmpty(resultFiles)) {
            Date date = new Date();
            resultFiles.forEach(file -> {
                file.setMeetingId(upMeetingEntity.getMeetingId());
                file.setCreateTime(date);
                file.setUpdateTime(date);
                file.setLastChangeUser(sysHeader.getUserId());
                file.setCreateUser(sysHeader.getUserId());
                file.setIsDel(0);
            });
            meetingResultFileMapper.insertList(resultFiles);
        }

        // 判断是否为发起活动的修改
        if (fromType == FROM_TYPE_MEETING) {
            /**
             * 更新钉钉日程和任务中心任务信息
             * 先删除后添加
             * tc 2022-03-14
             */
            // 获取要创建任务的人员
            // 收集用户编号
            Set<Long> userIds = new HashSet<>();
            // 主持人
            if (CollectionUtils.isNotEmpty(upMeetingEntity.getHostUser())) {
                upMeetingEntity.getHostUser().forEach(t -> {
                    if (t.getUserId() != null) {
                        userIds.add(t.getUserId());
                    }
                });
            }
            // 参与人员
            if (CollectionUtils.isNotEmpty(upMeetingEntity.getParticipantUsers())) {
                upMeetingEntity.getParticipantUsers().forEach(t -> {
                    if (t.getUserId() != null) {
                        userIds.add(t.getUserId());
                    }
                });
            }
            // 讲课人
            if (CollectionUtils.isNotEmpty(upMeetingEntity.getLecturers())) {
                upMeetingEntity.getLecturers().forEach(t -> {
                    if (t.getUserId() != null) {
                        userIds.add(t.getUserId());
                    }
                });
            }
            // 联系领导干部
            if (CollectionUtils.isNotEmpty(upMeetingEntity.getContactLeaders())) {
                upMeetingEntity.getContactLeaders().forEach(t -> {
                    if (t.getUserId() != null) {
                        userIds.add(t.getUserId());
                    }
                });
            }

            /**如果以前存在钉钉日程，先删除旧日程
             * 应产品要求如果新修改的活动为不通知状态 则不删除旧的日程
             * tc 2022-03-24
             *
             * */
            if (StringUtils.isNotEmpty(oldMeetingEntity.getDingEventId()) && upMeetingEntity.getNotifyType() == 1) {
                this.delDingEvent(sysHeader.getRegionId(), oldMeetingEntity.getDingEventCreateUser(), oldMeetingEntity.getDingEventId(), upMeetingEntity.getMeetingId());
            }

            // 纪实通知提醒实现
            if (!userIds.isEmpty()) {
                // 如果勾选了钉钉提醒，就创建钉钉日程
                if (upMeetingEntity.getNotifyType() == 1) {
                    // 创建钉钉日程
                    this.createDingEvent(sysHeader.getRegionId(), sysHeader.getUserId(), upMeetingEntity, userIds.stream().collect(Collectors.toList()));
                }
            }

            /* 如果以前存在三方任务，先删除旧任务*/
            String scid = MeetingCanstant.MEETING_PENDING_TASK_KEY + sysHeader.getRegionId() + "_" + upMeetingEntity.getMeetingId();
            this.delThirdTask(headers, upMeetingEntity.getMeetingId(), Collections.singletonList(scid));
            /* 如果新组织生活的结束时间在当前时间之后，创建新任务*/
            if (upMeetingEntity.getEndTime().getTime() > new Date().getTime()) {
                //调用PendingService创建任务
                this.createThirdTask(headers, sysHeader.getRegionId(), sysHeader.getUserId(), upMeetingEntity, userIds.stream().collect(Collectors.toList()));
            }
            /*
             * 更新钉钉日程和任务中心任务信息 结束
             */
        }

        // 更新活动 刷新缓存
        indexService.collectRedis();
        return upnum;
    }

    /**
     * 修改活动、纪实时更新人员信息 全量更新
     */
    private void upUsers(HttpHeaders headers, MeetingEntity upMeetingEntity, Short fromType, MeetingEntity oldMeetingEntity) {
        Example example = new Example(MeetingUserEntity.class);
        example.createCriteria().andEqualTo("meetingId", oldMeetingEntity.getMeetingId()).andIn("tag", Arrays.asList(3, 4));
        meetingUserMapper.deleteByExample(example);
        this.addMeetingUsers(headers, upMeetingEntity, null, fromType);
    }

    /**
     * 撤销审批流
     */
    private void undoApprove(HttpHeaders headers, Short fromType, MeetingEntity meetingEntity, String remark) {
        // 如果状态是审批中的。撤回审批
        if (meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_APPROVAL_APPLY.shortValue()
                && fromType == FROM_TYPE_MEETING) {
            if (!workflowService.undoApprove(meetingEntity.getWorkflowTaskId(), headers)) {
                throw new ApiException(remark + "活动,撤销审批流出错", new Result<>(errors, 1806, HttpStatus.OK.value(), remark));
            }
        }
    }


    /**
     * 初始化活动状态
     */
    private void initMeetingStatus(MeetingEntity meetingEntity) {
        if (meetingEntity.getMustApprove() == 0) {
            meetingEntity.setStatus(MeetingCanstant.MEETING_STATUS_SOON_START.shortValue());
        } else if (meetingEntity.getMustApprove() == 1) {
            meetingEntity.setStatus(MeetingCanstant.MEETING_STATUS_APPROVAL_APPLY.shortValue());
        }
    }

    /**
     * 添加参会人员信息
     */
    private int addMeetingUsers(MeetingEntity meetingEntity, Short fromType, List<MeetingUserEntity> meetingUserEntities) {
        //查询该meeting所有请假了的人
        Long meetingId = meetingEntity.getMeetingId();
        List<MeetingLeaveEntity> leaveUserEntities = meetingLeaveMapper.queryLeaveUser(meetingId);
        Map<Long, Short> statusMap = new HashMap<>();
        leaveUserEntities.forEach(i -> statusMap.put(i.getUserId(), i.getType()));
        meetingUserEntities.forEach(meetingUserEntity -> {
            meetingUserEntity.setMeetingUserId(null);
            meetingUserEntity.setMeetingId(meetingEntity.getMeetingId());
            if (fromType == FROM_TYPE_MEETING) {// 活动管理添加
                meetingUserEntity.setIsAdd(MeetingUserEntity.MEETING_ADD);
                if (meetingEntity.getIsSignIn() == 1) {// 需要签到，默认为未签到
                    meetingUserEntity.setSignStatus((short) 2);
                    if (meetingEntity.getSignInWay() == 0) {
                        //如果签到方式是手写签到，默认为已签到
                        meetingUserEntity.setSignStatus((short) 1);
                    }
                } else {// 不需要签到，默认为签到
                    meetingUserEntity.setSignStatus((short) 1);
                }
                //判断是否请假，如果已经请假，则该用户的sign_status不变，更新old_sign_status
                if (statusMap.get(meetingUserEntity.getUserId()) != null) {
                    int signStatus = statusMap.get(meetingUserEntity.getUserId()) == 1 ? 3 : 4;
                    meetingUserEntity.setSignStatus((short) signStatus);
                    if (meetingEntity.getIsSignIn() == 1) {// 需要签到，默认为未签到
                        meetingUserEntity.setOldSignStatus((short) 2);
                        if (meetingEntity.getSignInWay() == 0) {
                            //如果签到方式是手写签到，默认为已签到
                            meetingUserEntity.setOldSignStatus((short) 1);
                        }
                    } else {// 不需要签到，默认为签到
                        meetingUserEntity.setOldSignStatus((short) 1);
                    }
                }

            } else {// 记实添加
                meetingUserEntity.setIsAdd(MeetingUserEntity.REPORT_ADD);
                if (meetingUserEntity.getSignStatus() == null) {
                    meetingUserEntity.setSignStatus((short) 1);
                }
            }
        });
        return meetingUserMapper.insertList(meetingUserEntities);
    }

    /**
     * 添加工作任务
     */
    private void addMeetingTopics(MeetingEntity meetingEntity, List<MeetingTopicEntity> meetingEntityTopics) {
        if (meetingEntityTopics != null && !meetingEntityTopics.isEmpty()) {
            meetingEntityTopics.forEach(meetingTopicEntity -> {
                meetingTopicEntity.setMeetingTopicId(null);
                meetingTopicEntity.setStatus(1);// 默认未填写任务答案
                meetingTopicEntity.setCreateUser(meetingEntity.getLastChangeUser() != null ? meetingEntity.getLastChangeUser() : meetingEntity.getCreateUser());
                meetingTopicEntity.setCreateTime(meetingEntity.getUpdateTime() != null ? meetingEntity.getUpdateTime() : meetingEntity.getCreateTime());
                meetingTopicEntity.setMeetingId(meetingEntity.getMeetingId());
            });
            meetingTopicMapper.insertList(meetingEntityTopics);
        }
    }

    /**
     * 添加活动议程
     */
    private void addMeetingAgenda(MeetingEntity meetingEntity, List<MeetingAgendaEntity> meetingAgenda, HttpHeaders headers) {
        if (meetingAgenda != null && !meetingAgenda.isEmpty()) {
            meetingAgenda.forEach(ma -> {
                ma.setLastChangeUser(meetingEntity.getLastChangeUser() != null ? meetingEntity.getLastChangeUser() : meetingEntity.getCreateUser());
                ma.setLastUpdateTime(meetingEntity.getUpdateTime() != null ? meetingEntity.getUpdateTime() : meetingEntity.getCreateTime());
                ma.setMeetingId(meetingEntity.getMeetingId());
            });
            meetingAgendaMapper.insertList(meetingAgenda);
            //添加议程标签
            insertAgendaTag(meetingAgenda, meetingEntity);
        }
    }


    private void insertAgendaTag(List<MeetingAgendaEntity> meetingAgenda, MeetingEntity meetingEntity) {
        List<MeetingTagEntity> tags = new ArrayList<>();
        meetingAgenda.forEach(i -> {
            addAgendaTag(meetingEntity, i.getMeetingTag(), i.getAgendaId(), tags);
        });
        if (CollectionUtils.isNotEmpty(tags)) {
            meetingTagMapper.insertList(tags);
        }
    }

    /**
     * 添加议程标签
     */
    private void addAgendaTag(MeetingEntity meetingEntity, List<MeetingTagEntity> meetingTag, Long agendaId, List<MeetingTagEntity> tags) {
        if (meetingTag != null && !meetingTag.isEmpty()) {
            meetingTag.forEach(mt -> {
                mt.setAgendaId(agendaId);
                mt.setLastChangeUser(meetingEntity.getLastChangeUser() != null ? meetingEntity.getLastChangeUser() : meetingEntity.getCreateUser());
                mt.setLastUpdateTime(meetingEntity.getUpdateTime() != null ? meetingEntity.getUpdateTime() : meetingEntity.getCreateTime());
                mt.setMeetingId(meetingEntity.getMeetingId());
                tags.add(mt);
            });
        }
    }

    /**
     * 添加活动议程
     */
    private void addMeetingTag(MeetingEntity meetingEntity, List<MeetingTagEntity> meetingTag) {
        if (meetingTag != null && !meetingTag.isEmpty()) {
            meetingTag.forEach(mt -> {
                mt.setLastChangeUser(meetingEntity.getLastChangeUser() != null ? meetingEntity.getLastChangeUser() : meetingEntity.getCreateUser());
                mt.setLastUpdateTime(meetingEntity.getUpdateTime() != null ? meetingEntity.getUpdateTime() : meetingEntity.getCreateTime());
                mt.setMeetingId(meetingEntity.getMeetingId());
            });
            meetingTagMapper.insertList(meetingTag);
        }
    }

    /**
     * 添加活动类型
     */
    private void addMeetingTypes(MeetingEntity meetingEntity, List<MeetingTypeEntity> meetingTypeEntities) {
        if (meetingTypeEntities != null && !meetingTypeEntities.isEmpty()) {
            meetingTypeEntities.forEach(meetingTypeEntity -> {
                meetingTypeEntity.setMeetingTypeId(null);
                meetingTypeEntity.setMeetingId(meetingEntity.getMeetingId());
            });
            meetingTypeMapper.insertList(meetingTypeEntities);
        }
    }

    /**
     * 发起审批
     */
    private void addApproval(HttpHeaders headers, MeetingEntity meetingEntity, boolean isUpdate) {
        ApprovalBase approvalBase = workflowService.buildApprovalBase(meetingEntity.getWorkflowId(), null,
                meetingEntity.getName(), meetingEntity.getMeetingId(), 6, null, headerService.bulidHeader(headers));
        long workfowTaskId = workflowService.addApproval(approvalBase, headers);
        if (workfowTaskId < 0) {
            String remark = isUpdate ? "修改" : "新增";
            throw new ApiException(remark + "活动出错", new Result<>(errors, 1806, HttpStatus.OK.value(), remark));
        } else {
            // 更新workfowTaskId
            MeetingEntity me = new MeetingEntity();
            me.setMeetingId(meetingEntity.getMeetingId());
            me.setWorkflowTaskId(workfowTaskId);
            me.setStatus(MeetingCanstant.MEETING_STATUS_APPROVAL_APPLY.shortValue());
            if (StringUtils.isBlank(meetingEntity.getWorkflowName())) {
                me.setWorkflowName("自定义");
            }
            this.updateMeeting(me);
            // 发起审批
            MeetingHistoryEntity meetingHistoryEntity = this.buildMeetingHistoryEntity(meetingEntity);
            if (isUpdate) {
                meetingHistoryEntity.setReason("修改" + StringCanstant.ACTIVITY + "信息");
            }
            meetingHistoryMapper.insert(meetingHistoryEntity);
        }
    }

    /**
     * 删除填写结果时添加的参会人员
     */
    private void delMeetingUsersByIsAdd(MeetingEntity upMeetingEntity, MeetingEntity oldMeetingEntity) {
        List<MeetingUserEntity> delMeetingUserEntities = getDelIsAddMeetingUserEntities(upMeetingEntity, oldMeetingEntity);
        delMeetingUserEntities.forEach(meetingUserMapper::delete);
    }

    private List<MeetingUserEntity> getDelIsAddMeetingUserEntities(MeetingEntity upMeetingEntity, MeetingEntity oldMeetingEntity) {
        List<MeetingUserEntity> delMeetingUserEntities = new ArrayList<>();// 删除
        List<MeetingUserEntity> oldParticipantUsers = oldMeetingEntity.getParticipantUsers();
        List<MeetingUserEntity> oldAttendUsers = oldMeetingEntity.getAttendUsers();
        // 删除的参会人员和列席人员
        oldParticipantUsers.forEach(
                meetingUserEntity -> {
                    if (isDelAddMeetingUser(upMeetingEntity.getParticipantUsers(), meetingUserEntity)) {
                        delMeetingUserEntities.add(meetingUserEntity);
                    }
                });
        oldAttendUsers.forEach(
                meetingUserEntity -> {
                    if (isDelAddMeetingUser(upMeetingEntity.getAttendUsers(), meetingUserEntity)) {
                        delMeetingUserEntities.add(meetingUserEntity);
                    }
                });
        return delMeetingUserEntities;
    }

    /**
     * 是否删除meetingUserEntity
     *
     * @param upMeetingUsers 更新后的meetingUser
     */
    private boolean isDelAddMeetingUser(List<MeetingUserEntity> upMeetingUsers, MeetingUserEntity meetingUserEntity) {
        boolean isDel = true;
        // 非记实结果添加不能删除
        if (meetingUserEntity.getIsAdd() != MeetingUserEntity.REPORT_ADD) {
            return false;
        }
        // upMeetingEntity 存在 不能删除
        for (MeetingUserEntity me : upMeetingUsers) {
            if ((me.getUserId() != null && me.getUserId().equals(meetingUserEntity.getUserId()))
                    || (me.getMeetingUserId() != null && me.getMeetingUserId().equals(meetingUserEntity.getMeetingUserId()))) {
                isDel = false;
                break;
            }
        }
        return isDel;
    }

    /**
     * 更新工作任务
     */
    private void upMeetingTopics(MeetingEntity upMeetingEntity, MeetingEntity oldMeetingEntity) {
        // 删除
        List<MeetingTopicEntity> delTopics = oldMeetingEntity.getTopics().stream().filter(meetingTopicEntity -> meetingTopicEntity.getMeetingTopicId() != null &&
                upMeetingEntity.getTopics().stream().noneMatch(omt -> meetingTopicEntity.getMeetingTopicId().equals(omt.getMeetingTopicId()))).collect(Collectors.toList());
        if (delTopics != null && !delTopics.isEmpty()) {
            delTopics.forEach(meetingTopicMapper::delete);
        }
        // 新增
        List<MeetingTopicEntity> addTopics = upMeetingEntity.getTopics().stream().filter(meetingTopicEntity -> meetingTopicEntity.getMeetingTopicId() == null).collect(Collectors.toList());
        addMeetingTopics(upMeetingEntity, addTopics);

        // 修改
        List<MeetingTopicEntity> updTopics = upMeetingEntity.getTopics().stream().filter(meetingTopicEntity -> meetingTopicEntity.getMeetingTopicId() != null &&
                oldMeetingEntity.getTopics().stream().anyMatch(omt -> meetingTopicEntity.getMeetingTopicId().equals(omt.getMeetingTopicId()))).collect(Collectors.toList());
        if (updTopics != null && !updTopics.isEmpty()) {
            updTopics.forEach(meetingTopicMapper::updateByPrimaryKeySelective);
        }
    }

    /**
     * 更新活动议程
     */
    private void upMeetingAgenda(MeetingEntity upMeetingEntity, MeetingEntity oldMeetingEntity, HttpHeaders headers) {
        log.debug("进入更新活动议程方法! upMeetingEntity={}    oldMeetingEntity={}", upMeetingEntity, oldMeetingEntity);
        // 删除
        List<MeetingAgendaEntity> delAgenda = CollectionUtils.isEmpty(oldMeetingEntity.getAgenda()) ? Collections.EMPTY_LIST : oldMeetingEntity.getAgenda().stream().filter(
                meetingAgenda -> {
                    if (CollectionUtils.isEmpty(upMeetingEntity.getAgenda())) {
                        return true;
                    }
                    return meetingAgenda.getAgendaId() != null &&
                            upMeetingEntity.getAgenda().stream().noneMatch(omt -> meetingAgenda.getAgendaId().equals(omt.getAgendaId()));
                }
        ).collect(Collectors.toList());

        if (!delAgenda.isEmpty()) {
            delAgenda.forEach(meetingAgendaMapper::delete);
            //删除对应的议程标签
            delAgendaTag(delAgenda, oldMeetingEntity.getMeetingId());
        }

        // 新增
        List<MeetingAgendaEntity> addAgenda = CollectionUtils.isEmpty(upMeetingEntity.getAgenda()) ? Collections.EMPTY_LIST : upMeetingEntity.getAgenda().stream().filter(meetingAgenda -> meetingAgenda.getAgendaId() == null && (StringUtils.isNotBlank(meetingAgenda.getAgendaTitle()) || StringUtils.isNotBlank(meetingAgenda.getAgendaContent()))).collect(Collectors.toList());
        addMeetingAgenda(upMeetingEntity, addAgenda, headers);
        //新增对应的议程标签
        addAgendaTag(addAgenda, upMeetingEntity);
        // 修改
        List<MeetingAgendaEntity> updAgenda = CollectionUtils.isEmpty(upMeetingEntity.getAgenda()) ? Collections.EMPTY_LIST : upMeetingEntity.getAgenda().stream().filter(meetingAgenda -> meetingAgenda.getAgendaId() != null &&
                oldMeetingEntity.getAgenda().stream().anyMatch(omt -> meetingAgenda.getAgendaId().equals(omt.getAgendaId()))).collect(Collectors.toList());
        if (!updAgenda.isEmpty()) {
            updAgenda.forEach(meetingAgendaMapper::updateByPrimaryKeySelective);
            //更新的议程，直接先删除议程标签再插入
            delAgendaTag(updAgenda, oldMeetingEntity.getMeetingId());
            addAgendaTag(updAgenda, upMeetingEntity);
        }
        log.debug("更新活动议程!  delAgenda={}    addAgenda={}     updAgenda={}", delAgenda, addAgenda, updAgenda);
    }


    private void addAgendaTag(List<MeetingAgendaEntity> agendaEntities, MeetingEntity meetingEntity) {
        List<Long> addAgendaIds = agendaEntities.stream().map(MeetingAgendaEntity::getAgendaId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addAgendaIds)) {
            insertAgendaTag(agendaEntities, meetingEntity);
        }
    }

    private void delAgendaTag(List<MeetingAgendaEntity> agendaEntities, Long meetingId) {
        List<Long> delAgendaIds = agendaEntities.stream().map(MeetingAgendaEntity::getAgendaId).collect(Collectors.toList());
        //将对应的议程标签删除
        if (CollectionUtils.isNotEmpty(delAgendaIds)) {
            meetingTagMapper.deleteByAgendaId(meetingId, delAgendaIds);
        }
    }

    /**
     * 更新活动标签
     */
    private void upMeetingTag(MeetingEntity upMeetingEntity, MeetingEntity oldMeetingEntity) {
        log.debug("进入更新活动标签方法! upMeetingEntity={}    oldMeetingEntity={}", upMeetingEntity, oldMeetingEntity);
//        if (null == oldMeetingEntity.getAgenda() || ObjectUtil.isEmpty(oldMeetingEntity.getMeetingTag())) {
//            return;
//        }
        // 新增
        List<MeetingTagEntity> delTag = null;
        List<MeetingTagEntity> addTag = null;
        List<MeetingTagEntity> updTag = null;
        if (CollectionUtils.isNotEmpty(upMeetingEntity.getMeetingTag())) {
            // 删除
            delTag = oldMeetingEntity.getMeetingTag().stream().filter(meetingTag -> meetingTag.getMeetingTagId() != null &&
                    upMeetingEntity.getMeetingTag().stream().noneMatch(omt -> meetingTag.getMeetingTagId().equals(omt.getMeetingTagId()))).collect(Collectors.toList());
            if (!delTag.isEmpty()) {
                delTag.forEach(meetingTagMapper::delete);
            }
            addTag = upMeetingEntity.getMeetingTag().stream().filter(meetingTag -> meetingTag.getMeetingTagId() == null).collect(Collectors.toList());
            addMeetingTag(upMeetingEntity, addTag);

            // 修改
            updTag = upMeetingEntity.getMeetingTag().stream().filter(meetingTag -> meetingTag.getMeetingTagId() != null &&
                    oldMeetingEntity.getMeetingTag().stream().anyMatch(omt -> meetingTag.getMeetingTagId().equals(omt.getMeetingTagId()))).collect(Collectors.toList());
            if (!updTag.isEmpty()) {
                updTag.forEach(meetingTagMapper::updateByPrimaryKeySelective);
            }
        }

        log.debug("更新活动议程!   delTag={}    addTag={}     updTag={}", delTag, addTag, updTag);
    }

    /**
     * 更新活动类型
     */
    private void upMeetingTypes(MeetingEntity upMeetingEntity, MeetingEntity oldMeetingEntity) {
        // 删除类型
        List<MeetingTypeEntity> delMeetingTypes = oldMeetingEntity.getMeetingTypes().stream().filter(meetingTypeEntity -> meetingTypeEntity.getMeetingTypeId() != null &&
                upMeetingEntity.getMeetingTypes().stream().noneMatch(omt -> meetingTypeEntity.getMeetingTypeId().equals(omt.getMeetingTypeId()))).collect(Collectors.toList());
        if (!delMeetingTypes.isEmpty()) {
            delMeetingTypes.forEach(meetingTypeMapper::delete);
        }
        // 新增类型
        List<MeetingTypeEntity> addMeetingTypes = upMeetingEntity.getMeetingTypes().stream().filter(meetingTypeEntity -> meetingTypeEntity.getMeetingTypeId() == null).collect(Collectors.toList());
        this.addMeetingTypes(upMeetingEntity, addMeetingTypes);
    }

    /**
     * 更新时重新设置活动状态
     */
    private void reSetStatus(MeetingEntity meetingEntity, Short fromType) {
        if (fromType == FROM_TYPE_REPORT) {//纪实结果更新活动不更新活动状态
            return;
        }
        //活动状态
        if (meetingEntity.getMustApprove() == 0) {
            meetingEntity.setStatus(MeetingCanstant.MEETING_STATUS_SOON_START.shortValue());
        } else if (meetingEntity.getMustApprove() == 1) {
            meetingEntity.setStatus(MeetingCanstant.MEETING_STATUS_APPROVAL_APPLY.shortValue());
        }
    }

    /**
     * 校验是否是编辑全部
     * 活动管理编辑 发起审批中、审批未通过
     * 纪实结果编辑 通过纪实结果添加的活动不能修改除人员信息外的活动基本信息
     */
    private boolean isEditAll(MeetingEntity meetingEntity, Short fromType) {
        Short status = meetingEntity.getStatus();
        return (fromType.equals(FROM_TYPE_MEETING) || (
                status.equals(MeetingCanstant.MEETING_STATUS_APPROVAL_NOT_PASS.shortValue())
                        || status.equals(MeetingCanstant.MEETING_STATUS_SOON_START.shortValue())
                        || status.equals(MeetingCanstant.MEETING_STATUS_APPROVAL.shortValue())
                        || status.equals(MeetingCanstant.MEETING_STATUS_CANCEL.shortValue())
                        || status.equals(MeetingCanstant.MEETING_STATUS_BACK.shortValue())
                        || status.equals(MeetingCanstant.MEETING_STATUS_REVOKE.shortValue())));
    }

    /**
     * 校验是更新参会人员信息 纪实结果编辑 已举办的活动或纪实结果添加的活动
     */
    private boolean isReportUpdateUsers(MeetingEntity meetingEntity, Short fromType) {
        return (fromType.equals(FROM_TYPE_REPORT)
                && meetingEntity.getAddType().equals(FROM_TYPE_REPORT)) // 直接填报纪实结果添加的活动 可直接修改人员信息
                || (fromType.equals(FROM_TYPE_REPORT)
                && meetingEntity.getAddType().equals(FROM_TYPE_MEETING)
                && meetingEntity.getStartTime().getTime() <= DateTime.now().toDate().getTime());
    }

    /**
     * 校验是否是活动管理添加人员
     * 活动管理编辑 会待举办
     */
    private boolean isMeetingAddUsers(MeetingEntity meetingEntity, Short fromType) {
        Short status = meetingEntity.getStatus();
        return fromType.equals(FROM_TYPE_MEETING) && status.equals(MeetingCanstant.MEETING_STATUS_SOON_START.shortValue()) && meetingEntity.getStartTime().getTime() > DateTime.now().toDate().getTime();
    }

    /**
     * 更新人员信息
     */
    private int upMeetingUsers(MeetingEntity upMeetingEntity, Short fromType, MeetingEntity oldMeetingEntity) {
        List<MeetingUserEntity> upMeetingUserEntities = new ArrayList<>();// 更新
        // 纪实更新所有人的考勤
        if ((fromType == FROM_TYPE_REPORT && oldMeetingEntity.getIsSignIn() == 0) || oldMeetingEntity.getAddType().equals(FROM_TYPE_REPORT)) {
            //更新参会人员和列席人员
            List<MeetingUserEntity> upParticipantUsers = upMeetingEntity.getParticipantUsers().stream()
                    .filter(meetingUserEntity -> meetingUserEntity.getMeetingUserId() != null && oldMeetingEntity.getParticipantUsers().stream().anyMatch(ome -> ome.getMeetingUserId().equals(meetingUserEntity.getMeetingUserId()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(upMeetingEntity.getAttendUsers())) {
                List<MeetingUserEntity> upAttendUsers = upMeetingEntity.getAttendUsers().stream()
                        .filter(meetingUserEntity -> meetingUserEntity.getMeetingUserId() != null && oldMeetingEntity.getAttendUsers().stream().anyMatch(ome -> ome.getMeetingUserId().equals(meetingUserEntity.getMeetingUserId()))).collect(Collectors.toList());
                upMeetingUserEntities.addAll(upParticipantUsers);
                upMeetingUserEntities.addAll(upAttendUsers);
            }
        } else if (fromType == FROM_TYPE_REPORT && oldMeetingEntity.getIsSignIn() == 1) {// 纪实更新添加人的考勤
            //更新参会人员和列席人员
            List<MeetingUserEntity> upParticipantUsers = upMeetingEntity.getParticipantUsers().stream()
                    .filter(meetingUserEntity -> meetingUserEntity.getMeetingUserId() != null
                            && oldMeetingEntity.getParticipantUsers().stream()
                            .anyMatch(ome -> ome.getMeetingUserId().equals(meetingUserEntity.getMeetingUserId()) && meetingUserEntity.getIsAdd().equals(MeetingUserEntity.REPORT_ADD))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(upMeetingEntity.getAttendUsers())) {
                List<MeetingUserEntity> upAttendUsers = upMeetingEntity.getAttendUsers().stream()
                        .filter(meetingUserEntity -> meetingUserEntity.getMeetingUserId() != null
                                && oldMeetingEntity.getAttendUsers().stream()
                                .anyMatch(ome -> ome.getMeetingUserId().equals(meetingUserEntity.getMeetingUserId()) && meetingUserEntity.getIsAdd().equals(MeetingUserEntity.REPORT_ADD))).collect(Collectors.toList());
                upMeetingUserEntities.addAll(upAttendUsers);
            }
            upMeetingUserEntities.addAll(upParticipantUsers);
        }
        upMeetingUserEntities.forEach(meetingUserMapper::updateByPrimaryKeySelective);
        return upMeetingUserEntities.size();
    }

    /**
     * 添加参会人员 已有的userId 不能添加
     * 删除主持人和记录人重新添加
     *
     * @param oldMeetingEntity 原来的活动信息 为null表示第一次添加
     */
    private int addMeetingUsers(HttpHeaders headers, MeetingEntity upMeetingEntity, MeetingEntity oldMeetingEntity, Short fromType) {
        // 会议参与所有用户
        List<MeetingUserEntity> meetingUserEntities = new ArrayList<>();
        // 删除记录人
        this.delRecordUserByMid(upMeetingEntity.getMeetingId());
        // 新增记录人
        List<MeetingUserEntity> recordUser = upMeetingEntity.getRecordUser();
        boolean flag = oldMeetingEntity == null;
        checkUser(recordUser, MeetingUserEntity.TAG_RECORD);
        if (CollectionUtils.isNotEmpty(recordUser)) {
            meetingUserEntities.addAll(recordUser);
        }

        // 删除主持人
        this.delHostUserByMid(upMeetingEntity.getMeetingId());
        // 新增主持人
        List<MeetingUserEntity> hostUser = upMeetingEntity.getHostUser();
        checkUser(hostUser, MeetingUserEntity.TAG_HOST);
        if (CollectionUtils.isNotEmpty(hostUser)) {
            meetingUserEntities.addAll(hostUser);
        }

        // 删除讲课人
        this.delLecturerByMid(upMeetingEntity.getMeetingId());
        // 新增讲课人
        List<MeetingUserEntity> lecturers = upMeetingEntity.getLecturers();
        if (CollectionUtils.isNotEmpty(lecturers)) {
            lecturers.forEach(user -> {
                user.setTag(MeetingUserEntity.TAG_LECTURER);
            });
            meetingUserEntities.addAll(lecturers);
        }

        // 参会人员
        List<MeetingUserEntity> addParticipantUsers = new ArrayList<>();
        // 列席人员
        List<MeetingUserEntity> addAttendUsers = new ArrayList<>();
        if (oldMeetingEntity != null) {
            if (upMeetingEntity.getParticipantUsers() != null) {
                for (MeetingUserEntity user : upMeetingEntity.getParticipantUsers()) {
                    if (user.getMeetingUserId() == null && (user.getUserId() == null || user.getUserId() < 0
                            || oldMeetingEntity.getParticipantUsers().stream().noneMatch(me -> user.getUserId().equals(me.getUserId())))) {
                        if (user.getUserId() != null && user.getUserId() < 0) {
                            user.setUserId(null);// UserId 为负值，表示手动录入人员，存入数据库设置为null
                        }
                        addParticipantUsers.add(user);
                    }
                }
            }
            if (upMeetingEntity.getAttendUsers() != null) {
                for (MeetingUserEntity user : upMeetingEntity.getAttendUsers()) {
                    if (user.getMeetingUserId() == null && (user.getUserId() == null || user.getUserId() < 0
                            || oldMeetingEntity.getAttendUsers().stream().noneMatch(me -> user.getUserId().equals(me.getUserId())))) {
                        if (user.getUserId() != null && user.getUserId() < 0) {
                            user.setUserId(null);// UserId 为负值，表示手动录入人员，存入数据库设置为null
                        }
                        addAttendUsers.add(user);
                    }
                }
            }
        } else {
            addParticipantUsers = upMeetingEntity.getParticipantUsers();
            addAttendUsers = upMeetingEntity.getAttendUsers();
        }
        // 参会人员
        if (addParticipantUsers != null && !addParticipantUsers.isEmpty()) {
            addParticipantUsers.forEach(
                    user -> {
                        user.setTag(MeetingUserEntity.TAG_PART);
                        if (user.getMeetingUserId() == null && (user.getUserId() == null || user.getUserId() < 0
                                || flag || oldMeetingEntity.getParticipantUsers().stream().noneMatch(me -> user.getUserId().equals(me.getUserId())))) {
                            // UserId 为负值，表示手动录入人员，存入数据库设置为null
                            if (user.getUserId() != null && user.getUserId() < 0) {
                                user.setUserId(null);
                            }
                        }
                    }
            );
            this.checkRepUser(addParticipantUsers, "参会人员");
            meetingUserEntities.addAll(addParticipantUsers);
        }
        // 列席人员
        if (addAttendUsers != null && !addAttendUsers.isEmpty()) {
            this.checkRepUser(addAttendUsers, "列席人员");
            addAttendUsers.forEach(meetingUserEntity -> meetingUserEntity.setTag(MeetingUserEntity.TAG_ATTEND));
            meetingUserEntities.addAll(addAttendUsers);
        }
        // 换取用户头像
        Map<Long, List<MeetingUserEntity>> userMap = new HashMap<>();
        meetingUserEntities.forEach(t -> {
            if (t.getUserId() != null && t.getUserId() >= 1L) {
                if (!userMap.containsKey(t.getUserId())) {
                    userMap.put(t.getUserId(), new ArrayList<>());
                }
                userMap.get(t.getUserId()).add(t);
            }
        });
        List<UserHead> userHeadUrls = getUserHeadUrl(headers, userMap.keySet());
        if (CollectionUtils.isNotEmpty(userHeadUrls)) {
            userHeadUrls.forEach(u -> {
                userMap.get(u.getUserId()).forEach(t -> {
                    t.setHeadUrl(u.getHeadUrl());
                });
            });
        }
        return this.addMeetingUsers(upMeetingEntity, fromType, meetingUserEntities);
    }

    /**
     * 获取用户头像信息
     *
     * @param headers
     * @param userIds
     * @return
     */
    private List<UserHead> getUserHeadUrl(HttpHeaders headers, Collection<Long> userIds) {
        String url = String.format("http://%s/user-org/get-user-third-info?th_type=4&user_ids=" + StringUtils.join(userIds, ","), userCenter);
        try {
            log.debug("远程调用开始");
            return RemoteApiHelper.get(restTemplate, url, headers, new TypeReference<Result<List<UserHead>>>() {
            });
        } catch (Exception e) {
            log.error("远程调用用户中心，获取用户头像信息失败", e);
            return null;
        }
    }

    private void checkUser(List<MeetingUserEntity> list, short tag) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(user -> {
            user.setTag(tag);
            if (/*说明是新增的情况*/user.getMeetingUserId() == null) {
                // UserId 为负值，表示手动录入人员，存入数据库设置为null
                if (user.getUserId() != null && user.getUserId() < 0) {
                    user.setUserId(null);
                }
            }
        });
    }

    /**
     * 人员是否重复
     */
    private void checkRepUser(List<MeetingUserEntity> meetingUserEntities, String tagStr) {
        if (meetingUserEntities != null && !meetingUserEntities.isEmpty() && meetingUserEntities.stream().anyMatch(meetingUserEntity -> meetingUserEntities.stream()
                .filter(me -> me.getUserId() != null && me.getUserId() > 0 && me.getUserId().equals(meetingUserEntity.getUserId())).count() > 1)) {
            throw new ApiException("add meeting user error ", new Result<>(errors, 1808, HttpStatus.BAD_REQUEST.value(), tagStr));
        }
    }

    /**
     * 删除活动记录人
     *
     * @param mid 活动id
     */
    private int delRecordUserByMid(long mid) {
        //删除原有记录人
        Example example = new Example(MeetingUserEntity.class);
        example.createCriteria().andEqualTo("meetingId", mid).andEqualTo("tag", MeetingUserEntity.TAG_RECORD);
        return meetingUserMapper.deleteByExample(example);
    }

    /**
     * 删除活动主持人
     *
     * @param mid 活动id
     */
    private int delHostUserByMid(long mid) {
        //删除原有记录人
        Example example = new Example(MeetingUserEntity.class);
        example.createCriteria().andEqualTo("meetingId", mid).andEqualTo("tag", MeetingUserEntity.TAG_HOST);
        return meetingUserMapper.deleteByExample(example);
    }

    /**
     * 删除活动讲课人
     *
     * @param mid 活动id
     */
    private int delLecturerByMid(long mid) {
        //删除原有记录人
        Example example = new Example(MeetingUserEntity.class);
        example.createCriteria().andEqualTo("meetingId", mid).andEqualTo("tag", MeetingUserEntity.TAG_LECTURER);
        return meetingUserMapper.deleteByExample(example);
    }

    /**
     * 更新活动审批状态
     */
    @Transactional
    public int updateMeeting(MeetingEntity meetingEntity) {
        return meetingMapper.updateByPrimaryKeySelective(meetingEntity);
    }

    /**
     * 更新审批状态
     *
     * @param mid    活动id
     * @param tid    审批流id
     * @param userId 审批用户
     * @param status 1.审批通过；2.审批被打回；3.审批被撤销,
     * @param reason 打回原因
     * @param reUid  打回人
     */
    public void updateStatusByWorkflow(long mid, long tid, Long userId, int status, String reason, Long reUid) {
        MeetingEntity me = new MeetingEntity();
        me.setMeetingId(mid);
        me.setWorkflowTaskId(tid);
        me.setUpdateTime(DateTime.now().toDate());
        if (status == 1) {//审批通过
            me.setStatus(MeetingCanstant.MEETING_STATUS_SOON_START.shortValue());
            me.setLastChangeUser(userId);
            //审批通过记录
            this.addMeetingStartHistory(me);
        } else {//审批未通过
            me.setStatus(MeetingCanstant.MEETING_STATUS_APPROVAL_NOT_PASS.shortValue());
            me.setLastChangeUser(reUid);
            //审批未通过
            MeetingHistoryEntity meetingHistoryEntity = this.buildMeetingHistoryEntity(me);
            meetingHistoryEntity.setCreateUser(reUid);
            meetingHistoryEntity.setReason(reason);
            meetingHistoryMapper.insert(meetingHistoryEntity);
            //补学回退逻辑
            meetingWaitSignService.backSignInfo(mid, null);
        }
        this.updateMeeting(me);
    }

    /**
     * 更新活动状态
     */
    void updateMeetingStatus(Short status, Long meetingId, Long uid) {
        MeetingEntity meeting = new MeetingEntity();
        meeting.setLastChangeUser(uid);
        meeting.setUpdateTime(DateTime.now().toDate());
        meeting.setStatus(status);
        meeting.setMeetingId(meetingId);
        this.meetingMapper.updateByPrimaryKeySelective(meeting);
    }

    /**
     * 活动待举办历史记录
     * 同时添加待填写记录
     */
    private void addMeetingStartHistory(MeetingEntity me) {
        //活动待举办
        MeetingHistoryEntity mh1 = this.buildMeetingHistoryEntity(me);
        meetingHistoryMapper.insert(mh1);

        //判断是否已经有待开始的记录
        Example example = new Example(MeetingHistoryEntity.class);
        example.createCriteria().andEqualTo("meetingId", me.getMeetingId()).andEqualTo("status", MeetingCanstant.MEETING_STATUS_APPROVAL);
        MeetingHistoryEntity mh2 = meetingHistoryMapper.selectOneByExample(example);
        if (mh2 == null) {
            mh2 = new MeetingHistoryEntity();
            mh2.setMeetingId(me.getMeetingId());
            mh2.setStatus(MeetingCanstant.MEETING_STATUS_APPROVAL);
            mh2.setCreateUser(me.getLastChangeUser() == null ? me.getCreateUser() : me.getLastChangeUser());
            mh2.setCreateTime(me.getStartTime());// 待填写更新时间为活动开始时间
            mh2.setReason(DateUtils.dateFormat(me.getStartTime(), "yyyy-MM-dd HH:mm") + StringCanstant.ACTIVITY + StringCanstant.HOLD);//日期格式化
            meetingHistoryMapper.insert(mh2);
        }

    }

    /**
     * 取消活动历史记录
     */
    private void addCancelMeetingHistory(MeetingEntity me) {
        MeetingHistoryEntity mh = this.buildMeetingHistoryEntity(me);
        meetingHistoryMapper.insert(mh);
    }

    /**
     * 构建历史状态实体
     *
     * @param me 活动信息
     * @return MeetingHistoryEntity
     */
    private MeetingHistoryEntity buildMeetingHistoryEntity(MeetingEntity me) {
        MeetingHistoryEntity meetingHistoryEntity = new MeetingHistoryEntity();
        meetingHistoryEntity.setMeetingId(me.getMeetingId());
        meetingHistoryEntity.setStatus(me.getStatus().intValue());
        meetingHistoryEntity.setCreateTime(me.getUpdateTime() == null ? me.getCreateTime() : me.getUpdateTime());
        meetingHistoryEntity.setCreateUser(me.getLastChangeUser() == null ? me.getCreateUser() : me.getLastChangeUser());
        return meetingHistoryEntity;
    }

    /**
     * 参数校验
     *
     * @param meetingEntity 参数
     * @param fromType      编辑来源：1.活动管理页面添加 2.记实情况页面添加
     */
    private void checkAddParam(HeaderHelper.SysHeader sysHeader, MeetingEntity meetingEntity, List<MeetingTaskEntity> meetingTaskEntities, Short fromType, Boolean hasGroup) {
        log.debug("查看消息头是否有传递=>{}", sysHeader);
        /**
         * 验证消息头数据是否合法
         * tc 2021-10-25
         */
        if (sysHeader.getOid() < 0 || sysHeader.getRegionId() < 0 || sysHeader.getUserId() < 0 || StringUtils.isEmpty(sysHeader.getOrgName()) || "-1".equals(sysHeader.getOrgName())) {
            throw new ApiException("消息头参数不正确!", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "消息头参数不正确"));
        }
        /*
         *  1.举办时间在当前时间之后
         *  2.所选活动类型是否存在 类型组合是否允许
         *  2.1 类型是否必须选择党支部或支委会届次
         *  3.所选任务是否存在
         *  4.签到时间校验
         *  5.是否签到、填写决议是否设置正确
         */
        if (meetingEntity.getStartTime() == null) {
            throw new ApiException("startTime is null", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "活动开始时间未设置"));
        }
        meetingEntity.setStartTime(DateUtils.set0Second(meetingEntity.getStartTime()));
        if (fromType == FROM_TYPE_MEETING) {
            if (DateTime.now().toDate().getTime() > meetingEntity.getStartTime().getTime()) {
                throw new ApiException("now > startTime ", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "活动开始时间在当前时间之前"));
            }
        }

        checkSelContactLeaders(meetingEntity);

        // hasLecturer hasLectureTitle 默认值
        meetingEntity.setHasLecturer(0);
        meetingEntity.setHasLectureTitle(0);
        if (hasGroup) {
            meetingEntity.setIsWResolution((short) 0);
            return;
        }
        meetingEntity
                .getMeetingTypes()
                .forEach(
                        meetingTypeEntity -> {
                            log.debug("mt:" + meetingTypeEntity.getMeetingTaskId());
                            log.debug("mt_meetingTaskEntities:" + meetingTaskEntities);
                            MeetingTaskEntity meetingTaskEntity =
                                    meetingTaskEntities
                                            .stream()
                                            .filter(
                                                    me -> me.getMeetingTaskId().equals(meetingTypeEntity.getMeetingTaskId()))
                                            .findFirst()
                                            .orElse(null);
                            if (meetingTaskEntity == null) {
                                throw new ApiException(
                                        "没有选择的活动类型或活动类型不在有效期内",
                                        new Result<>(
                                                errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), "类型"));
                            }
                            meetingTypeEntity.setTypeId(meetingTaskEntity.getTypeId());
                            meetingTypeEntity.setType(meetingTaskEntity.getType());
                            meetingTypeEntity.setCategoryId(meetingTaskEntity.getCategoryId());
                            meetingTypeEntity.setCategory(meetingTaskEntity.getCategory());
                            // 必须选择党小组
                            if (meetingTaskEntity.getCode().equals(RuleTypeEnum.HAS_ORG_GROUP.getType())
                                    && CollectionUtils.isEmpty(meetingEntity.getOrgGroups())) {
                                // 必须选择党小组
                                throw new ApiException("必须选择党小组", new Result<>(errors, 1837, HttpStatus.BAD_REQUEST.value()));
                            }
                            // 必须选择届次
                            if (meetingTaskEntity.getCode().equals(RuleTypeEnum.HAS_ORG_PERIOD.getType())
                                    && CollectionUtils.isEmpty(meetingEntity.getOrgPeriods())) {
                                // 必须选择支委会届次
                                throw new ApiException("必须选择支委会届次", new Result<>(errors, 1838, HttpStatus.BAD_REQUEST.value()));
                            }
                            // 需要填写讲课人
                            if (meetingTaskEntity.getHasLecturer().equals(Constant.YES)) {
                                meetingEntity.setHasLecturer(Constant.YES);
                                if (CollectionUtils.isEmpty(meetingEntity.getLecturers())) {
                                    throw new ApiException("需要填写讲课人", new Result<>(errors, 2007, HttpStatus.BAD_REQUEST.value()));
                                }
                            }
                            // 需要填写讲课标题
                            if (meetingTaskEntity.getHasLectureTitle().equals(Constant.YES)) {
                                // 需要填写讲课标题
                                meetingEntity.setHasLectureTitle(Constant.YES);
                                if (StringUtils.isBlank(meetingEntity.getLectureTitle())) {
                                    throw new ApiException("需要填写讲课标题", new Result<>(errors, 2008, HttpStatus.BAD_REQUEST.value()));
                                }
                            }

                        });

        // 组合
        if (meetingTaskEntities.size() > 1) {
            // 2019年1月14日 09:35:56  允许跨组织生活类型 必须为同一来源
            if (meetingTaskEntities.stream().map(MeetingTaskEntity::getPOrgId).distinct().count() > 1) {
                throw new ApiException("所选类型不属于同一" + StringCanstant.SOURCE, new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "所选类型不属于同一" + StringCanstant.SOURCE));
            }
            //是否为同一类别
            if (meetingTaskEntities.stream().map(MeetingTaskEntity::getCategoryId).distinct().count() > 1) {
                throw new ApiException("所选类型不属于同一类别", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "所选类型不属于同一类别"));
            }
            List<Long> typeIds = meetingTaskEntities.stream().map(MeetingTaskEntity::getTypeId).distinct().sorted().collect(Collectors.toList());
            log.debug("mt查询：" + meetingTaskEntities);
            log.debug("mt查询：" + typeIds);
            if (!existTypeGroup(sysHeader, typeIds)) {
                throw new ApiException("所选类型不能组合", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "所选类型不能组合"));
            }

        }
        meetingEntity.setPOrgId(meetingTaskEntities.get(0).getPOrgId());// 派发者所属组织

        //任务
        //校验meetingEntity.getTopics() 是否为null isEmpty
//        if (meetingEntity.getTopics() == null || meetingEntity.getTopics().isEmpty()) {
//            //任务不能为空
//            throw new ApiException("活动内容不能为空", new Result<>(errors, 1954, HttpStatus.BAD_REQUEST.value()));
//        }
        /*if (CollectionUtils.isNotEmpty(meetingEntity.getTopics())) {
            List<Long> topicIds = meetingEntity.getTopics().stream().filter(mt -> !mt.getIsNewAdd()).map(MeetingTopicEntity::getTopicId).distinct().collect(Collectors.toList());
            List<TopicEntity> topicEntities = topicService.findByOrgIdAndIds(sysHeader.getOid(), topicIds);
            meetingEntity.getTopics().forEach(topicOrgEntity -> {
                if (!topicOrgEntity.getIsNewAdd()) {
                    TopicEntity topicEntity = topicEntities.stream().filter(te -> te.getTopicId().equals(topicOrgEntity.getTopicId())).findFirst().orElse(null);
                    if (topicEntity == null) {
                        throw new ApiException("没有选择的任务或任务不在有效期内", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), StringCanstant.TASK));
                    }
                    topicOrgEntity.setTopicName(topicEntity.getName());
                }
            });
        }*/

        // 是否需要签到
        if (meetingTaskEntities.stream().anyMatch(meetingTaskEntity -> meetingTaskEntity.getMeetingRequireEntity().getIsSignIn() == 1)) {
            // 必须要签到
            if (meetingEntity.getIsSignIn() == 0) {
                throw new ApiException("活动必须设置为需要签到", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), StringCanstant.ACTIVITY + "必须设置为需要签到"));
            }
        }
        // 需要签到
//        if (meetingEntity.getIsSignIn() == 1) {
//            // 定位信息
//            if (meetingEntity.getGpsType() == null || meetingEntity.getLat() == null || meetingEntity.getLng() == null) {
//                throw new ApiException("GPS信息不全", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "GPS信息不全"));
//            }
//            // 时间校验
//            if (meetingEntity.getSignStartTime() == null || meetingEntity.getSignEndTime() == null) {
//                throw new ApiException("signStartTime or signEndTime is null", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "签到开始时间或结束时间未设置"));
//            }
//            meetingEntity.setSignStartTime(DateUtils.set0Second(meetingEntity.getSignStartTime()));
//            meetingEntity.setSignEndTime(DateUtils.set59Second(meetingEntity.getSignEndTime()));
//            if (meetingEntity.getSignStartTime().getTime() > meetingEntity.getSignEndTime().getTime()) {
//                throw new ApiException("now > signEndTime ", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "签到结束时间在签到开始时间之前"));
//            }
//
//            if (DateTime.now().toDate().getTime() > meetingEntity.getSignEndTime().getTime() && fromType == FROM_TYPE_MEETING) {
//                throw new ApiException("now > signEndTime ", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "签到结束时间在当前时间之前"));
//            }
//        }
        // 2018-12-26 17:55:06 不校验
        //是否填写决议 默认为0 不需要
        meetingEntity.setIsWResolution((short) 0);
//        if (meetingTaskEntities.stream().anyMatch(meetingTaskEntity -> meetingTaskEntity.getMeetingRequireEntity().getIsWResolution() == 1)) {
//            // 必须要填写决议
//            if (meetingEntity.getIsWResolution() == 0) {
//                throw new ApiException("活动必须设置为需要填写决议", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "活动必须设置为需要填写" + StringCanstant.RESOLUTION));
//            }
//        }
    }

    private void checkSelContactLeaders(MeetingEntity meetingEntity) {
        if (meetingEntity.getSelContactLeaders().equals(1) && CollectionUtils.isEmpty(meetingEntity.getContactLeaders())) {
            throw new ApiException("联系领导不能为空", new Result<>(errors, 1804, HttpStatus.BAD_REQUEST.value(), "联系领导不能为空"));
        }
    }

    private boolean existTypeGroup(HeaderHelper.SysHeader sysHeader, List<Long> typeIds) {
        if (typeIds == null || typeIds.stream().distinct().count() < 2) {
            return true;
        }
        //查询组合
        List<GroupEntity> groupEntities = groupService.listAllGroupEntity(sysHeader);
        List<List<Long>> groupAll = groupEntities.stream().map(ge -> ge.getTypes().stream().map(TypeEntity::getTypeId).collect(Collectors.toList())).collect(Collectors.toList());
        return groupAll.stream().anyMatch(group -> group.containsAll(typeIds));
    }

    /**
     * 签到
     *
     * @param meetingId
     * @return
     */
    public List<MeetingUserEntity> signList(Long meetingId) {
        return meetingUserMapper.signList(meetingId);
    }


    /**
     * 二维码
     *
     * @param meetingId
     * @param isDynamic
     * @param allowAllSign
     * @param response
     * @return
     * @throws IOException
     * @throws WriterException
     */
    @Transactional(rollbackFor = Exception.class)
    public QrCodeForm signQrCod(Long meetingId, Integer isDynamic, Integer allowAllSign, HttpServletResponse response) throws IOException, WriterException {
        MeetingEntity meetingEntity = meetingMapper.selectByPrimaryKey(meetingId);
        if (meetingEntity == null) {
            throw new ApiException(
                    "组织生活不存在",
                    new Result<>(
                            errors,
                            Global.Errors.NOT_FOUND,
                            HttpStatus.NOT_FOUND.value(),
                            StringCanstant.ORG_LIFE));
        }
        JSONObject data = new JSONObject();
        boolean update = false;
        if (isDynamic != 1) {
            if (!allowAllSign.equals(-1) && !allowAllSign.equals(meetingEntity.getAllowAllSign())) {
                data.set("hash", IdUtil.randomUUID());
                update = true;
            } else {
                if (StringUtils.isNotBlank(meetingEntity.getQrCodeHash())) {
                    data.set("hash", meetingEntity.getQrCodeHash());
                } else {
                    data.set("hash", IdUtil.randomUUID());
                    update = true;
                }
            }
        } else {
            data.set("hash", IdUtil.randomUUID());
            //把hash放入缓存，并在二维码图片生成后设置过期时间
            String key = DYNAMIC_KEY + data.getStr("hash");
            stringRedisTemplate.opsForValue().set(key, meetingId.toString());
        }
        //如果不是动态二维码，对二维码更新后需要修改数据库
        if (update) {
            MeetingEntity updateMeeting = new MeetingEntity();
            updateMeeting.setMeetingId(meetingId);
            updateMeeting.setQrCodeHash(data.getStr("hash"));
            if (allowAllSign.equals(-1)) {
                updateMeeting.setAllowAllSign(1);
            } else {
                updateMeeting.setAllowAllSign(allowAllSign);
            }
            meetingMapper.updateByPrimaryKeySelective(updateMeeting);
        }

        // 生成二维码
        String param = URLUtil.encodeAll("/ssr/sweep-code-sign-in?qr_code_hash=" + data.getStr("hash") + "&is_dynamic=" + isDynamic);
        String qrCode = ZxingUtil.QREncode(signQrCodeUrl + param, 300, 300, "png");
        //如果是动态码，设置过期时间(避免生成二维码耗时影响)
        if (isDynamic == 1) {
            String key = DYNAMIC_KEY + data.getStr("hash");
            stringRedisTemplate.expire(key, DYNAMIC_EXPIRE, TimeUnit.MILLISECONDS);
        }
        // 返回二维码
        QrCodeForm qcf = new QrCodeForm(meetingId, qrCode, data.getStr("hash"), DYNAMIC_EXPIRE, isDynamic);

        //将isDynamic通过hash放入redis，因为中烟存在多层路由，值会丢失
        stringRedisTemplate.opsForValue().set(data.getStr("hash"),isDynamic.toString());
        return qcf;
    }

    /**
     * 活动签到
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean signIn(HttpHeaders headers, Headers header, MeetingSignInForm meetingSignInForm) {
        String hash = meetingSignInForm.getQrCodeHash();
        Integer isDynamic = meetingSignInForm.getIsDynamic();
        Example example = new Example(MeetingEntity.class);

        //此处特殊处理，如果isDynamic值在多层路由中丢失，则去redis中获取
        if(isDynamic == null){
            String isDynamicStr = stringRedisTemplate.opsForValue().get(hash);
            if(StringUtils.isNotEmpty(isDynamicStr)){
                isDynamic = Integer.parseInt(isDynamicStr);
            }
        }
        if (isDynamic == 0) {
            //非动态二维码
            example.createCriteria().andEqualTo("qrCodeHash", hash);
        } else {
            //动态二维码，先从缓存里获取活动编号，如果缓存失效获取不到，就返回二维码过期错误
            String key = DYNAMIC_KEY + hash;
            if (stringRedisTemplate.hasKey(key)) {
                String meetingId = stringRedisTemplate.opsForValue().get(key);
                example.createCriteria().andEqualTo("meetingId", meetingId);
            } else {
                //动态签到二维码已失效
                throw new ApiException("签到二维码已失效", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "签到二维码已失效"));
            }
        }

        MeetingEntity oldMeetingEntity = meetingMapper.selectOneByExample(example);
        if (oldMeetingEntity == null) {
            throw new ApiException("活动未找到", new Result(errors, 9404, HttpStatus.OK.value(), StringCanstant.ACTIVITY));
        }
        if (oldMeetingEntity.getIsSignIn() == 0 || oldMeetingEntity.getSignInWay() == 0) {
            throw new ApiException("该活动不需要签到", new Result(errors, 1830, HttpStatus.OK.value()));
        }
        long meetingId = oldMeetingEntity.getMeetingId();
        long userId = header.getUserId();
        long orgId = header.getOid();
        String orgName = header.getOrgName();
        String userName = header.getUserName();

        // 一个用户可能有多个角色，需要签到的只是参与人员这个角色
        String signHash = SecureUtil.md5().digestHex(meetingId + "_" + userId);
        // 判断是不是邀请列表外的用户重复签到
        example = new Example(MeetingUserEntity.class);
        example.createCriteria()
                .andEqualTo("meetingId", meetingId)
                .andEqualTo("signHash", signHash);
        MeetingUserEntity signMeetingUser = meetingUserMapper.selectOneByExample(example);
        if (signMeetingUser != null) {
            throw new ApiException("请勿重复签到", new Result(errors, 1832, HttpStatus.OK.value()));
        }

        // 判断是不是参会人员重复签到
        example = new Example(MeetingUserEntity.class);
        example.createCriteria()
                .andEqualTo("meetingId", meetingId)
                .andEqualTo("userId", userId);
        List<MeetingUserEntity> meetingUserEntity = meetingUserMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(meetingUserEntity)) {
            // 是否允许非邀请人员进行签到？
            if (oldMeetingEntity.getAllowAllSign().equals(1)) {
                MeetingUserEntity temp = new MeetingUserEntity();
                temp.setMeetingId(meetingId);
                temp.setIsAdd(MeetingUserEntity.MEETING_ADD);
                temp.setSignStatus((short) 1);
                temp.setSignHash(signHash);
                temp.setSignTime(new Date());
                temp.setTag(MeetingUserEntity.TAG_ATTEND);

                List<UserHead> userHeadUrls = getUserHeadUrl(headers, Arrays.asList(userId));
                if (CollectionUtils.isNotEmpty(userHeadUrls)) {
                    UserHead userHead = userHeadUrls.get(0);
                    temp.setUserName(userHead.getNickName());
                    temp.setHeadUrl(userHead.getHeadUrl());
                }
                // 如果当前用户在当前组织内则加入当前组织用户中
                // 判断组织关系
                List<UserOrg> userOrgList = getUserOrgList(headers, userId);
                if (!CollectionUtils.isEmpty(userOrgList)) {
                    for (UserOrg userOrg : userOrgList) {
                        if (userOrg.getOrgId().equals(oldMeetingEntity.getOrgId())) {
                            temp.setOrgId(userOrg.getOrgId());
                            temp.setOrgName(userOrg.getOrgName());
                            temp.setUserId(userId);
                            break;
                        }
                    }
                }
                // 如果当前用户在当前组织内则加入当前组织用户中，则以当前登录的组织身份加入
                if (temp.getOrgId() == null) {
                    temp.setOrgId(orgId);
                }
                if (temp.getUserId() == null) {
                    temp.setUserId(userId);
                }
                if (temp.getOrgName() == null) {
                    temp.setOrgName(orgName);
                }
                if (temp.getUserName() == null) {
                    temp.setUserName(userName);
                }
                meetingUserMapper.insert(temp);
                return true;
            } else {
                throw new ApiException("该活动不允许签到签到列表之外的用户签到", new Result(errors, 1860, HttpStatus.OK.value()));
            }
        }

        // 按角色分组，获取参会人员角色
        Map<Short, MeetingUserEntity> map = meetingUserEntity.stream().collect(Collectors.toMap(MeetingUserEntity::getTag, t -> t));
        if (!map.containsKey(MeetingUserEntity.TAG_PART)) {
            throw new ApiException("该活动不需要签到", new Result(errors, 1830, HttpStatus.OK.value()));
        } else {
            // 已签到
            if (map.get(MeetingUserEntity.TAG_PART).getSignStatus() == 1) {
                throw new ApiException("请勿重复签到", new Result(errors, 1832, HttpStatus.OK.value()));
            }
        }

        // 更新签到状态并将reason重置为null
        meetingUserMapper.updateMeetingUser(1, null, meetingId, userId);

        // 签到成功后，判断是否存在请假，如果存在未通过或已通过的请假则将之前请假改为已撤销
        meetingLeaveService.signInAfterCancelLeave(meetingId, userId);

        if (StringUtils.isNotEmpty(oldMeetingEntity.getDingEventId())) {
            //签到成功后，同步签到状态到钉钉日程
            try {
                //获取用户对应钉钉的编号
                List<UserThirdInfoVO> userThirdInfo = orgUserDingTalkService.getUserThirdInfo(restTemplate, togServicesConfig.getUserCenter(), header.getRegionId(), userId);
                if (userThirdInfo != null && userThirdInfo.size() > 0) {
                    String unionId = Objects.requireNonNull(userThirdInfo).get(0).getOpenId();
                    if (StringUtils.isNotEmpty(unionId)) {
                        GetSignInRequest gsir = new GetSignInRequest();
                        gsir.setUnionId(unionId);
                        gsir.setEventId(oldMeetingEntity.getDingEventId());
                        scheduleService.checkInSchedule(gsir);
                    } else {
                        log.error("<签到成功后，同步签到状态到钉钉日程>失败！ 用户中心返回的unionId为空 meetingId={} userId={} userThirdInfo={} ", meetingId, userId, JsonUtils.toJson(userThirdInfo));
                    }
                } else {
                    log.error("<签到成功后，同步签到状态到钉钉日程>失败！ 用户中心返回的userThirdInfo为空 meetingId={} userId={}", meetingId, userId);
                }
            } catch (Exception e) {
                log.error("签到成功后，同步签到状态到钉钉日程 失败！ meetingId={} userId={}", meetingId, userId, e);
            }
        }
        return true;
    }

    private List<UserOrg> getUserOrgList(HttpHeaders headers, Long userId) {
        String url = String.format("http://%s/user-org/get-user-org-list?user_id=" + userId, userCenter);
        try {
            log.debug("远程调用开始");
            return RemoteApiHelper.get(restTemplate, url, headers, new TypeReference<Result<List<UserOrg>>>() {
            });
        } catch (Exception e) {
            log.error("远程调用用户中心，获取用户组织信息失败", e);
            return null;
        }
    }

    //日程标题拼接
    private String getDescription(List<MeetingAgendaEntity> agenda) {
        StringBuffer re = new StringBuffer();
        if (agenda != null && agenda.size() > 0) {
            for (int i = 1; i <= agenda.size(); i++) {
                String ti = NumConversionUtils.int2ChineseNumber(i) + "、";
                re.append(ti + agenda.get(i - 1).getAgendaTitle() + "\n");
            }
        }
        return re.toString();
    }

    /**
     * 组织生活删除日志方法
     *
     * @param regionId
     * @param createUserId
     * @param dingEventId
     */
    public void delDingEvent(Long regionId, Long createUserId, String dingEventId, Long meetingId) {
        try {
            /**
             * getUserThirdInfo 方法返回的是一个LIST
             */
            List<UserThirdInfoVO> userThirdInfoList = orgUserDingTalkService.getUserThirdInfo(restTemplate, userCenter, regionId, createUserId);
            if (CollectionUtils.isNotEmpty(userThirdInfoList) && StringUtils.isNotBlank(userThirdInfoList.get(0).getOpenId())) {
                log.debug("删除钉钉日程信息！createUserId={}  dingEventId={}", createUserId, dingEventId);
                scheduleService.deleteSchedule(userThirdInfoList.get(0).getOpenId(), dingEventId);
                //删除日程后修改组织生活字段
                meetingMapper.delDingDingEventInfo(meetingId);
            } else {
                log.error("<删除钉钉日程>失败！ 用户中心返回的OpenId为空 userThirdInfoList={}", JsonUtils.toJson(userThirdInfoList));
            }
        } catch (Exception e) {
            log.error("<删除钉钉日程>失败！ createUserId={}  dingEventId={}", createUserId, dingEventId, e);
        }
    }

    /**
     * 组织生活创建日志方法
     *
     * @param regionId
     * @param createUserId
     * @param meetingEntity
     * @param sendUserIds
     */
    public void createDingEvent(Long regionId, Long createUserId, MeetingEntity meetingEntity, List<Long> sendUserIds) {
        CreateEventResponse cer = null;
        try {
            CreateEventRequest createEventRequest = new CreateEventRequest();
            createEventRequest.setSummary(meetingEntity.getName());
            /**
             * 设置日程描述，使用 议程名称拼接
             * eg:
             * 一、议程名称
             * 二、议程名称
             * 三、议程名称
             */
            String des = this.getDescription(meetingEntity.getAgenda());
            createEventRequest.setDescription(des);
            createEventRequest.setLocation(new CreateEventRequest.CreateEventRequestLocation().setDisplayName(meetingEntity.getAddress()));
            //去用户中心换取钉钉用户编号
            List<UserThirdInfoVO> dingUserList = orgUserDingTalkService.getUserThirdInfo(restTemplate, userCenter, regionId, sendUserIds);
            log.debug("创建日程时去用户中心获取钉钉用户编号结果:size={} dingUserList={}", dingUserList.size(), JsonUtils.toJson(dingUserList));
            List<CreateEventRequest.CreateEventRequestAttendees> list = new ArrayList<>(dingUserList.size());
            dingUserList.forEach(dingUser -> {
                if (StringUtils.isNotEmpty(dingUser.getOpenId())) {
                    CreateEventRequest.CreateEventRequestAttendees createEventRequestAttendees = new CreateEventRequest.CreateEventRequestAttendees();
                    createEventRequestAttendees.setId(dingUser.getOpenId());
                    list.add(createEventRequestAttendees);
                } else {
                    log.error("<创建钉钉日程>失败！ 用户中心返回的OpenId为空 dingUser={}", JsonUtils.toJson(dingUser));
                }
            });
            if (meetingEntity.getNotifyType().equals(1) && StringUtils.isNotBlank(meetingEntity.getNotifyTime())) {
                String[] times = meetingEntity.getNotifyTime().split(",");
                List<CreateEventRequest.CreateEventRequestReminders> reminders = new ArrayList<>(times.length);
                String method;
                if (meetingEntity.getNotifyWay() == 0) {
                    method = "dingtalk";
                } else if (meetingEntity.getNotifyWay() == 1) {
                    method = "sms";
                } else {
                    method = "phone";
                }
                for (String time : times) {
                    CreateEventRequest.CreateEventRequestReminders reminder = new CreateEventRequest.CreateEventRequestReminders();
                    reminder.setMethod(method);
                    if ("0".equals(time)) {
                        reminder.setMinutes(1);
                    } else if ("1".equals(time)) {
                        reminder.setMinutes(5);
                    } else if ("2".equals(time)) {
                        reminder.setMinutes(15);
                    } else if ("3".equals(time)) {
                        reminder.setMinutes(30);
                    } else if ("4".equals(time)) {
                        reminder.setMinutes(60);
                    } else if ("5".equals(time)) {
                        reminder.setMinutes(60 * 24);
                    }
                    reminders.add(reminder);
                }
                createEventRequest.setReminders(reminders);
            }
            createEventRequest.setAttendees(list);
            //修改结束时间为必填属性(已经在参数字段里验证非空)
            createEventRequest.setStart(new CreateEventRequest.CreateEventRequestStart()
                    .setDateTime(DateUtils.dateFormat(meetingEntity.getStartTime(), "yyyy-MM-dd'T'HH:mm:ssXXX"))
                    .setTimeZone("Asia/Shanghai")
            );
            createEventRequest.setEnd(new CreateEventRequest.CreateEventRequestEnd()
                    .setDateTime(DateUtils.dateFormat(meetingEntity.getEndTime(), "yyyy-MM-dd'T'HH:mm:ssXXX"))
                    .setTimeZone("Asia/Shanghai")
            );
            /**
             * getUserThirdInfo 方法返回的是一个LIST
             */
            List<UserThirdInfoVO> userThirdInfoList = orgUserDingTalkService.getUserThirdInfo(restTemplate, userCenter, regionId, createUserId);
            if (CollectionUtils.isNotEmpty(userThirdInfoList) && StringUtils.isNotBlank(userThirdInfoList.get(0).getOpenId())) {
                log.debug("发送日程信息！meetingEntity={}  createEventRequest={}", meetingEntity, JsonUtils.toJson(createEventRequest));
                cer = scheduleService.createSchedule(userThirdInfoList.get(0).getOpenId(), createEventRequest);
            } else {
                log.error("<创建钉钉日程>失败！ 用户中心返回的OpenId为空 userThirdInfoList={}", JsonUtils.toJson(userThirdInfoList));
            }
            /**
             * 如果日程创建成功后，更新meeting表更新日程编号
             * tc 2022-03-01
             */
            if (cer != null && cer.getBody() != null && StringUtils.isNotEmpty(cer.getBody().getId())) {
                Example example = new Example(MeetingEntity.class);
                example.createCriteria()
                        .andEqualTo("meetingId", meetingEntity.getMeetingId());
                MeetingEntity entity = new MeetingEntity();
                entity.setDingEventId(cer.getBody().getId());
                entity.setDingEventSync(0);
                entity.setDingEventCreateUser(createUserId);
                meetingMapper.updateByExampleSelective(entity, example);
            }
        } catch (Exception e) {
            log.error("创建钉钉日程失败", e);
        }
    }

    /**
     * 删除三方任务
     */
    public void delThirdTask(HttpHeaders headers, Long meetingId, List<String> thirdTaskId) {
        try {
            //删除任务
            thirdService.delPending(headers, thirdTaskId);
        } catch (Exception e) {
            log.error("创建任务中心个人任务失败", e);
        }
    }

    /**
     * 创建三方任务
     */
    public void createThirdTask(HttpHeaders headers, Long regionId, Long createUserId, MeetingEntity meetingEntity, List<Long> sendUserIds) {
        /**
         * 创建任务提醒
         * tc 2021-09-26
         *
         *
         * 不管是否勾选通知，都创建任务提醒,并改为调用任务底座
         * tc 2021-11-04
         */
        try {
            //创建任务
            //第三方标签(用 ~~ 分隔)
            String sourceMark = meetingEntity.getTypes().replace(",", "~~");
            String scid = MeetingCanstant.MEETING_PENDING_TASK_KEY + regionId + "_" + meetingEntity.getMeetingId();
            Map<String, Object> uriMap = new HashMap<>();
            uriMap.put("type", "mt");
            uriMap.put("id", meetingEntity.getMeetingId());
            //创建任务系统任务表单
            ThirdAddForm thirdAddForm = ThirdAddForm.builder().beginTime(meetingEntity.getStartTime()).endTime(meetingEntity.getStartTime())
                    .taskTitle(meetingEntity.getName()).sourceId(scid).sourceMark(sourceMark).sourceUri(JsonUtils.toJson(uriMap))
                    .createOrg(meetingEntity.getOrgId()).callback(0).users(sendUserIds).build();
            //创建任务
            CreateTodoForm createTodoForm = CreateTodoForm.builder().thirdAddForm(thirdAddForm).build();
            thirdService.insertPending(headers, regionId, createUserId, 2, 2, createTodoForm);
        } catch (Exception e) {
            log.error("创建任务中心个人任务失败", e);
        }
    }


    /**
     * 活动回顾报表
     *
     * @param meetingListForm
     * @return
     */
    public List<MeetingReportVo> meetingReport(MeetingListForm meetingListForm) {
        List<MeetingReportDto> meeting = meetingMapper.meetingReport(meetingListForm);
        if (CollectionUtils.isEmpty(meeting)) {
            return null;
        }
        int i = 1;
        MeetingReportVo vo;
        List<MeetingReportVo> vos = new ArrayList<>();
        List<MeetingUserEntity> user;
        List<MeetingAgendaEntity> agenda;
        StringBuilder builder = new StringBuilder();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        for (MeetingReportDto dto : meeting) {
            vo = new MeetingReportVo();
            BeanUtils.copyProperties(dto, vo);
            //序号
            vo.setNumber(i);
            i++;
            //时间
            long second;
            vo.setStartTime(Objects.isNull(dto.getStartTime()) ? "" : format.format(dto.getStartTime()));
            if (Objects.isNull(dto.getEndTime())) {
                vo.setEndTime(vo.getStartTime());
                second = 0L;
            } else {
                vo.setEndTime(format.format(dto.getEndTime()));
                if (vo.getStartTime().equals(vo.getEndTime())) {
                    second = 0L;
                } else {
                    second = DateUtil.between(dto.getStartTime(), dto.getEndTime(), DateUnit.SECOND);
                }
            }
            vo.setSubmitTime(Objects.isNull(dto.getSubmitTime()) ? "" : format.format(dto.getSubmitTime()));
            Double hours;
            if (second == 0L) {
                hours = 0D;
            } else {
                hours = NumberUtil.div(second, 3600f, 1);
            }
            vo.setTotalHours(hours);
            //记录人
            user = dto.getRecordUser();
            if (CollectionUtils.isNotEmpty(user)) {
                vo.setRecordUser(user.stream().map(MeetingUserEntity::getUserName).collect(Collectors.joining("、")));
            }
            //主持人
            user = dto.getHostUser();
//            int hostNum = user.size();
            if (CollectionUtils.isNotEmpty(user)) {
                vo.setHostUser(user.stream().map(MeetingUserEntity::getUserName).collect(Collectors.joining("、")));
            }
            //参会人员
            user = dto.getParticipantUsers();
            if (CollectionUtils.isNotEmpty(user)) {
                vo.setParticipantUsers(user.stream().map(MeetingUserEntity::getUserName).collect(Collectors.joining("、")));
            }
            //列席人员
            user = dto.getAttendUsers();
            if (CollectionUtils.isNotEmpty(user)) {
                vo.setAttendUsers(user.stream().map(MeetingUserEntity::getUserName).collect(Collectors.joining("、")));
            }
            //议程
            agenda = dto.getAgenda();
            if (CollectionUtils.isNotEmpty(agenda)) {
                int num = 1;
                builder.delete(0, builder.length());
                for (MeetingAgendaEntity x : agenda) {
                    builder.append(num).append("、").append(x.getAgendaTitle()).append("：").append(x.getAgendaContent()).append("；").append("\n");
                    num++;
                }
                vo.setAgenda(builder.toString());
            }
            //签到信息
            user = dto.getSignList();
            if (CollectionUtils.isEmpty(user)) {
//                vo.setShouldAttend(hostNum);
                vo.setShouldAttend(0);
                vo.setResultAttend(0);
                vo.setSignNum(0);
                vo.setLeaveNum(0);
                vo.setUnSignNum(0);
            } else {
//                int shouldAttend = user.size()+hostNum;
                int shouldAttend = user.size();
                int signNum = (int) user.stream().filter(x -> x.getSignStatus() == 1).count();
                int unSignNum = (int) user.stream().filter(x -> x.getSignStatus() == 2).count();
                int leaveNum = (int) user.stream().filter(x -> x.getSignStatus() == 3 || x.getSignStatus() == 4).count();
                int makeupNum = (int) user.stream().filter(x -> x.getSignStatus() == 6).count();
                vo.setShouldAttend(shouldAttend);
                vo.setResultAttend(signNum);
                vo.setSignNum(signNum);
                vo.setLeaveNum(leaveNum);
                vo.setUnSignNum(unSignNum);
                vo.setMakeUpNum(makeupNum);
            }
            //签到方式
            vo.setSignInWay(Constant.SignWay.getWay(dto.getSignInWay()));
            //活动状态
            vo.setMeetingStatus(Constant.MeetingStatus.getStatus(dto.getStatus()));

            vos.add(vo);
        }
        return vos;
    }

    /**
     * 有日程的活动结束后同步钉钉日程的签到状态
     * tc 2022-03-03
     */
    public void dingEventSync(Long regionId) {
        this.dingEventSync(regionId, null, 0);
    }

    /**
     * 有日程的活动结束后同步钉钉日程的签到状态
     * meetingIdList 指定同步的活动编号集合
     * <p>
     * tc 2022-03-03
     */
    public void dingEventSync(Long regionId, List<Long> meetingIdList, Integer dingEventSync) {
        Example example = new Example(MeetingEntity.class);
        //查询所有未删除、需要签到、签到方式为扫码签到、时间已经结束、未同步的的活动信息
        Example.Criteria criteria = example.createCriteria().andEqualTo("regionId", regionId).andEqualTo("isDel", 0).andEqualTo("signInWay", 1).andIsNotNull("dingEventId");
        //dingEventSync为空表示前端页面通过按钮发起的同步日程签到状态请求
        if (dingEventSync != null) {
            criteria.andEqualTo("dingEventSync", dingEventSync).andLessThanOrEqualTo("endTime", DateUtils.dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (meetingIdList != null && meetingIdList.size() > 0) {
            //指定的活动进行同步
            criteria.andIn("meetingId", meetingIdList);
        }
        //查询活动信息
        List<MeetingEntity> meetings = meetingMapper.selectByExample(example);
        //获取活动的签到状态并同步
        if (meetings != null && meetings.size() > 0) {
            meetings.parallelStream().forEach(mt -> {
                //获取日程签到人员
                try {
                    List<UserThirdInfoVO> userThirdInfo = orgUserDingTalkService.getUserThirdInfo(restTemplate, togServicesConfig.getUserCenter(), regionId, mt.getDingEventCreateUser());
                    if (userThirdInfo != null && userThirdInfo.size() > 0) {
                        String unionId = Objects.requireNonNull(userThirdInfo).get(0).getOpenId();
                        if (StringUtils.isNotEmpty(unionId)) {
                            //获取用户对应钉钉的编号
                            GetSignInRequest getSignInRequest = new GetSignInRequest();
                            getSignInRequest.setEventId(mt.getDingEventId());
                            getSignInRequest.setUnionId(unionId);
                            List<GetSignInListResponseBody.GetSignInListResponseBodyUsers> re = scheduleService.getScheduleSignIn(1, null, getSignInRequest);
                            log.debug("<有日程的活动结束后同步钉钉日程的签到状态> 查询日程签到人员信息返回结果 size={} re={}", re != null ? re.size() : 0, JsonUtils.toJson(re));
                            //处理钉钉返回数据
                            if (re != null && re.size() > 0) {
                                List<String> unionIdList = re.stream().map(GetSignInListResponseBody.GetSignInListResponseBodyUsers::getUserId).collect(Collectors.toList());
                                //调用钉钉底座，通过钉钉用户编号获取用户中心用户编号
                                List<UserThirdInfoVO> userInfoList = orgUserDingTalkService.getUserInfoByThToken(restTemplate, togServicesConfig.getUserCenter(), regionId, unionIdList);
                                log.debug("<有日程的活动结束后同步钉钉日程的签到状态> 查询日程签到人员信息返回结果 size={} userInfoList={}", userInfoList != null ? userInfoList.size() : 0, JsonUtils.toJson(userInfoList));
                                if (userInfoList != null && userInfoList.size() > 0) {
                                    List<Long> userIdList = userInfoList.stream().map(UserThirdInfoVO::getUserId).collect(Collectors.toList());
                                    //在事务里修改meeting_user表里的签到状态和meeting表的是否已获取钉钉日程的签到状态
                                    meetingHelperService.updateMeetingSignStatusByDingEventSync(regionId, mt.getMeetingId(), userIdList, dingEventSync);
                                }
                            } else {
                                //dingEventSync为空表示前端页面通过按钮发起的同步日程签到状态请求，这种情况就不去更新自动更新状态
                                if (dingEventSync != null) {
                                    //如果查询到日程没有人签到，就只修改日程同步标记
                                    //修改活动表，修改是否已获取钉钉日程的签到状态
                                    Example example2 = new Example(MeetingEntity.class);
                                    example2.createCriteria().andEqualTo("meetingId", mt.getMeetingId());
                                    MeetingEntity entity2 = new MeetingEntity();
                                    entity2.setDingEventSync(1);
                                    //修改同步状态
                                    meetingMapper.updateByExampleSelective(entity2, example2);
                                }
                            }
                        } else {
                            log.error("<有日程的活动结束后同步钉钉日程的签到状态>失败！ 用户中心返回的unionId为空 meetingId={} userId={} userThirdInfo={} ", mt.getMeetingId(), mt.getDingEventCreateUser(), JsonUtils.toJson(userThirdInfo));
                        }
                    } else {
                        log.error("<有日程的活动结束后同步钉钉日程的签到状态>失败！ 用户中心返回的userThirdInfo为空 meetingId={} userId={}", mt.getMeetingId(), mt.getDingEventCreateUser());
                    }
                } catch (Exception e) {
                    log.error("有日程的活动结束后同步钉钉日程的签到状态 失败！ meetingId={} userId={}", mt.getMeetingId(), mt.getDingEventCreateUser(), e);
                }
            });
        }
    }

    public Map<String, Object> statisticalUserJoinTimes(int year, Long userId) {
        Map<String, Object> map = new HashMap<>();
        DecimalFormat df = new DecimalFormat("###.##");
        List<Integer> list = meetingMapper.statisticalUserJoinTimes(year, userId);
        Integer joinTimes = CollectionUtils.isEmpty(list) ? 0 : (int) list.stream().filter(i -> 1 == i).count();
        BigDecimal proportionDecimal = CollectionUtils.isEmpty(list) ? BigDecimal.valueOf(0) :
                BigDecimal.valueOf((double) joinTimes / list.size() * 100)
                        .setScale(4, RoundingMode.HALF_UP);
        String proportion = df.format(proportionDecimal) + "%";
        map.put("本年度累计", joinTimes);
        map.put("出勤率", proportion);
        return map;
    }

    //预留
//    public Map<String, Object> statisticalOrgJoinTimes(Integer year, Long oid) {
//        Map<String, Object> map = new HashMap<>();
//        Integer joinTimes = meetingMapper.statisticalOrgJoinTimes(year,oid);
//        List<MeetingTaskEntity> list = meetingTaskMapper.statiscalOrgRate(oid);
//        Map<Long,List<MeetingTaskEntity>> typeMap = list.stream().collect(Collectors.groupingBy(MeetingTaskEntity::getTypeId));
//        List<BigDecimal> rate = new ArrayList<>();
//        for(Map.Entry<Long,List<MeetingTaskEntity>> keySet: typeMap.entrySet()){
//           int total = keySet.getValue().size();
//           long finish = keySet.getValue().stream().filter(i->i.getStatus()==2).count();
//           BigDecimal proportionDecimal =
//                    BigDecimal.valueOf((double) finish/total*100)
//                            .setScale(4, RoundingMode.HALF_UP);
//            rate.add(proportionDecimal);
//        }
//        //求平均值
//        double proportionAvg = rate.stream().collect(Collectors.averagingDouble(BigDecimal::doubleValue));
//        DecimalFormat df = new DecimalFormat("###.##");
//        String proportion = df.format(proportionAvg)+"%";
//        map.put("本年度累计",joinTimes);
//        map.put("按期召开率",proportion);
//        return map;
//    }

    public Map<String, Object> statisticalOrgJoinTimes(Integer year, Long oid) {
        Map<String, Object> map = new HashMap<>();
        Integer joinTimes = meetingMapper.statisticalOrgJoinTimes(year, oid);
        BigDecimal rate = meetingTaskMapper.statiscalOrgRate(oid);
        if (null == rate) {
            map.put("本年度累计", joinTimes);
            map.put("按期召开率", "0%");
            return map;
        }
        DecimalFormat df = new DecimalFormat("###.##");
        String proportion = df.format(rate) + "%";
        map.put("本年度累计", joinTimes);
        map.put("按期召开率", proportion);
        return map;
    }

    public MeetingTaskEntity queryMeetingTask(Long typeId, Long orgId, String stime, String etime) {
        log.debug("开始xxxx");
        Example example = new Example(MeetingTaskEntity.class);
        example.createCriteria().andEqualTo("typeId", typeId).andEqualTo("orgId", orgId).andEqualTo("startTime", stime).andEqualTo("endTime", etime);
        List<MeetingTaskEntity> list = meetingTaskMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    /**
     * 同步市值的议程 根据meeting_id查询烟草议程信息
     *
     * @param meetingId
     * @return
     */
    public List<MeetingAgendaEntity> queryMeetingAgenda(Long meetingId) {
        Example example = new Example(MeetingAgendaEntity.class);
        example.createCriteria().andEqualTo("meetingId", meetingId);
        List<MeetingAgendaEntity> list = meetingAgendaMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            list = list.stream().sorted(Comparator.comparing(MeetingAgendaEntity::getAgendaId)).collect(Collectors.toList());
            return list;
        } else {
            return null;
        }
    }

    /**
     * 同步市值的meeting_type_id
     *
     * @param meetingId
     * @return
     */
    public List<MeetingTypeEntity> queryMeetingType(Long meetingId) {
        Example example = new Example(MeetingTypeEntity.class);
        example.createCriteria().andEqualTo("meetingId", meetingId);
        return meetingTypeMapper.selectByExample(example);
    }

    public void updateAgenda(UpdateAgendaForm form, HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        // 查询oldMeeting by id
        MeetingEntity oldMeetingEntity = this.findById(sysHeader, form.getMeetingId());
        MeetingEntity upMeetingEntity = this.findById(sysHeader, form.getMeetingId());
        upMeetingEntity.setAgenda(form.getAgenda());
        this.upMeetingAgenda(upMeetingEntity, oldMeetingEntity, headers);
        // 先移除本组织的第一议题数据
        var topPriorityMeeting = new TopPriorityMeeting();
        topPriorityMeeting.setMeetingId(oldMeetingEntity.getMeetingId());
        topPriorityMeeting.setAssociatedOrg(oldMeetingEntity.getOrgId());
        topPriorityService.removeAssociatedAndMeetings(topPriorityMeeting);
        // 添加新的第一议题数据
        this.setTopPriorityService(upMeetingEntity, headers);
        // 更新活动议程 刷新缓存
        indexService.collectRedis();
    }
}
