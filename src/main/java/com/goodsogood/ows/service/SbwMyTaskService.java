package com.goodsogood.ows.service;

import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.SbwHandleMapper;
import com.goodsogood.ows.mapper.SbwTaskOrgMapper;
import com.goodsogood.ows.model.db.SbwHandleEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SbwHandleForm;
import com.goodsogood.ows.model.vo.SbwTaskListForm;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 南岸区网信办我的任务
 * <AUTHOR>
 * @date 2021.07.29
 */
@Service
@Log4j2
public class SbwMyTaskService {

    private final SbwTaskOrgMapper sbwTaskOrgMapper;
    private final SbwHandleMapper sbwHandleMapper;
    private final SbwAsyncService sbwAsyncService;
    private final Errors errors;

    @Autowired
    public SbwMyTaskService(SbwTaskOrgMapper sbwTaskOrgMapper, SbwHandleMapper sbwHandleMapper, SbwAsyncService sbwAsyncService, Errors errors) {
        this.sbwTaskOrgMapper = sbwTaskOrgMapper;
        this.sbwHandleMapper = sbwHandleMapper;
        this.sbwAsyncService = sbwAsyncService;
        this.errors = errors;
    }


    /**
     * 查询我被分配的任务
     * @param form
     * @param page
     * @param pageSize
     * @return
     */
    public List<SbwTaskListForm> MyTaskList(SbwTaskListForm form, Integer page, Integer pageSize){
        return PageHelper.startPage(page,pageSize).doSelectPage(()-> sbwTaskOrgMapper.myOrgTask(form));
    }

    /**
     * 提交任务处理
     * @param form
     * @return
     */
    public Integer submit(SbwHandleForm form){
        if (ObjectUtils.isEmpty(form.getTaskId()) || ObjectUtils.isEmpty(form.getAcceptOrg())
                || ObjectUtils.isEmpty(form.getAcceptUser()) || ObjectUtils.isEmpty(form.getPhone())){
            log.error("缺少必填项");
            throw new ApiException("缺少必填项",new Result<>(errors,3007,HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        //工作任务
        if (Constant.TASK_WORD_TYPE.equals(form.getTaskType())){
            if (StringUtils.isBlank(form.getHandleContent())){
                log.error("缺少必填项");
                throw new ApiException("缺少必填项",new Result<>(errors,3007,HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }
            form.setHandleStatus(Constant.HANDLE_TYPE_SA);
        }else { //转办单
            form.setTaskType(Constant.TASK_PROXY_TYPE);
            if (ObjectUtils.isEmpty(form.getOpenStatus()) || ObjectUtils.isEmpty(form.getHandleStatus())){
                log.error("缺少必填项");
                throw new ApiException("缺少必填项",new Result<>(errors,3007,HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }
            if (StringUtils.isBlank(form.getHandleContent())){
                log.error("缺少处置内容");
                throw new ApiException("缺少处置内容",new Result<>(errors,3005,HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }
            if (form.getOpenStatus() == 2 && StringUtils.isBlank(form.getPrivateReason())){
                log.error("缺少不公开原因");
                throw new ApiException("缺少不公开原因",new Result<>(errors,3003,HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }
            if (form.getHandleStatus().equals(Constant.HANDLE_TYPE_SR) && StringUtils.isBlank(form.getHandleComment())){
                log.error("缺少拒绝原因");
                throw new ApiException("缺少拒绝原因",new Result<>(errors,3004,HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }
        }
        SbwHandleEntity entity = new SbwHandleEntity();
        long handleId = -1;
        boolean flag = true;
        Date date = new Date();
        if (ObjectUtils.isEmpty(form.getHandleId())){
            entity.setTaskId(form.getTaskId());
            entity.setRegionId(form.getRegionId());
            entity.setOrgId(form.getOrgId());
            entity.setUserOrgId(form.getUserOrgId());
            entity.setFlag(form.getFlag());
            entity = sbwHandleMapper.selectOne(entity);
            if (!ObjectUtils.isEmpty(entity)){
                flag = false;
                handleId = entity.getHandleId();
            }else {
                entity = new SbwHandleEntity();
            }
        }else {
            flag = false;
        }
        BeanUtils.copyProperties(form,entity);
        if (handleId != -1){
            entity.setHandleId(handleId);
        }
        entity.setAcceptTime(date);
        int result;
        if (flag){
            //新增
            entity.setCreateTime(date);
            result = sbwHandleMapper.insertUseGeneratedKeys(entity);
        }else {
            //提交草稿
            entity.setUpdateTime(date);
            if (StringUtils.isEmpty(form.getFileId())){
                sbwAsyncService.updateFile(form.getHandleId(),3);
            }
            result = sbwHandleMapper.updateByPrimaryKeySelective(entity);
        }
        //记录流水
        sbwAsyncService.recordFlow(entity,date);
        //更新任务组织对应状态
        sbwAsyncService.updateTaskOrgTypeStatus(entity,date);
        return result;
    }

    /**
     * 保存任务处理草稿
     * @param form
     * @return
     */
    public Long save(SbwHandleForm form){
        if (ObjectUtils.isEmpty(form.getTaskId())){
            log.error("缺少taskId");
            throw new ApiException("缺少必填项",new Result<>(errors,3007,HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        SbwHandleEntity entity = new SbwHandleEntity();
        BeanUtils.copyProperties(form,entity);
        Date date = new Date();
        if (ObjectUtils.isEmpty(form.getHandleId())){
            //新增草稿
            entity.setCreateTime(date);
            sbwHandleMapper.insertUseGeneratedKeys(entity);
        }else {
            //修改草稿
            entity.setUpdateTime(date);
            sbwHandleMapper.updateByPrimaryKeySelective(entity);
            if (StringUtils.isEmpty(form.getFileId())){
                sbwAsyncService.updateFile(form.getHandleId(),3);
            }
        }
        return entity.getHandleId();
    }
}
