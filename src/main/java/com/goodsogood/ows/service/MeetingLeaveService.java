package com.goodsogood.ows.service;

import com.goodsogood.ows.common.MeetingCanstant;
import com.goodsogood.ows.common.MeetingLeaveConstant;
import com.goodsogood.ows.common.StringCanstant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingLeaveMapper;
import com.goodsogood.ows.mapper.MeetingMapper;
import com.goodsogood.ows.mapper.MeetingUserMapper;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingLeaveEntity;
import com.goodsogood.ows.model.db.MeetingUserEntity;
import com.goodsogood.ows.model.vo.*;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018/10/29 16:08
 */
@Service
@Log4j2
public class MeetingLeaveService {

    private final MeetingMapper meetingMapper;
    private final MeetingLeaveMapper meetingLeaveMapper;
    private final MeetingUserMapper meetingUserMapper;
    private final StringRedisTemplate stringRedisTemplate;
    private final RedisService redisService;
    private final MeetingLeaveRedisService meetingLeaveRedisService;
    private final Errors errors;
    private static final String addLeaveLockKey = "leave_add";
    private static final int addLeaveLockExpire = 60000;
    private final OpenService openService;

    @Autowired
    public MeetingLeaveService(MeetingMapper meetingMapper,
                               MeetingLeaveMapper meetingLeaveMapper,
                               MeetingUserMapper meetingUserMapper,
                               StringRedisTemplate stringRedisTemplate,
                               RedisService redisService, MeetingLeaveRedisService meetingLeaveRedisService, Errors errors, OpenService openService) {
        this.meetingMapper = meetingMapper;
        this.meetingLeaveMapper = meetingLeaveMapper;
        this.meetingUserMapper = meetingUserMapper;
        this.stringRedisTemplate = stringRedisTemplate;
        this.redisService = redisService;
        this.meetingLeaveRedisService = meetingLeaveRedisService;
        this.errors = errors;
        this.openService = openService;
    }

    /**
     * 添加请假
     *
     * @param meetingLeaveAddForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public Long add(HeaderHelper.SysHeader sysHeader, MeetingLeaveAddForm meetingLeaveAddForm) throws Exception {
        Long meetingId = meetingLeaveAddForm.getMeetingId();
        Long userId = sysHeader.getUserId();
        if (userId == null) {
            throw new ApiException("没有请假权限", new Result<>(errors, 1834, HttpStatus.OK.value()));
        }
        MeetingEntity meetingEntity = meetingMapper.selectByPrimaryKey(meetingId);
        if (meetingEntity == null) {
            throw new ApiException("活动未找到", new Result<>(errors, 9404, HttpStatus.OK.value(), StringCanstant.ACTIVITY));
        }
        if (MeetingCanstant.MEETING_STATUS_SOON_START.shortValue() != meetingEntity.getStatus()) {
            throw new ApiException("活动状态不匹配，无法执行请假操作", new Result<>(errors, 1820, HttpStatus.OK.value()));
        }
        Boolean exists = existLeave(userId, meetingId);
        if (exists) {
            throw new ApiException("已执行过请假操作，无法再次请假", new Result<>(errors, 1821, HttpStatus.OK.value()));
        }
        //插入t_meeting_leave修改成插入或者更新
        MeetingLeaveEntity meetingLeaveEntity = new MeetingLeaveEntity();
        meetingLeaveEntity.setMeetingId(meetingLeaveAddForm.getMeetingId());
        meetingLeaveEntity.setType(meetingLeaveAddForm.getType());
        meetingLeaveEntity.setReason(meetingLeaveAddForm.getReason());
        meetingLeaveEntity.setUserId(userId);
        meetingLeaveMapper.addLeave(meetingLeaveEntity);
        return meetingLeaveEntity.getMeetingLeaveId();
    }

    /**
     * 检查用户是否在某个活动中请假
     *
     * @param userId    用户id
     * @param meetingId 活动id
     * @return
     */
    public Boolean existLeave(long userId, long meetingId) {
        Example example = new Example(MeetingLeaveEntity.class);
        example.createCriteria()
                .andEqualTo("userId", userId)
                .andEqualTo("meetingId", meetingId)
                .andIn("status", MeetingLeaveConstant.LeaveStatus.getCanNotLeaveStatus());
        List<MeetingLeaveEntity> meetingLeaveEntityList = meetingLeaveMapper.selectByExample(example);
        return !CollectionUtils.isEmpty(meetingLeaveEntityList);
    }

    /**
     * 可请假列表  可销假列表  请假记录列表
     * @param userId    当前用户id
     * @param type      审批类型(1:请假，2:销假 3：请假记录)
     * @param types     活动类型
     * @param startTime 活动开始时间
     * @param endTime   活动结束时间
     * @param keyWord   关键词（用户名）
     * @return
     */
    public List<LeaveMeetingForm> leaveList(long regionId, long userId, int type, List<Integer> types, String startTime,
                                            String endTime, String keyWord, Integer status) {
        if (1 == type) {
            return meetingMapper.queryCanLeaveMeeting(regionId, userId);
        }
        if(2 == type){//销假审批不通过的,也能再次发起销假
            return meetingLeaveMapper.queryCanOffLeave(userId,types,startTime,endTime,keyWord,status);
        }
        if(3 == type){
            return meetingLeaveMapper.queryAllLeave(regionId,userId,types,startTime,endTime,keyWord,status);
        }
        return null;
    }


    /**
     * 待我审批列表  我已经审批列表
     * @param userId    当前用户id
     * @param type      类型(1:待审核  2：已审核)
     * @param userName  筛选条件-姓名
     * @param startTime 活动开始时间
     * @param endTime   活动结束时间
     * @param keyWord   关键词（用户名）
     * @return
     */
    public List<LeaveMeetingForm> approveList(long regionId, long userId, int type, List<Integer> types, String userName,
                                              String startTime, String endTime, String keyWord, Integer status) {
        //查询是哪些机构的管理员
        List<Long> orgIds = openService.getManageOrgIds(userId,regionId);
//        List<Long> orgIds = Arrays.asList(3L);
        if(CollectionUtils.isEmpty(orgIds)){
            return Collections.EMPTY_LIST;
        }
        //查询这些机构下有哪些会议
        if (1 == type) {
            return meetingLeaveMapper.queryNotApprove(orgIds,userName,startTime,endTime,keyWord,types);
        }
        if(2 == type){
            return meetingLeaveMapper.queryApprove(userId,userName,startTime,endTime,keyWord,status,types);
        }
        return null;
    }



    /**
     * 查询请假详情
     *
     * @param meetingLeaveId 请假id
     * @return
     */
    public LeaveMeetingForm detail(Long meetingLeaveId) {
        return meetingLeaveMapper.queryOneDetail(meetingLeaveId);
    }

    /**
     * 撤销请假
     *
     * @param meetingLeaveId 请假id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancel(HeaderHelper.SysHeader sysHeader, long meetingLeaveId, String reason) {
        MeetingLeaveEntity meetingLeaveEntity = meetingLeaveMapper.selectByPrimaryKey(meetingLeaveId);
        if (meetingLeaveEntity == null) {
            throw new ApiException("请假未找到", new Result<>(errors, 9404, HttpStatus.OK.value(), "请假"));
        }

        Long meetingId = meetingLeaveEntity.getMeetingId();
        Long userId = meetingLeaveEntity.getUserId();

        MeetingEntity meetingEntity = meetingMapper.selectByPrimaryKey(meetingId);
        if (meetingEntity == null) {
            throw new ApiException("活动未找到", new Result<>(errors, 9404, HttpStatus.OK.value(), StringCanstant.ACTIVITY));
        }

        // 如果活动状态不为已举办、待填报
        if (MeetingCanstant.MEETING_STATUS_SOON_START.shortValue() != meetingEntity.getStatus().shortValue()) {
            throw new ApiException("活动状态不匹配，无法撤销请假", new Result<>(errors, 1836, HttpStatus.OK.value()));
        }

        if (!meetingLeaveEntity.getUserId().equals(sysHeader.getUserId())) {
            throw new ApiException("无权限修改此请假", new Result<>(errors, 1824, HttpStatus.OK.value()));
        }
        if (!MeetingLeaveConstant.LeaveStatus.getCanOffLeaveStatus().contains(meetingLeaveEntity.getStatus())) {
            throw new ApiException("请假状态不匹配，无法撤销请假", new Result<>(errors, 1822, HttpStatus.OK.value()));
        }
        if(MeetingLeaveConstant.LeaveStatus.LEAVE_APPROVE_PASS.getStatus()== meetingLeaveEntity.getStatus()){
            if(reason == null){
                throw new ApiException("未输入销假原因，无法销假!", new Result<>(errors, 1870, HttpStatus.OK.value()));
            }
        }
        MeetingLeaveEntity temp = new MeetingLeaveEntity();
        temp.setMeetingLeaveId(meetingLeaveId);
        if(MeetingLeaveConstant.LeaveStatus.LEAVE_APPROVE_PASS.getStatus()== meetingLeaveEntity.getStatus() ||
                MeetingLeaveConstant.LeaveStatus.OFF_LEAVE_NOT_PASS.getStatus()== meetingLeaveEntity.getStatus()){
            temp.setStatus(MeetingLeaveConstant.LeaveStatus.OFF_LEAVE_WAIT_APPROVE.getStatus());
        }else{
            temp.setStatus(MeetingLeaveConstant.LeaveStatus.OFF_LEAVE_BACK.getStatus());
        }
        temp.setUpdateTime(LocalDateTime.now());
        temp.setLastChangeUser(sysHeader.getUserId());
        temp.setOffLeaveReason(reason);//销假原因
        int result = meetingLeaveMapper.updateByPrimaryKeySelective(temp);
        if (result <= 0) {
            throw new ApiException("数据错误，请联系管理员", new Result<>(errors, 9913, HttpStatus.OK.value()));
        }
//        if(MeetingLeaveConstant.LeaveStatus.LEAVE_APPROVE_PASS.getStatus()!= meetingLeaveEntity.getStatus()){
//            Integer signStatus = null;
//            // 如果不需要签到，默认为已签到
//            if (meetingEntity.getIsSignIn() == 0) {
//                signStatus = 1;
//            }
//            // 如果需要签到，默认为未签到
//            else if (meetingEntity.getIsSignIn() == 1) {
//                signStatus = 2;
//                if (meetingEntity.getSignInWay() == 0) {
//                    //如果签到方式是手写签到，默认为未签到
//                    signStatus = 2;
//                }
//            }
//            meetingUserMapper.updateMeetingUser(signStatus, null, meetingId, userId);
//        }
//
        // 撤销请假 删除审批人员的审批数量缓存
//        redisService.delMapByKey(RedisConstant.MEETING_INDEX, meetingLeaveRedisService.waitApprovalCountRedisKey(sysHeader.getRegionId(), meetingLeaveEntity.getCheckUserId()));
        return true;
    }

    /**
     * 请假审批
     *
     * @param meetingLeaveCheckForm
     * @return
     */
    // 2018-11-16 10:19 zhanchuanhao修改
    @Transactional(rollbackFor = Exception.class)
    public Boolean check(HeaderHelper.SysHeader sysHeader, MeetingLeaveCheckForm meetingLeaveCheckForm) {
        Long meetingLeaveId = meetingLeaveCheckForm.getMeetingLeaveId();
        //查询状态，如果是请假0 1 ，更新status = 3且check_result，或者通过status=2
        //如果是销假4，更新status = 6且off_check_result，或者通过status=7
        MeetingLeaveEntity meetingLeaveEntity = meetingLeaveMapper.selectByPrimaryKey(meetingLeaveId);
        if(null == meetingLeaveEntity){
            return false;
        }
        int status = meetingLeaveEntity.getStatus();
        meetingLeaveEntity.setUpdateTime(LocalDateTime.now());
        meetingLeaveEntity.setLastChangeUser(sysHeader.getUserId());
        if(MeetingLeaveConstant.LeaveStatus.getLeaveStatus().contains(Integer.valueOf(status).shortValue())){//如果是请假审批
            meetingLeaveEntity.setCheckTime(LocalDateTime.now());
            meetingLeaveEntity.setCheckUserId(sysHeader.getUserId());
            meetingLeaveEntity.setCheckUserName(sysHeader.getUserName());
            if(2 == meetingLeaveCheckForm.getStatus()){//如果请假审批通过，同时更新meeting_user表
                meetingLeaveEntity.setStatus((short)2);
                //更新t_meeting_user的状态
                Integer signStatus = meetingLeaveEntity.getType()==1? 3 : 4;
                List<MeetingUserEntity> meetingUserEntityList = queryMeetingUserEntities(meetingLeaveEntity);
                Short oldStatus = meetingUserEntityList==null? 3 : meetingUserEntityList.get(0).getSignStatus();
                meetingUserMapper.updateSignStatus(meetingLeaveEntity.getMeetingId(),meetingLeaveEntity.getUserId(),
                        signStatus,oldStatus,meetingLeaveEntity.getReason());
                //更新t_meeting_user的状态,查询其原来的状态
//                List<MeetingUserEntity> meetingUserEntities = queryMeetingUserEntities(meetingLeaveEntity);
//                Short signStatus = meetingUserEntities.get(0).getSignStatus();
//                Short oldStatus = meetingUserEntities.get(0).getOldSignStatus();
//                meetingUserMapper.updateSignStatusNew(meetingLeaveEntity.getMeetingId(),meetingLeaveEntity.getUserId(),signStatus,oldStatus,meetingLeaveEntity.getReason());
            }else{//请假审批不通过，更新状态、不通过的原因
                meetingLeaveEntity.setStatus((short)3);
                if(meetingLeaveCheckForm.getResult()==null || "".equals(meetingLeaveCheckForm.getResult())){
                    throw new ApiException("必须输入审批不通过的原因!", new Result<>(errors, 1871, HttpStatus.OK.value()));
                }
                meetingLeaveEntity.setCheckResult(meetingLeaveCheckForm.getResult());
            }
        }
        //如果是销假审批
        if(MeetingLeaveConstant.LeaveStatus.getOffLeaveStatus().contains(Integer.valueOf(status).shortValue())){
            meetingLeaveEntity.setOffCheckUserId(sysHeader.getUserId());
            meetingLeaveEntity.setOffCheckTime(LocalDateTime.now());
            meetingLeaveEntity.setOffCheckUserName(sysHeader.getUserName());
            if(2 == meetingLeaveCheckForm.getStatus()){
                meetingLeaveEntity.setStatus((short)7);
                //更新t_meeting_user的状态,查询其原来的状态
                List<MeetingUserEntity> meetingUserEntities = queryMeetingUserEntities(meetingLeaveEntity);
                Short signStatus = meetingUserEntities.get(0).getSignStatus();
                Short oldStatus = meetingUserEntities.get(0).getOldSignStatus();
                if(null!=oldStatus && oldStatus!=0){//如果没有状态则不更新
                    meetingUserMapper.updateSignStatusNew(meetingLeaveEntity.getMeetingId(),meetingLeaveEntity.getUserId(),
                            signStatus,oldStatus,meetingLeaveEntity.getReason());
                }
            }else{
                meetingLeaveEntity.setStatus((short)6);
                meetingLeaveEntity.setOffCheckResult(meetingLeaveCheckForm.getResult());
                if(meetingLeaveCheckForm.getResult()==null || "".equals(meetingLeaveCheckForm.getResult())){
                    throw new ApiException("必须输入审批不通过的原因!", new Result<>(errors, 1871, HttpStatus.OK.value()));
                }
            }
        }
        //更新请假表状态
        meetingLeaveMapper.updateByPrimaryKeySelective(meetingLeaveEntity);
        return true;
    }

    private List<MeetingUserEntity> queryMeetingUserEntities(MeetingLeaveEntity meetingLeaveEntity) {
        Example example = new Example(MeetingUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("meetingId", meetingLeaveEntity.getMeetingId());
        criteria.andEqualTo("userId", meetingLeaveEntity.getUserId());
        return meetingUserMapper.selectByExample(example);
    }


    /**
     * 活动签到之后重置待审批和已通过的、销假审批不通过的请假为已撤销
     *
     * @param meetingId
     * @param userId
     * @return
     */
    // 2018-11-16 10:19 zhanchuanhao修改
    public int signInAfterCancelLeave(long meetingId, long userId) {
        return meetingLeaveMapper.signInAfterCancelLeave(meetingId, userId);
    }

    /**
     * 提交活动结果之后，拒绝所有待审核的请假
     *
     * @param meetingId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int meetingAfterRejectLeave(long meetingId) {
        Example example = new Example(MeetingLeaveEntity.class);
        example.createCriteria()
                .andEqualTo("meetingId", meetingId)
                .andIn("status", MeetingLeaveConstant.LeaveStatus.getRejectLeaveStatus());
        example.selectProperties("status","meetingLeaveId");
        List<MeetingLeaveEntity> list = meetingLeaveMapper.selectByExample(example);
        list.forEach(i->{
            short status = MeetingLeaveConstant.LeaveStatus.getLeaveStatus().contains(i.getStatus())?
                    MeetingLeaveConstant.LeaveStatus.LEAVE_APPROVE_NOT_PASS.getStatus():
                    MeetingLeaveConstant.LeaveStatus.OFF_LEAVE_NOT_PASS.getStatus();
                    i.setStatus(status);
             meetingLeaveMapper.updateByPrimaryKeySelective(i);

        });
        return 1;
    }

    /**
     * 查询我审批的和待我审批的请假列表
     */
//    public List<LeaveListForm> leaveList(LeaveListForm myCheckListForm) {
//        return meetingLeaveMapper.leaveList(myCheckListForm);
//    }
}
