package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.UserCommentMapper;
import com.goodsogood.ows.mapper.UserCommentStatisticsMapper;
import com.goodsogood.ows.model.db.UserCommentEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.user.UserChangeForm;
import com.goodsogood.ows.utils.SaasUtils;
import com.google.common.base.Preconditions;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 党员评议服务层
 * @date 2019/12/27
 */
@Service
@Log4j2
public class UserCommentService {

    private static final ObjectMapper mapper = new ObjectMapper();

    private final Errors errors;
    private final TogServicesConfig togServicesConfig;
    private final UserCommentMapper userCommentMapper;
    private final UserCommentStatisticsMapper userCommentStatisticsMapper;
    private final OpenService openService;
    private final RestTemplate restTemplate;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public UserCommentService(Errors errors, TogServicesConfig togServicesConfig, UserCommentMapper userCommentMapper,
                              UserCommentStatisticsMapper userCommentStatisticsMapper, OpenService openService,
                              RestTemplate restTemplate, StringRedisTemplate redisTemplate) {
        this.errors = errors;
        this.togServicesConfig = togServicesConfig;
        this.userCommentMapper = userCommentMapper;
        this.userCommentStatisticsMapper = userCommentStatisticsMapper;
        this.openService = openService;
        this.restTemplate = restTemplate;
        this.redisTemplate = redisTemplate;
    }

    public Result<?> getUserCommentList(UserCommentQueryForm queryForm, HttpHeaders headers) {
        // 判断是否选择评议等级，
        // 如果选择了等级，则从评议列表中取出人员列表进行查询
        Integer commentLevel = queryForm.getCommentLevel();
        FindUserListByOrgForm form = new FindUserListByOrgForm();
        if (null == commentLevel || commentLevel == 0) {
            form.setOrgId(queryForm.getOrgId());
            form.setUserName(queryForm.getUserName());
            form.setPage(queryForm.getPage());
            form.setPageSize(queryForm.getPageSize());
            Result<Page<UserInfoCommentVO>> result = this.getUserCommentInfoListByUserCenter(form, headers);
            // 获取userId列表
            Page<UserInfoCommentVO> userList = result.getData();
            if (CollectionUtils.isNotEmpty(userList)) {
                List<Long> userIds = userList.stream().map(UserInfoCommentVO::getUserId).collect(Collectors.toList());
                List<UserCommentEntity> list = new ArrayList<>(userIds.size());
                if (CollectionUtils.isNotEmpty(userIds)) {
                    HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
                    list = this.getUserListByUserId(queryForm.getYear(), userIds, sysHeader.getRegionId());
                }
                // 封装评级等级
                List<UserCommentEntity> finalList = list;
                userList.forEach(userInfo -> {
                    finalList.forEach(userInfoComment -> {
                        if (userInfo.getUserId().equals(userInfoComment.getUserId())) {
                            userInfo.setUserCommentId(userInfoComment.getUserCommentId());
                            userInfo.setCommentLevel(userInfoComment.getRating());
                        }
                    });
                });
            }
            return result;
        } else {
            Page<UserInfoCommentVO> pageList = this.selectUserListByLevel(queryForm.getOrgId(), queryForm.getYear(), commentLevel,
                    queryForm.getUserName(), queryForm.getPage(), queryForm.getPageSize());
            // 封装用户信息
            List<Long> userIds = pageList.stream().map(UserInfoCommentVO::getUserId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userIds)) {
                form.setOrgId(queryForm.getOrgId());
                form.setUserIds(userIds);
                form.setPageSize(100);
                Result<Page<UserInfoCommentVO>> result = this.getUserCommentInfoListByUserCenter(form, headers);
                pageList.forEach(userCommentInfo -> {
                    result.getData().forEach(userInfo -> {
                        if (userCommentInfo.getUserId().equals(userInfo.getUserId())) {
                            userCommentInfo.setCertNumber(userInfo.getCertNumber());
                            userCommentInfo.setOrgId(userInfo.getOrgId());
                            userCommentInfo.setOrgName(userInfo.getOrgName());
                            userCommentInfo.setSex(userInfo.getSex());
                            userCommentInfo.setEthnic(userInfo.getEthnic());
                            userCommentInfo.setBirthday(userInfo.getBirthday());
                            userCommentInfo.setJoiningTime(userInfo.getJoiningTime());
                            userCommentInfo.setEducation(userInfo.getEducation());
                            userCommentInfo.setGradeName(userInfo.getGradeName());
                            userCommentInfo.setPositionName(userInfo.getPositionName());
                        }
                    });
                });
            }
            return new Result<>(pageList, errors);
        }
    }

    /**
     * 调用用户查询用户信息，带分页
     *
     * @param form
     * @return com.github.pagehelper.Page<com.goodsogood.ows.model.vo.UserInfoCommentVO>
     * <AUTHOR>
     * @date 2019/12/28
     */
    public Result<Page<UserInfoCommentVO>> getUserCommentInfoListByUserCenter(FindUserListByOrgForm form, HttpHeaders headers) {
        log.debug("远程调用USER-CENTER查询用户信息请求参数: [{}]", form);
        Result<Page<UserInfoCommentVO>> result = null;
        // 远程调用地址
        String url = String.format("http://%s/find-user-info-by-org-id", this.togServicesConfig.getUserCenter());
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 调用远程方法
        try {
            HttpEntity<Object> requestEntity = new HttpEntity<Object>(form, headers);
            ResponseEntity<String> stringResponseEntity = this.restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            HttpStatus statusCode = stringResponseEntity.getStatusCode();
            if (statusCode.equals(HttpStatus.OK)) {
                result = mapper.readValue(stringResponseEntity.getBody(), new TypeReference<Result<Page<UserInfoCommentVO>>>() {
                });
                log.debug("远程调用USER-CENTER查询用户信息: [{}]", result);
            } else {
                throw new ApiException("用户中心服务异常 ", new Result<>(errors, 9907, HttpStatus.BAD_REQUEST.value(), "用户中心服务异常"));
            }
        } catch (Exception e) {
            log.error("远程调用USER-CENTER查询用户信息, 错误内容:", e);
            throw new ApiException("用户中心服务异常 ", new Result<>(errors, 9907, HttpStatus.BAD_REQUEST.value(), "用户中心服务异常"));
        }
        return result;
    }

    /**
     * 查询单个组织信息
     *
     * @param orgId
     * @return com.goodsogood.ows.model.vo.activity.OrganizationBase
     * <AUTHOR>
     * @date 2019/12/31
     */
    public OrganizationBase findOrgById(Long orgId) {
        OrganizationBase organizationBase = new OrganizationBase();
        // 远程调用地址
        String url = String.format("http://%s/find-org-by-id?org_id=%s", this.togServicesConfig.getUserCenter(), orgId);
        // 请求头部
        HttpHeaders headers = new HttpHeaders();
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        // 调用远程方法
        try {
            organizationBase = RemoteApiHelper.get(this.restTemplate, url, headers, new TypeReference<Result<OrganizationBase>>() {
            });
            log.debug("远程调用USER-CENTER查询组织信息: [{}]", organizationBase);
        } catch (Exception e) {
            log.error("远程调用USER-CENTER查询组织信息, 错误内容:", e);
            throw new ApiException("用户中心服务异常 ", new Result<>(errors, 9907, HttpStatus.BAD_REQUEST.value(), "用户中心服务异常"));
        }
        return organizationBase;
    }

    /**
     * 根据用户ID和评议年度查询用户
     *
     * @param year
     * @param userIds
     * @return java.util.List<com.goodsogood.ows.model.db.UserCommentEntity>
     * <AUTHOR>
     * @date 2019/12/30
     */
    public List<UserCommentEntity> getUserListByUserId(Integer year, List<Long> userIds, Long regionId) {
        Example example = new Example(UserCommentEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("reviewYear", year);
        criteria.andEqualTo("status", Constant.YES);
        criteria.andEqualTo("regionId", regionId);
        criteria.andIn("userId", userIds);
        List<UserCommentEntity> userList = this.userCommentMapper.selectByExample(example);
        return userList;
    }

    /**
     * 根据等级查询评议列表, 带分页
     *
     * @param year
     * @return java.util.List<com.goodsogood.ows.model.db.UserCommentEntity>
     * <AUTHOR>
     * @date 2019/12/30
     */
    public Page<UserInfoCommentVO> selectUserListByLevel(Long orgId, Integer year, Integer commentLevel, String userName, Integer page, Integer pageSize) {
        //设置分页属性
        int p = Preconditions.checkNotNull(page);
        int r = Preconditions.checkNotNull(pageSize);
        Page<UserInfoCommentVO> userCommentList = PageHelper.startPage(p, r).doSelectPage(() ->
                this.userCommentMapper.selectUserListByLevel(orgId, year, commentLevel, userName));
        return userCommentList;
    }

    /**
     * 新增用户评议结果
     *
     * @param addVo
     * @param userId
     * @return int
     * <AUTHOR>
     * @date 2019/12/30
     */
    public int insertUserComment(UserCommentAddVO addVo, Long userId, Long regionId) {
        int count = this.getCommentByYearAndUser(addVo.getUserId(), addVo.getReviewYear(), regionId);
        if (count == 0) {
            // 获取人员信息
            OrganizationBase org = this.findOrgById(addVo.getOrgId());
            if (org != null) {
                UserCommentEntity commentEntity = new UserCommentEntity();
                Date date = new Date(System.currentTimeMillis());
                commentEntity.setUserId(addVo.getUserId());
                commentEntity.setUserName(addVo.getUserName());
                commentEntity.setRegionId(regionId);
                commentEntity.setOrgId(org.getOrganizationId());
                commentEntity.setOrgName(org.getName());
                commentEntity.setOrgLevel(org.getOrgLevel());
                commentEntity.setReviewYear(addVo.getReviewYear());
                commentEntity.setRating(addVo.getRating());
                commentEntity.setDealOpinion(addVo.getDealOpinion());
                commentEntity.setAdditionalInformation(addVo.getAdditionalInformation());
                commentEntity.setStatus(Constant.YES);
                commentEntity.setCreateUser(userId);
                commentEntity.setCreateTime(date);
                return this.userCommentMapper.insert(commentEntity);
            }
        } else {
            throw new ApiException("当前党员在该年度已存在评议结果 ", new Result<>(errors, 2003, HttpStatus.BAD_REQUEST.value(), "当前党员在该年度已存在评议结果"));
        }
        return 0;
    }

    /**
     * 更新用户评议结果
     *
     * @param updateVo
     * @param userId
     * @return int
     * <AUTHOR>
     * @date 2019/12/30
     */
    public int updateUserComment(UserCommentUpdateVO updateVo, Long userId) {
        UserCommentEntity commentEntity = this.userCommentMapper.selectByPrimaryKey(updateVo.getUserCommentId());
        Date date = new Date(System.currentTimeMillis());
        commentEntity.setUserCommentId(updateVo.getUserCommentId());
        commentEntity.setRating(updateVo.getRating());
        commentEntity.setDealOpinion(updateVo.getDealOpinion());
        commentEntity.setAdditionalInformation(updateVo.getAdditionalInformation());
        commentEntity.setLastChangeUser(userId);
        commentEntity.setUpdateTime(date);
        return this.userCommentMapper.updateByPrimaryKey(commentEntity);
    }

    /**
     * 逻辑删除
     *
     * @param userCommentId
     * @param userId
     * @return int
     * <AUTHOR>
     * @date 2019/12/30
     */
    public int delUserComment(Long userCommentId, Long userId) {
        UserCommentEntity commentEntity = new UserCommentEntity();
        Date date = new Date(System.currentTimeMillis());
        commentEntity.setUserCommentId(userCommentId);
        commentEntity.setStatus(Constant.DEL);
        commentEntity.setLastChangeUser(userId);
        commentEntity.setUpdateTime(date);
        return this.userCommentMapper.updateByPrimaryKeySelective(commentEntity);
    }

    /**
     * 查询在党员和年代的记录数量
     *
     * @param userId
     * @param year
     * @return int
     * <AUTHOR>
     * @date 2020/1/3
     */
    public int getCommentByYearAndUser(Long userId, Integer year, Long regionId) {
        Example example = new Example(UserCommentEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("reviewYear", year);
        criteria.andEqualTo("status", Constant.YES);
        criteria.andEqualTo("regionId", regionId);
        return this.userCommentMapper.selectCountByExample(example);
    }

    /**
     * 根据主键查询民主评议实体
     *
     * @param userCommentId
     * @return com.goodsogood.ows.model.db.UserCommentEntity
     * <AUTHOR>
     * @date 2019/12/30
     */
    public UserCommentEntity getUserComment(Long userCommentId) {
        Example example = new Example(UserCommentEntity.class);
        example.createCriteria().andEqualTo("userCommentId", userCommentId)
                .andEqualTo("status", Constant.YES);
        UserCommentEntity userCommentEntity = this.userCommentMapper.selectOneByExample(example);
        return userCommentEntity;
    }

    /**
     * 同步修改组织信息
     *
     * @param organizationBase
     * @return int
     * <AUTHOR>
     * @date 2020/1/8
     */
    public int updateOrgInfo(OrganizationBase organizationBase) {
        // 封装查询条件
        Example example = new Example(UserCommentEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", organizationBase.getOrganizationId());
        final List<UserCommentEntity> entities = this.userCommentMapper.selectByExample(example);
        entities.forEach(entity -> {
            entity.setOrgName(organizationBase.getName());
            entity.setOrgLevel(organizationBase.getOrgLevel());
            entity.setLastChangeUser(-111L);
            entity.setUpdateTime(new Date());
            this.userCommentMapper.updateByPrimaryKey(entity);
        });
       return entities.size();
    }

    /**
     * 同步修改用户信息
     *
     * @param userChangeForm
     * @return int
     * <AUTHOR>
     * @date 2020/1/8
     */
    public int updateUserInfo(UserChangeForm userChangeForm) {
        // 封装查询条件
        Example example = new Example(UserCommentEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userChangeForm.getUserId());
        final List<UserCommentEntity> entities = this.userCommentMapper.selectByExample(example);
        OrganizationBase org = this.findOrgById(userChangeForm.getNewOrgId());
        entities.forEach(entity -> {
            entity.setOrgId(userChangeForm.getNewOrgId());
            entity.setOrgName(userChangeForm.getNewOrgName());
            entity.setOrgLevel(org.getOrgLevel());
            entity.setUserName(userChangeForm.getUserName());
            entity.setLastChangeUser(-222L);
            entity.setUpdateTime(new Date());
            this.userCommentMapper.updateByPrimaryKey(entity);
        });
        return entities.size();
    }
}
