package com.goodsogood.ows.service;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.model.db.MeetingOrgChangeLogEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * MeetingPlan 异步处理类
 *
 * <AUTHOR>
 * @create 2018-10-23 13:55
 */
@Service
@Log4j2
public class MeetingPlanAsyncService {


    private final MeetingPlanService meetingPlanService;

    public MeetingPlanAsyncService(MeetingPlanService meetingPlanService) {
        this.meetingPlanService = meetingPlanService;
    }


    /**
     * 组织变更 更新活动类型关联组织
     *
     * @param meetingOrgChangeLogEntity 组织变更信息
     * @param ssLog                     日志信息
     */
    @Async
    public void updateMeetingPlanOreByOrgChangeAsync(
            HeaderHelper.SysHeader sysHeader,
            MeetingOrgChangeLogEntity meetingOrgChangeLogEntity, LogAspectHelper.SSLog ssLog) {
        // 进入异步方法，重新设置下上下文
        LogHelper.asyncLog(ssLog, () -> {
            // 执行更新操作
            this.meetingPlanService.updateMeetingPlanOreByOrgChangeLock(sysHeader, meetingOrgChangeLogEntity);
        });
    }
}
