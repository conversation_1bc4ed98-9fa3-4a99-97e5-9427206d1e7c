package com.goodsogood.ows.service

import com.github.pagehelper.Page
import com.goodsogood.ows.common.Constant
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.mapper.MeetingCommentApproveMapper
import com.goodsogood.ows.model.db.CommentRatingEnum
import com.goodsogood.ows.model.db.CommentStatusEnum
import com.goodsogood.ows.model.db.MeetingCommentApproveEntity
import com.goodsogood.ows.model.db.MeetingCommentEntity
import com.goodsogood.ows.model.vo.ApproveCommentInfoForm
import com.goodsogood.ows.model.vo.CommentApproveQueryForm
import com.goodsogood.ows.model.vo.GloryType
import com.goodsogood.ows.model.vo.Result
import org.apache.ibatis.session.RowBounds
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import tk.mybatis.mapper.entity.Example
import java.time.LocalDateTime
import java.util.*


@Service
class CommentApproveService(@Autowired val errors: Errors,
                            @Autowired val thirdService: ThirdService,
                            @Autowired val commentService: CommentService,
                            @Autowired val openService: OpenService,
                            @Autowired val commentMemberComplexService: CommentMemberComplexService,
                            @Autowired val commentApproveMapper: MeetingCommentApproveMapper,
                            @Autowired val simpleApplicationConfigHelper: SimpleApplicationConfigHelper) {

    private val log = LoggerFactory.getLogger(CommentApproveService::class.java)

    fun selectApproveList(form: CommentApproveQueryForm, headers: HttpHeaders) : MutableList<MeetingCommentEntity> {
        val header = HeaderHelper.buildMyHeader(headers)
        // 判断当前登录组织的类型
        val headerOrgInfo = thirdService.findOrgInfoByOrgId(header.oid)
        var orgId = header.oid
        val orgData = simpleApplicationConfigHelper.getOrgByRegionId(header.regionId)
        if (form.orgId != null) {
            orgId = form.orgId
        }
        val commentList: Page<MeetingCommentEntity> = if (headerOrgInfo?.parentId == orgData.orgId) {
            // 市局直属下级 - 审定
            commentService.selectCommentPage(form.year, form.status, orgId, 1, 3, form.page, form.pageSize)
        } else {
            commentService.selectCommentPage(form.year, form.status, orgId, 1, 2, form.page, form.pageSize)
        }
        // 判断操作类型
        commentList.forEach { comment ->
            if (comment.status == CommentStatusEnum.REVIEWING.key // 状态待审查
                && (comment.orgLevel?.contains("-${header.oid}-") == true) // 层级关系里包含当前操作组织
            ) {
                // 审查
                comment.operate = 1
            } else if(comment.status == CommentStatusEnum.PENGDING.key // 状态待审定
                && ((comment.orgLevel?.contains("-${header.oid}-") == true) || comment.orgId == header.oid) // 层级关系里包含当前操作组织或者登录组织时当前组织
                && headerOrgInfo?.parentId == orgData.orgId // 当前操作组织的上级组织为顶级组织
            ) {
                // 审定
                comment.operate = 2
            } else if(comment.status == CommentStatusEnum.APPROVED.key
                && ((comment.orgLevel?.contains("-${header.oid}-") == true) || comment.orgId == header.oid) // 层级关系里包含当前操作组织或者登录组织时当前组织
                && headerOrgInfo?.parentId == orgData.orgId // 当前操作组织的上级组织为顶级组织
            ) {
                // 可以修订
                comment.operate = 3
            }
        }
        return commentList
    }

    /**
     * 审核民主评议
     * @param form
     * @param headers
     */
    @Transactional
    fun approveComment(form: ApproveCommentInfoForm, headers: HttpHeaders) : String  {
        log.debug("审核数据 -> [${form}]")
        val header = HeaderHelper.buildMyHeader(headers)
        val comment = commentService.getMeetingComment(form.commentId)
        if (comment != null) {
            val status = comment.status
            log.debug("当前民主评议[${form.commentId}] 状态[${CommentStatusEnum.getCommentStatusEnum(status)?.value}]")
            // 判断状态为待审查还是待审定
            if (status == CommentStatusEnum.REVIEWING.key) {
                // 待审查则需要判断上级组织
                if (comment.parentId == header.oid) {
                    //审查
                    saveApproveData(comment, form, 1, headers)
                } else {
                    throw ApiException(
                        "userId:[${header.userId}] orgId:[${header.oid}] 不是该民主评议[${form.commentId}}]的上级组织，不能审查",
                        Result<Any>(errors, 2009, HttpStatus.OK.value(), "您没有权限审查该民主评议"))
                }
            } else if (status == CommentStatusEnum.PENGDING.key) {
                // 待审定
                if (comment.orgId == header.oid || comment.orgLevel?.contains("-" + header.oid + "-") == true) {
                    // 审定组织需要是组织层级关系中
                    val orgInfo = thirdService.findOrgInfoByOrgId(header.oid)
                    val orgData = simpleApplicationConfigHelper.getOrgByRegionId(header.regionId)
                    // 判断当前组织是在市局直属下级
                    if (orgInfo?.parentId == orgData.orgId) {
                        // 审定
                        saveApproveData(comment, form, 2, headers)
                    } else {
                        throw ApiException(
                            "userId:[${header.userId}] orgId:[${header.oid}] 不是市局直属下级，不能审定",
                            Result<Any>(errors, 2009, HttpStatus.OK.value(), "您没有权限审定该民主评议"))
                    }
                } else {
                    throw ApiException(
                        "userId:[${header.userId}] orgId:[${header.oid}] 不是该民主评议[${form.commentId}]的上级组织，不能审定",
                        Result<Any>(errors, 2009, HttpStatus.OK.value(), "您没有权限审定该民主评议"))
                }
            } else {
                throw ApiException(
                    "userId:[${header.userId}] 该民主评议[${form.commentId}}]的状态[${CommentStatusEnum.getCommentStatusEnum(status)?.value}]，不能审定",
                    Result<Any>(errors, 2009, HttpStatus.OK.value(), "该民主评议状态不能进行审核"))
            }

        }
        return Constant.SUCCESS
    }

    /**
     *  保存审批数据
     *  @param comment
     *  @param form
     *  @param status 1-审查，2-审定
     */
    @Transactional
    fun saveApproveData(comment: MeetingCommentEntity, form: ApproveCommentInfoForm, status: Int, headers: HttpHeaders) {
        val header = HeaderHelper.buildMyHeader(headers)
        // 写入审批表
        val approveEntity = MeetingCommentApproveEntity()
        approveEntity.commentId = form.commentId
        approveEntity.approveUser = header.userId
        approveEntity.content = form.content
        approveEntity.createTime = LocalDateTime.now()
        approveEntity.createUser = header.userId
        if (form.type == 1) {
            // 审核通过
            approveEntity.approveStatus = if (status == 1) 1 else 3
            addMyGlory(form.commentId, headers)
            // 主表
            comment.status = if (status == 1) CommentStatusEnum.PENGDING.key else CommentStatusEnum.APPROVED.key
        } else {
            // 审核退回
            approveEntity.approveStatus = if (status == 1) 2 else 4
            // 主表
            comment.status = if (status == 1) CommentStatusEnum.REVIEW_FAILED.key else CommentStatusEnum.PEND_FAILED.key
        }
        comment.updateTime = LocalDateTime.now()
        comment.lastChangeUser = header.userId
        // 插入审批表
        commentApproveMapper.insert(approveEntity)
        commentService.insertOrUpdateComment(comment)
        // 生成登记表
        commentService.createGradeFormByComment(form.commentId, headers)
        // 创建自评互评表
        commentService.createAppraisal(form.commentId!!, headers)
    }

    /**
     * 获取审核详情
     */
    fun getApprove(commentId: Long) : ApproveCommentInfoForm {
        val ex = Example(MeetingCommentApproveEntity::class.java)
        ex.createCriteria().andEqualTo("commentId", commentId)
        ex.orderBy("createTime").desc()
        val rowBounds = RowBounds(0, 1)
        val approveList = commentApproveMapper.selectByExampleAndRowBounds(ex, rowBounds)
        return if (approveList.size == 1)
            ApproveCommentInfoForm(approveList[0].commentId, approveList[0].approveStatus, approveList[0].content)
        else
            ApproveCommentInfoForm()
    }

    fun addMyGlory(commentId: Long? = null, headers: HttpHeaders) {
        if (commentId != null) {
            val memberList = commentService.getCommentMemberList(commentId)
            memberList.forEach {
                it.commentMemberId?.let { id ->
                    val memberComplex = commentMemberComplexService.selectMemberComplex(id)
                    if (memberComplex.complexRating == CommentRatingEnum.EXCELLENT.key) {
                        openService.addMyGlory(
                            it.userId,
                            GloryType.COMMENT.key,
                            memberComplex.createTime,
                            "民主评议荣获优秀等次",
                            "在${it.year}年党员民主评议中荣获优秀等次",
                            null,
                            headers
                        )
                    }
                }
            }
        }
    }
}