/*
package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.StatisticsMapper;
import com.goodsogood.ows.model.vo.SasUserOrgLifeForm;
import com.goodsogood.ows.model.vo.StatisticsMeetingForm;
import com.goodsogood.ows.model.vo.StatisticsUserOrgLifeForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;

*/
/**
 * @program: ows-meeting
 * @description: 统计服务services
 * @author: Mr.<PERSON>
 * @create: 2019-04-22 11:49
 **//*

@Service
@Log4j2
public class StatisticsService {

    private  final StatisticsMapper statisticsMapper;

    public  StatisticsService(StatisticsMapper statisticsMapper){
        this.statisticsMapper=statisticsMapper;
    }

    public List<StatisticsMeetingForm> statisticsOrganizeInfo(Long orgId ,String queryCode,String queryStatus) {
        return statisticsMapper.statisticsOrganizeInfo(orgId,queryCode,queryStatus);
    }

    public List<StatisticsMeetingForm> statisticsOrganizeUserInfo(Long orgId, Long userId, String queryCode, String signStatus) {
        return statisticsMapper.statisticsOrganizeUserInfo(orgId, userId, queryCode, signStatus);
    }

    */
/**
 * 统计用户每月参加各类型的组织生活次数
 *
 * @param sasUserOrgLifeForm 统计条件
 * @return List<StatisticsMeetingForm>
 *//*

    public List<StatisticsUserOrgLifeForm> sasUserOrgLife(SasUserOrgLifeForm sasUserOrgLifeForm) {
        return statisticsMapper.sasUserOrgLife(sasUserOrgLifeForm);
    }
}
*/
