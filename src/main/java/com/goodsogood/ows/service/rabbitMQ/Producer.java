package com.goodsogood.ows.service.rabbitMQ;

import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * RabbitMQ生产者服务类
 *
 * <AUTHOR> tc
 * @date 2020/12/08
 */
@Component
@Log4j2
public class Producer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(String exchangeName, String bingdingKey, String messageBody) {
        log.debug("进入Rabbit队列 exchangeName={} bingdingKey={} msg ={}", exchangeName, bingdingKey, messageBody);
        try {
            rabbitTemplate.convertAndSend(exchangeName, bingdingKey, messageBody);
        } catch (Exception e) {
            log.debug("进入Rabbit队列断点一,{},{}", e, e.getMessage());
            log.error("进入Rabbit队列断点二,{},{}", e, e.getMessage());
        }
    }
}
