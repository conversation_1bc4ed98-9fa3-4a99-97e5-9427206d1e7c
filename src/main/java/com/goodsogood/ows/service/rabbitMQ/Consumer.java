package com.goodsogood.ows.service.rabbitMQ;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.Config;
import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.model.vo.MeetingScoreMQVo;
import com.goodsogood.ows.service.MeetingScoreService;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Collections;
import java.util.Map;

/**
 * RabbitMQ消费者服务类
 *
 * <AUTHOR> tc
 * @date 2020/12/9
 */
@Component
@Log4j2
public class Consumer {
    private final MeetingScoreService meetingScoreService;
    private final LogHelper logHelper;
    @Autowired
    public Consumer(MeetingScoreService meetingScoreService, LogHelper logHelper) {
        this.meetingScoreService = meetingScoreService;
        this.logHelper = logHelper;
    }

    /**
     * 用户积分队列消费者
     * @param msg
     * @param channel
     * @param message
     */
    @RabbitListener(queues = "${rabbitmq-queue-sign.meetingScore}")
    public void meetingScore(String msg,Channel channel, Message message) throws IOException {
        try{
        LogAspectHelper helper = LogAspectHelper.logAspectHelperBuilder();
        LogAspectHelper.SSLog ssLog = LogHelper.getSSlog(helper, "[组织生活积分操作出队列]");
        helper.reSetContext(ssLog);
            log.debug("组织生活积分操作出队列1 msg ={}",msg);
            MeetingScoreMQVo dto = JsonUtils.fromJson(msg,MeetingScoreMQVo.class);
            //根据类型判断处理方法
            if(Config.MeetingScoreConf.MQ_ADD_MEETING_SCORE.equals(dto.getType())){
                meetingScoreService.addMeetingScore(dto.getRegionId(),dto.getMeeting());
            }else if(Config.MeetingScoreConf.MQ_REDUCE_MEETING_SCORE.equals(dto.getType())){
                meetingScoreService.reduceMeetingScore(dto.getRegionId(),dto.getMeeting(),dto.getIsCancel());
            }else if(Config.MeetingScoreConf.MQ_USER_SCORE_ADD.equals(dto.getType())){
                Map<String,String> p  = dto.getParam();
                meetingScoreService.userScoreAdd(Long.valueOf(p.get("regionId")),Long.valueOf(p.get("meetingId")),
                        Integer.valueOf(p.get("typeId")),Integer.valueOf(p.get("1")), Collections.singletonList(Long.valueOf(p.get("userId"))),
                        DateUtils.stringToDate(p.get("queryDate"),"yyyy-MM-dd"),Integer.valueOf(p.get("isRollBack")));
            }
        }catch(Exception e){
            log.error("<组织生活积分操作> 报错！",e);
        }
    }
}
