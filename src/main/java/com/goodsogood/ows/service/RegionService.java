package com.goodsogood.ows.service;

import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.exception.ApiException;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 区县RegionService
 *
 * <AUTHOR>
 */
@Service
@Log4j2
public class RegionService {
  private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;

  public RegionService(SimpleApplicationConfigHelper simpleApplicationConfigHelper) {
    this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
  }


  /**
   * 区县id绑定的工委组织id
   *
   * @param regionId 区县id
   * @return 组织id
   */
  public Long bindingOrgId(Long regionId) {
    Region.OrgData orgData = simpleApplicationConfigHelper.getOrgByRegionId(regionId);
    if (orgData == null) {
      throw new ApiException("获取区县信息失败");
    }
    return orgData.getOrgId();
  }

  /**
   * 所有区县信息
   */
  public List<Region.RegionData> getRegions() {
    Region region = region();
    if (region == null) {
      return new ArrayList<>();
    }
    return region.getRegions();
  }

  /**
   * 区县配置基本信息
   *
   * @return 区县相关信息
   */
  public Region region() {
    Region region = simpleApplicationConfigHelper.getRegions();
    if (region == null) {
      throw new ApiException("获取区县信息失败");
    }
    return region;
  }

  /**
   * 区县相关信息
   *
   * @param regionId 区县id
   * @return 区县相关信息
   */
  public Region.RegionData regionData(Long regionId) {
    Region.RegionData regionData = simpleApplicationConfigHelper.getRegionByRegionId(regionId);

    if (regionData == null) {
      throw new ApiException("获取区县信息失败");
    }
    return regionData;
  }
}
