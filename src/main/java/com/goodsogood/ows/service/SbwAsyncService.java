package com.goodsogood.ows.service;

import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.SbwShiftTaskMapper;
import com.goodsogood.ows.mapper.SbwTaskFlowMapper;
import com.goodsogood.ows.mapper.SbwTaskMapper;
import com.goodsogood.ows.mapper.SbwTaskOrgMapper;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.SbwShiftTaskFrom;
import com.goodsogood.ows.utils.BeanUtil;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 南岸区网信办异步service
 */
@Service
@Log4j2
public class SbwAsyncService {

    private final SbwTaskFlowMapper sbwTaskFlowMapper;
    private final SbwTaskOrgMapper sbwTaskOrgMapper;
    private final SbwShiftTaskMapper sbwShiftTaskMapper;
    private final SbwTaskMapper sbwTaskMapper;

    @Autowired
    public SbwAsyncService(SbwTaskFlowMapper sbwTaskFlowMapper, SbwTaskOrgMapper sbwTaskOrgMapper, SbwShiftTaskMapper sbwShiftTaskMapper, SbwTaskMapper sbwTaskMapper) {
        this.sbwTaskFlowMapper = sbwTaskFlowMapper;
        this.sbwTaskOrgMapper = sbwTaskOrgMapper;
        this.sbwShiftTaskMapper = sbwShiftTaskMapper;
        this.sbwTaskMapper = sbwTaskMapper;
    }

    /**
     * 插入操作流水
     *
     * @param entity
     */
    @Async("sbwAsync")
    public void recordFlow(SbwHandleEntity entity, Date date) {
        SbwTaskFlowEntity flow = new SbwTaskFlowEntity();
        flow.setCreateTime(date);
        flow.setTaskId(entity.getTaskId());
        flow.setRegionId(entity.getRegionId());
        flow.setOrgId(entity.getOrgId());
        flow.setUserId(entity.getUserId());
        flow.setOperateOrgId(entity.getUserOrgId());
        flow.setOperateOrgName(entity.getUserOrgName());
        flow.setType(entity.getHandleStatus());
        if (entity.getHandleStatus() > 1) {
            flow.setContent(entity.getHandleComment());
        }
        sbwTaskFlowMapper.insertUseGeneratedKeys(flow);
    }


    /**
     * 代办任务插入操作流水
     *
     * @param header
     * @param taskId
     * @param handle
     * @param date
     */
    @Async("sbwAsync")
    public void shiftFlow(HeaderHelper.SysHeader header, Long taskId, Integer handle, String content, Date date) {
        SbwTaskFlowEntity flow = new SbwTaskFlowEntity();
        flow.setCreateTime(date);
        flow.setTaskId(taskId);
        flow.setRegionId(header.getRegionId());
        flow.setOrgId(header.getOid());
        flow.setUserId(header.getUserId());
        flow.setOperateOrgId(header.getOid());
        flow.setOperateOrgName(header.getOrgName());
        flow.setType(handle);
        if (handle.equals(Constant.SHIFT_FLOW_NOT_HANDLE)) {
            flow.setContent(content);
        } else if (handle.equals(1)) {
            flow.setType(-1);
        }
        sbwTaskFlowMapper.insertUseGeneratedKeys(flow);
    }

    /**
     * 代办任务同步状态
     *
     * @param taskId
     * @param handle
     */
    @Async("sbwAsync")
    public void shiftStatus(Long taskId, Integer handle) {
        Integer status;
        if (handle.equals(Constant.SHIFT_FLOW_NOT_HANDLE)) {
            status = Constant.SHIFT_NOT;
        } else if (!sbwShiftTaskMapper.timeStatus(taskId).equals(0)) {
            status = Constant.SHIFT_END;
        } else {
            status = Constant.SHIFT_HANDLING;
        }
        Example example = new Example(SbwShiftTaskEntity.class);
        example.createCriteria().andEqualTo("taskId", taskId);
        SbwShiftTaskEntity entity = new SbwShiftTaskEntity();
        entity.setStatus(status);
        sbwShiftTaskMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 执行操作同步状态
     *
     * @param entity
     * @param date
     * @param
     */
    @Async("sbwAsync")
    public void updateTaskOrgTypeStatus(SbwHandleEntity entity, Date date) {
        SbwTaskOrgEntity taskOrg = new SbwTaskOrgEntity();
        taskOrg.setUpdateTime(date);
        taskOrg.setType(entity.getHandleStatus());
        taskOrg.setTaskId(entity.getTaskId());
        taskOrg.setOrgId(entity.getOrgId());
        taskOrg.setRegionId(entity.getRegionId());
//        if (entity.getHandleStatus().equals(Constant.HANDLE_TYPE_SR)){
//            taskOrg.setStatus(Constant.TASK_STATUS_REFUSE);
//        }
        Example example = new Example(SbwTaskOrgEntity.class);
        example.createCriteria().andEqualTo("taskId", taskOrg.getTaskId())
                .andEqualTo("regionId", taskOrg.getRegionId())
                .andEqualTo("orgId", taskOrg.getOrgId());
        sbwTaskOrgMapper.updateByExampleSelective(taskOrg, example);
    }

    /**
     * 任务发布同步状态
     *
     * @param entity
     */
    @Async("sbwAsync")
    public void TaskStatus(SbwTaskEntity entity) {
        sbwTaskOrgMapper.updateTaskStatus(entity);
    }

    /**
     * 任务发布
     */
    @Async("sbwAsync")
    public Integer TaskTitle(Long taskId, Long regionId, String title, Date beginTime, Date endTime, Integer status) {
        log.debug("编辑任务成功");
        return sbwTaskOrgMapper.updateTaskTitle(taskId, regionId, title, beginTime, endTime, status);
    }

    /**
     * 设置已阅读
     */
    @Async("sbwAsync")
    public void isRead(Long oid, Long taskId, Integer flag) {
        //网信办已读待办单
        if (flag == 1) {
            sbwShiftTaskMapper.isRead(taskId);
        }
        //接收组织已读
        if (flag == 2) {
            sbwTaskOrgMapper.isRead(oid, taskId);
        }
    }

    /**
     * 同步代办任务到任务
     */
    @Async("sbwAsync")
    public void task(SbwShiftTaskEntity shift, Long shiftId) {
        SbwTaskEntity entity = new SbwTaskEntity();
        BeanUtils.copyProperties(shift, entity);
        Long oid = 10545L;
        String oName = "南岸区委网信办";
        entity.setStatus(-1);
        entity.setOrgId(oid);
        entity.setOrgName(oName);
        entity.setVerifyOrgId(oid);
        entity.setVerifyOrgName(oName);
        entity.setTaskType(Constant.TASK_PROXY_TYPE);
        fileGet(entity, shift);
        sbwTaskMapper.insertUseGeneratedKeys(entity);
        //将任务id同步到代办任务
        shift.setTaskId(entity.getTaskId());
        shift.setShiftTaskId(shiftId);
        sbwShiftTaskMapper.updateByPrimaryKeySelective(shift);
        //流水
        SbwTaskFlowEntity flow = new SbwTaskFlowEntity();
        flow.setCreateTime(entity.getCreateTime());
        flow.setTaskId(entity.getTaskId());
        flow.setRegionId(shift.getRegionId());
        flow.setOrgId(shift.getOrgId());
        flow.setUserId(entity.getCreateUser());
        flow.setOperateOrgId(shift.getOrgId());
        flow.setOperateOrgName(shift.getOrgName());
        flow.setType(Constant.SHIFT_FLOW_SUBMIT);
        sbwTaskFlowMapper.insertUseGeneratedKeys(flow);
    }

    /**
     * 同步代办任务到任务(代办提交后修改)
     */
    @Async("sbwAsync")
    public void taskUpdate(SbwShiftTaskEntity shift) {
        SbwTaskEntity entity = sbwTaskMapper.selectByPrimaryKey(shift.getTaskId());
        if (ObjectUtils.isEmpty(entity)) {
            return;
        }
        entity.setTypeId(shift.getTypeId());
        entity.setTitle(shift.getTitle());
        entity.setNumber(shift.getNumber());
        entity.setBeginTime(shift.getBeginTime());
        entity.setEndTime(shift.getEndTime());
        entity.setTimeType(shift.getTimeType());
        entity.setSource(shift.getSource());
        entity.setContent(shift.getContent());
        entity.setRemark(shift.getRemark());
        entity.setUpdateTime(shift.getUpdateTime());
        fileGet(entity, shift);
        log.debug("待办任务同步修改任务:异步开始->{}", entity);
        sbwTaskMapper.updateByPrimaryKey(entity);
        log.debug("待办任务同步修改任务:异步结束->{}", entity);
    }

    /**
     * 提取文件信息封装
     *
     * @param entity
     * @param shift
     */
    public void fileGet(SbwTaskEntity entity, SbwShiftTaskEntity shift) {
        if (StringUtils.isNotBlank(shift.getFileJson())) {
            List<SbwShiftTaskFrom.file> list = (List<SbwShiftTaskFrom.file>) JsonUtils.fromJson(shift.getFileJson(), List.class, SbwShiftTaskFrom.file.class);
            entity.setFileId(list.stream().map(x -> x.getFileId().toString()).collect(Collectors.joining(",")));
            entity.setFilename(list.stream().map(SbwShiftTaskFrom.file::getFileName).collect(Collectors.joining(",")));
        } else {
            entity.setFileId(null);
            entity.setFilename(null);
        }
    }

    /**
     * 编辑时清空待办单信息
     *
     * @param id
     * @param flag 1:更新转办单  2:更新待办单
     */
    @Async("sbwAsync")
    public void updateFile(Long id, Integer flag) {
        switch (flag) {
            case 1:
                sbwTaskMapper.updateFile(id);
                break;
            case 2:
                sbwTaskMapper.updateShiftFile(id);
                break;
            case 3:
                sbwTaskMapper.updateHandleFile(id);
                break;
        }
    }
}
