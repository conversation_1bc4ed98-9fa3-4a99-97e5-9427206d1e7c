package com.goodsogood.ows.service;

import com.goodsogood.ows.common.RedisConstant;
import com.goodsogood.ows.mapper.MeetingTaskMapper;
import com.goodsogood.ows.model.db.MeetingTaskEntity;
import com.goodsogood.ows.model.vo.MeetingTypeTaskListForm;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;

/**
 * <AUTHOR>
 * @create 2018-10-24 09:42
 */
@Service
@Log4j2
public class MeetingTaskRedisService {


    private final MeetingTaskMapper mapper;
    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public MeetingTaskRedisService(
            MeetingTaskMapper mapper,
            StringRedisTemplate stringRedisTemplate) {
        this.mapper = mapper;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 未完成任务数量统计（不包括逾期）
     *
     * @param oid 组织id
     */
    int undoneCountByRedis(Long oid) {
        if (oid == null) {
            return 0;
        }
        String redisTaskKey = undoneCountByRedisKey(oid);
        Object redisTaskCount = stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).get(redisTaskKey);
        // 缓存中有值，返回缓存值
        if (redisTaskCount != null) {
            return Integer.valueOf((String) redisTaskCount);
        }
        return undoneCountAndRedisByDb(oid);

    }

    /**
     * 未完成任务数量统计（不包括逾期）
     *
     * @param oid 组织id
     */
    private int undoneCountAndRedisByDb(Long oid) {
        if (oid == null) {
            return 0;
        }
        Integer count = undoneTaskCount(oid);
        // 存入缓存，有效时间1小时
        String redisKey = undoneCountByRedisKey(oid);
        stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).put(redisKey, count.toString());
        return count;

    }

    private Integer undoneTaskCount(Long oid) {
        MeetingTypeTaskListForm meetingTypeListForm = new MeetingTypeTaskListForm();
        meetingTypeListForm.setStatus((short) 1);
        meetingTypeListForm.setOrgIds(Collections.singletonList(oid));
        return taskCount(meetingTypeListForm);
    }

    /**
     * redis key
     *
     * @param oid 组织id
     * @return key
     */
    String undoneCountByRedisKey(Long oid) {
        return RedisConstant.MEETING_TASK_UNDONE_COUNT + oid;
    }

    /**
     * 未完成活动任务总数
     */
    public int taskCount(MeetingTypeTaskListForm meetingTypeListForm) {
        Example example = new Example(MeetingTaskEntity.class);
        Example.Criteria criteria =
                example
                        .createCriteria()
                        .andIn("orgId", meetingTypeListForm.getOrgIds())
                        .andEqualTo("status", meetingTypeListForm.getStatus());
        if (meetingTypeListForm.getStatus() == 1) { // 未完成 不包括逾期
            criteria.andGreaterThanOrEqualTo("endTime", DateUtils.getToday());
        } else if (meetingTypeListForm.getStatus() == 3) { // 逾期
            criteria.andLessThan("endTime", DateUtils.getToday());
        }
        return mapper.selectCountByExample(example);
    }

}
