package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.TopicLogFileMapper;
import com.goodsogood.ows.model.db.TopicLogFileEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-29 18:17
 **/
@Service
@Log4j2
public class TopicLogFileService {

    private final TopicLogFileMapper topicLogFileMapper;

    @Autowired
    public TopicLogFileService(TopicLogFileMapper topicLogFileMapper) {
        this.topicLogFileMapper = topicLogFileMapper;
    }

    /**
     * 根据回答的日志id 获取这个回答的附件
     * @param topicLogId
     * @return
     */
    public List<TopicLogFileEntity> getTopicLogFile(Long topicLogId) {
        if(topicLogId == null || topicLogId == 0) {
            return  new ArrayList<>();
        }
        Example example = new Example(TopicLogFileEntity.class);
        example.createCriteria().andEqualTo("topicLogId", topicLogId);
        return this.topicLogFileMapper.selectByExample(example);
    }

}