package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.TopicOptsMapper;
import com.goodsogood.ows.model.db.TopicOptsEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-22 14:16
 **/
@Service
@Log4j2
public class TopicOptsService {

    private final TopicOptsMapper topicOptsMapper;

    @Autowired
    public TopicOptsService(TopicOptsMapper topicOptsMapper) {
        this.topicOptsMapper = topicOptsMapper;
    }

    /**
     * 根据任务id获取任务的所有子选项
     * @param topicId
     * @return
     */
    public List<TopicOptsEntity> getTopicOptsList(Long topicId) {
        if(topicId == null) {
            return new ArrayList<>();
        }
        Example example = new Example(TopicOptsEntity.class);
        example.createCriteria().andEqualTo("topicId", topicId);
        return this.topicOptsMapper.selectByExample(example);
    }

    /**
     * 根据内容id删除选项
     * @param contentId
     */
    public void deleteByContentId(Long contentId) {
        Example example = new Example(TopicOptsEntity.class);
        example.createCriteria().andEqualTo("contentId", contentId);
        this.topicOptsMapper.deleteByExample(example);
    }

}