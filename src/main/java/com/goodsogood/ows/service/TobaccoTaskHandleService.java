package com.goodsogood.ows.service;

import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.TobaccoTaskHandleMapper;
import com.goodsogood.ows.model.db.TobaccoTaskHandleEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.TobaccoTaskHandleForm;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 烟草任务填报/审核service
 * <AUTHOR>
 * @date 2021.08.24
 */
@Service
@Log4j2
public class TobaccoTaskHandleService {

    private final TobaccoTaskHandleMapper tobaccoTaskHandleMapper;
    private final TobaccoAsyncService tobaccoAsyncService;
    private final Errors errors;

    @Autowired
    public TobaccoTaskHandleService(TobaccoTaskHandleMapper tobaccoTaskHandleMapper, TobaccoAsyncService tobaccoAsyncService, Errors errors) {
        this.tobaccoTaskHandleMapper = tobaccoTaskHandleMapper;
        this.tobaccoAsyncService = tobaccoAsyncService;
        this.errors = errors;
    }

    /**
     * 填报任务（提交）
     * @param header
     * @param form
     * @return
     */
    public Long fillTask(HeaderHelper.SysHeader header, TobaccoTaskHandleForm form){
        if (ObjectUtils.isEmpty(form.getTaskId()) || ObjectUtils.isEmpty(form.getAcceptScope())
                || ObjectUtils.isEmpty(form.getHandleContent())){
            log.error("缺少必填项");
            throw new ApiException("缺少必填项",new Result<>(errors,3002, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        Date date = new Date();
        TobaccoTaskHandleEntity entity = fillHandle(header,form,Constant.HANDLE_STATUS_FILL);
        entity.setUpdateTime(date);
        //入库
        if (ObjectUtils.isEmpty(form.getHandleId())){
            entity.setCreateTime(date);
            tobaccoTaskHandleMapper.insertUseGeneratedKeys(entity);
        }else {
            entity.setHandleId(form.getHandleId());
            tobaccoTaskHandleMapper.update(entity);
        }
        //异步同步
        tobaccoAsyncService.updateTaskOrgHandle(entity,form.getAcceptScope());
        //异步流水
        tobaccoAsyncService.taskFlow(entity);
        return entity.getHandleId();
    }

    /**
     * 填报任务（草稿）
     * @param header
     * @param form
     * @return
     */
    public Long fillTaskDraft(HeaderHelper.SysHeader header, TobaccoTaskHandleForm form){
        if (ObjectUtils.isEmpty(form.getTaskId()) || ObjectUtils.isEmpty(form.getAcceptScope())){
            log.error("缺少必填项");
            throw new ApiException("缺少必填项",new Result<>(errors,3002, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        Date date = new Date();
        TobaccoTaskHandleEntity entity = fillHandle(header,form,Constant.HANDLE_STATUS_DRAFT);
        entity.setUpdateTime(date);
        //入库
        if (ObjectUtils.isEmpty(form.getHandleId())){
            entity.setCreateTime(date);
            tobaccoTaskHandleMapper.insertUseGeneratedKeys(entity);
        }else {
            entity.setHandleId(form.getHandleId());
            tobaccoTaskHandleMapper.update(entity);
        }
        return entity.getHandleId();
    }


    /**
     * 任务执行审核（草稿）
     * @param header
     * @param form
     * @return
     */
    public Long handleVerifyDraft(HeaderHelper.SysHeader header, TobaccoTaskHandleForm form){
        if (ObjectUtils.isEmpty(form.getTaskId()) || ObjectUtils.isEmpty(form.getAcceptScope()) || ObjectUtils.isEmpty(form.getAcceptId())){
            log.error("缺少必填项");
            throw new ApiException("缺少必填项",new Result<>(errors,3002, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        Date date = new Date();
        TobaccoTaskHandleEntity entity = verifyHandle(header,form);
        entity.setUpdateTime(date);
        //入库
        if (ObjectUtils.isEmpty(form.getHandleId())){
            entity.setCreateTime(date);
            tobaccoTaskHandleMapper.insertUseGeneratedKeys(entity);
        }else {
            entity.setHandleId(form.getHandleId());
            tobaccoTaskHandleMapper.update(entity);
        }
        return entity.getHandleId();
    }


    /**
     * 任务执行审核
     * @param header
     * @param form
     * @return
     */
    public Long handleVerify(HeaderHelper.SysHeader header, TobaccoTaskHandleForm form){
        if (ObjectUtils.isEmpty(form.getTaskId()) || ObjectUtils.isEmpty(form.getAcceptScope()) || ObjectUtils.isEmpty(form.getAcceptId())
                || ObjectUtils.isEmpty(form.getHandleStatus()) || ObjectUtils.isEmpty(form.getHandleContent())){
            log.error("缺少必填项");
            throw new ApiException("缺少必填项",new Result<>(errors,3002, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        Date date = new Date();
        TobaccoTaskHandleEntity entity = verifyHandle(header,form);
        //入库
        entity.setUpdateTime(date);
        if (ObjectUtils.isEmpty(form.getHandleId())){
            entity.setCreateTime(date);
            tobaccoTaskHandleMapper.insertUseGeneratedKeys(entity);
        }else {
            entity.setHandleId(form.getHandleId());
            tobaccoTaskHandleMapper.update(entity);
        }
        //异步同步
        tobaccoAsyncService.updateTaskOrgHandle(entity,form.getAcceptScope());
        //异步流水
        tobaccoAsyncService.taskFlow(entity);
        return entity.getHandleId();
    }

    /**
     * 填报任务
     * @param header
     * @param form
     * @param handleStatus
     * @return
     */
    public TobaccoTaskHandleEntity fillHandle(HeaderHelper.SysHeader header, TobaccoTaskHandleForm form,Integer handleStatus){
        TobaccoTaskHandleEntity entity = new TobaccoTaskHandleEntity();
        //附件json
        if (CollectionUtils.isNotEmpty(form.getHandleFile())){
            entity.setHandleFile(JsonUtils.toJson(form.getHandleFile()));
        }
        //接收人员/组织
        if (form.getAcceptScope().equals(Constant.TASK_TO_ORG)){
            entity.setAcceptId(header.getOid());
        }else if (form.getAcceptScope().equals(Constant.TASK_TO_PERSON)){
            entity.setAcceptId(header.getUserId());
        }
        //内容
        entity.setHandleContent(form.getHandleContent());
        //填报方信息
        entity.setHandleUser(header.getUserName());
        entity.setHandleOrg(header.getOrgName());
        //其他信息
        if (CollectionUtils.isNotEmpty(form.getForwardOrg())){
            entity.setForwardJson(JsonUtils.toJson(form.getForwardOrg()));
        }
        entity.setRegionId(header.getRegionId());
        entity.setHandleStatus(handleStatus);
        entity.setFlag(Constant.HANDLE_Fill);
        entity.setTaskId(form.getTaskId());
        return entity;
    }

    /**
     * 任务审核
     * @param header
     * @param form
     * @return
     */
    public TobaccoTaskHandleEntity verifyHandle(HeaderHelper.SysHeader header, TobaccoTaskHandleForm form){
        TobaccoTaskHandleEntity entity = new TobaccoTaskHandleEntity();
        //接收人员/组织
        entity.setAcceptId(form.getAcceptId());
        //内容
        entity.setHandleStatus(form.getHandleStatus());
        entity.setHandleContent(form.getHandleContent());
        //审核方信息
        entity.setHandleUser(header.getUserName());
        entity.setHandleOrg(header.getOrgName());
        //其他信息
        entity.setRegionId(header.getRegionId());
        entity.setFlag(Constant.HANDLE_VERIFY);
        entity.setTaskId(form.getTaskId());
        return entity;
    }

}
