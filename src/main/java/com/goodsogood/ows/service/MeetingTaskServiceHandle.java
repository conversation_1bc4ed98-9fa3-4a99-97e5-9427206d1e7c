package com.goodsogood.ows.service;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingTaskMapper;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.MeetingAutoDistributionReqForm;
import com.goodsogood.ows.model.vo.MeetingTypeTaskListForm;
import com.goodsogood.ows.model.vo.OrgForm;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-24 09:42
 */
@Service
@Log4j2
public class MeetingTaskServiceHandle {
    private final UserCenterService userCenterService;
    private final MeetingPlanOrgService meetingPlanOrgService;
    private final MeetingTaskMapper mapper;

    public MeetingTaskServiceHandle(
            UserCenterService userCenterService,
            MeetingPlanOrgService meetingPlanOrgService,
            MeetingTaskMapper mapper) {
        this.userCenterService = userCenterService;
        this.meetingPlanOrgService = meetingPlanOrgService;
        this.mapper = mapper;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void createThisMeetingTask(HeaderHelper.SysHeader sysHeader, MeetingPlanEntity meetingPlanEntity, Date now, boolean updateExcuteOrgs) {
        log.debug("开始派发任务。任务id->{}", meetingPlanEntity.getMeetingPlanId());
        short executeType = meetingPlanEntity.getExecuteType();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);

        int year = calendar.get(Calendar.YEAR);
        // calendar.get 获取的月份从0开始，实际月份需要+1
        int month = calendar.get(Calendar.MONTH) + 1;
        // 时间段 未结束 派发任务
        if (executeType == 1
                && meetingPlanEntity.getEndTime() != null
                && DateUtils.gteToday(meetingPlanEntity.getEndTime())) {
            addMeetingTask(sysHeader, meetingPlanEntity, now, updateExcuteOrgs);
        }

//       按月份
//       1.开始年份小于当前年份
//       2.开始年份等于当前年份且开始月份小于等于当前月份
        else if (executeType == 2
                && (meetingPlanEntity.getStartYear() < year
                || (meetingPlanEntity.getStartYear() == year
                && meetingPlanEntity.getStartMonth() <= month))) {
            // 任务月份第一天
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
            meetingPlanEntity.setStartTime(calendar.getTime());
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            meetingPlanEntity.setEndTime(calendar.getTime());
            addMeetingTask(sysHeader, meetingPlanEntity, now, updateExcuteOrgs);
        }
//        按季度
//        1.开始年份小于当前年份
//        2.开始年份等于当前年份且开始季度小于等于当前季度
        else if (executeType == 3) {
            // 当前季度
            DateUtils.Quarter dateQuarter = DateUtils.getQuarter(now);
            if (meetingPlanEntity.getStartYear() < year
                    || (meetingPlanEntity.getStartYear() == year
                    && meetingPlanEntity.getStartQuarter() <= dateQuarter.getQuarter())) {
                // 任务开始时间，季度第一天
                meetingPlanEntity.setStartTime(DateUtils.firstDateOfQuarter(year, dateQuarter.getQuarter()));
                // 任务结束时间，季度最后一天
                meetingPlanEntity.setEndTime(DateUtils.lastDateOfQuarter(year, dateQuarter.getQuarter()));
                addMeetingTask(sysHeader, meetingPlanEntity, now, updateExcuteOrgs);
            }
        }
        // 年
        else if (executeType == 4 && meetingPlanEntity.getStartYear() <= year) {
            // 任务开始时间，年份第一天
            calendar.set(Calendar.MONTH, Calendar.JANUARY);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
            meetingPlanEntity.setStartTime(calendar.getTime());
            // 任务结束时间，年份最后一天
            calendar.set(Calendar.MONTH, Calendar.DECEMBER);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            meetingPlanEntity.setEndTime(calendar.getTime());
            addMeetingTask(sysHeader, meetingPlanEntity, now, updateExcuteOrgs);
        }
    }

    /**
     * 按组织生活批量添加任务
     */
    private void addMeetingTask(HeaderHelper.SysHeader sysHeader, MeetingPlanEntity meetingPlanEntity, Date now, boolean updateExcuteOrgs) {
        if (meetingPlanEntity == null || meetingPlanEntity.getStartTime() == null || meetingPlanEntity.getEndTime() == null) {
            return;
        }
        // 自动发放任务 更新执行组织
        if (updateExcuteOrgs) {
            MeetingPlanLimitEntity meetingPlanLimitEntity = meetingPlanEntity.getPlanLimitEntity();
            // 按条件查询
            List<Integer> orgTypeChild =
                    meetingPlanLimitEntity
                            .getLimitTypeEntityList()
                            .stream()
                            .map(MeetingPlanLimitTypeEntity::getOrgTypeChild)
                            .collect(Collectors.toList());
            MeetingAutoDistributionReqForm reqForm = new MeetingAutoDistributionReqForm();
            reqForm.setIsGroups(meetingPlanLimitEntity.getPartyGroup());
            reqForm.setIsPeriod(meetingPlanLimitEntity.getPeriod());
            reqForm.setIsRetire(meetingPlanLimitEntity.getIsRetire());
            reqForm.setOrgId(meetingPlanEntity.getOrgId());
            reqForm.setOrgTypeChilds(orgTypeChild);
            reqForm.setStartTime(meetingPlanEntity.getStartTime());
            reqForm.setEndTime(meetingPlanEntity.getEndTime());
            List<OrgForm> list = userCenterService.findOrgBy(sysHeader, reqForm);
            // 重置执行组织
            meetingPlanEntity.setExecuteOrgs(list);
            this.meetingPlanOrgService.updateExcuteOrgs(list, meetingPlanEntity.getMeetingPlanId());
        }

        if (meetingPlanEntity.getExecuteOrgs() == null) {
            log.warn("派发任务结束，执行组织为空！");
            return;
        }

        List<Long> executeOrgs =
                meetingPlanEntity
                        .getExecuteOrgs()
                        .stream()
                        .map(OrgForm::getOrgId)
                        .distinct()
                        .collect(Collectors.toList());
        List<MeetingRequireEntity> meetingTypes = meetingPlanEntity.getMeetingTypes();

        // 未指定活动类型 直接返回
        if (executeOrgs == null || executeOrgs.isEmpty() || meetingTypes == null || meetingTypes.isEmpty()) {
            log.warn("派发任务结束，活动类型为空！");
            return;
        }

        // 查询此计划未结束的相关任务
        MeetingTypeTaskListForm meetingTypeListForm = new MeetingTypeTaskListForm();
        meetingTypeListForm.setOrgIds(executeOrgs);
        meetingTypeListForm.setPlanId(meetingPlanEntity.getMeetingPlanId());
        meetingTypeListForm.setTypeIds(
                meetingTypes.stream().map(MeetingRequireEntity::getTypeId).collect(Collectors.toList()));
        meetingTypeListForm.setTag((short) 3);
        List<MeetingTaskEntity> list = mapper.findByForm(meetingTypeListForm);

        meetingTypes.forEach(
                meetingRequireEntity -> {
                    List<MeetingTaskEntity> meetingTaskEntities = new ArrayList<>();
                    executeOrgs.forEach(
                            orgId -> {
                                if (list == null || list.isEmpty() || list.stream().noneMatch(mt -> mt.getOrgId().equals(orgId) && mt.getTypeId().equals(meetingRequireEntity.getTypeId()))) {
                                    MeetingTaskEntity meetingTaskEntity = new MeetingTaskEntity();
                                    meetingTaskEntity.setMeetingPlanId(meetingRequireEntity.getMeetingPlanId());
                                    meetingTaskEntity.setTypeId(meetingRequireEntity.getTypeId());
                                    meetingTaskEntity.setOrgId(orgId);
                                    meetingTaskEntity.setEndTime(
                                            DateUtils.set59Second(meetingPlanEntity.getEndTime()));
                                    meetingTaskEntity.setMeetingRequireId(meetingRequireEntity.getMeetingRequireId());
                                    meetingTaskEntity.setCategoryId(meetingRequireEntity.getCategoryId());
                                    meetingTaskEntity.setPOrgId(meetingPlanEntity.getOrgId());
                                    meetingTaskEntity.setType(meetingRequireEntity.getType());
                                    meetingTaskEntity.setCategory(meetingRequireEntity.getCategory());
                                    meetingTaskEntity.setName(meetingPlanEntity.getName());
                                    meetingTaskEntity.setMeetingNum(0);
                                    meetingTaskEntity.setStatus((short) 1);
                                    meetingTaskEntity.setStartTime(meetingPlanEntity.getStartTime());
                                    meetingTaskEntity.setCreateTime(now);
                                    meetingTaskEntity.setCreateUser(meetingPlanEntity.getCreateUser());
                                    meetingTaskEntities.add(meetingTaskEntity);
                                    if (meetingTaskEntities.size() > 1000) {
                                        mapper.insertList(meetingTaskEntities);
                                        meetingTaskEntities.clear();
                                    }
                                }
                            });
                    if (!meetingTaskEntities.isEmpty()) {
                        mapper.insertList(meetingTaskEntities);
                    }
                });
    }

}
