package com.goodsogood.ows.service;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.MeetingWaitSignConfiguration;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingMapper;
import com.goodsogood.ows.mapper.MeetingUserMapper;
import com.goodsogood.ows.mapper.MeetingWaitSignMapper;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingUserEntity;
import com.goodsogood.ows.model.db.MeetingWaitSignEntity;
import com.goodsogood.ows.model.dto.MeetingWaitSignVo;
import com.goodsogood.ows.model.vo.PushRequest;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.utils.BeanUtil;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.RedisLockUtil;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 补学相关
 *
 * <AUTHOR> ruoyu
 * @date : 2021/11/10
 */
@Service
@Log4j2
public class MeetingWaitSignService {

    /**
     * 默认发送消息模板
     */
    private static final Long DEFAULT_TEMPLATE_ID = 159L;
    /**
     * 二次发送消息模板
     */
    private static final Long AGAIN_TEMPLATE_ID = 160L;
    private static final String SOURCE = "meeting-sign";

    /**
     * 新增分布式锁
     */
    private static final String addSignInfoLock = "addSignInfoLock:%s";

    /**
     * 撤回分布式锁
     */
    private static final String backSignInfo = "backSignInfo:%s";

    /**
     * 等待时间 5秒钟
     */
    private static final Integer waitTime = 1000 * 3;

    private final MeetingMapper meetingMapper;
    private final MeetingUserMapper meetingUserMapper;
    private final MeetingWaitSignMapper meetingWaitSignMapper;
    private final MeetingWaitSignConfiguration meetingWaitSignConfiguration;
    private final Errors errors;
    private final MeetingWaitSignRequestService meetingWaitSignRequestService;
    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public MeetingWaitSignService(MeetingMapper meetingMapper,
                                  MeetingUserMapper meetingUserMapper,
                                  MeetingWaitSignMapper meetingWaitSignMapper,
                                  MeetingWaitSignConfiguration meetingWaitSignConfiguration,
                                  Errors errors, MeetingWaitSignRequestService meetingWaitSignRequestService,
                                  StringRedisTemplate stringRedisTemplate) {
        this.meetingMapper = meetingMapper;
        this.meetingUserMapper = meetingUserMapper;
        this.meetingWaitSignMapper = meetingWaitSignMapper;
        this.meetingWaitSignConfiguration = meetingWaitSignConfiguration;
        this.errors = errors;
        this.meetingWaitSignRequestService = meetingWaitSignRequestService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 新增录入初始化补学数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void addSignInfo(Long meetingId, HeaderHelper.SysHeader sysHeader) {
        // 获取redis分布式锁
        String uuid = UUID.randomUUID().toString();
        String key = String.format(addSignInfoLock, meetingId);
        boolean lock = RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, key, uuid, waitTime);
        if (lock) {
            try {
                addSignInfo(meetingMapper.selectByPrimaryKey(meetingId), sysHeader);
            } finally {
                // 释放锁 这里不释放锁 让超时自动过期
                RedisLockUtil.releaseDistributedLock(stringRedisTemplate, key, uuid);
            }
        } else {
            log.debug("addSignInfo->重复调用:[{}]", meetingId);
        }
    }

    /**
     * 回退和审批不通过时调用
     */
    @Transactional(rollbackFor = Exception.class)
    public void backSignInfo(Long meetingId, HeaderHelper.SysHeader sysHeader) {
        // 获取redis分布式锁
        String uuid = UUID.randomUUID().toString();
        String key = String.format(backSignInfo, meetingId);
        boolean lock = RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, key, uuid, waitTime);
        if (lock) {
            try {
                backSignInfo(meetingMapper.selectByPrimaryKey(meetingId), sysHeader);
            } finally {
//                 释放锁 这里不释放锁 让超时自动过期
                RedisLockUtil.releaseDistributedLock(stringRedisTemplate, key, uuid);
            }
        } else {
            log.debug("backSignInfo->重复调用:[{}]", meetingId);
        }
    }

    /**
     * 查询补学详情
     *
     * @param waitSignId 主键id
     */
    public MeetingWaitSignVo find(Long waitSignId) {
        Example example = new Example(MeetingWaitSignEntity.class);
        example.createCriteria()
                .andEqualTo("waitSignId", waitSignId);
        example.selectProperties("content", "imgFile", "file", "type", "isNow");
        MeetingWaitSignEntity signEntity = meetingWaitSignMapper.selectOneByExample(example);
        log.debug("查询补学-> 查询Id:[{}] data:[{}]", waitSignId, JsonUtils.toJson(signEntity));
        if (null != signEntity) {
            MeetingWaitSignVo  ss = BeanUtil.copy(signEntity, MeetingWaitSignVo.class);
            if (null != signEntity.getImgFile()) {
                ss.setImgFile(JsonUtils.fromJson(signEntity.getImgFile(), List.class));
            }
            if (null != signEntity.getFile()) {
                ss.setFile(JsonUtils.fromJson(signEntity.getFile(),List.class));
            }
            return ss;
        }
        return null;
    }

    /**
     * 录入活动完成后 初始化补学相关逻辑
     */
    private void addSignInfo(MeetingEntity meetingEntity, HeaderHelper.SysHeader sysHeader) {
        log.debug("mt查询:" + meetingEntity);
        if (null == meetingEntity
                || meetingEntity.getCanSign().equals((short) 0)) {
            return;
        }

        if (!DateUtils.gtToday(meetingEntity.getSignTime())) {
            throw new ApiException("补学时间应大于当天!", new Result<>(errors, 3054, HttpStatus.BAD_REQUEST.value()));
        }

        log.debug("mt查询用户:" + meetingEntity);
        //查询最新所有未签到和请假的用户
        Example meetingUserExample = new Example(MeetingUserEntity.class);
        meetingUserExample.createCriteria()
                .andEqualTo("meetingId", meetingEntity.getMeetingId())
                .andIn("tag", Collections.singletonList(3))
                .andIn("signStatus", Arrays.asList(2, 3, 4));
        List<MeetingUserEntity> meetingUserEntities = meetingUserMapper.selectByExample(meetingUserExample);
        log.debug("mt查询用户:" + meetingUserEntities);
        if (CollectionUtils.isEmpty(meetingUserEntities)) {
            return;
        }
        meetingUserEntities = meetingUserEntities.stream().filter(x -> null != x.getUserId() && x.getUserId() > 0L).collect(Collectors.toList());
        //判断是否发送过补学信息
        Example example = new Example(MeetingWaitSignEntity.class);
        example.createCriteria()
                .andEqualTo("meetingId", meetingEntity.getMeetingId())
                .andEqualTo("isDel", Constant.YES_SHORT);
        int i = meetingWaitSignMapper.selectCountByExample(example);

        //初始化补学表数据
        Date date = new Date();
        List<MeetingWaitSignEntity> signList = new ArrayList<>(meetingUserEntities.size());
        meetingUserEntities.forEach(x -> {
            MeetingWaitSignEntity signEntity = new MeetingWaitSignEntity();
            signEntity.setMeetingId(meetingEntity.getMeetingId());
            signEntity.setCreateTime(date);
            signEntity.setMeetingUserId(x.getMeetingUserId());
            signEntity.setIsDel(Constant.YES_SHORT);
            signEntity.setType(Constant.YES_SHORT);
            signEntity.setIsNow(Constant.YES_SHORT);
            signEntity.setUserId(x.getUserId());
            meetingWaitSignMapper.insert(signEntity);
            signList.add(signEntity);
        });
        log.debug("mt查询:" + meetingWaitSignConfiguration.getRegion() + "_" + meetingEntity.getRegionId());
        //发送补签消息
        MeetingWaitSignConfiguration.MeetingWaitSignConfigVO configVO = meetingWaitSignConfiguration.getRegion().get(meetingEntity.getRegionId());
        //如果未配置 则不发送消息
        if (null != configVO && !CollectionUtils.isEmpty(signList)) {
            log.debug("mt查询:" + signList);
            PushRequest pushRequest = new PushRequest();
            pushRequest.setTemplateId(i > 0 ? AGAIN_TEMPLATE_ID : DEFAULT_TEMPLATE_ID);
            pushRequest.setChannelType((byte) 4);
            pushRequest.setSource(SOURCE);
            List<MeetingWaitSignMsgVo> dataList = new ArrayList<>();
            pushRequest.setData(dataList);

            String prefix = configVO.getPrefix();
            String redirectUrl = configVO.getRedirectUrl();
            signList.forEach(x -> {
                MeetingWaitSignMsgVo vo = new MeetingWaitSignMsgVo();
                vo.setMeetingName(meetingEntity.getName());
                vo.setUserId(x.getUserId());
                vo.setDateTime(meetingEntity.getSignTime());
                log.debug("录入新增补学->meetingId:[{}]  i:[{}] 处理数据:[{}] ", meetingEntity.getMeetingId(), i, JsonUtils.toJson(x));
                try {
                    vo.setTargetUrl(prefix + URLEncoder.encode(StrUtil.format(redirectUrl, meetingEntity.getMeetingId(), x.getWaitSignId()), "utf-8"));
                } catch (Exception e) {
                    log.error("初始化补学错误->" + e.getMessage(), e);
                    throw new ApiException("补学初始化失败,请联系系统管理员:[url格式化错误]!", new Result<>(errors, 3051, HttpStatus.BAD_REQUEST.value(), "url格式化错误"));
                }
                dataList.add(vo);
            });
            meetingWaitSignRequestService.sendPushRequest(pushRequest, meetingEntity.getRegionId());
        }
    }

    /**
     * 回退和审批不通过时调用
     */
    private void backSignInfo(MeetingEntity meetingEntity, HeaderHelper.SysHeader sysHeader) {
        //meetingUser状态还原 在录入中就进行 查出已补学数据 修改状态为原始状态
        Example meetingUser = new Example(MeetingUserEntity.class);
        meetingUser.createCriteria()
                .andEqualTo("meetingId", meetingEntity.getMeetingId())
                .andIn("signStatus", Collections.singletonList(6));
//                .andIn("signStatus", Arrays.asList(2, 3, 4, 6));
        List<MeetingUserEntity> meetingUserEntities = meetingUserMapper.selectByExample(meetingUser);
        if (!CollectionUtils.isEmpty(meetingUserEntities)) {
            meetingUserEntities.forEach(x -> {
                if (null != x.getOldSignStatus()) {
                    x.setSignStatus(x.getOldSignStatus());
                    x.setOldSignStatus(null);
                    meetingUserMapper.updateByPrimaryKey(x);
                }
            });
            log.debug("backSignInfo-> meetingUsers meetingId:[{}] meetingUsersSize:[{}]", meetingEntity.getMeetingId(), meetingUserEntities.size());
        } else {
            log.debug("backSignInfo-> meetingUsers meetingId:[{}] meetingUsersSize:[{}]", meetingEntity.getMeetingId(), 0);
        }

        //如果存在历史数据 需要把 修改为活动变更
        Example meetingWaitSign = new Example(MeetingWaitSignEntity.class);
        meetingWaitSign.createCriteria()
                .andEqualTo("meetingId", meetingEntity.getMeetingId())
                .andEqualTo("isDel", Constant.YES)
                .andEqualTo("isNow", Constant.YES);
        List<MeetingWaitSignEntity> meetingWaitSignEntities = meetingWaitSignMapper.selectByExample(meetingWaitSign);
        if (CollectionUtils.isEmpty(meetingWaitSignEntities)) {
            log.debug("backSignInfo-> waitSigns meetingId:[{}] meetingUsersSize:[{}]", meetingEntity.getMeetingId(), 0);
            return;
        }
        log.debug("backSignInfo-> waitSigns meetingId:[{}] meetingUsersSize:[{}]", meetingEntity.getMeetingId(), meetingWaitSignEntities.size());

        Date date = new Date();
        meetingWaitSignEntities.forEach(x -> {
            x.setUpdateTime(date);
            if (x.getType().equals((short) 5)) {
                x.setContent(null);
                x.setImgFile(null);
                x.setFile(null);
            }
            x.setType((short) 4);
            x.setIsNow((short) 0);
            meetingWaitSignMapper.updateByPrimaryKey(x);
        });
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public class MeetingWaitSignMsgVo {
        //活动名称
        @JsonProperty("meeting_name")
        private String meetingName;

        //截止时间
        @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
        @JsonProperty("date_time")
        private Date dateTime;

        //跳转地址
        @JsonProperty("target_url")
        private String targetUrl;

        //用户id
        @JsonProperty("user_id")
        private Long userId;
    }

}
