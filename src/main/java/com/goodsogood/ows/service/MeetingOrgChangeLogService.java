package com.goodsogood.ows.service;

import com.aidangqun.log4j2cm.aop.HttpLogAspect;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingOrgChangeLogMapper;
import com.goodsogood.ows.model.db.MeetingOrgChangeLogEntity;
import com.goodsogood.ows.model.vo.OrgChangeForm;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @create 2019-04-18 14:29
 */
@Service
@Log4j2
public class MeetingOrgChangeLogService {

    private final MeetingOrgChangeLogMapper meetingOrgChangeLogMapper;
    private final MeetingPlanAsyncService meetingPlanAsyncService;

    @Autowired
    public MeetingOrgChangeLogService(
            MeetingOrgChangeLogMapper meetingOrgChangeLogMapper,
            MeetingPlanAsyncService meetingPlanAsyncService) {
        this.meetingOrgChangeLogMapper = meetingOrgChangeLogMapper;
        this.meetingPlanAsyncService = meetingPlanAsyncService;
    }

    /**
     * 处理组织变更记录 保存组织变更日志
     *
     * @param orgChangeForm OrgChangeForm 变更信息
     * @return int 大于0保存成功
     */
    public int saveOrgChangeLog(HeaderHelper.SysHeader sysHeader, OrgChangeForm orgChangeForm) {
        log.info("保存组织变更记录.变更信息->{}", JsonUtils.toJson(orgChangeForm));

        // 更新组织变更记录
        MeetingOrgChangeLogEntity meetingOrgChangeLogEntity = this.updateOrgChangeLog(orgChangeForm);

        // 组织变更 更新相关的活动类型 添加任务
        meetingPlanAsyncService.updateMeetingPlanOreByOrgChangeAsync(
                sysHeader,
                meetingOrgChangeLogEntity, HttpLogAspect.getSSLog());
        return 1;
    }

    /**
     * @param orgChangeForm 变更信息
     *                      1、删除当前组织未处理的变更记录
     *                      2、保存变更记录
     * @return MeetingOrgChangeLogEntity
     */
    @Transactional
    public MeetingOrgChangeLogEntity updateOrgChangeLog(OrgChangeForm orgChangeForm) {

        // 更新该组织未处理的历史变更记录
        Example example = new Example(MeetingOrgChangeLogEntity.class);
        example.createCriteria()
                .andEqualTo("processTag", MeetingOrgChangeLogEntity.PROCESS_TAG_NO)
                .andEqualTo("orgId", orgChangeForm.getOrgId());
        MeetingOrgChangeLogEntity updateEntity = new MeetingOrgChangeLogEntity();
        updateEntity.setProcessTag(MeetingOrgChangeLogEntity.PROCESS_TAG_YES);
        meetingOrgChangeLogMapper.updateByExampleSelective(updateEntity, example);

        // 添加该组织的最新变更记录
        MeetingOrgChangeLogEntity meetingOrgChangeLogEntity = new MeetingOrgChangeLogEntity();
        BeanUtils.copyProperties(orgChangeForm, meetingOrgChangeLogEntity);

        meetingOrgChangeLogEntity.setProcessTag(MeetingOrgChangeLogEntity.PROCESS_TAG_NO);
        meetingOrgChangeLogEntity.setCreateTime(DateTime.now().toDate());

        // 保存变更信息
        meetingOrgChangeLogMapper.insert(meetingOrgChangeLogEntity);
        return meetingOrgChangeLogEntity;
    }

}
