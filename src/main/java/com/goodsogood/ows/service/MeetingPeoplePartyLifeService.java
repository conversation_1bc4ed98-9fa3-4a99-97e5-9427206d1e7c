package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.common.MeetingCanstant;
import com.goodsogood.ows.common.PartyLifeMeetingEnum;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.MeetingStatusHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.LifeAndMeetingVO;
import com.goodsogood.ows.model.vo.MeetingListForm;
import com.goodsogood.ows.model.vo.Org;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 民主生活会相关
 *
 * <AUTHOR> ruoyu
 * @date : 2021/11/24
 */
@Service
@Log4j2
public class MeetingPeoplePartyLifeService {

    private final LifeMapper lifeMapper;
    private final Errors errors;
    private final TypeMapper typeMapper;
    private final LifeStudyMapper lifeStudyMapper;
    private final LifeAdviceMapper lifeAdviceMapper;
    private final LifeNoticeMapper lifeNoticeMapper;
    private final MeetingService meetingService;
    private final MeetingResultFileMapper meetingResultFileMapper;
    private final MeetingMapper meetingMapper;
    private final IndexService indexService;

    /* 组织生活会 */
    private final OrgLifeMapper orgLifeMapper;
    private final OrgLifeStudyMapper orgLifeStudyMapper;
    private final OrgLifeAdviceMapper orgLifeAdviceMapper;
    private final OrgLifeNoticeMapper orgLifeNoticeMapper;

    @Autowired
    public MeetingPeoplePartyLifeService(LifeMapper lifeMapper, OrgLifeMapper orgLifeMapper, Errors errors,
                                         TypeMapper typeMapper, LifeStudyMapper lifeStudyMapper,
                                         OrgLifeStudyMapper orgLifeStudyMapper, LifeAdviceMapper lifeAdviceMapper,
                                         LifeNoticeMapper lifeNoticeMapper, @Lazy MeetingService meetingService,
                                         MeetingMapper meetingMapper, MeetingResultFileMapper meetingResultFileMapper,
                                         IndexService indexService, OrgLifeAdviceMapper orgLifeAdviceMapper,
                                         OrgLifeNoticeMapper orgLifeNoticeMapper) {
        this.lifeMapper = lifeMapper;
        this.orgLifeMapper = orgLifeMapper;
        this.errors = errors;
        this.typeMapper = typeMapper;
        this.lifeStudyMapper = lifeStudyMapper;
        this.orgLifeStudyMapper = orgLifeStudyMapper;
        this.lifeAdviceMapper = lifeAdviceMapper;
        this.lifeNoticeMapper = lifeNoticeMapper;
        this.meetingService = meetingService;
        this.meetingMapper = meetingMapper;
        this.indexService = indexService;
        this.meetingResultFileMapper = meetingResultFileMapper;
        this.orgLifeAdviceMapper = orgLifeAdviceMapper;
        this.orgLifeNoticeMapper = orgLifeNoticeMapper;
    }


    /**
     * 关联到民主生活会
     */
    public void joinToLife(HeaderHelper.SysHeader sysHeader, Long lifeId, List<Long> dataIds, Integer modelId, Integer step, Integer sourceType) {
        Constant.ModelType modelByModeId = Constant.ModelType.getModelByModeId(modelId);
        if (null == modelByModeId) {
            throw new ApiException("活动新增失败:[modelId: " + modelId + " 无效]",
                    new Result<>(errors, 3101, HttpStatus.INTERNAL_SERVER_ERROR.value(), "modelId: " + modelId + " 无效"));
        }
        for (Long dataId : dataIds) {
            switch (modelByModeId) {
                case ADVICE_QU:
                    if (sourceType.equals(1)) {
                        activityJoinToLife(modelByModeId, sysHeader, lifeId, dataId, step);
                    } else if (sourceType.equals(2)) {
                        activityJoinToOrgLife(modelByModeId, sysHeader, lifeId, dataId, step);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 民主生活会/组织生活会适用 删除组织生活
     */
    public void deleteMeetingById(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, Long meetingId) {
        MeetingEntity meetingEntity = meetingMapper.selectByPrimaryKey(meetingId);
        if (null == meetingEntity || meetingEntity.getIsDel().equals(Constant.YES_SHORT)) {
            return;
        }

        //判断活动状态 删除前是否需要内容取消
        Short status = MeetingStatusHelper.convertToFormStatus(meetingEntity.getStatus(), meetingEntity.getStartTime());
        if (status == MeetingCanstant.MEETING_STATUS_APPROVAL_APPLY.shortValue()
                || status == MeetingCanstant.MEETING_STATUS_APPROVAL_NOT_PASS.shortValue()
                || status == MeetingCanstant.MEETING_STATUS_APPROVAL.shortValue()
                || status == MeetingCanstant.MEETING_STATUS_SOON_START.shortValue()
                // 1.MEETING_STATUS_BACK 退回活动可以取消
                || status == MeetingCanstant.MEETING_STATUS_BACK.shortValue()
                || status == MeetingCanstant.MEETING_STATUS_REVOKE.shortValue()) {
            meetingService.cancelMeeting(headers, sysHeader, meetingId);
        }

        //删除组织生活
        MeetingEntity upm = new MeetingEntity();
        upm.setMeetingId(meetingId);
        upm.setIsDel((short) 1);
        upm.setUpdateTime(DateTime.now().toDate());
        upm.setLastChangeUser(sysHeader.getUserId());
        int delNum = meetingMapper.updateByPrimaryKeySelective(upm);
        // 删除活动 刷新缓存
        indexService.collectRedis();
    }

    /**
     * 批量获取meeting File文件 适用民主生活会/组织生活会
     */
    public void getFileByMeetings(Map<Integer, List<LifeAndMeetingVO>> lifeVoMap) {
        if (CollectionUtils.isEmpty(lifeVoMap)) {
            return;
        }
        List<LifeAndMeetingVO> listLifeVo = new ArrayList<>();
        lifeVoMap.values().forEach(listLifeVo::addAll);

        Example example = new Example(MeetingResultFileEntity.class);
        example.createCriteria()
                .andIn("meetingId", listLifeVo.stream().map(LifeAndMeetingVO::getMeetingId).collect(Collectors.toList()))
                .andEqualTo("isDel", 0);
        example.selectProperties("fileName", "id", "path", "meetingId");
        List<MeetingResultFileEntity> meetingFileEntities = meetingResultFileMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(meetingFileEntities)) {
            return;
        }
        Map<Long, LifeAndMeetingVO> collect = listLifeVo.stream().collect(Collectors.toMap(LifeAndMeetingVO::getMeetingId, x -> x));
        for (MeetingResultFileEntity item : meetingFileEntities) {
            LifeAndMeetingVO lifeAndMeetingVO = collect.get(item.getMeetingId());
            if (CollectionUtils.isEmpty(lifeAndMeetingVO.getMeetingFiles())) {
                lifeAndMeetingVO.setMeetingFiles(new ArrayList<>());
            }
            lifeAndMeetingVO.getMeetingFiles().add(new LifeAndMeetingVO.LifeAndMeetingFile(item.getFileName(), item.getId(), item.getPath()));
        }
    }

    public void orgLifeLinkToDelete(Long meetingId){
        //删除组织生活中组织学习中直接关联的
        Example exampleLifeStudy = new Example(OrgLifeStudyEntity.class);
        exampleLifeStudy.createCriteria()
                .andEqualTo("studyId", meetingId).andEqualTo("hasDirectRelate",1);
        orgLifeStudyMapper.deleteByExample(exampleLifeStudy);
    }
    /**
     * 删除组织生活时 关联查找下删除民主生活会/组织生活会
     */
    public void meetingDeleteLinkToDelete(Long meetingId) {
        PartyLifeMeetingEnum byModelId;
        MeetingEntity meetingEntity = meetingMapper.selectByPrimaryKey(meetingId);
        if (null == meetingEntity || null == meetingEntity.getLifeId()
                || null == meetingEntity.getModelId() ||
                (null == (byModelId = PartyLifeMeetingEnum.getByModelId(meetingEntity.getModelId(), meetingEntity.getSourceType())))) {
            return;
        }
        switch (byModelId) {
            case BEFORE_PARTY_STUDY:
                Example exampleLifeStudy = new Example(LifeStudyEntity.class);
                exampleLifeStudy.createCriteria()
                        .andEqualTo("lifeId", meetingEntity.getLifeId())
                        .andEqualTo("studyId", meetingId);
                lifeStudyMapper.deleteByExample(exampleLifeStudy);
                break;
            case BEFORE_PARTY_FORUM:
                Example exampleLifeAdvice = new Example(LifeAdviceEntity.class);
                exampleLifeAdvice.createCriteria()
                        .andEqualTo("lifeId", meetingEntity.getLifeId())
                        .andEqualTo("adviceType", 3)
                        .andEqualTo("isDel", Constant.NO)
                        .andEqualTo("dataId", meetingId);
                lifeAdviceMapper.deleteByExample(exampleLifeAdvice);
                break;
            case BEFORE_DONE_SUBMIT:
                LifeEntity lifeEntity = lifeMapper.selectByPrimaryKey(meetingEntity.getLifeId());
                if (null != lifeEntity) {
                    lifeEntity.setMeetingId(null);
                    lifeMapper.updateByPrimaryKey(lifeEntity);
                }
                break;
            case SUMMARY_PARTY_NOTICE:
                Example exampleLifeNotice = new Example(LifeNoticeEntity.class);
                exampleLifeNotice.createCriteria()
                        .andEqualTo("lifeId", meetingEntity.getLifeId())
                        .andEqualTo("noticeId", meetingId);
                lifeNoticeMapper.deleteByExample(exampleLifeNotice);
                break;
            case ORG_LIFE_BEFORE_PARTY_STUDY:
                Example examporgLeLifeStudy = new Example(OrgLifeStudyEntity.class);
                examporgLeLifeStudy.createCriteria()
                        .andEqualTo("lifeId", meetingEntity.getLifeId())
                        .andEqualTo("studyId", meetingId);
                orgLifeStudyMapper.deleteByExample(examporgLeLifeStudy);
                break;
            case ORG_LIFE_BEFORE_PARTY_FORUM:
                Example exampleOrgLifeAdvice = new Example(OrgLifeAdviceEntity.class);
                exampleOrgLifeAdvice.createCriteria()
                        .andEqualTo("lifeId", meetingEntity.getLifeId())
                        .andEqualTo("adviceType", 3)
                        .andEqualTo("isDel", Constant.NO)
                        .andEqualTo("dataId", meetingId);
                orgLifeAdviceMapper.deleteByExample(exampleOrgLifeAdvice);
                break;
            case ORG_LIFE_BEFORE_DONE_SUBMIT:
                OrgLifeEntity orgLifeEntity = orgLifeMapper.selectByPrimaryKey(meetingEntity.getLifeId());
                if (null != orgLifeEntity) {
                    orgLifeEntity.setMeetingId(null);
                    orgLifeMapper.updateByPrimaryKey(orgLifeEntity);
                }
                break;
            case ORG_LIFE_SUMMARY_PARTY_NOTICE:
                Example exampleOrgLifeNotice = new Example(OrgLifeNoticeEntity.class);
                exampleOrgLifeNotice.createCriteria()
                        .andEqualTo("lifeId", meetingEntity.getLifeId())
                        .andEqualTo("noticeId", meetingId);
                orgLifeNoticeMapper.deleteByExample(exampleOrgLifeNotice);
                break;
            default:
                return;
        }
    }

    /**
     * 民主生活会 组织生活列表查询
     *
     * @param modelId    模块id
     * @param lifeId     民主生活会主键Id
     * @param step       1:会前 2:会后
     * @param sourceType 1:民主生活会 2:组织生活会
     * @see PartyLifeMeetingEnum
     */
    public Page<MeetingEntity> listMeetingByLife(Long modelId, Long lifeId, Integer step, MeetingListForm queryForm, Integer sourceType) {
        PartyLifeMeetingEnum byModelId;
        if (null == modelId || null == lifeId || null == step ||
                null == (byModelId = PartyLifeMeetingEnum.getByModelId(modelId, sourceType))) {
            return null;
        }

        List<Long> meetingIds;
        switch (byModelId) {
            case BEFORE_PARTY_STUDY:
                meetingIds = beforeStudyListMeetingByLife(byModelId, lifeId, step);
                break;
            case BEFORE_PARTY_FORUM:
                meetingIds = beforeForumListMeetingByLife(byModelId, lifeId, step);
                break;
            case BEFORE_DONE_SUBMIT:
                meetingIds = beforeSubmitListMeetingByLife(byModelId, lifeId, step);
                break;
            case SUMMARY_PARTY_NOTICE:
                meetingIds = summaryNoticeListMeetingByLife(byModelId, lifeId, step);
                break;
            case ORG_LIFE_BEFORE_PARTY_STUDY:
                meetingIds = beforeOrgStudyListMeetingByLife(byModelId, lifeId, step);
                break;
            case ORG_LIFE_BEFORE_PARTY_FORUM:
                meetingIds = beforeOrgForumListMeetingByLife(byModelId, lifeId, step);
                break;
            case ORG_LIFE_BEFORE_DONE_SUBMIT:
                meetingIds = beforeOrgSubmitListMeetingByLife(byModelId, lifeId, step);
                break;
            case ORG_LIFE_SUMMARY_PARTY_NOTICE:
                meetingIds = summaryOrgNoticeListMeetingByLife(byModelId, lifeId, step);
                break;
            default:
                return null;
        }

        if (!CollectionUtils.isEmpty(meetingIds)) {
            queryForm.setFilterMeetingIds(meetingIds);
            return listMeetingByIds(meetingIds, queryForm);
        }

        return null;
    }

    /**
     * 民主生活会跳转 发起民族生活的组织生活
     */
    public void checkColumnAndSaveDataInfo(MeetingEntity meetingEntity, HeaderHelper.SysHeader sysHeader) {
        log.debug("checkColumnAndSaveDataInfo->[{}]", JsonUtils.toJson(meetingEntity));
        PartyLifeMeetingEnum byModelEnum = checkMeetingEntityByLife(meetingEntity);
        if (Objects.isNull(byModelEnum)) {
            return;
        }

        switch (byModelEnum) {
            case BEFORE_PARTY_STUDY:
                saveMeetingByPartyStudy(meetingEntity, sysHeader, byModelEnum);
                break;
            case BEFORE_PARTY_FORUM:
                saveMeetingByPartyForum(meetingEntity, sysHeader, byModelEnum);
                break;
            case BEFORE_DONE_SUBMIT:
                saveMeetingByDoneSubmit(meetingEntity, sysHeader, byModelEnum);
                break;
            case SUMMARY_PARTY_NOTICE:
                saveMeetingByPartyNotice(meetingEntity, sysHeader, byModelEnum);
                break;
            case ORG_LIFE_BEFORE_PARTY_STUDY:
                saveOrgMeetingByPartyStudy(meetingEntity, sysHeader, byModelEnum);
                break;
            case ORG_LIFE_BEFORE_PARTY_FORUM:
                saveOrgMeetingByPartyForum(meetingEntity, sysHeader, byModelEnum);
                break;
            case ORG_LIFE_BEFORE_DONE_SUBMIT:
                saveOrgMeetingByDoneSubmit(meetingEntity, sysHeader, byModelEnum);
                break;
            case ORG_LIFE_SUMMARY_PARTY_NOTICE:
                saveOrgMeetingByPartyNotice(meetingEntity, sysHeader, byModelEnum);
                break;
            default:
                break;
        }
    }

    /**
     * 验证在:
     * 1.正常发起活动
     * 2.直接录入活动
     * [1,2]的时候是否包含民主生活会的组织类型
     * 如果lifeId存在的话则跳过验证
     */
    public void checkExistLifeType(MeetingEntity meetingEntity) {
        if (null == meetingEntity || null != meetingEntity.getLifeId()) {
            return;
        }
        List<Long> typeId = meetingEntity.getMeetingTypes().stream().map(MeetingTypeEntity::getTypeId).collect(Collectors.toList());
        checkExistLifeType(typeId);
    }

    /**
     * 验证在typeId中是否存在民主生活会的组织生活类型
     */
    public void checkExistLifeType(List<Long> typeId) {
        if (CollectionUtils.isEmpty(typeId)) {
            return;
        }

        Example example = new Example(TypeEntity.class);
        example.createCriteria()
                .andIn("typeId", typeId)
                .andIn("typeSys",
                        Arrays.stream(PartyLifeMeetingEnum.values()).map(PartyLifeMeetingEnum::getTypeSys).collect(Collectors.toSet()));
        List<TypeEntity> typeEntities = typeMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(typeEntities)) {
            throw new ApiException("不能存在[" + typeEntities.get(0).getType() + "]类型的活动,请确认后提交!",
                    new Result<>(errors, 3102, HttpStatus.INTERNAL_SERVER_ERROR.value(), typeEntities.get(0).getType()));
        }
    }

    /**
     * 组织生活修改时 判断原有民主生活会类型是否被修改
     */
    public void checkMeetingUpdateByLife(MeetingEntity meetingEntitySubmit) {
        checkMeetingEntityByLife(meetingEntitySubmit);
        MeetingEntity meetingEntity = meetingMapper.findByIdAndOrg(meetingEntitySubmit.getMeetingId(), meetingEntitySubmit.getOrgId());
        if (null == meetingEntity || null == meetingEntity.getModelId()) {
            return;
        }
        //判断是否存在原有的民主生活会类型
        String join = Arrays.stream(PartyLifeMeetingEnum.values()).map(x -> x.getTypeSys().toString()).collect(Collectors.joining(","));
        TypeEntity typeByLife = typeMapper.findTypesByTypeSys(meetingEntity.getMeetingId(), join);
        boolean match = meetingEntity.getMeetingTypes().stream().anyMatch(x -> x.getTypeId().equals(typeByLife.getTypeId()));
        if (!match) {
            throw new ApiException("修改失败[原有民主生活会类型不能变更]",
                    new Result<>(errors, 3103, HttpStatus.INTERNAL_SERVER_ERROR.value(), "原有民主生活会类型不能变更"));
        }
        //判断是否包含其他民主生活会类型
        Example example = new Example(TypeEntity.class);
        example.createCriteria()
                .andIn("typeSys", Arrays.stream(PartyLifeMeetingEnum.values()).map(PartyLifeMeetingEnum::getTypeSys).collect(Collectors.toList()));
        List<TypeEntity> typeEntities = typeMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(typeEntities)) {
            List<Long> collect = typeEntities.stream().map(TypeEntity::getTypeId).collect(Collectors.toList());
            int i = 0;
            for (MeetingTypeEntity meetingTypeEntity : meetingEntity.getMeetingTypes()) {
                if (collect.contains(meetingTypeEntity.getTypeId())) {
                    i++;
                }
            }
            if (i > 1) {
                throw new ApiException("修改失败[活动类型中不能存在多条民主生活会类型]",
                        new Result<>(errors, 3103, HttpStatus.INTERNAL_SERVER_ERROR.value(), "活动类型中不能存在多条民主生活会类型"));
            }
        }
    }

    private List<Long> summaryOrgNoticeListMeetingByLife(PartyLifeMeetingEnum byModelId, Long lifeId, Integer step) {
        Example example = new Example(OrgLifeNoticeEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId", lifeId)
                .andEqualTo("step", step);
        example.selectProperties("noticeId");
        List<OrgLifeNoticeEntity> lifeNoticeEntities = orgLifeNoticeMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(lifeNoticeEntities)) {
            return lifeNoticeEntities.stream().map(OrgLifeNoticeEntity::getNoticeId).collect(Collectors.toList());
        }
        return null;
    }

    private List<Long> beforeOrgSubmitListMeetingByLife(PartyLifeMeetingEnum byModelId, Long lifeId, Integer step) {
        Example example = new Example(OrgLifeEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId", lifeId)
                .andIsNotNull("meetingId");
        example.selectProperties("meetingId", "lifeId");
        OrgLifeEntity lifeEntities = orgLifeMapper.selectOneByExample(example);
        if (null != lifeEntities) {
            return Collections.singletonList(lifeEntities.getMeetingId());
        }
        return null;
    }

    private List<Long> beforeOrgForumListMeetingByLife(PartyLifeMeetingEnum byModelId, Long lifeId, Integer step) {
        Example example = new Example(OrgLifeAdviceEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId", lifeId)
                .andEqualTo("step", step)
                .andEqualTo("adviceType", 3)
                .andEqualTo("isDel", Constant.NO);
        example.selectProperties("dataId");
        List<OrgLifeAdviceEntity> lifeAdviceEntities = orgLifeAdviceMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(lifeAdviceEntities)) {
            return lifeAdviceEntities.stream().map(OrgLifeAdviceEntity::getDataId).collect(Collectors.toList());
        }
        return null;
    }

    private List<Long> beforeOrgStudyListMeetingByLife(PartyLifeMeetingEnum byModelId, Long lifeId, Integer step) {
        Example example = new Example(OrgLifeStudyEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId", lifeId)
                .andEqualTo("step", step);
        example.selectProperties("studyId");
        List<OrgLifeStudyEntity> lifeStudyEntities = orgLifeStudyMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(lifeStudyEntities)) {
            return lifeStudyEntities.stream().map(OrgLifeStudyEntity::getStudyId).collect(Collectors.toList());
        }
        return null;
    }

    private void saveOrgMeetingByPartyNotice(MeetingEntity meetingEntity, HeaderHelper.SysHeader sysHeader, PartyLifeMeetingEnum byModelEnum) {
        OrgLifeNoticeEntity orgLifeNoticeEntity = new OrgLifeNoticeEntity();
        orgLifeNoticeEntity.setLifeId(meetingEntity.getLifeId());
        orgLifeNoticeEntity.setNoticeId(meetingEntity.getMeetingId());
        orgLifeNoticeEntity.setStep(byModelEnum.getStep());
        orgLifeNoticeEntity.setCreateUser(sysHeader.getUserId());
        orgLifeNoticeEntity.setCreateTime(LocalDateTime.now());
        orgLifeNoticeMapper.insert(orgLifeNoticeEntity);
    }

    private void saveOrgMeetingByDoneSubmit(MeetingEntity meetingEntity, HeaderHelper.SysHeader sysHeader, PartyLifeMeetingEnum byModelEnum) {
        OrgLifeEntity orgLifeEntity = new OrgLifeEntity();
        orgLifeEntity.setLifeId(meetingEntity.getLifeId());
        orgLifeEntity.setMeetingId(meetingEntity.getMeetingId());
        orgLifeEntity.setUpdateTime(LocalDateTime.now());
        orgLifeEntity.setLastChangeUser(sysHeader.getUserId());
        orgLifeMapper.updateByPrimaryKeySelective(orgLifeEntity);
    }

    private void saveOrgMeetingByPartyForum(MeetingEntity meetingEntity, HeaderHelper.SysHeader sysHeader, PartyLifeMeetingEnum byModelEnum) {
        OrgLifeAdviceEntity orgLifeAdviceEntity = new OrgLifeAdviceEntity();
        orgLifeAdviceEntity.setLifeId(meetingEntity.getLifeId());
        orgLifeAdviceEntity.setAdviceType(3);
        orgLifeAdviceEntity.setDataId(meetingEntity.getMeetingId());
        orgLifeAdviceEntity.setStep(byModelEnum.getStep());
        orgLifeAdviceEntity.setIsDel(Constant.NO);
        orgLifeAdviceEntity.setCreateTime(LocalDateTime.now());
        orgLifeAdviceEntity.setCreateUser(sysHeader.getUserId());
        orgLifeAdviceMapper.insert(orgLifeAdviceEntity);
    }

    private void saveOrgMeetingByPartyStudy(MeetingEntity meetingEntity, HeaderHelper.SysHeader sysHeader, PartyLifeMeetingEnum byModelEnum) {
        OrgLifeStudyEntity orgLifeStudyEntity = new OrgLifeStudyEntity();
        orgLifeStudyEntity.setLifeId(meetingEntity.getLifeId());
        orgLifeStudyEntity.setStudyId(meetingEntity.getMeetingId());
        orgLifeStudyEntity.setStep(byModelEnum.getStep());
        orgLifeStudyEntity.setCreateUser(sysHeader.getUserId());
        orgLifeStudyEntity.setCreateTime(LocalDateTime.now());
        orgLifeStudyMapper.insert(orgLifeStudyEntity);
    }

    /**
     * 活动关联到民主生活会
     */
    private void activityJoinToLife(Constant.ModelType modelType, HeaderHelper.SysHeader sysHeader, Long lifeId, Long activityId, Integer step) {
        checkIfExist(lifeId, activityId);//判断是否存在
        LifeAdviceEntity lifeAdviceEntity = new LifeAdviceEntity();
        lifeAdviceEntity.setLifeId(lifeId);
        lifeAdviceEntity.setAdviceType(2);
        lifeAdviceEntity.setDataId(activityId);
        lifeAdviceEntity.setStep(step);
        lifeAdviceEntity.setIsDel(Constant.NO);
        lifeAdviceEntity.setCreateTime(LocalDateTime.now());
        lifeAdviceEntity.setCreateUser(sysHeader.getUserId());
        lifeAdviceEntity.setUpdateTime(LocalDateTime.now());
        lifeAdviceEntity.setLastChangeUser(sysHeader.getUserId());
        lifeAdviceMapper.insert(lifeAdviceEntity);
    }

    private void activityJoinToOrgLife(Constant.ModelType modelByModeId, HeaderHelper.SysHeader sysHeader, Long lifeId, Long activityId, Integer step) {
        checkIfExistOrgLife(lifeId, activityId);
        OrgLifeAdviceEntity orgLifeAdviceEntity = new OrgLifeAdviceEntity();
        orgLifeAdviceEntity.setLifeId(lifeId);
        orgLifeAdviceEntity.setAdviceType(2);
        orgLifeAdviceEntity.setDataId(activityId);
        orgLifeAdviceEntity.setStep(step);
        orgLifeAdviceEntity.setIsDel(Constant.NO);
        orgLifeAdviceEntity.setCreateTime(LocalDateTime.now());
        orgLifeAdviceEntity.setCreateUser(sysHeader.getUserId());
        orgLifeAdviceEntity.setUpdateTime(LocalDateTime.now());
        orgLifeAdviceEntity.setLastChangeUser(sysHeader.getUserId());
        orgLifeAdviceMapper.insert(orgLifeAdviceEntity);

    }

    private void checkIfExist(Long lifeId, Long activityId) {
        Example example = new Example(LifeAdviceEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId", lifeId).andEqualTo("adviceType", 2).andEqualTo("dataId", activityId).andEqualTo("isDel", 0);
        if (!CollectionUtils.isEmpty(lifeAdviceMapper.selectByExample(example))) {
            throw new ApiException("该问卷调查已被关联:[activityId无效]!",
                    new Result<>(errors, 3170, HttpStatus.INTERNAL_SERVER_ERROR.value(), "activityId无效:" + activityId));
        }
    }

    private void checkIfExistOrgLife(Long orgLifeId, Long activityId) {
        Example example = new Example(OrgLifeAdviceEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lifeId", orgLifeId).andEqualTo("adviceType", 2).andEqualTo("dataId", activityId).andEqualTo("isDel", 0);
        if (!CollectionUtils.isEmpty(orgLifeAdviceMapper.selectByExample(example))) {
            throw new ApiException("该问卷调查已被关联:[activityId无效]!",
                    new Result<>(errors, 3170, HttpStatus.INTERNAL_SERVER_ERROR.value(), "activityId无效:" + activityId));
        }
    }

    /**
     * 会议通报-会议通报
     */
    private List<Long> summaryNoticeListMeetingByLife(PartyLifeMeetingEnum byModelId, Long lifeId, Integer step) {
        Example example = new Example(LifeNoticeEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId", lifeId)
                .andEqualTo("step", step);
        example.selectProperties("noticeId");
        List<LifeNoticeEntity> lifeNoticeEntities = lifeNoticeMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(lifeNoticeEntities)) {
            return lifeNoticeEntities.stream().map(LifeNoticeEntity::getNoticeId).collect(Collectors.toList());
        }

        return null;
    }

    /**
     * 会前准备完毕-发起民主生活会
     */
    private List<Long> beforeSubmitListMeetingByLife(PartyLifeMeetingEnum byModelId, Long lifeId, Integer step) {
        Example example = new Example(LifeEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId", lifeId)
                .andIsNotNull("meetingId");
        example.selectProperties("meetingId", "lifeId");
        LifeEntity lifeEntities = lifeMapper.selectOneByExample(example);
        if (null != lifeEntities) {
            return Collections.singletonList(lifeEntities.getMeetingId());
        }
        return null;
    }

    /**
     * 征求意见-座谈会
     */
    private List<Long> beforeForumListMeetingByLife(PartyLifeMeetingEnum byModelId, Long lifeId, Integer step) {
        Example example = new Example(LifeAdviceEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId", lifeId)
                .andEqualTo("step", step)
                .andEqualTo("adviceType", 3)
                .andEqualTo("isDel", Constant.NO);
        example.selectProperties("dataId");
        List<LifeAdviceEntity> lifeAdviceEntities = lifeAdviceMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(lifeAdviceEntities)) {
            return lifeAdviceEntities.stream().map(LifeAdviceEntity::getDataId).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 会前学习-组织学习
     */
    private List<Long> beforeStudyListMeetingByLife(PartyLifeMeetingEnum byModelId, Long lifeId, Integer step) {
        Example example = new Example(LifeStudyEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId", lifeId)
                .andEqualTo("step", step);
        example.selectProperties("studyId");
        List<LifeStudyEntity> lifeStudyEntities = lifeStudyMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(lifeStudyEntities)) {
            return lifeStudyEntities.stream().map(LifeStudyEntity::getStudyId).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 过滤组织生活 返回指定模块的组织生活
     */
    private Page<MeetingEntity> listMeetingByIds(List<Long> meetingIds, MeetingListForm queryForm) {
        if (CollectionUtils.isEmpty(meetingIds)) {
            return null;
        }
        return meetingService.listPageV2(queryForm);
    }

    /**
     * 会前准备完毕-发起民主生活会
     */
    private void saveMeetingByDoneSubmit(MeetingEntity meetingEntity, HeaderHelper.SysHeader sysHeader,
                                         PartyLifeMeetingEnum byModelEnum) {
        LifeEntity lifeEntity = new LifeEntity();
        lifeEntity.setLifeId(meetingEntity.getLifeId());
        lifeEntity.setMeetingId(meetingEntity.getMeetingId());
        lifeEntity.setUpdateTime(LocalDateTime.now());
        lifeEntity.setLastChangeUser(sysHeader.getUserId());
        lifeMapper.updateByPrimaryKeySelective(lifeEntity);
    }

    /**
     * 会议通报-会议通报
     */
    private void saveMeetingByPartyNotice(MeetingEntity meetingEntity, HeaderHelper.SysHeader sysHeader,
                                          PartyLifeMeetingEnum byModelEnum) {
        LifeNoticeEntity lifeNoticeEntity = new LifeNoticeEntity();
        lifeNoticeEntity.setLifeId(meetingEntity.getLifeId());
        lifeNoticeEntity.setNoticeId(meetingEntity.getMeetingId());
        lifeNoticeEntity.setStep(byModelEnum.getStep());
        lifeNoticeEntity.setCreateUser(sysHeader.getUserId());
        lifeNoticeEntity.setCreateTime(LocalDateTime.now());
        lifeNoticeMapper.insert(lifeNoticeEntity);
    }

    /**
     * 征求意见-座谈会
     */
    private void saveMeetingByPartyForum(MeetingEntity meetingEntity,
                                         HeaderHelper.SysHeader sysHeader,
                                         PartyLifeMeetingEnum lifeMeetingEnum) {
        LifeAdviceEntity lifeAdviceEntity = new LifeAdviceEntity();
        lifeAdviceEntity.setLifeId(meetingEntity.getLifeId());
        lifeAdviceEntity.setAdviceType(3);
        lifeAdviceEntity.setDataId(meetingEntity.getMeetingId());
        lifeAdviceEntity.setStep(lifeMeetingEnum.getStep());
        lifeAdviceEntity.setIsDel(Constant.NO);
        lifeAdviceEntity.setCreateTime(LocalDateTime.now());
        lifeAdviceEntity.setCreateUser(sysHeader.getUserId());
        lifeAdviceMapper.insert(lifeAdviceEntity);
    }

    /**
     * 会前学习-组织学习
     */
    private void saveMeetingByPartyStudy(MeetingEntity meetingEntity,
                                         HeaderHelper.SysHeader sysHeader,
                                         PartyLifeMeetingEnum lifeMeetingEnum) {
        LifeStudyEntity lifeStudyEntity = new LifeStudyEntity();
        lifeStudyEntity.setLifeId(meetingEntity.getLifeId());
        lifeStudyEntity.setStudyId(meetingEntity.getMeetingId());
        lifeStudyEntity.setStep(lifeMeetingEnum.getStep());
        lifeStudyEntity.setCreateUser(sysHeader.getUserId());
        lifeStudyEntity.setCreateTime(LocalDateTime.now());
        lifeStudyMapper.insert(lifeStudyEntity);
    }

    /**
     * 校验数据是否满足依赖
     */
    private PartyLifeMeetingEnum checkMeetingEntityByLife(MeetingEntity meetingEntitySubmit) {
        if (Objects.isNull(meetingEntitySubmit)
                || Objects.isNull(meetingEntitySubmit.getMeetingId())
                || Objects.isNull(meetingEntitySubmit.getLifeId())
                || Objects.isNull(meetingEntitySubmit.getSourceType())
                || Objects.isNull(meetingEntitySubmit.getModelId())) {
            return null;
        }

        PartyLifeMeetingEnum byModelId = PartyLifeMeetingEnum.getByModelId(meetingEntitySubmit.getModelId(),
                meetingEntitySubmit.getSourceType());
        //枚举中是否存在对应modelId
        if (Objects.isNull(byModelId)) {
            throw new ApiException("活动新增失败:[modelId无效]!",
                    new Result<>(errors, 3101, HttpStatus.INTERNAL_SERVER_ERROR.value(), "modelId无效:" + meetingEntitySubmit.getModelId()));
        }

        List<Short> typeSys = Arrays.stream(PartyLifeMeetingEnum.values()).map(PartyLifeMeetingEnum::getTypeSys).collect(Collectors.toList());
        Example typeExample = new Example(TypeEntity.class);
        typeExample.createCriteria()
                .andIn("typeSys", typeSys);
        List<TypeEntity> typeEntitiesFromDb = typeMapper.selectByExample(typeExample);
        //数据库是否初始化数据
        if (CollectionUtils.isEmpty(typeEntitiesFromDb)) {
            throw new ApiException("活动新增失败:[数据库typeSys未配置]!",
                    new Result<>(errors, 3101, HttpStatus.INTERNAL_SERVER_ERROR.value(), "数据库typeSys未配置"));
        }

        //modelId对应的类型在数据库中是否存在
        Optional<TypeEntity> first = typeEntitiesFromDb.stream().filter(x -> x.getTypeSys().equals(byModelId.getTypeSys())).findFirst();
        if (!first.isPresent()) {
            throw new ApiException("活动新增失败:[]!",
                    new Result<>(errors, 3101, HttpStatus.INTERNAL_SERVER_ERROR.value(), "typeSys:" + byModelId.getModelName() + "在库中不存在"));
        }

        //活动中是否存在modelId对应的类型
        TypeEntity typeEntity = first.get();
        List<MeetingTypeEntity> meetingTypes = meetingEntitySubmit.getMeetingTypes();
        boolean match = meetingTypes.stream().anyMatch(x -> x.getTypeId().equals(typeEntity.getTypeId()));

        if (!match) {
            throw new ApiException("活动新增失败:[]!",
                    new Result<>(errors, 3101, HttpStatus.INTERNAL_SERVER_ERROR.value(), "活动中未存在typeSys:" + byModelId.getModelName() + "的类型"));
        }

        //判断除需要的民主生活类型外是否含有其他类型民主生活会类型
        int count = 0;
        List<Long> typeIds = typeEntitiesFromDb.stream().map(TypeEntity::getTypeId).collect(Collectors.toList());
        for (MeetingTypeEntity entity : meetingTypes) {
            if (typeIds.contains(entity.getTypeId())) {
                count++;
            }
        }

        if (count > 1) {
            throw new ApiException("活动新增失败:[]!",
                    new Result<>(errors, 3101, HttpStatus.INTERNAL_SERVER_ERROR.value(), "存在多条民主生活会类型,请确认后提交"));
        }

        //判断指定民主生活会当前状态是否允许添加
        if (meetingEntitySubmit.getSourceType().equals(1)) {
            Example lifeExample = new Example(LifeEntity.class);
            lifeExample.createCriteria()
                    .andEqualTo("lifeId", meetingEntitySubmit.getLifeId())
                    .andEqualTo("isDel", Constant.NO);
            LifeEntity lifeEntity = lifeMapper.selectOneByExample(lifeExample);
            if (Objects.isNull(lifeEntity)) {
                throw new ApiException("活动新增失败:[未查询到民主生活会数据]!",
                        new Result<>(errors, 3101, HttpStatus.INTERNAL_SERVER_ERROR.value(), "未查询到民主生活会数据:" + meetingEntitySubmit.getLifeId()));
            }

            if (!byModelId.getStayStatus().contains(lifeEntity.getStatus())) {
                throw new ApiException("活动新增失败:[民主生活会状态异常]",
                        new Result<>(errors, 3101, HttpStatus.INTERNAL_SERVER_ERROR.value(), "民主生活会状态异常,需要:" + byModelId.getStayStatus().toString() + " 找到:" + lifeEntity.getStatus()));
            }
        } else if (meetingEntitySubmit.getSourceType().equals(2)) {
            Example orgLifeExample = new Example(OrgLifeEntity.class);
            orgLifeExample.createCriteria()
                    .andEqualTo("lifeId", meetingEntitySubmit.getLifeId())
                    .andEqualTo("isDel", Constant.NO);
            OrgLifeEntity orgLifeEntity = orgLifeMapper.selectOneByExample(orgLifeExample);
            if (Objects.isNull(orgLifeEntity)) {
                throw new ApiException("活动新增失败:[未查询到民主生活会数据]!",
                        new Result<>(errors, 3101, HttpStatus.INTERNAL_SERVER_ERROR.value(), "未查询到民主生活会数据:" + meetingEntitySubmit.getLifeId()));
            }

            if (!byModelId.getStayStatus().contains(orgLifeEntity.getStatus())) {
                throw new ApiException("活动新增失败:[民主生活会状态异常]",
                        new Result<>(errors, 3101, HttpStatus.INTERNAL_SERVER_ERROR.value(), "民主生活会状态异常,需要:" + byModelId.getStayStatus().toString() + " 找到:" + orgLifeEntity.getStatus()));
            }
        }

        return byModelId;
    }
}
