package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Config;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.RabbitmqQueueConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.AddStudyCertForm;
import com.goodsogood.ows.model.vo.MeetingScoreMQVo;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.StudyAddListForm;
import com.goodsogood.ows.service.rabbitMQ.Producer;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Log4j2
public class MeetingAndStudyService {

    private final StudyAddListMapper studyAddListMapper;
    private final MeetingUserMapper meetingUserMapper;
    private final Errors errors;
    private final MeetingMapper meetingMapper;
    private final MeetingScoreService meetingScoreService;
    private final MeetingTaskMapper meetingTaskMapper;
    private final MeetingTypeMapper meetingTypeMapper;
    private final Producer producer;
    private final RabbitmqQueueConfig rabbitmqQueueConfig;


    @Autowired
    public MeetingAndStudyService(StudyAddListMapper studyAddListMapper, MeetingUserMapper meetingUserMapper, MeetingMapper meetingMapper, Errors errors, MeetingScoreService meetingScoreService, MeetingTaskMapper meetingTaskMapper, MeetingTypeMapper meetingTypeMapper, Producer producer, RabbitmqQueueConfig rabbitmqQueueConfig) {
        this.studyAddListMapper = studyAddListMapper;
        this.meetingUserMapper = meetingUserMapper;
        this.meetingMapper = meetingMapper;
        this.errors = errors;
        this.meetingScoreService = meetingScoreService;
        this.meetingTaskMapper = meetingTaskMapper;
        this.meetingTypeMapper = meetingTypeMapper;
        this.producer = producer;
        this.rabbitmqQueueConfig = rabbitmqQueueConfig;
    }

    public Page<StudyAddListForm> selList(StudyAddListForm studyAddListForm){
        Integer pageSize = studyAddListForm.getPageSize();//
        Integer pageNum = studyAddListForm.getPageNum();
        Page<StudyAddListForm> list = PageHelper.startPage(pageNum, pageSize).doSelectPage(()->studyAddListMapper.selList(studyAddListForm));
        return list;
    }

    public Boolean addStudyCert(AddStudyCertForm addStudyCertForm,HeaderHelper.SysHeader header){
        Long id = addStudyCertForm.getWaitSignId();
        MeetingWaitSignEntity entity = studyAddListMapper.selectByPrimaryKey(id);
        if(entity==null){
            return true;
        }
        //判断是否为本人
        Long user_id_put = header.getUserId();
        Long user_id_db = entity.getUserId();
        if(user_id_put.longValue()!=user_id_db.longValue()){
            throw new ApiException("只能由本人进行补学", new Result(errors, 3053, HttpStatus.OK.value()));
        }
        Short isNow = (entity==null? 1 : entity.getIsNow());
        if(isNow!=1){
            throw new ApiException("补学消息已过期，请选择其他消息", new Result(errors, 3052, HttpStatus.OK.value()));
        }
        entity.setContent(addStudyCertForm.getContent());
        entity.setFile(JsonUtils.toJson(addStudyCertForm.getFile()));
        entity.setImgFile(JsonUtils.toJson(addStudyCertForm.getImgFile()));

        Date nowDate =  DateUtils.today();
        Date now = new Date();
        entity.setUpdateTime(now);
        //获取补学截止日期
        Long meetingId = entity.getMeetingId();
        MeetingEntity meetingEntity = meetingMapper.selectByPrimaryKey(meetingId);
        Date endDate = meetingEntity.getSignTime();
        Long meetingUserId = entity.getMeetingUserId();
        MeetingUserEntity meetingUserEntity = meetingUserMapper.selectByPrimaryKey(meetingUserId);
        Short oldStatus = meetingUserEntity.getSignStatus();
        //如果是补学，更新t_meeting_user old_sign_status
        Short type = addStudyCertForm.getType();
        updateStudy(type,oldStatus,meetingUserId,entity,nowDate,endDate,now);
        //在规定时间内补学后增加用户积分   tc 2021-12-09
        if(entity.getType()==2){
            log.debug("调用补学后增加人员积分 regionId={} meeting={} isCancel={}",header.getRegionId(),meetingEntity,false);
            // 查询出活动绑定的活动类型
            List<MeetingTypeEntity> typeList = meetingTypeMapper.findByMeetingId(meetingId);
            if (!ListUtils.isEmpty(typeList)) {
                typeList.forEach(
                        ty -> {
                            // 查找对应MeetingTask的开始时间
                            MeetingTaskEntity task = meetingTaskMapper.selectByPrimaryKey(ty.getMeetingTaskId());
                            Date queryDate = task.getStartTime();
                            Integer typeId = ty.getTypeId().intValue();
//                          meetingScoreService.userScoreAdd(header.getRegionId(),meetingId,typeId,1, Collections.singletonList(entity.getUserId()),queryDate,0);   丢入队列 tc 2022-01-25
                            Map<String,String> p = new HashMap<>();
                            p.put("regionId",header.getRegionId().toString());
                            p.put("meetingId",meetingId.toString());
                            p.put("typeId",typeId.toString());
                            p.put("scoreType","1");
                            p.put("userId",entity.getUserId().toString());
                            p.put("queryDate",DateUtils.dateFormat(queryDate,"yyyy-MM-dd"));
                            p.put("isRollBack","0");
                            MeetingScoreMQVo msmq = new MeetingScoreMQVo(null,null,null,p, Config.MeetingScoreConf.MQ_USER_SCORE_ADD);
                            producer.send(Config.RabbitmqConf.DIRECT_EXCHANGE_NAME,rabbitmqQueueConfig.getMeetingScoreRouting(), JsonUtils.toJson(msmq));
                        });
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStudy(Short type,Short oldStatus,Long meetingUserId,MeetingWaitSignEntity entity,Date nowDate,Date endDate,Date now){
        if(type==1){
            //将原来状态放入old_sign_status,并将新状态更新为补学t_meeting_user
            meetingUserMapper.updateMeetingUserOldStatus(oldStatus,meetingUserId);
            //判断是否超出截止时间
            if(nowDate.after(endDate)){
                //将当前状态更新为超期补学
                entity.setType((short) 3);
            }else{
                //将当前状态更新为补学完成
                entity.setType((short) 2);
            }
            entity.setSignTime(now);
        }else{//状态为草稿
            entity.setType((short) 5);
        }
        studyAddListMapper.updateByPrimaryKeySelective(entity);
    }
}
