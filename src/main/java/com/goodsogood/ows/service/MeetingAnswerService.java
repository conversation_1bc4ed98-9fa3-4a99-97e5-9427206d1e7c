package com.goodsogood.ows.service;

import com.goodsogood.ows.common.ResultConstant;
import com.goodsogood.ows.common.TopicConstant;
import com.goodsogood.ows.common.pojo.Headers;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 纪实结果 - service
 * <AUTHOR>
 * @create 2018-10-24 16:11
 **/
@Service
@Log4j2
public class MeetingAnswerService {

    private final Errors errors;
    private final TopicService topicService;
    private final TopicLogMapper topicLogMapper;
    private final TopicOrgMapper topicOrgMapper;
    private final TopicLogService topicLogService;
    private final MeetingTopicMapper meetingTopicMapper;
    private final TopicLogFileMapper topicLogFileMapper;
    private final OpenService openService;
    private final TopicLogInsertMapper topicLogInsertMapper;
    private final TopicContentService topicContentService;
    private final IndexService indexService;

    private final MeetingResultHsService meetingResultHsService;

    @Autowired
    public MeetingAnswerService(
            Errors errors,
            TopicLogMapper topicLogMapper,
            TopicOrgMapper topicOrgMapper,
            MeetingTopicMapper meetingTopicMapper,
            TopicLogFileMapper topicLogFileMapper,
            OpenService openService,
            MeetingResultHsService meetingResultHsService,
            TopicService topicService,
            TopicLogService topicLogService,
            TopicLogInsertMapper topicLogInsertMapper,
            TopicContentService topicContentService,
            IndexService indexService) {
        this.errors = errors;
        this.topicOrgMapper = topicOrgMapper;
        this.topicLogFileMapper = topicLogFileMapper;
        this.openService = openService;
        this.topicService = topicService;
        this.topicLogMapper = topicLogMapper;
        this.topicLogService = topicLogService;
        this.meetingTopicMapper = meetingTopicMapper;
        this.meetingResultHsService = meetingResultHsService;
        this.topicLogInsertMapper = topicLogInsertMapper;
        this.topicContentService = topicContentService;
        this.indexService = indexService;
    }

    /**
     * 回答活动所选的任务
     *
     * @param sysHeader  头信息
     * @param topicOrgId 任务组织关联表id
     * @param orgId      组织id
     */
    public TopicSubmitPreconditionStatus.STATUS topicSubmitPreconditionCheck(HeaderHelper.SysHeader sysHeader, Long topicOrgId, Long orgId) {
        // 查询orgId组织的管理员，不包含当前用户时，提示无权限
        List<SendMsgForm> adminList = openService.getAdmin(sysHeader.getRegionId(), Collections.singletonList(orgId),null);
        if (CollectionUtils.isEmpty(adminList) || adminList.stream().noneMatch(m -> sysHeader.getUserId().equals(m.getUserId()))) {
            return TopicSubmitPreconditionStatus.STATUS.NO_PERMISSION;
        }
        // 查询任务是否存在
        TopicOrgEntity topicOrgEntity = topicOrgMapper
                .findOne(topicOrgId, TopicConstant.STATUS_LEADER);
        if (topicOrgEntity == null) {
            return TopicSubmitPreconditionStatus.STATUS.REVOKED;
        }
        // 任务组织与提交时指定组织不一致
        if (!orgId.equals(topicOrgEntity.getOrgId())) {
            return TopicSubmitPreconditionStatus.STATUS.NO_PERMISSION;
        }
        // 完成状态
        if (topicOrgEntity.getStatus().equals(2)) {
            return TopicSubmitPreconditionStatus.STATUS.DONE;
        }
        return TopicSubmitPreconditionStatus.STATUS.OK;
    }

    /**
     * 回答活动所选的任务
     *
     * @param reportTopicContentForm 提交信息
     * @param header                 头信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void topicSubmit(HeaderHelper.SysHeader sysHeader, Headers header, MeetingReportTopicContentForm reportTopicContentForm) {
        if (reportTopicContentForm.getTopicOrgId() == null) {
            throw new ApiException("参数错误", new Result<>(errors, 1804, HttpStatus.OK.value(), "topic_org_id不能为空"));
        }
        TopicSubmitPreconditionStatus.STATUS status = topicSubmitPreconditionCheck(sysHeader, reportTopicContentForm.getTopicOrgId(), reportTopicContentForm.getOrgId());
        if (status != TopicSubmitPreconditionStatus.STATUS.OK) {
            throw new ApiException("参数错误", new Result<>(errors, 1804, HttpStatus.OK.value(), status.getErrMsg()));
        }
        MeetingReportForm reportForm = new MeetingReportForm();
        reportForm.setTopics(Collections.singletonList(reportTopicContentForm));
        this.answer(reportForm, header, ResultConstant.HAS_NO_MEETING);
        //更改t_topic_org完成状态
        this.updateTopicOrgStatus(reportTopicContentForm.getTopicOrgId());
        // 提交报告 刷新首页缓存
        indexService.collectRedis();
    }

    /**
     * 回答活动所选的任务
     *
     * @param reportForm
     * @param header
     * @param type       0：直接填写， 1：有活动流程
     */
    @Transactional(rollbackFor = Exception.class)
    public void answer(MeetingReportForm reportForm, Headers header, int type) {
        if (!ListUtils.isEmpty(reportForm.getTopics())) {
            if (type == ResultConstant.HAS_MEETING && StringUtils.isEmpty(reportForm.getMeetingId())) {
                throw new ApiException("参数错误", new Result<>(errors, 1804, HttpStatus.OK.value(), "活动id不能为空"));
            }
            String remark = "", beforeJson = "";
            //step1、插入任务答案
            //支持多个任务，所以先遍历每个任务
            for (MeetingReportTopicContentForm topicContent : reportForm.getTopics()) {
                if (type == ResultConstant.HAS_MEETING && StringUtils.isEmpty(topicContent.getMeetingTopicId())) {
                    throw new ApiException("参数错误", new Result<>(errors, 1804, HttpStatus.OK.value(), "活动与任务的绑定id(meeting_topic_id)不能为空"));
                }
                remark = "新增任务答案";
                boolean isAnswer = this.isAnswered(topicContent.getTopicId(), topicContent.getMeetingTopicId(), topicContent.getTopicOrgId());
                //判断该活动的该任务是否已经有答案，如果有答案，则删除以前的回答，插入新的记录，并且写入历史
                if (isAnswer) {
                    log.debug("该活动绑定的任务已经有答案了，执行修改");
                    //修改、 先删除该任务所有的答案
                    remark = "修改任务答案";
                    beforeJson = JsonUtils.toJson(
                            this.answerDetail(topicContent.getMeetingTopicId(), topicContent.getTopicId(), topicContent.getTopicOrgId()));
                    this.deleteAnswer(topicContent.getTopicId(), topicContent.getMeetingTopicId(), topicContent.getTopicOrgId());
                }
                //*******************************************************************************
                //执行新增操作
                List<TopicLogEntity> addList = new ArrayList<>();
                int seq = 1;
                //遍历每个任务下的内容
                for (MeetingReportAnswerForm answerForm : topicContent.getContents()) {
                    //构造每个日志的对象
                    TopicLogEntity logEntity;
                    if (TopicConstant.isOpts(answerForm.getType())) {
                        List<Integer> opts = answerForm.getOpts();
                        if (!ListUtils.isEmpty(opts)) {
                            for (Integer optId : opts) {
                                if (optId != null && optId != 0) {
                                    logEntity = this.buildTopicLogEntity(reportForm, answerForm, topicContent, header);
                                    logEntity.setOptsId(optId.longValue());

                                    addList.add(logEntity);
                                } else {
                                    throw new ApiException("选项有误", new Result<>(errors, 1804, HttpStatus.OK.value(), "第" + seq + "题，回答选项有误"));
                                }
                            }
                        } else {
                            throw new ApiException("单选题或者多选题，回答不能无选项", new Result<>(errors, 1804, HttpStatus.OK.value(), "第" + seq + "题，单选题或者多选题，回答不能无选项"));
                        }
                    }
                    if (TopicConstant.isContent(answerForm.getType())) {
                        if (StringUtils.isEmpty(answerForm.getContent())) {
                            throw new ApiException("请检查活动内容是否填写完整", new Result<>(errors, 1933, HttpStatus.OK.value()));
                        }
                        logEntity = this.buildTopicLogEntity(reportForm, answerForm, topicContent, header);
                        logEntity.setAnsCnt(answerForm.getContent());
                        if (ListUtils.isEmpty(answerForm.getFiles())) {
                            addList.add(logEntity);
                        } else {
                            this.topicLogInsertMapper.insertUseGeneratedKeys(logEntity);
                            Long topicLogId = logEntity.getTopicLogId();
                            answerForm.getFiles().forEach(file -> {
                                file.setTopicLogId(topicLogId);
                                file.setMeetingTopicId(topicContent.getMeetingTopicId());
                                file.setTopicOrgId(topicContent.getTopicOrgId());
                                file.setCreateUser(header.getUserId());
                                file.setCreateTime(DateTime.now().toDate());
                                file.setTopicId(topicContent.getTopicId());
                                this.topicLogFileMapper.insertSelective(file);
                            });
                        }
                    }
                    seq++;
                }

                //批量插入一个任务的答案
                if (!ListUtils.isEmpty(addList)) {
                    this.topicLogMapper.insertList(addList);
                }

                //step2、更改t_meeting_topic状态
                if (topicContent.getMeetingTopicId() != null) {
                    this.updateMeetingTopicStatus(topicContent.getMeetingTopicId(), header);
                }

                //step4、插入文本修改历史流水表
                this.meetingResultHsService.insertReportHs(beforeJson,
                        JsonUtils.toJson(reportForm), reportForm.getMeetingId(),
                        header.getUserId(), remark);

            }
        }
//        else {
//            throw new ApiException(
//                    "参数错误",
//                    new Result<>(
//                            errors, 1804, HttpStatus.OK.value(), "未填写" + StringCanstant.JOB_TASK + "答案"));
//        }
    }



     /**
      * 构建答案的对象
      * @param reportForm
      * @param answerForm
      * @param topicContent
      * @param header
      * @return
      */
     public TopicLogEntity buildTopicLogEntity(MeetingReportForm reportForm,
                                               MeetingReportAnswerForm answerForm,
                                               MeetingReportTopicContentForm topicContent,
                                               Headers header) {
         TopicLogEntity logEntity = new TopicLogEntity();
         logEntity.setContentId(answerForm.getContentId());
         logEntity.setCreateTime(DateTime.now().toDate());
         logEntity.setCreateUser(header.getUserId());
         logEntity.setTopicId(topicContent.getTopicId());
         logEntity.setMeetingTopicId(topicContent.getMeetingTopicId());
         logEntity.setTopicOrgId(topicContent.getTopicOrgId());
         return logEntity;

     }

     /**
      * 更新活动关联的任务状态
      * @param meetingTopicId
      */
     public void updateMeetingTopicStatus(Long meetingTopicId, Headers header) {
          MeetingTopicEntity update = new MeetingTopicEntity();
          update.setStatus(2);
          update.setMeetingTopicId(meetingTopicId);
          update.setLastChangeUser(header.getUserId());
          update.setUpdateTime(DateTime.now().toDate());
          this.meetingTopicMapper.updateByPrimaryKeySelective(update);
     }

    /**
     * 更新任务与组织的关联状态
     *
     * @param
     */
    public void updateTopicOrgStatus(long topicOrgId) {
        TopicOrgEntity entity = new TopicOrgEntity();
        entity.setStatus(2);

        Example example = new Example(TopicOrgEntity.class);
        example.createCriteria().andEqualTo("topicOrgId", topicOrgId);
        this.topicOrgMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 任务回答的详情
     *
     * @param meetingTopicId
     * @param topicId
     * @return
     */
    public TopicEntity answerDetail(Long meetingTopicId, Long topicId, Long topicOrgId) {
        //查找任务的详情
        TopicEntity topic = this.topicService.detail(topicId);
        if (topic == null) {
            throw new ApiException("not found", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), "任务详情"));
        }
        if (meetingTopicId == null && topicOrgId == null) {
            return topic;
        }
        topic.setTopicOrgId(topicOrgId);
        if (topicOrgId != null) {
            topic.getOrgs().stream().filter(to -> to.getTopicOrgId().equals(topicOrgId))
                    .findFirst()
                    .ifPresent(topicOrgEntity -> topic.setToStatus(topicOrgEntity.getStatus()));
        }
        //查询内容和选项
        List<TopicContentEntity> contents = this.topicContentService.getTopicContentList(topicId);
        if (!ListUtils.isEmpty(contents)) {
            contents = this.topicService.setOpts(contents, topicId);
            topic.setContents(contents);
        }
        //查找任务的答案
        List<TopicLogEntity> logList = this.topicLogService.getToplicLog(meetingTopicId, topicOrgId);
        //if(ListUtils.isEmpty(logList)) {
        //     throw new ApiException("not found", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), "任务答案"));
        //}
        topicLogService.topicAnswerSetToContent(logList, topic.getContents());
        //去掉返回值
        //topic.setOrgs(null);
        //topic.setFiles(null);

        return topic;
    }

    /**
     * 判断是否被回答过
     *
     * @return true: 被回答过 false: 没有
     */
    public boolean isAnswered(long topicId, Long meetingTopicId, Long topicOrgId) {
        return this.topicLogMapper.getMeetingTopicCount(topicId, meetingTopicId, topicOrgId) > 0;
    }

    /**
     * 删除一个活动题相关的数据
     *
     * @param topicId
     * @param meetingTopicId
     */
    public void deleteAnswer(long topicId, Long meetingTopicId, Long topicOrgId) {
        this.topicLogMapper.deleteLog(topicId, meetingTopicId, topicOrgId);
        this.topicLogMapper.deleteLogFile(topicId, meetingTopicId, topicOrgId);
    }

}
