package com.goodsogood.ows.service;

import com.aliyun.dingtalkconference_1_0.Client;
import com.aliyun.dingtalkconference_1_0.models.*;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 天子钉-视频会议实现类
 *
 */
@Service
@Log4j2
public class TzdVieoConferencesService {

    @Value("${tzd.app-key}")
    @NotNull
    private String appKey;

    @Value("${tzd.app-secret}")
    @NotNull
    private String appSecret;

    private final RestTemplate generalRestTemplate;

    private final RedisTemplate redisTemplate;

    private final OpenService openService;

    @Autowired
    public TzdVieoConferencesService(RestTemplate generalRestTemplate, RedisTemplate redisTemplate, OpenService openService) {

        this.generalRestTemplate = generalRestTemplate;
        this.redisTemplate = redisTemplate;
        this.openService = openService;
    }

    /**
     * 天子钉-创建视频会议API
     *
     * @param unionId                           会议发起人的unionId。
     * @param confTitle                         会议标题
     * @param inviteUnionIds                    邀请参会人员unionId列表
     * @return
     */
    public CreateVideoConferenceResponse createVideoConference(String unionId, String confTitle, List<String> inviteUnionIds) {
        //获取token
        String token = getTZDAccessToken();
        if(StringUtils.isEmpty(token)){
            return null;
        }
        try {
            Config config = new Config();
            config.protocol = "https";
            config.regionId = "central";
            Client client = new Client(config);
            CreateVideoConferenceHeaders createVideoConferenceHeaders = new CreateVideoConferenceHeaders();
            createVideoConferenceHeaders.xAcsDingtalkAccessToken = token;
            CreateVideoConferenceRequest createVideoConferenceRequest = new CreateVideoConferenceRequest();
            createVideoConferenceRequest.setConfTitle(confTitle);
            createVideoConferenceRequest.setUserId(unionId);
            createVideoConferenceRequest.setInviteUserIds(inviteUnionIds);
            createVideoConferenceRequest.setInviteCaller(true);
            CreateVideoConferenceResponse response = client.createVideoConferenceWithOptions(createVideoConferenceRequest, createVideoConferenceHeaders, new RuntimeOptions());
            return response;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }
        }
        return null;
    }

    /**
     * 天子钉-创建预约会议API
     *
     * @param unionId                           会议发起人的unionId。
     * @param confTitle                         会议标题
     * @return
     */
    public CreateScheduleConferenceResponse createScheduleConference(String unionId, String confTitle, Date startTime, Date endTime) {
        //获取token
        String token = getTZDAccessToken();
        if(StringUtils.isEmpty(token)){
            return null;
        }
        try {
            Config config = new Config();
            config.protocol = "https";
            config.regionId = "central";
            Client client = new Client(config);
            CreateScheduleConferenceHeaders createScheduleConferenceHeaders = new CreateScheduleConferenceHeaders();
            createScheduleConferenceHeaders.xAcsDingtalkAccessToken = token;
            CreateScheduleConferenceRequest createScheduleConferenceRequest = new CreateScheduleConferenceRequest();
            createScheduleConferenceRequest.setCreatorUnionId(unionId);
            createScheduleConferenceRequest.setTitle(confTitle);
            createScheduleConferenceRequest.setStartTime(startTime.getTime());
            createScheduleConferenceRequest.setEndTime(endTime.getTime());
            CreateScheduleConferenceResponse response = client.createScheduleConferenceWithOptions(createScheduleConferenceRequest, createScheduleConferenceHeaders, new RuntimeOptions());
            return response;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }
        }
        return null;
    }

    /**
     * 天子钉-修改预约会议API
     *
     * @param unionId                           会议发起人的unionId。
     * @param confTitle                         会议标题
     * @return
     */
    public UpdateScheduleConferenceResponse updateScheduleConference(String tzdConferenceId,String unionId, String confTitle, Date startTime, Date endTime) {
        //获取token
        String token = getTZDAccessToken();
        if(StringUtils.isEmpty(token)){
            return null;
        }
        try {
            Config config = new Config();
            config.protocol = "https";
            config.regionId = "central";
            Client client = new Client(config);
            UpdateScheduleConferenceHeaders updateScheduleConferenceHeaders = new UpdateScheduleConferenceHeaders();
            updateScheduleConferenceHeaders.xAcsDingtalkAccessToken = token;
            UpdateScheduleConferenceRequest updateScheduleConferenceRequest = new UpdateScheduleConferenceRequest();
            updateScheduleConferenceRequest.setCreatorUnionId(unionId);
            updateScheduleConferenceRequest.setTitle(confTitle);
            updateScheduleConferenceRequest.setStartTime(startTime.getTime());
            updateScheduleConferenceRequest.setEndTime(endTime.getTime());
            updateScheduleConferenceRequest.setScheduleConferenceId(tzdConferenceId);
            UpdateScheduleConferenceResponse response = client.updateScheduleConferenceWithOptions(updateScheduleConferenceRequest, updateScheduleConferenceHeaders, new RuntimeOptions());
            return response;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }
        }
        return null;
    }

    /**
     * 天子钉-取消预约会议API
     *
     * @param unionId                           会议发起人的unionId。
     * @return
     */
    public CancelScheduleConferenceResponse cancelScheduleConference(String tzdConferenceId,String unionId) {
        //获取token
        String token = getTZDAccessToken();
        if(StringUtils.isEmpty(token)){
            return null;
        }
        try {
            Config config = new Config();
            config.protocol = "https";
            config.regionId = "central";
            Client client = new Client(config);
            CancelScheduleConferenceHeaders cancelScheduleConferenceHeaders = new CancelScheduleConferenceHeaders();
            cancelScheduleConferenceHeaders.xAcsDingtalkAccessToken = token;
            CancelScheduleConferenceRequest cancelScheduleConferenceRequest = new CancelScheduleConferenceRequest();
            cancelScheduleConferenceRequest.setCreatorUnionId(unionId);
            cancelScheduleConferenceRequest.setScheduleConferenceId(tzdConferenceId);
            CancelScheduleConferenceResponse response = client.cancelScheduleConferenceWithOptions(cancelScheduleConferenceRequest, cancelScheduleConferenceHeaders, new RuntimeOptions());
            return response;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }
        }
        return null;
    }

    /**
     * 天子钉-查询视频会议信息
     *
     * @param tzdConferenceId                           天子钉的会议id
     * @return
     */
    public QueryConferenceInfoResponse queryConferenceInfoWithOptions(String tzdConferenceId) {
        //获取token
        String token = getTZDAccessToken();
        if(StringUtils.isEmpty(token)){
            return null;
        }
        try {
            Config config = new Config();
            config.protocol = "https";
            config.regionId = "central";
            Client client = new Client(config);
            QueryConferenceInfoHeaders queryConferenceInfoHeaders = new QueryConferenceInfoHeaders();
            queryConferenceInfoHeaders.xAcsDingtalkAccessToken = token;
            QueryConferenceInfoResponse response = client.queryConferenceInfoWithOptions(tzdConferenceId,queryConferenceInfoHeaders, new RuntimeOptions());
            return response;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }
        }
        return null;
    }

    /**
     * 天子钉-查询预约会议信息
     *
     * @param tzdConferenceId                           天子钉的会议id
     * @return
     */
    public QueryScheduleConferenceInfoResponse queryScheduleConferenceInfoWithOptions(String tzdConferenceId) {
        //获取token
        String token = getTZDAccessToken();
        if(StringUtils.isEmpty(token)){
            return null;
        }
        try {
            Config config = new Config();
            config.protocol = "https";
            config.regionId = "central";
            Client client = new Client(config);
            QueryScheduleConferenceInfoHeaders queryScheduleConferenceInfoHeaders = new QueryScheduleConferenceInfoHeaders();
            queryScheduleConferenceInfoHeaders.xAcsDingtalkAccessToken = token;
            QueryScheduleConferenceInfoRequest scheduleConferenceInfoRequest = new QueryScheduleConferenceInfoRequest();
            QueryScheduleConferenceInfoResponse response = client.queryScheduleConferenceInfoWithOptions(tzdConferenceId,scheduleConferenceInfoRequest,queryScheduleConferenceInfoHeaders, new RuntimeOptions());
            return response;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Exception,code:" + err.code + ",message:" + err.message);
            }
        }
        return null;
    }

    /**
     * 获取天子钉accessToken
     *
     * @return
     */
    public String getTZDAccessToken(){
        //先从redis获取
        String tokenKey = "TZDAccessToken";
        if(this.redisTemplate.hasKey(tokenKey)){
            return this.redisTemplate.opsForValue().get(tokenKey).toString();
        }
        try {
            Config config = new Config();
            config.protocol = "https";
            config.regionId = "central";
            com.aliyun.dingtalkoauth2_1_0.Client client = new com.aliyun.dingtalkoauth2_1_0.Client(config);
            GetAccessTokenRequest getAccessTokenRequest = new GetAccessTokenRequest();
            getAccessTokenRequest.setAppKey(appKey);
            getAccessTokenRequest.setAppSecret(appSecret);
            GetAccessTokenResponse response = client.getAccessToken(getAccessTokenRequest);
            String token =  response.getBody().getAccessToken();
            //token过期时间为两小时，redis设置为一小时重新获取，会刷新token过期时间
            this.redisTemplate.opsForValue().set(tokenKey,token,1, TimeUnit.HOURS);
            return token;
        }catch (TeaException err){
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Access-Token-TeaException,code:"+err.code+",message:"+err.message);
            }
            return null;
        }catch (Exception _err){
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("Access-Token-Exception,code:"+err.code+",message:"+err.message);
            }
            return null;
        }
    }

}
