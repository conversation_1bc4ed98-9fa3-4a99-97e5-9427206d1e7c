package com.goodsogood.ows.service

import com.fasterxml.jackson.core.type.TypeReference
import com.goodsogood.ows.configuration.ClientExceptionHandler
import com.goodsogood.ows.configuration.TogServicesConfig
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.RemoteApiHelper
import com.goodsogood.ows.model.vo.*
import com.goodsogood.ows.model.vo.activity.OrganizationBase
import com.goodsogood.ows.model.vo.activity.UserInfoBase
import com.goodsogood.ows.utils.JsonUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Service
import org.springframework.util.CollectionUtils
import org.springframework.web.client.RestTemplate
import java.util.*

/**
 * 第三方远程接口调用服务类
 *
 * <AUTHOR> tc
 * @date 2021/3/18
 */
@Service
class ThirdService(
    @Autowired val restTemplate: RestTemplate,
    @Autowired val togServicesConf: TogServicesConfig
) {

    private val log = LoggerFactory.getLogger(ThirdService::class.java)

    /**
     * 根据组织ID查询组织信息
     * orgId 组织编号
     * <AUTHOR>
     */
    fun findOrgInfoByOrgId(orgId: Long?): OrganizationBase? {
        var re: OrganizationBase? = null
        val headers = HttpHeaders()
        restTemplate.errorHandler = ClientExceptionHandler()
        var count = 0
        do {
            try {
                re = RemoteApiHelper.get(
                    restTemplate, String.format(
                        "http://%s/find-org-by-id?org_id=%s",
                        togServicesConf.userCenter, orgId
                    ), headers, object : TypeReference<Result<OrganizationBase?>?>() {})
            } catch (e: Exception) {
                log.error("根据组织ID查询组织信息失败！ orgId ={}  第{}次调用", orgId, count + 1, e)
            }
            count++
        } while (null == re && count < 5)
        log.debug("根据组织ID查询组织信息结果:查询 orgId ={}  结果 res ={} 调用数{}次", orgId, re, count)
        return if (re != null && re.organizationId != null) re else null
    }

    /**
     * 根据组织ID集合查询组织权重排序
     * orgIdList 组织编号集合
     * <AUTHOR>
     */
    fun findOrgWeightByOrgId(regionId: Long, orgIdList: List<Long?>): List<Long>? {
        var re: List<Long>? = null
        val headers = HttpHeaders()
        headers.add("_region_id", regionId.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        val body: MutableMap<String, Any> = HashMap()
        body["id_list"] = orgIdList
        var count = 0
        do {
            try {
                re = RemoteApiHelper.post(
                    restTemplate, String.format(
                        "http://%s/org/sort-org-by-list",
                        togServicesConf.userCenter
                    ), JsonUtils.toJson(body), headers, object : TypeReference<Result<List<Long>?>?>() {})
            } catch (e: Exception) {
                log.error("根据组织ID查询组织信息失败！ orgIdList ={}  第{}次调用", orgIdList, count + 1, e)
            }
            count++
        } while (null == re && count < 5)
        log.debug("根据组织ID查询组织信息结果:查询 orgIdList ={}  结果 res ={} 调用数{}次", orgIdList, re, count)
        return if (re != null && re.size > 0) re else null
    }

    /**
     * 根据用户编号集合，添加任务管理中心任务
     * @param regionId  区县编号
     * @param beginTime   开始时间
     * @param endTime   结束时间(如果没有结束时间时填充为开始时间)
     * @param taskTitle 任务标题
     * @param sourceMark  第三方标签(用 ~~ 分隔)
     * @param sourceUri  对应服务网关前缀
     * @param createOrg  创建组织id
     * @param userIdList 接收用户id集合
     * @return  新增任务数量
     * <AUTHOR>
     * @date 2021/9/26 13:52
     */
    fun addTaskByUserIdList(
        regionId: Long,
        beginTime: Date,
        endTime: Date,
        taskTitle: String,
        sourceMark: String,
        sourceUri: String,
        createOrg: Long,
        createUserId: Long,
        userIdList: List<Long?>
    ): Int? {
        //返回的用户编号
        var re: Int? = null
        val headers = HttpHeaders()
        headers.add("_region_id", regionId.toString())
        headers.add("_uid", createUserId.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        val body: MutableMap<String, Any> = HashMap()
        body["callback"] = 0
        body["region_id"] = regionId
        body["begin_time"] = beginTime
        body["end_time"] = endTime
        body["task_title"] = taskTitle
        body["source_mark"] = sourceMark
        body["source_uri"] = sourceUri
        body["create_org"] = createOrg
        body["users"] = userIdList
        var count = 0
        do {
            try {
                re = RemoteApiHelper.post(
                    restTemplate, String.format(
                        "http://%s/task/third-add",
                        togServicesConf.taskManager
                    ), JsonUtils.toJson(body), headers, object : TypeReference<Result<Int?>?>() {})
            } catch (e: Exception) {
                log.error(
                    "<添加任务管理中心任务>失败！regionId ={} beginTime ={} endTime ={} taskTitle ={} sourceMark ={} sourceUri ={} createOrg ={} userIdList ={}  第{}次调用",
                    regionId, beginTime, endTime, taskTitle, sourceMark, sourceUri, createOrg, userIdList, count + 1, e
                )
            }
            count++
        } while (null == re && count < 5)
        log.debug(
            "<添加任务管理中心任务>结果:查询 regionId ={} beginTime ={} endTime ={} taskTitle ={} sourceMark ={} sourceUri ={} createOrg ={} userIdList ={}  结果 res ={} 调用数{}次",
            regionId, beginTime, endTime, taskTitle, sourceMark, sourceUri, createOrg, userIdList, re, count
        )
        return re
    }

    /**
     * 添加支部风采和发展史
     * type         number		Y	类型	1.支部风采 2.支部发展史
     * desc	        string		Y	描述
     * node_time	string		N	获得时间	当type=2 当支部发展史，这个字段必传
     * url	        string		Y	图片或者视频地址 （系统自动调用没有就不传）	多个以逗号分割
     * org_id	    number		Y	当前用户所在组织id
     * <AUTHOR>
     */
    fun highlightAdd(headers: HttpHeaders?, param: Map<String, Any>): Long? {
        log.debug("添加支部风采和发展史  调用用户中心  :条件 headers ={}  param ={} ", headers, param)
        var re: Long? = null
        restTemplate.errorHandler = ClientExceptionHandler()
        try {
            re = RemoteApiHelper.post(
                restTemplate, String.format(
                    "http://%s/person-center/org/highlight/add",
                    togServicesConf.userCenter
                ), param, headers, object : TypeReference<Result<Long?>?>() {})
        } catch (e: Exception) {
            log.error("添加支部风采和发展史  失败！ param ={}  ", param, e)
        }
        log.debug("添加支部风采和发展史  结果:条件  param ={}  结果 re ={}", param, re)
        return re
    }

    /**
     * 调用任务底座，新增任务
     * pendingForm 任务封装信息
     * <AUTHOR>
     */
    fun insertPending(
        headers: HttpHeaders?,
        regionId: Long?,
        userId: Long?,
        clearType: Int?,
        status: Int?,
        createTodoForm: CreateTodoForm?
    ): String? {
        log.debug(
            "调用任务底座，新增任务  userId={},clearType={},status={},createTodoForm={}",
            userId,
            clearType,
            status,
            createTodoForm
        )
        //返回的任务id(钉钉代办编号)
        var re: String? = null
        restTemplate.errorHandler = ClientExceptionHandler()
        var count = 0
        //        do {
        try {
            re = RemoteApiHelper.post(
                restTemplate, String.format(
                    "http://%s/insert-third?user_id=%s&clear_type=%s&status=%s",
                    togServicesConf.pendingServer, userId, clearType, status
                ), JsonUtils.toJson(createTodoForm), headers, object : TypeReference<Result<String?>?>() {})
        } catch (e: Exception) {
            log.error(
                "<新增任务>失败！ userId={},clearType={},status={},createTodoForm={}  第{}次调用",
                userId,
                clearType,
                status,
                createTodoForm,
                count + 1,
                e
            )
        }
        count++
        //        } while (null == re && count < 5);
        log.debug(
            "<新增任务>结果:查询 userId={},clearType={},status={},createTodoForm ={}  结果 res ={} 调用数{}次",
            userId,
            clearType,
            status,
            createTodoForm,
            re,
            count
        )
        return re
    }

    /**
     * 调用任务底座，删除任务
     * pendingForm 任务封装信息
     * <AUTHOR>
     */
    fun delPending(
        headers: HttpHeaders?,
        thirdTaskId: MutableList<String>?
    ) {
        log.debug(
            "调用任务底座，删除任务  thirdTaskId={}",
            thirdTaskId
        )
        var re: String? = null
        restTemplate.errorHandler = ClientExceptionHandler()
        var count = 0
        try {
            re = RemoteApiHelper.get(
                restTemplate, String.format(
                    "http://%s/del-third?source_ids=%s",
                    togServicesConf.pendingServer, thirdTaskId?.joinToString(",")
                ), headers, object : TypeReference<Result<String?>?>() {})
        } catch (e: Exception) {
            log.error(
                "<删除任务>失败！ thirdTaskId={} 第{}次调用",
                thirdTaskId,
                count + 1,
                e
            )
        }
        count++
        log.debug(
            "<删除任务>结果:查询 thirdTaskId={} 结果 res ={} 调用数{}次",
            thirdTaskId,
            re,
            count
        )
    }

    /**
     * 调用任务底座，完成代办任务
     * userId 用户编号
     * taskId 任务编号
     * <AUTHOR>
     */
    fun finishPending(regionId: Long, userId: Long?, sourceId: String?): String? {
        //返回的用户编号
        var re: String? = null
        val headers = HttpHeaders()
        headers.add("_region_id", regionId.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        var count = 0
        do {
            try {
                re = RemoteApiHelper.get(
                    restTemplate, String.format(
                        "http://%s/finish-third?user_id=%s&source_id=%s&status=2",
                        togServicesConf.pendingServer, userId, sourceId
                    ), headers, object : TypeReference<Result<String?>?>() {})
            } catch (e: Exception) {
                log.error("<完成任务>失败！userId ={} sourceId={}  第{}次调用", userId, sourceId, count + 1, e)
            }
            count++
        } while (null == re && count < 5)
        log.debug("<完成任务>结果:查询 userId ={} sourceId={}  结果 res ={} 调用数{}次", userId, sourceId, re, count)
        return re
    }

    /**
     * 根据用户编号增加个人积分
     * scoreConsumeForm 积分封装信息
     * <AUTHOR>
     */
    fun addScoreByUserId(regionId: Long, scoreConsumeForm: ScoreConsumeForm): Long? {
        //返回的用户编号
        var re: Long? = null
        val headers = HttpHeaders()
        headers.add("access_key", "ows")
        headers.add("_region_id", regionId.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        val body: MutableMap<String, Any?> = HashMap()
        body["token"] = scoreConsumeForm.token
        body["org_id"] = scoreConsumeForm.orgId
        body["user_id"] = scoreConsumeForm.userId
        //积分类型
        body["score_type"] = scoreConsumeForm.scoreType
        //操作类型 0：新增，1：扣分
        body["oper_type"] = scoreConsumeForm.operType
        body["score"] = scoreConsumeForm.score
        body["explain_txt"] = scoreConsumeForm.explainTxt
        body["remark"] = scoreConsumeForm.remark
        body["consume_time"] = scoreConsumeForm.consumeTime
        var count = 0
        do {
            try {
                re = RemoteApiHelper.post(
                    restTemplate, String.format(
                        "http://%s/score/consume/nocheck/nopwd",
                        togServicesConf.creditCenter
                    ), JsonUtils.toJson(body), headers, object : TypeReference<Result<Long?>?>() {})
            } catch (e: Exception) {
                log.error(
                    "<根据用户编号增加个人积分>失败11！scoreConsumeForm ={}  第{}次调用",
                    JsonUtils.toJson(scoreConsumeForm),
                    count + 1,
                    e
                )
            }
            count++
            try {
                Thread.sleep(1000)
            } catch (e: InterruptedException) {
            }
        } while (null == re && count < 5)
        log.debug(
            "<根据用户编号增加个人积分>结果11:查询 scoreConsumeForm ={}  结果 res ={} 调用数{}次",
            JsonUtils.toJson(scoreConsumeForm),
            re,
            count
        )
        return re
    }


    /**
     * 根据用户编号增加到doris
     *
     * <AUTHOR>
     */
    fun addDorisUserScore(regionId: Long?, dataList: List<IndexUserScoreForm>):Int? {
        if (CollectionUtils.isEmpty(dataList)) {
            return 2
        }
        log.debug("doris表添加用户数据=>{}", JsonUtils.toJson(dataList))
        //返回的用户编号
        val region = regionId ?: 19
        val headers = HttpHeaders()
        headers.add("access_key", "ows")
        headers.add("_region_id", region.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        return try {
            val ret = RemoteApiHelper.postAndResult(
                restTemplate, String.format(
                    "http://%s/doris/user-score",
                    togServicesConf.sas
                ), dataList, headers, object : TypeReference<Result<Boolean?>?>() {})
            if ((null != ret && null != ret.data) || ret.data == true) 1 else 2
        } catch (e: Exception) {
            log.error(
                "<调用sas增加人员指标分值>失败11！dataList ={}  第{}次调用",
                JsonUtils.toJson(dataList),
                e.message
            )
            2
        }
    }

    fun addDorisOrgScore(regionId: Long?, dataList: List<IndexOrgScoreForm>):Int? {
        if (CollectionUtils.isEmpty(dataList)) {
            return 2
        }
        log.debug("doris表添加组织数据=>{}", JsonUtils.toJson(dataList))
        //返回的用户编号
        val headers = HttpHeaders()
        val region = regionId ?: 19
        headers.add("access_key", "ows")
        headers.add("_region_id", region.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        return try {
            val ret = RemoteApiHelper.postAndResult(
                restTemplate, String.format(
                    "http://%s/doris/org-score",
                    togServicesConf.sas
                ), dataList, headers, object : TypeReference<Result<Boolean?>?>() {})
            if ((null != ret && null != ret.data) || ret.data == true) 1 else 2
        } catch (e: Exception) {
            log.error(
                "<调用sas增加机构指标分值>失败11！dataList ={}  第{}次调用",
                JsonUtils.toJson(dataList),
                e.message
            )
            2
        }
    }

    /**
     * 根据组织编号增加组织积分
     * scoreConsumeForm 积分封装信息
     * <AUTHOR>
     */
    fun addScoreByOrgId(regionId: Long, scoreConsumeForm: ScoreConsumeForm): Long? {
        //返回的用户编号
        var re: Long? = null
        val headers = HttpHeaders()
        headers.add("access_key", "ows")
        headers.add("_region_id", regionId.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        val body: MutableMap<String, Any?> = HashMap()
        body["token"] = scoreConsumeForm.token
        body["org_id"] = scoreConsumeForm.orgId
        body["score_org_id"] = scoreConsumeForm.scoreOrgId
        //积分类型
        body["score_type"] = scoreConsumeForm.scoreType
        //操作类型 0：新增，1：扣分
        body["oper_type"] = scoreConsumeForm.operType
        body["score"] = scoreConsumeForm.score
        body["explain_txt"] = scoreConsumeForm.explainTxt
        body["remark"] = scoreConsumeForm.remark
        body["consume_time"] = scoreConsumeForm.consumeTime
        var count = 0
        do {
            try {
                re = RemoteApiHelper.post(
                    restTemplate, String.format(
                        "http://%s/score/consume/nocheck/org",
                        togServicesConf.creditCenter
                    ), JsonUtils.toJson(body), headers, object : TypeReference<Result<Long?>?>() {})
            } catch (e: Exception) {
                log.error(
                    "<根据组织编号增加组织积分>失败！scoreConsumeForm ={}  第{}次调用",
                    scoreConsumeForm,
                    count + 1,
                    e
                )
            }
            count++
            try {
                Thread.sleep(1000)
            } catch (e: InterruptedException) {
            }
        } while (null == re && count < 5)
        log.debug(
            "<根据组织编号增加组织积分>结果:查询 scoreConsumeForm ={}  结果 res ={} 调用数{}次",
            scoreConsumeForm,
            re,
            count
        )
        return re
    }

    /**
     * 调用用户中心，获取党小组组长
     * regionId 区县编号
     * orgIdList 党小组组织编号集合 (逗号间隔)
     * <AUTHOR>
     */
    fun findGroupLeader(regionId: Long, orgIdList: String?): Map<Long?, Long?>? {
        log.debug("调用用户中心，获取党小组组长  regionId={},orgIdList={}", regionId, orgIdList)
        var re: Map<Long?, Long?>? = null
        val headers = HttpHeaders()
        headers.add("_region_id", regionId.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        var count = 0
        do {
            try {
                re = RemoteApiHelper.get(
                    restTemplate, String.format(
                        "http://%s/org-group/find-leader-users?org_id=%s",
                        togServicesConf.userCenter, orgIdList
                    ), headers, object : TypeReference<Result<Map<Long?, Long?>?>?>() {})
            } catch (e: Exception) {
                log.error(
                    "<获取党小组组长>失败！ regionId={},orgIdList={} 第{}次调用",
                    regionId,
                    orgIdList,
                    count + 1,
                    e
                )
            }
            count++
        } while (null == re && count < 5)
        log.debug(
            "<获取党小组组长>结果:查询 regionId={},orgIdList={}  结果 re ={} 调用数{}次",
            regionId,
            orgIdList,
            re,
            count
        )
        return re
    }

    /**
     * 调用用户中心，获取支委会成员
     * regionId 区县编号
     * queryTime 查询时间   yyyy-mm-dd
     * <AUTHOR>
     */
    fun findBranchCommittee(regionId: Long, queryTime: String?): List<Long?>? {
        log.debug("调用用户中心，获取支委会成员  regionId={},queryTime={}", regionId, queryTime)
        var re: List<Long?>? = null
        val headers = HttpHeaders()
        headers.add("_region_id", regionId.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        var count = 0
        do {
            try {
                re = RemoteApiHelper.get(
                    restTemplate, String.format(
                        "http://%s/period/find-users-by-time?query_time=%s",
                        togServicesConf.userCenter, queryTime
                    ), headers, object : TypeReference<Result<List<Long?>?>?>() {})
            } catch (e: Exception) {
                log.error(
                    "<获取支委会成员>失败！ regionId={},queryTime={} 第{}次调用",
                    regionId,
                    queryTime,
                    count + 1,
                    e
                )
            }
            count++
        } while (null == re && count < 5)
        log.debug(
            "<获取支委会成员>结果:查询 regionId={},queryTime={}  结果 re ={} 调用数{}次",
            regionId,
            queryTime,
            re,
            count
        )
        return re
    }

    /**
     * 调用用户中心，根据党小组编号获取所属支部信息
     * regionId 区县编号
     * groupId    党小组编号
     * <AUTHOR>
     */
    fun findBranchByGroup(regionId: Long, groupId: Long?): Long? {
        log.debug("调用用户中心，根据党小组编号获取所属支部信息  regionId={},groupId={}", regionId, groupId)
        var re: Long? = null
        val headers = HttpHeaders()
        headers.add("_region_id", regionId.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        var count = 0
        do {
            try {
                re = RemoteApiHelper.get(
                    restTemplate, String.format(
                        "http://%s/org-group/find/branchId/by/orgId?org_id=%s",
                        togServicesConf.userCenter, groupId
                    ), headers, object : TypeReference<Result<Long?>?>() {})
            } catch (e: Exception) {
                log.error(
                    "<根据党小组编号获取所属支部信息>失败！ regionId={},groupId={} 第{}次调用",
                    regionId,
                    groupId,
                    count + 1,
                    e
                )
            }
            count++
        } while (null == re && count < 5)
        log.debug(
            "<根据党小组编号获取所属支部信息>结果:查询 regionId={},groupId={}  结果 re ={} 调用数{}次",
            regionId,
            groupId,
            re,
            count
        )
        return re
    }

    /**
     * 调用用户中心，根据支部编号获取下属党小组信息
     * regionId 区县编号
     * branchId   支部编号
     * <AUTHOR>
     */
    fun findGroupByBranch(regionId: Long, branchId: Long?): List<Long?>? {
        log.debug("调用用户中心，根据支部编号获取下属党小组  regionId={},branchId={}", regionId, branchId)
        var re: List<Long?>? = null
        val headers = HttpHeaders()
        headers.add("_region_id", regionId.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        var count = 0
        do {
            try {
                re = RemoteApiHelper.get(
                    restTemplate, String.format(
                        "http://%s/org-group/find/orgId/by/branchId?branch_id=%s",
                        togServicesConf.userCenter, branchId
                    ), headers, object : TypeReference<Result<List<Long?>?>?>() {})
            } catch (e: Exception) {
                log.error(
                    "<根据支部编号获取下属党小组>失败！ regionId={},branchId={} 第{}次调用",
                    regionId,
                    branchId,
                    count + 1,
                    e
                )
            }
            count++
        } while (null == re && count < 5)
        log.debug(
            "<根据支部编号获取下属党小组>结果:查询 regionId={},branchId={}  结果 re ={} 调用数{}次",
            regionId,
            branchId,
            re,
            count
        )
        return re
    }

    /**
     * 调用用户中心，根据支部编号获取党员你列表
     * regionId 区县编号
     * branchId   支部编号
     * <AUTHOR>
     */
    fun getUserListByOrgId(orgId: Long, isEmployee: Int, headers: HttpHeaders?): List<UserInfoBase> {
        log.debug("调用用户中心，根据支部编号获取党员你列表  orgId=${orgId}")
        val header = HeaderHelper.buildMyHeader(headers)
        var re: List<UserInfoBase> = mutableListOf()
        val form = FindOrgListForm()
        form.idList = mutableListOf(orgId)
        form.isEmployee = isEmployee
        form.isFilter = 1
        form.regionId = header.regionId
        form.isInclude = 1
        restTemplate.errorHandler = ClientExceptionHandler()
        var count = 0
        do {
            try {
                re = RemoteApiHelper.post(restTemplate, String.format(
                    "http://%s/find-user-by-org-id",
                    togServicesConf.userCenter
                ), form, headers, object : TypeReference<Result<List<UserInfoBase>>>() {})
            } catch (e: Exception) {
                log.error("<根据支部编号获取党员你列表>失败！ orgId=${orgId} 第${count + 1}次调用", e)
            }
            count++
        } while (re.isEmpty() && count < 5)
        log.debug("<根据支部编号获取党员你列表>结果:查询 orgId=${orgId} 结果 re =${re} 调用数${count}次")
        return re
    }

    /**
     * 获取用户手写签名url
     */
    fun findUserHandWrite(userId: Long): String? {
        log.debug("调用用户中心，根据用户ID获取手写签名")
        var url: String? = null
        val headers = HttpHeaders()
        restTemplate.errorHandler = ClientExceptionHandler()
        try {
            url = RemoteApiHelper.get(
                restTemplate, String.format(
                    "http://%s//user-expand/get-user-hand-sign?user_id=%s",
                    togServicesConf.userCenter, userId
                ), headers, object : TypeReference<Result<String?>?>() {})
        } catch (e: Exception) {
            log.error(
                "调用用户中心，根据用户ID获取手写签名", e
            )
        }
        return url
    }

    /**
     * 调用用户中心，根据用户ID和组织ID查询人员详情
     * regionId 区县编号
     * branchId   支部编号
     * <AUTHOR>
     */
    fun findUserInfoById(userId: Long, orgId: Long, regionId: Long): UserInfoForm? {
        log.debug("调用用户中心，根据用户ID和组织ID查询人员详情  regionId=${regionId},userId=${userId},orgId=${orgId}")
        var re: UserInfoForm? = null
        val headers = HttpHeaders()
        headers.add("_region_id", regionId.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        var count = 0
        do {
            try {
                re = RemoteApiHelper.get(
                    restTemplate, String.format(
                        "http://%s/org/user/info?user_id=%s&org_id=%s",
                        togServicesConf.userCenter, userId, orgId
                    ), headers, object : TypeReference<Result<UserInfoForm?>?>() {})
            } catch (e: Exception) {
                log.error(
                    "<调用用户中心，根据用户ID和组织ID查询人员详情  regionId=${regionId},userId=${userId},orgId=${orgId} 第${count + 1}次调用",
                    e
                )
            }
            count++
        } while (null == re && count < 5)
        log.debug(
            "<<调用用户中心，根据用户ID和组织ID查询人员详情  regionId=${regionId},userId=${userId},orgId=${orgId}  结果 re = ${re} 调用数${count}次"
        )
        return re
    }

    /**
     * 调用用户中心，根据用户ID查询人员基本信息
     * regionId 区县编号
     * branchId   支部编号
     * <AUTHOR>
     */
    fun findUserInfoByKey(userId: Long, regionId: Long): List<UserInfoBase>? {
        log.debug("调用用户中心，根据用户ID查询人员基本信息  userId=${userId}")
        var re: List<UserInfoBase>? = null
        val headers = HttpHeaders()
        headers.add("_region_id", regionId.toString())
        restTemplate.errorHandler = ClientExceptionHandler()
        var count = 0
        do {
            try {
                re = RemoteApiHelper.get(
                    restTemplate, String.format(
                        "http://%s/find-user-by-key?user_id=%s",
                        togServicesConf.userCenter, userId
                    ), headers, object : TypeReference<Result<List<UserInfoBase>>>() {})
            } catch (e: Exception) {
                log.error(
                    "<调用用户中心，根据用户ID查询人员基本信息  regionId=${regionId},userId=${userId} 第${count + 1}次调用",
                    e
                )
            }
            count++
        } while (null == re && count < 5)
        log.debug(
            "<<调用用户中心，根据用户ID查询人员基本信息  regionId=${regionId},userId=${userId}  结果 re = ${re} 调用数${count}次"
        )
        return re
    }

    fun sendMessage(templateId: Long, headers: HttpHeaders) {
        log.debug("调用推送中心，推送消息  ")

    }
}
