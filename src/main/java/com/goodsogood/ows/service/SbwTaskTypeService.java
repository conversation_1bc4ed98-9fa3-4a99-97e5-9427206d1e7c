package com.goodsogood.ows.service;

import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.mapper.SbwTaskTypeMapper;
import com.goodsogood.ows.model.vo.SbwTaskTypeForm;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Log4j2
public class SbwTaskTypeService {

    private final SbwTaskTypeMapper sbwTaskTypeMapper;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public SbwTaskTypeService(SbwTaskTypeMapper sbwTaskTypeMapper, StringRedisTemplate redisTemplate) {
        this.sbwTaskTypeMapper = sbwTaskTypeMapper;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 舆情分类列表
     * @return
     */
    public List<SbwTaskTypeForm> list(){
        String key = Constant.SBW_TYPE;
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(json)){
            return (List<SbwTaskTypeForm>) JsonUtils.fromJson(json,List.class,SbwTaskTypeForm.class);
        }
        List<SbwTaskTypeForm> list = sbwTaskTypeMapper.list();
        if (CollectionUtils.isNotEmpty(list)){
            redisTemplate.opsForValue().set(key,JsonUtils.toJson(list));
        }
        return list;
    }
}
