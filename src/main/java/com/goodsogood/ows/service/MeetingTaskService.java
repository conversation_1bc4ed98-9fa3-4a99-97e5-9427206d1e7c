package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.MeetingCanstant;
import com.goodsogood.ows.common.OrgTypeConstant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.MeetingTaskCount;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.JsonUtils;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2018-10-24 09:42
 */
@Service
@Log4j2
public class MeetingTaskService {

    @Value("${tog-services.user-center}")
    private String userCenter;

    // 根据组织ID查询所有下级组织
    private static final String FIND_ALL_CHILD_URL =
            "http://%s/org/find-all-child-org?org_id=%s&is_include=1";

    private final MeetingTaskMapper mapper;
    private final MeetingPlanMapper meetingPlanMapper;
    private final MeetingPlanLogMapper meetingPlanLogMapper;
    private final TypeMapper typeMapper;
    private final RestTemplate restTemplate;
    private final Errors errors;
    private final MeetingTaskRedis meetingTaskRedis;
    private final MeetingTaskCount meetingTaskCount;

    private final MeetingTypeMapper meetingTypeMapper;
    private final MeetingRequireMapper meetingRequireMapper;
    private final TopicOrgService topicOrgService;
    private final MeetingTaskServiceAsync meetingTaskServiceAsync;
    private final RegionService regionService;

    @Autowired
    public MeetingTaskService(
            MeetingTaskMapper mapper,
            MeetingPlanMapper meetingPlanMapper,
            MeetingPlanLogMapper meetingPlanLogMapper,
            TypeMapper typeMapper,
            RestTemplate restTemplate,
            Errors errors,
            MeetingTaskRedis meetingTaskRedis,
            MeetingTaskCount meetingTaskCount,
            MeetingTypeMapper meetingTypeMapper,
            MeetingRequireMapper meetingRequireMapper,
            TopicOrgService topicOrgService,
            MeetingTaskServiceAsync meetingTaskServiceAsync, RegionService regionService) {
        this.mapper = mapper;
        this.meetingPlanMapper = meetingPlanMapper;
        this.meetingPlanLogMapper = meetingPlanLogMapper;
        this.typeMapper = typeMapper;
        this.restTemplate = restTemplate;
        this.errors = errors;
        this.meetingTaskRedis = meetingTaskRedis;
        this.meetingTaskCount = meetingTaskCount;
        this.meetingTypeMapper = meetingTypeMapper;
        this.meetingRequireMapper = meetingRequireMapper;
        this.topicOrgService = topicOrgService;
        this.meetingTaskServiceAsync = meetingTaskServiceAsync;
        this.regionService = regionService;
    }

    /**
     * 查询任务来源
     */
    public List<OrgForm> orgListAllByOid(Long orgId) {
        return mapper.findPOrgIdByOrgId(orgId);
    }

    /**
     * 定时派发任务 未删除 执行方式不为1 启用中的组织生活
     *
     * @param now 任务启动时的时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void createAllMeetingTask(HeaderHelper.SysHeader sysHeader, Date now) {
        log.debug("纪实系统定时派发任务开始！");
        // 查询组织生活
        List<MeetingPlanEntity> meetingPlanEntities = meetingPlanMapper.findWithCreateTask(sysHeader.getRegionId());
        meetingPlanEntities.forEach(
                meetingPlanEntity -> {
                    try {
                        meetingTaskServiceAsync.createThisMeetingTask(sysHeader,
                                meetingPlanEntity, now,
                                meetingPlanEntity.getSendType() != null && meetingPlanEntity.getSendType() == MeetingPlanEntity.SEND_TYPE_AUTO,
                                true);
                    } catch (Exception e) {
                        log.error("定时派发任务失败！meetingPlanId:" + meetingPlanEntity.getMeetingPlanId(), e);
                        MeetingPlanLogEntity meetingPlanLogEntity = new MeetingPlanLogEntity();
                        meetingPlanLogEntity.setMeetingPlanId(meetingPlanEntity.getMeetingPlanId());
                        meetingPlanLogEntity.setCreateTime(now);
                        meetingPlanLogEntity.setCreateUser(meetingPlanEntity.getCreateUser());
                        meetingPlanLogEntity.setNum(0);
                        meetingPlanLogMapper.insert(meetingPlanLogEntity);
                    }
                });
        log.debug("纪实系统定时派发任务结束！");
    }

    /**
     * 条件查询活动任务
     */
    public List<MeetingTaskEntity> typeList(MeetingTypeTaskListForm meetingTypeListForm) {
        List<MeetingTaskEntity> tasks = mapper.findByForm(meetingTypeListForm);
        this.setRequire(tasks);
        return tasks;
    }

    /**
     * 条件查询活动任务
     */
    public Page<MeetingTaskEntity> typeListPage(MeetingTypeTaskListForm meetingTypeListForm) {
        Page<MeetingTaskEntity> page =
                PageHelper.startPage(
                        meetingTypeListForm.getPageBean().getPageNo(),
                        meetingTypeListForm.getPageBean().getPageSize())
                        .doSelectPage(() -> this.mapper.findByForm(meetingTypeListForm));
        this.setRequire(page);
        return page;
    }

    /**
     * MeetingTaskEntity 返回值中组装活动要求
     */
    private void setRequire(List<MeetingTaskEntity> tasks) {
        if (tasks != null && !tasks.isEmpty()) {
            tasks.forEach(
                    meetingTaskEntity -> {
                        MeetingRequireEntity meetingRequireEntity = meetingTaskEntity.getMeetingRequireEntity();
                        meetingTaskEntity.setMeetingReqNum(meetingRequireEntity.getMeetingNum());
                        meetingTaskEntity.setIsSignIn(meetingRequireEntity.getIsSignIn());
                        meetingTaskEntity.setIsWResolution(meetingRequireEntity.getIsWResolution());
                        meetingTaskEntity.setDeduct(meetingRequireEntity.getDeduct());
                        meetingTaskEntity.setMeetingRequireEntity(null);
                    });
        }
    }

    /**
     * 当前组织在有效时间内的任务
     */
    List<MeetingTaskEntity> findByOrgIdAndIds(Long orgId, List<Long> ids, Date startTime) {
        MeetingTypeTaskListForm meetingTypeListForm = new MeetingTypeTaskListForm();
        meetingTypeListForm.setTag(MeetingTypeTaskListForm.TAG_MEETING);
        meetingTypeListForm.setOrgId(orgId);
        meetingTypeListForm.setIds(ids);
        meetingTypeListForm.setStartTime(startTime);
        log.debug("查询烟草同步市值的参数=>{}", JsonUtils.toJson(meetingTypeListForm));
        List<MeetingTaskEntity> list = mapper.findByForm(meetingTypeListForm);
        log.debug("查询结果mt"+list);
        list.forEach(
                meetingTaskEntity -> {
                    MeetingRequireEntity meetingRequireEntity = meetingTaskEntity.getMeetingRequireEntity();
                    if (meetingRequireEntity != null) {
                        meetingTaskEntity.setIsSignIn(meetingRequireEntity.getIsSignIn());
                        meetingTaskEntity.setIsWResolution(meetingRequireEntity.getIsWResolution());
                        meetingTaskEntity.setDeduct(meetingRequireEntity.getDeduct());
                        meetingTaskEntity.setMeetingReqNum(meetingRequireEntity.getMeetingNum());
                    }
                });
        return list;
    }



    /**
     * 定时派发任务设置开始时间和结束时间
     */
    private void setStartAndEndTime(MeetingPlanEntity meetingPlanEntity, Date now) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        short executeType = meetingPlanEntity.getExecuteType();
        if (executeType == 2) { // 自然月
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            meetingPlanEntity.setStartTime(calendar.getTime());
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            meetingPlanEntity.setEndTime(calendar.getTime());
        } else if (executeType == 3) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            meetingPlanEntity.setStartTime(calendar.getTime());
            calendar.add(Calendar.MONTH, 2);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            meetingPlanEntity.setEndTime(calendar.getTime());
        } else if (executeType == 4) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            meetingPlanEntity.setStartTime(calendar.getTime());
            calendar.set(Calendar.MONTH, 12);
            calendar.set(Calendar.DAY_OF_MONTH, 31);
            meetingPlanEntity.setEndTime(calendar.getTime());
        }
    }

    /**
     * 获取未完成的任务列表
     *
     * @return
     */
    public List<MeetingTaskEntity> findUnfinishedTask() {
        return mapper.findUnfinishedTask();
    }


    /**
     * 未完成三会一课活动任务的组织统计
     *
     * @param sysHeader HeaderHelper.SysHeader
     * @return UndoneMeeting 统计结果
     */
    public UndoneMeeting undoneMeetingTaskStatistics(HeaderHelper.SysHeader sysHeader) throws Exception {
        String key = meetingTaskRedis.keySHYK(sysHeader.getOid(), DateUtils.getMonth());
        UndoneMeeting undoneMeeting = meetingTaskRedis.get(key, UndoneMeeting.class);
        if (undoneMeeting == null) {
            List<TypeForm> typeFormList = getTypeList(sysHeader.getRegionId());
            undoneMeeting = this.undoneMeetingTaskStatistics(sysHeader, sysHeader.getOid(), typeFormList);
            meetingTaskRedis.setSHYK(key, undoneMeeting);
        }
        return undoneMeeting;
    }

    /**
     * 根据配置动态获取会议类型
     *
     * @return
     */
    public List<TypeForm> getTypeList(long regionId) {
        List<TypeForm> typeForms = new ArrayList<>();
        for (MeetingTaskCount.Count count : meetingTaskCount.sort()) {
            try {
                TypeEntity typeEntity = typeMapper.findByName(regionId, count.getType());
                typeForms.add(new TypeForm(typeEntity, count.getCountType()));
            } catch (NullPointerException ex) {
                log.error("会议类型未找到,type={}", count.getType());
            }
        }
        return typeForms;
    }

    /**
     * 未完成三会一课会议任务的组织统计
     */
    public UndoneMeeting undoneMeetingTaskStatistics(HeaderHelper.SysHeader sysHeader, long orgId, List<TypeForm> typeFormList)
            throws Exception {
        // 获取当前组织及子组织
        List<Org> orgList = getOrg(orgId);
        // 判断当前组织类型
        UndoneMeeting undoneMeeting = getOrgType(sysHeader, orgId, orgList);
        // 组织类型
        Integer orgType = undoneMeeting.getOrgType();
        // 当前月份
        String currentYearMonth = DateUtils.currentYearMonth();
        // 当前季度
        String currentYearQuarter = DateUtils.currentYearQuarter();
        // root和非root统计结果
        List<OrgStatus> orgStatusList = new ArrayList<>();
        // leaf统计结果
        List<LeafOrgStatus> leafOrgStatusList = new ArrayList<>();
        for (TypeForm typeForm : typeFormList) {
            List<OrgStatus.UndoneOrg> undoneOrgList = null;
            Integer countType = typeForm.getCountType();
            Long typeId = typeForm.getTypeId();
            String type = typeForm.getType();
            String time = null;
            int size;
            int status;
            if (typeForm.getCountType() == 0) {
                undoneOrgList = mapper.meetingStatistics(orgList, typeId, orgType, countType, null, null);
                time = currentYearMonth;
            } else if (typeForm.getCountType() == 1) {
                String startTime = DateUtils.currentQuarterStartTime();
                String endTime = DateUtils.currentQuarterEndTime();
                undoneOrgList =
                        mapper.meetingStatistics(orgList, typeId, orgType, countType, startTime, endTime);
                time = currentYearQuarter;
            }
            size = undoneOrgList == null ? 0 : undoneOrgList.size();
            status = size == 0 ? 2 : 1;
            // root和非支部
            if (orgType.equals(OrgTypeConstant.ROOT)
                    || orgType.equals(OrgTypeConstant.NOT_ROOT_OR_LEAF)) {
                OrgStatus orgStatus;
                orgStatus = new OrgStatus(typeId, type, time, size);
                if (orgType.equals(OrgTypeConstant.NOT_ROOT_OR_LEAF)) {
                    if (undoneOrgList != null && !undoneOrgList.isEmpty()) {
                        orgStatus.setUndoneOrgList(undoneOrgList);
                    }
                }
                orgStatusList.add(orgStatus);
            }
            // 支部
            else {
                LeafOrgStatus leafOrgStatus = new LeafOrgStatus(typeId, type, time, status);
                leafOrgStatusList.add(leafOrgStatus);
            }
        }
        // root组织
        if (orgType.equals(OrgTypeConstant.ROOT) || orgType.equals(OrgTypeConstant.NOT_ROOT_OR_LEAF)) {
            if (orgStatusList != null && !orgStatusList.isEmpty()) {
                undoneMeeting.setOrgStatus(orgStatusList);
            }
        } else {
            undoneMeeting.setLeafOrgStatus(leafOrgStatusList);
        }
        return undoneMeeting;
    }

    /**
     * 获取当前组织以及子组织
     *
     * @param orgId
     * @return
     * @throws Exception
     */
    public List<Org> getOrg(long orgId) throws Exception {
        String url = String.format(FIND_ALL_CHILD_URL, userCenter, orgId);
        HttpHeaders httpHeaders = new HttpHeaders();
        return RemoteApiHelper.get(
                restTemplate, url, httpHeaders, new TypeReference<Result<List<Org>>>() {
                });
    }

    /**
     * 获取组织类型 1 root组织,2 非root且有下级组织的组织,3 支部
     *
     * @param orgId 当前组织id
     * @return
     */
    public UndoneMeeting getOrgType(HeaderHelper.SysHeader sysHeader, long orgId, List<Org> orgList) {
        UndoneMeeting undoneMeeting = new UndoneMeeting();
        undoneMeeting.setOrgId(orgId);
        if (orgList == null || orgList.isEmpty()) {
            throw new ApiException("未找到当前组织", new Result<>(errors, 1950, HttpStatus.OK.value()));
        }
        Optional<Org> self = orgList.stream().filter(tmp -> tmp.getOrgId().equals(orgId)).findFirst();

        Org org = self.get();
        undoneMeeting.setOrgName(org.getOrgName());
        Long rootOrgId = regionService.bindingOrgId(sysHeader.getRegionId());
        if (rootOrgId != null && rootOrgId.equals(orgId)) {
            undoneMeeting.setOrgType(OrgTypeConstant.ROOT);
        } else if (orgList.size() == 1) {
            undoneMeeting.setOrgType(OrgTypeConstant.LEAF);
        } else {
            undoneMeeting.setOrgType(OrgTypeConstant.NOT_ROOT_OR_LEAF);
        }
        return undoneMeeting;
    }

    /**
     * 更新任务完成状态 2018-12-21 14:09:24 chenanshun
     *
     * @param meetingId 活动id
     * @param stat      活动状态
     */
    public void upTaskStatus(long meetingId, Short stat, boolean szfFlag) {
        if (stat == MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue()
                || stat == MeetingCanstant.MEETING_STATUS_CANCEL.shortValue()) { // 提交 或 取消
            // 如果是已提交 完成次数+1
            this.updateMeetingTaskNum(meetingId, stat, szfFlag);
            // 更改t_topic_org完成状态
            topicOrgService.updateAllTopicOrgStatus(meetingId, stat);
        }
    }

    /**
     * 根据类型编号、年份和组织编号获取活动开展情况
     *
     * @param typeId 活动类型编号
     * @param orgIdList   执行组织编号集合
     * @param queryYear   查询年份
     */
    public List<TaskQueryVo> findByTypeIdOrgIdQueryDate(Integer typeId, List<Long> orgIdList, String queryYear) {
        List<TaskQueryVo> re = new ArrayList<>();
        try {
            re = mapper.findByTypeIdOrgIdQueryDate(typeId,orgIdList, queryYear);
        }catch (Exception e){
            log.error("根据类型编号、年份和组织编号获取活动开展情况 报错！",e);
        }
        return re;
    }


    /**
     * 更新活动的举办次数和完成情况 2018-12-21 14:22:50 chenanshun
     *
     * @param szfFlag  市值传过来的是否是党小组会的标志 就不走烟草逻辑
     * @param meetingId
     */
    private void updateMeetingTaskNum(long meetingId, Short stat, boolean szfFlag) {
        // 查询出活动绑定的活动类型
        List<MeetingTypeEntity> typeList = this.meetingTypeMapper.findByMeetingId(meetingId);
        if (!ListUtils.isEmpty(typeList) && !szfFlag) {
            typeList.forEach(
                    type -> {
                        // 查找对应t_meeting_paln的要求数量
                        MeetingTaskEntity task = this.mapper.selectByPrimaryKey(type.getMeetingTaskId());
                        MeetingRequireEntity require =
                                this.meetingRequireMapper.selectByPrimaryKey(task.getMeetingRequireId());
                        // 提交 对应数量任务完成数量+1
                        int addNum = 0;
                        int status = 1; // 状态（：1未完成 2：已完成）
                        if (stat.equals(MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue())) {
                            addNum = 1;
                            // 判断是否已完成
                            if ((task.getMeetingNum() + 1) >= require.getMeetingNum()) {
                                status = 2;
                            }
                        } else if (stat.equals(
                                MeetingCanstant.MEETING_STATUS_CANCEL.shortValue())) { // 提交 对应数量任务完成数量-1
                            addNum = -1;
                            // 判断是否已完成
                            if ((task.getMeetingNum() - 1) >= require.getMeetingNum()) {
                                status = 2;
                            }
                        }
                        // 更新t_meeting_task对应的数量
                        this.mapper.updateMeetingNumByTaskId(type.getMeetingTaskId(), addNum);
                        this.mapper.updateMeetingComplate(type.getMeetingTaskId(), status);
                    });
        }
    }
}
