package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.common.RuleTypeEnum;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingRequireMapper;
import com.goodsogood.ows.mapper.MeetingTaskMapper;
import com.goodsogood.ows.mapper.TypeGroupMapper;
import com.goodsogood.ows.mapper.TypeMapper;
import com.goodsogood.ows.model.db.MeetingRequireEntity;
import com.goodsogood.ows.model.db.TypeEntity;
import com.goodsogood.ows.model.db.TypeGroupEntity;
import com.goodsogood.ows.model.vo.TypeListForm;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:21
 **/
@Service
@Log4j2
public class TypeService {

    private final TypeMapper mapper;
    private final TypeGroupMapper typeGroupMapper;
    private final MeetingRequireMapper meetingRequireMapper;
    private final MeetingTaskMapper meetingTaskMapper;

    @Autowired
    public TypeService(TypeMapper mapper, TypeGroupMapper typeGroupMapper, MeetingRequireMapper meetingRequireMapper, MeetingTaskMapper meetingTaskMapper) {
        this.mapper = mapper;
        this.typeGroupMapper = typeGroupMapper;
        this.meetingRequireMapper = meetingRequireMapper;
        this.meetingTaskMapper = meetingTaskMapper;
    }

    /**
     * 添加类型
     *
     * @param sysHeader  用户信息
     * @param typeEntity 类型信息 type不能为null 添加前id设置为null
     * @return 新增的类型id
     */
    public Long addType(HttpHeaders headers, HeaderHelper.SysHeader sysHeader, TypeEntity typeEntity) {
        typeEntity.setTypeId(null);//添加前id设置为null
        typeEntity.setOrgId(sysHeader.getOid());
        typeEntity.setCreateUser(sysHeader.getUserId());
        typeEntity.setCreateTime(DateTime.now().toDate());
        typeEntity.setOrgName(sysHeader.getOrgName());
        typeEntity.setCode(RuleTypeEnum.NO_RULE.getType());
        typeEntity.setHasLecturer(Constant.NO);
        typeEntity.setHasLectureTitle(Constant.NO);
        mapper.insert(typeEntity);
        return typeEntity.getTypeId();
    }

    /**
     * 删除类型
     */
    public int delType(long id) {
        return mapper.deleteByPrimaryKey(id);
    }


    /**
     * 修改类型
     */
    public int updateType(HeaderHelper.SysHeader sysHeader, TypeEntity typeEntity) {
        typeEntity.setLastChangeUser(sysHeader.getUserId());
        typeEntity.setUpdateTime(DateTime.now().toDate());
        return mapper.updateByPrimaryKeySelective(typeEntity);
    }


    /**
     * 查询活动类型
     */
    public TypeEntity listOneByName(long regionId, String type) {
        return mapper.findByName(regionId, type);
    }

    /**
     * 查询活动类型
     */
    public List<TypeEntity> listAllByIds(List<Long> ids) {
        Example example = new Example(TypeEntity.class);
        example.createCriteria().andIn("typeId", ids);
        return mapper.selectByExample(example);
    }

    /**
     * 查询活动类型
     */
    public Page<TypeEntity> listPageType(TypeListForm typeListFrom) {
        return PageHelper.startPage(typeListFrom.getPageBean().getPageNo(), typeListFrom.getPageBean().getPageSize())
                .doSelectPage(() -> this.mapper.listAll(typeListFrom));
    }

    /**
     * 查询活动类型。按类别分类
     */
    public List<TypeEntity> listAllType(TypeListForm typeListFrom) {
        return mapper.listAll(typeListFrom);
    }

    /**
     * 校验当前类型是否存在
     */
    public boolean isExist(long regionId, String type) {
        return this.mapper.findByName(regionId, type) != null;
    }

    /**
     * 查询类型列表
     * @param tag 发起活动下拉框；2:任务完成情况下拉框
     */
    public List<TypeEntity> listAllType(long oid, short tag) {
        return meetingTaskMapper.findTypeIdByOrgId(oid,tag);
    }

    /**
     * 校验当前类别是否有类型
     * 组合中使用
     * 组织生活中使用
     */
    public boolean isUse(long id) {
        //组合中使用
        Example example1 = new Example(TypeGroupEntity.class);
        example1.createCriteria().andEqualTo("typeId", id);

        //组织生活中使用
        Example example2 = new Example(MeetingRequireEntity.class);
        example2.createCriteria().andEqualTo("typeId", id);

        return typeGroupMapper.selectByExample(example1).size() > 0 || meetingRequireMapper.selectByExample(example2).size() > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateUserRule(Long typeId, Integer code, Integer hasLecturer, Integer hasLectureTitle) {
        mapper.updateUserRule(typeId, code, hasLecturer, hasLectureTitle);
    }
}