package com.goodsogood.ows.service;

import com.goodsogood.ows.common.RedisConstant;
import com.goodsogood.ows.mapper.MeetingHistoryMapper;
import com.goodsogood.ows.mapper.MeetingMapper;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingHistoryEntity;
import com.goodsogood.ows.model.vo.MeetingIndexForm;
import com.goodsogood.ows.model.vo.MeetingListForm;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-25 10:53
 **/
@Service
@Log4j2
public class MeetingRedisService {

    private final MeetingMapper meetingMapper;
    private final MeetingHistoryMapper meetingHistoryMapper;
    private final StringRedisTemplate stringRedisTemplate;


    @Autowired
    public MeetingRedisService(
            MeetingMapper meetingMapper,
            MeetingHistoryMapper meetingHistoryMapper,
            StringRedisTemplate stringRedisTemplate) {
        this.meetingMapper = meetingMapper;
        this.meetingHistoryMapper = meetingHistoryMapper;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    int meetingH5CountByRedis(Long oid) {
        // uid 为空 返回0
        if (oid == null) {
            return 0;
        }
        String redisKey = meetingH5CountByRedisKey(oid);
        Object redisCount = stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).get(redisKey);
        // 缓存中有值，返回缓存值
        if (redisCount != null) {
            return Integer.valueOf((String) redisCount);
        }
        return meetingH5CountAndRedisByDb(oid);
    }

    private int meetingH5CountAndRedisByDb(Long oid) {
        MeetingIndexForm meetingIndexForm = meetingH5Count(oid);
        Integer count = meetingIndexForm.getApprovalCount() + meetingIndexForm.getBackCount();
        // 存入缓存，有效时间1小时
        String redisKey = meetingH5CountByRedisKey(oid);
        stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).put(redisKey, count.toString());
        return count;
    }

    /**
     * redis key
     *
     * @param oid 组织id
     * @return key
     */
    String meetingH5CountByRedisKey(Long oid) {
        return RedisConstant.MEETING_H5_COUNT + oid;
    }

    public MeetingIndexForm meetingH5Count(Long oid) {
        MeetingIndexForm meetingIndexForm = new MeetingIndexForm();
        MeetingListForm meetingListForm = new MeetingListForm();
        meetingListForm.setIsH5((short) 1);
        meetingListForm.setQOrgIds(Collections.singletonList(oid));

        List<MeetingEntity> meetingEntities = listAll(meetingListForm);
        AtomicInteger soonStartCount = new AtomicInteger();
        AtomicInteger approvalCount = new AtomicInteger();
        AtomicInteger submitCount = new AtomicInteger();
        AtomicInteger backCount = new AtomicInteger();

        long now = DateTime.now().toDate().getTime();
        meetingEntities.forEach(meetingEntity -> {
            short status = meetingEntity.getStatus();
            long startTime = meetingEntity.getStartTime().getTime();
            if ((status == 3 && startTime > now) || status == 1 || status == 2 || status == 9) {// 待举办 活动待举办 “活动待举办”、“发起审批中”、“发起未通过”、“活动已取消”的活动
                soonStartCount.addAndGet(1);
            } else if (status == 3 || status == 5 || status == 6 || status == 10 || status == 11) {// 待填写 “活动待填报”、“填报审批中”、“填报未通过”的活动
                approvalCount.addAndGet(1);
            } else if (status == 7 || status == 12 || status == 13 || status == 14) {//已提交 “已提交”、“检查通过”、“待复核”的活动
                submitCount.addAndGet(1);
            } else if (status == 8) {// 已退回
                backCount.addAndGet(1);
            }
        });
        meetingIndexForm.setSoonStartCount(soonStartCount.get());
        meetingIndexForm.setApprovalCount(approvalCount.get());
        meetingIndexForm.setSubmitCount(submitCount.get());
        meetingIndexForm.setBackCount(backCount.get());
        return meetingIndexForm;
    }

    /**
     * 查询活动列表 分页查询
     */
    public List<MeetingEntity> listAll(MeetingListForm meetingListForm) {
        List<MeetingEntity> all = this.meetingMapper.findAll(meetingListForm);
        //查询活动状态更新时间
        this.setStatusUpdateTime(all);
        return all;
    }

    /**
     * 设置活动状态更新时间
     */
    void setStatusUpdateTime(MeetingEntity meetingEntity) {
        if (meetingEntity == null) {
            return;
        }
        this.setStatusUpdateTime(Collections.singletonList(meetingEntity));
    }

    /**
     * 设置活动状态更新时间
     */
    void setStatusUpdateTime(List<MeetingEntity> meetingEntities) {
        if (meetingEntities == null || meetingEntities.isEmpty()) {
            return;
        }
        //查询活动状态更新时间
        Example example = new Example(MeetingHistoryEntity.class);
        example.createCriteria().andIn("meetingId", meetingEntities.stream().map(MeetingEntity::getMeetingId).collect(Collectors.toList()));
        List<MeetingHistoryEntity> meetingHistoryEntities = meetingHistoryMapper.selectByExample(example);
        meetingEntities.forEach(meetingEntity ->
                meetingHistoryEntities.forEach(meetingHistoryEntity -> {
                    if (meetingHistoryEntity.getMeetingId().equals(meetingEntity.getMeetingId())) {
                        if (isNot3(meetingHistoryEntity, meetingEntity) || is3(meetingHistoryEntity, meetingEntity) || is4(meetingHistoryEntity, meetingEntity)) {
                            meetingEntity.setStatusUpdateTime(meetingHistoryEntity.getCreateTime());
                            meetingEntity.setReason(meetingHistoryEntity.getReason());
                        }
                    }
                }));
    }

    private boolean isNot3(MeetingHistoryEntity meetingHistoryEntity, MeetingEntity meetingEntity) {
        return meetingEntity.getStatus() != 3 && meetingHistoryEntity.getStatus().equals(meetingEntity.getStatus().intValue());
    }

    private boolean is3(MeetingHistoryEntity meetingHistoryEntity, MeetingEntity meetingEntity) {
        return meetingEntity.getStatus() == 3 && meetingEntity.getStartTime().getTime() > DateTime.now().toDate().getTime() && meetingHistoryEntity.getStatus() == 3;
    }

    private boolean is4(MeetingHistoryEntity meetingHistoryEntity, MeetingEntity meetingEntity) {
        return meetingEntity.getStatus() == 3 && meetingEntity.getStartTime().getTime() <= DateTime.now().toDate().getTime() && meetingHistoryEntity.getStatus() == 4;
    }
}