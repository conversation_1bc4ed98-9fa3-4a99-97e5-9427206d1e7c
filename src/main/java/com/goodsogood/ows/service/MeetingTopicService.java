package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.MeetingTopicMapper;
import com.goodsogood.ows.model.db.MeetingTopicEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-23 11:38
 **/
@Service
@Log4j2
public class MeetingTopicService {

    private final MeetingTopicMapper meetingTopicMapper;

    @Autowired
    public MeetingTopicService(MeetingTopicMapper meetingTopicMapper) {
        this.meetingTopicMapper = meetingTopicMapper;
    }

    /**
     * 根据任务id查看被哪些活动使用
     * @param topicId
     * @return
     */
    public List<MeetingTopicEntity> getMeetingTopicList(Long topicId) {
        if(topicId == null) {
            return new ArrayList<>();
        }
        Example example = new Example(MeetingTopicEntity.class);
        example.createCriteria().andEqualTo("topicId", topicId);
        return this.meetingTopicMapper.selectByExample(example);
    }

}