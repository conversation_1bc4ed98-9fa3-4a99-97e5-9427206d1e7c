package com.goodsogood.ows.service;

import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.TobaccoTaskFlowMapper;
import com.goodsogood.ows.mapper.TobaccoTaskOrgMapper;
import com.goodsogood.ows.model.db.TobaccoTaskFlowEntity;
import com.goodsogood.ows.model.db.TobaccoTaskHandleEntity;
import com.goodsogood.ows.model.db.TobaccoTaskOrgEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;

/**
 * 烟草异步任务
 * <AUTHOR>
 * @date 2021.08.24
 */
@Service
@Log4j2
public class TobaccoAsyncService {

    private final TobaccoTaskOrgMapper tobaccoTaskOrgMapper;
    private final TobaccoTaskFlowMapper tobaccoTaskFlowMapper;

    @Autowired
    public TobaccoAsyncService(TobaccoTaskOrgMapper tobaccoTaskOrgMapper, TobaccoTaskFlowMapper tobaccoTaskFlowMapper) {
        this.tobaccoTaskOrgMapper = tobaccoTaskOrgMapper;
        this.tobaccoTaskFlowMapper = tobaccoTaskFlowMapper;
    }

    /**
     * 同步任务执行状态
     * @param handle
     * @param acceptScope
     */
    @Async("tobaccoAsync")
    public void updateTaskOrgHandle(TobaccoTaskHandleEntity handle, Integer acceptScope){
        TobaccoTaskOrgEntity entity = new TobaccoTaskOrgEntity();
        entity.setUpdateTime(handle.getUpdateTime());
        entity.setHandleStatus(handle.getHandleStatus());
        Example example = new Example(TobaccoTaskOrgEntity.class);
        example.createCriteria()
                .andEqualTo("regionId",handle.getRegionId())
                .andEqualTo("taskId",handle.getTaskId())
                .andEqualTo("acceptScope",acceptScope)
                .andEqualTo("acceptId",handle.getAcceptId());
        tobaccoTaskOrgMapper.updateByExampleSelective(entity,example);
    }

    /**
     * 任务执行流水
     * @param handle
     */
    @Async("tobaccoAsync")
    public void taskFlow(TobaccoTaskHandleEntity handle){
        TobaccoTaskFlowEntity entity = new TobaccoTaskFlowEntity();
        entity.setTaskId(handle.getTaskId());
        entity.setAcceptId(handle.getAcceptId());
        entity.setRegionId(handle.getRegionId());
        entity.setHandleStatus(handle.getHandleStatus());
        if (Constant.HANDLE_VERIFY.equals(handle.getFlag())){
            entity.setContent(handle.getHandleContent());
        }
        entity.setHandleUser(handle.getHandleUser());
        entity.setHandleOrg(handle.getHandleOrg());
        entity.setCreateTime(handle.getUpdateTime());
        tobaccoTaskFlowMapper.insertUseGeneratedKeys(entity);
    }

    /**
     * 转派任务处理
     * @param handle
     */
    public void taskForward(TobaccoTaskHandleEntity handle){

    }

}
