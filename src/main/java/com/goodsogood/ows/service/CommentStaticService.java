package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingCommentMapper;
import com.goodsogood.ows.mapper.MeetingCommentMemberComplexMapper;
import com.goodsogood.ows.mapper.MeetingCommentMemberMapper;
import com.goodsogood.ows.mapper.MeetingCommentStatisticsMapper;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.CommentStaticsVo;
import com.goodsogood.ows.model.vo.MeetingOrgDebriefReviewStatisticsQueryVO;
import com.goodsogood.ows.model.vo.StaticsForm;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.utils.ExcelUtils;
import com.goodsogood.ows.utils.ExportExcel;
import lombok.extern.log4j.Log4j2;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 民主评议统计处理
 */
@Log4j2
@Service
public class CommentStaticService {
    @Autowired
    MeetingCommentMapper meetingCommentMapper;
    @Autowired
    MeetingCommentMemberMapper meetingCommentMemberMapper;
    @Autowired
    MeetingCommentMemberComplexMapper meetingCommentMemberComplexMapper;
    @Autowired
    MeetingCommentStatisticsMapper meetingCommentStatisticsMapper;
    @Autowired
    ThirdService thirdService;


    /**
     * 查询统计表
     */
    public Page<StaticsForm> queryStatistics(Integer year, Long orgId, Integer page, Integer pageSize){
        return PageHelper.startPage(page, pageSize).doSelectPage(()->meetingCommentStatisticsMapper.queryStatics(year,orgId));
    }

    /**
     * 下载统计报表（根据查询条件进行下载）
     */
    public Boolean downStaticsReport(Integer year, Long orgId,List<Integer> values,HttpServletResponse response) {
        List<StaticsForm> formList = meetingCommentStatisticsMapper.queryStatics(year,orgId);
        String[] field = CommentStaticsVo.getNames(values);
        log.debug("生成excel");
        final int[] num = {1};
        try {
            ((ExportExcel<StaticsForm>) (obj, row) -> {

                int i = 0;
                int j = 1;
                while(i<values.size()){
                    Class<?> clazz = obj.getClass();
                    Method m = null;
                    if(CommentStaticsVo.getMethodNames(j)==null){
                        row.createCell(i,CellType.NUMERIC).setCellValue(num[0]);
                        i++;
                        num[0] = num[0] +1;
                    }else{
                        try {
                            m = clazz.getDeclaredMethod(CommentStaticsVo.getMethodNames(j));
                        } catch (NoSuchMethodException e) {
                            e.printStackTrace();
                        }
                        CellType cellType = CommentStaticsVo.getCellType(j);
                        if(cellType==CellType.STRING){
                            try {
                                assert m != null;
                                row.createCell(i,cellType).setCellValue((String)m.invoke(obj));
                            } catch (IllegalAccessException | InvocationTargetException e) {
                                e.printStackTrace();
                            }
                        }else{
                            try {
                                assert m != null;
                                row.createCell(i,cellType).setCellValue((Integer)m.invoke(obj));
                            } catch (IllegalAccessException | InvocationTargetException e) {
                                e.printStackTrace();
                            }
                        }
                        i++;
                    }
                    j++;
                }
            }).exportExcel(response, formList,
                    field, "民主评议统计报表.xls");
        } catch (IOException | NoSuchFieldException e) {
            log.error("导出民主评议统计表出错", e);
            return false;
        }
        return true;
    }
    /**
     * 保存到t_meeting_comment_statistics
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    public void saveToStatistics(List<Long> commentIds, HttpHeaders headers){
        final HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<MeetingCommentStatisticsEntity> statisticslist = new ArrayList<>();
        for(Long commentId:commentIds){
            MeetingCommentEntity commentEntity = meetingCommentMapper.selectByPrimaryKey(commentId);
            //判断是否已存在，如存在则先删除再新增
            if(!CollectionUtils.isEmpty(queryStatics(commentEntity.getOrgId(),commentEntity.getYear()))){
                delByOidYear(commentEntity.getOrgId(),commentEntity.getYear());
            }
            List<Long> memCommentIds = memCommentRealte(commentId);
            if(CollectionUtils.isEmpty(memCommentIds)){
                continue;
            }
            final List<UserInfoBase> userList = thirdService.getUserListByOrgId(commentEntity.getOrgId(), 1, headers);
            Integer partyNumber = userList.size();
            List<MeetingCommentMemberComplexEntity> complexEntity = memCommentRealte(memCommentIds);
            Integer joinNumber = CollectionUtils.isEmpty(memCommentIds)? 0: complexEntity.size();
            Arrays.stream(CommentRatingEnum.values()).forEach(i->{
                MeetingCommentStatisticsEntity statisticsEntity = new MeetingCommentStatisticsEntity();
                statisticsEntity.setCommentId(commentEntity.getCommentId());
                statisticsEntity.setCreateTime(LocalDateTime.now());
                statisticsEntity.setCreateUser(header.getUserId());//创建人为最终审批的人
                statisticsEntity.setLastChangeUser(header.getUserId());
                statisticsEntity.setUpdateTime(LocalDateTime.now());
                statisticsEntity.setYear(commentEntity.getYear());
                statisticsEntity.setOrgId(commentEntity.getOrgId());
                statisticsEntity.setOrgLevel(commentEntity.getOrgLevel());
                statisticsEntity.setOrgName(commentEntity.getOrgName());
                statisticsEntity.setRegionId(commentEntity.getRegionId());
                statisticsEntity.setPartyNumber(partyNumber);
                statisticsEntity.setJoinNumber(joinNumber);
                statisticsEntity.setRating(i.getKey());
                Long ratingNum = 0L;
//                if(i==CommentRatingEnum.UNKNOWN){//如为不确定等次
//                    ratingNum = CollectionUtils.isEmpty(complexEntity)?memCommentIds.size():(complexEntity.stream().filter(j-> Objects.isNull(j.getComplexRating())).count());
//                }else{
                    ratingNum = CollectionUtils.isEmpty(complexEntity)?0:(complexEntity.stream().filter(j->!Objects.isNull(j.getComplexRating()) && j.getComplexRating()==i.getKey()).count());
//                }
                statisticsEntity.setRatingNumber(ratingNum.intValue());
                statisticslist.add(statisticsEntity);
            });
        }
        if(!CollectionUtils.isEmpty(statisticslist)){
           log.debug("插入统计数据到t_meeting_comment_statistics");
            meetingCommentStatisticsMapper.insertList(statisticslist);
        }
    }

    /**
     * 批量删除
     * @param commentIds
     *
     */
    public void delStatistics(List<Long> commentIds){
        List<MeetingCommentStatisticsEntity> list = new ArrayList<>();
        for(Long commentId :commentIds ){
            MeetingCommentEntity commentEntity = meetingCommentMapper.selectByPrimaryKey(commentId);
            delByOidYear(commentEntity.getOrgId(),commentEntity.getYear());
        }

    }

    /**
     * 单个删除
     * @param commentId
     *
     */
    public void delStatistic(Long commentId){
            List<MeetingCommentStatisticsEntity> list = new ArrayList<>();
            MeetingCommentEntity commentEntity = meetingCommentMapper.selectByPrimaryKey(commentId);
            delByOidYear(commentEntity.getOrgId(),commentEntity.getYear());
    }

    private void delByOidYear(Long orgId,Integer year) {
        Example example = new Example(MeetingCommentStatisticsEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId",orgId).andEqualTo("year",year);
        log.debug("删除统计表数据");
        meetingCommentStatisticsMapper.deleteByExample(example);
    }

    //根据机构、年查询t_meeting_comment_statistics
    public List<MeetingCommentStatisticsEntity> queryStatics(Long orgId,Integer year){
        Example example = new Example(MeetingCommentStatisticsEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId",orgId).andEqualTo("year",year);
        return meetingCommentStatisticsMapper.selectByExample(example);
    }
    //commentId关联的自评id
    private List<Long> memCommentRealte(Long commentId){
        Example example = new Example(MeetingCommentMemberEntity.class);
        Example.Criteria criteria = example.createCriteria();
        example.selectProperties("commentMemberId");
        criteria.andEqualTo("commentId",commentId);
        List<MeetingCommentMemberEntity> list = meetingCommentMemberMapper.selectByExample(example);
        return list.stream().map(MeetingCommentMemberEntity::getCommentMemberId).collect(Collectors.toList());
    }

    //list commentMemId关联的综合评议
    private List<MeetingCommentMemberComplexEntity> memCommentRealte(List<Long> commentMemIds){
        Example example = new Example(MeetingCommentMemberComplexEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("commentMemberId",commentMemIds);
        return meetingCommentMemberComplexMapper.selectByExample(example);
    }
}
