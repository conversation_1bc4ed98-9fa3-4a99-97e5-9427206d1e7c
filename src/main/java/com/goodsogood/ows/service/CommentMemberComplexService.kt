package com.goodsogood.ows.service

import com.goodsogood.ows.common.Constant
import com.goodsogood.ows.common.FileSourceEnum
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.mapper.MeetingCommentMemberComplexHistoryMapper
import com.goodsogood.ows.mapper.MeetingCommentMemberComplexMapper
import com.goodsogood.ows.mapper.MeetingCommentMemberMapper
import com.goodsogood.ows.model.db.CommentRatingEnum
import com.goodsogood.ows.model.db.MeetingCommentMemberComplexEntity
import com.goodsogood.ows.model.db.MeetingCommentMemberComplexHistoryEntity
import com.goodsogood.ows.model.vo.*
import com.goodsogood.ows.utils.JsonUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example
import java.time.LocalDateTime
import java.util.*

@Service
class CommentMemberComplexService(@Autowired val errors: Errors,
                                  @Autowired val thirdService: ThirdService,
                                  @Autowired val meetingFileService: MeetingFileService,
                                  @Autowired val commentService: CommentService,
                                  @Autowired val openService: OpenService,
                                  @Autowired val meetingCommentMemberMapper: MeetingCommentMemberMapper,
                                  @Autowired val meetingCommentMemberComplexMapper: MeetingCommentMemberComplexMapper,
                                  @Autowired val meetingCommentMemberComplexHistoryMapper: MeetingCommentMemberComplexHistoryMapper) {

    private val log = LoggerFactory.getLogger(CommentMemberComplexService::class.java)

    /**
     * 新增民主评议综合评定
     * @param form
     * @param headers
     */
    fun insertMemberComplex(form : CommentMemberComplexAddForm, headers: HttpHeaders): String {
        val header = HeaderHelper.buildMyHeader(headers)
        val now = LocalDateTime.now()
        log.debug("新增民主评议综合评定 -> form:[${form}] header:[${header}]")
        val memberEntity = meetingCommentMemberMapper.selectByPrimaryKey(form.commentMemberId)
        if(memberEntity.politicalType != 1) {
            throw ApiException("预备党员不参加民主评议",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "预备党员不参加民主评议")
            )
        }
        val comment = commentService.getMeetingComment(memberEntity.commentId)
        commentService.isEdit(memberEntity.commentId, mutableListOf(0,2,4))
        val content = JsonUtils.toJson(form.suggestion)
        val complexEntity = selectMemberComplex(form.commentMemberId)
        if (complexEntity.commentMemberComplexId == null) {
            complexEntity.commentMemberId = form.commentMemberId
            complexEntity.complexRating = form.rating
            complexEntity.complexSuggestion = content
            complexEntity.createTime = now
            complexEntity.createUser = header.userId
            meetingCommentMemberComplexMapper.insert(complexEntity)
        } else {
            complexEntity.complexRating = form.rating
            complexEntity.complexSuggestion = content
            complexEntity.updateTime = now
            complexEntity.lastChangeUser = header.userId
            meetingCommentMemberComplexMapper.updateByPrimaryKey(complexEntity)
            // 删除附件
            meetingFileService.delete(complexEntity.commentMemberComplexId, FileSourceEnum.MEETING_COMMENT_ATTACH)
        }
        // 新增历史流水
        recordHistory(complexEntity, header.userId, now)
        // 新增附件
        meetingFileService.addFile(complexEntity.commentMemberComplexId, form.attachment, FileSourceEnum.MEETING_COMMENT_ATTACH)
        // 生成登记表
        commentService.createGradeForm(form.commentMemberId, memberEntity.commentId, headers)
        memberEntity.commentId?.let {
            // 生成评测表
            commentService.createCommentEvaluation(it, headers)
            // 统计
            commentService.createCommentStatics(it, headers)
        }
        // 我的荣耀
        if (comment?.status == 6) {
            val remark = "在${memberEntity.year}年党员民主评议中荣获优秀等次"
            if (complexEntity.complexRating == CommentRatingEnum.EXCELLENT.key) {
                openService.addMyGlory(
                    memberEntity.userId,
                    GloryType.COMMENT.key,
                    complexEntity.createTime,
                    "民主评议荣获优秀等次",
                    remark,
                    null,
                    headers
                )
            } else {
                openService.delMyGlory(
                    memberEntity.userId,
                    GloryType.COMMENT.key,
                    complexEntity.createTime,
                    "民主评议荣获优秀等次",
                    remark,
                    headers
                )
            }
        }
        return Constant.SUCCESS
    }

    /**
     * 批量新增民主评议综合评定
     * @param form
     * @param headers
     */
    fun batchInsertMemberComplex(form: CommentMemberComplexBatchAddForm, headers: HttpHeaders): String {
        log.debug("批量新增民主评议综合评定 -> form:[${form}] header:[${headers}]")
        form.commentMemberIds.forEach {
            val addForm = CommentMemberComplexAddForm(it, form.rating, form.suggestion, form.attachment)
            insertMemberComplex(addForm, headers)
        }
        return Constant.SUCCESS
    }

    fun getMemberComplex(commentMemberId: Long) : MemberComplexInfoForm {
        val form = MemberComplexInfoForm()
        val memberEntity = meetingCommentMemberMapper.selectByPrimaryKey(commentMemberId)
        if (memberEntity != null) {
            form.commentMemberId = memberEntity.commentMemberId
            form.userId = memberEntity.userId
            form.userName = memberEntity.userName
            form.phoneSecret = memberEntity.phoneSecret
            form.year = memberEntity.year
        }
        if(memberEntity.politicalType != 1) {
            throw ApiException("预备党员不参加民主评议",
                Result<Any>(errors, 2009, HttpStatus.OK.value(), "预备党员不参加民主评议")
            )
        }
        val memberComplex = selectMemberComplex(commentMemberId)
        form.commentMemberComplexId = memberComplex.commentMemberComplexId
        form.rating = memberComplex.complexRating
        val suggestion = memberComplex.complexSuggestion
        if (suggestion?.isNotBlank() == true) {
            val suggestionList = JsonUtils.fromJson(suggestion, List::class.java, String::class.java)
            form.suggestion = suggestionList.toMutableList()
        }
        // 获取文件
        val fileList = meetingFileService.selectByLinkedId(
            memberComplex.commentMemberComplexId,
            FileSourceEnum.MEETING_COMMENT_ATTACH
        )
        form.attachment = fileList
        return form
    }

    /**
     * 根据memberId查询综合评定结果
     * @param commentMemberId
     */
    fun selectMemberComplex(commentMemberId: Long) : MeetingCommentMemberComplexEntity {
        val example = Example(MeetingCommentMemberComplexEntity::class.java)
        example.createCriteria().andEqualTo("commentMemberId", commentMemberId)
        val entities = meetingCommentMemberComplexMapper.selectByExample(example)
        return if (entities.size  == 1) entities[0]
        else if (entities.size == 0) MeetingCommentMemberComplexEntity()
        else throw ApiException("综合评定数据错误，commentMemberId : $commentMemberId 对应多条综合评定数据",
            Result<Any>(errors, 2009, HttpStatus.OK.value(), "综合评定数据错误，请联系管理员"))
    }

    /**
     * 记录历史流水
     * @param entity
     * @param userId
     * @param now
     */
    private fun recordHistory(entity: MeetingCommentMemberComplexEntity, userId: Long, now: LocalDateTime) {
        val historyEntity = MeetingCommentMemberComplexHistoryEntity()
        historyEntity.complexId = entity.commentMemberComplexId
        historyEntity.rating = entity.complexRating
        historyEntity.suggestion = entity.complexSuggestion
        historyEntity.recorder = userId
        historyEntity.recordTime = now
        meetingCommentMemberComplexHistoryMapper.insert(historyEntity)
    }

    fun statisticalComplex(commentId: Long): CommentStatisticalDataForm {
        return meetingCommentMemberComplexMapper.complexStatistical(commentId) ?: CommentStatisticalDataForm()
    }
}