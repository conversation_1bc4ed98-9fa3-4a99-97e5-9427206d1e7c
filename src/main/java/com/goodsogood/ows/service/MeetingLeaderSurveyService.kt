package com.goodsogood.ows.service

import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.LocalDateTimeUtils
import com.goodsogood.ows.model.db.MeetingLeaderSurveyEntity
import com.goodsogood.ows.model.vo.MeetingLeaderSurveyForm
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.repository.MeetingLeaderSurveyRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.domain.Specification
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import java.time.LocalDateTime
import javax.persistence.criteria.Predicate

/**
 * <AUTHOR>
 * @date 2023/10/10
 * @description class MeetingLeaderSurveyService
 */
@Service
class MeetingLeaderSurveyService(
    val errors: Errors,
    val meetingLeaderSurveyRepository: MeetingLeaderSurveyRepository,
    val openService: OpenService,
) {
    /**
     * 添加领导调研
     * @param entity 领导调研实体
     * @param headers 请求头
     * @param sysHeader 系统头 用于获取用户id
     */
    fun saveOrUpdate(
        entity: MeetingLeaderSurveyEntity,
        headers: HttpHeaders,
        sysHeader: HeaderHelper.SysHeader
    ): MeetingLeaderSurveyEntity {
        if (entity.id == null) {
            // 新增
            // 添加创建人、创建时间、更新人、更新时间
            entity.orgId = sysHeader.uoid ?: sysHeader.oid
            entity.createUser = sysHeader.userId
            entity.createTime = LocalDateTime.now()
            entity.updateUser = entity.createUser
            entity.updateTime = entity.createTime
        } else {
            val temp = meetingLeaderSurveyRepository.getById(entity.id!!) ?: throw ApiException(
                "领导调研",
                Result<Any>(
                    errors,
                    9404,
                    HttpStatus.OK.value(),
                    "领导调研"
                )
            )
            // 修改
            // 添加更新人、更新时间
            entity.orgId = temp.orgId
            entity.createTime = temp.createTime
            entity.createUser = temp.createUser
            entity.updateUser = sysHeader.userId
            entity.updateTime = LocalDateTime.now()
        }
        return meetingLeaderSurveyRepository.saveAndFlush(entity)
    }

    fun getOne(id: Long): MeetingLeaderSurveyEntity {
        return meetingLeaderSurveyRepository.findById(id).orElseThrow {
            ApiException(
                "领导调研",
                Result<Any>(
                    errors,
                    9404,
                    HttpStatus.OK.value(),
                    "领导调研"
                )
            )
        }
    }

    fun deleteById(id: Long) {
        meetingLeaderSurveyRepository.deleteById(id)
    }

    /**
     * 查询领导调研列表
     * @param form 领导调研实体
     * @param page 页码
     * @param pageSize 每页条数
     * @param page Int 页码
     * @param pageSize Int 每页条数
     * @param httpHeaders HttpHeaders
     * @param header HeaderHelper.SysHeader
     * @return Page<MeetingLeaderSurveyEntity>
     */
    fun listByQuery(
        form: MeetingLeaderSurveyForm,
        page: Int,
        pageSize: Int,
        headers: HttpHeaders,
        sysHeader: HeaderHelper.SysHeader
    ): Page<MeetingLeaderSurveyEntity> {
        // 通过from表单的条件，生成查询条件，获取对应的MeetingLeaderSurveyEntity
        val oid = sysHeader.uoid ?: sysHeader.oid
        // 如果不是顶级组织，需要获取下级组织的条件
        val orgIds: List<Long> = if (oid == 3L) {
            listOf()
        } else {
            openService.getOrgInfoList(oid, 1).map { it.orgId }
        }
        // 通过Specification构建查询条件
        val spec = Specification<MeetingLeaderSurveyEntity> { root, _, criteriaBuilder ->
            val predicates: MutableList<Predicate> = mutableListOf()
            // 添加默认条件 where 1 = 1
            predicates.add(criteriaBuilder.equal(criteriaBuilder.literal(1), 1))
            if (orgIds.isNotEmpty()) {
                predicates.add(root.get<Long>("orgId").`in`(orgIds))
            }
            // 添加查询条件
            // 通过领导姓名查询,where  subject=1 and target like '%form.leader%'
            if (form.leader != null && form.leader?.isNotEmpty() == true) {
                predicates.add(criteriaBuilder.equal(root.get<Int>("subject"), 1))
                predicates.add(criteriaBuilder.like(root.get("target"), "%${form.leader}%"))
            }
            // 通过部门名称查询,where  subject=2 and target like '%form.orgName%'
            if (form.orgName != null && form.orgName?.isNotEmpty() == true) {
                predicates.add(criteriaBuilder.equal(root.get<Int>("subject"), 2))
                predicates.add(criteriaBuilder.like(root.get("target"), "%${form.orgName}%"))
            }
            // 通过调研类型查询
            if (form.surveryType != null) {
                predicates.add(criteriaBuilder.equal(root.get<Int>("surveryType"), form.surveryType))
            }
            // 通过调研方式查询 interview是个数组，需要循环数组，使用find_in_set去查询字符串字段interview
            if (form.interview != null) {
                form.interview?.forEach {
                    predicates.add(criteriaBuilder.like(root.get("interview"), "%$it%"))
                }
            }
            // 通过调研时间查询
            if (form.surveryTime != null && form.surveryTime?.size == 2) {
                predicates.add(
                    criteriaBuilder.between(
                        root.get("surveryTime"),
                        LocalDateTimeUtils.toLocalDate(form.surveryTime?.get(0) ?: "2000-01-01"),
                        LocalDateTimeUtils.toLocalDate(form.surveryTime?.get(1) ?: "2900-12-31"),
                    )
                )
            }

            criteriaBuilder.and(*predicates.toTypedArray())
        }
        return meetingLeaderSurveyRepository.findAll(
            spec,
            PageRequest.of(page - 1, pageSize, Sort.by("updateTime").descending())
        )
    }


    fun findSurveyByYear(year: Int): Map<String, Int> {
        val numYear = meetingLeaderSurveyRepository.countByYearAndSurveryType(year, 1) ?: 0
        val numDaily = meetingLeaderSurveyRepository.countByYearAndSurveryType(year, 2) ?: 0
        return mapOf("survey_year" to numYear, "survey_daily" to numDaily)
    };

}