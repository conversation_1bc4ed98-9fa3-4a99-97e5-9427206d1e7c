package com.goodsogood.ows.service;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.MeetingTypeCompareConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.TbcErrorLogFlowMapper;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.BatchForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Log4j2
public class TransferService {
    private final OpenService openService;
    private final Errors errors;
    private final MeetingTypeCompareConfig meetingTypeCompareConfig;
    private final TbcErrorLogFlowMapper tbcErrorLogFlowMapper;

    @Autowired
    public TransferService(OpenService openService, Errors errors, MeetingTypeCompareConfig meetingTypeCompareConfig, TbcErrorLogFlowMapper tbcErrorLogFlowMapper) {
        this.openService = openService;
        this.errors = errors;
        this.meetingTypeCompareConfig = meetingTypeCompareConfig;
        this.tbcErrorLogFlowMapper = tbcErrorLogFlowMapper;
    }


    /**
     * 将机构名称转为org_id
     */
    public void transferOrgNametoId(){

    }


    /**
     * 设置headers的user_id org_id
     * @param headers
     */
    public void transferHeaders(HttpHeaders headers){
//        log.debug("将手机号转为烟草的user_id");
//        String headerPhone = headers.get("_phone")==null? "" : headers.get("_phone").get(0);
//        if (StringUtils.isBlank(headerPhone)) {
//            throw new ApiException("用户在行动者先锋不存在，请联系管理员!", new Result<>(errors, 2120, HttpStatus.BAD_REQUEST.value()));
//        }
//        //TODO!! 调用用户中心根据用户名和手机号获取user_id
//        Long userId = null;
//        if (userId == null) {
//            throw new ApiException("用户在行动者先锋不存在，请联系管理员!", new Result<>(errors, 2120, HttpStatus.BAD_REQUEST.value()));
//        }
//        headers.set("_uid", userId.toString());
//        //调用用户中心根据orgName获取org_id
//        Long orgId = null;
//        headers.set("_oid", orgId.toString());
//        headers.set("_region_id","19");
    }

    /**
     * 设置headers sysHeader的user_id org_id
     * @param headers
     * @param sysHeader
     */
    public void transferAllHeaders(HttpHeaders headers, HeaderHelper.SysHeader sysHeader){
//        String headerPhone = headers.get("_phone").get(0);
//        //调用用户中心根据用户名和手机号获取user_id
//        UserInfoBase userInfoBase = new UserInfoBase();
//        userInfoBase.setName(sysHeader.getUserName());
//        userInfoBase.setPhone(headerPhone);
//        List<UserInfoBase> userInfo = openService.findUserIdByPhone(19L,Arrays.asList(userInfoBase));
//        Long userId = CollectionUtils.isEmpty(userInfo)? null: userInfo.get(0).getUserId();
//        if (userId == null) {
//            throw new ApiException("用户在烟草不存在", new Result<>(errors, 1807, HttpStatus.BAD_REQUEST.value()));
//        }
//        headers.set("_uid", userId.toString());
//        sysHeader.setUserId(userId);
//        //调用用户中心根据orgName获取org_id
//        List<OrganizationBase> orgList = openService.findOrgIdByName(19L,Arrays.asList(sysHeader.getOrgName()));
//        Long orgId = orgList.get(0).getOrgId();
//        headers.set("_oid", orgId.toString());
//        sysHeader.setOid(orgId);
//        log.debug("mtheaders:"+headers);
    }

    /**
     * 判断是否是红岩魂传入的
     * @param headers
     * @return
     */
    public Boolean fromSystemSzf(HttpHeaders headers){
        if(CollectionUtils.isEmpty(headers.get("source")))
            return false;
        return headers.get("source").get(0).equals("szf");
    }


    /**
     * meeting_type匹配，将红岩魂的meeting_type转为烟草的
     * @param meetingEntity
     */
    public void transferMeetingType(MeetingEntity meetingEntity) {
        List<MeetingTypeEntity> meetingTypes = meetingEntity.getMeetingTypes();
        for(MeetingTypeEntity meetingType : meetingTypes){
            meetingType.setTypeId(meetingTypeCompareConfig.getTobaccoType(meetingType.getTypeId()));
            meetingType.setCategoryId(tranferCategory(meetingType.getCategoryId()));
        }
    }

    //红岩魂的category转为烟草的
    public Long tranferCategory(Long categoryId){
        return categoryId.equals(1L)? 2L: null;
    }


    /**
     * 将红岩魂记录人等手机号转为烟草的user_id
     * @param headers
     * @param meetingEntity
     */
    public void transferPhoneToUserId(HttpHeaders headers, MeetingEntity meetingEntity) {
        //封装人员数据
        List<MeetingUserEntity> allUser = new ArrayList<>();
        List<MeetingUserEntity> participantUsers = meetingEntity.getParticipantUsers();//参会人
        List<MeetingUserEntity> hostUsers = meetingEntity.getHostUser();//主持人
        List<MeetingUserEntity> recordUsers = meetingEntity.getRecordUser();//记录人
        List<MeetingUserEntity> attendUsers = meetingEntity.getAttendUsers();//列席人
        List<MeetingUserEntity> lectureUsers = meetingEntity.getLecturers();//讲课人

        List<MeetingContactLeaderEntity> contactLeaders = meetingEntity.getContactLeaders();//联系领导
        if(CollectionUtils.isNotEmpty(participantUsers)){
            allUser.addAll(participantUsers);
        }
        if(CollectionUtils.isNotEmpty(hostUsers)){
            allUser.addAll(hostUsers);
        }
        if(CollectionUtils.isNotEmpty(recordUsers)){
            allUser.addAll(recordUsers);
        }
        if(CollectionUtils.isNotEmpty(attendUsers)){
            allUser.addAll(attendUsers);
        }
        if(CollectionUtils.isNotEmpty(lectureUsers)){
            allUser.addAll(lectureUsers);
        }
        if(CollectionUtils.isNotEmpty(contactLeaders)){
            contactLeaders.forEach(i->{
                MeetingUserEntity userEntity = new MeetingUserEntity();
                userEntity.setRealPhone(i.getRealPhone());
                userEntity.setUserName(i.getUserName());
                allUser.add(userEntity);
            });
        }

        //远程调用用户中心获取人员手机号
        List<BatchForm> userList = new ArrayList<>();
        allUser.forEach(i->{
            BatchForm userInfoBase = new BatchForm();
            userInfoBase.setName(i.getUserName());
            userInfoBase.setPhone(i.getRealPhone());
            userList.add(userInfoBase);
        });
        List<UserInfoBase> userPhoneList = openService.findUserIdByPhone(headers,userList);
        log.debug("List<UserInfoBase>" +userPhoneList);
        if(CollectionUtils.isEmpty(userPhoneList)){
            return;
        }
        Map<String,List<UserInfoBase>> userPhoneMap = userPhoneList.stream().collect(Collectors.groupingBy(i->i.getPhone()+i.getName()));
        List<TbcErrorLogFlowEntity>  errorFlow = new ArrayList();
        setUserIdByPhone(participantUsers, userPhoneMap,errorFlow);
        setUserIdByPhone(hostUsers, userPhoneMap,errorFlow);
        setUserIdByPhone(recordUsers, userPhoneMap,errorFlow);
        setUserIdByPhone(attendUsers, userPhoneMap,errorFlow);
        setUserIdByPhone(lectureUsers, userPhoneMap,errorFlow);
        setLeaderIdByPhone(contactLeaders,userPhoneMap,errorFlow);
        log.debug("将红岩魂记录人等手机号转为烟草的user_id=>{}", JsonUtils.toJson(meetingEntity));
        if(!CollectionUtils.isEmpty(errorFlow)){
            tbcErrorLogFlowMapper.insertList(errorFlow);
        }
    }

    private void setUserIdByPhone(List<MeetingUserEntity> meetingUsers, Map<String, List<UserInfoBase>> userPhoneMap,List<TbcErrorLogFlowEntity> errorFlow) {
        if(CollectionUtils.isEmpty(meetingUsers)){
            return;
        }
        for(MeetingUserEntity user : meetingUsers){
            String key = user.getRealPhone() + user.getUserName();
            if(CollectionUtils.isEmpty(userPhoneMap.get(key))){
                TbcErrorLogFlowEntity flow = new TbcErrorLogFlowEntity();
                flow.setReason("人员不匹配:"+key);
//                flow.setMeetingId(meetingId);
                flow.setUserId(user.getUserId());
                flow.setRequest("/result/add");
                flow.setCreateTime(LocalDateTime.now());
                errorFlow.add(flow);

            }else{
                UserInfoBase userInfo = userPhoneMap.get(key).get(0);
                user.setUserId(userInfo==null? null: userInfo.getUserId());
                user.setOrgId(userInfo==null? null: userInfo.getOrgId());
            }
        }
    }


    private void setLeaderIdByPhone(List<MeetingContactLeaderEntity> meetingUsers, Map<String, List<UserInfoBase>> userPhoneMap,List<TbcErrorLogFlowEntity> errorFlow) {
        for(MeetingContactLeaderEntity user : meetingUsers){
            String key = user.getRealPhone() + user.getUserName();
            if(CollectionUtils.isEmpty(userPhoneMap.get(key))){
                TbcErrorLogFlowEntity flow = new TbcErrorLogFlowEntity();
                flow.setReason("人员不匹配:"+key);
//                flow.setMeetingId(meetingId);
                flow.setUserId(user.getUserId());
                flow.setRequest("/result/add");
                flow.setCreateTime(LocalDateTime.now());
                errorFlow.add(flow);

            }else{
                UserInfoBase userInfo = userPhoneMap.get(key).get(0);
                user.setUserId(userInfo==null? null: userInfo.getUserId());
                user.setOrgId(userInfo==null? null: userInfo.getOrgId());
            }
        }
    }
}