package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.GroupMapper;
import com.goodsogood.ows.mapper.TypeGroupMapper;
import com.goodsogood.ows.model.db.GroupEntity;
import com.goodsogood.ows.model.db.TypeEntity;
import com.goodsogood.ows.model.db.TypeGroupEntity;
import com.goodsogood.ows.model.vo.GroupAllResultForm;
import com.goodsogood.ows.model.vo.GroupListForm;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:25
 **/
@Service
@Log4j2
public class GroupService {

    private final GroupMapper mapper;
    private final TypeGroupMapper typeGroupMapper;

    @Autowired
    public GroupService(GroupMapper mapper, TypeGroupMapper typeGroupMapper) {
        this.mapper = mapper;
        this.typeGroupMapper = typeGroupMapper;
    }

    /**
     * 添加类型组合
     *
     * @param sysHeader   用户信息
     * @param groupEntity 类型组合信息
     * @return 新增的类型id
     */
    @Transactional
    public Long addGroup(HeaderHelper.SysHeader sysHeader, GroupEntity groupEntity) {
        groupEntity.setOrgId(sysHeader.getOid());
        groupEntity.setCreateUser(sysHeader.getUserId());
        groupEntity.setOrgName(sysHeader.getOrgName());
        groupEntity.setCreateTime(DateTime.now().toDate());
        groupEntity.setRegionId(sysHeader.getRegionId());
        mapper.insert(groupEntity);
        List<TypeEntity> types = groupEntity.getTypes();
        List<TypeGroupEntity> typeGroupEntities = new ArrayList<>();
        types.forEach(type -> {
            TypeGroupEntity typeGroupEntity = new TypeGroupEntity();
            typeGroupEntity.setGroupId(groupEntity.getGroupId());
            typeGroupEntity.setTypeId(type.getTypeId());
            typeGroupEntities.add(typeGroupEntity);
        });
        typeGroupMapper.insertList(typeGroupEntities);
        return groupEntity.getGroupId();
    }

    /**
     * 删除活动组合
     */
    @Transactional(rollbackFor = Exception.class)
    public int delGroup(long id) {
        Example example = new Example(TypeGroupEntity.class);
        example.createCriteria().andEqualTo("groupId", id);
        typeGroupMapper.deleteByExample(example);
        return mapper.deleteByPrimaryKey(id);
    }

    /**
     * 查询活动类型组合
     */
    public List<GroupEntity> listAllGroupEntity(HeaderHelper.SysHeader sysHeader) {
        return mapper.findAll(sysHeader.getRegionId());
    }

    /**
     * 查询活动类型组合
     */
    public List<GroupAllResultForm> listAllGroupResultForm(HeaderHelper.SysHeader sysHeader) {
        return getGroupAllResultFroms(mapper.findAll(sysHeader.getRegionId()));
    }

    /**
     * 查询活动类型组合
     */
    public Page<GroupAllResultForm> listPageGroup(GroupListForm groupListFrom) {
        Page<GroupEntity> pageGroupEntity = PageHelper.startPage(groupListFrom.getPageBean().getPageNo(), groupListFrom.getPageBean().getPageSize())
                .doSelectPage(() -> this.mapper.findAll(groupListFrom.getRegionId()));
        Page<GroupAllResultForm> page = new Page<>();
        page.addAll(this.getGroupAllResultFroms(pageGroupEntity.getResult()));
        page.setPageNum(pageGroupEntity.getPageNum());
        page.setPages(pageGroupEntity.getPages());
        page.setPageSize(pageGroupEntity.getPageSize());
        page.setTotal(pageGroupEntity.getTotal());
        return page;
    }

    /**
     * 组装返回结果集
     */
    private List<GroupAllResultForm> getGroupAllResultFroms(List<GroupEntity> groupEntities) {
        List<GroupAllResultForm> groupAllResultFroms = new ArrayList<>();
        groupEntities.forEach(groupEntity -> {
            GroupAllResultForm groupAllResultFrom = new GroupAllResultForm();
            groupAllResultFroms.add(groupAllResultFrom);
            groupAllResultFrom.setGroupId(groupEntity.getGroupId());
            List<Long> typeIds = groupEntity.getTypes().stream().map(TypeEntity::getTypeId).collect(Collectors.toList());
            groupAllResultFrom.setTypeIds(typeIds);
            String types = groupEntity.getTypes().stream().map(TypeEntity::getType).collect(Collectors.joining(","));
            groupAllResultFrom.setTypes(types);
        });
        return groupAllResultFroms;
    }
}