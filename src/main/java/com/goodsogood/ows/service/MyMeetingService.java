package com.goodsogood.ows.service;

import com.goodsogood.ows.common.RedisConstant;
import com.goodsogood.ows.helper.MeetingStatusHelper;
import com.goodsogood.ows.mapper.MeetingLeaveMapper;
import com.goodsogood.ows.mapper.MeetingMapper;
import com.goodsogood.ows.model.db.MeetingLeaveEntity;
import com.goodsogood.ows.model.vo.MyMeetingListForm;
import com.goodsogood.ows.model.vo.MyMeetingResultListForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-25 10:53
 **/
@Service
@Log4j2
public class MyMeetingService {

    private final MeetingMapper meetingMapper;
    private final MeetingLeaveMapper meetingLeaveMapper;
    private final StringRedisTemplate stringRedisTemplate;


    @Autowired
    public MyMeetingService(MeetingMapper meetingMapper, MeetingLeaveMapper meetingLeaveMapper, StringRedisTemplate stringRedisTemplate) {
        this.meetingMapper = meetingMapper;
        this.meetingLeaveMapper = meetingLeaveMapper;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 查询活动列表
     */
    public List<MyMeetingResultListForm> list(MyMeetingListForm meetingListForm) {
        List<MyMeetingResultListForm> list = this.meetingMapper.findMyMeetingAll(meetingListForm);
        if (list != null && !list.isEmpty()) {
            List<Long> mids = list.stream().map(MyMeetingResultListForm::getMeetingId).collect(Collectors.toList());
            Example example = new Example(MeetingLeaveEntity.class);
            example.createCriteria().andIn("meetingId", mids).andEqualTo("userId", meetingListForm.getUserId());
            example.orderBy("createTime").desc();
            List<MeetingLeaveEntity> meetingLeaveEntities = meetingLeaveMapper.selectByExample(example);
            list.forEach(myMeeting -> {
                myMeeting.setMeetingStatus(MeetingStatusHelper.convertToFormStatus(myMeeting.getMeetingStatus(), myMeeting.getStartTime()));
                myMeeting.setFmMeetingStatus(MeetingStatusHelper.convertToMyMeetingFormStatus(myMeeting.getMeetingStatus()));
                MeetingLeaveEntity meetingLeaveEntity = null;
                for (MeetingLeaveEntity mlt : meetingLeaveEntities) {//最新的请假状态
                    if (mlt.getMeetingId().equals(myMeeting.getMeetingId())) {
                        meetingLeaveEntity = mlt;
                        break;
                    }
                }
                // 查询请假情况 返回状态：	1:待审批;2:已通过;3:未通过;4:取消;5:未请假;
                // 请假审批类型（1：待审批 2：同意 3：不同意 4：取消）',
                if (meetingLeaveEntity == null) {//未请假;
                    myMeeting.setStatus((short) 5);
                } else {
                    myMeeting.setMeetingLeaveId(meetingLeaveEntity.getMeetingLeaveId());
                    myMeeting.setStatus(meetingLeaveEntity.getStatus());
                }
            });
        }
        return list;
    }

    /**
     * 我的待举办活动
     *
     * @param uid 用户id
     */
    public int waitDoMeetingCountByRedis(Long regionId, Long uid) {
        // uid 为空 返回0
        if (uid == null) {
            return 0;
        }
        String redisKey = waitDoMeetingCountRedisKey(regionId, uid);

        log.debug("stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).hasKey(redisKey):{}", stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).hasKey(redisKey));
        Object redisTopicCount = stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).get(redisKey);
        // 缓存中有值，返回缓存值
        if (redisTopicCount != null) {
            return Integer.valueOf((String) redisTopicCount);
        }
        return waitDoMeetingCountAndRedisByDb(regionId, uid);
    }

    /**
     * 我的待举办活动 查询数据库 添加缓存
     *
     * @param uid 用户id
     */
    public Integer waitDoMeetingCountAndRedisByDb(Long regionId, Long uid) {
        String redisKey = waitDoMeetingCountRedisKey(regionId, uid);
        Integer count = waitDoMeetingCount(regionId, uid);
        stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).put(redisKey, count.toString());
        return count;
    }

    public Integer waitDoMeetingCount(Long regionId, Long uid) {
        MyMeetingListForm myMeetingListForm = new MyMeetingListForm();
        myMeetingListForm.setStatus((short) 1);//待举办
        myMeetingListForm.setUserId(uid);
        myMeetingListForm.setRegionId(regionId);
        return stats(myMeetingListForm);
    }

    /**
     * redis key
     *
     * @param uid 用户id
     * @return key
     */
    public String waitDoMeetingCountRedisKey(Long regionId, Long uid) {
        return RedisConstant.MEETING_WAIT_DO_MEETING + regionId + "_" + uid;
    }

    /**
     * 活动总数查询
     */
    public int stats(MyMeetingListForm meetingListForm) {
        return meetingMapper.findMyMeetingAll(meetingListForm).size();
    }
}