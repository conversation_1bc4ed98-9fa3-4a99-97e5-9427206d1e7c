package com.goodsogood.ows.service;

import cn.hutool.core.convert.Convert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.*;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.CommendPenalizeConfiguration;
import com.goodsogood.ows.configuration.DorisScoreConstant;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingOrgCommendPenalizeMapper;
import com.goodsogood.ows.model.db.MeetingOrgCommendPenalizeEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.workflow.ApprovalBase;
import com.goodsogood.ows.utils.BeanUtil;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ExportExcel;
import com.goodsogood.ows.utils.JsonUtils;
import com.google.common.base.Preconditions;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe 组织奖惩接口
 * @date 2019-12-30
 */
@Service
@Log4j2
public class MeetingOrgCommendPenalizeService {

    private static final String REDIS_KEY_FILE = "REDIS_KEY_ORG_FILE_";

    private final Errors errors;
    private final CommendPenalizeConfiguration commendPenalizeConfiguration;
    private final MeetingOrgCommendPenalizeMapper meetingOrgCommendPenalizeMapper;
    private final UserCenterService userCenterService;
    private final MeetingFileService meetingFileMapper;
    private final OpenService openService;
    private final HeaderService headerService;
    private final WorkflowService workflowService;
    private final RestTemplate restTemplate;
    private final TogServicesConfig togServicesConfig;
    private final StringRedisTemplate redisTemplate;
    private final ThirdService thirdService;


    @Autowired
    public MeetingOrgCommendPenalizeService(Errors errors, CommendPenalizeConfiguration commendPenalizeConfiguration,
                                            MeetingOrgCommendPenalizeMapper meetingOrgCommendPenalizeMapper,
                                            UserCenterService userCenterService, MeetingFileService meetingFileMapper, OpenService openService,
                                            HeaderService headerService, WorkflowService workflowService, RestTemplate restTemplate, TogServicesConfig togServicesConfig, StringRedisTemplate redisTemplate, ThirdService thirdService) {
        this.errors = errors;
        this.commendPenalizeConfiguration = commendPenalizeConfiguration;
        this.meetingOrgCommendPenalizeMapper = meetingOrgCommendPenalizeMapper;
        this.userCenterService = userCenterService;
        this.meetingFileMapper = meetingFileMapper;
        this.openService = openService;
        this.headerService = headerService;
        this.workflowService = workflowService;
        this.restTemplate = restTemplate;
        this.togServicesConfig = togServicesConfig;
        this.redisTemplate = redisTemplate;
        this.thirdService = thirdService;
    }


    /**
     * 添加组织奖励或者惩罚
     */
    @Transactional
    public void add(HttpHeaders headers, MeetingOrgCommendPenalizeForm dto) {
        Date date = new Date();
        MeetingOrgCommendPenalizeEntity entity = new MeetingOrgCommendPenalizeEntity();
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long regionId = sysHeader.getRegionId();
        // 判断奖惩级别
        final Integer type = dto.getType();
        final Integer level = dto.getLevel();
        final Integer name = dto.getName();
        if (type.equals(Constant.REWARD)) {
            // 级别
            if (level == null) {
                throw new ApiException("奖励级别不能为空 ", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "奖励级别不能为空"));
            }
            entity.setLevel(level);
            //不走审批流审核，直接给通过
//            entity.setApprovalStatus(CommendPenalizeStatusEnum.UNDER_REVIEW.getKey());
            entity.setApprovalStatus(CommendPenalizeStatusEnum.PASS.getKey());
        } else {
            entity.setApprovalStatus(CommendPenalizeStatusEnum.PASS.getKey());
        }
        // 党奖名称为其他时，内容为必填
        if (Objects.equals(name, Constant.REWARD_NAME_ORG_OTHER)) {
            String content = dto.getContent();
            if (StringUtils.isBlank(content)) {
                throw new ApiException("奖励名称不能为空 ", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "奖励名称不能为空"));
            }
        }

        OrganizationBase org = userCenterService.findOrgByList(Collections.singletonList(dto.getOrgId()), sysHeader).get(0);
        BeanUtils.copyProperties(dto, entity);
        entity.setStatus(StatusEnum.NORMAL.getStatus());
        entity.setCreateUser(sysHeader.getUserId());
        entity.setRegionId(regionId);
        entity.setOrgLevel(org.getOrgLevel());
        entity.setCreateTime(date);

        meetingOrgCommendPenalizeMapper.insertUseGeneratedKeys(entity);
        if (Objects.nonNull(dto.getFiles())) {
            meetingFileMapper.addFile(entity.getMeetingOrgCommendPenalizeId(), dto.getFiles(), FileSourceEnum.ORG_COMMEND_PENALIZE);
        }
        if (Objects.nonNull(dto.getHonorPic())) {
            meetingFileMapper.addFile(entity.getMeetingOrgCommendPenalizeId(), dto.getHonorPic(), FileSourceEnum.ORG_COMMEND_PENALIZE_PIC);
        }

        if (type.equals(Constant.REWARD)) {
            //暂时不做工作流审批
//            final Long workflowTaskId = this.createWorkflow(entity, headers);
//            // 保存审批流ID
//            entity.setWorkflowTaskId(workflowTaskId);
//            entity.setUpdateTime(new Date());
//            this.meetingOrgCommendPenalizeMapper.updateByPrimaryKey(entity);
            //暂时不使用工作流,回调直接执行回调增加相关奖励逻辑
            this.workflowSkipCallBack(entity, headers);
        }

    }

    /**
     * 修改组织奖惩信息
     */
    @Transactional
    public void edit(HeaderHelper.SysHeader user, MeetingOrgCommendPenalizeForm dto, HttpHeaders headers) {
        final MeetingOrgCommendPenalizeEntity entity = this.meetingOrgCommendPenalizeMapper.selectByPrimaryKey(dto.getMeetingOrgCommendPenalizeId());
        // 判断奖惩类型
        final Integer type = entity.getType();
        if (entity.getApprovalStatus().equals(CommendPenalizeStatusEnum.PASS.getKey())) {
            throw new ApiException("审核已通过，不支持编辑", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "审核已通过，不支持编辑"));
        }
        // 级别
        final Integer level = dto.getLevel();
        final Integer name = dto.getName();
        if (type.equals(Constant.REWARD)) {
            if (level == null) {
                throw new ApiException("奖励级别不能为空 ", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "奖励级别不能为空"));
            }
            entity.setLevel(level);
        }
        // 党奖名称为其他时，内容为必填
        if (Objects.equals(name, Constant.REWARD_NAME_ORG_OTHER)) {
            String content = dto.getContent();
            if (StringUtils.isBlank(content)) {
                throw new ApiException("奖励名称不能为空 ", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "奖励名称不能为空"));
            }
        }
        BeanUtils.copyProperties(dto, entity);
        //OrganizationBase org = userCenterService.findOrgByList(Collections.singletonList(dto.getOrgId())).get(0);

        //edit.setOrgLevel(org.getOrgLevel());
        entity.setApprovalStatus(CommendPenalizeStatusEnum.UNDER_REVIEW.getKey());
        entity.setUpdateTime(new Date());
        entity.setLastChangeUser(user.getUserId());
        meetingOrgCommendPenalizeMapper.updateByPrimaryKeySelective(entity);

        meetingFileMapper.delete(dto.getMeetingOrgCommendPenalizeId(), FileSourceEnum.ORG_COMMEND_PENALIZE);
        meetingFileMapper.delete(dto.getMeetingOrgCommendPenalizeId(), FileSourceEnum.ORG_COMMEND_PENALIZE_PIC);
        meetingFileMapper.addFile(dto.getMeetingOrgCommendPenalizeId(), dto.getFiles(), FileSourceEnum.ORG_COMMEND_PENALIZE);
        meetingFileMapper.addFile(dto.getMeetingOrgCommendPenalizeId(), dto.getHonorPic(), FileSourceEnum.ORG_COMMEND_PENALIZE_PIC);

        if (type.equals(Constant.REWARD)) {
            // 当审核状态为待审核时，需要先撤销审批流程
            if (entity.getApprovalStatus().equals(CommendPenalizeStatusEnum.UNDER_REVIEW.getKey())) {
                this.workflowService.undoApprove(entity.getWorkflowTaskId(), headers);
            }
            final Long workflowTaskId = this.createWorkflow(entity, headers);
            // 保存审批流ID
            entity.setWorkflowTaskId(workflowTaskId);
            entity.setUpdateTime(new Date());
            this.meetingOrgCommendPenalizeMapper.updateByPrimaryKey(entity);
        }
    }

    /**
     * 查询组织奖惩详情
     */
    public MeetingOrgCommendPenalizeDetailVO detail(MeetingOrgCommendPenalizeForm dto) {
        MeetingOrgCommendPenalizeEntity example = new MeetingOrgCommendPenalizeEntity();
        example.setMeetingOrgCommendPenalizeId(dto.getMeetingOrgCommendPenalizeId());
        example.setStatus(StatusEnum.NORMAL.getStatus());
        MeetingOrgCommendPenalizeEntity select = meetingOrgCommendPenalizeMapper.selectOne(example);
        MeetingOrgCommendPenalizeDetailVO vo = BeanUtil.copy(select, MeetingOrgCommendPenalizeDetailVO.class);
        if (!Objects.isNull(vo)) {
            vo.setFiles(meetingFileMapper.selectByLinkedId(dto.getMeetingOrgCommendPenalizeId(),
                    FileSourceEnum.ORG_COMMEND_PENALIZE));
            vo.setHonorPic(meetingFileMapper.selectByLinkedId(dto.getMeetingOrgCommendPenalizeId(),
                    FileSourceEnum.ORG_COMMEND_PENALIZE_PIC));
        }

        return vo;
    }

    /**
     * 删除组织奖惩
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(HttpHeaders headers, MeetingOrgCommendPenalizeForm dto) {
        final MeetingOrgCommendPenalizeEntity entity =
                this.meetingOrgCommendPenalizeMapper.selectByPrimaryKey(dto.getMeetingOrgCommendPenalizeId());
        if (entity.getApprovalStatus().equals(CommendPenalizeStatusEnum.PASS.getKey())) {
            throw new ApiException("审核已通过，不支持编辑", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "审核已通过，不支持编辑"));
        }
        final HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        entity.setStatus(StatusEnum.DELETE.getStatus());
        entity.setUpdateTime(new Date());
        entity.setLastChangeUser(header.getUserId());
        meetingOrgCommendPenalizeMapper.updateByPrimaryKeySelective(entity);
        if (entity.getType().equals(Constant.REWARD)) {
            // 当审核状态为待审核时，需要先撤销审批流程
            if (entity.getApprovalStatus().equals(CommendPenalizeStatusEnum.UNDER_REVIEW.getKey())) {
                this.workflowService.undoApprove(entity.getWorkflowTaskId(), headers);
            }
        }
    }

    /**
     * 查询组织奖惩信息列表
     */
    public Page<MeetingOrgCommendPenalizeQueryVO> list(MeetingOrgCommendPenalizeQueryForm dto, HttpHeaders headers) {
        final HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        OrganizationBase org = userCenterService.findOrgByList(Collections.singletonList(dto.getOrgId()), header).get(0);
        dto.setOrgLevel(org.getOrgLevel());
        dto.setStatus(StatusEnum.NORMAL.getStatus());
        Page<MeetingOrgCommendPenalizeQueryVO> dataList = PageHelper.startPage(dto.getPageNum(), dto.getPageSize())
                .doSelectPage(() -> meetingOrgCommendPenalizeMapper.listMeetingOrgCommendPenalizeQueryVO(dto));
        return dataList;
    }

    /**
     * 导出组织奖惩信息列表
     */
    @Async("commendPenalizeAsync")
    public void excelList(MeetingOrgCommendPenalizeQueryForm form, String uuid, HttpHeaders headers) {
        final HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        OrganizationBase org = userCenterService.findOrgByList(Collections.singletonList(form.getOrgId()), header).get(0);
        form.setOrgLevel(org.getOrgLevel());
        form.setStatus(StatusEnum.NORMAL.getStatus());
        final List<MeetingOrgCommendPenalizeQueryVO> voList = meetingOrgCommendPenalizeMapper.listMeetingOrgCommendPenalizeQueryVO(form);

        // 奖励类别
        final List<OptionForm> categoryCodeList = this.openService.getOptionListByCode(Constant.REWARD_CATEGORY.toString());
        // 奖励级别
        final List<OptionForm> levelCodeList = this.openService.getOptionListByCode(Constant.REWARD_LEVEL.toString());
        // 奖励名称
        final List<OptionForm> nameCodeList = this.openService.getOptionListByCode(Constant.REWARD_NAME_ORG_NEW.toString());
        // 惩罚ALL
        List<OptionForm> penalizeCodeList = this.openService.getOptionListByCode(Constant.PENALIZE_ALL.toString());
        // 准备数据
        final List<CommendPenalizeExportForm> forms = convertObject(voList, categoryCodeList, levelCodeList, nameCodeList, penalizeCodeList);
        final List<CommendPenalizeExportForm> rewardList = forms.stream().filter(vo -> Objects.equals(vo.getType(), Constant.REWARD)).collect(Collectors.toList());
        final List<CommendPenalizeExportForm> penalizeList = forms.stream().filter(vo -> Objects.equals(vo.getType(), Constant.PENALIZE)).collect(Collectors.toList());
        List<UploadFileResultForm> fileList = new ArrayList<>();
        // 奖励
        if (form.getType() == null || form.getType() == Constant.REWARD) {
            final UploadFileResultForm rewardForm = createExcel(rewardList, form.getAwardFields(), Constant.REWARD, headers);
            fileList.add(rewardForm);
        }
        // 惩罚
        if (form.getType() == null || form.getType() == Constant.PENALIZE) {
            final UploadFileResultForm penalizeForm = createExcel(penalizeList, form.getPenalizeFields(), Constant.PENALIZE, headers);
            fileList.add(penalizeForm);
        }
        redisTemplate.opsForValue().set(REDIS_KEY_FILE + uuid, JsonUtils.toJson(fileList), 5, TimeUnit.MINUTES);
    }

    public Object getCommendPenalizeFile(String uuid) {
        if(Boolean.TRUE.equals(redisTemplate.hasKey(REDIS_KEY_FILE + uuid))) {
            final String json = redisTemplate.opsForValue().get(REDIS_KEY_FILE + uuid);
            return JsonUtils.toObjList(json, UploadFileResultForm.class);
        } else {
            return -1;
        }
    }

    private List<CommendPenalizeExportForm> convertObject(List<MeetingOrgCommendPenalizeQueryVO> voList, List<OptionForm> rewardCategoryCodeList,
                                                          List<OptionForm> rewardLevelCodeList, List<OptionForm> rewardNameCodeList, List<OptionForm> penalizeCodeList) {
        List<CommendPenalizeExportForm> resultList = new ArrayList<>(voList.size());
        voList.forEach(vo -> {
            OptionForm categoryOption;
            OptionForm levelOption;
            OptionForm nameOption;
            if (vo.getType().equals(Constant.REWARD)) {
                categoryOption = this.openService.getOptionByList(rewardCategoryCodeList, vo.getCategory());
                levelOption = this.openService.getOptionByList(rewardLevelCodeList, vo.getLevel());
                nameOption = this.openService.getOptionByList(rewardNameCodeList, vo.getName());
            } else {
                categoryOption = this.openService.getOptionByList(penalizeCodeList, vo.getCategory());
                levelOption = this.openService.getOptionByList(penalizeCodeList, vo.getLevel());
                nameOption = this.openService.getOptionByList(penalizeCodeList, vo.getName());
            }
            CommendPenalizeExportForm form = new CommendPenalizeExportForm();
            form.setObjectName(vo.getOrgName());
            form.setDate(DateUtils.dateFormat(vo.getRatifyTime(), DateUtils.DATE_FORMAT));
            form.setCategory(categoryOption.getOpValue());
            form.setLevel(levelOption.getOpValue());
            form.setName(nameOption.getOpValue());
            form.setRelatedFile(vo.getRelatedFile());
            form.setAwardUnit(vo.getAwardUnit());
            form.setBasisDescription(vo.getBasisDescription());
            form.setType(vo.getType());
            resultList.add(form);
        });
        return resultList;
    }

    private UploadFileResultForm createExcel(List<CommendPenalizeExportForm> list,
                                             List<String> title, int type, HttpHeaders headers) {
        if (CollectionUtils.isNotEmpty(list)) {
            String name = (type == 1 ? "组织奖励信息" : "组织惩罚信息") + ".xls";
            try {
                return ((ExportExcel<CommendPenalizeExportForm>) (form, row) -> {
                    for (int i = 0; i < title.size(); i++) {
                        final String fieldName = title.get(i);
                        final CommendPenalizeDownloadFieldsEnum anEnum =
                                CommendPenalizeDownloadFieldsEnum.Companion.getFieldByTypeAndValue((type == 1 ? 3 : 4), fieldName);
                        if (anEnum != null) {
                            // 反射取值
                            try {
                                final Class<? extends CommendPenalizeExportForm> commentClass = form.getClass();
                                final Field field = commentClass.getDeclaredField(anEnum.getKey());
                                field.setAccessible(true);
                                row.createCell(i, CellType.STRING).setCellValue(Convert.toStr(field.get(form), ""));
                            } catch (Exception e) {
                                log.error("反射报错 -> ", e);
                            }
                        }
                    }
                }).createExcelUploadFileCenter(list, title, name, errors, headers, restTemplate, togServicesConfig);
            } catch (NoSuchFieldException e) {
                log.error("反射异常报错 -> ", e);
            } catch (IOException e) {
                log.error("IO异常 -> ", e);
            }
        }
        return null;
    }

    /**
     * 查询组织奖惩列表
     * <AUTHOR>
     * @date 2021/
     * @param orgId
     * @return com.github.pagehelper.Page<com.goodsogood.ows.model.vo.UserCommendPenalizeVO>
     */
    public Page<MeetingOrgCommendPenalizeQueryVO> queryOrgCommendPenalizeListByOrgId(Long orgId, Integer page, Integer pageSize) {
        //设置分页属性
        int p = Preconditions.checkNotNull(page);
        int r = Preconditions.checkNotNull(pageSize);
        Page<MeetingOrgCommendPenalizeQueryVO> pageData = PageHelper.startPage(p, r).doSelectPage(() ->
                this.meetingOrgCommendPenalizeMapper.getMeetingOrgCommendPenalizeByOrgId(orgId));
        // 奖励类别
        List<OptionForm> categoryCodeList = this.openService.getOptionListByCode(Constant.REWARD_CATEGORY.toString());
        // 奖励级别
        List<OptionForm> levelCodeList = this.openService.getOptionListByCode(Constant.REWARD_LEVEL.toString());
        // 奖励名称
        List<OptionForm> nameCodeList = this.openService.getOptionListByCode(Constant.REWARD_NAME_ORG_NEW.toString());
        pageData.forEach(data -> {
            final OptionForm categoryOption = this.openService.getOptionByList(categoryCodeList, data.getCategory());
            final OptionForm levelOption = this.openService.getOptionByList(levelCodeList, data.getLevel());
            final OptionForm nameOption = this.openService.getOptionByList(nameCodeList, data.getName());
            if (Objects.nonNull(categoryOption)) {
                data.setCategoryValue(categoryOption.getOpValue());
            }
            if (Objects.nonNull(levelOption)) {
                data.setLevelValue(levelOption.getOpValue());
            }
            if (Objects.nonNull(nameOption)) {
                data.setNameValue(nameOption.getOpValue());
            }
            /*List<Integer> categoryOptionKeyList = this.openService.getOptionKeyList(categoryCodeList, data.getName());
            List<Integer> levelOptionKeyList = this.openService.getOptionKeyList(levelCodeList, data.getName());
            List<Integer> nameOptionKeyList = this.openService.getOptionKeyList(nameCodeList, data.getName());
            data.setNameOpList(nameOptionKeyList);*/
            data.setFiles(meetingFileMapper.selectByLinkedId(data.getMeetingOrgCommendPenalizeId(), FileSourceEnum.ORG_COMMEND_PENALIZE));
            data.setHonorPic(meetingFileMapper.selectByLinkedId(data.getMeetingOrgCommendPenalizeId(), FileSourceEnum.ORG_COMMEND_PENALIZE_PIC));
        });
        return pageData;
    }

    private Long createWorkflow(MeetingOrgCommendPenalizeEntity entity, HttpHeaders headers) {
        // 建立审核流程
        final Long workflowId = Objects.equals(entity.getLevel(), Constant.REWARD_LEVEL_OTHER) ? this.commendPenalizeConfiguration.getWorkflowIdOther() : this.commendPenalizeConfiguration.getWorkflowIdNormal();
        final Integer workflowType = Objects.equals(entity.getLevel(), Constant.REWARD_LEVEL_OTHER) ? this.commendPenalizeConfiguration.getWorkflowTypeOther() : this.commendPenalizeConfiguration.getWorkflowTypeNormal();
        // 奖励类型
        final List<OptionForm> listByCategory = this.openService.getOptionListByCode(Constant.REWARD_CATEGORY.toString());
        final OptionForm optionByCategory = this.openService.getOptionByList(listByCategory, entity.getCategory());
        // 奖励级别
        final List<OptionForm> listByLevel = this.openService.getOptionListByCode(Constant.REWARD_LEVEL.toString());
        final OptionForm optionByLevel = this.openService.getOptionByList(listByLevel, entity.getLevel());
        // 奖励名称
        final String name = this.getRewardName(entity);
        final String title = entity.getOrgName() + "添加[" + name + "]奖励";
        // 审核body
        final CommendPenalizeApprovalForm approvalForm = new CommendPenalizeApprovalForm(CommendPenalizeApprovalForm.ORG, entity.getOrgName(), optionByCategory.getOpValue(), optionByLevel.getOpValue(),name);
        final ApprovalBase approvalBase = this.workflowService.buildApprovalBase(workflowId, entity.getOrgId(), title, entity.getMeetingOrgCommendPenalizeId(), workflowType, JsonUtils.toJson(approvalForm), this.headerService.bulidHeader(headers));
        final long workflowTaskId = this.workflowService.addApproval(approvalBase, headers);
        if (workflowTaskId == -1) {
            throw new ApiException("新建审批流程失败 ", new Result<>(errors, 2009, HttpStatus.BAD_REQUEST.value(), "新建审批流程失败"));
        }
        return workflowTaskId;
    }

    /**
     * 操作组织积分
     * @param entity
     * @param operType 0：新增，1：扣分
     * @param headers
     */
    private void operateOrgCredit(MeetingOrgCommendPenalizeEntity entity, int operType, HttpHeaders headers) {
        final List<CommendPenalizeConfiguration.Score> score = this.commendPenalizeConfiguration.getOrgScoreList();
        final Optional<CommendPenalizeConfiguration.Score> scoreOptional =
                score.stream().filter(s -> s.getKey().equals(entity.getLevel())).findFirst();
        if (scoreOptional.isPresent()) {
            final Integer score1 = scoreOptional.get().getScore();
            // 备注
            String explainTxt;
            if (entity.getType().equals(Constant.REWARD)) {
                // 级别
                final List<OptionForm> optionListByLevel = this.openService.getOptionListByCode(Constant.REWARD_LEVEL.toString());
                final OptionForm optionByReward = this.openService.getOptionByList(optionListByLevel, entity.getLevel());
                // 名称
                explainTxt = optionByReward.getOpValue() + this.getRewardName(entity);
            } else {
                // 名称
                final List<OptionForm> optionListByName = this.openService.getOptionListByCode(Constant.REWARD_NAME_ORG_NEW.toString());
                final OptionForm optionByName = this.openService.getOptionByList(optionListByName, entity.getName());
                explainTxt = optionByName.getOpValue();
            }
            this.openService.operateOrgCredit(entity.getOrgId(), operType, ScoreTypeNum.PIONEER_BEST.getType(), score1, explainTxt, headers);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
            Integer dataMonth = Integer.valueOf(dateFormat.format(new Date()));
            List<IndexOrgScoreForm> dataList = new ArrayList();
            Long dorisScore = Long.valueOf(score1);
            Integer ruleId = operType==1? DorisScoreConstant.MEETING_COMMEND_BAD_ORG: DorisScoreConstant.MEETING_COMMEND_GOOD_ORG;
            if(operType==1){
                if(score1>0){
                    dorisScore = Long.valueOf(score1*(-1));
                }
            }
            IndexOrgScoreForm orgScoreForm = new IndexOrgScoreForm(ruleId,dataMonth,entity.getOrgId(),1,dorisScore);
            dataList.add(orgScoreForm);
            if(CollectionUtils.isNotEmpty(dataList)){
                thirdService.addDorisOrgScore(null,dataList);
            }
        }
    }

    public void workflowCallBack(long meetingOrgCommendPenalizeId, long workflowTaskId, long userId,
                                 int status, String reason, Long reUid, HttpHeaders headers) {
        log.debug("组织奖惩审批回调入参：meetingId={}, status={}, userId={}, workflowTaskId={}, reUid={}, reason={} ",
                meetingOrgCommendPenalizeId, status, userId, workflowTaskId, reUid, reason);
        final MeetingOrgCommendPenalizeEntity entity = this.meetingOrgCommendPenalizeMapper.selectByPrimaryKey(meetingOrgCommendPenalizeId);
        if (!entity.getWorkflowTaskId().equals(workflowTaskId)) {
            log.error("审批流程回调taskId[{}], 与数据taskId[{}]， 不一致", workflowTaskId, entity.getWorkflowTaskId());
            return;
        }
        entity.setLastChangeUser(userId);
        entity.setUpdateTime(new Date());
        if (status == 1) {
            // 审核通过
            entity.setApprovalStatus(CommendPenalizeStatusEnum.PASS.getKey());
            if (entity.getType().equals(Constant.REWARD)) {
                final List<MeetingFileVO> fileVOS = this.meetingFileMapper.selectByLinkedId(entity.getMeetingOrgCommendPenalizeId(), FileSourceEnum.MEMBER_COMMEND_PENALIZE_PIC);
                // 调用新增组织风采或轨迹
                this.openService.addOrgHighlight(entity, fileVOS, 1, headers);
                this.openService.addOrgHighlight(entity, fileVOS, 2, headers);
                // 调用积分
                this.operateOrgCredit(entity, 0, headers);
            }
        } else {
            // 审核不通过
            entity.setApprovalStatus(CommendPenalizeStatusEnum.NO_PASS.getKey());
        }
        this.meetingOrgCommendPenalizeMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 当前函数为不走工作流审批，默认通过，增加积分和组织风采以及轨迹
     *
     */
    private void workflowSkipCallBack(MeetingOrgCommendPenalizeEntity entity,HttpHeaders headers){
        final List<MeetingFileVO> fileVOS = this.meetingFileMapper.selectByLinkedId(entity.getMeetingOrgCommendPenalizeId(), FileSourceEnum.MEMBER_COMMEND_PENALIZE_PIC);
        // 调用新增组织风采或轨迹
        this.openService.addOrgHighlight(entity, fileVOS, 1, headers);
        this.openService.addOrgHighlight(entity, fileVOS, 2, headers);
        // 调用积分
        this.operateOrgCredit(entity, 0, headers);
    }

    private String getRewardName(MeetingOrgCommendPenalizeEntity entity) {
        String name = "";
        if (Constant.REWARD_NAME_ORG_OTHER.equals(entity.getName())) {
            name = StringUtils.isNotBlank(entity.getContent()) ? entity.getContent() : "";
        } else {
            final List<OptionForm> nameOption = this.openService.getOptionListByCode(Constant.REWARD_NAME_ORG_NEW.toString());
            final OptionForm optionByName = this.openService.getOptionByList(nameOption, entity.getName());
            if (Objects.nonNull(optionByName)) {
                name = optionByName.getOpValue();
            }
        }
        return name;
    }

    public CommendStatisticsForm getOrgCommendStatistics(Long orgId) {
        CommendStatisticsForm form = new CommendStatisticsForm();
        final Example example = new Example(MeetingOrgCommendPenalizeEntity.class);
        example.orderBy("level").asc().orderBy("ratifyTime").desc();
        final Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", orgId);
        criteria.andEqualTo("type", Constant.REWARD);
        criteria.andEqualTo("status", Constant.YES);
        criteria.andEqualTo("approvalStatus", CommendPenalizeStatusEnum.PASS.getKey());
        final List<MeetingOrgCommendPenalizeEntity> list = meetingOrgCommendPenalizeMapper.selectByExample(example);
        form.setCommendNum(list.size());
        if (!list.isEmpty()) {
            StringBuilder sb = new StringBuilder("在这一天获得了");
            final MeetingOrgCommendPenalizeEntity entity = list.get(0);
            // 奖励级别
            final List<OptionForm> optionListByLevel = this.openService.getOptionListByCode(Constant.REWARD_LEVEL.toString());
            final OptionForm optionByReward = this.openService.getOptionByList(optionListByLevel, entity.getLevel());
            // 奖励名称
            final String name = this.getRewardName(entity);
            sb.append(optionByReward.getOpValue()).append("的“").append(name).append("”称号呢~");
            form.setContent(sb.toString());
            if (Objects.nonNull(entity.getRatifyTime())) {
                final SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
                form.setDate(sdf.format(entity.getRatifyTime()));
            }
        }
        return form;
    }
}
