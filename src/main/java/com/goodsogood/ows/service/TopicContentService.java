package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.TopicContentMapper;
import com.goodsogood.ows.model.db.TopicContentEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-22 14:10
 **/
@Service
@Log4j2
public class TopicContentService {

    private final TopicContentMapper topicContentMapper;

    @Autowired
    public TopicContentService(TopicContentMapper topicContentMapper) {
        this.topicContentMapper = topicContentMapper;
    }


    /**
     * 根据任务返回该任务的所有内容
     * @param topicId
     * @return
     */
    public List<TopicContentEntity> getTopicContentList(Long topicId) {
        if(topicId == null || topicId == 0) {
            return new ArrayList<>();
        }
        Example example = new Example(TopicContentEntity.class);
        example.createCriteria().andEqualTo("topicId", topicId);
        return this.topicContentMapper.selectByExample(example);
    }

}