package com.goodsogood.ows.service;

import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.mapper.LifeAdviceMapper;
import com.goodsogood.ows.mapper.LifeCheckMapper;
import com.goodsogood.ows.mapper.LifeFileMapper;
import com.goodsogood.ows.mapper.LifeMapper;
import com.goodsogood.ows.model.db.LifeAdviceEntity;
import com.goodsogood.ows.model.db.LifeCheckEntity;
import com.goodsogood.ows.model.db.LifeEntity;
import com.goodsogood.ows.model.db.LifeFileEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Log4j2
public class LifeAsyncService {

    private final LifeMapper lifeMapper;
    private final LifeFileMapper lifeFileMapper;
    private final LifeCheckMapper lifeCheckMapper;
    private final LifeAdviceMapper lifeAdviceMapper;
    private final LifeFileService lifeFileService;
    private final MeetingTalkService meetingTalkService;

    public LifeAsyncService(LifeMapper lifeMapper,
                            LifeFileMapper lifeFileMapper,
                            LifeCheckMapper lifeCheckMapper,
                            LifeAdviceMapper lifeAdviceMapper,
                            LifeFileService lifeFileService,
                            MeetingTalkService meetingTalkService) {
        this.lifeMapper = lifeMapper;
        this.lifeFileMapper = lifeFileMapper;
        this.lifeCheckMapper = lifeCheckMapper;
        this.lifeAdviceMapper = lifeAdviceMapper;
        this.lifeFileService = lifeFileService;
        this.meetingTalkService = meetingTalkService;
    }

    /**
     * 批量更新修改时间/人
     * @param lifeIds
     * @param userId
     * @param time
     */
    @Async("lifeAsync")
    public void multiUpdateId(List<Long> lifeIds, Long userId, LocalDateTime time){
        lifeMapper.multiUpdate(lifeIds,userId,time);
    }

    /**
     * 批量更新修改时间/人
     * @param life
     * @param userId
     * @param time
     */
    @Async("lifeAsync")
    public void multiUpdateEntity(List<LifeEntity> life, Long userId, LocalDateTime time){
        if (!CollectionUtils.isEmpty(life)){
            List<Long> ids = life.stream().map(LifeEntity::getLifeId).collect(Collectors.toList());
            this.multiUpdateId(ids,userId,time);
        }
    }

    /**
     * 拷贝只上传文件的模块
     * @param lifeId
     * @param time
     */
    @Async("lifeAsync")
    public void copyFile(Long lifeId,LocalDateTime time){
        try {
            log.debug("lifeId -> [{}]，拷贝 【上传会议方案】【会前学习】【检视剖析材料（领导班子）】【上传通报材料】【会前其他材料】",lifeId);
            Example example = fileExample(
                    lifeId,
                    Arrays.asList(
                            Constant.ModelType.PLAN.getModelId(), Constant.ModelType.PRE_STUDY.getModelId(),
                            Constant.ModelType.CHECK_LEADER.getModelId(),Constant.ModelType.NOTICE_DATA.getModelId(),
                            Constant.ModelType.PRE_OTHER.getModelId())
            );
            List<LifeFileEntity> list = lifeFileMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(list)){
                list.forEach(x -> {
                    x.setLifeFileId(null);
                    x.setStep(2);
                    x.setCreateTime(time);
                    x.setUpdateTime(time);
                });
                lifeFileMapper.insertList(list);
            }
        }catch (Exception e){
            log.error("lifeId -> [{}]，拷贝 【上传会议方案】【会前学习】【检视剖析材料（领导班子）】【上传通报材料】【会前其他材料】失败",lifeId,e);
        }

    }

    /**
     * 拷贝组织学习附件
     * @param lifeId
     */
    @Async("lifeAsync")
    public void copyOrgStudyFile(Long lifeId){
        log.debug("lifeId -> [{}]，拷贝 【组织学习】",lifeId);
        lifeFileService.cloneMeetingToLife(lifeId);
    }

    /**
     * 拷贝检视剖析
     * @param lifeId
     * @param time
     */
    @Async("lifeAsync")
    @Transactional(rollbackFor = Exception.class)
    public void copyCheck(Long lifeId,LocalDateTime time){
        log.debug("lifeId -> [{}]，拷贝 【个人检视剖析】",lifeId);
        //查询个人检视剖析记录
        Example example = new Example(LifeCheckEntity.class);
        copyExample(lifeId,example);
        List<LifeCheckEntity> checkEntities = lifeCheckMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(checkEntities)) return;
        //查询相关附件
        example = fileExample(
                lifeId,
                Arrays.asList(
                        Constant.ModelType.CHECK_SELF_ORIGIN.getModelId(),
                        Constant.ModelType.CHECK_SELF_SIGN.getModelId())
        );
        List<LifeFileEntity> fileEntities = lifeFileMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(fileEntities)){ //没有附件
            checkEntities.forEach(x -> {
                x.setCheckId(null);
                x.setStep(2);
                x.setCreateTime(time);
                x.setUpdateTime(time);
            });
            lifeCheckMapper.insertList(checkEntities);
        }else { //有附件
            for (LifeCheckEntity entity : checkEntities){
                long oldId = entity.getCheckId();
                entity.setCheckId(null);
                entity.setStep(2);
                entity.setCreateTime(time);
                entity.setUpdateTime(time);
                lifeCheckMapper.insertUseGeneratedKeys(entity);
                fileEntities.forEach(x -> {
                    if (x.getDataId().equals(oldId)){
                        x.setDataId(entity.getCheckId());
                        x.setLifeFileId(null);
                        x.setStep(2);
                        x.setCreateTime(time);
                        x.setUpdateTime(time);
                    }
                });
            }
            lifeFileMapper.insertList(fileEntities);
        }
    }

    /**
     * 拷贝征求意见-直接上传
     * @param lifeId
     * @param time
     */
    @Async("lifeAsync")
    @Transactional(rollbackFor = Exception.class)
    public void copyAdviceDirect(Long lifeId,LocalDateTime time){
        log.debug("lifeId -> [{}]，拷贝 【征求意见-直接上传】",lifeId);
        //查询征求意见
        Example example = new Example(LifeAdviceEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId",lifeId)
                .andEqualTo("adviceType",1)
                .andEqualTo("step",1)
                .andNotEqualTo("isDel",1);
        List<LifeAdviceEntity> adviceEntities = lifeAdviceMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(adviceEntities)) return;
        //查询相关附件
        example = fileExample(
                lifeId,
                Collections.singletonList(Constant.ModelType.ADVICE_DIRECT.getModelId())
        );
        List<LifeFileEntity> fileEntities = lifeFileMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(fileEntities)){ //没有附件
            adviceEntities.forEach(x -> {
                x.setAdviceId(null);
                x.setStep(2);
                x.setCreateTime(time);
                x.setUpdateTime(time);
            });
            lifeAdviceMapper.insertList(adviceEntities);
        }else { //有附件
            for (LifeAdviceEntity entity : adviceEntities){
                long oldId = entity.getAdviceId();
                entity.setAdviceId(null);
                entity.setStep(2);
                entity.setCreateTime(time);
                entity.setUpdateTime(time);
                lifeAdviceMapper.insertUseGeneratedKeys(entity);
                fileEntities.forEach(x -> {
                    if (x.getDataId().equals(oldId)){
                        x.setDataId(entity.getAdviceId());
                        x.setLifeFileId(null);
                        x.setStep(2);
                        x.setCreateTime(time);
                        x.setUpdateTime(time);
                    }
                });
            }
            lifeFileMapper.insertList(fileEntities);
        }
    }

    /**
     * 拷贝征求意见-其他
     * @param lifeId
     * @param time
     */
    @Async("lifeAsync")
    public void copyAdviceOther(Long lifeId,LocalDateTime time){
        log.debug("lifeId -> [{}]，拷贝 【征求意见-问卷、座谈会",lifeId);
        //查询征求意见
        Example example = new Example(LifeAdviceEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId",lifeId)
                .andNotIn("adviceType",Arrays.asList(1,4))
                .andEqualTo("step",1)
                .andNotEqualTo("isDel",1);
        List<LifeAdviceEntity> adviceEntities = lifeAdviceMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(adviceEntities)) {
            adviceEntities.forEach(x -> {
                x.setAdviceId(null);
                x.setStep(2);
                x.setCreateTime(time);
                x.setUpdateTime(time);
            });
            lifeAdviceMapper.insertList(adviceEntities);
        }
    }

    /**
     * 拷贝谈心谈话
     * @param headers
     * @param lifeId
     * @param oid
     */
    @Async("lifeAsync")
    public void copyTalk(HttpHeaders headers, Long lifeId, Long oid){
        log.debug("lifeId -> [{}]，拷贝 【谈心谈话】",lifeId);
        meetingTalkService.generaTalkData(oid,2,lifeId,headers);
    }

    /**
     * 拷贝example
     * @param lifeId
     * @param example
     * @return
     */
    private void copyExample(Long lifeId,Example example){
        example.createCriteria()
                .andEqualTo("lifeId",lifeId)
                .andEqualTo("step",1)
                .andNotEqualTo("isDel",1);
    }

    /**
     * 附件查询example
     * @param lifeId
     * @param modelType
     * @return
     */
    private Example fileExample(Long lifeId,List<Integer> modelType){
        Example example = new Example(LifeFileEntity.class);
        example.createCriteria()
                .andEqualTo("lifeId",lifeId)
                .andEqualTo("step",1)
                .andNotEqualTo("isDel",1)
                .andIn("type", modelType);
        return example;
    }
}
