package com.goodsogood.ows.service;

import com.goodsogood.ows.common.RedisConstant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.MeetingPlanEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.utils.RedisLockUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @create 2018-10-24 09:42
 */
@Service
@Log4j2
public class MeetingTaskServiceAsync {
    private final StringRedisTemplate stringRedisTemplate;
    private final MeetingTaskServiceHandle meetingTaskServiceHelper;
    private final Errors errors;

    public MeetingTaskServiceAsync(
            StringRedisTemplate stringRedisTemplate,
            MeetingTaskServiceHandle meetingTaskServiceHelper, Errors errors) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.meetingTaskServiceHelper = meetingTaskServiceHelper;
        this.errors = errors;
    }

    /**
     * @param updateExcuteOrgs 是否需要更新执行组织
     */
    void createThisMeetingTask(HeaderHelper.SysHeader sysHeader, MeetingPlanEntity meetingPlanEntity, Date now, boolean updateExcuteOrgs) {
        this.createThisMeetingTask(sysHeader, meetingPlanEntity, now, updateExcuteOrgs, false);
    }

    /**
     * 派发任务，添加分布式锁 防止任务重复派发
     *
     * @param updateExcuteOrgs   是否需要更新执行组织
     * @param isExecuteScheduled 是否是定时派发任务调用 true 是
     */
    void createThisMeetingTask(HeaderHelper.SysHeader sysHeader, MeetingPlanEntity meetingPlanEntity, Date now, boolean updateExcuteOrgs, boolean isExecuteScheduled) {
        // 添加分布式锁 防止重复派发任务
        // 获取锁 防止分布式部署情况下冲突
        // 1.获取锁 10分钟
//        String uuid = UUID.randomUUID().toString();
        String key = "LOCK_" + RedisConstant.MEETING_CREATE_TASK + meetingPlanEntity.getMeetingPlanId();
        long time = System.currentTimeMillis();
        // 获取redis分布式锁
        while (true) {
            boolean lock =
                    RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, key, "LOCK_" + RedisConstant.MEETING_CREATE_TASK, 10 * 60 * 1000);
            if (lock) {
                try {
                    meetingTaskServiceHelper.createThisMeetingTask(sysHeader, meetingPlanEntity, now, updateExcuteOrgs);
                    break;
                } finally {
                    // 释放锁
                    RedisLockUtil.releaseDistributedLock(stringRedisTemplate, key, "LOCK_" + RedisConstant.MEETING_CREATE_TASK);
                }
            } else {
                try {
                    // 非定时任务未获取到锁，直接返回
                    // 定时任务获取锁时间为10分钟
                    if (!isExecuteScheduled || System.currentTimeMillis() - time > 10 * 60 * 1000) {
                        // 请求超时 9902
                        throw new ApiException("派发任务获取锁超时！", new Result<>(errors, 9902, HttpStatus.REQUEST_TIMEOUT.value()));
                    }
                    Thread.sleep(33);
                } catch (InterruptedException ignored) {
                }
            }
        }
    }
}
