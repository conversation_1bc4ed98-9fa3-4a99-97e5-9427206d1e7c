//package com.goodsogood.ows.service;
//
//import com.goodsogood.ows.model.vo.TobaccoAddTaskFrom;
//import com.goodsogood.ows.model.vo.TobaccoTaskHandleForm;
//import lombok.extern.log4j.Log4j2;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * 烟草任务填报/审核service
// * <AUTHOR>
// * @date 2021.08.24
// */
//@Service
//@Log4j2
//public class TobaccoTaskService {
//
//    /**
//     * 发布任务
//     */
//    public String addTask(){
//
//        return "发布成功";
//    }
//
//    /**
//     * 判断用户发送的数据真实性
//     */
//    public void checkTask(){
//
//    }
//
//    /**
//     * 判断开始时间 ~ 结束时间 是否在范围内
//     */
//    public void checkTime(){
//
//    }
//
//    /**
//     * 判断用户上传文件
//     */
//    public String checkFile(List<TobaccoTaskHandleForm.HandleFile> files){
//        return null;
//    }
//
//
//}
