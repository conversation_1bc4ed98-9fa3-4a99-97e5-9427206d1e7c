package com.goodsogood.ows.service;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.pojo.MeetingPushParam;
import com.goodsogood.ows.configuration.NoticeTypes;
import com.goodsogood.ows.configuration.OrgTypeConfig;
import com.goodsogood.ows.configuration.SchedulerConfiguration;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.mapper.MeetingTaskMapper;
import com.goodsogood.ows.model.vo.SasOrgLifeConditionForm;
import com.goodsogood.ows.model.vo.SasOrgLifeReponse;
import com.goodsogood.ows.model.vo.SendMsgForm;
import com.goodsogood.ows.model.vo.StatisticalOrgLifeEntity;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-05-10 14:39
 * @since 1.0.3
 **/
@Service
@Log4j2
public class MeetingNoticeSupportService {

    private final SchedulerConfiguration schedulerConfiguration;
    private final OpenService openService;
    private final RegionService regionService;
    private final MeetingTaskMapper meetingTaskMapper;
    private final UserCenterService userCenterService;
    private final OrgTypeConfig orgTypeConfig;

    public final static int PUSH_CURRENT = 1;
    public final static int PUSH_TOPIC = 2;
    public final static int PUSH_ALL = 3;


    //当前推送的model
    private final ThreadLocal<MeetingPushParam> pushModel = new ThreadLocal<>();

    @Autowired
    public MeetingNoticeSupportService(SchedulerConfiguration schedulerConfiguration, OpenService openService, RegionService regionService, MeetingTaskMapper meetingTaskMapper, UserCenterService userCenterService, OrgTypeConfig orgTypeConfig) {
        this.schedulerConfiguration = schedulerConfiguration;
        this.openService = openService;
        this.regionService = regionService;
        this.meetingTaskMapper = meetingTaskMapper;
        this.userCenterService = userCenterService;
        this.orgTypeConfig = orgTypeConfig;
    }

    /**
     * 发送通知的公共方法
     *
     * @param ssLog 日志横切对象
     * @param type  需要发送通知的配置
     */
    @Async
    public void sendNotice(HeaderHelper.SysHeader header, final LogAspectHelper.SSLog ssLog, NoticeTypes type,
                           MeetingPushParam pushParam, Long regionId, List<Long> groupIds,List<Long> orgIds) {
        LogHelper.asyncLog(ssLog, () -> {
            this.pushModel.set(pushParam);
            log.debug("发送通知：name = {}, type = {}", type.getName(), type);
            Calendar cale = Calendar.getInstance();
            int month = cale.get(Calendar.MONTH) + 1;
            int startMonth = month;
            //如果是季度，则校验当前月是否季度最后一个月
            if (type.getQuarter() == 1 && month % 3 != 0) {
                log.debug("定时发送通知：{}，处理月份month = {}", type.getName(), month);
                log.debug("定时发送通知：{}，不是季度最后月份，退出发送通知任务", type.getName());
                return;
            }
            //如果是季度则重置开始月份
            if (type.getQuarter() == 1 && (month % 3 == 0)) {
                startMonth = (month - 2);
            }
            log.debug("定时发送通知：{}，处理月份month = {}", type.getName(), month);

            // 市局下的支部跳转地址不一样

            /**
             * 从t_meeting_task中获取未完成的组织
             * 2021-11-23  tc
             */
            //计算查询时间为当前日期
            String queryDate = DateUtils.dateFormat(new Date(), "yyyy-MM-dd");
            List<Long> sendOrgId = meetingTaskMapper.findUnfinishedTaskOid(type.getId(), queryDate);
            /* 只要他是烟草市局的党小组,党小组会就不发 */
            // 市局的党小组会不发消息
            Integer TYPE_ID = 3;
            if (null != type.getId() && type.getId().equals(TYPE_ID)) {
                if (!CollectionUtils.isEmpty(groupIds)) {
                    sendOrgId.removeAll(groupIds);
                }
            }

            // 在排除市局下的支部 因为市局下的支部要单独发
            List<Long> szfOrgId = new ArrayList<>();
            sendOrgId.forEach(x -> {
                if (orgIds.contains(x)) {
                    szfOrgId.add(x);
                }
            });
            sendOrgId.removeAll(szfOrgId);
            log.debug("发送通知的公共方法二>>>>>>>>>>>>>{},{},{},{},{},{}", type, type.getName(), orgIds, groupIds, szfOrgId, sendOrgId);
            //跳转地址
            String targetUrl = null;
            if (!ListUtils.isEmpty(sendOrgId)) {
                log.debug("跳转断点一>{},{}", sendOrgId, schedulerConfiguration.getRedirectUrlAdd());
                           /*//支委会
               List<Long> data = new ArrayList<>();
               if (type.getBiz().equals("OrgPeriodMember")) {
                    data = dataPeriodGroup.get(OpenService.ORG_PERIOD);
               }
               //党小组
               if (type.getBiz().equals("OrgGroupMeeting")) {
                    data = dataPeriodGroup.get(OpenService.ORG_GROUP);
               }*/
                try {
                    if (!StringUtils.isEmpty(schedulerConfiguration.getPrefix()) && !StringUtils.isEmpty(schedulerConfiguration.getRedirectUrlAdd())) {
                        targetUrl = schedulerConfiguration.getPrefix() + URLEncoder.encode(schedulerConfiguration.getRedirectUrlAdd(), "UTF-8");
                    }
                } catch (Exception e) {
                    log.error("<发送通知> 组合跳转地址报错！", e);
                }
                //发送通知
                this.send(startMonth,
                        month,
                        type.getMsgTemplateId(),
                        type.getMsgChannelType(),
                        regionId,
                        targetUrl,
                        sendOrgId);
            } else {
                log.debug("定时发送通知：没有未完成组织，退出发送通知");
            }


            if (!ListUtils.isEmpty(szfOrgId)) {
                // 发送市值同步的url
                String szfTargetUrl = null;
                log.debug("跳转断点二>{},{}", sendOrgId, schedulerConfiguration.getSzfRedirectUrlAdd());
                try {
                    if (!StringUtils.isEmpty(schedulerConfiguration.getPrefix()) && !StringUtils.isEmpty(schedulerConfiguration.getSzfRedirectUrlAdd())) {
                        szfTargetUrl = schedulerConfiguration.getPrefix() + URLEncoder.encode(schedulerConfiguration.getSzfRedirectUrlAdd(), "UTF-8");
                    }
                } catch (Exception e) {
                    log.error("<发送通知> 组合跳转地址报错！", e);
                }
                //发送通知
                this.send(startMonth,
                        month,
                        type.getMsgTemplateId(),
                        type.getMsgChannelType(),
                        regionId,
                        szfTargetUrl,
                        szfOrgId);
            }
        });
    }

    /**
     * 构建查询条件
     *
     * @param pageNo     查询页码
     * @param startMonth 开始月份
     * @param endMonth   结束月份
     * @return
     */
    private SasOrgLifeConditionForm bulidCondition(Long regoinId, int pageNo, int startMonth, int endMonth, int activityTypeId) {
        Calendar cale = Calendar.getInstance();
        int year = cale.get(Calendar.YEAR);
        return SasOrgLifeConditionForm.builder()
                .activityTypeId(activityTypeId)
                .orgId(regionService.bindingOrgId(regoinId))
                .pageNo(pageNo)
                .pageSize(this.schedulerConfiguration.getPageSize())//获取数据分页大小
                .year(year)
                .timeType(4)
                .startMonth(1)//兼容v1.0.4考核二期之前，党务看板没有统计当月数据的问题，所以把起始月份扩大
                .endMonth(endMonth)
                .orgTypeIdFlag(true)//查询sas的统计数据，根据设置的组织类型进行过滤
                .build();
    }

    /**
     * 远程获取组织开会统计数据
     *
     * @param quarter    true: 是否是查询季度
     * @param startMonth 开始月
     * @param endMonth   结束月
     * @return
     */
    public List<Long> getRemoteOrgIds(Long regoinId, boolean quarter, int startMonth, int endMonth, int activityTypeId) {
        List<Long> list = new ArrayList<>();
        int pageNo = 1;
        while (true) {
            // 远程调用sas的统计接口
            SasOrgLifeReponse rs =
                    this.openService.getSasCount(
                            regoinId, this.bulidCondition(regoinId, pageNo, startMonth, endMonth, activityTypeId));
            if (rs == null || rs.getResultPage().getData().size() == 0) {
                break;
            }
            //处理数据
            rs.getResultPage().getData().forEach(reponse -> {
                try {
                    Class clazz = StatisticalOrgLifeEntity.class;
                    Method mOrg = clazz.getMethod("getOrgId");
                    Object org = mOrg.invoke(reponse);
                    long orgId = org == null ? 0 : (Long) mOrg.invoke(reponse);
                    if (quarter) {
                        Method m1 = clazz.getMethod("getMonth" + startMonth);
                        Method m2 = clazz.getMethod("getMonth" + (startMonth + 1));
                        Method m3 = clazz.getMethod("getMonth" + endMonth);
                        Object o1 = m1.invoke(reponse);
                        Object o2 = m2.invoke(reponse);
                        Object o3 = m3.invoke(reponse);
                        long count1 = o1 == null ? 0 : ((Integer) m1.invoke(reponse)).longValue();
                        long count2 = o2 == null ? 0 : ((Integer) m2.invoke(reponse)).longValue();
                        long count3 = o3 == null ? 0 : ((Integer) m3.invoke(reponse)).longValue();
                        //开会次数
                        if ((count1 + count2 + count3) == 0) {
                            list.add(orgId);
                        }
                    } else {
                        Method m = clazz.getMethod("getMonth" + startMonth);
                        Object o = m.invoke(reponse);
                        long count = o == null ? 0 : ((Integer) m.invoke(reponse)).longValue();
                        //开会次数
                        if (count == 0) {
                            list.add(orgId);
                        }
                    }
                } catch (Exception e) {
                    log.debug("解析sas接口出错：" + e.getMessage(), e);
                }
            });
            pageNo++;
        }
        return list;
    }

    /**
     * 发送通知
     *
     * @param startMonth  开始月份
     * @param endMonth    结束月份
     * @param templateId  通知模板的id
     * @param channelType 发送通知的渠道 渠道类型 1:短信 2:微信
     * @param regionId    区县编号
     * @param targetUrl   跳转地址
     */
    private void send(int startMonth, int endMonth, int templateId,
                      int channelType, Long regionId, String targetUrl, List<Long> sendOrgId) {
        log.debug("发送通知：channelType ={} startMonth = {}， endMonth = {} sendOrgId={}", channelType, startMonth, endMonth, sendOrgId);

        /**
         * 取消考核组织/管理组织的限制，并改为从t_meeting_task中获取未完成的组织
         * 2021-11-23  tc
         */
          /*
          boolean flag = false;
          //默认为查询单月，如果起始月份不相同，则认为是季度
          if (startMonth != endMonth) {
               flag = true;
          }
          //通过sas接口返回没有完成任务的党组织
          List<Long> sasOrgIds = this.getRemoteOrgIds(regionId, flag, startMonth, endMonth, type);
          log.debug("考核单位合并前checkOrgIdList 长度： {}", checkOrgIdList.size());
          log.debug("考核单位合并前checkOrgIdList = {}", checkOrgIdList);
          log.debug("sas统计返回的数据,orgIds = {}", sasOrgIds);
          //考核单位的所有组织与sas返回统计数据等于0的单位求交集,用交集处理发送，主要是避免统计出错导致发送通知出错
          checkOrgIdList.retainAll(sasOrgIds);
          //第一次交集
          List<Long> checkOrgIdListRetain1 = new ArrayList<>();
          checkOrgIdListRetain1.addAll(checkOrgIdList);
          if(ListUtils.isEmpty(checkOrgIdList)) {
               log.debug("发送通知：考核单位与sas统计返回的数据开会数等于0，无交集，退出发送通知");
               return;
          }
          log.debug("考核组织和sas统计返回数据，交集后的数据，checkOrgIdListRetain1 = {}", checkOrgIdListRetain1);
          //考核组织 与 sas统计返回的组织id求交集后的数据，再与其他条件必须满足的求交集
          if(!ListUtils.isEmpty(conditionDataList)) {
               log.debug("需要满足的条件集合，conditionDataList = {}", conditionDataList);
               checkOrgIdList.retainAll(conditionDataList);
               log.debug("考核组织和sas统计返回数据，交集后的数据再与满足条件再次交集后的数据，checkOrgIdList = {}", checkOrgIdList);
          }*/

        //获取这些组织的所有管理员
        List<SendMsgForm> admins = this.openService.getAdmin(regionId, sendOrgId, targetUrl);
        //获取这些组织的支部书记
//          List<SendMsgForm> leaders = this.openService.getLeader(regionId, sendOrgId,targetUrl);

        //合并管理员集合和书记集合，并去重。
          /*List<SendMsgForm> list = new ArrayList<>();
          Set<Long> userIdSet = new HashSet<>();
          admins.stream().forEach(a->{
               if(userIdSet.add(a.userId)){
                    list.add(a);
               }
          });
          leaders.stream().forEach(a->{
               if(userIdSet.add(a.userId)){
                    list.add(a);
               }
          });*/
        log.debug("接收通知的管理员和书记： list = {}", admins);
        //发送通知
        this.openService.sendNotice(admins, templateId, channelType, this.pushModel.get(), regionId);
    }


}
