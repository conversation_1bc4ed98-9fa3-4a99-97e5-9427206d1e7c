package com.goodsogood.ows.service;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.IndexForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 首页汇总
 *
 * <AUTHOR>
 * @create 2018/10/31 13:50
 */
@Service
@Log4j2
public class IndexService {

    private final MeetingLeaveRedisService meetingLeaveRedisService;
    private final MeetingRedisService meetingRedisService;
    private final MeetingTaskRedisService meetingTaskRedisService;
    private final TopicOrgRedisService topicOrgRedisService;
    private final MyMeetingService myMeetingService;
    private final IndexServiceAsync indexServiceAsync;
    private final MeetingConfig meetingConfig;

    @Autowired
    public IndexService(
            MeetingLeaveRedisService meetingLeaveRedisService,
            MeetingRedisService meetingRedisService,
            MeetingTaskRedisService meetingTaskRedisService,
            TopicOrgRedisService topicOrgRedisService,
            MyMeetingService myMeetingService,
            IndexServiceAsync indexServiceAsync, MeetingConfig meetingConfig) {
        this.meetingLeaveRedisService = meetingLeaveRedisService;
        this.meetingRedisService = meetingRedisService;
        this.meetingTaskRedisService = meetingTaskRedisService;
        this.topicOrgRedisService = topicOrgRedisService;
        this.myMeetingService = myMeetingService;
        this.indexServiceAsync = indexServiceAsync;
        this.meetingConfig = meetingConfig;
    }


    public void collectRedis(LogAspectHelper.SSLog ssLog) {
        try {
            indexServiceAsync.collectRedis(ssLog);
        } catch (Exception e) {
            // e.getMessage() 为 null,为线程池队列已满，添加到队列失败。
            log.warn("全量刷首页汇总（移动端）异常！" + e.getMessage(), e);
        }
    }

    /**
     * 全量刷新首页汇总（移动端） 单线程
     */
    public void collectRedis() {
        if (meetingConfig.isRefreshIndexCollectRedis()) {
            // 刷新缓存 不继承上层接口的日志上下文
            collectRedis(null);
        } else {
            log.info("首页汇总缓存关闭！");
        }
    }

    /**
     * 获取汇总信息
     *
     * @version 2019年11月4日 14:55:13
     */
    public IndexForm collect(HeaderHelper.SysHeader sysHeader) {
        // 待审批请假数量
        IndexForm indexForm = new IndexForm();
        indexForm.setLeaveCount(meetingLeaveRedisService.getWaitApprovalCountByRedis(sysHeader.getRegionId(), sysHeader.getUserId()));

        //活动管理
        indexForm.setMeetingCount(meetingRedisService.meetingH5CountByRedis(sysHeader.getOid()));

        //任务管理
        // 活动未完成总数，不包含逾期任务
        int mtc = meetingTaskRedisService.undoneCountByRedis(sysHeader.getOid());
        // 任务未完成总数，不包含逾期任务
        int ptc = topicOrgRedisService.undoneCountByRedis(sysHeader.getOid());
        indexForm.setTaskCount(mtc + ptc);

        //我的活动
        indexForm.setMyMeetingCount(myMeetingService.waitDoMeetingCountByRedis(sysHeader.getRegionId(), sysHeader.getUserId()));
        return indexForm;
    }


}
