package com.goodsogood.ows.service;

import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.SbwHandleMapper;
import com.goodsogood.ows.mapper.SbwTaskOrgMapper;
import com.goodsogood.ows.model.db.SbwHandleEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SbwHandleForm;
import com.goodsogood.ows.model.vo.SbwTaskListForm;
import com.goodsogood.ows.model.vo.SbwTaskOrgListForm;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 南岸区网信办任务审核
 * <AUTHOR>
 * @date 2021.07.30
 */
@Service
@Log4j2
public class SbwVerifyTaskService {

    private final SbwHandleMapper sbwHandleMapper;
    private final SbwTaskOrgMapper sbwTaskOrgMapper;
    private final SbwAsyncService sbwAsyncService;
    private final Errors errors;

    @Autowired
    public SbwVerifyTaskService(SbwHandleMapper sbwHandleMapper, SbwTaskOrgMapper sbwTaskOrgMapper, SbwAsyncService sbwAsyncService, Errors errors) {
        this.sbwHandleMapper = sbwHandleMapper;
        this.sbwTaskOrgMapper = sbwTaskOrgMapper;
        this.sbwAsyncService = sbwAsyncService;
        this.errors = errors;
    }

    /**
     * 审核组织任务列表
     * @param form
     * @param page
     * @param pageSize
     * @return
     */
    public List<SbwTaskListForm> verifyList(SbwTaskListForm form, Integer page, Integer pageSize){
        return PageHelper.startPage(page,pageSize).doSelectPage(()-> sbwTaskOrgMapper.myVerifyTask(form));
    }

    /**
     * 任务下发组织
     * @param taskId
     * @return
     */
    public List<SbwTaskOrgListForm> orgList(Long taskId){
        return sbwTaskOrgMapper.orgList(taskId);
    }

    /**
     * 保存审批草稿
     * @param form
     * @return
     */
    public Long save(SbwHandleForm form){
        if (ObjectUtils.isEmpty(form.getOrgId()) || ObjectUtils.isEmpty(form.getTaskId())){
            log.error("缺少orgId或taskId");
            throw new ApiException("缺少必填项",new Result<>(errors,3002, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        SbwHandleEntity entity = new SbwHandleEntity();
        BeanUtils.copyProperties(form,entity);
        Date date = new Date();
        if (ObjectUtils.isEmpty(form.getHandleId())){
            //新增草稿
            entity.setCreateTime(date);
            sbwHandleMapper.insertUseGeneratedKeys(entity);
        }else {
            //修改草稿
            entity.setUpdateTime(date);
            sbwHandleMapper.updateByPrimaryKeySelective(entity);
        }
        return entity.getHandleId();
    }

    /**
     * 提交审核
     * @param form
     * @return
     */
    public Integer submit(SbwHandleForm form){
        if (ObjectUtils.isEmpty(form.getTaskId()) || ObjectUtils.isEmpty(form.getOrgId())
                || ObjectUtils.isEmpty(form.getHandleStatus()) || StringUtils.isBlank(form.getHandleComment())){
            log.error("缺少必填项");
            throw new ApiException("缺少必填项",new Result<>(errors,3002,HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        SbwHandleEntity entity = new SbwHandleEntity();
        long handleId = -1;
        boolean flag = true;
        if (ObjectUtils.isEmpty(form.getHandleId())){
            entity.setTaskId(form.getTaskId());
            entity.setRegionId(form.getRegionId());
            entity.setOrgId(form.getOrgId());
            entity.setUserOrgId(form.getUserOrgId());
            entity.setFlag(form.getFlag());
            entity = sbwHandleMapper.selectOne(entity);
            if (!ObjectUtils.isEmpty(entity)){
                flag = false;
                handleId = entity.getHandleId();
            }else {
                entity = new SbwHandleEntity();
            }
        }else {
            flag = false;
        }
        BeanUtils.copyProperties(form,entity);
        if (handleId != -1){
            entity.setHandleId(handleId);
        }
        Date date = new Date();
        int result;
        if (flag){
            //新增
            entity.setCreateTime(date);
            result = sbwHandleMapper.insertUseGeneratedKeys(entity);
        }else {
            //提交草稿
            entity.setUpdateTime(date);
            result = sbwHandleMapper.updateByPrimaryKeySelective(entity);
        }
        //记录流水
        sbwAsyncService.recordFlow(entity,date);
        //更新任务组织对应状态
        sbwAsyncService.updateTaskOrgTypeStatus(entity,date);
        return result;
    }

}
