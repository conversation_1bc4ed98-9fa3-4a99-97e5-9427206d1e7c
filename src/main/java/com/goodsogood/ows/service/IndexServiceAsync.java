package com.goodsogood.ows.service;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.RedisConstant;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.mapper.MeetingLeaveMapper;
import com.goodsogood.ows.mapper.MeetingMapper;
import com.goodsogood.ows.mapper.MeetingTaskMapper;
import com.goodsogood.ows.mapper.TopicOrgMapper;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 首页汇总
 *
 * <AUTHOR>
 * @create 2018/10/31 13:50
 */
@Service
@Log4j2
public class IndexServiceAsync {

    private final MeetingLeaveRedisService meetingLeaveRedisService;
    private final MeetingRedisService meetingRedisService;
    private final MeetingTaskRedisService meetingTaskRedisService;
    private final TopicOrgRedisService topicOrgRedisService;
    private final MyMeetingService myMeetingService;
    private final UserCenterService userCenterService;
    private final StringRedisTemplate stringRedisTemplate;
    private final MeetingConfig meetingConfig;
    private final MeetingLeaveMapper meetingLeaveMapper;
    private final MeetingMapper meetingMapper;
    private final TopicOrgMapper topicOrgMapper;
    private final MeetingTaskMapper meetingTaskMapper;
    private final RegionService regionService;

    @Autowired
    public IndexServiceAsync(
            MeetingLeaveRedisService meetingLeaveRedisService,
            MeetingRedisService meetingRedisService,
            MeetingTaskRedisService meetingTaskRedisService,
            TopicOrgRedisService topicOrgRedisService,
            MyMeetingService myMeetingService,
            UserCenterService userCenterService,
            StringRedisTemplate stringRedisTemplate,
            MeetingConfig meetingConfig,
            MeetingLeaveMapper meetingLeaveMapper,
            MeetingMapper meetingMapper,
            TopicOrgMapper topicOrgMapper,
            MeetingTaskMapper meetingTaskMapper, RegionService regionService) {
        this.meetingLeaveRedisService = meetingLeaveRedisService;
        this.meetingRedisService = meetingRedisService;
        this.meetingTaskRedisService = meetingTaskRedisService;
        this.topicOrgRedisService = topicOrgRedisService;
        this.myMeetingService = myMeetingService;
        this.userCenterService = userCenterService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.meetingConfig = meetingConfig;
        this.meetingLeaveMapper = meetingLeaveMapper;
        this.meetingMapper = meetingMapper;
        this.topicOrgMapper = topicOrgMapper;
        this.meetingTaskMapper = meetingTaskMapper;
        this.regionService = regionService;
    }

    /**
     * 异步全量刷新首页汇总（移动端） 单线程
     */
    @Async("indexCollectRedis")
    public void collectRedis(LogAspectHelper.SSLog ssLog) {
        LogHelper.asyncLog(ssLog, () -> {
            String mark = "全量刷新首页汇总（移动端）缓存";
            log.info("【MEETING】" + mark + "开始!");
            StopWatch stopWatch = new StopWatch(mark);
            // 新的keys
            Set<String> nKeys = new HashSet<>();
            List<Region.RegionData> regionDataList = regionService.getRegions();

            stopWatch.start("管理的活动和任务管理");
            nKeys.addAll(collectOrgRedis(regionDataList));
            stopWatch.stop();


            stopWatch.start("待审批的请假和我的活动");
            nKeys.addAll(collectUserrRedis(regionDataList));
            stopWatch.stop();

            stopWatch.start("更新缓存");
            // 删除本次没有出现的key
            Set<Object> oKeys = stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).keys();
            oKeys.removeAll(nKeys);
            if (oKeys.size() > 0) {
                stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).delete(oKeys.toArray());
            }
            stopWatch.stop();

            log.info("\n【MEETING】" + mark + "结束!耗时:{}", stopWatch.prettyPrint());
        });
    }

    /**
     * 用户相关相数据
     *
     * @return Set<String> keys
     */
    private Set<String> collectUserrRedis(List<Region.RegionData> regionDataList) {
        Set<String> keys = new HashSet<>();
        regionDataList.forEach(
                regionData -> {
                    Long regionId = regionData.getRegionId();
                    if (regionId != null) {
                        HeaderHelper.SysHeader sysHeader =
                                RestTemplateHelper.getRegionIdLogHeader(regionId);
                        // 分页查询用户中心数据 一页一页处理 每页500 数据
                        Long bindOrgId = regionService.bindingOrgId(regionId);
                        int page = 1, pageSize = 1000;
                        for (; ; ) {
                            List<UserForm> regionUserFormList =
                                    userCenterService.findAllUsersByOid(sysHeader, bindOrgId, page, pageSize);
                            if (CollectionUtils.isEmpty(regionUserFormList)) {
                                break;
                            }
                            page++; // 页码+1
                            // 最新的数据
                            Map<String, String> indexCollect = new HashMap<>();

                            Set<Long> userIdSet = regionUserFormList.stream().map(UserForm::getUserId).collect(Collectors.toSet());
                            // 查询用户待审批的请假数量
                            List<WaitApprovalCountForm> waitApprovalCountFormList =
                                    meetingLeaveMapper.waitApprovalCount(regionId, userIdSet);
                            Map<Long, Integer> waitApprovalCountMap =
                                    new HashMap<>(waitApprovalCountFormList.size());
                            for (WaitApprovalCountForm waitApprovalCountForm : waitApprovalCountFormList) {
                                waitApprovalCountMap.put(
                                        waitApprovalCountForm.getUserId(), waitApprovalCountForm.getNum());
                            }

                            // 查询我的活动数量
                            List<WaitDoMeetingCountForm> waitDoMeetingCountFormList =
                                    meetingMapper.waitDoMeetingCount(regionId, userIdSet);
                            Map<Long, Integer> waitDoMeetingCountMap =
                                    new HashMap<>(waitDoMeetingCountFormList.size());
                            for (WaitDoMeetingCountForm waitDoMeetingCountForm : waitDoMeetingCountFormList) {
                                waitDoMeetingCountMap.put(
                                        waitDoMeetingCountForm.getUserId(), waitDoMeetingCountForm.getNum());
                            }
                            for (UserForm userForm : regionUserFormList) {
                                Long uid = userForm.getUserId();
                                //  待审批的请假缓存刷新
                                String waitApprovalCountRedisKey =
                                        meetingLeaveRedisService.waitApprovalCountRedisKey(regionId, uid);
                                Integer waitApprovalCount = 0;
                                if (waitApprovalCountMap.containsKey(uid)) {
                                    waitApprovalCount = waitApprovalCountMap.get(uid);
                                }
                                indexCollect.put(waitApprovalCountRedisKey, waitApprovalCount.toString());
                                // 我的活动缓存刷新
                                String waitDoMeetingCountRedisKey =
                                        myMeetingService.waitDoMeetingCountRedisKey(regionId, uid);
                                Integer waitDoMeetingCount = 0;

                                if (waitDoMeetingCountMap.containsKey(uid)) {
                                    waitDoMeetingCount = waitDoMeetingCountMap.get(uid);
                                }
                                indexCollect.put(waitDoMeetingCountRedisKey, waitDoMeetingCount.toString());
                            }
                            keys.addAll(indexCollect.keySet());
                            // 更新值
                            stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).putAll(indexCollect);
                            stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).expire(1, TimeUnit.HOURS);
                        }
                    }
                });
        return keys;
    }

    /**
     * 组织相关相数据
     *
     * @return Set<String> keys
     */
    private Set<String> collectOrgRedis(List<Region.RegionData> regionDataList) {
        Set<String> keys = new HashSet<>();
        regionDataList.forEach(regionData -> {
            if (regionData.getRegionId() != null) {
                // 最新的数据
                Map<String, String> indexCollect = new HashMap<>();

                HeaderHelper.SysHeader sysHeader = RestTemplateHelper.getRegionIdLogHeader(regionData.getRegionId());
                // 查询区县下的所有组织
                List<OrganizationBase> list = userCenterService.allOrg(sysHeader);
                /* 查询组织信息
                 * 管理的活动
                 * 任务管理
                 */
                if (CollectionUtils.isNotEmpty(list)) {
                    // “已退回”、“活动待填报”、“填报审批中”、“填报未通过”的活动数量
                    List<MeetingH5CountForm> meetingH5CountFormList = meetingMapper.meetingH5Count();
                    Map<Long, Integer> meetingH5CountMap = new HashMap<>(meetingH5CountFormList.size());
                    for (MeetingH5CountForm meetingH5CountForm : meetingH5CountFormList) {
                        meetingH5CountMap.put(meetingH5CountForm.getOrgId(), meetingH5CountForm.getNum());
                    }
                    // 查询组织未完成task数量
                    List<UndoneTaskCountForm> undoneTaskCountFormList = meetingTaskMapper.undoneTaskCount();
                    Map<Long, Integer> undoneTaskCountMap = new HashMap<>(undoneTaskCountFormList.size());
                    for (UndoneTaskCountForm undoneTaskCountForm : undoneTaskCountFormList) {
                        undoneTaskCountMap.put(undoneTaskCountForm.getOrgId(), undoneTaskCountForm.getNum());
                    }
                    // 查询组织未完成topic数量
                    List<UndoneTopicCountForm> undoneTopicCountFormList = topicOrgMapper.undoneTopicCount();
                    Map<Long, Integer> undoneTopicCountMap = new HashMap<>(undoneTopicCountFormList.size());
                    for (UndoneTopicCountForm undoneTopicCountForm : undoneTopicCountFormList) {
                        undoneTopicCountMap.put(undoneTopicCountForm.getOrgId(), undoneTopicCountForm.getNum());
                    }
                    for (OrganizationBase org : list) {
                        //  管理的活动缓存刷新
                        Long oid = org.getOrgId();
                        String meetingH5CountRedisKey = meetingRedisService.meetingH5CountByRedisKey(oid);
                        Integer meetingH5Count = 0;
                        if (meetingH5CountMap.containsKey(oid)) {
                            meetingH5Count = meetingH5CountMap.get(oid);
                        }
                        indexCollect.put(meetingH5CountRedisKey, meetingH5Count.toString());
                        // 未完成task缓存刷新
                        String undoneTaskCountRedisKey = meetingTaskRedisService.undoneCountByRedisKey(oid);
                        Integer undoneTaskCount = 0;
                        if (undoneTaskCountMap.containsKey(oid)) {
                            undoneTaskCount = undoneTaskCountMap.get(oid);
                        }
                        indexCollect.put(undoneTaskCountRedisKey, undoneTaskCount.toString());
                        // 未完成topic缓存刷新
                        String undoneTopicCountRedisKey = topicOrgRedisService.undoneCountByRedisKey(oid);
                        Integer undoneTopicCount = 0;
                        if (undoneTopicCountMap.containsKey(oid)) {
                            undoneTopicCount = undoneTopicCountMap.get(oid);
                        }
                        indexCollect.put(undoneTopicCountRedisKey, undoneTopicCount.toString());
                    }
                    keys.addAll(indexCollect.keySet());
                    // 更新值
                    stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).putAll(indexCollect);
                    stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).expire(1, TimeUnit.HOURS);
                }
            }
        });
        return keys;
    }
}
