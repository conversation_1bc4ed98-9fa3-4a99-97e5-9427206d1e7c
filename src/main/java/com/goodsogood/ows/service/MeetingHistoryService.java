package com.goodsogood.ows.service;

import com.goodsogood.ows.common.MeetingCanstant;
import com.goodsogood.ows.mapper.MeetingHistoryMapper;
import com.goodsogood.ows.mapper.MeetingMapper;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.db.MeetingHistoryEntity;
import com.goodsogood.ows.model.db.MeetingResultHsEntity;
import com.goodsogood.ows.utils.ListUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-10-25 18:24
 **/
@Service
@Log4j2
public class MeetingHistoryService {

    private final MeetingHistoryMapper meetingHistoryMapper;
    private final MeetingMapper meetingMapper;

    @Autowired
    public MeetingHistoryService(MeetingHistoryMapper meetingHistoryMapper, MeetingMapper meetingMapper) {
        this.meetingHistoryMapper = meetingHistoryMapper;
        this.meetingMapper = meetingMapper;
    }

    /**
     * 获取当前活动的所有历史记录
     * @param meetingId
     * @return
     */
    public List<MeetingHistoryEntity> getMeetingHsList(long meetingId) {
        Example example = new Example(MeetingResultHsEntity.class);
        example.createCriteria().andEqualTo("meetingId", meetingId);
        return this.meetingHistoryMapper.selectByExample(example);
    }

    /**
     * 获取活动历史记录（如果活动未开始则只有一条待举办的记录，活动开始则返回所有历史记录）
     *
     * @param meetingId
     * @return
     */
    public List<MeetingHistoryEntity> getMeetingHistoryList(long meetingId) {
        return this.meetingHistoryMapper.getMeetingHistoryList(meetingId);
    }

    /**
     * 判断是否是第一次提交纪实报表
     * @return
     * chenanshun 2018年11月28日 10:29:57
     */
    public boolean isFirstCommitReport(long meetingId) {
         MeetingEntity meetingEntity = this.meetingMapper.selectByPrimaryKey(meetingId);
        //状态等于3、4、5、6则是第一次提交 status<7
        return meetingEntity != null && (
                meetingEntity.getStatus() < MeetingCanstant.MEETING_STATUS_SUBMIT.shortValue()
        );
    }

    /**
     * 判断是否是第一次提交纪实报表
     * @return
     */
    public boolean isFirstCommitReportInCallback(long meetingId) {
         MeetingEntity meetingEntity = this.meetingMapper.selectByPrimaryKey(meetingId);
        //状态等于3则是第一次提交
        return meetingEntity != null && (
                meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_SOON_START.shortValue()
                        || meetingEntity.getStatus() == MeetingCanstant.MEETING_STATUS_SUBMIT_FIRST.shortValue()
        );
    }

    /**
     * 判断是否是第一次提交纪实报表
     * @return
     */
    public boolean isFirstCheckReport(long meetingId) {
        return ListUtils.isEmpty(getHsList(meetingId, MeetingCanstant.MEETING_STATUS_BACK));
    }

    /**
     * 查询历史列表
     * @param meetingId
     * @param status
     * @return
     */
    public List<MeetingHistoryEntity> getHsList(long meetingId, Integer status) {
        Example example = new Example(MeetingHistoryEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("meetingId", meetingId);
        if(status != null) {
            criteria.andEqualTo("status", status);
        }
        example.orderBy("createTime").desc();

        return this.meetingHistoryMapper.selectByExample(example);
    }



}