package com.goodsogood.ows.service;

import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.SbwShiftTaskEntity;
import com.goodsogood.ows.model.db.SbwTaskEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SbwShiftTaskFrom;
import com.goodsogood.ows.model.vo.SbwShiftTaskListForm;
import com.goodsogood.ows.model.vo.SbwTaskForm;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.goodsogood.ows.service.SbwNewTaskService.getDate;

@Service
@Log4j2
public class SbwShiftTaskService {
    private final SbwShiftTaskMapper sbwShiftTaskMapper;
    private final SbwTaskMapper sbwTaskMapper;
    private final SbwHandleMapper sbwHandleMapper;
    private final SbwTaskOrgMapper sbwTaskOrgMapper;
    private final SbwNewTaskMapper sbwNewTaskMapper;
    private final SbwAsyncService sbwAsyncService;
    private final Errors errors;

    public SbwShiftTaskService(SbwShiftTaskMapper sbwShiftTaskMapper, SbwTaskMapper sbwTaskMapper, SbwHandleMapper sbwHandleMapper, SbwTaskOrgMapper sbwTaskOrgMapper, SbwNewTaskMapper sbwNewTaskMapper, SbwAsyncService sbwAsyncService, Errors errors) {
        this.sbwShiftTaskMapper = sbwShiftTaskMapper;
        this.sbwTaskMapper = sbwTaskMapper;
        this.sbwHandleMapper = sbwHandleMapper;
        this.sbwTaskOrgMapper = sbwTaskOrgMapper;
        this.sbwNewTaskMapper = sbwNewTaskMapper;
        this.sbwAsyncService = sbwAsyncService;
        this.errors = errors;
    }

    /**
     * 代办任务列表
     *
     * @param form
     * @param page
     * @param pageSize
     * @return
     */
    public List<SbwShiftTaskListForm> list(SbwShiftTaskListForm form, Integer page, Integer pageSize) {
        return PageHelper.startPage(page, pageSize).doSelectPage(() -> sbwShiftTaskMapper.list(form));
    }

    /**
     * 代办任务流水
     *
     * @param taskId
     * @return
     */
    public List<SbwShiftTaskFrom.ExecutiveLogging> flow(Long taskId) {
        List<SbwShiftTaskFrom.ExecutiveLogging> list = sbwShiftTaskMapper.flow(taskId);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        StringBuilder builder = new StringBuilder();
        int type;
        int length;
        for (SbwShiftTaskFrom.ExecutiveLogging flow : list) {
            if (StringUtils.isBlank(flow.getTypeLink())) {
                continue;
            }
            length = flow.getTypeLink().length();
            if (flow.getTypeLink().indexOf(",") > 0) {
                type = Integer.parseInt(builder.append(flow.getTypeLink()).substring(builder.lastIndexOf(",") + 1));
            } else {
                type = Integer.parseInt(builder.append(flow.getTypeLink()).substring(0));
            }
            builder.delete(0, length);
            if (type > 0 && type != Constant.HANDLE_TYPE_RETURN) {
                flow.setType(Constant.HANDLE_TYPE_SA);
            } else {
                flow.setType(type);
            }
            flow.setTypeLink(null);
        }
        list = list.stream().filter(x -> !x.getType().equals(Constant.HANDLE_TYPE_RETURN)).collect(Collectors.toList());
        Collections.reverse(list);
        return list;
    }


    /**
     * 融媒体发布转办任务 - 提交任务
     */
    public String submitShiftTask(HeaderHelper.SysHeader header, SbwShiftTaskFrom from) {
        SbwShiftTaskEntity entity = sbwShiftTaskMapper.selectByPrimaryKey(from.getShiftTaskId());
        Date date = new Date();
        int row;
        //修改
        if (null != entity) {
            entity.setRegionId(header.getRegionId());
            entity.setOrgId(header.getOid());
            entity.setOrgName(header.getOrgName().trim());
            entity.setTitle(from.getTitle().trim());
            entity.setNumber(from.getNumber());
            entity.setTimeType(from.getTimeType());
            entity.setBeginTime(from.getBeginTime());
            entity.setEndTime(getDate(from.getEndTime()));
            entity.setSource(from.getSource().trim());
            entity.setTypeId(from.getTypeId());
            entity.setContent(from.getContent().trim());
            fileToJson(entity, from);
            entity.setRemark(from.getRemark());
            checkTask(entity);
            if (entity.getStatus() == 2) {//提交在修改
                entity.setUpdateUser(header.getUserId());
                entity.setUpdateTime(date);
                row = sbwShiftTaskMapper.updateByPrimaryKeySelective(entity);
                if (null == entity.getFileJson()){
                    sbwAsyncService.updateFile(entity.getShiftTaskId(),2);
                }
                sbwAsyncService.taskUpdate(entity);
            } else if (entity.getStatus() == 1) {//保存在提交
                entity.setStatus(2);
                entity.setShiftTaskId(from.getShiftTaskId());
                entity.setUpdateUser(header.getUserId());
                entity.setUpdateTime(date);
                checkTask(entity);
                row = sbwShiftTaskMapper.updateByPrimaryKeySelective(entity);
                if (null == entity.getFileJson()){
                    sbwAsyncService.updateFile(entity.getShiftTaskId(),2);
                }
                log.debug("草稿->提交完毕" + row + "entity: " + entity);
                sbwAsyncService.task(entity, from.getShiftTaskId());
            } else {
                throw new ApiException("任务已经开始不能修改",
                        new Result<>(errors, 1804, HttpStatus.INTERNAL_SERVER_ERROR.value(), ",任务状态错误"));
            }
        } else {//直接提交
            entity = new SbwShiftTaskEntity();
            BeanUtils.copyProperties(from, entity);
            entity.setRegionId(header.getRegionId());
            entity.setOrgId(header.getOid());
            entity.setOrgName(header.getOrgName().trim());
            entity.setCreateUser(header.getUserId());
            entity.setEndTime(getDate(from.getEndTime()));
            entity.setCreateTime(date);
            fileToJson(entity, from);
            entity.setStatus(2);
            entity.setIsDel(0);
            entity.setIsRead(0);
            checkTask(entity);
            row = sbwShiftTaskMapper.insertUseGeneratedKeys(entity);
            sbwAsyncService.task(entity, entity.getShiftTaskId());
        }
        if (row > 0) {
            return "提交成功";
        } else {
            throw new ApiException("提交任务失败",
                    new Result<>(errors, 9913, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }

    }

    /**
     * 融媒体发布转办任务 - 保存草稿
     */
    public Long saveShiftTask(HeaderHelper.SysHeader header, SbwShiftTaskFrom from) {
        SbwShiftTaskEntity entity = new SbwShiftTaskEntity();
        Date date = new Date();
        entity.setRegionId(header.getRegionId());
        entity.setOrgId(header.getOid());
        entity.setOrgName(header.getOrgName());
        entity.setShiftTaskId(from.getShiftTaskId());
        entity.setTitle(from.getTitle());
        entity.setNumber(from.getNumber());
        entity.setTimeType(from.getTimeType());
        entity.setBeginTime(from.getBeginTime());
        entity.setEndTime(getDate(from.getEndTime()));
        entity.setSource(from.getSource());
        entity.setTypeId(from.getTypeId());
        entity.setContent(from.getContent());
        fileToJson(entity, from);
        entity.setRemark(from.getRemark());
        entity.setStatus(1);
        entity.setIsDel(0);
        entity.setIsRead(0);
        entity.setCreateTime(new Date());
        int row;
        if (null != from.getShiftTaskId()) {
            Integer status = sbwShiftTaskMapper.checkShiftStatusIsNotNull(from.getShiftTaskId());
            if (null != status && status == 1) {
                entity.setShiftTaskId(from.getShiftTaskId());
                entity.setUpdateUser(header.getUserId());
                if (null == entity.getFileJson()){
                    sbwAsyncService.updateFile(entity.getShiftTaskId(),2);
                }
                row = sbwShiftTaskMapper.updateByPrimaryKeySelective(entity);
            } else {
                throw new ApiException("任务已经开始不能保存",
                        new Result<>(errors, 1804, HttpStatus.INTERNAL_SERVER_ERROR.value(), ",任务状态错误,任务已经开始不能保存"));
            }
        } else {
            entity.setCreateUser(header.getUserId());
            row = sbwShiftTaskMapper.insertUseGeneratedKeys(entity);
        }
        if (row > 0) {
            return entity.getShiftTaskId();
        } else {
            throw new ApiException("保存任务失败",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "未查询到该任务"));
        }

    }

    /**
     * 融媒体发布转办任务 - 编辑任务
     */
    public String compileShiftTask(HeaderHelper.SysHeader header, SbwShiftTaskFrom from) {
        if (from.getShiftTaskId() == null) {
            throw new ApiException("编辑任务失败",
                    new Result<>(errors, 1804, HttpStatus.INTERNAL_SERVER_ERROR.value(), "shift_task_id不能为空"));
        }
        SbwShiftTaskEntity entity = new SbwShiftTaskEntity();
        BeanUtils.copyProperties(from, entity);
        entity.setRegionId(header.getRegionId());
        entity.setOrgId(header.getOid());
        entity.setOrgName(header.getOrgName());
        checkTask(entity);
        int row = sbwShiftTaskMapper.updateByPrimaryKeySelective(entity);
        if (row > 0) {
            return "修改成功";
        } else {
            throw new ApiException("修改任务失败",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 融媒体转办单详情
     *
     * @param header      头
     * @param shiftTaskId 任务id
     * @return 转办单列表
     */
    public SbwShiftTaskFrom showTask(HeaderHelper.SysHeader header, Long shiftTaskId) {
        if (ObjectUtils.isEmpty(shiftTaskId)) {
            throw new ApiException("查询失败,请传递shift_task_id数据",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "请传递shift_task_id数据"));
        }
        Long regionId = header.getRegionId();
        //任务表查询
        Example example = new Example(SbwShiftTaskEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("regionId", regionId)
                .andEqualTo("shiftTaskId", shiftTaskId)
                .andEqualTo("isDel", 0);
        SbwShiftTaskEntity entity = sbwShiftTaskMapper.selectOneByExample(example);
        SbwShiftTaskFrom form = new SbwShiftTaskFrom();
        if (!ObjectUtils.isEmpty(entity)) {
            BeanUtils.copyProperties(entity, form);
            log.debug("转办任务列表->>" + form);
        } else {
            throw new ApiException("查询列表失败",
                    new Result<>(errors, 3007, HttpStatus.INTERNAL_SERVER_ERROR.value(), ",未获取到该转办任务信息"));
        }
        //查询执行记录
        form.setRecord(flow(entity.getTaskId()));
        if (StringUtils.isNotEmpty(entity.getFileJson())) {
            form.setFiles((List<SbwShiftTaskFrom.file>) JsonUtils.fromJson(entity.getFileJson(), List.class, SbwShiftTaskFrom.file.class));
        }
        return form;
    }

    /**
     * 删除转办任务
     *
     * @return
     */
    public String delShiftTask(HeaderHelper.SysHeader header, Long shiftTaskId) {
        int row = sbwShiftTaskMapper.delShiftTask(shiftTaskId);
        if (row != 0) {
            return "删除成功";
        } else {
            throw new ApiException("查询列表失败",
                    new Result<>(errors, 3006, HttpStatus.INTERNAL_SERVER_ERROR.value(), ",未获取到该转办任务信息"));

        }

    }

    /*文件转json*/
    public void fileToJson(SbwShiftTaskEntity entity, SbwShiftTaskFrom from) {
        if (!CollectionUtils.isEmpty(from.getFiles())) {
            entity.setFileJson(JsonUtils.toJson(from.getFiles()));
        } else {
            entity.setFileJson(null);
        }
    }

    /*获取主任务文件信息*/
    public void files(SbwTaskEntity entity, SbwTaskForm form) {
        if (!StringUtils.isEmpty(entity.getFileId())) {
            String[] s1 = entity.getFileId().split(",");
            String[] s2 = entity.getFilename().split(",");
            if (s2.length == 0) {
                throw new ApiException("文件名获取失败",
                        new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "文件名获取失败"));
            }
            List<SbwTaskForm.File> files = new ArrayList<>(s1.length);
            for (int i = 0; i < s1.length; i++) {
                SbwTaskForm.File file = new SbwTaskForm.File();
                file.setFileId(Long.parseLong(s1[i]));
                file.setFilename(s2[i]);
                files.add(file);
            }
            form.setFiles(files);
        }
    }

    /*根据时间判断获取flag*/
    public int getFlag(Date date, SbwShiftTaskFrom form) {
        if (getDate(form.getEndTime()).getTime() < date.getTime()) {
            return 5;
        } else {
            return 2;
        }
    }

    /*检验发布任务的信息*/
    public void checkTask(SbwShiftTaskEntity entity) {
        if (ObjectUtils.isEmpty(entity.getOrgId())) {
            //判断orgId是否为空
            throw new ApiException("新建任务失败,orgId不能为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "orgId不能为空"));
        } else if (StringUtils.isEmpty(entity.getOrgName())) {
            //判断orgName是否为空
            throw new ApiException("新建任务失败,orgName支部名称为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "orgName支部名称不能为空"));
        } else if (StringUtils.isEmpty(entity.getTitle())) {
            //判断title是否为空
            throw new ApiException("新建任务失败,title标题为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "title标题不能为空"));
        } else if (StringUtils.isEmpty(entity.getNumber())) {
            //判断number编号是否为空
            throw new ApiException("新建任务失败,number编号为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "number编号不能为空"));
        } else if (ObjectUtils.isEmpty(entity.getTimeType())) {
            //判断timeType时间类型是否为空
            throw new ApiException("新建任务失败,timeType时间类型为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "timeType时间类型不能为空"));
        } else if (ObjectUtils.isEmpty(entity.getBeginTime())) {
            //判断beginTime是否为空
            throw new ApiException("新建任务失败,beginTime开始时间为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "beginTime开始时间不能为空"));
        } else if (ObjectUtils.isEmpty(entity.getEndTime())) {
            //判断endTime是否为空
            throw new ApiException("新建任务失败,endTime结束时间为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "endTime结束时间不能为空"));
        } else if (StringUtils.isEmpty(entity.getSource())) {
            //判断source是否为空,字数是否超过2000
            throw new ApiException("新建任务失败,source舆情来源为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "source舆情来源不能为空"));
        } else if (StringUtils.isEmpty(entity.getContent())) {
            //判断content是否为空,字符是否超过2000
            throw new ApiException("新建任务失败,content舆情概要为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "content舆情概要不能为空"));
        } else if (ObjectUtils.isEmpty(entity.getCreateUser())) {
            //判断createUser是否为空
            throw new ApiException("新建任务失败,createUser创建用户id为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "createUser创建用户id不能为空"));
        } else if (entity.getEndTime().before(entity.getBeginTime())) {
            //判断结束时间,和开始时间
            throw new ApiException("新建任务失败,createUser创建用户id为空",
                    new Result<>(errors, 3001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "结束时间不能小于开始时间"));
        }
    }

}
