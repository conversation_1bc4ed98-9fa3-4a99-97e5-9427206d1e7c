package com.goodsogood.ows.service;

import com.goodsogood.ows.common.RedisConstant;
import com.goodsogood.ows.mapper.MeetingLeaveMapper;
import com.goodsogood.ows.model.vo.WaitApprovalCountForm;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018/10/29 16:08
 */
@Service
@Log4j2
public class MeetingLeaveRedisService {

    private final MeetingLeaveMapper meetingLeaveMapper;
    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public MeetingLeaveRedisService(
            MeetingLeaveMapper meetingLeaveMapper,
            StringRedisTemplate stringRedisTemplate) {
        this.meetingLeaveMapper = meetingLeaveMapper;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 获取待审批的请假数量 如果缓存中有数据 从缓存中读取 没有则从数据库中读取
     *
     * @param uid 用户id，为空 返回0
     * @return int 待审批的请假数量
     */
    int getWaitApprovalCountByRedis(Long regionId, Long uid) {
        // uid 为空 返回0
        if (uid == null) {
            return 0;
        }
        String redisKey = waitApprovalCountRedisKey(regionId, uid);
        Object redisCount = stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).get(redisKey);
        // 缓存中有值，返回缓存值
        if (redisCount != null) {
            return Integer.valueOf((String) redisCount);
        }
        return waitApprovalCountAndRedisByDb(regionId, uid);
    }

    /**
     * 统计用户待审批的请假数量并添加缓存
     *
     * @param uid 用户id
     * @return int 待审批的请假数量
     */
    private int waitApprovalCountAndRedisByDb(Long regionId, Long uid) {
        // uid 为空 返回0
        if (uid == null) {
            return 0;
        }
        // 缓存中没有值 读取数据，添加到缓存中
        Integer count = waitApprovalCount(regionId, uid);
        // 存入缓存，有效时间1小时
        String redisKey = waitApprovalCountRedisKey(regionId, uid);
        stringRedisTemplate.boundHashOps(RedisConstant.MEETING_INDEX).put(redisKey, count.toString());
        return count;
    }

    private Integer waitApprovalCount(Long regionId, Long uid) {
        // 查询用户待审批的请假数量
        List<WaitApprovalCountForm> waitApprovalCountFormList =
                meetingLeaveMapper.waitApprovalCount(regionId, Collections.singleton(uid));
        if (CollectionUtils.isNotEmpty(waitApprovalCountFormList)) {
            return waitApprovalCountFormList.get(0).getNum();
        }
        return 0;
    }

    /**
     * redis key
     *
     * @param regionId 用户id
     * @param uid      用户id
     * @return key
     */
    String waitApprovalCountRedisKey(Long regionId, Long uid) {
        return RedisConstant.MEETING_WAIT_APPROVAL + regionId + "_" + uid;
    }
}
