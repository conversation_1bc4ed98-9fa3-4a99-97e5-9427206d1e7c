package com.goodsogood.ows.service;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MeetingDraftMapper;
import com.goodsogood.ows.model.db.MeetingDraftEntity;
import com.goodsogood.ows.model.db.MeetingEntity;
import com.goodsogood.ows.model.vo.MeetingDraftSaveForm;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/12/14
 */
@Service
@Log4j2
public class MeetingDraftService {

    /**
     * 纪实草稿缓存，%s = region_id，%s = org_id，%s = user_id，%s = type，%s = meeting_id
     */
    private static final String DRAFT_CACHE = "MEETING_DRAFT_%s_%s_%s_%s_%s";

    private final StringRedisTemplate stringRedisTemplate;

    private final MeetingDraftMapper meetingDraftMapper;
    private final TransferService transferService;

    @Autowired
    public MeetingDraftService(StringRedisTemplate stringRedisTemplate, MeetingDraftMapper meetingDraftMapper, TransferService transferService) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.meetingDraftMapper = meetingDraftMapper;
        this.transferService = transferService;
    }

    private String getCacheKey(Long regionId, Long orgId, Long userId, Integer type, Long meetingId) {
        meetingId = meetingId == null ? -1L : meetingId;
        return String.format(DRAFT_CACHE, regionId, orgId, userId, type, meetingId);
    }

    /**
     * 保存或更新草稿
     *
     * @param headers
     * @param orgId
     * @param meetingDraftSaveForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveDraft(HttpHeaders headers, Long orgId, MeetingDraftSaveForm meetingDraftSaveForm) {
//        log.debug("判断是否是红岩魂传入的,如果是红岩魂传入的，需要设置user_id org_id,还需要将记录人等手机号转为烟草的user_id");
//        if(transferService.fromSystemSzf(headers)) {
//            String szfContent = meetingDraftSaveForm.getContent();
//            MeetingEntity meetingEntity = JsonUtils.fromJson(szfContent, MeetingEntity.class);
//            transferService.transferPhoneToUserId(headers, meetingEntity);
//            meetingDraftSaveForm.setContent(JsonUtils.toJson(meetingEntity));
//        }
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        String content = meetingDraftSaveForm.getContent();
        String cache = getCacheKey(sysHeader.getRegionId(), orgId, sysHeader.getUserId(), meetingDraftSaveForm.getType(), meetingDraftSaveForm.getMeetingId());
        MeetingDraftEntity meetingDraftEntity = getDraft(sysHeader.getRegionId(), orgId, sysHeader.getUserId(), meetingDraftSaveForm.getType(), meetingDraftSaveForm.getMeetingId());
        boolean success;
        if (meetingDraftEntity != null) {
            meetingDraftEntity.setContent(content)
                    .setUpdateTime(new Date())
                    .setContent(content);
            success = meetingDraftMapper.updateByPrimaryKeySelective(meetingDraftEntity) > 0;
        } else {
            meetingDraftEntity = new MeetingDraftEntity();
            meetingDraftEntity.setContent(content)
                    .setCreateTime(new Date())
                    .setRegionId(sysHeader.getRegionId())
                    .setOrgId(orgId)
                    .setUserId(sysHeader.getUserId())
                    .setType(meetingDraftSaveForm.getType())
                    .setMeetingId(meetingDraftSaveForm.getMeetingId());
            success = meetingDraftMapper.insert(meetingDraftEntity) > 0;
        }
        stringRedisTemplate.delete(cache);
        return meetingDraftEntity.getDraftId();
    }

    /**
     * 获取草稿
     *
     * @param regionId
     * @param userId
     * @return
     */
    public MeetingDraftEntity getDraft(Long regionId, Long orgId, Long userId, Integer type, Long meetingId) {
        String cache = getCacheKey(regionId, orgId, userId, type, meetingId);
        if (stringRedisTemplate.hasKey(cache)) {
            String json = stringRedisTemplate.opsForValue().get(cache);
            return JsonUtils.fromJson(json, MeetingDraftEntity.class);
        }
        Example example = new Example(MeetingDraftEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("regionId", regionId)
                .andEqualTo("orgId", orgId)
                .andEqualTo("userId", userId)
                .andEqualTo("type", type);
        if (type == 1) {
            criteria.andEqualTo("meetingId", meetingId);
        }
        MeetingDraftEntity meetingDraftEntity = meetingDraftMapper.selectOneByExample(example);
        if (meetingDraftEntity != null) {
            stringRedisTemplate.opsForValue().set(cache, JsonUtils.toJson(meetingDraftEntity), 30, TimeUnit.MINUTES);
        }
        return meetingDraftEntity;
    }

    /**
     * 删除草稿
     *
     * @param regionId
     * @param userId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDraft(Long regionId, Long orgId, Long userId, Integer type, Long meetingId) {
        String cache = getCacheKey(regionId, orgId, userId, type, meetingId);
        Example example = new Example(MeetingDraftEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("regionId", regionId)
                .andEqualTo("orgId", orgId)
                .andEqualTo("userId", userId)
                .andEqualTo("type", type);
        if (type == 1) {
            criteria.andEqualTo("meetingId", meetingId);
        }
        boolean success = meetingDraftMapper.deleteByExample(example) > 0;
        stringRedisTemplate.delete(cache);
        return success;
    }
}
