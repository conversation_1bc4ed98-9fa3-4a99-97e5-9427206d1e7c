package com.goodsogood.ows.service;

import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.TopicConstant;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.configuration.MeetingConfig;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.LogHelper;
import com.goodsogood.ows.model.db.TopicEntity;
import com.goodsogood.ows.model.db.TopicOrgEntity;
import com.goodsogood.ows.model.vo.SendMsgForm;
import com.goodsogood.ows.parsing.SimpleGenericTokenParser;
import com.goodsogood.ows.push.template.sms.SmsNormalMessageTemplate;
import com.goodsogood.ows.push.template.wechat.WorkNoticeTemplate;
import com.goodsogood.ows.utils.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2018-10-19 16:39
 **/
@Service
@Log4j2
public class TopicServiceAsync {

    private final Errors errors;
    private final OpenService openService;
    private final RestTemplatePushCenterService pushCenterService;
    private final MeetingConfig meetingConfig;
    private final RegionService regionService;
    private final WechatService wechatService;

    public TopicServiceAsync(Errors errors, OpenService openService, RestTemplatePushCenterService pushCenterService, MeetingConfig meetingConfig, RegionService regionService, WechatService wechatService) {
        this.errors = errors;
        this.openService = openService;
        this.pushCenterService = pushCenterService;
        this.meetingConfig = meetingConfig;
        this.regionService = regionService;
        this.wechatService = wechatService;
    }

    /**
     * 派发任务后异步推送消息
     */
    @Async
    void addTopicNotice(HeaderHelper.SysHeader sysHeader, TopicEntity topic, LogAspectHelper.SSLog ssLog) {
        LogHelper.asyncLog(ssLog, () -> {
            if (topic.getStatus() == TopicConstant.STATUS_OWNER) {
                log.debug("自己添加的任务，不推送消息,topicId:{}", topic.getTopicId());
                return;
            }
            MeetingConfig.TopicConfig.NoticeConfig noticeConfig = meetingConfig.getTopic().getNotice();
            if (!noticeConfig.getEnable()) {
                log.info("派发任务消息推送关闭!");
                return;
            }
            // 查询接收组织的管理员
            if (topic != null && topic.getNoticeType() != null && CollectionUtils.isNotEmpty(topic.getOrgs())) {
                log.info("派发任务后异步推送消息,topicId:{}", topic.getTopicId());
                Map<Long, List<TopicOrgEntity>> adminMap = new HashMap<>(1024);
                // 查询组织管理员
                Long oid;
                for (TopicOrgEntity to : topic.getOrgs()) {
                    if ((oid = to.getOrgId()) != null) {
                        List<SendMsgForm> adminList = openService.getAdmin(sysHeader.getRegionId(), Collections.singletonList(oid),null);
                        if (CollectionUtils.isNotEmpty(adminList)) {
                            for (SendMsgForm smf : adminList) {
                                if (smf.getUserId() != null) {
                                    if (adminMap.containsKey(smf.getUserId())) {
                                        adminMap.get(smf.getUserId()).add(to);
                                    } else {
                                        List<TopicOrgEntity> list = new ArrayList<>(16);
                                        adminMap.put(smf.getUserId(), list);
                                        list.add(to);
                                    }
                                }
                            }
                        }
                    }
                }
                if (adminMap.size() > 0) {
                    if (topic.getNoticeType().equals(1)) {
                        smsNotice(sysHeader, topic, adminMap.keySet());
                    } else if (topic.getNoticeType().equals(2)) {
                        try {
                            wechatNotice(sysHeader, topic, adminMap);
                        } catch (UnsupportedEncodingException e) {
                            log.error("微信通知失败", e);
                        }
                    }
                }
            } else {
                log.info("接收组织为空，topicId:{}", topic != null ? topic.getTopicId() : -1);
            }
        });
    }


    private void smsNotice(HeaderHelper.SysHeader sysHeader, TopicEntity topic, Set<Long> uidSet) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return;
        }
        MeetingConfig.TopicConfig.NoticeConfig.SmsTemplateConfig smsTemplateConfig
                = meetingConfig.getTopic().getNotice().getSmsTemplate();
        SmsNormalMessageTemplate smsNormalMessageTemplate = new SmsNormalMessageTemplate();
        // 渠道类型	1-短信，2-微信
        smsNormalMessageTemplate.setChannelType(smsTemplateConfig.getChannelType());
        // 短信普通消息模板 26
        smsNormalMessageTemplate.setTemplateId(smsTemplateConfig.getId());
        // 是否全量推送 0-否，1-是
        smsNormalMessageTemplate.setIsFull((byte) 0);
        smsNormalMessageTemplate.setUserIds(new ArrayList<>(uidSet));
        SimpleGenericTokenParser parser = SimpleGenericTokenParser.builder()
                .addFieldVal(topic)
                .addFieldVal("startTime", DateUtils.dateFormat(topic.getStartTime(), "yyyy-MM-dd"))
                .addFieldVal("endTime", DateUtils.dateFormat(topic.getEndTime(), "yyyy-MM-dd"))
                .build();
        smsNormalMessageTemplate.setData(parser.parse(smsTemplateConfig.getMsg()));
        smsNormalMessageTemplate.setSource(smsTemplateConfig.getSource());
        // 推送类型,0-即时推送，1-定时推送，此处传0即可
        smsNormalMessageTemplate.setPushType((byte) 0);
        // 是否外链 0-否，1-是，此处传1即可
        smsNormalMessageTemplate.setIsLinkOut((byte) 0);
        Long orgId = regionService.bindingOrgId(sysHeader.getRegionId());
        smsNormalMessageTemplate.setOrgId(orgId);
        pushCenterService.globalPushSameByUserIds(sysHeader, smsNormalMessageTemplate);
    }

    private void wechatNotice(HeaderHelper.SysHeader sysHeader, TopicEntity topic, Map<Long, List<TopicOrgEntity>> adminMap) throws UnsupportedEncodingException {
        MeetingConfig.TopicConfig.NoticeConfig.WechatTemplateConfig wechatTemplateConfig
                = meetingConfig.getTopic().getNotice().getWechatTemplate();
        WorkNoticeTemplate workNoticeTemplate = new WorkNoticeTemplate();
        workNoticeTemplate.setTemplateId(wechatTemplateConfig.getId());
        workNoticeTemplate.setChannelType(wechatTemplateConfig.getChannelType());
        workNoticeTemplate.setSource(wechatTemplateConfig.getSource());
        long regionId = sysHeader.getRegionId();
        Long orgId = regionService.bindingOrgId(regionId);
        workNoticeTemplate.setOrgId(orgId);
        // 批量推送
        Long parentPushId = 0L;
        int uidSize = adminMap.size();
        int num = 0;
        int batchNum = wechatTemplateConfig.getBatchNum();

        List<WorkNoticeTemplate.WorkNotice> dataList = new ArrayList<>();
        SimpleGenericTokenParser parser = SimpleGenericTokenParser.builder()
                .addFieldVal(topic)
                .addFieldVal("startTime", DateUtils.dateFormat(topic.getStartTime(), "yyyy-MM-dd"))
                .addFieldVal("endTime", DateUtils.dateFormat(topic.getEndTime(), "yyyy-MM-dd"))
                .build();
        WorkNoticeTemplate.WorkNotice workNotice = wechatTemplateConfig.getMsg();
        WorkNoticeTemplate.WorkNotice workNoticeMore = wechatTemplateConfig.getMsgMore();
        for (Map.Entry<Long, List<TopicOrgEntity>> admin : adminMap.entrySet()) {
            if (admin.getKey() != null && CollectionUtils.isNotEmpty(admin.getValue())) {
                WorkNoticeTemplate.WorkNotice msg;
                if (admin.getValue().size() == 1) {
                    msg = parser.parse(workNotice);
                    msg.setLinkUrl(templateLinkUrl(regionId, admin.getValue().get(0)));
                } else {
                    // 管理多个组织时无外链
                    msg = parser.parse(workNoticeMore);
                }
                msg.setUserId(admin.getKey());
                dataList.add(msg);
                num++;
                //按消息中心最大条数切割，不足的直接全部
                if ((num % batchNum == 0) || num == uidSize) {
                    workNoticeTemplate.setData(dataList);
                    workNoticeTemplate.setParentPushId(parentPushId);
                    parentPushId = pushCenterService.globalPushDiffByUserIds(sysHeader, workNoticeTemplate);
                    dataList.clear();
                }
            }
        }
        if (CollectionUtils.isNotEmpty(dataList)) {
            workNoticeTemplate.setData(dataList);
            workNoticeTemplate.setParentPushId(parentPushId);
            pushCenterService.globalPushDiffByUserIds(sysHeader, workNoticeTemplate);
            dataList.clear();
        }
    }

    private String templateLinkUrl(
            long regionId, TopicOrgEntity topicOrgEntity)
            throws UnsupportedEncodingException {
        MeetingConfig.TopicConfig.NoticeConfig.WechatTemplateConfig wechatTemplateConfig
                = meetingConfig.getTopic().getNotice().getWechatTemplate();
        Region.RegionData regionData = regionService.regionData(regionId);
        boolean isSaasVersion = regionData.wechatIsSaasVersion();
        SimpleGenericTokenParser parser =
                SimpleGenericTokenParser.builder()
                        .addFieldVal("bindingOrgId", regionService.bindingOrgId(regionId).toString())
                        .addFieldVal("regionId", String.valueOf(regionId))
                        .addFieldVal(regionData)
                        .addFieldVal(topicOrgEntity)
                        .build();
        if (isSaasVersion) {
            // saas 跳转到微信中心
            String redirectCheckPageUri =
                    parser.parse(wechatTemplateConfig.getRedirectCheckPageUri());
            return wechatService.loginPreRouterUrl(regionId, redirectCheckPageUri);
        } else {
            // V1.0.6 跳转前端
            StringBuilder templateLinkUrl = new StringBuilder();
            templateLinkUrl.append(regionData.getWechatDomain().trim());
            String checkPageToDetailUri = wechatTemplateConfig.getCheckPageToDetailUri();
            templateLinkUrl.append(parser.parse(checkPageToDetailUri));
            return templateLinkUrl.toString();
        }
    }
}
