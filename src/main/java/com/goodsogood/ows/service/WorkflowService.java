package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.aidangqun.log4j2cm.helper.LogAspectHelper;
import com.goodsogood.ows.common.pojo.Headers;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.workflow.ApprovalBase;
import com.goodsogood.ows.utils.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

/**
 * <p>审批流程service</p>
 * <p>提供公共的审批服务</p>
 * <AUTHOR>
 * @create 2018-10-25 16:02
 **/
@Service
@Log4j2
public class WorkflowService {

     private final RestTemplate restTemplate;

     @Value("${tog-services.workflow}")
     private String workflow;

     @Autowired
     public WorkflowService(RestTemplate restTemplate) {
          this.restTemplate = restTemplate;
     }

     /**
      * 新建审批
      *
      * @param approvalBase
      * @param headers
      * @return
      */
     public long addApproval(ApprovalBase approvalBase, HttpHeaders headers) {
          log.debug("审批流程申请：[{}]", JsonUtils.toJson(approvalBase));
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          headers = HeaderHelper.setMyHttpHeader(headers, sysHeader);
          if (sysHeader.getUoid() != null) {
               headers.set(HeaderHelper.OPERATOR_OID, sysHeader.getUoid().toString());
          }
          this.restTemplate.setErrorHandler(new ClientExceptionHandler());
          long workflowTaskId;
          try {
               workflowTaskId = RemoteApiHelper.post(this.restTemplate, String.format("http://%s/%s",
                       workflow,
                       "approval/add"), approvalBase, headers, new TypeReference<Result<Long>>() {
               });
          } catch (ApiException ae) {
               log.error("添加审批发生错误:{}", ae.getMessage());
               log.error("添加审批发生错误:{}", ae.getResult());
               return -1;
          } catch (IllegalArgumentException | IOException ile) {
               log.error("添加审批发生未知错误" + ile.getMessage(), ile);
               return -1;
          } catch (Exception e) {
               log.error("添加审批发生未知错误" + e.getMessage(), e);
               return -1;
          }
          return workflowTaskId;
     }

     /**
      * 撤销审批流程
      *
      * @param workTaskId            需要撤销的审批流程id
      * @param headers
      * @return
      */
     public boolean undoApprove(Long workTaskId, HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          HttpHeaders loggerHeaders = LogAspectHelper.putLogHeader();
          loggerHeaders = HeaderHelper.setMyHttpHeader(loggerHeaders, sysHeader);
          HttpEntity<String> entity = new HttpEntity<>(null, loggerHeaders);
          ResponseEntity<Result> resultResponseEntity = this.restTemplate.exchange(
                  String.format("http://%s/%s/%d?uc=%s",
                          workflow,
                          "approval/undo",
                          workTaskId, true),
                  HttpMethod.GET, entity, Result.class);
          return resultResponseEntity.getStatusCode() == HttpStatus.OK && resultResponseEntity.getBody() != null
                  && (resultResponseEntity.getBody().getCode() == 0 || resultResponseEntity.getBody().getCode() == 1203);
     }

     /**
      * 构建审批流对象
      * @param workflowId     工作流id
      * @param orgId          组织ID
      * @param title          对应操作数据的title，如活动的标题
      * @param actionId       操作数据的id，如活动的id，action_id
      * @param actionType     审批类型1.普通；2.文件类型审批；3.活动审批；4.活动栏目审批；5.发起活动（记实系统）；6.填写活动记实
      * @param actionBody     事件body
      * @param header         经过校验过的头信息
      * @return
      */
     public ApprovalBase buildApprovalBase(Long workflowId, Long orgId, String title, Long actionId, Integer actionType, String actionBody, Headers header) {
          ApprovalBase approvalBase = new ApprovalBase();
          approvalBase.setWorkflowId(workflowId);
          approvalBase.setOid(orgId);
          approvalBase.setTitle(title + "审批");
          approvalBase.setContext(title);
          approvalBase.setActionType(actionType);
          approvalBase.setActionId(actionId);
          approvalBase.setActionTitle(title);
          approvalBase.setActionBody(actionBody);
          approvalBase.setUserId(header.getUserId());
          approvalBase.setUserName(header.getUserName());
          approvalBase.setDepartmentId(header.getOid());
          approvalBase.setDepartmentName(header.getOrgName());
          return approvalBase;
     }

}
