package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.RedisConstant;
import com.goodsogood.ows.common.pojo.PageBean;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.MeetingPlanAddForm;
import com.goodsogood.ows.model.vo.OrgForm;
import com.goodsogood.ows.utils.DateUtils;
import com.goodsogood.ows.utils.RedisLockUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDateTime;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2018-10-23 13:55
 **/
@Service
@Log4j2
public class MeetingPlanService {

    private final MeetingPlanMapper mapper;
    private final MeetingTaskMapper meetingTaskMapper;
    private final MeetingRequireMapper meetingRequireMapper;
    private final MeetingOrgMapper meetingOrgMapper;
    private final MeetingPlanOrgService meetingPlanOrgService;
    private final MeetingPlanLimitMapper meetingPlanLimitMapper;
    private final MeetingPlanLimitTypeMapper meetingPlanLimitTypeMapper;
    private final MeetingOrgChangeLogMapper meetingOrgChangeLogMapper;
    private final Errors errors;
    private final IndexService indexService;
    private final StringRedisTemplate stringRedisTemplate;
    private final MeetingTaskServiceAsync meetingTaskServiceAsync;
    private final MeetingTaskService meetingTaskService;

    public MeetingPlanService(
            MeetingPlanMapper mapper,
            MeetingTaskMapper meetingTaskMapper,
            MeetingRequireMapper meetingRequireMapper,
            MeetingOrgMapper meetingOrgMapper, MeetingPlanOrgService meetingPlanOrgService, MeetingTaskService meetingTaskService,
            MeetingPlanLimitMapper meetingPlanLimitMapper,
            MeetingPlanLimitTypeMapper meetingPlanLimitTypeMapper,
            MeetingOrgChangeLogMapper meetingOrgChangeLogMapper,
            Errors errors,
            IndexService indexService, StringRedisTemplate stringRedisTemplate, MeetingTaskServiceAsync meetingTaskServiceAsync) {
        this.mapper = mapper;
        this.meetingTaskMapper = meetingTaskMapper;
        this.meetingRequireMapper = meetingRequireMapper;
        this.meetingOrgMapper = meetingOrgMapper;
        this.meetingPlanOrgService = meetingPlanOrgService;
        this.meetingTaskService = meetingTaskService;
        this.meetingPlanLimitMapper = meetingPlanLimitMapper;
        this.meetingPlanLimitTypeMapper = meetingPlanLimitTypeMapper;
        this.meetingOrgChangeLogMapper = meetingOrgChangeLogMapper;
        this.errors = errors;
        this.indexService = indexService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.meetingTaskServiceAsync = meetingTaskServiceAsync;
    }

    /**
     * 添加活动组织生活 调用用户中心获取组织信息
     *
     * @param sysHeader          HeaderHelper.SysHeader
     * @param meetingPlanAddForm MeetingPlanAddForm
     * @return Long 添加的记录数
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addMeetingPlan(HeaderHelper.SysHeader sysHeader, final MeetingPlanAddForm meetingPlanAddForm) {
        Long id = addMeetingPlan(sysHeader, meetingPlanAddForm, meetingPlanAddForm.getExecuteOrgs());
        // 添加成功后异步刷新移动端首页统计缓存
        indexService.collectRedis();
        return id;
    }


    /**
     * 添加活动组织生活
     *
     * @param sysHeader          用户信息
     * @param meetingPlanAddForm
     * @return 新增的类别id
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addMeetingPlan(HeaderHelper.SysHeader sysHeader,
                               final MeetingPlanAddForm meetingPlanAddForm, final List<OrgForm> executeOrgs) {
        StopWatch stopWatch = new StopWatch("添加组织生活");
        stopWatch.start("添加任务计划信息");
        final MeetingPlanEntity meetingPlanEntity = meetingPlanAddForm.toEntity();
        Date now = DateTime.now().toDate();
        meetingPlanEntity.setRegionId(sysHeader.getRegionId());
        meetingPlanEntity.setOrgId(sysHeader.getOid());
        meetingPlanEntity.setCreateUser(sysHeader.getUserId());
        meetingPlanEntity.setCreateTime(now);
        meetingPlanEntity.setOrgName(sysHeader.getOrgName());
        //新增组织生活时，默认启用
        meetingPlanEntity.setIsExecute((short) 1);
        //新增组织生活时，默认不删除
        meetingPlanEntity.setIsDel((short) 0);
        meetingPlanEntity.setSendType(meetingPlanAddForm.getSendType());
        mapper.insert(meetingPlanEntity);
        meetingPlanEntity.getMeetingTypes().forEach(meetingRequireEntity -> {
            meetingRequireEntity.setMeetingPlanId(meetingPlanEntity.getMeetingPlanId());
            // 2018-12-26 17:51:52 是否填写决议设置为0
            meetingRequireEntity.setIsWResolution((short) 0);
        });
        meetingRequireMapper.insertList(meetingPlanEntity.getMeetingTypes());
        //自动发放
        if (meetingPlanAddForm.getSendType() == 1) {
            //   保存 信息 t_meeting_plan_limit
            final MeetingPlanLimitEntity meetingPlanLimitEntity = new MeetingPlanLimitEntity();
            meetingPlanEntity.setPlanLimitEntity(meetingPlanLimitEntity);
            meetingPlanLimitEntity.setMeetingPlanId(meetingPlanEntity.getMeetingPlanId());
            meetingPlanLimitEntity.setIsRetire(meetingPlanAddForm.getAutoSendTypeForm().getIsRetire());
            meetingPlanLimitEntity.setPartyGroup(meetingPlanAddForm.getAutoSendTypeForm().getPartyGroup());
            meetingPlanLimitEntity.setPeriod(meetingPlanAddForm.getAutoSendTypeForm().getPeriod());
            meetingPlanLimitEntity.setCreateTime(LocalDateTime.now().toDate());
            meetingPlanLimitEntity.setCreateUser(sysHeader.getUserId());
            meetingPlanLimitMapper.insert(meetingPlanLimitEntity);
            //  保存 信息 t_meeting_plan_limit_type
            final List<MeetingPlanAddForm.MeetingPlanLimitTypeAddForm> mpltaList = meetingPlanAddForm.getAutoSendTypeForm().getOrgTypeList();
            if (!mpltaList.isEmpty()) {
                List<MeetingPlanLimitTypeEntity> mpltList = new ArrayList<>(mpltaList.size());
                mpltaList.forEach(entity -> {

                    final MeetingPlanLimitTypeEntity meetingPlanLimitTypeEntity = new MeetingPlanLimitTypeEntity();
                    meetingPlanLimitTypeEntity.setMeetingPlanLimitId(meetingPlanLimitEntity.getMeetingPlanLimitId());
                    meetingPlanLimitTypeEntity.setOrgTypeChild(entity.getOrgTypeChild());
                    meetingPlanLimitTypeEntity.setOrgTypeChildName(entity.getOrgTypeChildName());
                    mpltList.add(meetingPlanLimitTypeEntity);
                });
                meetingPlanLimitEntity.setLimitTypeEntityList(mpltList);
                meetingPlanLimitTypeMapper.insertList(mpltList);
            }
            // 手动发放 添加组织；自动发放，在创建任务时添加组织
        } else if (meetingPlanAddForm.getSendType() == 2) {
            this.meetingPlanOrgService.updateExcuteOrgs(executeOrgs, meetingPlanEntity.getMeetingPlanId());
        }

        stopWatch.stop();
        stopWatch.start("派发任务");
        meetingTaskServiceAsync.createThisMeetingTask(sysHeader, meetingPlanEntity, now, meetingPlanEntity.getSendType() == MeetingPlanEntity.SEND_TYPE_AUTO);
        stopWatch.stop();
        log.debug("新加计划总耗时->\n{}", stopWatch.prettyPrint());
        return meetingPlanEntity.getMeetingPlanId();
    }

    /**
     * 修改活动组织生活启用状态
     * 停用时间段的组织生活，在开始时间前，撤销任务
     * 启用时间段的组织生活，在结束时间前，派发任务
     *
     * @param sysHeader         用户信息
     * @param meetingPlanEntity 组织生活信息
     */
    public int executeMeetingPlan(HeaderHelper.SysHeader sysHeader, MeetingPlanEntity meetingPlanEntity) {
        //停用时间段的组织生活，在开始时间前，撤销任务
        if (meetingPlanEntity.getExecuteType() == 1 &&
                meetingPlanEntity.getIsExecute() == 0
                && DateUtils.gtToday(meetingPlanEntity.getStartTime())) {
            delMeetingTask(meetingPlanEntity);
        }
        //启用时间段的组织生活，在结束时间前，派发任务
        if (meetingPlanEntity.getExecuteType() == 1 &&
                meetingPlanEntity.getIsExecute() == 1
                && DateUtils.gtToday(meetingPlanEntity.getStartTime())) {
            meetingTaskServiceAsync.createThisMeetingTask(sysHeader, meetingPlanEntity, DateTime.now().toDate(),
                    meetingPlanEntity.getSendType() != null && meetingPlanEntity.getSendType() == MeetingPlanEntity.SEND_TYPE_AUTO);
        }
        meetingPlanEntity.setLastChangeUser(sysHeader.getUserId());
        meetingPlanEntity.setUpdateTime(DateTime.now().toDate());
        int n = mapper.updateByPrimaryKeySelective(meetingPlanEntity);
        // 修改成功后异步刷新移动端首页统计缓存
        indexService.collectRedis();
        return n;
    }

    /**
     * 删除任务组织生活
     *
     * @param meetingPlanEntity 活动
     */
    private void delMeetingTask(MeetingPlanEntity meetingPlanEntity) {
        Example example = new Example(MeetingTaskEntity.class);
        example.createCriteria().andEqualTo("meetingPlanId", meetingPlanEntity.getMeetingPlanId());
        meetingTaskMapper.deleteByExample(example);
    }

    /**
     * 逻辑删除活动组织生活
     * 时间段的组织生活
     *
     * @param sysHeader         用户信息
     * @param meetingPlanEntity 组织生活id
     */
    public int delMeetingPlan(HeaderHelper.SysHeader sysHeader, MeetingPlanEntity meetingPlanEntity) {
        //时间段的组织生活，在开始时间前，撤销任务
        if (meetingPlanEntity.getExecuteType() == 1 && meetingPlanEntity.getStartTime().getTime() > DateTime.now().toDate().getTime()) {
            delMeetingTask(meetingPlanEntity);
        }
        MeetingPlanEntity upMeetingPlanEntity = new MeetingPlanEntity();
        upMeetingPlanEntity.setMeetingPlanId(meetingPlanEntity.getMeetingPlanId());
        upMeetingPlanEntity.setIsDel((short) 1);
        upMeetingPlanEntity.setLastChangeUser(sysHeader.getUserId());
        upMeetingPlanEntity.setUpdateTime(DateTime.now().toDate());
        int delNum = mapper.updateByPrimaryKeySelective(upMeetingPlanEntity);
        // 删除成功后异步刷新移动端首页统计缓存
        indexService.collectRedis();
        return delNum;
    }

    /**
     * 查询组织生活详情
     */
    public MeetingPlanEntity detail(long id) {
        MeetingPlanEntity meetingPlanEntity = mapper.findById(id);
        if (meetingPlanEntity != null && meetingPlanEntity.getPlanLimitEntity() != null) {
            meetingPlanEntity.setLimitTypeList(meetingPlanLimitTypeMapper
                    .findByMeetingPlanLimitType(meetingPlanEntity.getPlanLimitEntity().getMeetingPlanLimitId()));
            meetingPlanEntity.getPlanLimitEntity().setLimitTypeEntityList(meetingPlanEntity.getLimitTypeList());
        }
        return meetingPlanEntity;
    }

    /**
     * 查询组织生活列表
     */
    public List<MeetingPlanEntity> listAllMeetingPlan(String name, Long oid) {
        return mapper.findAll(name, oid);
    }

    /**
     * 查询组织生活列表 分页查询
     */
    public Page<MeetingPlanEntity> listPageMeetingPlan(String name, Long oid, PageBean pageBean) {
        return PageHelper.startPage(pageBean.getPageNo(), pageBean.getPageSize())
                .doSelectPage(() -> this.mapper.findAll(name, oid));
    }

    /**
     * 查询组织生活列表
     *
     * @param tag 发起活动下拉框；2:任务完成情况下拉框
     */
    public List<MeetingPlanEntity> listAllMeetingPlan(long oid, short tag) {
        return meetingTaskMapper.findPlanIdByOrgId(oid, tag);
    }



    /**
     * 基本组织生活-执行组织分页查询
     *
     * @param orgName
     * @param page
     * @return
     */
   /* public Page<MeetingOrgEntity> executeOrgPage(final long meetingPlanId,
                                                 final String orgName, final int pageNo, final int pageSize) {
        final String orgNameSql = (orgName == null || StringUtils.isBlank(orgName)) ? null : orgName.trim();
        return PageHelper.startPage(pageNo, pageSize)
                .doSelectPage(() -> this.meetingOrgMapper.executeOrgPage(meetingPlanId, orgNameSql));
    }*/
    public List<MeetingOrgEntity> findExecuteOrg(Long meetingId, Integer page, String orgName) {

        Page<MeetingOrgEntity> objects = PageHelper.startPage(page, 10).doSelectPage(() -> meetingOrgMapper.findExecuteOrg(meetingId, orgName));

        return objects;
    }


    /**
     * 组织变更 更新活动类型关联组织
     *
     * @param meetingOrgChangeLogEntity 组织变更信息
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    void updateMeetingPlanOreByOrgChangeLock(HeaderHelper.SysHeader sysHeader, MeetingOrgChangeLogEntity meetingOrgChangeLogEntity) {
        if (meetingOrgChangeLogEntity == null
                || meetingOrgChangeLogEntity.getMeetingOrgChangeLogId() == null
                || meetingOrgChangeLogEntity.getOrgId() == null) {
            return;
        }
        // 获取锁 防止分布式部署情况下冲突
        // 1.获取锁 10分钟
        String uuid = UUID.randomUUID().toString();
        String key = "LOCK_" + RedisConstant.MEETING_ORG_CHANGE + meetingOrgChangeLogEntity.getOrgId();
        // 获取redis分布式锁
        boolean lock =
                RedisLockUtil.tryGetDistributedLock(stringRedisTemplate, key, uuid, 10 * 60 * 1000);
        if (lock) {
            try {
                updateMeetingPlanOreByOrgChanges(sysHeader, meetingOrgChangeLogEntity);
            } finally {
                // 释放锁
                RedisLockUtil.releaseDistributedLock(stringRedisTemplate, key, uuid);
            }
        }
    }

    /**
     * 组织变更 更新活动类型关联组织
     *
     * @param meetingOrgChangeLogEntity 组织变更信息
     */
    private void updateMeetingPlanOreByOrgChanges(HeaderHelper.SysHeader sysHeader, MeetingOrgChangeLogEntity meetingOrgChangeLogEntity) {
        StopWatch sw = new StopWatch("组织变更,更新活动类型关联组织");
        if (meetingOrgChangeLogEntity.getType() != null) {
            // 操作类型： 1:新增　 2:修改 3:删除
            Short type = meetingOrgChangeLogEntity.getType();
            // 删除
            if (type.equals(MeetingOrgChangeLogEntity.TYPE_DEL)) {
                // 删除关联的组织 不撤回已下发的任务
                delMeetingPlanOrg(meetingOrgChangeLogEntity.getOrgId());
            } else if (type.equals(MeetingOrgChangeLogEntity.TYPE_ADD)
                    || type.equals(MeetingOrgChangeLogEntity.TYPE_UPDATE)) {
                // 触发一次自动派发任务
                meetingTaskService.createAllMeetingTask(sysHeader, DateTime.now().toDate());
            }
        }
        // 更新记录为已处理
        MeetingOrgChangeLogEntity upEntity = new MeetingOrgChangeLogEntity();
        upEntity.setMeetingOrgChangeLogId(meetingOrgChangeLogEntity.getMeetingOrgChangeLogId());
        upEntity.setProcessTag(MeetingOrgChangeLogEntity.PROCESS_TAG_YES);
        meetingOrgChangeLogMapper.updateByPrimaryKeySelective(upEntity);
        log.debug("组织变更,更新活动类型关联组织耗时->\n{}", sw.prettyPrint());
    }

    /**
     * 判读执行组织中是否存在变更的组织
     *
     * @param meetingOrgChangeLogEntity 变更组织
     * @param meetingPlanEntity         计划
     * @return null 不存在 ，非null 存在，返回meetingOrgId
     */
    private Long existOrg(MeetingOrgChangeLogEntity meetingOrgChangeLogEntity, MeetingPlanEntity meetingPlanEntity) {
        if (meetingPlanEntity.getExecuteOrgs() == null
                || meetingPlanEntity.getExecuteOrgs().isEmpty()) {
            return null;
        }
        for (OrgForm orgForm : meetingPlanEntity.getExecuteOrgs()) {
            if (orgForm.getOrgId().equals(meetingOrgChangeLogEntity.getOrgId())) {
                return orgForm.getMeetingOrgId();
            }
        }
        return null;
    }

    /**
     * 删除活动类型的执行组织 根据关联id
     *
     * @param meetingOrgIds meetingOrgId
     */
    private void delMeetingPlanOrg(List<Long> meetingOrgIds) {
        if (meetingOrgIds != null && !meetingOrgIds.isEmpty()) {
            Example example = new Example(MeetingOrgEntity.class);
            example.createCriteria().andIn("meetingOrgId", meetingOrgIds);
            meetingOrgMapper.deleteByExample(example);
        }
    }

    /**
     * 删除活动类型的执行组织 根据组织id
     *
     * @param oigId 组织id
     */
    private void delMeetingPlanOrg(Long oigId) {
        if (oigId != null) {
            Example example = new Example(MeetingOrgEntity.class);
            example.createCriteria().andEqualTo("orgId", oigId);
            meetingOrgMapper.deleteByExample(example);
        }
    }


    /**
     * 校验变更的组织是否满足活动计划发放要求
     *
     * @param meetingOrgChangeLogEntity 组织变更信息
     * @param meetingPlanEntity         活动类型
     * @return true 满足 false 不满足
     */
    private boolean checkLimit(
            MeetingOrgChangeLogEntity meetingOrgChangeLogEntity, MeetingPlanEntity meetingPlanEntity) {
        MeetingPlanLimitEntity meetingPlanLimitEntity = meetingPlanEntity.getPlanLimitEntity();
        // 校验是否是下级组织
        String orgLevel = meetingOrgChangeLogEntity.getOrgLevel();
        // 上级组织不存在
        if (StringUtils.isBlank(orgLevel)) {
            return false;
        }
        List<String> orgLevels = Arrays.asList(orgLevel.split("-"));
        if (!orgLevels.contains(meetingPlanEntity.getOrgId().toString())) {
            return false;
        }

        // 校验组织类型是否满足限制
        List<MeetingPlanLimitTypeEntity> limitTypeEntities =
                meetingPlanLimitEntity.getLimitTypeEntityList();
        if (limitTypeEntities != null && !limitTypeEntities.isEmpty()) {
            List<Integer> orgTypeChildes =
                    limitTypeEntities
                            .stream()
                            .map(MeetingPlanLimitTypeEntity::getOrgTypeChild)
                            .collect(Collectors.toList());
            if (meetingOrgChangeLogEntity.getOrgTypeChild() == null
                    || !orgTypeChildes.contains(meetingOrgChangeLogEntity.getOrgTypeChild())) {
                return false;
            }
        }

        // 校验是否离退休
        if (meetingPlanLimitEntity.getIsRetire().equals(MeetingPlanLimitEntity.CONTAINS_NO)
                && meetingOrgChangeLogEntity.getIsRetire().equals(MeetingOrgChangeLogEntity.YES)) {
            return false;
        }
        if (meetingPlanLimitEntity.getIsRetire().equals(MeetingPlanLimitEntity.CONTAINS_ONLY)
                && !meetingOrgChangeLogEntity.getIsRetire().equals(MeetingOrgChangeLogEntity.YES)) {
            return false;
        }
        // 校验是否成立党小组
        if (meetingPlanLimitEntity.getPartyGroup().equals(MeetingPlanLimitEntity.CONTAINS_NO)
                && meetingOrgChangeLogEntity.getGroup().equals(MeetingOrgChangeLogEntity.YES)) {
            return false;
        }
        if (meetingPlanLimitEntity.getPartyGroup().equals(MeetingPlanLimitEntity.CONTAINS_ONLY)
                && !meetingOrgChangeLogEntity.getGroup().equals(MeetingOrgChangeLogEntity.YES)) {
            return false;
        }
        // 校验是否成立支委会
        if (meetingPlanLimitEntity.getPeriod().equals(MeetingPlanLimitEntity.CONTAINS_NO)
                && meetingOrgChangeLogEntity.getPeriod().equals(MeetingOrgChangeLogEntity.YES)) {
            return false;
        }
        return !meetingPlanLimitEntity.getPeriod().equals(MeetingPlanLimitEntity.CONTAINS_ONLY)
                || meetingOrgChangeLogEntity.getPeriod().equals(MeetingOrgChangeLogEntity.YES);
    }
}