package com.goodsogood.ows;

import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.filter.TranslateRequestFilter;
import com.goodsogood.ows.helper.SpringContextUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.Executor;

@SpringBootApplication(scanBasePackages = {"com.goodsogood.ows", "com.aidangqun.ows"})
@EnableDiscoveryClient
@EnableAsync
@EnableScheduling
public class OwsMeetingApplication {

    private final HttpComponentsClientHttpRequestFactory clientHttpRequestFactory;

    @Autowired
    public OwsMeetingApplication(HttpComponentsClientHttpRequestFactory clientHttpRequestFactory) {
        this.clientHttpRequestFactory = clientHttpRequestFactory;
    }

    @LoadBalanced
    @Bean //必须new 一个RestTemplate并放入spring容器当中,否则启动时报错
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory);
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        return restTemplate;
    }

    // 非LoadBalanced的RestTemplate实例
    @Bean
    public RestTemplate generalRestTemplate() {
        RestTemplate generalRestTemplate = new RestTemplate();
        generalRestTemplate.setRequestFactory(clientHttpRequestFactory);
        generalRestTemplate.setErrorHandler(new ClientExceptionHandler());
        return generalRestTemplate;
    }


    @Bean
    public RestTemplate myRestTemplate() {
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(30000);
        httpRequestFactory.setReadTimeout(30000);
        httpRequestFactory.setConnectTimeout(30000);
        return new RestTemplate(httpRequestFactory);
    }

    @Bean
    public FilterRegistrationBean<TranslateRequestFilter> translateRequestFilterRegistration() {
        FilterRegistrationBean<TranslateRequestFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new TranslateRequestFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(1);
        return registration;
    }


    @Bean
    public Executor indexCollectRedis() {
        return getExecutor(1, 1, 10, "INDEX_COLLECT_REDIS");
    }

    @Bean
    public Executor asyncGenerateExcelExecutor() {
        return getExecutor(5, 10, 100, "async-generate-excel-");
    }

    @Bean
    public Executor sbwAsync() {
        return getExecutor(5, 10, 100, "sbw_async_");
    }

    @Bean
    public Executor tobaccoAsync() {
        return getExecutor(5, 10, 100, "tobacco_async_");
    }

    @Bean
    public Executor lifeAsync() {
        return getExecutor(5, 10, 100, "life_async_");
    }

    @Bean
    public Executor orgLifeAsync() {
        return getExecutor(5, 10, 100, "life_async_");
    }

    @Bean
    public Executor wordDownAsync() {
        return getExecutor(5, 10, 100,"word_down_async_");
    }

    @Bean
    public Executor meetingScoreAsync() {
        return getExecutor(15, 30, 200,"meeting_score_async_");
    }

    @Bean
    public Executor commentAsync() {
        return getExecutor(5, 10, 100, "comment_async_");
    }

    @Bean
    public Executor gloryAsync() {
        return getExecutor(1, 5, 100, "glory_async_");
    }

    @Bean
    public Executor commendPenalizeAsync() {
        return getExecutor(1, 5, 100, "commend_penalize_async");
    }

    /**
     * 线程池
     *
     * @param i  core pool
     * @param i2 max pool
     * @param i3 queue
     * @param s  name
     * @return new ThreadPoolTaskExecutor
     */
    private Executor getExecutor(int i, int i2, int i3, String s) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(i);
        executor.setMaxPoolSize(i2);
        executor.setQueueCapacity(i3);
        executor.setThreadNamePrefix(s);
        executor.initialize();
        return executor;
    }

    public static void main(String[] args) {
        //关闭spring自动装载日志
//        System.setProperty("org.springframework.boot.logging.LoggingSystem", LoggingSystem.NONE);
        // rocketmq log level
//        System.setProperty(ClientLogger.CLIENT_LOG_LEVEL, "ERROR");
        ApplicationContext app = SpringApplication.run(OwsMeetingApplication.class, args);
        SpringContextUtil.setApplicationContext(app);
    }
}
