#online
spring:
  cloud:
    config:
      #失败就停止
      fail-fast: true
      uri: http://configserver.cqjgdj.gov.online:8194
      label: v1.0.5
  profiles:
    active: online
  application:
    name: meeting
logging:
  level:
    .: DEBUG

#端口
sys-port: 8140
#节点
sys-id: szf1
#局点
sys-channel: goodsogood.online
#config dir
sys-config-dir: -szf
#数据库
sys-db0:
    db-url: rm-bp1im6v8b0g73q0of.mysql.rds.aliyuncs.com
    db-name: gs_ows_szf_online
    db-user: gszw_owsszf
    db-password: l*A@p8Szn+&p&h#H
#redis
sys-redis-config:
    database: 9
    password: G3S@Redis
    host: r-bp14672c3a3de1c4.redis.rds.aliyuncs.com
    port: 6379

#是否开启定时任务
sys-scheduler: true

