#通用配置
# swagger: #swagger
#   show: true
#数据库 - 达梦数据库（唯一数据源）

sys-dm-db:

  db-url: 113.204.105.250:55237

  db-name: GS_OWS_ZY_GRAY

  db-user: SYSDBA

  db-password: Dameng@123456
#系统级和业务特殊异常（表单/请求验证异常请配置在ValidationMessages.properties中，默认错误类型为9906）
#支持${index}方式替换，index从0开始
error-message:
  errors:
    9999: 当前接口已废除
    9913: 数据错误，请联系管理员
    9901: 未知错误
    9902: 当前请求过多或服务器繁忙，请稍后再试
    9903: 调用外部接口发生错误
    9904: 当前客户端版本已经过期
    9905: 登录失效请重新登录
    9906: 请求参数发生错误
    9907: 远程服务调用失败
    9404: ${0}找不到
    1336: 用户当前位置不在签到范围内
    1801: ${0}已经存在
    1802: 该${0}已被使用，不能${1}
    1803: 所选活动类型不属于同一类别
    1804: 请求参数错误，${0}
    1805: 发起活动失败，新增审批流程出错
    1806: ${0}活动失败，更新审批流程出错
    1807: 该活动信息不能${0}
    1808: ${0}不能重复添加
    1809: 执行组织不能包含自己所在组织
    1810: 当前活动类型组合已存在，请重新选择！
    1811: 非待举办活动不能发送通知
    1812: 执行组织不能为空
    1813: 选择的组织类型未找到相关组织
    1814: 自动发放活动类型值不能为空
    1815: 规则类型选择错误

    1820: 活动状态不匹配，无法执行请假操作
    1821: 已执行过请假操作，无法再次请假
    1822: 请假状态不匹配，无法撤销请假
    1823: 请假状态不匹配，无法审批
    1824: 无权限修改此请假
    1825: 退回理由不能为空
    1826: 无权限审批此纪实结果
    1827: 操作失败，该活动已被他人处理

    1828: 还没有到签到时间
    1829: 签到时间已经结束
    1830: 该活动不需要签到
    1831: 不在签到名单中
    1832: 请勿重复签到
    1833: 活动状态不匹配，无法签到
    1834: 没有请假权限
    1835: 您的位置不在签到范围内
    1836: 活动状态不匹配，无法撤销请假

    1920: 开始时间大于结束时间
    1921: 该工作任务已被使用，不能删除！
    1922: 该工作任务已被使用，不能修改！
    1923: 解析请求头出错
    1924: 填报纪实结果，新增纪实报表审批流程出错
    1925: 该活动已填纪实报表
    1926: 请填写决议
    1927: 没有修改权限
    1928: 填写纪实考核报表，修改活动出错
    1929: 填写纪实考核报表，取消审批流程出错
    1930: 该组织人员无操作纪实考核权限
    1931: 当前状态无操作权限
    1932: 任务内容为单选或者多选时，选项不能为空
    1933: 请检查活动内容是否填写完整
    1934: 活动开始时间还未到，不能填写纪实报告
    1935: 已填报过纪实报告
    1936: 不能对本组织的活动进行检查或退回，请通知上级组织操作
    1950: 未找到当前组织
    1951: 活动类型未找到
    1952: 仅允许取消当前季度的活动
    1953: 取消活动出错,纪实考核报表信息未找到
    1954: 活动内容不能为空
    1837: 请选择党小组
    1838: 请选择支委会届次

    2001: 处理意见不能为空
    2002: 奖励类型不能为空
    2003: 当前党员在该年度已存在评议结果
    2004: 处理意见应该为空
    2005: 导出失败
    2006: 该组织当年已经有评议记录 不能重复添加
    2007: 需要填写讲课人
    2008: 需要填写讲课标题

    3001: 新建任务失败${0}
    3002: 缺少必填项
    3003: 缺少不公开原因
    3004: 缺少拒绝原因
    3005: 缺少处置内容
    3006: 删除失败${0}
    3007: 查询失败${0}

    1860: 该活动不允许签到列表之外的用户签到

#关闭endpoint的鉴权
management:
  health:
    elasticsearch:
      enabled: false
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: '*'
      base-path: '/'
      path-mapping:
        health: ows-meeting/root/ping
        info: actuator/info
        metrics: actuator/metrics
        env: actuator/env
        beans: actuator/beans
        configprops: actuator/configprops
        auditevents: actuator/auditevents
        flyway: actuator/flyway
        threaddump: actuator/threaddump
        logfile: actuator/logfile
        prometheus: actuator/prometheus

#mapper,需要单独写批量mapper才需要配置
mapper:
  mappers:
    - com.goodsogood.ows.mapper.MyMapper
    - com.goodsogood.ows.mapper.TopicOrgInsertListMapper
    - com.goodsogood.ows.mapper.TopicLogInsertListMapper
    - com.goodsogood.ows.mapper.MeetingRequireInsertListMapper
    - com.goodsogood.ows.mapper.MeetingOrgInsertListMapper
    - com.goodsogood.ows.mapper.TypeGroupInsertListMapper
    - com.goodsogood.ows.mapper.MeetingTaskInsertListMapper
    - com.goodsogood.ows.mapper.TopicLogInsertListMapper
    - com.goodsogood.ows.mapper.MeetingUserInsertListMapper
    - com.goodsogood.ows.mapper.MeetingTypeInsertListMapper
    - com.goodsogood.ows.mapper.MeetingTopicInsertListMapper
    - com.goodsogood.ows.mapper.MeetingResolutionFileInsertListMapper
    - com.goodsogood.ows.mapper.MeetingPlanLimitTypeMapper
    - com.goodsogood.ows.mapper.MeetingOrgGroupInsertListMapper
    - com.goodsogood.ows.mapper.MeetingOrgPeriodInsertListMapper
    - com.goodsogood.ows.mapper.MeetingContactLeaderInsertListMapper
    - com.goodsogood.ows.mapper.MeetingResultFileInsertListMapper
  not-empty: true
  identity: MYSQL

sys-scheduler: false

scheduler:
  execute: ${sys-scheduler} #定时任务开关
  checkTaskComplete: ${sys-scheduler} #检查任务是否完成
  cron:
    executeScheduled: 0 0 0 * * ? #定时派发任务，每天凌晨执行
    checkTaskComplete: 0 0 0 * * ? #检查任务是否完成，每天凌晨执行
    statsMeetingTask:  0 0 0/2 * * ? #会议任务完成情况统计 每2小时执行一次
  statsMeetingTask:
    run: false
    cron: 0 0 0/2 * * ? #会议任务完成情况统计 每2小时执行一次
  notice: #未完成任务通知  v1.0.4
    run: ${sys-scheduler} #定时任务开关
    cron: 0 0 10 25 * ? #每月25号10点执行
    page-szie: 50 #获取数据分页大小
    #未完成通知跳转前缀
    prefix: https://dangjian.cq.tobacco.gov.cn/html/ssr?redirect_url=
    #未完成通知跳转活动发起(需要urlEncoding)
    szf-redirect-url-add: /ssr/meeting-entering/1
    redirect-url-add: /ssr/meeting-entering/1
    types: #id:消息号， msg-template-id：模板号，msg-channel-type：渠道类型 1:短信 2:微信 3:主题推送 4:钉钉，quarter：季度 1是2否，biz：业务描述
      19:  #区县编号
        - { id: 1,msg-template-id: 16, msg-channel-type: 4, quarter: 1, biz: 'OrgMemberMeeting', name: '党支部党员大会'} #党支部党员大会
        - { id: 2,msg-template-id: 17, msg-channel-type: 4, quarter: 2, biz: 'OrgPeriodMember', name: '党支部委员会会议'} #党支部委员会会议
        - { id: 3,msg-template-id: 18, msg-channel-type: 4, quarter: 2, biz: 'OrgGroupMeeting', name: '党小组会'} #党小组会
        - { id: 4,msg-template-id: 19, msg-channel-type: 4, quarter: 1, biz: 'PartySubject', name: '党课'} #党课
        - { id: 5,msg-template-id: 173, msg-channel-type: 4, quarter: 2, biz: 'PartyDay', name: '主题党日'} #主题党日
  topic-push: #主题推送
    run: ${sys-scheduler} #定时任务开关
    cron: 0 0 14 1,8,15,22,29 * ? #每7天执行一次
  refreshCacheOfIndexCollect: #首页汇总缓存刷新
    run: ${sys-scheduler} #定时任务开关
    cron: 0 0/30 * * * ? #半小时执行一次
  user-comment:
    run: ${sys-scheduler} #定时任务开关
    cron: 0 50 23 * * ? #每天晚上11点50
  org-debrief-review:
    run: ${sys-scheduler} #定时任务开关
    cron: 0 50 23 * * ? #每天晚上11点50

  committeeAndGroupScore: #个支委会人员和党小组组长增加积分
    run: ${sys-scheduler} #定时任务开关
    cron: 0 0 3 * * ? #每天早上3点执行

  unfinishedGroupScore: #未开展党小组会，组长扣减积分
    run: ${sys-scheduler} #定时任务开关
    cron: 0 0 2 1 * ? #每月1号早上2点执行
  comment:
    run: ${sys-scheduler} #定时任务开关
    cron: 0 0 0 1 1 *
    region-id: 19
  dingEventSync: #有日程的活动结束后同步钉钉日程的签到状态定时任务
    runFlag: false
    cron: '0 0 6-23/1 * * ?'
  create-meeting-work-point:
    run: ${sys-scheduler} #定时任务开关
    cron: 0 0 0 1 1 *
    region-id: 19
  top-priority-spider:
    run: true
    cron: 0 0/1 * * * ?
    region: 86
  video-conference:
    run: true
    cron: 10 0 * * * ?
    region: 86
scope: 1000 #签到距离

top-priority:
  # 专题读书班 tag_id
  special-reading: 86 # 灰度是69

tbc-city-id: 9

# 组织生活定时配置

gray: 1

#奖惩登记

commend-penalize:

  workflow-id-normal: 2

  workflow-id-other: 1

  workflow-type-normal: 9  #两层审批

  workflow-type-other: 11  #一层审批

  user-score-list:

    - { key: ********, score: 20 }

    - { key: ********, score: 10 }

    - { key: ********, score: 10 }

    - { key: ********, score: 5 }

    - { key: ********, score: 5 }

    - { key: ********, score: 2 }

    - { key: 10480207, score: 0 }

    - { key: 1049010101, score: 10 }

    - { key: 1049010102, score: 20 }

    - { key: 1049010103, score: 30 }

    - { key: 1049010104, score: 50 }

    - { key: 1049010201, score: 5 }

    - { key: 1049010202, score: 10 }

    - { key: 1049010203, score: 20 }

    - { key: **********, score: 40 }

  org-score-list:

    - { key: ********, score: 20 }

    - { key: ********, score: 10 }

    - { key: ********, score: 10 }

    - { key: ********, score: 5 }

    - { key: ********, score: 5 }

    - { key: ********, score: 2 }

    - { key: 1049010101, score: 10 }

    - { key: 1049010102, score: 20 }

    - { key: 1049010103, score: 30 }

    - { key: 1049010104, score: 50 }

    - { key: 1049010201, score: 5 }

    - { key: 1049010202, score: 10 }

    - { key: 1049010203, score: 20 }

    - { key: **********, score: 40 }

  change-org-score:

    - { key: ********, score: 10 }

    - { key: ********, score: 5 }

    - { key: ********, score: 5 }

    - { key: ********, score: 3 }

    - { key: ********, score: 3 }

    - { key: ********, score: 1 }

file-path: /home/<USER>/appdata/tmp/ows-meeting-gray

#因为多个服务器共用的一个rabbitmq，所以需要区分rabbitmq队列

rabbitmq-queue-sign:

  meetingScore: meeting_queue_meeting_score_cqyc_gray

  meetingScoreRouting: meeting_routing_meeting_score_cqyc_gray

sys-rabbit:

  address: **************

  port: 5673

  username: gs_admin

  password: CFXo56LfXxVdsGb

lifemeeting:

  file:

    num:

      usual: 20

      special: 5
# 会议类型中文名称，防止id发生变化之后导致无法获取type_id
meeting:
  task:
    counts[0]:
      level: 1
      type: 党支部党员大会
      count-type: 1
    counts[1]:
      level: 2
      type: 党支部委员会会议
      count-type: 0
    counts[2]:
      level: 3
      type: 党小组会
      count-type: 0
    counts[3]:
      level: 4
      type: 党课
      count-type: 1
    counts[4]:
      level: 5
      type: 主题党日
      count-type: 0
  sms.leader.send: false
# root组织id
root-org-id: 3

meeting-score-conf-new:

  score-conf:

    86: # 区县编号

      basics-score:

        # score_type: 积分类型 4.支委会成员每年基础分 5.党小组长每年基础分，cycle: 积分周期 1 月度 2 季度 3 年度

        # score: 单次加分值，log_txt: 积分备注  type:1-只加积分  2-只同步到doris  3-既要加积分，又要同步doris ruleId:t_score_rule_explain中的主鍵

        - { score_type: 4, cycle: 3, score: 20, min: 0, max: 20, log_txt: '支委会成员基础分',type: 2,ruleId: 17 }

        - { score_type: 5, cycle: 3, score: 20, min: 0, max: 20, log_txt: '党小组组长基础分',type: 2,ruleId: 11 }

      user-score:

        # meeting_type: 活动类型 1.党支部党员大会 2.党支部委员会会议 3.党小组会 4.党课  5.主题党日，

        # type-id: 对应类型编号(数据里t_type表)，score_type: 积分类型 1.党员考勤积分 2.讲课人积分 3.党小组组长开展积分，

        # cycle: 积分周期 1 月度 2 季度 3 年度，score: 单次加分值，min: 积分下限，max: 积分上限，log_txt: 积分备注

        - { meeting_type: 1,type-id: 1,score_type: 1, cycle: 2, score: 5, min: 0, max: 5, log_txt: '积分任务_按时参加支部党员大会',remark: '积分任务_按时参加支部党员大会',type: 3,ruleId: 10 }

        - { meeting_type: 2,type-id: 2,score_type: 1, cycle: 1, score: 5, min: -5, max: 0, log_txt: '支部委员会党员考勤积分',type: 2,ruleId: 18 }

        - { meeting_type: 3,type-id: 3,score_type: 1, cycle: 1, score: 5, min: 0, max: 5, log_txt: '党小组会党员考勤积分',type: 2,ruleId: 12 }

        - { meeting_type: 3,type-id: 3,score_type: 3, cycle: 1, score: 5, min: -5, max: 0, log_txt: '党小组组长开展积分',type: 2,ruleId: 14 }

        - { meeting_type: 4,type-id: 4,score_type: 1, cycle: 2, score: 5, min: 0, max: 5, log_txt: '积分任务_按时参加党课',remark: '积分任务_按时参加党课',type: 3,ruleId: 13 }

        - { meeting_type: 4,type-id: 4,score_type: 2, cycle: 3, score: 10, min: 0, max: 20, log_txt: '积分任务_主动讲党课',remark: '积分任务_主动讲党课',type: 3,ruleId: 16 }

        - { meeting_type: 5,type-id: 5,score_type: 1, cycle: 1, score: 5, min: 0, max: 5, log_txt: '积分任务_按时参加主题党日活动',remark: '积分任务_按时参加主题党日活动',type: 3,ruleId: 15 }

      org-score:

        # meeting_type: 活动类型 1.党支部党员大会 2.党支部委员会会议 3.党小组会 4.党课  5.主题党日，

        # type-id: 对应类型编号(数据里t_type表)，score_type: 积分类型 1.组织生活开展积分，

        # cycle: 积分周期 1 月度 2 季度 3 年度，score: 单次加分值，min: 积分下限，max: 积分上限，log_txt: 积分备注

        - { meeting_type: 1,type-id: 1,score_type: 1, cycle: 2, score: 5, min: 0, max: 5, log_txt: '党支部党员大会开展积分',type: 2,ruleId: 41 }

        - { meeting_type: 2,type-id: 2,score_type: 1, cycle: 1, score: 5, min: 0, max: 5, log_txt: '支部委员会开展积分',type: 2,ruleId: 42 }

        - { meeting_type: 3,type-id: 3,score_type: 1, cycle: 1, score: 5, min: 0, max: 5, log_txt: '党小组会开展积分',type: 2,ruleId: 43 }

        - { meeting_type: 4,type-id: 4,score_type: 1, cycle: 2, score: 5, min: 0, max: 5, log_txt: '党课开展积分',type: 2,ruleId: 44 }

        - { meeting_type: 5,type-id: 5,score_type: 1, cycle: 1, score: 5, min: 0, max: 5, log_txt: '主题党日开展积分',type: 2,ruleId: 45 }


#开发环境
server:
  port: 8140
logging:
  level:
    .: DEBUG
  config: classpath:log4j2-dev.xml

spring:
  jackson:
    #date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Chongqing
  application:
    name: meeting-tc

  # mq配置

  rabbitmq:

    host: ${sys-rabbit.address}

    port: ${sys-rabbit.port}

    username: ${sys-rabbit.username}

    password: ${sys-rabbit.password}

    listener:

      simple:

        acknowledge-mode: manual  # 手动ack

        retry:

          enabled: true

          max-attempts: 3

          max-interval: 10000   # 重试最大间隔时间

          initial-interval: 2000  # 重试初始间隔时间

          multiplier: 2 # 间隔时间乘子，间隔时间*乘子=下一次的间隔时间，最大不能超过设置的最大间隔时间



  #mysql
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    username: ${sys-dm-db.db-user}
    url: jdbc:dm://${sys-dm-db.db-url}/${sys-dm-db.db-name}?stringtype=unspecified&currentSchema=GS_OWS_ZY_TEST
    password: ${sys-dm-db.db-password}
    driver-class-name: dm.jdbc.driver.DmDriver
    hikari:
      connection-test-query: SELECT 1 FROM DUAL
      minimum-idle: 1
      maximum-pool-size: 5
      pool-name: hikari-dm-pool
      idle-timeout: 60000
      max-lifetime: 2000000
      connection-timeout: 60000
      register-mbeans: true
      data-source-properties:
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        cachePrepStmts: true
        useServerPrepStmts: true
  jpa:
    show-sql: true
    database-platform: org.hibernate.dialect.DmDialect
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.DmDialect
        jdbc:
          time_zone: Asia/Shanghai
        format_sql: true
        enhancer:
          enableDirtyTracking: false
          enableLazyInitialization: false
        connection:
          is-connection-validation-required: true
          physical_naming_strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
  data:
    mongodb:
      host: **************
      port: 27017
      database: gs_ows_zy_test
      username: zy_test_uto
      password: Qwer1234..
  #正式环境 redis
  redis:
    # test
    database: 86
    password: **********
    host: **************
    port: 16379
  messages:
    basename: messages
    encoding: UTF-8
  cloud:
    config:
      enabled: false

#正式环境 关闭 swagger
swagger:
  show: false

#正式环境 eureka客户端，服务自动发现
eureka:
  client:
    service-url: # 指定服务注册中心
      defaultZone: http://**************:8104/eureka/
  instance:
    health-check-url-path: /ows-meeting/root/ping
    appname: meeting-tc
    hostname: meeting.goodsogood.online
    #节点的名称
    instance-id: ${eureka.instance.appname}@${eureka.instance.hostname}
    leaseRenewalIntervalInSeconds: 10
    leaseExpirationDurationInSeconds: 30

#template: #推送模板
#  default-oid: 3
#  template-ids:
#    3: 0wdDhN0o1jidlq2RBQHHGkOV6_eeTlKM0MxixWKFj1U #市直机关 会议通知模版ID
app:
  url: https://owsswx.cqjgdj.gov.cn/ssr  #模板消息跳转url

#正式环境 其他模块名称（eureka.client.instance.appname）
tog-services:
  #文件中心
  file-center: 127.0.0.1:8811
  #网宣平台
  cms-plat: CMS-PLAT-DEV
  #活动中心
  activity-plat: ACTIVITY-PLAT-DEV
  #工作流服务
  workflow: WORKFLOW-DEV
  #积分中心
  credit-center: CREDIT-CENTER-DEV
  #用户中心
  user-center: 127.0.0.1:8106
  #推送中心
  push-center: PUSH-CENTER-DEV
  #统计
  sas: SAS-DEV #v1.0.4


#saas配置
saas:
  label: v4.0.0

excludeOrgId: 1

#临时目录
temp-path: C:\Users\<USER>\Desktop\work

#组织类型

org-type:

  #党委

  communist-child[0]: 10280301

  communist-child[1]: 10280310

  communist-child[2]: 10280322

  #党总支

  general-branch-child[0]: 10280303

  general-branch-child[1]: 10280308

  general-branch-child[2]: 10280311

  general-branch-child[3]: 10280318

  #党支部

  branch-child[0]: 10280304

  branch-child[1]: 10280309

  branch-child[2]: 10280314

  branch-child[3]: 10280315

  branch-child[4]: 10280319

  #党组

  party-group[0]: 10280312

  party-group[1]: 10280313

  #委员会

  committee[0]: 10280316

  committee[1]: 10280317

  #不统计党组织类型

  no-statistics-child[0]: 10280329

  no-statistics-child[1]: 10280330

  #党小组

  communist-group[0]: 10280306

  #顶级党委

  top-group: 10280307

thread:
  pools:
    sbwAsync:
      coreSize: 4
      maxPoolSize: 6
      threadNamePrefix: sbw_async_
      queueSize: 5000
      keepAliveTime: 60

scheduled:
  sbwEnable: false
  sbw: 0 0 2 ? * *

# 新增
sign-qr-code-url: https://dangjian.cq.tobacco.gov.cn/html/ssr?redirect_url=

ding-talk:
  agent-id: 1253521850
  app-key: dingxadfexkmxpf3lviq
  app-secret: QQbkew6ybcD7Rm7C4LJ2BLYSAOe_6xYouiLABbXX9pepkxQOKH6xFEykenTRMbkm
  token: 9PAtrj7K2WeahfKBo97
  aes-key: 4msSmgAm4cHIxmsTvwGgg29yUY4JHXvXuboBYTyG3Rk
  url: https://dangjian.cq.tobacco.gov.cn/zuul/owsz/cb/dingTalk/getCallBack/19

remote-spider:
  region:
    86:
      LpSpeech:
        source: 中烟官网
        column-name:
          - 第一议题
  url-host: SPIDER
  protocol:

orglifemeeting:
  file:
    num:
      usual: 20
      special: 5

tzd:
  app-key: ding9df5cxztput97hv0
  app-secret: eEMJ0_zUOlVJ2GWwlYuabsGPb2ZFJMmrlXjGrhShInnccSW9r9H_KxVt-PRd96gr