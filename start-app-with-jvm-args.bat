@echo off
echo Starting OWS Meeting Application with explicit Java 17 compatibility parameters...

REM Java 17 compatibility parameters for MyBatis and reflection libraries
set JAVA_OPTS=--add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.text=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/sun.nio.ch=ALL-UNNAMED --add-opens java.desktop/java.awt.font=ALL-UNNAMED

echo Using Java options: %JAVA_OPTS%

REM Start the application using Maven with explicit JVM arguments
mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

pause
