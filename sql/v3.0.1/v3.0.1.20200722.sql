ALTER TABLE `t_meeting`
ADD COLUMN `sel_contact_leaders` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否选择联系领导	0：不选择，1：选择' AFTER `last_change_user`;

ALTER TABLE `t_topic` ADD COLUMN `notice_type` TINYINT ( 1 ) NULL
COMMENT '通知方式: 1.短信通知 2.微信通知（派发任务时的默认值）。自己添加时为空' AFTER `last_change_user`;

ALTER TABLE `t_topic_log`
ADD COLUMN `topic_org_id` bigint(20) NULL COMMENT '任务组织关联表id.' AFTER `last_change_user`,
ADD INDEX `topic_org_id`(`topic_log_id`);

ALTER TABLE `t_topic_log`
MODIFY COLUMN `meeting_topic_id` bigint(20) NULL COMMENT '工作任务关联id' AFTER `topic_log_id`;

ALTER TABLE `t_topic_log_file`
ADD COLUMN `topic_org_id` bigint(20) NULL AFTER `last_change_user`,
ADD INDEX `topic_org_id`(`topic_org_id`);

ALTER TABLE `t_topic_log_file`
MODIFY COLUMN `meeting_topic_id` bigint(20) NULL AFTER `topic_log_id`;


CREATE TABLE `t_meeting_contact_leader` (
  `meeting_contact_leader_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `meeting_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `org_id` bigint(20) NOT NULL,
  `user_name` varchar(900) NOT NULL COMMENT '用户名',
  `org_name` varchar(255) NOT NULL,
  `is_del` tinyint(1) NOT NULL,
  `create_time` datetime NOT NULL,
  `create_user` bigint(20) NOT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`meeting_contact_leader_id`),
  KEY `meeting_id` (`meeting_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系领导信息表';


CREATE TABLE `t_meeting_result_file` (
  `meeting_result_file_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `meeting_id` bigint(20) NOT NULL COMMENT '会议id 冗余',
  `id` bigint(20) NOT NULL COMMENT '文件id',
  `name` varchar(900) NOT NULL COMMENT '附件名称',
  `path` varchar(900) NOT NULL COMMENT '附件路径',
  `file_name` varchar(600) NOT NULL COMMENT '文件原名称',
  `size` bigint(20) NOT NULL COMMENT '文件大小(byte)',
  `is_del` tinyint(1) NOT NULL COMMENT '逻辑删除 0:未删除 1.删除',
  `create_time` datetime NOT NULL,
  `create_user` bigint(20) NOT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`meeting_result_file_id`),
  KEY `meeting_id` (`meeting_id`),
  KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='纪实结果附件表';
