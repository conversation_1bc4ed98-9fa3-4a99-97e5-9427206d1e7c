ALTER TABLE `t_meeting`
ADD COLUMN `has_lecturer` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要填写讲课人 0 否(默认)；1 是' AFTER `last_change_user`,
ADD COLUMN `has_lecture_title` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要填写讲课标题 0 否（默认）；1 是' AFTER `has_lecturer`,
ADD COLUMN `lecture_title` varchar(255) NULL COMMENT '讲课标题(限制50字)' AFTER `sel_contact_leaders`;

ALTER TABLE `t_meeting_user`
MODIFY COLUMN `tag` tinyint(1) NOT NULL COMMENT '参会人员与列席人员标识（1：参会人员与列席人员标识（1：记录人员，2：主持人，3：参与人员 ，4：列席人员，5：讲课人）' AFTER `sign_status`;

ALTER TABLE `t_type`
ADD COLUMN `has_lecturer` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要填写讲课人 0 否(默认)；1 是' AFTER `last_change_user`,
ADD COLUMN `has_lecture_title` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要填写讲课标题 0 否（默认）；1 是' AFTER `has_lecturer`;