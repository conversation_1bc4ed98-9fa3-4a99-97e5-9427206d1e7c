CREATE TABLE `t_meeting_draft` (
  `draft_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `content` longtext NOT NULL COMMENT '内容',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `org_id` bigint(20) NOT NULL COMMENT '组织id',
  `region_id` bigint(20) NOT NULL COMMENT '区县id',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`draft_id`),
  UNIQUE KEY `region_id_user_id_unique` (`region_id`,`org_id`,`user_id`)
) ENGINE=InnoDB COMMENT='纪实草稿表';