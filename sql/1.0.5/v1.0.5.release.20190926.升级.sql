ALTER TABLE `t_type`
ADD COLUMN `code` tinyint(2) NOT NULL DEFAULT -1 COMMENT '选择人员 规则类型：-1 未指定（默认） 1.必须选择党小组 2.必须选择支委会届次 3.必须选择组织所有成员' AFTER `category`;

CREATE TABLE t_meeting_org_group (
  meeting_org_group_id  bigint(20) NOT NULL AUTO_INCREMENT, 
  meeting_id            bigint(20) NOT NULL, 
  org_group_id          bigint(20) NOT NULL comment '党小组ID', 
  org_group_name        varchar(200) NOT NULL comment '党小组名称', 
  status                tinyint(1) NOT NULL comment '状态 1.有效; 2.删除', 
  org_group_create_date datetime NOT NULL comment '党小组创建时间', 
  create_time           datetime NULL, 
  create_user           bigint(20), 
  PRIMARY KEY (meeting_org_group_id), 
  INDEX (meeting_id), 
  INDEX (org_group_id), 
  INDEX (status)) comment='会议关联的党小组';



CREATE TABLE t_meeting_org_period (
  meeting_org_period_id bigint(20) NOT NULL AUTO_INCREMENT,
  meeting_id            bigint(20) NOT NULL,
  period_id             bigint(20) NOT NULL comment '届次ID',
  status                tinyint(1) NOT NULL comment '状态 1.有效; 2.删除',
  start_time            datetime NOT NULL comment '届次开始时间',
  end_time              datetime NOT NULL comment '届次结束时间',
  create_time           datetime NULL,
  create_user           bigint(20),
  PRIMARY KEY (meeting_org_period_id),
  INDEX (meeting_id),
  INDEX (period_id),
  INDEX (status)) comment='会议关联的支委会届次';
