-- t_meeting_plan 添加send_type
ALTER TABLE `t_meeting_plan`
ADD COLUMN `send_type`  tinyint(1) NULL DEFAULT 2 COMMENT '发放任务类型：1 自动发放; 2 手动发放(默认)' AFTER `is_execute`;

-- 回滚sql
-- ALTER TABLE `t_meeting_plan` DROP COLUMN `send_type`;

-- ----------------------------
-- Table structure for t_meeting_plan_limit
-- ----------------------------
CREATE TABLE `t_meeting_plan_limit` (
  `meeting_plan_limit_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `meeting_plan_id` bigint(20) NOT NULL COMMENT '活动计划id',
  `is_retire` tinyint(2) NOT NULL COMMENT '离退休组织：1 包含; 2 不包含; 3 仅包含',
  `party_group` tinyint(2) NOT NULL COMMENT '已成立党小组 1 包含; 2 不包含; 3 仅包含',
  `period` tinyint(2) NOT NULL COMMENT '已成立支委会 1 包含; 2 不包含; 3 仅包含',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建用户',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `last_change_user` bigint(20) DEFAULT NULL COMMENT '更新用户',
  PRIMARY KEY (`meeting_plan_limit_id`),
  KEY `meeting_plan_id` (`meeting_plan_id`),
  KEY `is_retire` (`is_retire`),
  KEY `group` (`party_group`),
  KEY `period` (`period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动发放的活动类型 组织条件限制';


-- ----------------------------
-- Table structure for t_meeting_plan_limit_type
-- ----------------------------
CREATE TABLE `t_meeting_plan_limit_type` (
  `meeting_plan_limit_type_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `meeting_plan_limit_id` bigint(20) NOT NULL,
  `org_type_child` bigint(20) NOT NULL COMMENT '组织实际类型',
  `org_type_child_name` varchar(255) NOT NULL COMMENT '对应的组织类型名称',
  PRIMARY KEY (`meeting_plan_limit_type_id`),
  KEY `meeting_plan_limit_id` (`meeting_plan_limit_id`),
  KEY `org_type_child` (`org_type_child`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动发放的活动类型 限制的组织类型';

-- ----------------------------
-- Table structure for t_meeting_org_change_log
-- ----------------------------
CREATE TABLE `t_meeting_org_change_log` (
  `meeting_org_change_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `org_id` bigint(20) NOT NULL COMMENT '组织id',
  `org_name` varchar(255) NOT NULL COMMENT '组织名称',
  `type` tinyint(2) NOT NULL COMMENT '操作类型： 1:新增　 2:修改 3:删除',
  `org_type_child` int(10) NOT NULL COMMENT '对应的组织类型',
  `is_retire` tinyint(2) DEFAULT NULL COMMENT '是否离退休 　　1-是 2-否',
  `party_group` tinyint(2) DEFAULT NULL COMMENT '党小组是否存在 　1:是　2:否',
  `period` tinyint(2) DEFAULT NULL COMMENT '支委会是否存在　 1:是 2:否',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `process_tag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否处理 0 未处理（默认）1 已处理',
  `org_level` varchar(512) NOT NULL COMMENT '组织树父级路径',
  PRIMARY KEY (`meeting_org_change_log_id`),
  KEY `org_id` (`org_id`),
  KEY `type` (`type`),
  KEY `org_type_child` (`org_type_child`),
  KEY `is_retire` (`is_retire`),
  KEY `party_group` (`party_group`),
  KEY `period` (`period`),
  KEY `process_tag` (`process_tag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组织变更记录。同一组织只有一条未处理记录，以最新记录为准';