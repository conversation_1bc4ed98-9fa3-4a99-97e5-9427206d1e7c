ALTER TABLE`t_meeting_work_report`
    CHANGE COLUMN `leader_name` `leader_names` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '领导姓名' AFTER `report_type`,
    CHANGE COLUMN `leader_id` `leader_ids` varchar(500) NOT NULL COMMENT '领导id' AFTER `leader_names`;


ALTER TABLE `t_meeting_work_report`
ADD COLUMN `leader` varchar(500) NOT NULL COMMENT '领导信息' AFTER `report_type`,
MODIFY COLUMN `leader_names` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '领导姓名' AFTER `report_type`,
MODIFY COLUMN `leader_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '领导id' AFTER `leader_names`;

ALTER TABLE `t_meeting_work_report`
    MODIFY COLUMN `leader` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '领导信息' AFTER `leader_ids`;

ALTER TABLE `t_meeting_leader_survey`
    ADD COLUMN `information` varchar(500) NOT NULL COMMENT '调研信息' AFTER `update_time`;

ALTER TABLE `t_meeting_leader_survey`
    MODIFY COLUMN `interview` varchar(500) NOT NULL COMMENT '调研方式' AFTER `org_id`,
    MODIFY COLUMN `target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调研对象' AFTER `subject`,
    MODIFY COLUMN `target_id` varchar(500) NULL DEFAULT NULL COMMENT '调研对象id' AFTER `target`;

ALTER TABLE `t_meeting_leader_survey`
    MODIFY COLUMN `information` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '调研信息' AFTER `update_time`;

ALTER TABLE `t_meeting_leader_survey`
    MODIFY COLUMN `information` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '调研信息' AFTER `update_time`;