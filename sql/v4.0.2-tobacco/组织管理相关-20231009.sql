DROP TABLE IF EXISTS t_meeting_work_points;
CREATE TABLE t_meeting_work_points(
                                      work_points_id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
                                      org_id BIGINT NOT NULL   COMMENT '所属组织' ,
                                      owner_id BIGINT NOT NULL   COMMENT '所属单位' ,
                                      owner_name VARCHAR(120) NOT NULL   COMMENT '所属单位名称' ,
                                      file_name VARCHAR(500)    COMMENT '文件名称' ,
                                      half_year INT    COMMENT '上下半年(1.上半年，2.下半年)' ,
                                      year INT    COMMENT '年度' ,
                                      doc_code VARCHAR(255)    COMMENT '文号' ,
                                      publish_time DATETIME    COMMENT '发文时间' ,
                                      attachment TEXT    COMMENT '附件（JSON）' ,
                                      create_user BIGINT NOT NULL   COMMENT '创建人' ,
                                      create_time DATETIME NOT NULL   COMMENT '创建时间' ,
                                      update_user BIGINT    COMMENT '更新人' ,
                                      update_time DATETIME    COMMENT '更新时间' ,
                                      PRIMARY KEY (work_points_id)
)  COMMENT = '党建工作要点';


CREATE INDEX idx_mwp_oname ON t_meeting_work_points(owner_name);
CREATE INDEX idx_mwp_fname ON t_meeting_work_points(file_name);
CREATE INDEX idx_mwp_year ON t_meeting_work_points(year);
CREATE INDEX idx_mwp_ptime ON t_meeting_work_points(publish_time);


DROP TABLE IF EXISTS t_meeting_work_report;
CREATE TABLE t_meeting_work_report(
                                      work_report_id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
                                      report_type INT    COMMENT '汇报类型(1.工作，2.意识形态)' ,
                                      leader_name VARCHAR(255) NOT NULL   COMMENT '领导姓名' ,
                                      leader_id BIGINT NOT NULL   COMMENT '领导id' ,
                                      department VARCHAR(255)    COMMENT '汇报部门' ,
                                      reporter VARCHAR(255)    COMMENT '汇报人' ,
                                      report_time DATETIME    COMMENT '汇报时间' ,
                                      year INT    COMMENT '年度' ,
                                      half_year_interval INT    COMMENT '上下半年(1.上半年，2.下半年)' ,
                                      report_record VARCHAR(500)    COMMENT '汇报记录' ,
                                      attachment TEXT    COMMENT '汇报材料（JSON）' ,
                                      create_user BIGINT NOT NULL   COMMENT '创建人' ,
                                      create_time DATETIME NOT NULL   COMMENT '创建时间' ,
                                      update_user BIGINT    COMMENT '更新人' ,
                                      update_time DATETIME    COMMENT '更新时间' ,
                                      PRIMARY KEY (work_report_id)
)  COMMENT = '半年听取汇报';

CREATE INDEX idx_wr_rtype ON t_meeting_work_report(report_type);
CREATE INDEX idx_wr_lname ON t_meeting_work_report(leader_name);
CREATE INDEX idx_wr_dept ON t_meeting_work_report(department);
CREATE INDEX idx_wr_re ON t_meeting_work_report(reporter);
CREATE INDEX idx_wr_rtime ON t_meeting_work_report(report_time);


DROP TABLE IF EXISTS t_meeting_leader_survey;
CREATE TABLE t_meeting_leader_survey(
                                        leader_survey_id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
                                        interview INT NOT NULL   COMMENT '调研方式， 1.座谈访谈、2.随机走访、3.问卷调查（轻流）、4.问卷调查、5.专家调查、6.抽样调查、7.统计分析、8.其他' ,
                                        subject INT NOT NULL   COMMENT '调研主体(1.领导、2.部门)' ,
                                        target VARCHAR(255)    COMMENT '调研对象' ,
                                        target_id BIGINT    COMMENT '调研对象id' ,
                                        survery_time DATETIME    COMMENT '调研时间' ,
                                        year INT    COMMENT '年度' ,
                                        num_people INT    COMMENT '人员数量' ,
                                        question TEXT    COMMENT '调研问题' ,
                                        location VARCHAR(900)    COMMENT '调研地点' ,
                                        num_customer INT    COMMENT '客户数量' ,
                                        basic_info TEXT    COMMENT '基本情况' ,
                                        survery_type INT    COMMENT '调研类型(1.日常调研、2.年度调研)' ,
                                        attachment TEXT    COMMENT '调研报告（JSON）' ,
                                        create_user BIGINT NOT NULL   COMMENT '创建人' ,
                                        create_time DATETIME NOT NULL   COMMENT '创建时间' ,
                                        update_user BIGINT    COMMENT '更新人' ,
                                        update_time DATETIME    COMMENT '更新时间' ,
                                        PRIMARY KEY (leader_survey_id)
)  COMMENT = '领导调研';

CREATE INDEX idx_ls_s_t ON t_meeting_leader_survey(subject,target);
CREATE INDEX idx_ls_inter ON t_meeting_leader_survey(interview);
CREATE INDEX idx_ls_stime ON t_meeting_leader_survey(survery_time);


ALTER TABLE `t_meeting_leader_survey`
    ADD COLUMN `org_id` bigint NOT NULL COMMENT '创建人组织id' AFTER `leader_survey_id`;


