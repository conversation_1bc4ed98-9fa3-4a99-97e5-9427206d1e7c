ALTER TABLE `t_meeting_work_report`
    MODIFY COLUMN `leader` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '领导信息' AFTER `leader_ids`;

ALTER TABLE `t_meeting_work_report`
    ADD COLUMN `unit_id` bigint NULL COMMENT '单位id' AFTER `half_year_interval`,
ADD COLUMN `unit_name` varbinary(255) NULL COMMENT '单位名称' AFTER `unit_id`;

ALTER TABLE `t_meeting_work_report`
    MODIFY COLUMN `unit_id` varchar(500) NULL DEFAULT NULL COMMENT '单位id' AFTER `half_year_interval`,
    MODIFY COLUMN `unit_name` varchar(500) NULL DEFAULT NULL COMMENT '单位名称' AFTER `unit_id`;

CREATE TABLE t_meeting_practicable (
                                       practicable_id bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
                                       leader_id      bigint(20) NOT NULL comment '领导id',
                                       unit_id        bigint(20) NOT NULL comment '单位id',
                                       org_id         bigint(20) NOT NULL comment '组织id',
                                       leader_name    varchar(255) NOT NULL comment '领导姓名',
                                       unit_name      varchar(255) NOT NULL comment '单位名称',
                                       org_name       varchar(255) NOT NULL comment '所属支部名称',
                                       substrate_name varchar(255) comment '基层联系点名称',
                                       interview_time date comment '走访时间',
                                       create_time    datetime NOT NULL comment '创建时间',
                                       create_user    bigint(20) NOT NULL comment '创建人',
                                       update_time    datetime NULL comment '修改时间',
                                       update_user    bigint(20) comment '修改人',
                                       attachment     text comment '文件',
                                       PRIMARY KEY (practicable_id));



