-- ----------------------------
-- Table structure for t_category
-- ----------------------------
DROP TABLE IF EXISTS `t_category`;
CREATE TABLE `t_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `org_id` bigint(20) NOT NULL COMMENT '创建人所属组织id',
  `org_name` varchar(255) NOT NULL COMMENT '创建人所属组织名称',
  `category` varchar(450) NOT NULL COMMENT '类别',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建人id',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后更新人id',
  PRIMARY <PERSON>EY (`category_id`),
  KEY `index_org_id` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COMMENT='活动类别表';

-- ----------------------------
-- Table structure for t_meeting
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting`;
CREATE TABLE `t_meeting` (
  `meeting_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `org_id` bigint(20) NOT NULL COMMENT '创建人所属组织id',
  `p_org_id` bigint(20) DEFAULT NULL COMMENT '活动所属组织生活的派发组织id',
  `p_user_id` bigint(20) DEFAULT NULL COMMENT '活动所属组织生活的派发人',
  `workflow_id` bigint(20) DEFAULT NULL COMMENT '对应的审批流程类型id 如果传-999则为不需要审批',
  `workflow_task_id` bigint(20) DEFAULT NULL COMMENT '实际的审批任务流的id',
  `workflow_name` varchar(100) DEFAULT NULL COMMENT '对应的审批流程类型名称 默认名称为自定义',
  `org_name` varchar(255) NOT NULL COMMENT '创建人所属组织名称',
  `name` varchar(450) NOT NULL COMMENT '活动名称',
  `types` text NOT NULL COMMENT '活动类型',
  `address` text NOT NULL COMMENT '活动地点',
  `gps_type` tinyint(1) DEFAULT NULL COMMENT 'gps来源类型:1.高德/微信（GCJ02）、2.百毒（百毒坐标体系）、3.google（真实坐标）、4.gps设备（真实坐标）',
  `lng` double DEFAULT NULL COMMENT '经度',
  `lat` double DEFAULT NULL COMMENT '纬度',
  `real_lng` double DEFAULT NULL COMMENT '实际的经度（纠偏以后）',
  `real_lat` double DEFAULT NULL COMMENT '实际纬度（纠偏以后）',
  `start_time` datetime NOT NULL COMMENT '举办时间',
  `is_sign_in` tinyint(1) NOT NULL COMMENT '是否需要签到。0：不需要；1：需要',
  `is_w_resolution` tinyint(1) NOT NULL COMMENT '是否需要填写决议 0：不需要；1：需要',
  `sign_start_time` datetime DEFAULT NULL COMMENT '签到开始时间',
  `sign_end_time` datetime DEFAULT NULL COMMENT '签到截止时间',
  `must_approve` tinyint(1) NOT NULL COMMENT '是否需要审批：0：不需要，1：需要',
  `resolution` text COMMENT '决议',
  `status` tinyint(2) NOT NULL COMMENT '活动状态 1：发起审批中、2：发起未通过、3：活动待举办（待填报结果）、5：填报审查中（第一次）、6：填报未通过（第一次）、7：已提交、8：退回、9：活动已取消、10：填报审查中（第一次之后）、11：填报未通过（第一次之后）12：待复核（第二次提交并且审批通过）13：通过（第一次考核就通过）14：通过（第一次考核被退回后的通过）',
  `is_del` tinyint(1) NOT NULL COMMENT '是否删除：0 正常（默认） 1 删除',
  `add_type` tinyint(1) NOT NULL COMMENT '添加来源：1.活动管理页面添加 2.记实情况页面添加',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建人id',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后更新人id',
  PRIMARY KEY (`meeting_id`),
  KEY `index_org_id` (`org_id`),
  KEY `index_start_time` (`start_time`),
  KEY `index_add_type` (`add_type`),
  KEY `index_is_del` (`is_del`),
  KEY `index_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=165 DEFAULT CHARSET=utf8mb4 COMMENT='活动表';

-- ----------------------------
-- Table structure for t_meeting_history
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_history`;
CREATE TABLE `t_meeting_history` (
  `meeting_history_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `meeting_id` bigint(20) NOT NULL,
  `status` int(10) NOT NULL COMMENT '活动状态 1：发起审批中、2：发起未通过、3：活动待举办（待填报结果）、5：填报审查中（第一次）、6：填报未通过（第一次）、7：已提交、8：退回、9：活动已取消、10：填报审查中（第一次之后）、11：填报未通过（第一次之后）12：待复核（第二次提交并且审批通过）',
  `reason` varchar(4500) DEFAULT NULL COMMENT '退回原因',
  `create_time` datetime NOT NULL,
  `create_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`meeting_history_id`),
  KEY `meeting_id` (`meeting_id`)
) ENGINE=InnoDB AUTO_INCREMENT=343 DEFAULT CHARSET=utf8mb4 COMMENT='活动流转历史表';

-- ----------------------------
-- Table structure for t_meeting_leave
-- ----------------------------
drop table t_meeting_leave;

CREATE TABLE `t_meeting_leave` (
  `meeting_leave_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '请假id',
  `meeting_id` bigint(20) NOT NULL COMMENT '会议id',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `type` int(1) NOT NULL COMMENT '请假类型（1：因公请假 2：因私请假）',
  `reason` varchar(4500) NOT NULL COMMENT '请假原因',
  `status` int(1) NOT NULL COMMENT '审批类型（0:初始  1：待审批/审批中/提交 2：请假审批通过 3：请假审批不通过  4：销假审批中 5：已撤销  6：销假审批不通过）',
  `check_user_id` bigint(20) DEFAULT NULL COMMENT '审批人',
  `check_user_name` varchar(255) DEFAULT NULL COMMENT '请假审批人',
  `check_time` datetime DEFAULT NULL COMMENT '审批时间',
  `check_result` varchar(255) DEFAULT NULL COMMENT '请假审批不通过原因',
  `off_leave_reason` varchar(255) DEFAULT NULL COMMENT '销假原因',
  `off_check_user_id` varchar(255) DEFAULT NULL COMMENT '销假审批人',
  `off_check_user_name` varchar(255) DEFAULT NULL COMMENT '销假审批人',
  `off_check_result` varchar(255) DEFAULT NULL COMMENT '销假审批不通过原因',
  `off_check_time` datetime DEFAULT NULL COMMENT '销假审批时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后修改人',
  `del_status` int(1) NOT NULL COMMENT '0-正常  1-删除',
  PRIMARY KEY (`meeting_leave_id`),
  UNIQUE KEY `id` (`meeting_id`,`user_id`) USING BTREE,
  KEY `meeting_id` (`meeting_id`),
  KEY `type` (`type`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=133 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for t_meeting_org
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_org`;
CREATE TABLE `t_meeting_org` (
  `meeting_org_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `meeting_plan_id` bigint(20) NOT NULL COMMENT '组织生活id',
  `org_id` bigint(20) NOT NULL COMMENT '执行组织id',
  `org_name` varchar(255) NOT NULL COMMENT '组织名称',
  PRIMARY KEY (`meeting_org_id`),
  KEY `index_meeting_plan_id` (`meeting_plan_id`)
) ENGINE=InnoDB AUTO_INCREMENT=273 DEFAULT CHARSET=utf8mb4 COMMENT='组织生活执行组织';

-- ----------------------------
-- Table structure for t_meeting_plan
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_plan`;
CREATE TABLE `t_meeting_plan` (
  `meeting_plan_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `org_id` bigint(20) NOT NULL COMMENT '创建人所属组织id',
  `org_name` varchar(255) NOT NULL COMMENT '创建人所属组织名称',
  `name` varchar(450) NOT NULL COMMENT '组织生活名称',
  `execute_type` tinyint(1) NOT NULL COMMENT '执行方式:1.自定义间段(默认值);2自然月周期;3.季度周期;4.年度周期',
  `start_time` date NOT NULL COMMENT '开始时间',
  `end_time` date DEFAULT NULL COMMENT '结束时间',
  `start_year` int(4) DEFAULT NULL COMMENT '开始年份',
  `start_month` int(2) DEFAULT NULL COMMENT '开始月份',
  `start_quarter` tinyint(1) DEFAULT NULL COMMENT '	开始季度',
  `is_execute` tinyint(1) NOT NULL COMMENT '停、启用状态。0：停用；1：启用',
  `is_del` tinyint(1) NOT NULL COMMENT '是否删除：0 正常（默认） 1 删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建人id',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后更新人id',
  PRIMARY KEY (`meeting_plan_id`),
  KEY `index_org_id` (`org_id`),
  KEY `index_is_execute` (`is_execute`),
  KEY `index_is_del` (`is_del`)
) ENGINE=InnoDB AUTO_INCREMENT=138 DEFAULT CHARSET=utf8mb4 COMMENT='活动组织生活表';

-- ----------------------------
-- Table structure for t_meeting_plan_log
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_plan_log`;
CREATE TABLE `t_meeting_plan_log` (
  `meeting_plan_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `meeting_plan_id` bigint(20) NOT NULL COMMENT '组织生活id',
  `num` int(10) NOT NULL COMMENT '重试次数',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建人id',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后更新人id',
  PRIMARY KEY (`meeting_plan_log_id`),
  KEY `index_meeting_plan_id` (`meeting_plan_id`),
  KEY `index_num` (`num`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='活动任务派发日志表';

-- ----------------------------
-- Table structure for t_meeting_require
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_require`;
CREATE TABLE `t_meeting_require` (
  `meeting_require_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `meeting_plan_id` bigint(20) NOT NULL COMMENT '组织生活id',
  `type_id` bigint(20) NOT NULL COMMENT '类型id',
  `category_id` bigint(20) NOT NULL COMMENT '所属类别id。冗余',
  `type` varchar(450) NOT NULL COMMENT '类型。冗余',
  `category` varchar(450) NOT NULL COMMENT '类别。冗余',
  `meeting_num` int(10) NOT NULL COMMENT '要求举办次数。',
  `deduct` float NOT NULL COMMENT '未执行扣分。',
  `is_sign_in` tinyint(1) NOT NULL COMMENT '是否需要签到。0：不需要；1：需要',
  `is_w_resolution` tinyint(1) NOT NULL COMMENT '是否需要填写决议 0：不需要；1：需要',
  PRIMARY KEY (`meeting_require_id`),
  KEY `index_meeting_plan_id` (`meeting_plan_id`)
) ENGINE=InnoDB AUTO_INCREMENT=242 DEFAULT CHARSET=utf8mb4 COMMENT='活动举办要求表';

-- ----------------------------
-- Table structure for t_meeting_resolution_file
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_resolution_file`;
CREATE TABLE `t_meeting_resolution_file` (
  `meeting_resolution_file_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `meeting_id` bigint(20) NOT NULL COMMENT '活动id',
  `name` varchar(450) NOT NULL COMMENT '附件名称',
  `path` varchar(450) NOT NULL COMMENT '附件路径',
  `file_name` varchar(450) NOT NULL COMMENT '文件原名称',
  `size` varchar(450) NOT NULL COMMENT '文件大小(Byte)',
  `is_del` int(1) DEFAULT NULL COMMENT '0：正常 1：删除',
  `create_user` bigint(20) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`meeting_resolution_file_id`),
  KEY `index_meeting_id` (`meeting_id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COMMENT='决议附件表';

-- ----------------------------
-- Table structure for t_meeting_result
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_result`;
CREATE TABLE `t_meeting_result` (
  `meeting_reslut_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '纪实情况id',
  `meeting_id` bigint(20) NOT NULL COMMENT '活动id',
  `org_id` bigint(20) NOT NULL COMMENT '当前组织id',
  `is_del` int(1) NOT NULL DEFAULT '0' COMMENT '0：默认 1：删除',
  `must_approve` int(1) DEFAULT NULL COMMENT '是否需要审批（0：不需要，1：需要）',
  `approve_status` int(1) DEFAULT NULL COMMENT '审批状态（1:正常;2:审批中,3:审批不通过）',
  `workflow_id` bigint(20) DEFAULT NULL COMMENT '工作流id',
  `workflow_task_id` bigint(20) DEFAULT NULL COMMENT '工作流任务id',
  `workflow_name` varchar(540) DEFAULT NULL COMMENT '工作流名称',
  `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  `create_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`meeting_reslut_id`),
  KEY `meeting_id` (`meeting_id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COMMENT='活动纪实表';

-- ----------------------------
-- Table structure for t_meeting_result_hs
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_result_hs`;
CREATE TABLE `t_meeting_result_hs` (
  `meeting_result_hs_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `meeting_id` bigint(20) NOT NULL,
  `update_content` longtext COMMENT '修改的内容（json报文）',
  `update_before` longtext COMMENT '修改之前的结果记录（json）',
  `remark` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `create_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`meeting_result_hs_id`),
  KEY `meeting_id` (`meeting_id`)
) ENGINE=InnoDB AUTO_INCREMENT=181 DEFAULT CHARSET=utf8mb4 COMMENT='纪实结果填报历史记录';

-- ----------------------------
-- Table structure for t_meeting_task
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_task`;
CREATE TABLE `t_meeting_task` (
  `meeting_task_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `meeting_plan_id` bigint(20) NOT NULL COMMENT '组织生活id',
  `meeting_require_id` bigint(20) NOT NULL COMMENT '活动举办要求id',
  `type_id` bigint(20) NOT NULL COMMENT '类型id',
  `category_id` bigint(20) NOT NULL COMMENT '所属类别id。冗余',
  `org_id` bigint(20) NOT NULL COMMENT '执行组织id',
  `p_org_id` bigint(20) NOT NULL COMMENT '活动所属组织生活的派发组织id',
  `type` varchar(450) NOT NULL COMMENT '类型。冗余',
  `category` varchar(450) NOT NULL COMMENT '类别。冗余',
  `name` varchar(450) NOT NULL COMMENT '所属组织生活名称。冗余',
  `meeting_num` int(10) NOT NULL COMMENT '举办次数。',
  `status` tinyint(1) NOT NULL COMMENT '状态（：1未完成 2：已完成）',
  `start_time` date NOT NULL COMMENT '开始时间',
  `end_time` date NOT NULL COMMENT '结束时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建人id',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后更新人id',
  PRIMARY KEY (`meeting_task_id`),
  KEY `index_meeting_plan_id` (`meeting_plan_id`),
  KEY `index_type_id` (`type_id`),
  KEY `index_category_id` (`category_id`),
  KEY `index_org_id` (`org_id`),
  KEY `index_p_org_id` (`p_org_id`),
  KEY `index_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=354 DEFAULT CHARSET=utf8mb4 COMMENT='活动任务表';

-- ----------------------------
-- Table structure for t_meeting_topic
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_topic`;
CREATE TABLE `t_meeting_topic` (
  `meeting_topic_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `meeting_id` bigint(20) NOT NULL COMMENT '活动id',
  `topic_id` bigint(20) NOT NULL COMMENT '任务id',
  `topic_name` varchar(900) DEFAULT NULL,
  `status` int(1) DEFAULT '0' COMMENT '状态（1：未完成 2：已完成）',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  `create_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`meeting_topic_id`),
  UNIQUE KEY `index_mid_tid` (`meeting_id`,`topic_id`) USING BTREE,
  KEY `meeting_id` (`meeting_id`),
  KEY `topic_id` (`topic_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=180 DEFAULT CHARSET=utf8mb4 COMMENT='工作任务关联表';

-- ----------------------------
-- Table structure for t_meeting_type
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_type`;
CREATE TABLE `t_meeting_type` (
  `meeting_type_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `meeting_id` bigint(20) NOT NULL COMMENT '活动id',
  `meeting_task_id` bigint(20) NOT NULL COMMENT '任务id',
  `type_id` bigint(20) NOT NULL COMMENT '类型id',
  `category_id` bigint(20) NOT NULL COMMENT '所属类别id。冗余',
  `type` varchar(450) NOT NULL COMMENT '类型。冗余',
  `category` varchar(450) NOT NULL COMMENT '类别。冗余',
  PRIMARY KEY (`meeting_type_id`),
  KEY `index_meeting_id` (`meeting_id`),
  KEY `index_meeting_task_id` (`meeting_task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=159 DEFAULT CHARSET=utf8mb4 COMMENT='活动与活动类型关联表';

-- ----------------------------
-- Table structure for t_meeting_user
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_user`;
CREATE TABLE `t_meeting_user` (
  `meeting_user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `meeting_id` bigint(20) NOT NULL COMMENT '活动id',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `org_id` bigint(20) DEFAULT NULL COMMENT '用户所属组织id',
  `user_name` varchar(100) NOT NULL COMMENT '用户名',
  `org_name` varchar(255) DEFAULT NULL COMMENT '用户所属组织名称',
  `phone` varchar(100) DEFAULT NULL COMMENT '电话号码',
  `cert_number` varchar(100) DEFAULT NULL COMMENT '身份证号码',
  `is_add` tinyint(1) NOT NULL COMMENT '是否是通过填写记实情况中的“添加参会人员”、“添加列席人员”两个功能添加的人员 0:活动管理添加；1：记实添加',
  `sign_status` tinyint(1) NOT NULL COMMENT '签到情况1：已签到（默认） 2：未签到 3：因公请假 4：因私请假 5: 缺席',
  `tag` tinyint(1) NOT NULL COMMENT '参会人员与列席人员标识（1：记录人员，2：主持人，3：参与人员 ，4：列席人员',
  `reason` varchar(4500) DEFAULT NULL COMMENT '请假原因',
  PRIMARY KEY (`meeting_user_id`),
  KEY `index_meeting_id` (`meeting_id`),
  KEY `index_sign_status` (`sign_status`),
  KEY `index_tag` (`tag`)
) ENGINE=InnoDB AUTO_INCREMENT=743 DEFAULT CHARSET=utf8mb4 COMMENT='参会人员';

-- ----------------------------
-- Table structure for t_topic
-- ----------------------------
DROP TABLE IF EXISTS `t_topic`;
CREATE TABLE `t_topic` (
  `topic_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务id',
  `org_id` bigint(20) DEFAULT NULL COMMENT '组织id',
  `org_name` varchar(255) DEFAULT NULL,
  `name` varchar(900) NOT NULL COMMENT '任务名称',
  `description` varchar(4500) DEFAULT NULL COMMENT '描述',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `status` int(1) DEFAULT NULL COMMENT '1:派发 2：自有的',
  `is_del` int(1) DEFAULT '0' COMMENT '是否删除 （0：默认 1：删除）',
  `create_time` datetime NOT NULL,
  `create_user` bigint(20) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`topic_id`),
  KEY `org_id` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=190 DEFAULT CHARSET=utf8mb4 COMMENT='任务';

-- ----------------------------
-- Table structure for t_topic_content
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_content`;
CREATE TABLE `t_topic_content` (
  `content_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '内容id',
  `topic_id` bigint(20) NOT NULL COMMENT '任务id',
  `type` int(1) NOT NULL COMMENT '内容类型（1 ：文本内容  2：单项选择  3：多项选择）',
  `name` varchar(900) NOT NULL COMMENT '内容名称',
  `description` varchar(4500) DEFAULT NULL COMMENT '内容描述',
  `create_time` datetime DEFAULT NULL,
  `create_user` bigint(20) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`content_id`),
  KEY `topic_id` (`topic_id`)
) ENGINE=InnoDB AUTO_INCREMENT=329 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for t_topic_file
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_file`;
CREATE TABLE `t_topic_file` (
  `topic_file_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '附件id',
  `topic_id` bigint(20) NOT NULL COMMENT '任务id',
  `name` varchar(900) NOT NULL COMMENT '附件名称',
  `path` varchar(900) NOT NULL COMMENT '附件路径',
  `file_name` varchar(540) NOT NULL COMMENT '文件原名称',
  `size` bigint(20) DEFAULT NULL COMMENT '文件大小（byte）',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  `create_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`topic_file_id`),
  KEY `topic_id` (`topic_id`)
) ENGINE=InnoDB AUTO_INCREMENT=282 DEFAULT CHARSET=utf8mb4 COMMENT='任务附件';

-- ----------------------------
-- Table structure for t_topic_log
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_log`;
CREATE TABLE `t_topic_log` (
  `topic_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志id',
  `meeting_topic_id` bigint(20) NOT NULL COMMENT '工作任务关联id',
  `content_id` bigint(20) NOT NULL COMMENT '内容id',
  `topic_id` bigint(20) NOT NULL COMMENT '任务id',
  `ans_cnt` varchar(4500) DEFAULT NULL COMMENT '问答题的回答内容',
  `opts_id` bigint(20) DEFAULT NULL COMMENT '选项id',
  `create_time` datetime DEFAULT NULL,
  `create_user` bigint(20) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`topic_log_id`),
  KEY `meeting_topic_id` (`meeting_topic_id`),
  KEY `content_id` (`content_id`),
  KEY `topic_id` (`topic_id`)
) ENGINE=InnoDB AUTO_INCREMENT=219 DEFAULT CHARSET=utf8mb4 COMMENT='组织参与任务具体选择日志';

-- ----------------------------
-- Table structure for t_topic_log_file
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_log_file`;
CREATE TABLE `t_topic_log_file` (
  `topic_log_file_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `topic_log_id` bigint(20) NOT NULL,
  `meeting_topic_id` bigint(20) NOT NULL,
  `topic_id` bigint(20) DEFAULT NULL,
  `name` varchar(900) DEFAULT NULL,
  `path` varchar(900) DEFAULT NULL,
  `file_name` varchar(540) DEFAULT NULL,
  `size` bigint(20) DEFAULT NULL,
  `is_del` int(1) DEFAULT NULL COMMENT '0：正常  1：删除',
  `create_time` datetime DEFAULT NULL,
  `create_user` bigint(20) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`topic_log_file_id`),
  KEY `meeting_id` (`meeting_topic_id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COMMENT='任务问答题回答的附件';

-- ----------------------------
-- Table structure for t_topic_opts
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_opts`;
CREATE TABLE `t_topic_opts` (
  `opts_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '选项id',
  `content_id` bigint(20) NOT NULL COMMENT '内容id',
  `topic_id` bigint(20) NOT NULL COMMENT '任务id',
  `opts_name` varchar(900) NOT NULL COMMENT '选项名称',
  `seq` int(2) NOT NULL COMMENT '排序',
  `create_time` datetime DEFAULT NULL,
  `create_user` bigint(20) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`opts_id`),
  KEY `content_id` (`content_id`),
  KEY `topic_id` (`topic_id`)
) ENGINE=InnoDB AUTO_INCREMENT=304 DEFAULT CHARSET=utf8mb4 COMMENT='任务具体选项（单选题或者多选题）';

-- ----------------------------
-- Table structure for t_topic_org
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_org`;
CREATE TABLE `t_topic_org` (
  `topic_org_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务组织关联表id',
  `topic_id` bigint(20) NOT NULL,
  `org_id` bigint(20) NOT NULL COMMENT '组织id',
  `org_name` varchar(900) NOT NULL COMMENT '组织名称',
  `status` int(1) NOT NULL DEFAULT '0' COMMENT '状态（1：未完成 2：已完成）',
  `create_time` datetime DEFAULT NULL,
  `create_user` bigint(20) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_change_user` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`topic_org_id`),
  KEY `topic_id` (`topic_id`),
  KEY `org_id` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=197 DEFAULT CHARSET=utf8mb4 COMMENT='任务组织关联表';

-- ----------------------------
-- Table structure for t_type
-- ----------------------------
DROP TABLE IF EXISTS `t_type`;
CREATE TABLE `t_type` (
  `type_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `category_id` bigint(20) NOT NULL COMMENT '所属类别id',
  `org_id` bigint(20) NOT NULL COMMENT '创建人所属组织id',
  `org_name` varchar(255) NOT NULL COMMENT '创建人所属组织名称',
  `type` varchar(450) NOT NULL COMMENT '类型',
  `category` varchar(450) NOT NULL COMMENT '类别。冗余',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建人id',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后更新人id',
  PRIMARY KEY (`type_id`),
  KEY `index_category_id` (`category_id`),
  KEY `index_org_id` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COMMENT='活动类型表';


-- ----------------------------
-- Table structure for t_type_group
-- ----------------------------
DROP TABLE IF EXISTS `t_type_group`;
CREATE TABLE `t_type_group` (
  `type_group_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `group_id` bigint(20) NOT NULL COMMENT '组合id',
  `type_id` bigint(20) NOT NULL COMMENT '类型id',
  PRIMARY KEY (`type_group_id`),
  KEY `index_group_id` (`group_id`)
) ENGINE=InnoDB AUTO_INCREMENT=85 DEFAULT CHARSET=utf8mb4 COMMENT='活动类型组与类型关联表';

DROP TABLE IF EXISTS `t_group`;
CREATE TABLE `t_group` (
  `group_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `org_id` bigint(20) NOT NULL COMMENT '创建人所属组织id',
  `org_name` varchar(255) NOT NULL COMMENT '创建人所属组织名称',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建人id',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后更新人id',
  PRIMARY KEY (`group_id`),
  KEY `index_org_id` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COMMENT='活动类型组表';