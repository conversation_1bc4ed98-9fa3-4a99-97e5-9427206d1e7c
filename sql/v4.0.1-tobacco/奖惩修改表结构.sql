ALTER TABLE `t_meeting_user_commend_penalize`
ADD COLUMN `category` int(10) NULL COMMENT '奖惩类别' AFTER `effective_time`,
ADD COLUMN `level` int(10) NULL COMMENT '奖惩级别' AFTER `category`,
ADD COLUMN `content` varchar(500) NULL COMMENT '奖惩名称内容' AFTER `name`,
ADD COLUMN `workflow_task_id` bigint(20) NULL COMMENT '审核流ID' AFTER `update_time`,
ADD COLUMN `approval_status` int(1) NULL COMMENT '审核状态 1-待审核、2-审核通过、3-审核不通过' AFTER `workflow_task_id`;


ALTER TABLE `t_meeting_org_commend_penalize`
ADD COLUMN `category` int(10) NULL COMMENT '奖惩类别' AFTER `ratify_time`,
ADD COLUMN `level` int(10) NULL COMMENT '奖惩级别' AFTER `category`,
ADD COLUMN `content` varchar(500) NULL COMMENT '奖惩名称内容' AFTER `name`,
ADD COLUMN `workflow_task_id` bigint(20) NULL COMMENT '审核流ID' AFTER `update_time`,
ADD COLUMN `approval_status` int(1) NULL COMMENT '审核状态 1-待审核、2-审核通过、3-审核不通过' AFTER `workflow_task_id`;