-- DROP TABLE IF EXISTS t_meeting_comment;
CREATE TABLE t_meeting_comment (
       comment_id       bigint(20) NOT NULL AUTO_INCREMENT comment '民主评议主键ID',
       year             int(4) NOT NULL comment '评议年度',
       org_id           bigint(20) NOT NULL comment '组织ID',
       parent_id        bigint(20) NOT NULL comment '组织父级ID',
       org_name         varchar(500) NOT NULL comment '组织名称',
       org_level        varchar(100) NOT NULL comment '组织层级关系',
       region_id        bigint(20) NOT NULL comment '区县ID',
       status           int(2) NOT NULL comment '状态 0-未开启，1-已开启，2-待审查，3-审查未通过，4-待审定，5-审定未通过，6-审定通过',
       create_time      datetime NOT NULL comment '创建时间',
       create_user      bigint(20) NOT NULL comment '创建人',
       update_time      datetime NULL comment '更新时间',
       last_change_user bigint(20) comment '最后修改人',
       PRIMARY KEY (comment_id),
       INDEX (year),
       INDEX (org_id)) comment='民主评议主表';




-- DROP TABLE IF EXISTS t_meeting_comment_approve;
CREATE TABLE t_meeting_comment_approve (
       comment_approve_id bigint(20) NOT NULL AUTO_INCREMENT comment '审批表ID',
       comment_id         bigint(20) NOT NULL comment '民主评议主表ID',
       approve_status     int(1) NOT NULL comment '审批状态 1-审查通过，2-审查未通过，3-审定通过，4-审定未通过',
       approve_user       bigint(20) NOT NULL comment '审批人',
       content            text NOT NULL comment '审批内容',
       create_user        bigint(20) comment '创建人',
       create_time        datetime NULL comment '创建时间',
       PRIMARY KEY (comment_approve_id),
       INDEX (comment_id)) comment='民主评议审批表';



-- DROP TABLE IF EXISTS t_meeting_comment_member;
CREATE TABLE t_meeting_comment_member (
        comment_member_id bigint(20) NOT NULL AUTO_INCREMENT comment '党员评议表主键',
        comment_id        bigint(20) NOT NULL comment '评议主表ID',
        year              int(4) NOT NULL comment '评议年度',
        user_id           bigint(20) NOT NULL comment '评议人ID（被打分人）',
        user_name         varchar(255) NOT NULL comment '评议人姓名（被打分人）',
        phone             varchar(100) NOT NULL comment '评议人手机号',
        phone_secret      varchar(20) NOT NULL comment '手机号密文',
        org_id            bigint(20) NOT NULL comment '所属组织ID',
        org_name          varchar(255) NOT NULL comment '所属组织名称',
        self_rating       int(1) comment '评议等级 1-优秀，2-合格，3-基本合格，4-不合格',
        self_content      text comment '内容',
        is_draft          int(1) NOT NULL comment '草稿状态 0-草稿，1-正常',
        create_user       bigint(20) NOT NULL comment '创建人',
        create_time       datetime NOT NULL comment '创建时间',
        last_change_user  bigint(20) comment '最后修改人',
        update_time       datetime NULL comment '更新时间',
        PRIMARY KEY (comment_member_id),
        INDEX (comment_id),
        INDEX (year),
        INDEX (user_id),
        INDEX (org_id)) comment='党员评议表';




DROP TABLE IF EXISTS t_meeting_comment_member_appraisal;
CREATE TABLE t_meeting_comment_member_appraisal (
        comment_member_appraisal_id bigint(20) NOT NULL AUTO_INCREMENT comment '互评表主键ID',
        comment_member_id           bigint(20) NOT NULL comment '党员评议表主键ID',
        user_id                     int(10) NOT NULL comment '评价人',
        user_name                   varchar(200) NOT NULL comment '评价人姓名',
        phone                       varchar(100) NOT NULL comment '评价人手机号',
        phone_secret                varchar(20) NOT NULL comment '手机号密文',
        appraisal_rating            int(10) NOT NULL comment '互评等级 1-优秀，2-合格，3-基本合格，4-不合格',
        appraisal_content           text comment '互评内容',
        is_draft                    int(1) NOT NULL comment '草稿状态 0-草稿，1-正常',
        create_time                 date NOT NULL comment '创建人',
        create_user                 bigint(20) NOT NULL comment '创建人',
        update_time                 date comment '更新人',
        last_change_user            bigint(20) comment '最后更新人',
        PRIMARY KEY (comment_member_appraisal_id),
        INDEX (comment_member_id)) comment='党员互评表';




-- DROP TABLE IF EXISTS t_meeting_comment_member_complex;
CREATE TABLE t_meeting_comment_member_complex (
      comment_member_complex_id bigint(20) NOT NULL AUTO_INCREMENT comment '综合评议结果',
      comment_member_id         bigint(20) NOT NULL comment '党员评议ID',
      complex_rating            int(2) NOT NULL comment '互评等级 1-优秀，2-合格，3-基本合格，4-不合格',
      complex_suggestion        text comment '互评内容',
      create_time               datetime NOT NULL comment '创建时间',
      create_user               bigint(20) NOT NULL comment '创建人',
      update_time               datetime NULL comment '更新时间',
      last_change_user          bigint(20) comment '最后修改人',
      PRIMARY KEY (comment_member_complex_id),
      INDEX (comment_member_complex_id));



-- DROP TABLE IF EXISTS t_meeting_comment_statistics;
CREATE TABLE t_meeting_comment_statistics (
          comment_statistics_id bigint(20) NOT NULL AUTO_INCREMENT comment '民主评议统计表主键ID',
          comment_id            bigint(20) NOT NULL comment '民主评议ID',
          org_id                int(10) NOT NULL comment '组织ID',
          org_name              varchar(255) NOT NULL comment '组织名称',
          org_level             varchar(100) NOT NULL comment '组织层级关系',
          region_id             bigint(20) NOT NULL comment '区县ID',
          year                  int(4) NOT NULL comment '评议年度',
          party_number          int(10) NOT NULL comment '党员数量',
          join_number           int(10) NOT NULL comment '参加评议的党员数量',
          rating                int(10) NOT NULL comment '评议等级',
          rating_number         int(10) NOT NULL comment '当前等级数量',
          create_user           bigint(20) NOT NULL comment '创建人',
          create_time           datetime NOT NULL comment '创建时间',
          last_change_user      bigint(20) comment '最后修改人',
          update_time           datetime NULL comment '更新时间',
          PRIMARY KEY (comment_statistics_id)) comment='民主评议统计表';
