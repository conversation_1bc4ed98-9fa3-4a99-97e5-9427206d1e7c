ALTER TABLE `t_meeting_user`
    MODIFY COLUMN `sign_status` tinyint(1) NOT NULL COMMENT '签到情况1：已签到（默认） 2：未签到 3：因公请假 4：因私请假 5: 缺席 6:补学' AFTER `is_add`,
    ADD COLUMN `old_sign_status` tinyint(3) NULL COMMENT '原签到状态' AFTER `sign_status`;

ALTER TABLE `t_meeting`
    MODIFY COLUMN `status` tinyint(2) NOT NULL COMMENT '活动状态 1：发起审批中、2：发起未通过、3：活动待举办（待填报结果）、5：填报审查中（第一次）、6：填报未通过（第一次）、7：已提交、8：退回、9：活动已取消、10：填报审查中（第一次之后）、11：填报未通过（第一次之后）12：待复核（第二次提交并且审批通过）13：通过（第一次考核就通过）14：通过（第一次考核被退回后的通过）15:管理员撤回' AFTER `resolution`;

ALTER TABLE `t_meeting`
    ADD COLUMN `can_sign` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否补学 1:是 0:否' AFTER `allow_all_sign`,
ADD COLUMN `sign_time` date NULL COMMENT '正常补学截止时间' AFTER `can_sign`;

CREATE TABLE t_meeting_wait_sign
(
    wait_sign_id    bigint(20) NOT NULL AUTO_INCREMENT,
    meeting_id      bigint(20) NOT NULL,
    meeting_user_id bigint(20) NOT NULL,
    user_id         bigint(20) NOT NULL,
    is_del          tinyint(1) NOT NULL comment '是否有效 1:有效 0:无效',
    type            tinyint(1) NOT NULL comment '数据状态  1:待补学 2:补学完成 3:超期补学 4:活动变更 5:草稿',
    is_now          tinyint(1) NOT NULL comment '是否是该用户最新的补学记录 1:是 0:否',
    content         mediumtext comment '补学内容',
    img_file        text comment '照片文件地址 英文逗号分隔',
    `file`          text comment '附件地址 英文逗号分隔',
    sign_time       datetime NULL comment '补学时间',
    create_time     datetime NOT NULL,
    update_time     datetime NULL,
    PRIMARY KEY (wait_sign_id),
    INDEX (meeting_id),
    INDEX (meeting_user_id),
    INDEX (user_id)
) comment='补签列表';
