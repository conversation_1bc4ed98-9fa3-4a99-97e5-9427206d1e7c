CREATE TABLE t_meeting_comment_member_complex_history (
          history_id  bigint(20) NOT NULL AUTO_INCREMENT comment '主键ID',
          complex_id  bigint(20) NOT NULL comment '综合评定主键ID',
          rating      tinyint(1) NOT NULL comment '等级',
          suggestion  text NOT NULL comment '评定内容',
          recorder    bigint(20) NOT NULL comment '记录人',
          record_time datetime NOT NULL comment '记录时间',
          PRIMARY KEY (history_id)) comment='综合评定流水表';

ALTER TABLE `t_meeting_comment`
    ADD COLUMN `excellent_num` int(5) NOT NULL DEFAULT 0 COMMENT '优秀党员数量' AFTER `org_level`;