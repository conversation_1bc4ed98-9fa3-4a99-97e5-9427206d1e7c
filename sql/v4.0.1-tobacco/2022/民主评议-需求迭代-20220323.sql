ALTER TABLE `t_meeting_comment_member`
    ADD COLUMN `political_type` int(20) NULL COMMENT '政治面貌' AFTER `phone_secret`;
-- 设置政治面貌
UPDATE t_meeting_comment_member mcm SET mcm.political_type = (select political_type from t_user where user_id = mcm.user_id);
-- 删除附件
DELETE
FROM
    t_meeting_file
WHERE
        source = 6
  AND link_id IN ( SELECT comment_member_id FROM t_meeting_comment_member WHERE political_type <> 1 );


ALTER TABLE `t_meeting_comment_member`
    MODIFY COLUMN `phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '评议人手机号' AFTER `user_name`,
    MODIFY COLUMN `phone_secret` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '手机号密文' AFTER `phone`;