-- 组织生活个人积分记录流水表
CREATE TABLE `t_meeting_user_score_detail` (
  `user_score_detail_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `meeting_id` bigint(20) DEFAULT NULL COMMENT '活动编号',
  `meeting_type` tinyint(3) DEFAULT NULL COMMENT '1.党支部党员大会 2.党支部委员会会议 3.党小组会 4.党课  5.主题党日',
  `score` bigint(20) DEFAULT NULL COMMENT '积分数量 正负值',
  `operation_type` tinyint(3) NOT NULL COMMENT '操作类型：0:添加  1:扣分',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户编号',
  `score_type` tinyint(3) DEFAULT NULL COMMENT '1.党员考勤积分 2.讲课人积分 3.党小组长开展积分  4.支委会成员每年基础分 5.党小组长每年基础分',
  `score_sign` int(8) DEFAULT NULL COMMENT '积分时间标记:年或年月数值 例:(2021,202109,202111)',
  `score_token` varchar(32) DEFAULT NULL COMMENT '积分token',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` tinyint(1) DEFAULT NULL COMMENT '上报状态: 1 成功 2 失败',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`user_score_detail_id`),
  KEY (`meeting_id`),
  KEY (`meeting_type`),
  KEY (`user_id`),
  KEY (`status`),
  KEY (`meeting_type`,`user_id`,`score_type`,`score_sign`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='组织生活个人积分记录流水表';

-- 组织生活组织积分记录流水表
CREATE TABLE `t_meeting_org_score_detail` (
  `org_score_detail_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `meeting_id` bigint(20) DEFAULT NULL COMMENT '活动编号',
  `meeting_type` tinyint(3) DEFAULT NULL COMMENT '1.党支部党员大会 2.党支部委员会会议 3.党小组会 4.党课  5.主题党日',
  `score` bigint(20) DEFAULT NULL COMMENT '积分数量 正负值',
  `operation_type` tinyint(3) NOT NULL COMMENT '操作类型：0:添加  1:扣分',
  `org_id` bigint(20) DEFAULT NULL COMMENT '组织编号',
  `score_type` tinyint(3) DEFAULT NULL COMMENT '1.组织生活开展积分',
  `score_sign` int(8) DEFAULT NULL COMMENT '积分时间标记:年或年月数值 例:(2021,202109,202111)',
  `score_token` varchar(32) DEFAULT NULL COMMENT '积分token',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` tinyint(1) DEFAULT NULL COMMENT '上报状态: 1 成功 2 失败',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`org_score_detail_id`),
  KEY (`meeting_id`),
  KEY (`meeting_type`),
  KEY (`org_id`),
  KEY (`status`),
  KEY (`meeting_type`,`org_id`,`score_type`,`score_sign`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='组织生活组织积分记录流水表';