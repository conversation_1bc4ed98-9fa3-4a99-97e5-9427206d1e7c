-- 烟草个性化需求
ALTER TABLE `t_meeting`
    ADD COLUMN `end_time` datetime(0) NULL COMMENT '结束时间',
    ADD COLUMN `record_type` tinyint NULL COMMENT '录入类型 0-发起活动，1-活动录入',
    ADD COLUMN `content` text NULL COMMENT '活动内容',
    ADD COLUMN `notify_type` tinyint NULL COMMENT '推送类型',
    ADD COLUMN `notify_way` tinyint NULL COMMENT '提醒方式',
    ADD COLUMN `notify_time` varchar(50) NULL COMMENT '提醒时间',
    ADD COLUMN `qr_code_hash` varchar(50) NULL COMMENT '二维码hash值',
    ADD COLUMN `allow_all_sign` tinyint NULL COMMENT '允许未收到邀请的人签到',
    ADD COLUMN `total_hours` double DEFAULT NULL COMMENT '活动总时长' AFTER `address`,
    ADD COLUMN `theory_learn` double DEFAULT NULL COMMENT '理论学习时长' AFTER `total_hours`,
    ADD COLUMN `sign_in_way` tinyint(1) COMMENT '签到方式 0：手动签到；1：扫码签到；3：GPS定位签到；4：人脸识别' AFTER `is_sign_in`;

ALTER TABLE `t_meeting_user`
    ADD COLUMN `sign_hash` varchar(50) NULL COMMENT '签到hash值，MD5(meeting_id + user_id)',
    ADD COLUMN `sign_time` datetime(0) NULL COMMENT '签到时间',
    ADD COLUMN `head_url`  varchar(255) NULL COMMENT '头像';


ALTER TABLE `t_meeting_result_file`
    ADD COLUMN `type` tinyint NULL COMMENT '类型 0-图片，1-附件';

ALTER TABLE `t_meeting_draft`
    ADD COLUMN `type` tinyint NOT NULL COMMENT '类型 0-发起活动，1-已有活动之后录入活动，2-直接录入活动',
    ADD COLUMN `meeting_id` bigint(20) NULL COMMENT '会议id';

ALTER TABLE `t_meeting_draft`
DROP INDEX `region_id_user_id_unique`,
ADD INDEX `region_id_user_id`(`region_id`, `org_id`, `user_id`, `type`, `meeting_id`) USING BTREE;


-- 烟草议程表
CREATE TABLE `t_meeting_agenda` (
  `agenda_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '议程编号',
  `meeting_id` bigint(20) NOT NULL COMMENT '活动id',
  `agenda_title` varchar(200) NOT NULL COMMENT '议程标题',
  `agenda_content` varchar(5000) COMMENT '议程内容',
  `last_update_time` datetime DEFAULT now() COMMENT '最后一次操作时间',
  `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后一次操作人',
  PRIMARY KEY (`agenda_id`),
  KEY `meeting_id` (`meeting_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 活动标签表
CREATE TABLE `t_meeting_tag` (
  `meeting_tag_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `meeting_id` bigint(20) DEFAULT NULL COMMENT '活动编号',
  `tag_id` bigint(20) DEFAULT NULL COMMENT '用户中心标签编号',
  `tag_name` varchar(500) DEFAULT NULL COMMENT '标签名称(冗余字段)',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后一次操作时间',
  `last_change_user` bigint DEFAULT NULL COMMENT '最后一次操作人',
  PRIMARY KEY (`meeting_tag_id`),
  KEY (`meeting_id`),
  KEY (`tag_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='活动标签表';