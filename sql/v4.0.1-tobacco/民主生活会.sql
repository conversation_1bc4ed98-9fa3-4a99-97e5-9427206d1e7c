CREATE TABLE t_meeting_life (
  life_id          bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
  title            varchar(100) NOT NULL comment '会议名称',
  years            int(11) NOT NULL comment '年度',
  meeting_id       bigint(20) comment '活动id',
  region_id        bigint(20) NOT NULL comment '区县id',
  org_id           bigint(20) NOT NULL comment '所属组织id',
  org_name         varchar(100) NOT NULL comment '所属组织名称',
  status           tinyint(3) NOT NULL comment '1：新建，2：保存了准备草稿，3：已结束，4：保存会后梳理草稿，5：已上报或归档',
  is_del           tinyint NOT NULL comment '是否删除 0：否，1：是',
  create_time      datetime NOT NULL comment '创建时间',
  update_time      datetime NOT NULL comment '修改时间',
  create_user      bigint(20) NOT NULL,
  last_change_user bigint(20) NOT NULL,
  PRIMARY KEY (life_id)) comment='生活会表';
CREATE INDEX t_meeting_life
  ON t_meeting_life (years, title, region_id, org_id, status, is_del);

CREATE TABLE t_meeting_life_tag (
  life_tag_id bigint(20) NOT NULL AUTO_INCREMENT,
  life_id       bigint(20) NOT NULL comment '民主会议id',
  tag_id      bigint(20) NOT NULL comment '标签id',
  create_user bigint(20) NOT NULL,
  create_time datetime NOT NULL comment '创建时间',
  PRIMARY KEY (life_tag_id));
CREATE INDEX t_meeting_lm_tag2
  ON t_meeting_life_tag (life_id);
CREATE INDEX t_meeting_lm_tag
  ON t_meeting_life_tag (tag_id);
CREATE UNIQUE INDEX t_meeting_life_tag
  ON t_meeting_life_tag (life_id, tag_id);

CREATE TABLE t_meeting_life_notice (
  life_notice_id bigint(20) NOT NULL AUTO_INCREMENT,
  life_id        bigint(20) NOT NULL comment '民主生活会id',
  notice_id      bigint(20) NOT NULL comment '会议通报id',
  step           tinyint NOT NULL comment '1：会前，2：会后',
  create_user    bigint(20) NOT NULL,
  create_time    datetime NOT NULL comment '创建时间',
  PRIMARY KEY (life_notice_id)) comment='会议通报';
CREATE INDEX t_meeting_lm_notice
  ON t_meeting_life_notice (life_id, step);


CREATE TABLE t_meeting_life_study (
  life_study_id bigint(20) NOT NULL AUTO_INCREMENT,
  life_id       bigint(20) NOT NULL comment '民主生活会id',
  study_id      bigint(20) NOT NULL comment '组织生活id',
  step          tinyint NOT NULL comment '1：会前，2：会后',
  create_user   bigint(20) NOT NULL,
  create_time   datetime NOT NULL comment '创建时间',
  PRIMARY KEY (life_study_id)) comment='组织学习';
CREATE INDEX t_meeting_life_study
  ON t_meeting_life_study (life_id, step);

CREATE TABLE t_meeting_advice (
  advice_id        bigint(20) NOT NULL AUTO_INCREMENT,
  life_id          bigint(20) NOT NULL comment '民主生活会id',
  advice_type      tinyint NOT NULL comment '征求类型 1：直接上传，2：问卷调查，3：座谈会，4：个别访谈',
  data_id          bigint(20) comment '问卷/座谈会/个别访谈 id',
  step             tinyint NOT NULL comment '1：会前，2：会后',
  is_del           tinyint NOT NULL comment '是否删除 0：否，1：是',
  create_time      datetime NOT NULL comment '创建时间',
  update_time      datetime NOT NULL comment '修改时间',
  create_user      bigint(20) NOT NULL,
  last_change_user bigint(20) NOT NULL,
  PRIMARY KEY (advice_id)) comment='征求意见';
CREATE INDEX t_meeting_advice
  ON t_meeting_advice (life_id, is_del, step);

CREATE TABLE t_meeting_talk (
  talk_id          bigint(20) NOT NULL AUTO_INCREMENT,
  life_id          bigint(20) NOT NULL comment '民主生活会id',
  talk_type        tinyint(1) NOT NULL comment '1：成员之间 2：成员与部门 3：成员与支部 4：成员与联系点 5：个人访谈',
  location         varchar(100) comment '谈话地点',
  is_submit        tinyint NOT NULL comment '是否提交 0：否，1：是',
  begin_time       datetime NOT NULL comment '开始时间',
  end_time         datetime NOT NULL comment '结束时间',
  create_time      datetime NOT NULL comment '创建时间',
  update_time      datetime NOT NULL comment '修改时间',
  create_user      bigint(20) NOT NULL,
  last_change_user bigint(20) NOT NULL,
  source           tinyint(3) NOT NULL comment '来源 1：无来源，2：民主生活会',
  source_id        bigint(20) NOT NULL comment '来源主键',
  PRIMARY KEY (talk_id)) comment='谈心谈话';
CREATE INDEX t_meeting_talk
  ON t_meeting_talk (life_id, talk_type);


CREATE TABLE t_meeting_talk_content (
  talk_content_id  bigint(20) NOT NULL AUTO_INCREMENT,
  talk_id          bigint(20) NOT NULL,
  title            varchar(100) comment '标题',
  content          text comment '内容',
  is_del           tinyint NOT NULL comment '是否删除 0：否，1：是',
  create_user      bigint(20) NOT NULL,
  last_change_user bigint(20) NOT NULL,
  create_time      datetime NOT NULL comment '创建时间',
  update_time      datetime NOT NULL comment '修改时间',
  PRIMARY KEY (talk_content_id)) comment='谈话内容';
CREATE INDEX t_meeting_talk_content
  ON t_meeting_talk_content (talk_id, is_del);

CREATE TABLE t_meeting_talk_link (
  talk_link_id bigint(20) NOT NULL AUTO_INCREMENT,
  talk_id      bigint(20) NOT NULL,
  user_id      bigint(20) NOT NULL comment '用户id',
  username     varchar(100) NOT NULL comment '用户名',
  type    tinyint(1) NOT NULL comment '1:谈话人，2：被谈话人',
  create_user  bigint(20) NOT NULL,
  create_time  datetime NOT NULL comment '创建时间',
  PRIMARY KEY (talk_link_id),
  INDEX (talk_id)) comment='谈话人员关联表';

CREATE TABLE t_meeting_life_talk (
  life_talk_id bigint(20) NOT NULL AUTO_INCREMENT,
  life_id      bigint(20) NOT NULL comment '主键',
  talk_id      bigint(20) NOT NULL,
  talk_type    tinyint(1) NOT NULL comment '1：成员之间 2：成员与部门 3：成员与支部 4：成员与联系点 5：个人访谈',
  step         tinyint NOT NULL comment '1：会前，2：会后',
  create_user  bigint(20) NOT NULL,
  create_time  datetime NOT NULL comment '创建时间',
  PRIMARY KEY (life_talk_id));
CREATE INDEX t_meeting_life_talk
  ON t_meeting_life_talk (life_id, talk_type, step);

CREATE TABLE t_meeting_check (
  check_id         bigint(20) NOT NULL AUTO_INCREMENT,
  life_id          bigint(20) NOT NULL comment '民主生活会id',
  user_id          bigint(20) NOT NULL comment '用户id',
  username         varchar(100) NOT NULL comment '姓名',
  org_id           bigint(20) NOT NULL comment '用户所属组织',
  org_name         varchar(100) NOT NULL comment '所属组织名称',
  step             tinyint NOT NULL comment '1：会前，2：会后',
  is_del           tinyint NOT NULL comment '是否删除 0：否，1：是',
  create_time      datetime NOT NULL comment '创建时间',
  update_time      datetime NOT NULL comment '修改时间',
  create_user      bigint(20) NOT NULL,
  last_change_user bigint(20) NOT NULL,
  PRIMARY KEY (check_id)) comment='检视剖析表';
CREATE INDEX t_meeting_check
  ON t_meeting_check (life_id, is_del, step);

CREATE TABLE t_meeting_life_file (
  life_file_id bigint(20) NOT NULL AUTO_INCREMENT,
  file_id      bigint(20) NOT NULL comment '文件id',
  file_name    varchar(100) NOT NULL comment '文件名',
  life_id      bigint(20) NOT NULL comment '民主生活会id',
  is_submit    tinyint NOT NULL comment '是否上报 0：否，1：是',
  user_id      bigint(20) comment '上传人id',
  username     varchar(100) comment '上传人姓名',
  type         tinyint NOT NULL,
  data_id      bigint(20) comment '精确数据id',
  step         tinyint NOT NULL comment '1：会前，2：会后',
  is_direct    tinyint NOT NULL comment '是否直接上传 0：否，1：是',
  url          varchar(255) NOT NULL,
  is_del       tinyint NOT NULL comment '是否删除 0：否，1：是',
  create_time  datetime NOT NULL comment '创建时间',
  create_user  bigint(20) NULL,
  update_time      datetime NOT NULL comment '修改时间',
  last_change_user bigint(20),
  PRIMARY KEY (life_file_id),
  INDEX (step));
CREATE INDEX t_meeting_life_file
  ON t_meeting_life_file (life_id, type, step, data_id, is_del);

CREATE TABLE t_meeting_uploader (
  uploader_id bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
  life_id     bigint(20) NOT NULL comment '民主生活会id',
  user_id     bigint(20) NOT NULL comment '用户id',
  username    varchar(50) NOT NULL comment '姓名',
  type        tinyint NOT NULL comment '关联的模块',
  data_id     bigint(20) comment '精确数据id',
  end_time    datetime NOT NULL comment '上传截至时间',
  create_time datetime NOT NULL,
  create_user bigint(20) NOT NULL,
  PRIMARY KEY (uploader_id)) comment='他人上传';
CREATE INDEX t_meeting_uploader
  ON t_meeting_uploader (life_id);
CREATE INDEX t_meeting_uploader_data
  ON t_meeting_uploader (data_id);

CREATE TABLE t_meeting_file_model (
  model_id  bigint(20) NOT NULL AUTO_INCREMENT,
  type        tinyint(3) NOT NULL comment '打包分类',
  name      varchar(30) NOT NULL comment '模块名',
  pack_name varchar(30) NOT NULL comment '打包分类',
  `check`   tinyint(3) NOT NULL comment '是否需要精确数据id 0：否，1：是',
  PRIMARY KEY (model_id)) comment='上传文件相关数据初始化表';
CREATE INDEX t_meeting_file_model
  ON t_meeting_file_model (type);

INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (1, 1, '上传会议方案', '会议方案', 0);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (2, 2,'会前学习', '会前学习', 0);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (3, 2,'组织学习', '会前学习', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (4, 3,'检视剖析材料（领导班子）', '检视剖析材料', 0);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (5, 3,'班子成员个人检视剖析（原版）', '检视剖析材料', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (6, 3,'班子成员个人检视剖析（签字版）', '检视剖析材料', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (7, 4,'征求意见-直接上传', '征求意见', 0);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (8, 4,'征求意见-问卷', '征求意见', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (9, 4,'征求意见-个别访谈', '征求意见', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (10, 4,'征求意见-座谈会', '征求意见', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (11, 5,'谈心谈话（班子成员之间）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (12, 5,'谈心谈话（成员与分管部门）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (13, 5,'谈心谈话（成员与党支部）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (14, 5,'谈心谈话（成员与联系点单位）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (15, 6,'上传通报材料', '通报材料', 0);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (16, 7,'会前其他材料', '会前其他资料', 0);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (17, 8,'发起民主生活会', '会前准备完毕', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (18, 9,'整改方案', '整改方案', 0);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (19, 10,'情况报告', '情况报告', 0);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (20, 11,'会议通报', '会议通报', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (21, 12,'会后其他材料', '会后其他材料', 0);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (22, 4,'征求意见-个别访谈（模版）', '征求意见', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (23, 5,'谈心谈话（班子成员之间-模板）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (24, 5,'谈心谈话（成员与分管部门-模板）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (25, 5,'谈心谈话（成员与党支部-模板）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_model` (`model_id`, `type` , `name`, `pack_name`, `check`) VALUES (26, 5,'谈心谈话（成员与联系点单位-模板）', '谈心谈话', 1);

ALTER TABLE `t_meeting_file`
MODIFY COLUMN `source` int(10) NOT NULL COMMENT '文件来源 1 组织奖惩 2组织评议 3 党员奖惩 4 谈心谈话' AFTER `link_id`;

-- 新增类型
ALTER TABLE `t_type`
    ADD COLUMN `type_sys` tinyint(1) NOT NULL DEFAULT 1 COMMENT '特殊类型 1:历史组织生活类型  2:民主生活会 3:民主生活会会前学习 4:民主生活会座谈会 5:组织生活会 6:组织生活会会前学习 7:组织生活会座谈会' AFTER `code`;

set @category=2;
set @topOrgId=3;
set @topOrgName='中国烟草总公司重庆市公司';

-- 新增民主生活会
INSERT INTO `t_type`( `category_id`, `org_id`, `org_name`, `type`, `category`, `code`, `create_time`, `create_user`, `update_time`, `last_change_user`, `has_lecturer`, `has_lecture_title`,`type_sys`) VALUES ( @category, @topOrgId, @topOrgName, '民主生活会', '基本组织生活', -1, now(), -999, NULL, NULL, 0, 0, 2);

-- 新增民主生活会会前学习
INSERT INTO `t_type`( `category_id`, `org_id`, `org_name`, `type`, `category`, `code`, `create_time`, `create_user`, `update_time`, `last_change_user`, `has_lecturer`, `has_lecture_title`,`type_sys`) VALUES ( @category, @topOrgId, @topOrgName, '民主生活会会前学习', '基本组织生活', -1, now(), -999, NULL, NULL, 0, 0, 3);

-- 新增民主生活会座谈会
INSERT INTO `t_type`( `category_id`, `org_id`, `org_name`, `type`, `category`, `code`, `create_time`, `create_user`, `update_time`, `last_change_user`, `has_lecturer`, `has_lecture_title`,`type_sys`) VALUES ( @category, @topOrgId, @topOrgName, '民主生活会座谈会', '基本组织生活', -1, now(), -999, NULL, NULL, 0, 0, 4);

-- meeting主表新增关联民主生活会字段
ALTER TABLE `t_meeting`
    ADD COLUMN `life_id` bigint(0) NULL COMMENT '民主生活会主键id' AFTER `sign_time`,
ADD COLUMN `model_id` bigint(0) NULL COMMENT '关联上民主生活的模块id :t_meeting_file_model' AFTER `life_id`,
ADD INDEX `life_id`(`life_id`) USING BTREE;
-- 附件表新增下载附件名
 alter table  t_meeting_life_file  ADD COLUMN file_name_down varchar(255) comment '下载名' after file_name;
 -- t_meeting_life增加org_level字段，便于按组织层级统计
 alter table t_meeting_life add column `org_level` varchar(512) DEFAULT NULL COMMENT '组织树父级路径' after org_name;