drop table t_meeting_talk;
CREATE TABLE t_meeting_talk (
                                talk_id          bigint(20) NOT NULL AUTO_INCREMENT,
                                talk_type        tinyint(1) NOT NULL comment '1：成员之间   2：成员与部门    3：成员与支部    4：成员与联系点   5：个人访谈',
                                location         varchar(500) comment '谈话地点',
                                is_submit        tinyint(1) NOT NULL comment '是否提交 0：否，1：是',
                                org_id           bigint(20) NOT NULL comment '组织ID',
                                org_name         varchar(200) NOT NULL comment '组织姓名',
                                org_level        varchar(100) NOT NULL comment '组织层级关系',
                                `status`         int(10) comment '状态1-正常，0-删除',
                                begin_time       datetime NULL comment '开始时间',
                                end_time         datetime NULL comment '结束时间',
                                source           tinyint(3) NOT NULL comment '来源：0 - 无来源 1 - 民族生活会会前，2 - 民主生活会会后',
                                source_id        bigint(20) NULL comment '来源主键',
                                copy_id          bigint(20) NOT NULL DEFAULT 0 comment '复制源ID',
                                region_id        bigint(20) NOT NULL COMMENT '区县ID',
                                create_time      datetime NOT NULL comment '创建时间',
                                update_time      datetime NULL comment '修改时间',
                                create_user      bigint(20) NOT NULL comment '创建人',
                                last_change_user bigint(20) NULL comment '最后修改人',
                                PRIMARY KEY (talk_id)) comment='谈心谈话主表';
drop table t_meeting_talk_content;
CREATE TABLE t_meeting_talk_content (
                                talk_content_id  bigint(20) NOT NULL AUTO_INCREMENT,
                                talk_id          bigint(20) NOT NULL comment '主表ID',
                                title            varchar(500) comment '标题',
                                content          text comment '内容',
                                `status`         tinyint NOT NULL comment '状态1-正常，0-删除',
                                create_user      bigint(20) NOT NULL comment '创建人',
                                last_change_user bigint(20) NULL comment '最后修改人',
                                create_time      datetime NOT NULL comment '创建时间',
                                update_time      datetime NULL comment '修改时间',
                                PRIMARY KEY (talk_content_id)) comment='谈话内容';
drop table t_meeting_talk_link;
CREATE TABLE t_meeting_talk_link (
                                 talk_link_id bigint(20) NOT NULL AUTO_INCREMENT,
                                 talk_id      bigint(20) NOT NULL,
                                 user_id      bigint(20) NOT NULL comment '用户id',
                                 username     varchar(100) NOT NULL comment '用户名',
                                 type         tinyint(1) NOT NULL comment '1:谈话人  2：被谈话人',
                                 status       int(10) comment '状态： 1-正常， 0-删除',
                                 create_user      bigint(20) NOT NULL comment '创建人',
                                 last_change_user bigint(20) NULL comment '最后修改人',
                                 create_time      datetime NOT NULL comment '创建时间',
                                 update_time      datetime NULL comment '修改时间',
                                 PRIMARY KEY (talk_link_id),
                                 INDEX (talk_id)) comment='谈话人员关联表';


ALTER TABLE `t_meeting_file`
    ADD COLUMN `file_id` bigint(20) NULL COMMENT '文件ID' AFTER `t_meeting_file_id`;

