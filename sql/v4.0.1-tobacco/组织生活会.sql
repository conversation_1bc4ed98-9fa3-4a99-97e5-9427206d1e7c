CREATE TABLE t_meeting_org_life (
  life_id          bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
  title            varchar(100) NOT NULL comment '会议名称',
  years            int(11) NOT NULL comment '年度',
  meeting_id       bigint(20) comment '活动id',
  region_id        bigint(20) NOT NULL comment '区县id',
  org_id           bigint(20) NOT NULL comment '所属组织id',
  org_name         varchar(100) NOT NULL comment '所属组织名称',
  status           tinyint(3) NOT NULL comment '1：新建，2：保存了准备草稿，3：已结束，4：保存会后梳理草稿，5：已上报或归档',
  is_del           tinyint NOT NULL comment '是否删除 0：否，1：是',
  create_time      datetime NOT NULL comment '创建时间',
  update_time      datetime  comment '修改时间',
  create_user      bigint(20) NOT NULL,
  last_change_user bigint(20) ,
  PRIMARY KEY (life_id)) comment='组织生活会表';
CREATE INDEX t_meeting_org_life
  ON t_meeting_org_life (years, title, region_id, org_id, status, is_del);

CREATE TABLE t_meeting_org_life_tag (
  life_tag_id bigint(20) NOT NULL AUTO_INCREMENT,
  life_id       bigint(20) NOT NULL comment '组织生活会议id',
  tag_id      bigint(20) NOT NULL comment '标签id',
  create_user bigint(20) NOT NULL,
  create_time datetime NOT NULL comment '创建时间',
  PRIMARY KEY (life_tag_id));
CREATE INDEX t_meeting_lm_tag2
  ON t_meeting_org_life_tag (life_id);
CREATE INDEX t_meeting_lm_tag
  ON t_meeting_org_life_tag (tag_id);
CREATE UNIQUE INDEX t_meeting_life_tag
  ON t_meeting_org_life_tag (life_id, tag_id);

CREATE TABLE t_meeting_org_life_notice (
  life_notice_id bigint(20) NOT NULL AUTO_INCREMENT,
  life_id        bigint(20) NOT NULL comment '组织生活会id',
  notice_id      bigint(20) NOT NULL comment '会议通报id',
  step           tinyint NOT NULL comment '1：会前，2：会后',
  create_user    bigint(20) NOT NULL,
  create_time    datetime NOT NULL comment '创建时间',
  PRIMARY KEY (life_notice_id)) comment='会议通报';
CREATE INDEX t_meeting_lm_notice
  ON t_meeting_org_life_notice (life_id, step);


CREATE TABLE t_meeting_org_life_study (
  life_study_id bigint(20) NOT NULL AUTO_INCREMENT,
  life_id       bigint(20) NOT NULL comment '组织生活会id',
  study_id      bigint(20) NOT NULL comment '组织生活id',
  step          tinyint NOT NULL comment '1：会前，2：会后',
  create_user   bigint(20) NOT NULL,
  create_time   datetime NOT NULL comment '创建时间',
  PRIMARY KEY (life_study_id)) comment='组织学习';
CREATE INDEX t_meeting_org_life_study
  ON t_meeting_org_life_study (life_id, step);

CREATE TABLE t_meeting_org_advice (
  advice_id        bigint(20) NOT NULL AUTO_INCREMENT,
  life_id          bigint(20) NOT NULL comment '组织生活会id',
  advice_type      tinyint NOT NULL comment '征求类型 1：直接上传，2：问卷调查，3：座谈会，4：个别访谈',
  data_id          bigint(20) comment '问卷/座谈会/个别访谈 id',
  step             tinyint NOT NULL comment '1：会前，2：会后',
  is_del           tinyint NOT NULL comment '是否删除 0：否，1：是',
  create_time      datetime NOT NULL comment '创建时间',
  update_time      datetime  comment '修改时间',
  create_user      bigint(20) NOT NULL,
  last_change_user bigint(20) ,
  PRIMARY KEY (advice_id)) comment='征求意见';
CREATE INDEX t_meeting_advice
  ON t_meeting_org_advice (life_id, is_del, step);


CREATE TABLE t_meeting_org_check (
  check_id         bigint(20) NOT NULL AUTO_INCREMENT,
  life_id          bigint(20) NOT NULL comment '组织生活会id',
  user_id          bigint(20) NOT NULL comment '用户id',
  username         varchar(100) NOT NULL comment '姓名',
  org_id           bigint(20) NOT NULL comment '用户所属组织',
  org_name         varchar(100) NOT NULL comment '所属组织名称',
  step             tinyint NOT NULL comment '1：会前，2：会后',
  is_del           tinyint NOT NULL comment '是否删除 0：否，1：是',
  create_time      datetime NOT NULL comment '创建时间',
  update_time      datetime  comment '修改时间',
  create_user      bigint(20) NOT NULL,
  last_change_user bigint(20) ,
  PRIMARY KEY (check_id)) comment='检视剖析表';
CREATE INDEX t_meeting_org_check
  ON t_meeting_org_check (life_id, is_del, step);

CREATE TABLE t_meeting_org_life_file (
  life_file_id bigint(20) NOT NULL AUTO_INCREMENT,
  file_id      bigint(20) comment '文件id',
  file_name    varchar(100) NOT NULL comment '文件名',
  life_id      bigint(20) NOT NULL comment '组织生活会id',
  is_submit    tinyint NOT NULL comment '是否上报 0：否，1：是',
  user_id      bigint(20) comment '上传人id',
  username     varchar(100) comment '上传人姓名',
  type         tinyint NOT NULL,
  data_id      bigint(20) comment '精确数据id',
  step         tinyint NOT NULL comment '1：会前，2：会后',
  is_direct    tinyint NOT NULL comment '是否直接上传 0：否，1：是',
  url          varchar(255) NOT NULL,
  is_del       tinyint NOT NULL comment '是否删除 0：否，1：是',
  create_time  datetime NOT NULL comment '创建时间',
  create_user  bigint(20) NULL,
  update_time      datetime NOT NULL comment '修改时间',
  last_change_user bigint(20),
  PRIMARY KEY (life_file_id),
  INDEX (step));
CREATE INDEX t_meeting_org_life_file
  ON t_meeting_org_life_file (life_id, type, step, data_id, is_del);

CREATE TABLE t_meeting_org_uploader (
  uploader_id bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
  life_id     bigint(20) NOT NULL comment '组织生活会id',
  user_id     bigint(20) NOT NULL comment '用户id',
  username    varchar(50) NOT NULL comment '姓名',
  type        tinyint NOT NULL comment '关联的模块',
  data_id     bigint(20) comment '精确数据id',
  end_time    datetime NOT NULL comment '上传截至时间',
  create_time datetime NOT NULL,
  create_user bigint(20) NOT NULL,
  PRIMARY KEY (uploader_id)) comment='他人上传';
CREATE INDEX t_meeting_org_uploader
  ON t_meeting_org_uploader (life_id);
CREATE INDEX t_meeting_org_uploader_data
  ON t_meeting_org_uploader (data_id);

CREATE TABLE t_meeting_file_org_model (
  model_id  bigint(20) NOT NULL AUTO_INCREMENT,
  type        tinyint(3) NOT NULL comment '原民主生活打包分类',
  org_life_type        tinyint(3) NOT NULL comment '组织生活会分类',
  name      varchar(30) NOT NULL comment '模块名',
  pack_name varchar(30) NOT NULL comment '打包分类名',
  `check`   tinyint(3) NOT NULL comment '是否需要精确数据id 0：否，1：是',
  PRIMARY KEY (model_id)) comment='上传文件相关数据初始化表';
CREATE INDEX t_meeting_file_org_model
  ON t_meeting_file_org_model (type);

INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (1, 1,1,'上传会议方案', '会议方案', 0);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (2, 2,2,'会前学习', '会前学习', 0);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` , `org_life_type`,`name`, `pack_name`, `check`) VALUES (3, 2,2,'组织学习', '会前学习', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (7, 4,3,'征求意见-直接上传', '征求意见', 0);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` , `org_life_type`,`name`, `pack_name`, `check`) VALUES (8, 4,3,'征求意见-问卷', '征求意见', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` , `org_life_type`,`name`, `pack_name`, `check`) VALUES (9, 4,3,'征求意见-个别访谈', '征求意见', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (10, 4,3,'征求意见-座谈会', '征求意见', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (11, 5,4,'谈心谈话（党支部书记与班子成员）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (12, 5,4,'谈心谈话（党支部班子成员之间）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (13, 5,4,'谈心谈话（班子成员和党员之间）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (14, 5,4,'谈心谈话（党员之间）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` , `org_life_type`,`name`, `pack_name`, `check`) VALUES (16, 7,6,'会前其他材料', '会前其他资料', 0);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (17, 8,7,'发起民主生活会', '会前准备完毕', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (18, 9,9,'整改方案', '整改方案', 0);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (19, 10,10,'情况报告', '情况报告', 0);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (20, 11,11,'会议通报', '会议通报', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (21, 12,12,'会后其他材料', '会后其他材料', 0);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (22, 4,3,'征求意见-个别访谈（模版）', '征求意见', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (23, 5,4,'谈心谈话（党支部书记与班子成员-模板）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` , `org_life_type`,`name`, `pack_name`, `check`) VALUES (24, 5,4,'谈心谈话（党支部班子成员之间-模板）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (25, 5,4,'谈心谈话（班子成员和党员之间-模板）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (26, 5,4,'谈心谈话（党员之间-模板）', '谈心谈话', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` , `org_life_type`,`name`, `pack_name`, `check`) VALUES (27, 3,5,'支部班子和支部委员对照检查材料', '对照检查材料', 1);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` , `org_life_type`,`name`, `pack_name`, `check`) VALUES (28, 13,8,'党员民主评议测评表', '民主评议', 0);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` ,`org_life_type`, `name`, `pack_name`, `check`) VALUES (29, 13,8,'组织民主评议测评表', '民主评议', 0);
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type` , `org_life_type`,`name`, `pack_name`, `check`) VALUES (30, 13,8,'综合评定附件', '民主评议', 0);
-- 附件表新增下载附件名
 alter table  t_meeting_org_life_file  ADD COLUMN file_name_down varchar(255) comment '下载名' after file_name;
 -- t_meeting_org_life增加org_level字段，便于按组织层级统计
 alter table t_meeting_org_life add column `org_level` varchar(512) DEFAULT NULL COMMENT '组织树父级路径' after org_name;

 CREATE TABLE `t_meeting_org_life_comment` (
   `life_comment_id` bigint(20) NOT NULL AUTO_INCREMENT,
   `life_id` bigint(20) NOT NULL COMMENT '组织生活会id',
   `comment_id` bigint(20) NOT NULL COMMENT '民主评议id',
  `create_user` bigint(20) NOT NULL,
   `create_time` datetime NOT NULL COMMENT '创建时间',
   PRIMARY KEY (`life_comment_id`) USING BTREE,
   KEY `t_meeting_life_comment` (`life_id`) USING BTREE
 ) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='民主评议关联关系表';

-- 新增类型
ALTER TABLE `t_type`
    MODIFY COLUMN `type_sys` tinyint(1) NOT NULL DEFAULT 1 COMMENT '特殊类型 1:历史组织生活类型  2:民主生活会 3:民主生活会会前学习 4:民主生活会座谈会 5:组织生活会 6:组织生活会会前学习 7:组织生活会座谈会' AFTER `code`;

set @category=2;
set @topOrgId=3;
set @topOrgName='中国烟草总公司重庆市公司';

-- 新增民主生活会
INSERT INTO `t_type`( `category_id`, `org_id`, `org_name`, `type`, `category`, `code`, `create_time`, `create_user`, `update_time`, `last_change_user`, `has_lecturer`, `has_lecture_title`,`type_sys`) VALUES ( @category, @topOrgId, @topOrgName, '组织生活会', '基本组织生活', -1, now(), -999, NULL, NULL, 0, 0, 5);

-- 新增民主生活会会前学习
INSERT INTO `t_type`( `category_id`, `org_id`, `org_name`, `type`, `category`, `code`, `create_time`, `create_user`, `update_time`, `last_change_user`, `has_lecturer`, `has_lecture_title`,`type_sys`) VALUES ( @category, @topOrgId, @topOrgName, '组织生活会会前学习', '基本组织生活', -1, now(), -999, NULL, NULL, 0, 0, 6);

-- 新增民主生活会座谈会
INSERT INTO `t_type`( `category_id`, `org_id`, `org_name`, `type`, `category`, `code`, `create_time`, `create_user`, `update_time`, `last_change_user`, `has_lecturer`, `has_lecture_title`,`type_sys`) VALUES ( @category, @topOrgId, @topOrgName, '组织生活会座谈会', '基本组织生活', -1, now(), -999, NULL, NULL, 0, 0, 7);

-- 用于区分组织生活会与民主生活会
ALTER TABLE `t_meeting`
ADD COLUMN `source_type` tinyint(3) DEFAULT 0 COMMENT '1:民主生活会 2:组织生活会' AFTER `can_sign`;

-- 补填以前的民主生活会修改source_type=1
update t_meeting set source_type=1 where life_id is not null

-- 关联活动
ALTER TABLE `t_meeting_org_life_study` ADD UNIQUE lifeid_meetingid_step( life_id,study_id,step);

-- 增加支部班子对照检查
INSERT INTO `t_meeting_file_org_model` (`model_id`, `type`, `org_life_type`, `name`, `pack_name`, `check`) VALUES (31, 3, 5, '支部班子对照检查材料', '对照检查材料', 0);
update t_meeting_file_org_model set name='班子成员对照检查材料' where model_id=27;
alter table t_meeting_org_life_study add column has_direct_relate tinyint(1) COMMENT '是否是直接关联的：null：否-直接发起的 1：是';
alter table t_meeting_org_life_study  modify has_direct_relate tinyint(1)   COMMENT '是否是直接关联的：null：否-直接发起的 1：是' after step;


--组织生活会/民主生活会选择上传人发送钉钉消息
INSERT INTO `t_msg_template`(`template_id`, `channel_id`, `user_id`, `name`, `is_delete`, `channel_sub_id`, `channel_type`, `title`, `content`, `attachment`, `create_id`, `update_id`, `create_time`, `update_time`, `third_template_id`) VALUES (205, 240, 13, '生活会上传任务通知消息', 0, 1, 3, NULL, '{{user_name}}您好，您收到一项新的任务：{{source_name}}-{{task_name}},请您及时到电脑端的待办任务中进行处理。如已处理，请忽略此消息', NULL, -1, NULL, now(), NULL, NULL);
INSERT INTO `t_msg_placeholder`(`template_id`, `name`, `length`, `create_id`, `update_id`, `create_time`, `update_time`) VALUES (205, 'user_name', 100, NULL, NULL, now(), NULL);
INSERT INTO `t_msg_placeholder`(`template_id`, `name`, `length`, `create_id`, `update_id`, `create_time`, `update_time`) VALUES (205, 'source_name', 100, NULL, NULL, now(), NULL);
INSERT INTO `t_msg_placeholder`(`template_id`, `name`, `length`, `create_id`, `update_id`, `create_time`, `update_time`) VALUES (205, 'task_name', 100, NULL, NULL, now(), NULL);