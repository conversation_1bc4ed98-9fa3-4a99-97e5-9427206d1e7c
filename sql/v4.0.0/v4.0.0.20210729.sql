-- 南岸区网信办任务管理系统
-- ----------------------------
-- Table structure for t_meeting_sbw_task
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_sbw_task`;
CREATE TABLE `t_meeting_sbw_task`  (
  `task_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务id',
  `region_id` bigint(20) NOT NULL COMMENT '区县id',
  `org_id` bigint(20) NOT NULL COMMENT '发起组织id',
  `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发起组织名称',
  `task_type` int(1) NULL DEFAULT NULL COMMENT '1：工作任务，2：转办单',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务编号',
  `time_type` int(1) NULL DEFAULT NULL COMMENT '时间类型',
  `begin_time` datetime(0) NULL DEFAULT NULL COMMENT '任务开始时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '任务结束时间',
  `source` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '舆情来源',
  `type_id` bigint(20) NULL DEFAULT NULL COMMENT '舆情分类',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '舆情概要',
  `verify_org_id` bigint(20) NULL DEFAULT NULL COMMENT '审核组织id',
  `verify_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核组织名称',
  `file_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件id \',\'分隔',
  `filename` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文件名 ‘,’分隔',
  `notice` int(1) NULL DEFAULT NULL COMMENT '1：短信，2：微信，',
  `remark` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `status` int(1) NOT NULL COMMENT '-1: 待处理 -2: 不予处理\r\n1：草稿，2：未开始，3：进行中，4：已结束',
  `is_del` int(1) NOT NULL DEFAULT 0 COMMENT '是否删除\r\n0：否，1：是',
  `is_handle` int(1) NULL DEFAULT NULL COMMENT '0:不予处理 1:处理',
  `no_handle_content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '不予处理原因',
  `create_user` bigint(20) NOT NULL COMMENT '创建人id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '最后更新用户id',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`task_id`) USING BTREE,
  INDEX `region_id`(`region_id`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `begin_time`(`begin_time`) USING BTREE,
  INDEX `end_time`(`end_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '南岸区网信办任务表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for t_meeting_sbw_shift_task
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_sbw_shift_task`;
CREATE TABLE `t_meeting_sbw_shift_task`  (
  `shift_task_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_id` bigint(20) NULL DEFAULT NULL COMMENT '任务id',
  `region_id` bigint(20) NOT NULL COMMENT '区县id',
  `org_id` bigint(20) NOT NULL COMMENT '发起组织id',
  `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发起组织名称',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务编号',
  `time_type` tinyint(1) NULL DEFAULT NULL COMMENT '时间类型  1:本月内 2:本季度内 3:本年内 4:自定义时间',
  `begin_time` datetime(0) NULL DEFAULT NULL COMMENT '任务开始时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '任务结束时间',
  `source` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '舆情来源',
  `type_id` bigint(20) NULL DEFAULT NULL COMMENT '舆情分类',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '舆情概要',
  `file_json` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文件json',
  `remark` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `status` int(1) NOT NULL COMMENT '1：草稿，2：已提交，3：处理中，4：不予处理  5,已结束',
  `is_del` int(1) NOT NULL DEFAULT 0 COMMENT '是否删除\r\n0：否，1：是',
  `is_read` int(1) NOT NULL COMMENT '0:未读 1:已读',
  `create_user` bigint(20) NOT NULL COMMENT '创建人id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '最后更新用户id',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`shift_task_id`) USING BTREE,
  INDEX `task_id`(`task_id`) USING BTREE,
  INDEX `region_id`(`region_id`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `begin_time`(`begin_time`) USING BTREE,
  INDEX `end_time`(`end_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for t_meeting_sbw_task_flow
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_sbw_task_flow`;
CREATE TABLE `t_meeting_sbw_task_flow`  (
  `flow_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '流水id',
  `task_id` bigint(20) NOT NULL COMMENT '任务id',
  `region_id` bigint(20) NOT NULL COMMENT '区县id',
  `org_id` bigint(20) NOT NULL COMMENT '组织id',
  `user_id` bigint(20) NOT NULL COMMENT '操作用户id',
  `operate_org_id` bigint(20) NOT NULL COMMENT '操作组织id',
  `operate_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作组织名称',
  `type` int(1) NOT NULL COMMENT '操作类型\r\n-3：代办已提交\r\n-2： 不予处理\r\n-1： 已处理\r\n1：已提交接受\r\n2：已提交拒绝\r\n3：已退回\r\n4：已拒绝\r\n5：已同意',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '拒绝原因/审核意见',
  `create_time` datetime(0) NOT NULL COMMENT '操作时间',
  PRIMARY KEY (`flow_id`) USING BTREE,
  INDEX `task_id`(`task_id`) USING BTREE,
  INDEX `region_id`(`region_id`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '南岸区网信办任务流水表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for t_meeting_sbw_task_org
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_sbw_task_org`;
CREATE TABLE `t_meeting_sbw_task_org`  (
  `to_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `region_id` bigint(20) NOT NULL COMMENT '区县id',
  `task_id` bigint(20) NOT NULL COMMENT '任务id',
  `org_id` bigint(20) NOT NULL COMMENT '组织id',
  `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组织名称',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务标题',
  `type` int(1) NOT NULL COMMENT '操作类型\r\n1：已提交接受\r\n2：已提交拒绝\r\n3：已退回\r\n4：已拒绝\r\n5：已同意',
  `status` int(1) NOT NULL COMMENT '2：未开始，3：进行中，4：已结束，5：已拒绝',
  `is_read` int(1) NOT NULL COMMENT '0:未读 1:已读',
  `begin_time` datetime(0) NOT NULL COMMENT '开始时间',
  `end_time` datetime(0) NOT NULL COMMENT '结束时间',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`to_id`) USING BTREE,
  UNIQUE INDEX `t_meeting_sbw_task_org`(`region_id`, `task_id`, `org_id`) USING BTREE,
  INDEX `begin_time`(`begin_time`) USING BTREE,
  INDEX `end_time`(`end_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务-组织中间表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for t_meeting_sbw_handle
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_sbw_handle`;
CREATE TABLE `t_meeting_sbw_handle`  (
  `handle_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '审核转办单信息主键id',
  `task_id` bigint(20) NOT NULL COMMENT '任务id',
  `region_id` bigint(20) NOT NULL COMMENT '区县id',
  `org_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL COMMENT '操作用户id',
  `user_org_id` bigint(20) NOT NULL COMMENT '操作用户的所属组织id',
  `user_org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作用户的支部名称',
  `open_status` tinyint(3) NULL DEFAULT NULL COMMENT '是否建议公开回复(1：是，2：否)',
  `private_reason` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '不公开理由',
  `flag` tinyint(3) NOT NULL COMMENT '1:我的任务-提交转办单\r\n2:任务审核-审核转办单',
  `handle_content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '处置内容/任务完成情况',
  `handle_status` tinyint(3) NOT NULL COMMENT '处理状态：\r\n1：已提交接受\r\n2：已提交拒绝\r\n3：已退回\r\n4：已拒绝\r\n5：已同意',
  `handle_comment` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '拒绝原因、审核意见',
  `status` int(1) NOT NULL COMMENT '1：草稿，2：提交',
  `file_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件id \',\'分隔',
  `filename` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文件名 ‘,’分隔',
  `accept_org` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '转办组织',
  `accept_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收人',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `accept_time` datetime(0) NULL DEFAULT NULL COMMENT '转办时间',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`handle_id`) USING BTREE,
  UNIQUE INDEX `t_meeting_sbw_handle`(`task_id`, `region_id`, `org_id`, `user_org_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务信息通用表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for t_meeting_sbw_type
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_sbw_type`;
CREATE TABLE `t_meeting_sbw_type`  (
  `type_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type_name` varchar(32) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL COMMENT '舆情分类名称',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`type_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Compact;

SET FOREIGN_KEY_CHECKS = 1;