ALTER TABLE `t_meeting_org_commend_penalize`
ADD COLUMN `region_id`  bigint(20) NULL COMMENT '区县id' AFTER `reward_type`;


ALTER TABLE `t_meeting_org_debrief_review`
ADD COLUMN `region_id`  bigint(20) NULL COMMENT '区县id' AFTER `org_level`;


ALTER TABLE `t_meeting_user_commend_penalize`
ADD COLUMN `region_id`  bigint(20) NULL COMMENT '区县id' AFTER `last_change_user`;

ALTER TABLE `t_meeting_user_comment`
ADD COLUMN `region_id`  bigint(20) NULL COMMENT '区县id' AFTER `last_change_user`;


ALTER TABLE t_meeting_user_comment_statistics
ADD COLUMN `region_id`  bigint(20) NULL COMMENT '区县id' AFTER `last_change_user`;

ALTER TABLE `t_category`
ADD COLUMN `region_id`  bigint(20) NULL COMMENT '区县id' AFTER `update_time`;


-- t_meeting_plan 添加region_id字段
ALTER TABLE `t_meeting_plan`
ADD COLUMN `region_id` bigint(20) NOT NULL COMMENT '区县ID' AFTER `create_time`,
ADD INDEX `region_id`(`region_id`);


-- t_group 添加region_id字段
ALTER TABLE `t_group`
ADD COLUMN `region_id` bigint(20) NOT NULL COMMENT '区县ID' AFTER `create_time`,
ADD INDEX `region_id`(`region_id`);


-- t_meeting 添加region_id字段
ALTER TABLE `t_meeting`
ADD COLUMN `region_id` bigint(20) NOT NULL COMMENT '区县ID' AFTER `create_time`,
ADD INDEX `region_id`(`region_id`);

update t_meeting_org_commend_penalize set region_id = 3;
update t_meeting_org_debrief_review set region_id = 3;
update t_meeting_user_commend_penalize set region_id = 3;
update t_meeting_user_comment set region_id = 3;
update t_meeting_user_comment_statistics set region_id = 3;
update t_category set region_id = 3;
update t_meeting_plan set region_id = 3;
update t_group set region_id = 3;
update t_meeting set region_id = 3;