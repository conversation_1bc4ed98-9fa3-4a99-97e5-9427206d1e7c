-- Data migration sql


-- ==============================================
-- 备份表数据
-- ==============================================
-- CREATE TABLE IF NOT EXISTS t_meeting_back SELECT * FROM t_meeting;
-- CREATE TABLE IF NOT EXISTS t_category_back SELECT * FROM t_category;
-- CREATE TABLE IF NOT EXISTS t_group_back SELECT * FROM t_group
-- CREATE TABLE IF NOT EXISTS t_meeting_file_back SELECT * FROM t_meeting_file;
-- CREATE TABLE IF NOT EXISTS t_meeting_history_back SELECT * FROM t_meeting_history
-- CREATE TABLE IF NOT EXISTS t_meeting_leave_back SELECT * FROM t_meeting_leave;
-- CREATE TABLE IF NOT EXISTS t_meeting_user_back SELECT * FROM t_meeting_user;
-- CREATE TABLE IF NOT EXISTS t_meeting_org_back SELECT * FROM t_meeting_org;
-- CREATE TABLE IF NOT EXISTS t_meeting_plan_back SELECT * FROM t_meeting_plan;
-- CREATE TABLE IF NOT EXISTS t_meeting_org_change_log_back SELECT * FROM t_meeting_org_change_log;
-- CREATE TABLE IF NOT EXISTS t_meeting_org_commend_penalize_back SELECT * FROM t_meeting_org_commend_penalize;
-- CREATE TABLE IF NOT EXISTS t_meeting_org_debrief_review_back SELECT * FROM t_meeting_org_debrief_review;
-- CREATE TABLE IF NOT EXISTS t_meeting_org_group_back SELECT * FROM t_meeting_org_group;
-- CREATE TABLE IF NOT EXISTS t_meeting_org_period_back SELECT * FROM t_meeting_org_period;
-- CREATE TABLE IF NOT EXISTS t_meeting_plan_limit_back SELECT * FROM t_meeting_plan_limit;
-- CREATE TABLE IF NOT EXISTS t_meeting_plan_limit_type_back SELECT * FROM t_meeting_plan_limit_type;
-- CREATE TABLE IF NOT EXISTS t_meeting_plan_log_back SELECT * FROM t_meeting_plan_log;
-- CREATE TABLE IF NOT EXISTS t_meeting_require_back SELECT * FROM t_meeting_require;
-- CREATE TABLE IF NOT EXISTS t_type_back SELECT * FROM t_type;
-- CREATE TABLE IF NOT EXISTS t_meeting_resolution_file_back SELECT * FROM t_meeting_resolution_file;
-- CREATE TABLE IF NOT EXISTS t_meeting_result_back SELECT * FROM t_meeting_result;
-- CREATE TABLE IF NOT EXISTS t_meeting_result_hs_back SELECT * FROM t_meeting_result_hs;
-- CREATE TABLE IF NOT EXISTS t_meeting_task_back SELECT * FROM t_meeting_task;
-- CREATE TABLE IF NOT EXISTS t_meeting_topic_back SELECT * FROM t_meeting_topic;
-- CREATE TABLE IF NOT EXISTS t_topic_back SELECT * FROM t_topic;
-- CREATE TABLE IF NOT EXISTS t_meeting_type_back SELECT * FROM t_meeting_type;
-- CREATE TABLE IF NOT EXISTS t_meeting_user_back SELECT * FROM t_meeting_user;
-- CREATE TABLE IF NOT EXISTS t_meeting_user_commend_penalize_back SELECT * FROM t_meeting_user_commend_penalize;
-- CREATE TABLE IF NOT EXISTS t_meeting_user_comment_back SELECT * FROM t_meeting_user_comment;
-- CREATE TABLE IF NOT EXISTS t_meeting_user_comment_statistics_back SELECT * FROM t_meeting_user_comment_statistics;
-- CREATE TABLE IF NOT EXISTS t_topic_content_back SELECT * FROM t_topic_content;
-- CREATE TABLE IF NOT EXISTS t_topic_file_back SELECT * FROM t_topic_file;
-- CREATE TABLE IF NOT EXISTS t_topic_log_back SELECT * FROM t_topic_log;
-- CREATE TABLE IF NOT EXISTS t_topic_opts_back SELECT * FROM t_topic_opts;
-- CREATE TABLE IF NOT EXISTS t_topic_org_back SELECT * FROM t_topic_org;
-- CREATE TABLE IF NOT EXISTS t_type_group_back SELECT * FROM t_type_group;
-- CREATE TABLE IF NOT EXISTS t_topic_log_file_back SELECT * FROMt_topic_log_filet_type_group;



-- ==============================================
-- org_level 相关表
-- ==============================================
-- t_meeting_org_change_log.org_level
-- t_meeting_org_change_log.org_level
-- t_meeting_org_debrief_review.org_level
-- t_meeting_user_commend_penalize.org_level
-- t_meeting_user_comment.org_level
-- t_meeting_user_comment_statistics.org_level

-- ==============================================
-- 用户中心相关ID
-- ==============================================
START transaction ;


-- 用户id偏移量
SET @USER_ID_OFFSET = 100000;

-- 组织id偏移量 organization_id org_id
SET @ORG_ID_OFFSET = 100000;

-- 部门id偏移量 department_id
SET @DEP_ID_OFFSET = 100000;

-- 党小组 t_user_org_group
SET @ORGG_ID_OFFSET =  1000;

-- 支委会 t_user_org_period
SET @ORGP_ID_OFFSET =  10000;

-- ==============================================
-- 审批流相关ID
-- ==============================================
-- 审批流ID偏移量 t_workflow
SET @WOR_ID_OFFSET = 1000;
-- 审批任务ID t_workflow_task
SET @WORT_ID_OFFSET = 1000;


-- ==============================================
-- 文件相关ID
-- ==============================================
--  file_id
SET @FILE_ID_OFFSET = 1000000;

-- ==============================================
-- meeting相关ID
-- ==============================================

SET @BASE_ID_OFFSET = 10000000;

-- t_meeting
SET @MEET_ID_OFFSET = 1000000;

-- t_category
SET @CAT_ID_OFFSET = 1000;

-- t_group
SET @GRO_ID_OFFSET = 1000;


-- t_meeting_file
SET @MFIL_ID_OFFSET = 1000;

-- t_meeting_history
SET @MHIS_ID_OFFSET = 1000;


-- t_meeting_leave
SET @MLEA_ID_OFFSET = 1000;


-- t_meeting_user
SET @MUSER_ID_OFFSET = 10000000;


-- t_meeting_org
SET @MORG_ID_OFFSET = 1000000;


-- t_meeting_plan
SET @MPLAN_ID_OFFSET = 1000;

-- t_meeting_org_change_log
SET @MORGC_ID_OFFSET = 1000000;



-- t_meeting_org_commend_penalize
SET @MOCP_ID_OFFSET = @BASE_ID_OFFSET;

-- t_meeting_org_debrief_review
SET @MODR_ID_OFFSET = @BASE_ID_OFFSET;

-- t_meeting_org_group
SET @MORGG_ID_OFFSET = @BASE_ID_OFFSET;


-- t_meeting_org_period
SET @MORGP_ID_OFFSET = @BASE_ID_OFFSET;


-- t_meeting_plan_limit
SET @MLIM_ID_OFFSET = @BASE_ID_OFFSET;


-- t_meeting_plan_limit_type
SET @MLIMT_ID_OFFSET = @BASE_ID_OFFSET;


-- t_meeting_plan_log
SET @MPLAL_ID_OFFSET = @BASE_ID_OFFSET;

-- t_meeting_require
SET @MREQ_ID_OFFSET = @BASE_ID_OFFSET;


-- t_type
SET @TYPE_ID_OFFSET = @BASE_ID_OFFSET;

-- t_meeting_resolution_file
SET @MREF_ID_OFFSET = @BASE_ID_OFFSET;


-- t_meeting_result
SET @MRES_ID_OFFSET = @BASE_ID_OFFSET;


-- t_meeting_result_hs
SET @MRESH_ID_OFFSET = @BASE_ID_OFFSET;



-- t_meeting_task
SET @MEETT_ID_OFFSET = @BASE_ID_OFFSET;



-- t_meeting_topic
SET @MTOP_ID_OFFSET = @BASE_ID_OFFSET;

-- t_topic
SET @TOPI_ID_OFFSET = @BASE_ID_OFFSET;


-- t_meeting_type
SET @MTYPE_ID_OFFSET = @BASE_ID_OFFSET;


-- t_meeting_user
SET @MEETING_USER_ID = @BASE_ID_OFFSET;

-- t_meeting_user_commend_penalize
SET @MEETING_USER_COMMEND_PENALIZE_ID= @BASE_ID_OFFSET;


-- t_meeting_user_comment
SET @USER_COMMENT_ID= @BASE_ID_OFFSET;

-- t_meeting_user_comment_statistics
SET @USER_COMMENT_STATISTICS_ID= @BASE_ID_OFFSET;

-- t_topic_content
SET @CONTENT_ID = @BASE_ID_OFFSET;

-- t_topic_file
SET @TOPIC_FILE_ID = @BASE_ID_OFFSET;

-- t_topic_log_file
SET @TOPIC_LOG_FILE_ID = @BASE_ID_OFFSET;

-- t_topic_log
SET @TOPIC_LOG_ID = @BASE_ID_OFFSET;

-- t_topic_opts
SET @OPTS_ID = @BASE_ID_OFFSET;


-- t_topic_org
SET @TOPIC_ORG_ID = @BASE_ID_OFFSET;


-- t_type_group
SET @TYPE_GROUP_ID = @BASE_ID_OFFSET;


-- ====================================================================
-- 更新SQL
-- ====================================================================
-- t_meeting
-- 更新id
UPDATE t_meeting
SET meeting_id = meeting_id + @MEET_ID_OFFSET,
org_id = org_id + @ORG_ID_OFFSET,
p_org_id = p_org_id + @ORG_ID_OFFSET,
workflow_id = workflow_id + @WOR_ID_OFFSET,
workflow_task_id = workflow_task_id + @WORT_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;


-- t_category
-- 更新id
UPDATE t_category
SET category_id = category_id + @CAT_ID_OFFSET,
org_id =  (CASE WHEN org_id > 0 THEN  org_id + @ORG_ID_OFFSET ELSE org_id END  ) ,
create_user =  (CASE WHEN create_user > 0 THEN  create_user + @USER_ID_OFFSET ELSE create_user END),
last_change_user = last_change_user + @USER_ID_OFFSET;

-- t_group
-- 更新id
UPDATE t_group
SET group_id = group_id + @GRO_ID_OFFSET,
org_id = org_id + @ORG_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;


-- t_meeting_file
-- 更新id
-- UPDATE t_meeting_file
-- SET t_meeting_file_id = t_meeting_file_id + @MFIL_ID_OFFSET,
-- `name` = CONCAT(SUBSTRING_INDEX(`name`, ".", 1)+@FILE_ID_OFFSET,'.',SUBSTRING_INDEX(`name`, ".", -1)),
-- link_id = link_id + @MOCP_ID_OFFSET;

-- t_meeting_history
-- 更新id
UPDATE t_meeting_history
SET meeting_history_id = meeting_history_id + @MHIS_ID_OFFSET,
meeting_id = meeting_id + @MEET_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET;


-- t_meeting_leave
-- 更新id
UPDATE t_meeting_leave
SET meeting_leave_id = meeting_leave_id + @MLEA_ID_OFFSET,
meeting_id = meeting_id + @MEET_ID_OFFSET,
meeting_user_id = meeting_user_id + @MUSER_ID_OFFSET,
user_id = user_id + @USER_ID_OFFSET,
p_org_id = p_org_id + @ORG_ID_OFFSET,
check_user_id = check_user_id  + @USER_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET;


-- t_meeting_org
-- 更新id
UPDATE t_meeting_org
SET meeting_org_id = meeting_org_id + @MORG_ID_OFFSET,
meeting_plan_id = meeting_plan_id + @MPLAN_ID_OFFSET,
org_id = org_id + @ORG_ID_OFFSET;



-- t_meeting_org_change_log
-- 更新id
UPDATE t_meeting_org_change_log
SET meeting_org_change_log_id = meeting_org_change_log_id + @MORGC_ID_OFFSET,
-- org_level = org_level + @USER_ID_OFFSET,
org_id = org_id + @ORG_ID_OFFSET;



-- t_meeting_org_commend_penalize
-- 更新id
-- UPDATE t_meeting_org_commend_penalize
-- SET meeting_org_commend_penalize_id = meeting_org_commend_penalize_id + @MOCP_ID_OFFSET,
-- -- org_level = org_level + @USER_ID_OFFSET,
-- org_id = org_id + @ORG_ID_OFFSET,
-- create_user = create_user + @USER_ID_OFFSET,
-- last_change_user = last_change_user + @USER_ID_OFFSET;
--
-- -- t_meeting_org_debrief_review
-- -- 更新id
-- UPDATE t_meeting_org_debrief_review
-- SET meeting_org_debrief_review_id = meeting_org_debrief_review_id + @MODR_ID_OFFSET,
-- -- org_level = org_level + @USER_ID_OFFSET,
-- org_id = org_id + @ORG_ID_OFFSET,
-- create_user = create_user + @USER_ID_OFFSET,
-- last_change_user = last_change_user + @USER_ID_OFFSET;


-- t_meeting_org_group
-- 更新id
UPDATE t_meeting_org_group
SET meeting_org_group_id = meeting_org_group_id + @MORGG_ID_OFFSET,
meeting_id = meeting_id + @MEET_ID_OFFSET,
org_group_id = org_group_id + @ORGG_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET;



-- t_meeting_org_period
-- 更新id
UPDATE t_meeting_org_period
SET meeting_org_period_id = meeting_org_period_id + @MORGG_ID_OFFSET,
meeting_id = meeting_id + @MEET_ID_OFFSET,
period_id = period_id + @ORGP_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET;



-- t_meeting_plan
-- 更新id
UPDATE t_meeting_plan
SET meeting_plan_id = meeting_plan_id + @MPLAN_ID_OFFSET,
org_id = org_id + @ORG_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;


-- t_meeting_plan_limit
-- 更新id
UPDATE t_meeting_plan_limit
SET meeting_plan_limit_id = meeting_plan_limit_id + @MLIM_ID_OFFSET,
meeting_plan_id = meeting_plan_id + @MPLAN_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;


-- t_meeting_plan_limit_type
-- 更新id
UPDATE t_meeting_plan_limit_type
SET meeting_plan_limit_type_id = meeting_plan_limit_type_id + @MLIMT_ID_OFFSET,
meeting_plan_limit_id = meeting_plan_limit_id + @MLIM_ID_OFFSET;



-- t_meeting_plan_log
-- 更新id
UPDATE t_meeting_plan_log
SET meeting_plan_log_id = meeting_plan_log_id + @MPLAL_ID_OFFSET,
meeting_plan_id = meeting_plan_id + @MPLAN_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;


-- t_meeting_require
-- 更新id
UPDATE t_meeting_require
SET meeting_require_id = meeting_require_id + @MPLAL_ID_OFFSET,
meeting_plan_id = meeting_plan_id + @MPLAN_ID_OFFSET,
type_id = type_id + @TYPE_ID_OFFSET,
category_id = category_id + @CAT_ID_OFFSET;



-- t_meeting_resolution_file
-- 更新id
UPDATE t_meeting_resolution_file
SET meeting_resolution_file_id = meeting_resolution_file_id + @MREF_ID_OFFSET,
meeting_id = meeting_id + @MEET_ID_OFFSET,
`name` = CONCAT(SUBSTRING_INDEX(`name`, ".", 1)+@FILE_ID_OFFSET,'.',SUBSTRING_INDEX(`name`, ".", -1)),
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;



-- t_meeting_result
-- 更新id
UPDATE t_meeting_result
SET meeting_reslut_id = meeting_reslut_id + @MRES_ID_OFFSET,
org_id = org_id + @ORG_ID_OFFSET,
meeting_id = meeting_id + @MEET_ID_OFFSET,
workflow_id = workflow_id + @WOR_ID_OFFSET,
workflow_task_id = workflow_task_id + @WORT_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;

-- t_meeting_result_hs
-- 更新id
UPDATE t_meeting_result_hs
SET meeting_result_hs_id = meeting_result_hs_id + @MRES_ID_OFFSET,
meeting_id = meeting_id + @MEET_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET;

-- t_meeting_task
-- 更新id
UPDATE t_meeting_task
SET meeting_task_id = meeting_task_id + @MEETT_ID_OFFSET,
meeting_plan_id = meeting_plan_id + @MPLAN_ID_OFFSET,
meeting_require_id = meeting_require_id + @MPLAL_ID_OFFSET,
type_id = type_id + @TYPE_ID_OFFSET,
category_id = category_id + @CAT_ID_OFFSET,
org_id = org_id + @ORG_ID_OFFSET,
p_org_id = p_org_id + @ORG_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;



-- t_meeting_topic
-- 更新id
UPDATE t_meeting_topic
SET meeting_topic_id = meeting_topic_id + @MTOP_ID_OFFSET,
meeting_id = meeting_id + @MEET_ID_OFFSET,
topic_id = topic_id + @TOPI_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;

-- t_meeting_type
-- 更新id
UPDATE t_meeting_type
SET meeting_type_id = meeting_type_id + @MTYPE_ID_OFFSET,
meeting_id = meeting_id + @MEET_ID_OFFSET,
meeting_task_id = meeting_task_id + @MEETT_ID_OFFSET,
type_id = type_id + @TYPE_ID_OFFSET,
category_id = category_id + @CAT_ID_OFFSET;



-- t_meeting_user_commend_penalize
-- 更新id
-- UPDATE t_meeting_user_commend_penalize
-- SET meeting_user_commend_penalize_id = meeting_user_commend_penalize_id + @MEETING_USER_COMMEND_PENALIZE_ID,
-- user_id = user_id + @USER_ID_OFFSET,
-- org_id = org_id + @ORG_ID_OFFSET,
-- create_user = create_user + @USER_ID_OFFSET,
-- last_change_user = last_change_user + @USER_ID_OFFSET;



-- t_meeting_user_comment
-- 更新id
-- UPDATE t_meeting_user_comment
-- SET user_comment_id = user_comment_id + @USER_COMMENT_ID,
-- user_id = user_id + @USER_ID_OFFSET,
-- org_id = org_id + @ORG_ID_OFFSET,
-- create_user = create_user + @USER_ID_OFFSET,
-- last_change_user = last_change_user + @USER_ID_OFFSET;
--
--
--
--
-- -- t_meeting_user_comment_statistics
-- -- 更新id
-- UPDATE t_meeting_user_comment_statistics
-- SET user_comment_statistics_id = user_comment_statistics_id + @USER_COMMENT_STATISTICS_ID,
-- org_id = org_id + @ORG_ID_OFFSET,
-- org_parent_id = org_parent_id + @ORG_ID_OFFSET,
-- create_user = create_user + @USER_ID_OFFSET,
-- last_change_user = last_change_user + @USER_ID_OFFSET;
--
-- t_topic
-- 更新id
UPDATE t_topic
SET topic_id = topic_id + @TOPI_ID_OFFSET,
org_id = org_id + @ORG_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;



-- t_topic_content
-- 更新id
UPDATE t_topic_content
SET content_id = content_id + @CONTENT_ID,
topic_id = topic_id + @TOPI_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;

-- t_topic_file
-- 更新id
UPDATE t_topic_file
SET topic_file_id = topic_file_id + @TOPIC_FILE_ID,
topic_id = topic_id + @TOPI_ID_OFFSET,
`name` = CONCAT(SUBSTRING_INDEX(`name`, ".", 1)+@FILE_ID_OFFSET,'.',SUBSTRING_INDEX(`name`, ".", -1)),
create_user = create_user + @USER_ID_OFFSET;

-- t_topic_log_file
-- 更新id
UPDATE t_topic_log_file
SET topic_log_file_id = topic_log_file_id + @TOPIC_LOG_FILE_ID,
topic_log_id = topic_log_id + @TOPIC_LOG_ID,
meeting_topic_id = meeting_topic_id + @MTOP_ID_OFFSET,
topic_id = topic_id + @TOPI_ID_OFFSET,
`name` = CONCAT(SUBSTRING_INDEX(`name`, ".", 1)+@FILE_ID_OFFSET,'.',SUBSTRING_INDEX(`name`, ".", -1)),
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;

-- t_topic_log
-- 更新id
UPDATE t_topic_log
SET topic_log_id = topic_log_id + @TOPIC_LOG_ID,
meeting_topic_id = meeting_topic_id + @MTOP_ID_OFFSET,
content_id = content_id + @CONTENT_ID,
topic_id = topic_id + @TOPI_ID_OFFSET,
opts_id = opts_id + @OPTS_ID,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;


-- t_topic_opts
-- 更新id
UPDATE t_topic_opts
SET opts_id = opts_id + @OPTS_ID,
content_id = content_id + @CONTENT_ID,
topic_id = topic_id + @TOPI_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;





-- t_topic_org
-- 更新id
UPDATE t_topic_org
SET topic_org_id = topic_org_id + @TOPIC_ORG_ID,
org_id = org_id + @ORG_ID_OFFSET,
topic_id = topic_id + @TOPI_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;



-- t_type
-- 更新id
UPDATE t_type
SET type_id = type_id + @TYPE_ID_OFFSET,
org_id = org_id + @ORG_ID_OFFSET,
category_id = category_id + @CAT_ID_OFFSET,
create_user = create_user + @USER_ID_OFFSET,
last_change_user = last_change_user + @USER_ID_OFFSET;




-- t_type_group
-- 更新id
UPDATE t_type_group
SET type_group_id = type_group_id + @TYPE_GROUP_ID,
group_id = group_id + @GRO_ID_OFFSET,
type_id = type_id + @TYPE_ID_OFFSET;




-- t_meeting_user
-- 更新id
UPDATE t_meeting_user
SET meeting_user_id = meeting_user_id + @MEETING_USER_ID,
meeting_id = meeting_id + @MEET_ID_OFFSET,
user_id = (CASE WHEN user_id >0 THEN  user_id + @USER_ID_OFFSET ELSE user_id END) ,
org_id =  (CASE WHEN org_id >0 THEN  org_id + @ORG_ID_OFFSET ELSE org_id END) ;

-- ROLLBACK;
-- COMMIT;


-- DROP 备份表
-- DROP TABLE IF EXISTS t_meeting_back;
-- DROP TABLE IF EXISTS t_category_back;
-- DROP TABLE IF EXISTS t_group_back;
-- DROP TABLE IF EXISTS t_meeting_file_back;
-- DROP TABLE IF EXISTS t_meeting_history_back;
-- DROP TABLE IF EXISTS t_meeting_file_back;
-- DROP TABLE IF EXISTS t_meeting_leave_back;
-- DROP TABLE IF EXISTS t_meeting_user_back;
-- DROP TABLE IF EXISTS t_meeting_org_back;
-- DROP TABLE IF EXISTS t_meeting_plan_back;
-- DROP TABLE IF EXISTS t_meeting_org_change_log_back;
-- DROP TABLE IF EXISTS t_meeting_org_commend_penalize_back;
-- DROP TABLE IF EXISTS t_meeting_org_debrief_review_back;
-- DROP TABLE IF EXISTS t_meeting_org_group_back;
-- DROP TABLE IF EXISTS t_meeting_org_period_back;
-- DROP TABLE IF EXISTS t_meeting_plan_limit_back;
-- DROP TABLE IF EXISTS t_meeting_plan_limit_type_back;
-- DROP TABLE IF EXISTS t_meeting_plan_log_back;
-- DROP TABLE IF EXISTS t_meeting_require_back;
-- DROP TABLE IF EXISTS t_type_back;
-- DROP TABLE IF EXISTS t_meeting_resolution_file_back;
-- DROP TABLE IF EXISTS t_meeting_result_back;
-- DROP TABLE IF EXISTS t_meeting_result_hs_back;
-- DROP TABLE IF EXISTS t_meeting_task_back;
-- DROP TABLE IF EXISTS t_meeting_topic_back;
-- DROP TABLE IF EXISTS t_topic_back;
-- DROP TABLE IF EXISTS t_meeting_type_back;
-- DROP TABLE IF EXISTS t_meeting_user_back;
-- DROP TABLE IF EXISTS t_meeting_user_commend_penalize_back;
-- DROP TABLE IF EXISTS t_meeting_user_comment_back;
-- DROP TABLE IF EXISTS t_meeting_user_comment_statistics_back;
-- DROP TABLE IF EXISTS t_topic_content_back;
-- DROP TABLE IF EXISTS t_topic_file_back;
-- DROP TABLE IF EXISTS t_topic_log_back;
-- DROP TABLE IF EXISTS t_topic_opts_back;
-- DROP TABLE IF EXISTS t_type_group_back;
