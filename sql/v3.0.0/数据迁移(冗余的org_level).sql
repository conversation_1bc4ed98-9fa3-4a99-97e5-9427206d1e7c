START transaction ;

UPDATE t_meeting_org_change_log SET org_level = idsAddNum(org_level,"-",100000)
WHERE org_id > 100000;

UPDATE t_meeting_org_change_log SET  org_level = REPLACE(org_level,"-100000-100001-","-0-1-")
WHERE org_id > 100000;

UPDATE t_eval_org_change_log SET org_level = idsAddNum(org_level,"-",100000)
WHERE org_id > 100000;

UPDATE t_eval_org_change_log SET  org_level = REPLACE(org_level,"-100000-100001-","-0-1-")
WHERE org_id > 100000;


UPDATE t_eval_leader_log SET org_level = idsAddNum(org_level,"-",100000)
WHERE org_id > 100000;

UPDATE t_eval_leader_log SET  org_level = REPLACE(org_level,"-100000-100001-","-0-1-")
WHERE org_id > 100000;
-- ROLLBACK;
-- COMMIT;