START transaction ;

SET @BASE_ID_OFFSET = 10000000;

-- t_type
SET @TYPE_ID_OFFSET = @BASE_ID_OFFSET;

-- t_eval_statistical_org_life
-- 更新meeting_type_id
UPDATE t_eval_statistical_org_life
SET meeting_type_id = meeting_type_id + @TYPE_ID_OFFSET;

-- t_eval_option
-- 更新meeting_type_ids
-- 需要添加idsAddNum函数
UPDATE t_eval_option SET meeting_type_ids = idsAddNum(meeting_type_ids,",",@TYPE_ID_OFFSET);


-- t_statistical_leader_org_life
-- 更新activity_type_id
UPDATE t_statistical_leader_org_life
SET activity_type_id = activity_type_id + @TYPE_ID_OFFSET;


-- t_statistical_org_life
-- 更新activity_type_id
UPDATE t_statistical_org_life
SET activity_type_id = activity_type_id  + @TYPE_ID_OFFSET;


-- t_statistical_org_life_view
-- 更新type_id
UPDATE t_statistical_org_life_view
SET type_id = type_id + @TYPE_ID_OFFSET where  type_id != -1;


-- t_statistical_user_org_life
-- 更新type_id
UPDATE t_statistical_user_org_life
SET type_id = type_id + @TYPE_ID_OFFSET;


-- ROLLBACK;
-- COMMIT;
