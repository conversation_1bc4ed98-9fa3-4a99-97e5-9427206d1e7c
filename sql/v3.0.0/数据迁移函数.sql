CREATE FUNCTION `idsAddNum`(ids VARCHAR(255),sp VARCHAR(255),add_num bigint(20)) RETURNS varchar(255) CHARSET utf8mb4
BEGIN

 declare res VARCHAR(255) DEFAULT NULL;
 declare fid VARCHAR(255);
 declare nfid VARCHAR(255) DEFAULT '';
 declare lid VARCHAR(255);

 SET lid = SUBSTRING(`ids`,-1);


	WHILE (ids IS NOT NULL AND ids != '') do
	  -- 左边第一个数字
		SET fid = SUBSTRING_INDEX(`ids`,sp,1);

		IF fid != '' THEN
			 SET nfid = fid + add_num;
		END IF;

		IF res IS NULL THEN
		    SET res = nfid;
	  ELSE
	   	SET res = CONCAT(res,sp,nfid);
	  END IF;

	  -- 更新ids为剩下的字符
    SET `ids` = SUBSTRING(`ids`,LENGTH(fid)+2);
		SET nfid = '';
		SET fid = NULL;
  END WHILE;

	IF lid = sp THEN
	  SET res = CONCAT(res,sp);
	END IF;

	RETURN res;
END