-- 新增菜单
INSERT INTO t_menu (menu_id, parent_id, `name`, status, create_time, update_time,seq,belong)
VALUES ('35','pc01','民主评议',1,SYSDATE(),SYSDATE(),110,1),
       ('3501','35','民主评议统计',1,SY<PERSON><PERSON><PERSON>(),SYSDATE(),110,1),
       ('3502','35','民主评议党员',1,SYSDATE(),SYSDATE(),110,1),
       ('36','pc01','述职评议',1,SYSDATE(),SYSDATE(),120,1),
       ('3601','36','述职评议统计',1,SY<PERSON>AT<PERSON>(),SYSDATE(),120,1),
       ('3602','36','基层组织述职评议',1,SYSDATE(),SYSDATE(),120,1),
       ('37','pc01','奖惩信息',1,SYSDAT<PERSON>(),SYSDATE(),130,1),
       ('3701','37','组织奖惩',1,SY<PERSON><PERSON><PERSON>(),SYSDATE(),130,1),
       ('3702','37','党员奖惩',1,SYSDATE(),SYSDATE(),130,1);

-- ----------------------------
-- Table structure for t_meeting_user_commend_penalize
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_user_commend_penalize`;
CREATE TABLE `t_meeting_user_commend_penalize` (
    `meeting_user_commend_penalize_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '党员奖惩表主键',
    `user_id` bigint(20) DEFAULT NULL COMMENT '党员ID',
    `user_name` varchar(100) DEFAULT NULL COMMENT '党员姓名',
    `org_id` bigint(20) DEFAULT NULL COMMENT '所属组织ID',
    `org_name` varchar(255) DEFAULT NULL COMMENT '所属组织名称',
    `cert_number` varchar(100) DEFAULT NULL COMMENT '脱敏身份证',
    `org_level` varchar(255) DEFAULT NULL COMMENT '所属组织层级关系',
    `effective_time` varchar(20) DEFAULT NULL COMMENT '生效日期',
    `name` int(10) DEFAULT NULL COMMENT '名称',
    `reason` varchar(100) DEFAULT NULL COMMENT '原因',
    `reward_type` int(10) DEFAULT NULL COMMENT '奖励类型(1-及时性表彰、2-定期集中性表彰)',
    `basis_description` text COMMENT '依据说明',
    `office_name` varchar(500) DEFAULT NULL COMMENT '受奖批准机关名称',
    `office_level` int(10) DEFAULT NULL COMMENT '受奖批准机关级别',
    `attachment` varchar(100) DEFAULT NULL COMMENT '附件',
    `type` int(10) DEFAULT NULL COMMENT '奖惩类型（1:奖励 2:惩罚）',
    `status` int(1) DEFAULT NULL COMMENT '数据状态',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后修改人',
    `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`meeting_user_commend_penalize_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='党员奖惩表';

-- ----------------------------
-- Table structure for t_meeting_user_comment
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_user_comment`;
CREATE TABLE `t_meeting_user_comment` (
    `user_comment_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '民主评议表主键',
    `review_year` int(4) DEFAULT NULL COMMENT '年度',
    `user_id` bigint(20) DEFAULT NULL COMMENT '党员ID',
    `user_name` varchar(100) DEFAULT NULL COMMENT '党员姓名',
    `org_id` bigint(20) DEFAULT NULL COMMENT '所属组织ID',
    `org_name` varchar(255) DEFAULT NULL COMMENT '所属组织名称',
    `org_level` varchar(50) DEFAULT NULL COMMENT '组织层级关系',
    `rating` int(10) DEFAULT NULL COMMENT '评议等级(1-优秀、2-合格、3-基本合格、4-不合格)',
    `deal_opinion` int(10) DEFAULT NULL COMMENT '处理意见(1-限期整改，2-除名)',
    `additional_information` text COMMENT '附加说明',
    `status` int(1) DEFAULT NULL COMMENT '数据状态',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后修改人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`user_comment_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COMMENT='党员评议表';

-- ----------------------------
-- Table structure for t_meeting_user_comment_statistics
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_user_comment_statistics`;
CREATE TABLE `t_meeting_user_comment_statistics` (
  `user_comment_statistics_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '民主评议统计',
  `org_id` bigint(20) DEFAULT NULL COMMENT '组织ID',
  `org_parent_id` bigint(20) DEFAULT NULL COMMENT '组织父级ID',
  `org_name` varchar(500) DEFAULT NULL COMMENT '组织名称',
  `org_type_child` int(20) DEFAULT NULL COMMENT '组织类型',
  `org_level` varchar(100) DEFAULT NULL COMMENT '组织层级关系',
  `review_year` int(4) DEFAULT NULL COMMENT '评议年度',
  `party_number` int(10) DEFAULT NULL COMMENT '党员数量',
  `join_comment_number` int(10) DEFAULT NULL COMMENT '参加评议的党员数',
  `rating` int(1) DEFAULT NULL COMMENT '评议等级',
  `rating_number` int(10) DEFAULT NULL COMMENT '当前等级党员数数量',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `last_change_user` bigint(255) DEFAULT NULL COMMENT '最后修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`user_comment_statistics_id`),
  KEY `org_id_idx` (`org_id`) USING BTREE,
  KEY `org_parent_id_idx` (`org_parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=76945 DEFAULT CHARSET=utf8mb4 COMMENT='党员评议统计';


-- ----------------------------
-- Table structure for t_meeting_file
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_file`;
CREATE TABLE `t_meeting_file` (
    `t_meeting_file_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `file_name` varchar(500) DEFAULT NULL COMMENT '文件原名称',
    `path` varchar(500) DEFAULT NULL COMMENT '附件路径',
    `size` bigint(20) DEFAULT NULL COMMENT '文件大小（byte）',
    `name` varchar(255) DEFAULT NULL COMMENT '附件名称',
    `link_id` bigint(20) DEFAULT NULL COMMENT '关联表的主键id',
    `source` int(10) NOT NULL COMMENT '文件来源 1 组织奖惩 2组织评议 3 党员奖惩',
    PRIMARY KEY (`t_meeting_file_id`)
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8mb4 COMMENT='文件实体类';

-- ----------------------------
-- Table structure for t_meeting_org_commend_penalize
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_org_commend_penalize`;
CREATE TABLE `t_meeting_org_commend_penalize` (
    `meeting_org_commend_penalize_id` bigint(20) NOT NULL AUTO_INCREMENT,
    `org_id` bigint(20) DEFAULT NULL COMMENT '组织id',
    `org_name` varchar(500) DEFAULT NULL COMMENT '组织名称',
    `ratify_time` datetime DEFAULT NULL COMMENT '批准日期',
    `name` varchar(256) DEFAULT NULL COMMENT '奖励或者惩罚名称',
    `reason` varchar(256) DEFAULT NULL COMMENT '奖励或者惩罚原因',
    `org_level` varchar(1000) DEFAULT NULL COMMENT '组织层级',
    `reward_type` int(10) DEFAULT NULL COMMENT '奖励类型 1及时性表彰 2 定期集中性表彰',
    `basis_description` varchar(2000) DEFAULT NULL COMMENT '依据说明',
    `attachment` varchar(1000) DEFAULT NULL COMMENT '附件',
    `type` int(10) DEFAULT NULL COMMENT '1:奖励 2:惩罚',
    `status` int(1) DEFAULT NULL COMMENT '数据状态 1 正常 2删除',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后更新人',
    `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`meeting_org_commend_penalize_id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COMMENT='组织奖惩表';

-- ----------------------------
-- Table structure for t_meeting_org_debrief_review
-- ----------------------------
DROP TABLE IF EXISTS `t_meeting_org_debrief_review`;
CREATE TABLE `t_meeting_org_debrief_review` (
  `meeting_org_debrief_review_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` bigint(20) DEFAULT NULL COMMENT '组织id',
  `org_name` varchar(500) DEFAULT NULL COMMENT '组织名称',
  `current_org_level` varchar(1000) DEFAULT NULL COMMENT '当前组织层级',
  `rating` int(4) DEFAULT NULL COMMENT '评议等级 1:好 2:较好 3:一般 4:差',
  `attachment` varchar(1000) DEFAULT NULL COMMENT '附件',
  `additional_information` text COMMENT '附加说明',
  `review_year` int(4) DEFAULT NULL COMMENT '评议年度',
  `status` int(1) DEFAULT NULL COMMENT '数据状态 1 正常 2删除',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `last_change_user` bigint(20) DEFAULT NULL COMMENT '最后修改人',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `org_type_child` bigint(20) DEFAULT NULL COMMENT '组织类型',
  `org_level` varchar(1000) DEFAULT NULL COMMENT '添加时候的组织层级',
  PRIMARY KEY (`meeting_org_debrief_review_id`)
) ENGINE=InnoDB AUTO_INCREMENT=118 DEFAULT CHARSET=utf8mb4 COMMENT='组织述职评议表';

-- 字典
-- 组织奖惩字典
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('1037', 103701, '表彰名称', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('1037', 103702, '处理名称', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('1037', 103703, '奖励原因', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('1037', 103704, '惩罚原因', 4);

-- 表彰名称
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103701', 10370101, '全国先进党组织', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103701', 10370102, '部、委级先进党组织', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103701', 10370103, '省（区、市）级先进党组织', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103701', 10370104, '地（市、州）级先进党组织', 4);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103701', 10370105, '县（市、区）级先进党组织', 5);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103701', 10370106, '本系统级先进党组织', 6);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103701', 10370107, '基层先进党组织', 7);

-- 处理名称
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103702', 10370201, '改组', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103702', 10370202, '解散', 2);

-- 奖励原因
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103703', 10370301, '忠于职守，积极工作，成绩显著', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103703', 10370302, '遵守纪律，廉洁奉公，作风正派，办事公道，模范作用突出', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103703', 10370303, '在工作中有发明创造或者提出合理化意见，取得显著经济效益或者社会效益', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103703', 10370304, '为增进民族团结、维护社会稳定做出突出贡献', 4);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103703', 10370305, '爱护公共财产，节约国家资财有突出成绩', 5);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103703', 10370306, '防止或者挽救事故有功，使国家和人民群众利益免受或者减少损失', 6);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103703', 10370307, '在抢险、救灾等特定环境中奋不顾身，做出贡献', 7);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103703', 10370308, '同违法违纪行为作斗争有功绩', 8);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103703', 10370309, '在对外交往中为国家争得荣誉和利益', 9);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103703', 10370310, '有其他突出功绩', 10);

-- 惩罚原因
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103704', 10370401, '违反政治纪律', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103704', 10370402, '违反组织纪律', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103704', 10370403, '违反群众纪律', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103704', 10370404, '违反廉洁纪律', 4);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103704', 10370405, '违反工作纪律', 5);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103704', 10370406, '违反生活纪律', 6);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103704', 10370407, '其他', 7);

-- 惩罚原因-其他
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10370407', 1037040701, '缺乏革命意志', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10370407', 1037040702, '不履行党员义务', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10370407', 1037040703, '不履行党员条件', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10370407', 1037040704, '“三不”党员', 4);

-- 人员奖惩字典
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('1038', 103801, '奖励名称', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('1038', 103802, '奖励原因', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('1038', 103803, '惩罚名称', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('1038', 103804, '惩罚原因', 4);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('1038', 103805, '奖惩批准机关级别', 5);

-- 奖励名称
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380101, '全国优秀共产党员', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380102, '全国优秀党务工作者', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380103, '全国优秀党组织书记', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380104, '部、委级优秀共产党员', 4);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380105, '部、委级优秀党务工作者', 5);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380106, '省（区、市）级优秀共产党员', 6);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380107, '省（区、市）级优秀党务工作者', 7);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380108, '省（区、市）级优秀党组织书记', 8);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380109, '地（市、州）级优秀共产党员', 9);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380110, '地（市、州）级优秀党务工作者', 10);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380111, '县（市、区）级优秀共产党员', 11);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380112, '县（市、区）级优秀党务工作者', 12);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380113, '系统（单位）优秀共产党员', 13);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380114, '系统（单位）优秀党务工作者', 14);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380115, '基层优秀共产党员', 15);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103801', 10380116, '基层优秀党务工作者', 16);

-- 奖励原因
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103802', 10380201, '忠于职守，积极工作，成绩显著', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103802', 10380202, '遵守纪律，廉洁奉公，作风正派，办事公道，模范作用突出', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103802', 10380203, '在工作中有发明创造或者提出合理化意见，取得显著经济效益或者社会效益', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103802', 10380204, '为增进民族团结、维护社会稳定做出突出贡献', 4);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103802', 10380205, '爱护公共财产，节约国家资财有突出成绩', 5);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103802', 10380206, '防止或者挽救事故有功，使国家和人民群众利益免受或者减少损失', 6);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103802', 10380207, '在抢险、救灾等特定环境中奋不顾身，做出贡献', 7);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103802', 10380208, '同违法违纪行为作斗争有功绩', 8);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103802', 10380209, '在对外交往中为国家争得荣誉和利益', 9);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103802', 10380210, '有其他突出功绩', 10);

-- 惩罚名称
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103803', 10380301, '警告', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103803', 10380302, '严重警告', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103803', 10380303, '撤销党内职务', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103803', 10380304, '留党察看', 4);

-- 惩罚名称
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103804', 10380401, '违法犯罪', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103804', 10380402, '违纪', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103804', 10380403, '组织处置原因', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103804', 10380404, '其他', 4);

-- 惩罚名称-违纪
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380402', 1038040201, '违反政治纪律', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380402', 1038040202, '违反组织纪律', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380402', 1038040203, '违反干部选拔任用规定', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380402', 1038040204, '违反廉洁纪律', 4);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380402', 1038040205, '违反群众纪律', 5);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380402', 1038040206, '违反工作纪律', 6);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380402', 1038040207, '违反生活纪律', 7);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380402', 1038040208, '涉法行为', 8);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380402', 1038040209, '其他', 9);

-- 惩罚名称-组织处置原因
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380403', 1038040301, '理想信念缺失', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380403', 1038040302, '政治立场动摇', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380403', 1038040303, '宗旨观念淡薄', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380403', 1038040304, '工作消极懈怠', 4);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380403', 1038040305, '组织纪律散漫', 5);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380403', 1038040306, '道德行为不端', 6);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('10380403', 1038040307, '涉法行为', 7);

-- 奖惩批准机关级别', 5);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380501, '省（部）级及以上', 1);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380502, '地（局）级', 2);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380503, '省（区、市）人民政府直属工作部门、人民团体', 3);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380504, '县（处）级', 4);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380505, '乡（科）级', 5);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380506, '中央军委级', 6);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380507, '解放军总部级', 7);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380508, '大军区正级', 8);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380509, '大军区副级', 9);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380510, '正军级', 10);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380511, '副军级', 11);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380512, '正师级', 12);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380513, '副师（正旅）级', 13);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380514, '正团（副旅）级', 14);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380515, '副团级', 15);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380516, '正营级', 16);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380517, '副营级', 17);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380518, '正连级', 18);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380519, '副连级', 19);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380520, '排级', 20);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380521, '班级', 21);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380522, '基层单位', 22);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380523, '国际国外', 23);
INSERT INTO `t_option`(`code`, `op_key`, `op_value`, `seq`) VALUES ('103805', 10380524, '其他', 24);


