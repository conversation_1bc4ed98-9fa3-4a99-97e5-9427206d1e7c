<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.goodsogood</groupId>
    <artifactId>ows-meeting</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>ows-meeting</name>
    <description>组织生活</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.14</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <bean-utils.version>1.1.0</bean-utils.version>
        <commons-collections4.version>4.2</commons-collections4.version>
        <java.version>17</java.version>
        <kotlin.version>1.6.21</kotlin.version>
        <spring-cloud.version>2020.0.5</spring-cloud.version>
        <poi.version>3.17</poi.version>
        <zxing.version>3.4.0</zxing.version>
        <poi-scratchpad.version>3.12</poi-scratchpad.version>
        <dingtalk.version>2.2.12</dingtalk.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.goodsogood</groupId>
            <artifactId>ows-starter-spring-boot</artifactId>
            <version>4.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.persistence</groupId>
                    <artifactId>persistence-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 达梦数据库驱动 -->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.3.140</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.goodsogood</groupId>-->
        <!--            <artifactId>log4j2-elasticsearch</artifactId>-->
        <!--            <version>1.0.1-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.aidangqun</groupId>
            <artifactId>log4j2-chain-monitor</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>io.springfox</groupId>-->
        <!--            <artifactId>springfox-swagger2</artifactId>-->
        <!--            <version>${io.springfox.version}</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>io.springfox</groupId>-->
        <!--            <artifactId>springfox-swagger-ui</artifactId>-->
        <!--            <version>${io.springfox.version}</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.mottox</groupId>
            <artifactId>bean-utils</artifactId>
            <version>${bean-utils.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${commons-collections4.version}</version>
        </dependency>

        <!-- POI使用3.17版本 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>${poi.version}</version>
        </dependency>

        <!-- 文档模板操作依赖 -->
        <dependency>
            <groupId>fr.opensagres.xdocreport</groupId>
            <artifactId>fr.opensagres.xdocreport.document.docx</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>fr.opensagres.xdocreport</groupId>
            <artifactId>fr.opensagres.xdocreport.template.freemarker</artifactId>
            <version>2.0.1</version>
        </dependency>

        <!-- 二维码jar -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>${zxing.version}</version>
        </dependency>

        <!-- other dependencies -->


        <!-- 使用异步写日志功能 必须引入此包-->
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>3.4.2</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.apache.rocketmq</groupId>-->
        <!--            <artifactId>rocketmq-spring-boot-starter</artifactId>-->
        <!--            <version>2.1.0</version>-->
        <!--        </dependency>-->

        <!--        https://mvnrepository.com/artifact/org.apache.rocketmq/rocketmq-client-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.rocketmq</groupId>-->
        <!--            <artifactId>rocketmq-client</artifactId>-->
        <!--            <version>4.6.0</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.goodsogood</groupId>-->
        <!--            <artifactId>log4j2-rocketmq</artifactId>-->
        <!--            <version>0.0.1-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <!-- RabbitMQ -->
        <dependency>
            <groupId>com.aidangqun</groupId>
            <artifactId>log4j2-rabbitmq</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.22</version>
        </dependency>

        <dependency> <!-- exclude掉默认log配置 -->
            <groupId>com.aidangqun</groupId>
            <artifactId>ows-dingding-starter</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--easyExcel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.1</version>
            <!--            <version>2.2.10</version>-->
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>${dingtalk.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <jvmTarget>17</jvmTarget>
                    <compilerPlugins>
                        <plugin>spring</plugin>
                    </compilerPlugins>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>


</project>
